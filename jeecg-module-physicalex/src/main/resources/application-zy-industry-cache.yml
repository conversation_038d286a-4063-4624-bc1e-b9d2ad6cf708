# ZyIndustry 缓存配置
# 在主配置文件中通过 spring.profiles.include 引入此配置

spring:
  cache:
    # 缓存类型
    type: redis
    # 缓存名称配置
    cache-names:
      - zyIndustryLeafNodes
    redis:
      # 默认缓存过期时间（24小时）
      time-to-live: 86400000
      # 是否缓存空值
      cache-null-values: false
      # 键前缀
      key-prefix: "zy_industry_cache:"
      # 是否使用键前缀
      use-key-prefix: true

# 日志配置
logging:
  level:
    # ZyIndustry模块缓存相关日志
    org.jeecg.modules.occu.service.impl.ZyIndustryServiceImpl: DEBUG
    # Spring缓存日志
    org.springframework.cache: DEBUG
