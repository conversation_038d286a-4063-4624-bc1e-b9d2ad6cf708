-- 表单字段显示配置系统数据库表结构
-- 适用于MySQL 5.7+
-- 用于管理表单字段的显示位置、可见性等配置

-- 1. 表单显示配置主表
DROP TABLE IF EXISTS `form_display_config`;
CREATE TABLE `form_display_config` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `config_name` varchar(200) NOT NULL COMMENT '配置名称',
  `center_id` varchar(32) DEFAULT NULL COMMENT '体检中心ID',
  `center_name` varchar(200) DEFAULT NULL COMMENT '体检中心名称',
  `form_type` varchar(100) NOT NULL COMMENT '表单类型：customer_reg/company_reg等',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用：1启用，0禁用',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标志：0正常，1删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_form_display_config` (`form_type`, `center_id`, `config_name`, `del_flag`),
  KEY `idx_form_type` (`form_type`),
  KEY `idx_center_id` (`center_id`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表单显示配置主表';

-- 2. 字段显示配置表
DROP TABLE IF EXISTS `field_display_config`;
CREATE TABLE `field_display_config` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `config_id` varchar(32) NOT NULL COMMENT '配置ID，关联form_display_config.id',
  `field_key` varchar(100) NOT NULL COMMENT '字段标识',
  `field_name` varchar(200) NOT NULL COMMENT '字段名称',
  `is_visible` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否显示（兼容字段）：1显示，0隐藏',
  `display_location` varchar(50) NOT NULL DEFAULT 'outside' COMMENT '显示位置：outside-外部显示，collapse-折叠面板内，hidden-隐藏',
  `group_name` varchar(100) DEFAULT NULL COMMENT '分组名称',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序号',
  `is_required` tinyint(1) DEFAULT 0 COMMENT '是否必填：1必填，0非必填',
  `field_description` varchar(500) DEFAULT NULL COMMENT '字段描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_field_display_config` (`config_id`, `field_key`),
  KEY `idx_config_id` (`config_id`),
  KEY `idx_field_key` (`field_key`),
  KEY `idx_display_location` (`display_location`),
  KEY `idx_sort_order` (`sort_order`),
  CONSTRAINT `fk_field_display_config_id` FOREIGN KEY (`config_id`) REFERENCES `form_display_config` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字段显示配置表';

-- 3. 插入默认的客户登记表单显示配置
INSERT INTO `form_display_config` (
  `id`, 
  `config_name`, 
  `center_id`,
  `center_name`,
  `form_type`, 
  `is_active`,
  `create_by`
) VALUES (
  'default_customer_reg_config',
  '客户登记表单默认配置',
  'default',
  '默认体检中心',
  'customer_reg',
  1,
  'system'
);

-- 4. 插入默认的客户登记表单字段配置
INSERT INTO `field_display_config` (
  `id`,
  `config_id`,
  `field_key`,
  `field_name`,
  `is_visible`,
  `display_location`,
  `group_name`,
  `sort_order`,
  `is_required`
) VALUES 
-- 核心字段 - 外部区域固定显示
('field_exam_category', 'default_customer_reg_config', 'examCategory', '体检分类', 1, 'outside', '基本信息', 1, 1),
('field_appointment_date', 'default_customer_reg_config', 'appointmentDate', '预约日期', 1, 'outside', '基本信息', 2, 1),
('field_name', 'default_customer_reg_config', 'name', '姓名', 1, 'outside', '基本信息', 3, 1),
('field_card_type', 'default_customer_reg_config', 'cardType', '证件类型', 1, 'outside', '基本信息', 4, 1),
('field_id_card', 'default_customer_reg_config', 'idCard', '证件号', 1, 'outside', '基本信息', 5, 1),
('field_gender', 'default_customer_reg_config', 'gender', '性别', 1, 'outside', '基本信息', 6, 1),
('field_age', 'default_customer_reg_config', 'age', '年龄', 1, 'outside', '基本信息', 7, 0),
('field_birthday', 'default_customer_reg_config', 'birthday', '出生日期', 1, 'outside', '基本信息', 8, 0),
('field_phone', 'default_customer_reg_config', 'phone', '电话', 1, 'outside', '基本信息', 9, 1),
('field_career', 'default_customer_reg_config', 'career', '职业', 1, 'outside', '基本信息', 10, 0),

-- 地址信息 - 外部区域
('field_pca_code', 'default_customer_reg_config', 'pcaCode', '省市区县', 1, 'outside', '地址信息', 11, 0),
('field_address', 'default_customer_reg_config', 'address', '详细地址', 1, 'outside', '地址信息', 12, 0),

-- 联系信息 - 外部区域
('field_emergency_contact', 'default_customer_reg_config', 'emergencyContact', '紧急联系人', 1, 'outside', '联系信息', 13, 0),
('field_emergency_phone', 'default_customer_reg_config', 'emergencyPhone', '紧急电话', 1, 'outside', '联系信息', 14, 0),

-- 详细信息 - 折叠区域
('field_nation', 'default_customer_reg_config', 'nation', '民族', 1, 'collapse', '详细信息', 20, 0),
('field_blood_type', 'default_customer_reg_config', 'bloodType', '血型', 1, 'collapse', '详细信息', 21, 0),
('field_country_code', 'default_customer_reg_config', 'countryCode', '国籍', 1, 'collapse', '详细信息', 22, 0),
('field_post_code', 'default_customer_reg_config', 'postCode', '邮政编码', 1, 'collapse', '详细信息', 23, 0),
('field_edu_level', 'default_customer_reg_config', 'eduLevel', '文化程度', 1, 'collapse', '详细信息', 24, 0),
('field_marriage_status', 'default_customer_reg_config', 'marriageStatus', '婚姻状况', 1, 'collapse', '详细信息', 25, 0),
('field_customer_category', 'default_customer_reg_config', 'customerCategory', '客户类别', 1, 'collapse', '详细信息', 26, 0),

-- 健康信息 - 折叠区域
('field_is_pregnancy_prep', 'default_customer_reg_config', 'isPregnancyPrep', '是否备孕', 1, 'collapse', '健康信息', 30, 0),

-- 证件信息 - 折叠区域
('field_health_no', 'default_customer_reg_config', 'healthNo', '健康证号', 1, 'collapse', '证件信息', 35, 0),
('field_exam_card_no', 'default_customer_reg_config', 'examCardNo', '体检卡号', 1, 'collapse', '证件信息', 36, 0),

-- 职业信息 - 折叠区域
('field_work_years', 'default_customer_reg_config', 'workYears', '工龄', 1, 'collapse', '职业信息', 40, 0),

-- 单位信息 - 折叠区域
('field_company_name', 'default_customer_reg_config', 'companyName', '单位名称', 1, 'collapse', '单位信息', 45, 0),
('field_belong_company', 'default_customer_reg_config', 'belongCompany', '所属单位', 1, 'collapse', '单位信息', 46, 0),
('field_department', 'default_customer_reg_config', 'department', '所属科室', 1, 'collapse', '单位信息', 47, 0),

-- 标识信息 - 折叠区域
('field_supply_flag', 'default_customer_reg_config', 'supplyFlag', '补检', 1, 'collapse', '标识信息', 50, 0),
('field_pre_pay_flag', 'default_customer_reg_config', 'prePayFlag', '预缴', 1, 'collapse', '标识信息', 51, 0),
('field_re_exam_status', 'default_customer_reg_config', 'reExamStatus', '是否复查', 1, 'collapse', '标识信息', 52, 0),
('field_re_exam_remark', 'default_customer_reg_config', 'reExamRemark', '复查备注', 1, 'collapse', '标识信息', 53, 0),

-- 财务信息 - 折叠区域
('field_recipe_title', 'default_customer_reg_config', 'recipeTitle', '发票抬头', 1, 'collapse', '财务信息', 60, 0),

-- 原检信息 - 折叠区域
('field_original_id_card', 'default_customer_reg_config', 'originalIdCard', '原检证件号', 1, 'collapse', '原检信息', 65, 0),
('field_relation_with_original', 'default_customer_reg_config', 'relationWithOriginal', '与原检关系', 1, 'collapse', '原检信息', 66, 0),

-- 其他信息 - 折叠区域
('field_introducer', 'default_customer_reg_config', 'introducer', '介绍人', 1, 'collapse', '其他信息', 70, 0),
('field_secret_level', 'default_customer_reg_config', 'secretLevel', '保密等级', 1, 'collapse', '其他信息', 71, 0),
('field_remark', 'default_customer_reg_config', 'remark', '备注', 1, 'collapse', '其他信息', 72, 0);

-- 5. 创建索引优化查询性能
CREATE INDEX `idx_form_display_config_composite` ON `form_display_config` (`form_type`, `is_active`, `center_id`);
CREATE INDEX `idx_field_display_config_composite` ON `field_display_config` (`config_id`, `display_location`, `sort_order`);

-- 6. 创建视图简化查询
CREATE VIEW `v_form_display_config_summary` AS
SELECT 
  fdc.`id`,
  fdc.`config_name`,
  fdc.`center_id`,
  fdc.`center_name`,
  fdc.`form_type`,
  fdc.`is_active`,
  COUNT(fdcf.`id`) as `field_count`,
  COUNT(CASE WHEN fdcf.`display_location` = 'outside' THEN 1 END) as `outside_field_count`,
  COUNT(CASE WHEN fdcf.`display_location` = 'collapse' THEN 1 END) as `collapse_field_count`,
  COUNT(CASE WHEN fdcf.`display_location` = 'hidden' THEN 1 END) as `hidden_field_count`,
  fdc.`create_time`,
  fdc.`update_time`
FROM `form_display_config` fdc
LEFT JOIN `field_display_config` fdcf ON fdc.`id` = fdcf.`config_id`
WHERE fdc.`del_flag` = 0
GROUP BY fdc.`id`, fdc.`config_name`, fdc.`center_id`, fdc.`center_name`, 
         fdc.`form_type`, fdc.`is_active`, fdc.`create_time`, fdc.`update_time`;
