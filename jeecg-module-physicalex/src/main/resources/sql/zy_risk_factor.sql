-- 危害因素表
CREATE TABLE `zy_risk_factor` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '危害因素名称',
  `code` varchar(50) DEFAULT NULL COMMENT '危害因素编码',
  `help_char` varchar(20) DEFAULT NULL COMMENT '助记码',
  `wubi_char` varchar(20) DEFAULT NULL COMMENT '五笔码',
  `type` varchar(50) DEFAULT NULL COMMENT '危害因素类型',
  `category` varchar(50) DEFAULT NULL COMMENT '危害因素分类',
  `harm_level` varchar(20) DEFAULT NULL COMMENT '危害等级',
  `detection_method` varchar(200) DEFAULT NULL COMMENT '检测方法',
  `occupational_limit` varchar(100) DEFAULT NULL COMMENT '职业接触限值',
  `health_effect` text COMMENT '健康影响',
  `protective_measures` text COMMENT '防护措施',
  `status` varchar(10) DEFAULT '1' COMMENT '状态',
  `del_flag` varchar(1) DEFAULT '0' COMMENT '删除标志',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `sort_no` int DEFAULT '0' COMMENT '排序号',
  `enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_zy_risk_factor_name` (`name`),
  UNIQUE KEY `uk_zy_risk_factor_code` (`code`),
  KEY `idx_zy_risk_factor_name` (`name`),
  KEY `idx_zy_risk_factor_code` (`code`),
  KEY `idx_zy_risk_factor_help_char` (`help_char`),
  KEY `idx_zy_risk_factor_wubi_char` (`wubi_char`),
  KEY `idx_zy_risk_factor_type` (`type`),
  KEY `idx_zy_risk_factor_category` (`category`),
  KEY `idx_zy_risk_factor_enabled` (`enabled`),
  KEY `idx_zy_risk_factor_del_flag` (`del_flag`),
  KEY `idx_zy_risk_factor_sort_no` (`sort_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='危害因素信息表';

-- 插入示例数据
INSERT INTO `zy_risk_factor` (`id`, `name`, `code`, `help_char`, `wubi_char`, `type`, `category`, `harm_level`, `detection_method`, `occupational_limit`, `health_effect`, `protective_measures`, `status`, `del_flag`, `remark`, `create_by`, `sort_no`, `enabled`) VALUES
('1', '噪声', 'ZS001', 'ZS', 'JQRN', '物理因素', '噪声', '中等', '声级计测量', '85dB(A)', '听力损失、心血管疾病', '佩戴耳塞、降噪设备', '1', '0', '工作场所常见物理危害因素', 'system', 1, 1),
('2', '粉尘', 'FC002', 'FC', 'OWFI', '化学因素', '粉尘', '高', '粉尘浓度测定', '根据粉尘类型确定', '尘肺病、呼吸系统疾病', '佩戴防尘口罩、通风除尘', '1', '0', '工作场所常见化学危害因素', 'system', 2, 1),
('3', '高温', 'GW003', 'GW', 'YMJY', '物理因素', '高温', '中等', '温度计测量', 'WBGT指数<28℃', '中暑、脱水', '降温措施、补充水分', '1', '0', '高温作业环境危害因素', 'system', 3, 1),
('4', '振动', 'ZD004', 'ZD', 'RJHH', '物理因素', '振动', '中等', '振动测量仪', '手臂振动<5m/s²', '振动病、血管神经功能障碍', '减振手套、限制作业时间', '1', '0', '机械振动危害因素', 'system', 4, 1),
('5', '苯', 'B005', 'B', 'SGD', '化学因素', '有机溶剂', '高', '气相色谱法', '6mg/m³', '白血病、再生障碍性贫血', '密闭作业、个人防护', '1', '0', '有机溶剂类危害因素', 'system', 5, 1),
('6', '甲醛', 'JQ006', 'JQ', 'LKGJ', '化学因素', '有机化合物', '高', '分光光度法', '0.5mg/m³', '鼻咽癌、白血病', '通风排毒、防护用品', '1', '0', '室内空气污染物', 'system', 6, 1),
('7', '铅', 'Q007', 'Q', 'MYV', '化学因素', '重金属', '高', '原子吸收法', '0.05mg/m³', '铅中毒、神经系统损害', '个人防护、定期体检', '1', '0', '重金属危害因素', 'system', 7, 1),
('8', '紫外线', 'ZWX008', 'ZWX', 'XJNH', '物理因素', '电离辐射', '中等', 'UV辐射计', '根据波长确定', '皮肤癌、白内障', '防护服、防护眼镜', '1', '0', '电离辐射危害因素', 'system', 8, 1),
('9', '一氧化碳', 'YHTWT009', 'YHTW', 'SGDG', '化学因素', '有毒气体', '高', '不分光红外法', '20mg/m³', '一氧化碳中毒', '通风换气、气体检测', '1', '0', '有毒气体危害因素', 'system', 9, 1),
('10', '手动阀', 'SDV010', 'SDV', 'RJHH', '人体工效学', '重复性动作', '低', '人体工效学评估', '无明确限值', '肌肉骨骼疾病', '工作轮换、休息', '1', '0', '人体工效学危害因素', 'system', 10, 1);

-- 添加数据约束，防止名称与助记码、五笔码、编码相同
ALTER TABLE `zy_risk_factor` 
ADD CONSTRAINT `chk_name_not_help_char` 
CHECK (`name` != `help_char` OR `help_char` IS NULL);

ALTER TABLE `zy_risk_factor` 
ADD CONSTRAINT `chk_name_not_wubi_char` 
CHECK (`name` != `wubi_char` OR `wubi_char` IS NULL);

ALTER TABLE `zy_risk_factor` 
ADD CONSTRAINT `chk_name_not_code` 
CHECK (`name` != `code` OR `code` IS NULL);
