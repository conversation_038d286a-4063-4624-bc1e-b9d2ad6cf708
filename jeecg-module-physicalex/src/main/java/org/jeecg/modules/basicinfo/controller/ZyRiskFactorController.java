package org.jeecg.modules.basicinfo.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.basicinfo.entity.ZyRiskFactor;
import org.jeecg.modules.basicinfo.service.IZyRiskFactorService;
import org.jeecg.modules.basicinfo.dto.ZyRiskFactorAutoCompleteDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 危害因素信息管理
 */
@Api(tags = "危害因素信息管理")
@RestController
@RequestMapping("/basicinfo/zyRiskFactor")
@Slf4j
public class ZyRiskFactorController extends JeecgController<ZyRiskFactor, IZyRiskFactorService> {

    @Autowired
    private IZyRiskFactorService zyRiskFactorService;

    /**
     * 分页列表查询
     *
     * @param zyRiskFactor
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "危害因素-分页列表查询")
    @ApiOperation(value = "危害因素-分页列表查询", notes = "危害因素-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<ZyRiskFactor>> queryPageList(ZyRiskFactor zyRiskFactor,
                                                     @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                     @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                     HttpServletRequest req) {
        QueryWrapper<ZyRiskFactor> queryWrapper = QueryGenerator.initQueryWrapper(zyRiskFactor, req.getParameterMap());
        Page<ZyRiskFactor> page = new Page<ZyRiskFactor>(pageNo, pageSize);
        IPage<ZyRiskFactor> pageList = zyRiskFactorService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param zyRiskFactor
     * @return
     */
    @AutoLog(value = "危害因素-添加")
    @ApiOperation(value = "危害因素-添加", notes = "危害因素-添加")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody ZyRiskFactor zyRiskFactor) {
        zyRiskFactorService.save(zyRiskFactor);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param zyRiskFactor
     * @return
     */
    @AutoLog(value = "危害因素-编辑")
    @ApiOperation(value = "危害因素-编辑", notes = "危害因素-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody ZyRiskFactor zyRiskFactor) {
        zyRiskFactorService.updateById(zyRiskFactor);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "危害因素-通过id删除")
    @ApiOperation(value = "危害因素-通过id删除", notes = "危害因素-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        zyRiskFactorService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "危害因素-批量删除")
    @ApiOperation(value = "危害因素-批量删除", notes = "危害因素-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.zyRiskFactorService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "危害因素-通过id查询")
    @ApiOperation(value = "危害因素-通过id查询", notes = "危害因素-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<ZyRiskFactor> queryById(@RequestParam(name = "id", required = true) String id) {
        ZyRiskFactor zyRiskFactor = zyRiskFactorService.getById(id);
        if (zyRiskFactor == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(zyRiskFactor);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param zyRiskFactor
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ZyRiskFactor zyRiskFactor) {
        return super.exportXls(request, zyRiskFactor, ZyRiskFactor.class, "危害因素信息");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ZyRiskFactor.class);
    }

    /**
     * 危害因素自动完成接口（带缓存）
     * 
     * @param keyword 搜索关键词
     * @param pageSize 返回数量限制，默认50
     * @param searchType 搜索类型
     * @return
     */
    @AutoLog(value = "危害因素-自动完成")
    @ApiOperation(value = "危害因素-自动完成", notes = "危害因素-自动完成")
    @GetMapping(value = "/autoComplete")
    @Cacheable(cacheNames = "zy_risk_factor_auto_complete", 
               key = "'zy_risk_factor_auto_' + (#keyword != null ? #keyword : 'all') + '_' + #pageSize + '_' + (#searchType != null ? #searchType : 'all')",
               unless = "#result == null || #result.result == null || #result.result.size() == 0")
    public Result<List<ZyRiskFactorAutoCompleteDTO>> autoComplete(
            @ApiParam(value = "搜索关键词", required = false) @RequestParam(required = false) String keyword,
            @ApiParam(value = "返回数量限制", required = false) @RequestParam(defaultValue = "50") Integer pageSize,
            @ApiParam(value = "搜索类型", required = false) @RequestParam(defaultValue = "all") String searchType) {
        
        log.info("危害因素自动完成查询，关键词: {}, 数量限制: {}, 搜索类型: {}", keyword, pageSize, searchType);
        
        try {
            List<ZyRiskFactorAutoCompleteDTO> result = zyRiskFactorService.getAutoCompleteList(keyword, searchType, pageSize);
            log.info("危害因素自动完成查询成功，返回 {} 条数据", result.size());
            return Result.OK(result);
        } catch (Exception e) {
            log.error("危害因素自动完成查询失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 批量验证危害因素名称
     * 
     * @param requestBody 请求体，包含names字段
     * @return
     */
    @AutoLog(value = "危害因素-批量验证")
    @ApiOperation(value = "危害因素-批量验证", notes = "危害因素-批量验证")
    @PostMapping(value = "/batchValidate")
    public Result<IZyRiskFactorService.BatchValidationResult> batchValidate(@RequestBody Map<String, Object> requestBody) {
        
        try {
            @SuppressWarnings("unchecked")
            List<String> names = (List<String>) requestBody.get("names");
            
            if (names == null || names.isEmpty()) {
                return Result.error("名称列表不能为空");
            }
            
            log.info("危害因素批量验证，数量: {}", names.size());
            
            IZyRiskFactorService.BatchValidationResult result = zyRiskFactorService.batchValidateNames(names);
            log.info("危害因素批量验证完成，成功: {}, 失败: {}", result.getSuccessCount(), result.getFailureCount());
            
            return Result.OK(result);
        } catch (Exception e) {
            log.error("危害因素批量验证失败", e);
            return Result.error("验证失败: " + e.getMessage());
        }
    }

    /**
     * 精确匹配危害因素名称
     * 
     * @param name 危害因素名称
     * @return
     */
    @AutoLog(value = "危害因素-精确匹配")
    @ApiOperation(value = "危害因素-精确匹配", notes = "危害因素-精确匹配")
    @GetMapping(value = "/exactMatch")
    public Result<ZyRiskFactorAutoCompleteDTO> exactMatch(
            @ApiParam(value = "危害因素名称", required = true) @RequestParam String name) {
        
        log.info("危害因素精确匹配，名称: {}", name);
        
        try {
            ZyRiskFactorAutoCompleteDTO result = zyRiskFactorService.getByExactName(name);
            
            if (result != null) {
                log.info("危害因素精确匹配成功: {}", result.getName());
                return Result.OK(result);
            } else {
                log.info("危害因素精确匹配未找到: {}", name);
                return Result.error("未找到匹配的危害因素");
            }
        } catch (Exception e) {
            log.error("危害因素精确匹配失败", e);
            return Result.error("匹配失败: " + e.getMessage());
        }
    }

    /**
     * 根据名称列表获取危害因素
     * 
     * @param requestBody 请求体，包含names字段
     * @return
     */
    @AutoLog(value = "危害因素-根据名称获取")
    @ApiOperation(value = "危害因素-根据名称获取", notes = "危害因素-根据名称获取")
    @PostMapping(value = "/getByNames")
    public Result<List<ZyRiskFactorAutoCompleteDTO>> getByNames(@RequestBody Map<String, Object> requestBody) {
        
        try {
            @SuppressWarnings("unchecked")
            List<String> names = (List<String>) requestBody.get("names");
            
            if (names == null || names.isEmpty()) {
                return Result.error("名称列表不能为空");
            }
            
            log.info("根据名称列表获取危害因素，数量: {}", names.size());
            
            List<ZyRiskFactorAutoCompleteDTO> result = zyRiskFactorService.getByNames(names);
            log.info("根据名称列表获取危害因素完成，返回 {} 条数据", result.size());
            
            return Result.OK(result);
        } catch (Exception e) {
            log.error("根据名称列表获取危害因素失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }
}
