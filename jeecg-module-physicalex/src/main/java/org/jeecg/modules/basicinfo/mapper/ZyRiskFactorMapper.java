package org.jeecg.modules.basicinfo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import org.jeecg.modules.basicinfo.entity.ZyRiskFactor;
import org.jeecg.modules.basicinfo.dto.ZyRiskFactorAutoCompleteDTO;

import java.util.List;

/**
 * 危害因素 Mapper 接口
 */
@Mapper
@Repository
public interface ZyRiskFactorMapper extends BaseMapper<ZyRiskFactor> {

    /**
     * 自动完成查询
     * @param keyword 搜索关键词
     * @param searchType 搜索类型
     * @param pageSize 返回数量限制
     * @return 危害因素列表
     */
    @Select("<script>" +
            "SELECT id, name, code, help_char as help<PERSON>har, wubi_char as wubi<PERSON>har, " +
            "type, category, harm_level as harmLevel, sort_no as sortNo " +
            "FROM zy_risk_factor " +
            "WHERE del_flag = '0' AND enabled = 1 " +
            "<if test='keyword != null and keyword != \"\"'>" +
            "  <choose>" +
            "    <when test='searchType == \"name\"'>" +
            "      AND name LIKE CONCAT('%', #{keyword}, '%')" +
            "    </when>" +
            "    <when test='searchType == \"code\"'>" +
            "      AND code LIKE CONCAT('%', #{keyword}, '%')" +
            "    </when>" +
            "    <when test='searchType == \"helpChar\"'>" +
            "      AND help_char LIKE CONCAT('%', #{keyword}, '%')" +
            "    </when>" +
            "    <when test='searchType == \"wubiChar\"'>" +
            "      AND wubi_char LIKE CONCAT('%', #{keyword}, '%')" +
            "    </when>" +
            "    <otherwise>" +
            "      AND (name LIKE CONCAT('%', #{keyword}, '%') " +
            "           OR code LIKE CONCAT('%', #{keyword}, '%') " +
            "           OR help_char LIKE CONCAT('%', #{keyword}, '%') " +
            "           OR wubi_char LIKE CONCAT('%', #{keyword}, '%'))" +
            "    </otherwise>" +
            "  </choose>" +
            "</if>" +
            "ORDER BY " +
            "<if test='keyword != null and keyword != \"\"'>" +
            "  CASE " +
            "    WHEN name = #{keyword} THEN 1 " +
            "    WHEN name LIKE CONCAT(#{keyword}, '%') THEN 2 " +
            "    WHEN code = #{keyword} THEN 3 " +
            "    WHEN help_char = #{keyword} THEN 4 " +
            "    WHEN wubi_char = #{keyword} THEN 5 " +
            "    ELSE 6 " +
            "  END, " +
            "</if>" +
            "sort_no ASC, name ASC " +
            "LIMIT #{pageSize}" +
            "</script>")
    List<ZyRiskFactorAutoCompleteDTO> getAutoCompleteList(
            @Param("keyword") String keyword,
            @Param("searchType") String searchType,
            @Param("pageSize") Integer pageSize
    );

    /**
     * 精确匹配危害因素名称
     * @param name 危害因素名称
     * @return 危害因素信息
     */
    @Select("SELECT id, name, code, help_char as helpChar, wubi_char as wubiChar, " +
            "type, category, harm_level as harmLevel, sort_no as sortNo " +
            "FROM zy_risk_factor " +
            "WHERE del_flag = '0' AND enabled = 1 AND name = #{name} " +
            "ORDER BY sort_no ASC " +
            "LIMIT 1")
    ZyRiskFactorAutoCompleteDTO getByExactName(@Param("name") String name);

    /**
     * 根据名称列表获取危害因素
     * @param names 危害因素名称列表
     * @return 危害因素列表
     */
    @Select("<script>" +
            "SELECT id, name, code, help_char as helpChar, wubi_char as wubiChar, " +
            "type, category, harm_level as harmLevel, sort_no as sortNo " +
            "FROM zy_risk_factor " +
            "WHERE del_flag = '0' AND enabled = 1 " +
            "AND name IN " +
            "<foreach collection='names' item='name' open='(' separator=',' close=')'>" +
            "  #{name}" +
            "</foreach>" +
            "ORDER BY sort_no ASC, name ASC" +
            "</script>")
    List<ZyRiskFactorAutoCompleteDTO> getByNames(@Param("names") List<String> names);
}
