package org.jeecg.modules.basicinfo.controller;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 危害因素测试控制器 - 用于快速验证接口
 */
@RestController
@RequestMapping("/basicinfo/zyRiskFactor")
@Slf4j
public class ZyRiskFactorTestController {

    /**
     * 测试接口 - 返回模拟数据
     */
    @GetMapping(value = "/test")
    public Result<String> test() {
        log.info("危害因素测试接口被调用");
        return Result.OK("危害因素接口正常工作！");
    }

    /**
     * 自动完成接口 - 返回模拟数据
     */
    @GetMapping(value = "/autoComplete")
    public Result<List<Map<String, Object>>> autoComplete(
            @RequestParam(required = false) String keyword,
            @RequestParam(defaultValue = "50") Integer pageSize,
            @RequestParam(defaultValue = "all") String searchType) {
        
        log.info("危害因素自动完成查询，关键词: {}, 数量限制: {}, 搜索类型: {}", keyword, pageSize, searchType);
        
        List<Map<String, Object>> result = new ArrayList<>();
        
        // 模拟数据
        String[] names = {"噪声", "粉尘", "高温", "振动", "苯", "甲醛", "铅", "紫外线", "一氧化碳", "手动阀"};
        String[] codes = {"ZS001", "FC002", "GW003", "ZD004", "B005", "JQ006", "Q007", "ZWX008", "YHTWT009", "SDV010"};
        String[] helpChars = {"ZS", "FC", "GW", "ZD", "B", "JQ", "Q", "ZWX", "YHTW", "SDV"};
        
        for (int i = 0; i < names.length && i < pageSize; i++) {
            // 如果有关键词，进行简单过滤
            if (keyword != null && !keyword.trim().isEmpty()) {
                String kw = keyword.trim().toLowerCase();
                if (!names[i].toLowerCase().contains(kw) && 
                    !codes[i].toLowerCase().contains(kw) && 
                    !helpChars[i].toLowerCase().contains(kw)) {
                    continue;
                }
            }
            
            Map<String, Object> item = new HashMap<>();
            item.put("id", String.valueOf(i + 1));
            item.put("name", names[i]);
            item.put("code", codes[i]);
            item.put("helpChar", helpChars[i]);
            item.put("wubiChar", "WUBI" + (i + 1));
            item.put("type", i % 2 == 0 ? "物理因素" : "化学因素");
            item.put("category", names[i]);
            item.put("harmLevel", i % 3 == 0 ? "高" : (i % 3 == 1 ? "中等" : "低"));
            item.put("sortNo", i + 1);
            
            result.add(item);
        }
        
        log.info("危害因素自动完成查询成功，返回 {} 条数据", result.size());
        return Result.OK(result);
    }

    /**
     * 列表查询接口 - 返回模拟数据
     */
    @GetMapping(value = "/list")
    public Result<Map<String, Object>> list(
            @RequestParam(defaultValue = "1") Integer pageNo,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        
        log.info("危害因素列表查询，页码: {}, 页大小: {}", pageNo, pageSize);
        
        List<Map<String, Object>> records = new ArrayList<>();
        
        // 模拟分页数据
        String[] names = {"噪声", "粉尘", "高温", "振动", "苯", "甲醛", "铅", "紫外线", "一氧化碳", "手动阀"};
        
        int start = (pageNo - 1) * pageSize;
        int end = Math.min(start + pageSize, names.length);
        
        for (int i = start; i < end; i++) {
            Map<String, Object> record = new HashMap<>();
            record.put("id", String.valueOf(i + 1));
            record.put("name", names[i]);
            record.put("code", "CODE" + String.format("%03d", i + 1));
            record.put("helpChar", names[i].substring(0, Math.min(2, names[i].length())));
            record.put("type", i % 2 == 0 ? "物理因素" : "化学因素");
            record.put("status", "1");
            record.put("enabled", true);
            record.put("createTime", "2024-12-24 10:00:00");
            
            records.add(record);
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("records", records);
        result.put("total", names.length);
        result.put("current", pageNo);
        result.put("size", pageSize);
        result.put("pages", (names.length + pageSize - 1) / pageSize);
        
        log.info("危害因素列表查询成功，返回 {} 条数据", records.size());
        return Result.OK(result);
    }

    /**
     * 精确匹配接口
     */
    @GetMapping(value = "/exactMatch")
    public Result<Map<String, Object>> exactMatch(@RequestParam String name) {
        log.info("危害因素精确匹配，名称: {}", name);
        
        // 模拟精确匹配
        if ("手动阀".equals(name)) {
            Map<String, Object> result = new HashMap<>();
            result.put("id", "10");
            result.put("name", "手动阀");
            result.put("code", "SDV010");
            result.put("helpChar", "SDV");
            result.put("wubiChar", "RJHH");
            result.put("type", "人体工效学");
            result.put("category", "重复性动作");
            result.put("harmLevel", "低");
            result.put("sortNo", 10);
            
            return Result.OK(result);
        }
        
        return Result.error("未找到匹配的危害因素");
    }

    /**
     * 批量验证接口
     */
    @PostMapping(value = "/batchValidate")
    public Result<Map<String, Object>> batchValidate(@RequestBody Map<String, Object> requestBody) {
        @SuppressWarnings("unchecked")
        List<String> names = (List<String>) requestBody.get("names");
        
        log.info("危害因素批量验证，数量: {}", names != null ? names.size() : 0);
        
        List<Map<String, Object>> results = new ArrayList<>();
        List<Map<String, Object>> matchedFactors = new ArrayList<>();
        int successCount = 0;
        
        if (names != null) {
            for (String name : names) {
                Map<String, Object> result = new HashMap<>();
                result.put("inputName", name);
                
                // 模拟验证逻辑
                if ("手动阀".equals(name) || "噪声".equals(name) || "粉尘".equals(name)) {
                    result.put("found", true);
                    
                    Map<String, Object> matched = new HashMap<>();
                    matched.put("id", String.valueOf(successCount + 1));
                    matched.put("name", name);
                    matched.put("code", "CODE" + String.format("%03d", successCount + 1));
                    matched.put("helpChar", name.substring(0, Math.min(2, name.length())));
                    
                    result.put("matchedFactor", matched);
                    matchedFactors.add(matched);
                    successCount++;
                } else {
                    result.put("found", false);
                    result.put("errorMessage", "未找到匹配的危害因素");
                }
                
                results.add(result);
            }
        }
        
        Map<String, Object> response = new HashMap<>();
        response.put("results", results);
        response.put("successCount", successCount);
        response.put("failureCount", (names != null ? names.size() : 0) - successCount);
        response.put("matchedFactors", matchedFactors);
        
        return Result.OK(response);
    }
}
