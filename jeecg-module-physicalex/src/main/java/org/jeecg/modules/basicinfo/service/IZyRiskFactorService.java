package org.jeecg.modules.basicinfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.basicinfo.entity.ZyRiskFactor;
import org.jeecg.modules.basicinfo.dto.ZyRiskFactorAutoCompleteDTO;

import java.util.List;

/**
 * 危害因素 Service 接口
 */
public interface IZyRiskFactorService extends IService<ZyRiskFactor> {

    /**
     * 获取自动完成列表
     * @param keyword 搜索关键词
     * @param searchType 搜索类型
     * @param pageSize 返回数量限制
     * @return 危害因素列表
     */
    List<ZyRiskFactorAutoCompleteDTO> getAutoCompleteList(String keyword, String searchType, Integer pageSize);

    /**
     * 精确匹配危害因素名称
     * @param name 危害因素名称
     * @return 危害因素信息
     */
    ZyRiskFactorAutoCompleteDTO getByExactName(String name);

    /**
     * 根据名称列表获取危害因素
     * @param names 危害因素名称列表
     * @return 危害因素列表
     */
    List<ZyRiskFactorAutoCompleteDTO> getByNames(List<String> names);

    /**
     * 批量验证危害因素名称
     * @param names 危害因素名称列表
     * @return 验证结果
     */
    BatchValidationResult batchValidateNames(List<String> names);

    /**
     * 批量验证结果类
     */
    class BatchValidationResult {
        private List<ValidationResult> results;
        private Integer successCount;
        private Integer failureCount;
        private List<ZyRiskFactorAutoCompleteDTO> matchedFactors;

        // 构造函数
        public BatchValidationResult() {}

        public BatchValidationResult(List<ValidationResult> results, Integer successCount, 
                                   Integer failureCount, List<ZyRiskFactorAutoCompleteDTO> matchedFactors) {
            this.results = results;
            this.successCount = successCount;
            this.failureCount = failureCount;
            this.matchedFactors = matchedFactors;
        }

        // Getters and Setters
        public List<ValidationResult> getResults() { return results; }
        public void setResults(List<ValidationResult> results) { this.results = results; }

        public Integer getSuccessCount() { return successCount; }
        public void setSuccessCount(Integer successCount) { this.successCount = successCount; }

        public Integer getFailureCount() { return failureCount; }
        public void setFailureCount(Integer failureCount) { this.failureCount = failureCount; }

        public List<ZyRiskFactorAutoCompleteDTO> getMatchedFactors() { return matchedFactors; }
        public void setMatchedFactors(List<ZyRiskFactorAutoCompleteDTO> matchedFactors) { this.matchedFactors = matchedFactors; }
    }

    /**
     * 验证结果类
     */
    class ValidationResult {
        private String inputName;
        private Boolean found;
        private ZyRiskFactorAutoCompleteDTO matchedFactor;
        private String errorMessage;

        // 构造函数
        public ValidationResult() {}

        public ValidationResult(String inputName, Boolean found, 
                              ZyRiskFactorAutoCompleteDTO matchedFactor, String errorMessage) {
            this.inputName = inputName;
            this.found = found;
            this.matchedFactor = matchedFactor;
            this.errorMessage = errorMessage;
        }

        // Getters and Setters
        public String getInputName() { return inputName; }
        public void setInputName(String inputName) { this.inputName = inputName; }

        public Boolean getFound() { return found; }
        public void setFound(Boolean found) { this.found = found; }

        public ZyRiskFactorAutoCompleteDTO getMatchedFactor() { return matchedFactor; }
        public void setMatchedFactor(ZyRiskFactorAutoCompleteDTO matchedFactor) { this.matchedFactor = matchedFactor; }

        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }
}
