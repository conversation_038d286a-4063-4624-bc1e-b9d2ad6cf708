package org.jeecg.modules.basicinfo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 危害因素信息
 */
@Data
@TableName("zy_risk_factor")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "zy_risk_factor对象", description = "危害因素信息")
public class ZyRiskFactor implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 危害因素名称
     */
    @Excel(name = "危害因素名称", width = 15)
    @ApiModelProperty(value = "危害因素名称")
    private String name;

    /**
     * 危害因素编码
     */
    @Excel(name = "危害因素编码", width = 15)
    @ApiModelProperty(value = "危害因素编码")
    private String code;

    /**
     * 助记码
     */
    @Excel(name = "助记码", width = 15)
    @ApiModelProperty(value = "助记码")
    private String helpChar;

    /**
     * 五笔码
     */
    @Excel(name = "五笔码", width = 15)
    @ApiModelProperty(value = "五笔码")
    private String wubiChar;

    /**
     * 危害因素类型
     */
    @Excel(name = "危害因素类型", width = 15)
    @ApiModelProperty(value = "危害因素类型")
    private String type;

    /**
     * 危害因素分类
     */
    @Excel(name = "危害因素分类", width = 15)
    @ApiModelProperty(value = "危害因素分类")
    private String category;

    /**
     * 危害等级
     */
    @Excel(name = "危害等级", width = 15)
    @ApiModelProperty(value = "危害等级")
    private String harmLevel;

    /**
     * 检测方法
     */
    @Excel(name = "检测方法", width = 15)
    @ApiModelProperty(value = "检测方法")
    private String detectionMethod;

    /**
     * 职业接触限值
     */
    @Excel(name = "职业接触限值", width = 15)
    @ApiModelProperty(value = "职业接触限值")
    private String occupationalLimit;

    /**
     * 健康影响
     */
    @Excel(name = "健康影响", width = 15)
    @ApiModelProperty(value = "健康影响")
    private String healthEffect;

    /**
     * 防护措施
     */
    @Excel(name = "防护措施", width = 15)
    @ApiModelProperty(value = "防护措施")
    private String protectiveMeasures;

    /**
     * 状态
     */
    @Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    @ApiModelProperty(value = "删除标志")
    private String delFlag;

    /**
     * 备注
     */
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 排序号
     */
    @Excel(name = "排序号", width = 15)
    @ApiModelProperty(value = "排序号")
    private Integer sortNo;

    /**
     * 是否启用
     */
    @Excel(name = "是否启用", width = 15)
    @ApiModelProperty(value = "是否启用")
    private Boolean enabled;
}
