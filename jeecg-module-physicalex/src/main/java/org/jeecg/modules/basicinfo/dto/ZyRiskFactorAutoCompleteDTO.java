package org.jeecg.modules.basicinfo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 危害因素自动完成DTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ZyRiskFactorAutoCompleteDTO", description = "危害因素自动完成DTO")
public class ZyRiskFactorAutoCompleteDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 危害因素ID
     */
    @ApiModelProperty(value = "危害因素ID")
    private String id;

    /**
     * 危害因素名称
     */
    @ApiModelProperty(value = "危害因素名称")
    private String name;

    /**
     * 危害因素编码
     */
    @ApiModelProperty(value = "危害因素编码")
    private String code;

    /**
     * 助记码
     */
    @ApiModelProperty(value = "助记码")
    private String helpChar;

    /**
     * 五笔码
     */
    @ApiModelProperty(value = "五笔码")
    private String wubiChar;

    /**
     * 危害因素类型
     */
    @ApiModelProperty(value = "危害因素类型")
    private String type;

    /**
     * 危害因素分类
     */
    @ApiModelProperty(value = "危害因素分类")
    private String category;

    /**
     * 危害等级
     */
    @ApiModelProperty(value = "危害等级")
    private String harmLevel;

    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer sortNo;
}
