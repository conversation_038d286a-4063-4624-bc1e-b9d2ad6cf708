package org.jeecg.modules.fee.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.fee.service.ICompanyPaymentStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * 团体支付统计分析控制器
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Api(tags = "团体支付统计分析")
@RestController
@RequestMapping("/fee/companyPaymentStatistics")
@Slf4j
public class CompanyPaymentStatisticsController {
    
    @Autowired
    private ICompanyPaymentStatisticsService statisticsService;
    
    @GetMapping("/exportBasic")
    @ApiOperation("导出基础统计")
    @AutoLog(value = "导出基础统计")
    @RequiresPermissions("fee:companyPaymentStatistics:exportBasic")
    public void exportBasicStatistics(
            @RequestParam String companyRegId,
            @RequestParam(required = false, defaultValue = "basic") String exportType,
            @RequestParam(required = false, defaultValue = "true") Boolean enableMasking,
            @RequestParam(required = false, defaultValue = "excel") String format,
            HttpServletResponse response) {

        try {
            log.info("导出基础统计，单位ID: {}, 导出类型: {}, 脱敏: {}", companyRegId, exportType, enableMasking);

            statisticsService.exportBasicStatistics(companyRegId, enableMasking, format, response);

            log.info("导出基础统计完成，单位ID: {}", companyRegId);

        } catch (Exception e) {
            log.error("导出基础统计失败，单位ID: {}", companyRegId, e);
            throw new RuntimeException("导出失败：" + e.getMessage());
        }
    }

    @GetMapping("/exportDetailed")
    @ApiOperation("导出详细报告")
    @AutoLog(value = "导出详细报告")
    @RequiresPermissions("fee:companyPaymentStatistics:exportDetailed")
    public void exportDetailedReport(
            @RequestParam String companyRegId,
            @RequestParam(required = false, defaultValue = "detailed") String exportType,
            @RequestParam(required = false, defaultValue = "true") Boolean enableMasking,
            @RequestParam(required = false, defaultValue = "true") Boolean includeSubstitute,
            @RequestParam(required = false, defaultValue = "excel") String format,
            HttpServletResponse response) {

        try {
            log.info("导出详细报告，单位ID: {}, 脱敏: {}, 包含替检: {}", companyRegId, enableMasking, includeSubstitute);

            statisticsService.exportDetailedReport(companyRegId, enableMasking, includeSubstitute, format, response);

            log.info("导出详细报告完成，单位ID: {}", companyRegId);

        } catch (Exception e) {
            log.error("导出详细报告失败，单位ID: {}", companyRegId, e);
            throw new RuntimeException("导出失败：" + e.getMessage());
        }
    }

    @GetMapping("/exportPaymentRecords")
    @ApiOperation("导出支付记录")
    @AutoLog(value = "导出支付记录")
    @RequiresPermissions("fee:companyPaymentStatistics:exportPaymentRecords")
    public void exportPaymentRecords(
            @RequestParam String companyRegId,
            @RequestParam(required = false, defaultValue = "payment") String exportType,
            @RequestParam(required = false, defaultValue = "true") Boolean enableMasking,
            @RequestParam(required = false, defaultValue = "true") Boolean includeSubstitute,
            @RequestParam(required = false, defaultValue = "excel") String format,
            HttpServletResponse response) {

        try {
            log.info("导出支付记录，单位ID: {}, 脱敏: {}, 包含替检: {}", companyRegId, enableMasking, includeSubstitute);

            statisticsService.exportPaymentRecords(companyRegId, enableMasking, includeSubstitute, format, response);

            log.info("导出支付记录完成，单位ID: {}", companyRegId);

        } catch (Exception e) {
            log.error("导出支付记录失败，单位ID: {}", companyRegId, e);
            throw new RuntimeException("导出失败：" + e.getMessage());
        }
    }
}
}
