package org.jeecg.modules.fee.vo;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 单位支付统计详情VO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Builder
public class CompanyPaymentStatisticsDetail {
    
    /**
     * 基础统计信息
     */
    private CompanyPaymentStatistics statistics;
    
    /**
     * 替检详情列表
     */
    private List<SubstituteDetail> substituteDetails;
    
    /**
     * 支付记录详情列表
     */
    private List<PaymentRecordDetail> paymentRecords;
}
