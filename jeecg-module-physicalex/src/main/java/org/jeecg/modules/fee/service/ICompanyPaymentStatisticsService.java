package org.jeecg.modules.fee.service;

import javax.servlet.http.HttpServletResponse;

/**
 * 团体支付统计分析服务接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface ICompanyPaymentStatisticsService {

    /**
     * 导出基础统计
     *
     * @param companyRegId 单位预约ID
     * @param enableMasking 是否启用数据脱敏
     * @param format 导出格式
     * @param response HTTP响应
     */
    void exportBasicStatistics(String companyRegId, Boolean enableMasking, String format, HttpServletResponse response);

    /**
     * 导出详细报告
     *
     * @param companyRegId 单位预约ID
     * @param enableMasking 是否启用数据脱敏
     * @param includeSubstitute 是否包含替检数据
     * @param format 导出格式
     * @param response HTTP响应
     */
    void exportDetailedReport(String companyRegId, Boolean enableMasking, Boolean includeSubstitute, String format, HttpServletResponse response);

    /**
     * 导出支付记录
     *
     * @param companyRegId 单位预约ID
     * @param enableMasking 是否启用数据脱敏
     * @param includeSubstitute 是否包含替检数据
     * @param format 导出格式
     * @param response HTTP响应
     */
    void exportPaymentRecords(String companyRegId, Boolean enableMasking, Boolean includeSubstitute, String format, HttpServletResponse response);
    
    /**
     * 异步导出体检登记名单
     * 
     * @param exportRequest 导出请求
     * @return 任务信息
     */
    Object asyncExportRegistrationList(Object exportRequest);
    
    /**
     * 批量导出体检登记名单
     * 
     * @param batchExportRequest 批量导出请求
     * @param response HTTP响应
     */
    void batchExportRegistrationList(Object batchExportRequest, HttpServletResponse response);
    
    /**
     * 获取导出任务状态
     * 
     * @param taskId 任务ID
     * @return 任务状态
     */
    Object getExportTaskStatus(String taskId);
    
    /**
     * 取消导出任务
     * 
     * @param taskId 任务ID
     * @return 是否成功
     */
    boolean cancelExportTask(String taskId);
    
    /**
     * 下载导出文件
     * 
     * @param taskId 任务ID
     * @param response HTTP响应
     */
    void downloadExportFile(String taskId, HttpServletResponse response);
}
