package org.jeecg.modules.reg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.system.entity.SysDepart;

import java.util.List;

/**
 * @Description: 客户登记
 * @Author: jeecg-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
public interface CustomerRegMapper extends BaseMapper<CustomerReg> {

    List<CustomerRegItemGroup> getItemGroupOfCustomer(@Param("customerRegId") String customerRegId);

    List<CustomerRegItemGroup> list4GuidanceSheet(@Param("customerRegId") String customerRegId);

    Page<CustomerReg> pageCustomerReg(Page<CustomerReg> page, @Param("idCard") String idCard, @Param("examNo") String examNo, @Param("dateType") String dateType, @Param("regTimeStart") String regTimeStart, @Param("regTimeEnd") String regTimeEnd, @Param("companyRegId") String companyRegId, @Param("teamId") String teamId, @Param("name") String name, @Param("status") String status, @Param("retrieveStatus") String retrieveStatus, @Param("emplyee") String emplyee, @Param("paymentState") String paymentState, @Param("companyNotifyFlag") String companyNotifyFlag, @Param("customerId") String customerId,@Param("examCategory") String examCategory,@Param("examCatoryOperator")String examCatoryOperator);

    @Select("org.jeecg.modules.reg.mapper.CustomerRegMapper.pageCustomerReg")
    List<CustomerReg> listCustomerReg(@Param("idCard") String idCard, @Param("examNo") String examNo, @Param("regTimeStart") String regTimeStart, @Param("regTimeEnd") String regTimeEnd, @Param("companyRegId") String companyRegId, @Param("teamId") String teamId, @Param("name") String name, @Param("status") String status, @Param("retrieveStatus") String retrieveStatus, @Param("emplyee") String emplyee, @Param("paymentState") String paymentState);

    List<SysDepart> getDepartsByUser(String userId);

    List<CustomerReg> getCustomerRegsByCompanyRegId(@Param("companyRegId") String companyRegId,@Param("companyDeptId") String companyDeptId,@Param("jobStatus") String jobStatus);

    public List<CustomerReg> listByEReportStatus(@Param("eReportStatus") String eReportStatus,@Param("paperReportStatus") String paperReportStatus ,@Param("limit") Integer limit);

    CustomerReg getLiteById(String id);

    List<CustomerReg> getLastNYearsReg(@Param("idCard") String idCard, @Param("years") int years);

    List<CustomerReg> getLastNYearsRegByCustomer(@Param("customerId") String customerId, @Param("status") String status, @Param("years") int years);

    CustomerReg selectLatestReg(@Param("customerId") String customerId);

    CustomerReg selectLatestRegByTeamIdAndIdCard(@Param("teamId") String teamId, @Param("idCard") String idCard);

    List<CustomerReg> selectRelationRegsByOriginCustomerIdAndTeamId(@Param("originCustomerId") String originRegId,@Param("originTeamId") String originTeamId);

    List<CustomerReg> selectOriginRegsByRelationCustomerAccountId(@Param("relationCustomerAccountId") String relationCustomerAccountId);

    List<CustomerReg> getRegListByIdCardOrExamNo(@Param("idCard")String idCard, @Param("examNo")String examNo);

    List<CustomerReg> getRegList4ReportByIdCardOrExamNo(@Param("idCard")String idCard, @Param("examNo")String examNo,@Param("status")String status);

    Page<CustomerReg> pageCustomerReg4Occu(Page<CustomerReg> page, @Param("idCard") String idCard,@Param("examNo") String examNo, @Param("dateType")String dateType,@Param("regTimeStart") String regTimeStart,@Param("regTimeEnd") String regTimeEnd,@Param("companyRegId") String companyRegId, @Param("teamId")String teamId, @Param("name")String name,@Param("status") String status, @Param("retrieveStatus")String retrieveStatus , @Param("emplyee")String emplyee, @Param("paymentState")String paymentState, @Param("companyNotifyFlag")String companyNotifyFlag,@Param("customerId") String customerId, @Param("riskFactor")String riskFactor, @Param("examCategory")String examCategory, @Param("occuReportResultStatus")String occuReportResultStatus, @Param("occuReportUploadTimeStart")String occuReportUploadTimeStart, @Param("occuReportUploadTimeEnd")String occuReportUploadTimeEnd, @Param("summaryStatus")String summaryStatus);
}
