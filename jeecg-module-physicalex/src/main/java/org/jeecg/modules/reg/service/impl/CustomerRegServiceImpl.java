package org.jeecg.modules.reg.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.mzlion.easyokhttp.HttpClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.CommonAPI;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.MinioUtil;
import org.jeecg.excommons.BatchResult;
import org.jeecg.excommons.ExApiConstants;
import org.jeecg.excommons.ExConstants;
import org.jeecg.excommons.jms.JmsMessageSender;
import org.jeecg.excommons.utils.DateFormatUtils;
import org.jeecg.excommons.utils.IdCardUtils;
import org.jeecg.excommons.utils.MustacheUtil;
import org.jeecg.excommons.utils.SequenceGenerator;
import org.jeecg.modules.appointment.entity.CustomerOrder;
import org.jeecg.modules.basicinfo.entity.*;
import org.jeecg.modules.basicinfo.entity.CheckPartDict;
import org.jeecg.modules.basicinfo.mapper.ItemGroupMapper;
import org.jeecg.modules.basicinfo.mapper.ItemInfoMapper;
import org.jeecg.modules.basicinfo.mapper.ItemSuitMapper;
import org.jeecg.modules.basicinfo.mapper.PccaMapper;
import org.jeecg.modules.basicinfo.service.IAutoSmsSettingService;
import org.jeecg.modules.basicinfo.service.ICheckPartDictService;
import org.jeecg.modules.basicinfo.service.ICompanyService;
import org.jeecg.modules.basicinfo.service.IItemGroupRelationService;
import org.jeecg.modules.basicinfo.service.IItemGroupService;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecg.modules.fee.bo.PaymentAnalysis;
import org.jeecg.modules.occu.entity.ZyRiskFactor;
import org.jeecg.modules.occu.entity.ZyRiskFactorItemgroup;
import org.jeecg.modules.occu.service.IZyRiskFactorItemgroupService;
import org.jeecg.modules.occu.service.IZyRiskFactorService;
import org.jeecg.modules.reg.dto.AddItemGroupWithCheckPartsRequest;
import org.jeecg.modules.reg.dto.DependentItemResultDTO;
import org.jeecg.modules.fee.entity.CustomerRegBill;
import org.jeecg.modules.fee.entity.FeePayRecord;
import org.jeecg.modules.fee.entity.LimitOperationRecord;
import org.jeecg.modules.fee.entity.TeamCustomerLimitAmount;
import org.jeecg.modules.fee.mapper.LimitOperationRecordMapper;
import org.jeecg.modules.fee.mapper.TeamCustomerLimitAmountMapper;
import org.jeecg.modules.fee.service.ICustomerRegBillService;
import org.jeecg.modules.fee.service.ITeamCustomerLimitAmountService;
import org.jeecg.modules.psy.entity.PsyCard;
import org.jeecg.modules.psy.entity.PsyCustomer;
import org.jeecg.modules.psy.service.IPsyCardService;
import org.jeecg.modules.recheck.entity.RecheckNotify;
import org.jeecg.modules.recheck.mapper.RecheckNotifyMapper;
import org.jeecg.modules.reg.bo.*;
import org.jeecg.modules.reg.entity.*;
import org.jeecg.modules.reg.mapper.*;
import org.jeecg.modules.reg.service.*;
import org.jeecg.modules.sms.bo.SmsResult;
import org.jeecg.modules.sms.service.ISmsRecordsService;
import org.jeecg.modules.station.entity.CustomerRegItemResult;
import org.jeecg.modules.station.service.ICustomerRegItemResultService;
import org.jeecg.modules.summary.entity.CustomerRegSummary;
import org.jeecg.modules.summary.entity.ReportActionRecord;
import org.jeecg.modules.summary.mapper.CustomerRegSummaryMapper;
import org.jeecg.modules.summary.mapper.ReportActionRecordMapper;
import org.jeecg.modules.summary.service.PdfGeneratorService;
import org.jeecg.modules.survey.entity.DiseaseSurveyAnswer;
import org.jeecg.modules.survey.mapper.DiseaseSurveyAnswerMapper;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.entity.SysDictItem;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.mapper.SysDepartMapper;
import org.jeecg.modules.system.mapper.SysDictItemMapper;
import org.jeecg.modules.system.mapper.SysUserMapper;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.mobile.auth.service.WxApiService;
import org.jeecg.modules.mobile.bo.ShortLinkRequest;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 客户登记
 * @Author: jeecg-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
@Service
@Slf4j
public class CustomerRegServiceImpl extends ServiceImpl<CustomerRegMapper, CustomerReg> implements ICustomerRegService {

    @Resource
    private CustomerRegMapper customerRegMapper;
    @Resource
    private CompanyTeamMapper companyTeamMapper;
    @Resource
    private CompanyRegMapper companyRegMapper;
    @Resource
    private CommonAPI commonAPI;
    @Resource
    private JdbcTemplate jdbcTemplate;
    @Resource
    private SysDepartMapper sysDepartMapper;
    @Resource
    private ICustomerRegItemGroupService customerRegItemGroupService;
    @Resource
    private SequenceGenerator sequenceGenerator;
    @Resource
    private ICustomerService customerService;
    @Resource
    private JmsMessageSender jmsMessageSender;
    @Resource
    private ISysSettingService sysSettingService;
    @Resource
    private DiseaseSurveyAnswerMapper diseaseSurveyAnswerMapper;
    @Resource
    private ItemInfoMapper itemInfoMapper;
    @Resource
    private CustomerMapper customerMapper;
    @Resource
    private ItemSuitMapper itemSuitMapper;
    @Resource
    private CustomerRegSummaryMapper customerRegSummaryMapper;
    @Resource
    private ReportActionRecordMapper reportActionRecordMapper;
    @Resource
    private CompanyTeamItemGroupMapper companyTeamItemGroupMapper;
    @Resource
    private IAutoSmsSettingService autoSmsSettingService;
    @Resource
    private ISmsRecordsService smsRecordsService;
    @Resource
    private RecheckNotifyMapper recheckNotifyMapper;
    @Resource
    private WxApiService wxApiService;
    @Resource
    private IItemGroupRelationService itemGroupRelationService;
    @Resource
    private ItemGroupMapper itemGroupMapper;
    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    private PccaMapper pccaMapper;
    @Resource
    private ISysUserService sysUserService;
    @Resource
    private ICustomerRegBillService billService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private CustomerRegBarcodeMapper customerRegBarcodeMapper;
    @Resource
    private ICustomerRegBarcodeService customerRegBarcodeService;
    @Resource
    private ICompanyImportRecordService companyImportRecordService;
    @Resource
    private IPsyCardService psyCardService;
    @Resource
    private ICustomerRegBillService customerRegBillService;
    @Resource
    private ICompanyService companyService;
    @Resource
    private PdfGeneratorService pdfGeneratorService;
    @Resource
    private LimitOperationRecordMapper limitOperationRecordMapper;
    @Resource
    private ITeamCustomerLimitAmountService teamCustomerLimitAmountService;
    @Resource
    private TeamCustomerLimitAmountMapper teamCustomerLimitAmountMapper;
    @Resource
    private CustomerRegItemGroupMapper customerRegItemGroupMapper;
    @Resource
    private SysDictItemMapper sysDictItemMapper;
    @Resource
    private ICustomerRegItemResultService customerRegItemResultService;
    @Resource
    private ICheckPartDictService checkPartDictService;
    @Resource
    private IItemGroupService itemGroupService;
    @Resource
    private IZyRiskFactorItemgroupService riskFactorItemgroupService;
    @Resource
    private IZyRiskFactorService riskFactorService;

    // 危害因素缓存，避免频繁查询数据库
    private volatile Map<String, String> riskFactorNameToCodeCache = null;
    private volatile Map<String, String> riskFactorCodeToCodeCache = null;
    private volatile long riskFactorCacheTimestamp = 0;
    private static final long CACHE_EXPIRE_TIME = 30 * 60 * 1000; // 30分钟缓存过期


    @Resource
    private RedisTemplate<String, String> redisTemplate;
    private static final String REQUEST_COUNT_KEY_PREFIX = "customerRegRequestCount:";

    @Override
    public Page<CustomerReg> pageCustomerReg(Page<CustomerReg> page, String idCard, String examNo, String dateType, String regTimeStart, String regTimeEnd, String companyRegId, String teamId, String name, String status, String retreiveStatus, String emplyee, String paymentState, String companyNotifyFlag, String customerId,String examCategory,String examCategoryOperator) {

        customerRegMapper.pageCustomerReg(page, idCard, examNo, dateType, regTimeStart, regTimeEnd, companyRegId, teamId, name, status, retreiveStatus, emplyee, paymentState, companyNotifyFlag, customerId,examCategory,examCategoryOperator);

        page.getRecords().forEach(customerReg -> {
            customerReg.setPaymentState(statPayStatus4Reg(customerReg.getId()));
            if (customerReg.getAge() == null) {
                Date birthday = customerReg.getBirthday();
                if (birthday != null) {
                    customerReg.setAge(DateUtil.ageOfNow(birthday));
                }
            }

            if (StringUtils.isNotBlank(customerReg.getCompanyRegId())) {
                CompanyReg companyReg = companyRegMapper.selectById(customerReg.getCompanyRegId());
                if (companyReg != null) {
                    customerReg.setCompanyReg(companyReg);
                }
            }
      /*      if (StringUtils.isNotBlank(customerReg.getOriginCustomerLimitAmountId())) {
                TeamCustomerLimitAmount customerLimitAmount = teamCustomerLimitAmountMapper.getCustomerLimitAmountById(customerReg.getOriginCustomerLimitAmountId());
                if (customerLimitAmount != null) {
                    customerReg.setTeamId(customerLimitAmount.getTeamId());
                    customerReg.setLimitAmount(customerLimitAmount.getTeamLimitAmount());
                }
            }else{

                CompanyTeam companyTeam = companyTeamMapper.selectById(customerReg.getTeamId());
                if (Objects.nonNull(companyTeam) && Objects.nonNull(companyTeam.getLimitAmount()) && BigDecimal.ZERO.compareTo(companyTeam.getLimitAmount()) != 0) {
                    TeamCustomerLimitAmount customerLimitAmount = teamCustomerLimitAmountMapper.getLatestLimit(customerReg.getTeamId(),customerReg.getCustomerId(),null);
//                    TeamCustomerLimitAmount customerLimitAmount = teamCustomerLimitAmountMapper.selectOne(new LambdaQueryWrapper<TeamCustomerLimitAmount>().eq(TeamCustomerLimitAmount::getCustomerId, customerReg.getCustomerId()).eq(TeamCustomerLimitAmount::getTeamId, customerReg.getTeamId()).orderByDesc(TeamCustomerLimitAmount::getCreateTime).last("limit 1"));
                    if (customerLimitAmount != null) {
                        customerReg.setTeamId(customerLimitAmount.getTeamId());
                        customerReg.setLimitAmount(customerLimitAmount.getAmount());
                    }*//*else{
                        TeamCustomerLimitAmount initCustomerLimitAmount = new TeamCustomerLimitAmount();
                        initCustomerLimitAmount.setCustomerId(customerReg.getCustomerId());
                        initCustomerLimitAmount.setTeamId(customerReg.getTeamId());
                        initCustomerLimitAmount.setTeamName(customerReg.getTeamName());
                        initCustomerLimitAmount.setName(customerReg.getName());
                        initCustomerLimitAmount.setIdCard(customerReg.getIdCard());
                        initCustomerLimitAmount.setCompanyRegId(customerReg.getCompanyRegId());
                        initCustomerLimitAmount.setCompanyRegName(customerReg.getCompanyRegName());
                        initCustomerLimitAmount.setCompanyName(customerReg.getCompanyName());
                        initCustomerLimitAmount.setAmount(companyTeam.getLimitAmount());
                        initCustomerLimitAmount.setCreateBy(customerReg.getCreatorBy());
                        initCustomerLimitAmount.setCreateTime(new Date());
                        teamCustomerLimitAmountMapper.insert(initCustomerLimitAmount);
                        customerReg.setLimitAmount(initCustomerLimitAmount.getAmount());
                    }*//*
                }
            }*/
        });
        return page;
    }

    @Override
    public Page<CustomerReg> pageCustomerReg4Occu(Page<CustomerReg> page, String idCard, String examNo, String dateType, String regTimeStart, String regTimeEnd, String companyRegId, String teamId, String name, String status, String retreiveStatus, String emplyee, String paymentState, String companyNotifyFlag, String customerId, String riskFactor, String examCategory, String occuReportResultStatus, String occuReportUploadTimeStart, String occuReportUploadTimeEnd, String summaryStatus) {
        customerRegMapper.pageCustomerReg4Occu(page, idCard, examNo, dateType, regTimeStart, regTimeEnd, companyRegId, teamId, name, status, retreiveStatus, emplyee, paymentState, companyNotifyFlag, customerId, riskFactor, examCategory, occuReportResultStatus, occuReportUploadTimeStart, occuReportUploadTimeEnd, summaryStatus);

        page.getRecords().forEach(customerReg -> {
            customerReg.setPaymentState(statPayStatus4Reg(customerReg.getId()));
            if (customerReg.getAge() == null) {
                Date birthday = customerReg.getBirthday();
                if (birthday != null) {
                    customerReg.setAge(DateUtil.ageOfNow(birthday));
                }
            }

            if (StringUtils.isNotBlank(customerReg.getCompanyRegId())) {
                CompanyReg companyReg = companyRegMapper.selectById(customerReg.getCompanyRegId());
                if (companyReg != null) {
                    customerReg.setCompanyReg(companyReg);
                }
            }

        });
        return page;
    }

    @Override
    public List<CustomerReg> listCustomerReg(String idCard, String examNo, String regTimeStart, String regTimeEnd, String companyRegId, String teamId, String name, String status, String retreiveStatus, String emplyee, String paymentState) {
        List<CustomerReg> list = customerRegMapper.listCustomerReg(idCard, examNo, regTimeStart, regTimeEnd, companyRegId, teamId, name, status, retreiveStatus, emplyee, paymentState);
        list.forEach(customerReg -> {
            customerReg.setPaymentState(statPayStatus4Reg(customerReg.getId()));
            if (customerReg.getAge() == null) {
                Date birthday = customerReg.getBirthday();
                if (birthday != null) {
                    customerReg.setAge(DateUtil.ageOfNow(birthday));
                }
            }

            if (StringUtils.isNotBlank(customerReg.getCompanyRegId())) {
                CompanyReg companyReg = companyRegMapper.selectById(customerReg.getCompanyRegId());
                if (companyReg != null) {
                    customerReg.setCompanyReg(companyReg);
                }
            }
        });

        return list;
    }

    @Override
    public Map<String, StatBean> statReg() {
        //统计总体检人数，本周体检人数
        Map<String, StatBean> statMap = new HashMap<>();
        StatBean monthReg = new StatBean();
        monthReg.setName("monthRegCount");
        Integer monthRegCount = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM customer_reg WHERE del_flag = 0   AND status = ?   AND DATE_FORMAT(reg_time, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')", Integer.class, ExConstants.REG_STATUS_REGED);
        monthReg.setValue(String.valueOf(monthRegCount));
        statMap.put("monthReg", monthReg);

        StatBean totalReg = new StatBean();
        totalReg.setName("totalRegCount");
        Integer totalRegCount = jdbcTemplate.queryForObject("SELECT count(*) FROM customer_reg WHERE del_flag = 0 and status=?", Integer.class, ExConstants.REG_STATUS_REGED);
        totalReg.setValue(String.valueOf(totalRegCount));
        statMap.put("totalReg", totalReg);

        StatBean monthRegAmount = new StatBean();
        monthRegAmount.setName("monthRegAmount");
        Double monthRegAmountValue = null;
        try {
            monthRegAmountValue = jdbcTemplate.queryForObject("SELECT SUM(price_after_dis) FROM customer_reg_item_group WHERE add_minus_flag != '-1' and pay_status = ? AND DATE_FORMAT(create_time, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')", Double.class, ExConstants.PAY_STATE_已支付);
        } catch (Exception e) {
        }
        monthRegAmount.setValue(monthRegAmountValue == null ? "0" : String.valueOf(monthRegAmountValue));
        statMap.put("monthRegAmount", monthRegAmount);

        StatBean totalRegAmount = new StatBean();
        totalRegAmount.setName("totalRegAmount");
        Double totalRegAmountValue = null;
        try {
            totalRegAmountValue = jdbcTemplate.queryForObject("SELECT SUM(price_after_dis) FROM customer_reg_item_group WHERE add_minus_flag != '-1' and pay_status = ?", Double.class, ExConstants.PAY_STATE_已支付);
        } catch (Exception e) {
        }
        totalRegAmount.setValue(totalRegAmountValue == null ? "0" : String.valueOf(totalRegAmountValue));
        statMap.put("totalRegAmount", totalRegAmount);

        StatBean criticalToday = new StatBean();
        criticalToday.setName("criticalToday");
        Integer criticalTodayCount = jdbcTemplate.queryForObject("SELECT count(distinct customer_reg_id) FROM customer_reg_critical_item WHERE del_flag = 0 and DATE_FORMAT(create_time, '%Y-%m-%d') = DATE_FORMAT(CURDATE(), '%Y-%m-%d')", Integer.class);
        criticalToday.setValue(String.valueOf(criticalTodayCount));
        statMap.put("criticalToday", criticalToday);

        //查询customer_reg 中summary_status为'未总检'并且reg_time 已过去指定天数的记录
        String expiredReportDays = sysSettingService.getValueByCode("expiredReportDays");
        Integer expiredReportDaysInt = null;
        try {
            expiredReportDaysInt = Integer.parseInt(expiredReportDays);
        } catch (Exception e) {
        }
        if (expiredReportDaysInt == null) {
            expiredReportDaysInt = 7;
        }
        StatBean reportExpied = new StatBean();
        reportExpied.setName("reportExpied");
        Integer reportExpiedCount = jdbcTemplate.queryForObject("SELECT count(*) FROM customer_reg WHERE del_flag = 0 and status=? and  summary_status = '未总检' and reg_time < DATE_SUB(CURDATE(), INTERVAL ? DAY)", Integer.class, ExConstants.REG_STATUS_REGED, expiredReportDaysInt);
        reportExpied.setValue(String.valueOf(reportExpiedCount));
        statMap.put("reportExpied", reportExpied);

        return statMap;
    }

    @Override
    public String getDictText4ExcelTemplate(String companyRegId) {

        StringBuffer describe = new StringBuffer();
        //拼接填表说明
        List<DictModel> genderDictList = commonAPI.queryDictItemsByCode("sex");

        describe.append("数据填写说明：请从第4行开始填写数据，第一行为标题，第2行为说明，第3行为字段名，第4行为数据。");
        describe.append("1.性别可用值：");
        describe.append(genderDictList.stream().map(DictModel::getText).collect(Collectors.joining(",")));
        describe.append("; ");
        describe.append("2.出生日期格式示例：1988-01-01; ");
        describe.append("3.婚姻状况可用值： ");
        List<DictModel> marriageDictList = commonAPI.queryDictItemsByCode("material_type");
        describe.append(marriageDictList.stream().map(DictModel::getText).collect(Collectors.joining(",")));
        describe.append("; ");
        describe.append("4.证件类型可用值： ");
        List<DictModel> idTypeDictList = commonAPI.queryDictItemsByCode("idcard_type");
        describe.append(idTypeDictList.stream().map(DictModel::getText).collect(Collectors.joining(",")));
        describe.append("; ");
        describe.append("5.分组名称可用值： ");
        QueryWrapper<CompanyTeam> companyTeamQueryWrapper = new QueryWrapper<>();
        companyTeamQueryWrapper.eq("company_reg_id", companyRegId);
        List<CompanyTeam> companyTeamList = companyTeamMapper.selectList(companyTeamQueryWrapper);
        describe.append(companyTeamList.stream().map(CompanyTeam::getName).collect(Collectors.joining(",")));
        describe.append("; ");
        describe.append("6.岗位类别可用值： ");
        List<DictModel> postTypeDictList = commonAPI.queryDictItemsByCode("job_status");
        describe.append(postTypeDictList.stream().map(DictModel::getText).collect(Collectors.joining(",")));
        describe.append("; ");

        /*describe.append("7.体检类别： ");
        List<DictModel> examTypeDictList = commonAPI.queryDictItemsByCode("examination_type");
        describe.append(examTypeDictList.stream().map(DictModel::getText).collect(Collectors.joining(",")));
        describe.append("; ");*/


        return describe.toString();
    }

    @Override
    public CustomerReg getCustomerRegDetail(String id) {

        CustomerReg customerReg = getById(id);
        if (customerReg == null) {
            return null;
        }
        CompanyReg companyReg = companyRegMapper.selectById(customerReg.getCompanyRegId());
        customerReg.setCompanyReg(companyReg);
        CompanyTeam companyTeam = companyTeamMapper.selectById(customerReg.getTeamId());
        customerReg.setCompanyTeam(companyTeam);

        getItemGroupByCustomerRegId(id).forEach(customerRegItemGroup -> {
            SysDepart sysDepart = sysDepartMapper.selectById(customerRegItemGroup.getDepartmentId());
            customerRegItemGroup.setDepartment(sysDepart);
        });
        customerReg.setItemGroupList(getItemGroupByCustomerRegId(id));

        return customerReg;
    }

    @Override
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String companyRegId = request.getParameter("companyRegId");
        CompanyReg companyReg = companyRegMapper.selectById(companyRegId);

        if (companyReg == null) {
            return Result.error("单位不存在！");
        }
        List<CompanyTeam> companyTeams = companyTeamMapper.selectByMainId(companyReg.getId());
        if (CollectionUtils.isEmpty(companyTeams)) {
            return Result.error("该单位下未设置分组！");
        }

        BatchResult<CustomerReg> batchResult = new BatchResult<>();
        List<BatchResult.FailureResult<CustomerReg>> failureResults = new ArrayList<>();

        List<CustomerReg> successList = new ArrayList<>();
        List<CustomerReg> failureList = new ArrayList<>();

        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            // 获取上传文件对象
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);

            List<CustomerReg> list = ExcelImportUtil.importExcel(file.getInputStream(), CustomerReg.class, params);
            for (CustomerReg customerReg : list) {
                try {
                    customerReg.setCompanyRegId(companyRegId);
                    customerReg.setCompanyRegName(companyReg.getRegName());

                    String name = customerReg.getName();
                    //去除name中的空格
                    customerReg.setName(name.replaceAll("\\s", ""));

                    if (StringUtils.isNotBlank(customerReg.getRiskFactor())) {
                        //兼容riskFactor自动处理失败的情况，没有将危害因素名称自动解析成代码（危害因素不规范，或者多个危害因素间的分隔符不符合约定）
                        customerReg.setRiskFactor(normalizeAndValidateRiskFactors(customerReg.getRiskFactor()));
                    }

                    boolean valid = true;
                    boolean updateFlag = false;
                    StringBuilder errmsg = new StringBuilder();
                    List<CustomerReg> customerRegs = list(new LambdaQueryWrapper<CustomerReg>().eq(CustomerReg::getIdCard, customerReg.getIdCard()).eq(CustomerReg::getCompanyRegId, companyRegId).orderByDesc(CustomerReg::getExamNo));
                    String originTeamId = null;
                    int existCustomerRegCount = CollectionUtils.size(customerRegs);
                    if (existCustomerRegCount > 0) {
                        if (StringUtils.equals(customerRegs.get(0).getStatus(), "未登记")) {
                            updateFlag = true;
                            customerReg.setId(customerRegs.get(0).getId());
                            customerReg.setExamNo(customerRegs.get(0).getExamNo());
                            originTeamId = customerRegs.get(0).getTeamId();
                        } else {
                            valid = false;
                            errmsg.append("关联身份证号已登记，不可重复录入！");
                        }
                    } else {
                        customerReg.setExamNo(sequenceGenerator.getFormatedSerialNumBaseToday(ExConstants.SEQ_EXAMNO));
                    }

                    String idCard = customerReg.getIdCard();
                    String idCardType = customerReg.getCardType();
                    idCardType = StringUtils.isBlank(idCardType) ? "居民身份证" : idCardType;
                    customerReg.setCardType(idCardType);
                    if (StringUtils.equals(idCardType, "居民身份证")) {
                        if (StringUtils.isNotBlank(idCard)) {
                            if (!IdCardUtils.isValidCard(idCard)) {
                                valid = false;
                                errmsg.append("身份证号不合法！");
                                BatchResult.FailureResult<CustomerReg> failureResult = new BatchResult.FailureResult<>(customerReg, errmsg.toString());
                                failureResults.add(failureResult);
                                continue;
                            } else {
                                customerReg.setBirthday(IdCardUtils.getBirthDate(idCard));
                                customerReg.setAge(IdCardUtils.getAge(idCard));
                                customerReg.setGender(IdCardUtils.getGender(idCard));
                            }
                        }
                    }

                    if (StringUtils.isBlank(customerReg.getName())) {
                        valid = false;
                        errmsg.append("姓名不能为空！");
                    }
                    if (StringUtils.isBlank(customerReg.getGender())) {
                        valid = false;
                        errmsg.append("性别不能为空！");
                    }
                    if (null == customerReg.getBirthday()) {
                        valid = false;
                        errmsg.append("出生日期不能为空！");
                    }
                    if (!PhoneUtil.isPhone(customerReg.getPhone())) {
                        valid = false;
                        errmsg.append("手机号不正确");
                    }
                    if (null == customerReg.getPhone()) {
                        valid = false;
                        errmsg.append("手机号不能为空！");
                    }
                    CompanyTeam companyTeam = null;
                    if (StringUtils.isBlank(customerReg.getTeamName())) {
                        for (CompanyTeam team : companyTeams) {
                            boolean matchFlag = team.contains(customerReg.getAge(), customerReg.getGender(), customerReg.getMarriageStatus());
                            if (matchFlag) {
                                companyTeam = team;
                                break;
                            }
                        }
                    } else {
                        QueryWrapper<CompanyTeam> companyTeamQueryWrapper = new QueryWrapper<>();
                        companyTeamQueryWrapper.eq("company_reg_id", companyRegId);
                        companyTeamQueryWrapper.eq("name", customerReg.getTeamName());
                        companyTeamQueryWrapper.last("limit 1");
                        companyTeam = companyTeamMapper.selectOne(companyTeamQueryWrapper);
                    }

                    if (companyTeam == null) {
                        valid = false;
                        errmsg.append("未匹配到合适的分组！");
                    }
                    if (!valid) {
                        failureList.add(customerReg);
                        BatchResult.FailureResult<CustomerReg> failureResult = new BatchResult.FailureResult<>(customerReg, errmsg.toString());
                        failureResults.add(failureResult);
                        continue;
                    }

                    if (StringUtils.isBlank(customerReg.getUploadPlateFlag())) {
                        if (StringUtils.isNotBlank(companyTeam.getUploadPlateFlag())) {
                            customerReg.setUploadPlateFlag(companyTeam.getUploadPlateFlag());
                        }
                    }


                    if (StringUtils.isNotBlank(customerReg.getCompanyDeptId())) {
                        Company dept = companyService.getById(customerReg.getCompanyDeptId());
                        if (Objects.nonNull(dept)) {
                            customerReg.setCompanyDeptName(dept.getName());
                        }
                    }
                    if (StringUtils.isNotBlank(customerReg.getCompanyDeptName())) {
                        Company dept = companyService.getOne(new LambdaQueryWrapper<Company>().eq(Company::getName, customerReg.getCompanyDeptName()).eq(Company::getPid, companyReg.getCompanyId()).last("limit 1"));
                        if (Objects.nonNull(dept)) {
                            customerReg.setCompanyDeptId(dept.getId());
                        }
                    }
                    customerReg.setAgeUnit("岁");
                    customerReg.setCustomerCategory("企业客户");
                    customerReg.setCompanyId(companyReg.getCompanyId());
                    customerReg.setCompanyName(companyReg.getCompanyName());
                    customerReg.setTeamId(companyTeam.getId());
                    customerReg.setTeamNum(companyTeam.getTeamNum());
                    customerReg.setTeamName(companyTeam.getName());
                    //customerReg.setUploadPlateFlag(companyTeam.getUploadPlateFlag());
                    customerReg.setRiskFactor(StringUtils.isNotBlank(customerReg.getRiskFactor()) ? customerReg.getRiskFactor() : companyTeam.getRisks());
                    customerReg.setJobStatus(StringUtils.isNotBlank(customerReg.getJobStatus()) ? customerReg.getJobStatus() : companyTeam.getPost());
                    customerReg.setCreateTime(new java.util.Date());
                    customerReg.setExamCategory(companyTeam.getExamCategory());
                    customerReg.setStatus(ExConstants.REG_STATUS_WAIT);
                    customerReg.setPaymentState(ExConstants.PAY_STATUS_WAIT);
                    customerReg.setCreatorBy(sysUser.getUsername());
                    customerReg.setCreator(sysUser.getRealname());
                    customerReg.setDelFlag("0");
                    customerReg.setRetrieveStatus("0");
                    Date appointmentDate = customerReg.getAppointmentDate();
                    if (appointmentDate == null) {
                        appointmentDate = new Date();
                        customerReg.setAppointmentDate(appointmentDate);
                    }
                    if (customerReg.getAppointmentSort() == null) {
                        LocalDate localDate = appointmentDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                        customerReg.setAppointmentSort(sequenceGenerator.getSerialNoBaseDate(ExConstants.SEQ_APPOINTMENT_NO, localDate));
                    }

                    //处理总工龄和接害工龄年月的问题
                    if (StringUtils.isNotBlank(customerReg.getWorkYears()) && StringUtils.isBlank(customerReg.getWorkMonths())) {
                        String[] workYearAndMonth = StringUtils.split(customerReg.getWorkYears(), ".");
                        if (workYearAndMonth.length == 2) {
                            customerReg.setWorkYears(workYearAndMonth[0]);
                            customerReg.setWorkMonths(workYearAndMonth[1]);
                        } else {
                            customerReg.setWorkYears(workYearAndMonth[0]);
                            customerReg.setWorkMonths("0");
                        }
                    }

                    if (StringUtils.isNotBlank(customerReg.getRiskYears()) && StringUtils.isBlank(customerReg.getRiskMonths())) {
                        String[] riskYearAndMonth = StringUtils.split(customerReg.getRiskYears(), ".");
                        if (riskYearAndMonth.length == 2) {
                            customerReg.setRiskYears(riskYearAndMonth[0]);
                            customerReg.setRiskMonths(riskYearAndMonth[1]);
                        } else {
                            customerReg.setRiskYears(riskYearAndMonth[0]);
                            customerReg.setRiskMonths("0");
                        }
                    }

                    //查询职业病的行业
                    Company company = companyService.getById(companyReg.getCompanyId());
                    if (company != null) {
                        customerReg.setIndustry(company.getWorkIndustry());
                    }
                    //需要控制下调用频次
                    Customer customer = saveCustomerWithRateLimit(customerReg);

                    customerReg.setCustomerId(customer.getId());
                    customerReg.setArchivesNum(customer.getArchivesNum());
                    if (updateFlag) {
                        updateById(customerReg);
                    } else {
                        save(customerReg);
                    }
                    //根据分组添加检查项目
                    // 查询当前分组的项目组
                    List<CustomerRegItemGroup> itemGroups = getItemGroupByTeamId(companyTeam.getId(), customerReg);

                    // 查询已存在的项目组
                    List<CustomerRegItemGroup> existingItems = customerRegItemGroupService.list(new LambdaQueryWrapper<CustomerRegItemGroup>().eq(CustomerRegItemGroup::getCustomerRegId, customerReg.getId()));

                    // 构建已存在项目的唯一标识集合（项目ID + 部位ID）
                    Set<String> existingItemKeys = existingItems.stream().map(item -> {
                        String checkPartId = StringUtils.isNotBlank(item.getCheckPartId()) ? item.getCheckPartId() : "NULL";
                        return item.getItemGroupId() + ":" + checkPartId;
                    }).collect(Collectors.toSet());

                    // 过滤掉已存在的项目组（基于项目ID + 部位ID）
                    List<CustomerRegItemGroup> newItems = itemGroups.stream().filter(item -> {
                        String checkPartId = StringUtils.isNotBlank(item.getCheckPartId()) ? item.getCheckPartId() : "NULL";
                        String itemKey = item.getItemGroupId() + ":" + checkPartId;
                        return !existingItemKeys.contains(itemKey);
                    }).collect(Collectors.toList());

                    if (!newItems.isEmpty()) {
                        customerRegItemGroupService.saveBatch(newItems);
                        existingItems.addAll(newItems);
                    }


                    //初始化团检额度
                    dealLimitAmount(customerReg, originTeamId);
                    //增加必检项目
                    addMustCheckGroupList(customerReg, companyTeam, sysUser, existingItems);
                    //向消息队列和接口发送建档消息
                    try {
//                        customerService.sendCustomer2Mq(customer);
//                        customerService.addCustomer2Interface(customer);
                    } catch (Exception e) {
                        log.error("向消息队列和接口发送建档消息异常", e);
                        failureList.add(customerReg);
                        BatchResult.FailureResult<CustomerReg> failureResult = new BatchResult.FailureResult<>(customerReg, e.getMessage());
                        failureResults.add(failureResult);
                    }


                    successList.add(customerReg);
                } catch (Exception e) {
                    log.error("导入失败", e);
                    failureList.add(customerReg);
                    BatchResult.FailureResult<CustomerReg> failureResult = new BatchResult.FailureResult<>(customerReg, e.getMessage());
                    failureResults.add(failureResult);
                } finally {
                    try {
                        file.getInputStream().close();
                    } catch (IOException e) {
                        log.error("导入名单，关闭输入流异常", e);
                    }
                }
            }
            //从list中获取去重后的workShop，worKtype，并检查数据库中是否存在，不存在则插入
                /*List<String> workShopList = finalList.stream().map(CustomerReg::getWorkShop).distinct().toList();
                List<String> workTypeList = finalList.stream().map(CustomerReg::getWorkType).distinct().toList();
                for (String workShop : workShopList) {
                    Integer existCount = jdbcTemplate.queryForObject("SELECT count(*) FROM zy_work_shop WHERE name = ? and company_id=?", Integer.class, workShop, companyReg.getCompanyId());
                    existCount = existCount == null ? 0 : existCount;
                    if (existCount == 0) {
                        jdbcTemplate.update("INSERT INTO zy_work_shop (id,  create_time,  name, del_flag, company_id) VALUES (?,?,?,?,?)", snowflakeIdWorker.nextId(), new Date(), workShop, 0, companyReg.getCompanyId());
                    }
                }
                for (String workType : workTypeList) {
                    Integer existCount = jdbcTemplate.queryForObject("SELECT count(*) FROM zy_worktype WHERE name = ? and company_id=?", Integer.class, workType, companyReg.getCompanyId());
                    existCount = existCount == null ? 0 : existCount;
                    if (existCount == 0) {
                        jdbcTemplate.update("INSERT INTO zy_worktype (id,  create_time,  name, company_id,enable_flag) VALUES (?,?,?,?,?)", snowflakeIdWorker.nextId(), new Date(), workType, companyReg.getCompanyId(), 1);
                    }
                }*/
        }


        try {
            if (CollectionUtils.isNotEmpty(failureResults)) {
                List<CompanyImportRecord> records = failureResults.stream().map(failureRet -> {
                    CompanyImportRecord record = new CompanyImportRecord();
                    record.setCompanyRegId(failureRet.getItem().getCompanyRegId());
                    record.setCompanyTeamId(failureRet.getItem().getTeamId());
                    record.setCompanyRegName(failureRet.getItem().getCompanyRegName());
                    record.setCompanyTeamName(failureRet.getItem().getTeamName());
                    record.setName(failureRet.getItem().getName());
                    record.setErrMsg(StringUtils.substring(failureRet.getReason(), 0, 200));
                    record.setIdCard(failureRet.getItem().getIdCard());
                    record.setCreateTime(new Date());
                    record.setCreateBy(failureRet.getItem().getCreatorBy());
                    return record;
                }).toList();
                companyImportRecordService.saveBatch(records);
            }
        } catch (Exception e) {
            log.error("错误记录存储异常");
        }
        batchResult.setSuccessResults(successList);
        batchResult.setSuccessCount(successList.size());
        batchResult.setFailureResults(failureResults);
        batchResult.setFailureCount(failureResults.size());
        return Result.ok(batchResult);
    }

    //@Transactional(rollbackFor = Exception.class)
    public void addMustCheckGroupList(CustomerReg customerReg, CompanyTeam companyTeam, LoginUser sysUser, List<CustomerRegItemGroup> newItems) throws Exception {
        String riskFactor = customerReg.getRiskFactor();
        if (StringUtils.isNotBlank(riskFactor)) {
            String[] riskFactors = StringUtils.split(riskFactor, ",");
            if (riskFactors.length == 0) {
                return;
            }

            List<ItemGroup> mustGroupList = riskFactorItemgroupService.listByRiskFactors(customerReg.getJobStatus(), List.of(riskFactors));

            // 【修正】查询该客户登记记录的所有已存在项目（包括分组项目和之前添加的必检项目）
            List<CustomerRegItemGroup> allExistingItems = customerRegItemGroupService.list(new LambdaQueryWrapper<CustomerRegItemGroup>().eq(CustomerRegItemGroup::getCustomerRegId, customerReg.getId()));

            // 【修正】对于必检项目，只需要检查主项目是否重复（项目ID + 部位ID）
            // 不需要检查附属项目，因为附属项目会根据主项目自动生成
            Set<String> existingMainItemKeys = allExistingItems.stream().filter(item -> StringUtils.isBlank(item.getAttachBaseId())) // 只考虑主项目
                    .map(item -> {
                        String checkPartId = StringUtils.isNotBlank(item.getCheckPartId()) ? item.getCheckPartId() : "NULL";
                        return item.getItemGroupId() + ":" + checkPartId;
                    }).collect(Collectors.toSet());

            // 【修正】过滤掉与已存在主项目重复的必检项目
            List<ItemGroup> filteredMustGroupList = mustGroupList.stream().filter(mustGroup -> {
                String checkPartId = StringUtils.isNotBlank(mustGroup.getCheckPartId()) ? mustGroup.getCheckPartId() : "NULL";
                String itemKey = mustGroup.getId() + ":" + checkPartId;
                return !existingMainItemKeys.contains(itemKey);
            }).toList();


            if (CollectionUtils.isNotEmpty(filteredMustGroupList)) {
                List<String> mustGroupIds = filteredMustGroupList.stream().map(ItemGroup::getId).collect(Collectors.toList());

                // 【修正】删除原来的必检项目时，需要同时删除其附属项目和赠送项目
                // 这里删除的是可能存在的旧的必检项目及其关联项目
                customerRegItemGroupService.remove(new LambdaQueryWrapper<CustomerRegItemGroup>().eq(CustomerRegItemGroup::getCustomerRegId, customerReg.getId()).and(wrapper -> wrapper.in(CustomerRegItemGroup::getItemGroupId, mustGroupIds).or().in(CustomerRegItemGroup::getAttachBaseId, mustGroupIds)));

                // 【修正】使用过滤后的必检项目列表创建CustomerRegItemGroup
                List<CustomerRegItemGroup> customerRegItemGroupList = filteredMustGroupList.stream().map(mustGroup -> {
                    CustomerRegItemGroup customerRegItemGroup = new CustomerRegItemGroup();

                    if (Objects.nonNull(mustGroup)) {
                        customerRegItemGroup.setCustomerRegId(customerReg.getId());
                        customerRegItemGroup.setIdCard(customerReg.getIdCard());
                        customerRegItemGroup.setExamNo(customerReg.getExamNo());
                        customerRegItemGroup.setCustomerId(customerReg.getCustomerId());
                        customerRegItemGroup.setItemGroupId(mustGroup.getId());
                        customerRegItemGroup.setItemGroupName(mustGroup.getName());
                        customerRegItemGroup.setClassCode(mustGroup.getClassCode());
                        customerRegItemGroup.setDepartmentId(mustGroup.getDepartmentId());
                        customerRegItemGroup.setDepartmentName(mustGroup.getDepartmentName());
                        customerRegItemGroup.setDepartmentCode(mustGroup.getDepartmentCode());
                        customerRegItemGroup.setHisCode(mustGroup.getHisCode());
                        customerRegItemGroup.setHisName(mustGroup.getHisName());
                        customerRegItemGroup.setPayStatus(ExConstants.PAY_STATUS_WAIT);
                        customerRegItemGroup.setAddMinusFlag(0);
                        customerRegItemGroup.setPayerType(StringUtils.isNotBlank(companyTeam.getPayerType()) ? companyTeam.getPayerType() : "单位支付");
                        customerRegItemGroup.setPrice(mustGroup.getPrice());
                        customerRegItemGroup.setPriceAfterDis(mustGroup.getPrice());
                        customerRegItemGroup.setMinDiscountRate(mustGroup.getMinDiscountRate());
                        customerRegItemGroup.setDisRate(BigDecimal.ONE);
                        customerRegItemGroup.setCheckStatus(ExConstants.CHECK_STATUS_未检);
                        customerRegItemGroup.setCreateTime(new Date());
                        customerRegItemGroup.setUpdateTime(new Date());
                        customerRegItemGroup.setCreateBy(sysUser.getUsername());
                        customerRegItemGroup.setCreateName(sysUser.getRealname());
                        customerRegItemGroup.setUpdateBy(sysUser.getUsername());
                        customerRegItemGroup.setUpdateName(sysUser.getRealname());
                        if (StringUtils.isNotBlank(mustGroup.getCheckPartCode())) {
                            CheckPartDict checkPartDict = checkPartDictService.getOne(new LambdaQueryWrapper<CheckPartDict>().eq(CheckPartDict::getCode, mustGroup.getCheckPartCode()));
                            customerRegItemGroup.setCheckPartCode(mustGroup.getCheckPartCode());
                            customerRegItemGroup.setCheckPartName(checkPartDict.getName());
                            customerRegItemGroup.setCheckPartId(checkPartDict.getId());

                        } else {
                            customerRegItemGroup.setCheckPartCode(mustGroup.getCheckPartCode());
                            customerRegItemGroup.setCheckPartName(mustGroup.getCheckPartName());
                            customerRegItemGroup.setCheckPartId(mustGroup.getCheckPartId());
                        }
                        return customerRegItemGroup;
                    }
                    return null;
                }).toList();
                if (CollectionUtils.isEmpty(customerRegItemGroupList)) {
                    return;
                }

                //验证是否存在互斥项目
                itemGroupRelationService.checkIsHaveMutexes(customerRegItemGroupList);
                customerRegItemGroupService.saveBatch(customerRegItemGroupList);

                //【修正】获取附属项目 - 不需要额外的去重检查，让系统自动处理
                // 附属项目支持数量机制和部位逻辑，系统会根据配置自动生成正确数量的附属项目
                List<CustomerRegItemGroup> attachItemGroups = itemGroupRelationService.getAttachGroups(customerRegItemGroupList);
                if (CollectionUtils.isNotEmpty(attachItemGroups)) {
                    customerRegItemGroupList.addAll(attachItemGroups);
                    //验证是否存在互斥项目
                    itemGroupRelationService.checkIsHaveMutexes(attachItemGroups);
                    customerRegItemGroupService.saveBatch(attachItemGroups);
                }

                //【修正】获取赠送项目 - 同样不需要额外的去重检查
                // 赠送项目的生成逻辑已经考虑了业务规则，让系统自动处理
                List<CustomerRegItemGroup> allItemGroups = new ArrayList<>(customerRegItemGroupList);
                List<CustomerRegItemGroup> giftItemGroups = itemGroupRelationService.getGiftGroups(allItemGroups);
                if (CollectionUtils.isNotEmpty(giftItemGroups)) {
                    customerRegItemGroupList.addAll(giftItemGroups);
                    //验证是否存在互斥项目
                    itemGroupRelationService.checkIsHaveMutexes(giftItemGroups);
                    customerRegItemGroupService.saveBatch(giftItemGroups);
                }
            }

        }

    }

    public Customer saveCustomerWithRateLimit(CustomerReg customerReg) {
        String regRateLimit = sysSettingService.getValueByCode("customerRegRateLimit");
        if (StringUtils.isNumeric(regRateLimit)) {
            int maxRequests = Integer.parseInt(regRateLimit);
            // 使用分布式锁保证原子性操作
            RLock lock = redissonClient.getLock("CUSTOMER_REG_RATE_LOCK");
            try {
                lock.lock(10, TimeUnit.SECONDS);

                while (true) {
                    // 获取当前时间窗口（精确到分钟）
                    String currentMinute = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm"));
                    String key = REQUEST_COUNT_KEY_PREFIX + currentMinute;

                    // 原子性操作：获取当前计数并自动设置过期时间
                    Long count = redisTemplate.opsForValue().increment(key, 1);
                    if (count == 1) {
                        redisTemplate.expire(key, 65, TimeUnit.SECONDS); // 过期时间稍长于时间窗口
                    }

                    // 判断是否超限
                    if (count <= maxRequests) {
                        break;
                    }

                    // 计算到下一分钟的时间差（毫秒）
                    long current = System.currentTimeMillis();
                    Calendar cal = Calendar.getInstance();
                    cal.add(Calendar.MINUTE, 1);
                    cal.set(Calendar.SECOND, 0);
                    cal.set(Calendar.MILLISECOND, 0);
                    long sleepTime = cal.getTimeInMillis() - current;

                    // 等待至下一分钟窗口
                    if (sleepTime > 0) {
                        Thread.sleep(sleepTime);
                    }
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("限流等待被中断", e);
            } finally {
                lock.unlock();
            }
        }

        // 执行保存操作
        return customerService.saveCustomerByCustomerReg(customerReg);
    }

    @Override
    public void fillNames(CustomerReg customerReg) {
        if (StringUtils.isNotBlank(customerReg.getCompanyRegId())) {
            CompanyReg companyReg = companyRegMapper.selectById(customerReg.getCompanyRegId());
            customerReg.setCompanyRegName(companyReg.getRegName());
            customerReg.setCompanyName(companyReg.getCompanyName());
        }
        if (StringUtils.isNotBlank(customerReg.getTeamId())) {

            CompanyTeam companyTeam = companyTeamMapper.selectById(customerReg.getTeamId());
            if (companyTeam != null) {
                customerReg.setTeamName(companyTeam.getName());
//                customerReg.setLimitAmount(companyTeam.getLimitAmount());
            /*    if (companyTeam.getLimitAmount()!=null&&customerReg.getLimitAmount() == null&&StringUtils.isNotBlank(customerReg.getIdCard())) {
                if (companyTeam.getLimitAmount() != null && customerReg.getLimitAmount() == null && StringUtils.isNotBlank(customerReg.getIdCard())) {
                    try {
                        BigDecimal spendAmount = billService.getSpendAmountOfTeamByFeePayRecord(customerReg.getIdCard(), companyTeam.getId());
                        BigDecimal remainAmount = companyTeam.getLimitAmount().subtract(spendAmount);
                        if (remainAmount.compareTo(BigDecimal.ZERO) < 0) {
                            remainAmount = BigDecimal.ZERO;
                        }
                        customerReg.setLimitAmount(remainAmount);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                    customerReg.setLimitAmount(companyTeam.getLimitAmount());
                }*/
            }
        }
        if (StringUtils.isNotBlank(customerReg.getCompanyId())) {
            String companyName = jdbcTemplate.queryForObject("SELECT name FROM company WHERE id = ?", String.class, customerReg.getCompanyId());
            customerReg.setCompanyName(companyName);
        }
        if (customerReg.getAge() == null && customerReg.getBirthday() != null) {
            //根据出生日期计算年龄
            Integer age = DateUtil.ageOfNow(customerReg.getBirthday());
            customerReg.setAge(age);
        }
    }

    public void dealLimitAmount(CustomerReg customerReg, String originTeamId) {
        LoginUser loginUser = null;
        try {
            loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        } catch (Exception e) {
        }

        if (StringUtils.isNotBlank(customerReg.getTeamId())) {
            CompanyTeam companyTeam = companyTeamMapper.selectById(customerReg.getTeamId());
            if (companyTeam != null) {
                if (companyTeam.getLimitAmount() != null && companyTeam.getLimitAmount().compareTo(BigDecimal.ZERO) != 0 && StringUtils.isNotBlank(customerReg.getIdCard())) {
                    if (StringUtils.equals(companyTeam.getLimitAmountIssueStrategy(), "每次登记发放")) {
                        BigDecimal limitAmount = companyTeam.getLimitAmount();
                        TeamCustomerLimitAmount customerLimitAmount = teamCustomerLimitAmountMapper.getLatestLimit(customerReg.getTeamId(), customerReg.getCustomerId(), null);
//                            TeamCustomerLimitAmount customerLimitAmount = teamCustomerLimitAmountService.getOne(new LambdaQueryWrapper<TeamCustomerLimitAmount>()
//                                    .eq(TeamCustomerLimitAmount::getTeamId, customerReg.getTeamId()).eq(TeamCustomerLimitAmount::getCustomerId, customerReg.getCustomerId()).orderByDesc(TeamCustomerLimitAmount::getCreateTime).last("limit 1"));
                        if (Objects.nonNull(customerLimitAmount)) {
                            limitAmount = limitAmount.add(customerLimitAmount.getAmount());
                            teamCustomerLimitAmountService.update(new LambdaUpdateWrapper<TeamCustomerLimitAmount>().set(TeamCustomerLimitAmount::getAmount, limitAmount).eq(TeamCustomerLimitAmount::getId, customerLimitAmount.getId()));
                        } else {
                            customerLimitAmount = new TeamCustomerLimitAmount();
                            customerLimitAmount.setAmount(limitAmount);
                            customerLimitAmount.setCustomerId(customerReg.getCustomerId());
                            customerLimitAmount.setTeamId(customerReg.getTeamId());
                            customerLimitAmount.setTeamName(customerReg.getTeamName());
                            customerLimitAmount.setCompanyRegId(customerReg.getCompanyRegId());
                            customerLimitAmount.setCompanyRegName(customerReg.getCompanyRegName());
                            customerLimitAmount.setCompanyName(customerReg.getCompanyName());
                            customerLimitAmount.setName(customerReg.getName());
                            customerLimitAmount.setIdCard(customerReg.getIdCard());
                            customerLimitAmount.setCreateTime(new Date());
                            customerLimitAmount.setCreateBy(loginUser.getUsername());
                            teamCustomerLimitAmountService.save(customerLimitAmount);
                        }
                        //新增
                        if (StringUtils.isBlank(originTeamId)) {
                            LimitOperationRecord limitOperationRecord = new LimitOperationRecord();
                            limitOperationRecord.setCustomerRegId(customerReg.getId());
                            limitOperationRecord.setName(customerReg.getName());
                            limitOperationRecord.setExamNo(customerReg.getExamNo());
                            limitOperationRecord.setAmount(companyTeam.getLimitAmount());
                            limitOperationRecord.setOperation("获取额度");
                            limitOperationRecord.setBusinessDesc("获取额度");
                            limitOperationRecord.setCreateTime(new Date());
                            limitOperationRecord.setCreateBy(Objects.nonNull(loginUser) ? loginUser.getUsername() : "");
                            limitOperationRecord.setBizId(customerLimitAmount.getId());
                            limitOperationRecord.setLimitId(customerLimitAmount.getId());
                            limitOperationRecordMapper.insert(limitOperationRecord);
                        } else {
                            //编辑在切换分组
                            if (!StringUtils.equals(originTeamId, customerReg.getTeamId())) {
                                TeamCustomerLimitAmount originCustomerLimitAmount = teamCustomerLimitAmountMapper.getLatestLimit(originTeamId, customerReg.getCustomerId(), null);
//                                    TeamCustomerLimitAmount originCustomerLimitAmount = teamCustomerLimitAmountService.getOne(new LambdaQueryWrapper<TeamCustomerLimitAmount>()
//                                            .eq(TeamCustomerLimitAmount::getTeamId, originTeamId).eq(TeamCustomerLimitAmount::getCustomerId, customerReg.getCustomerId()).orderByDesc(TeamCustomerLimitAmount::getCreateTime).last("limit 1"));
                                if (Objects.nonNull(originCustomerLimitAmount)) {
                                    LimitOperationRecord limitOperationRecord = new LimitOperationRecord();
                                    limitOperationRecord.setCustomerRegId(customerReg.getId());
                                    limitOperationRecord.setName(customerReg.getName());
                                    limitOperationRecord.setExamNo(customerReg.getExamNo());
                                    limitOperationRecord.setAmount(originCustomerLimitAmount.getAmount().negate());
                                    limitOperationRecord.setOperation("切换分组，额度变更");
                                    limitOperationRecord.setBusinessDesc("切换分组，额度变更");
                                    limitOperationRecord.setCreateTime(new Date());
                                    limitOperationRecord.setCreateBy(Objects.nonNull(loginUser) ? loginUser.getUsername() : "");
                                    limitOperationRecord.setBizId(customerLimitAmount.getId());
                                    limitOperationRecord.setLimitId(originCustomerLimitAmount.getId());
                                    limitOperationRecordMapper.insert(limitOperationRecord);
                                }

                                LimitOperationRecord limitOperationRecord2 = new LimitOperationRecord();
                                limitOperationRecord2.setCustomerRegId(customerReg.getId());
                                limitOperationRecord2.setName(customerReg.getName());
                                limitOperationRecord2.setExamNo(customerReg.getExamNo());
                                limitOperationRecord2.setAmount(companyTeam.getLimitAmount());
                                limitOperationRecord2.setOperation("切换分组，额度变更");
                                limitOperationRecord2.setBusinessDesc("切换分组，额度变更");
                                limitOperationRecord2.setCreateTime(new Date());
                                limitOperationRecord2.setCreateBy(Objects.nonNull(loginUser) ? loginUser.getUsername() : "");
                                limitOperationRecord2.setBizId(customerLimitAmount.getId());
                                limitOperationRecord2.setLimitId(customerLimitAmount.getId());
                                limitOperationRecordMapper.insert(limitOperationRecord2);
                            } else {
                                LimitOperationRecord limitOperationRecord = new LimitOperationRecord();
                                limitOperationRecord.setCustomerRegId(customerReg.getId());
                                limitOperationRecord.setName(customerReg.getName());
                                limitOperationRecord.setExamNo(customerReg.getExamNo());
                                limitOperationRecord.setAmount(customerLimitAmount.getAmount());
                                limitOperationRecord.setOperation("获取额度");
                                limitOperationRecord.setBusinessDesc("获取额度");
                                limitOperationRecord.setCreateTime(new Date());
                                limitOperationRecord.setCreateBy(Objects.nonNull(loginUser) ? loginUser.getUsername() : "");
                                limitOperationRecord.setBizId(customerLimitAmount.getId());
                                limitOperationRecord.setLimitId(customerLimitAmount.getId());
                                limitOperationRecordMapper.insert(limitOperationRecord);
                            }
                        }

                        //仅首次发放
                    } else {
                        BigDecimal limitAmount = companyTeam.getLimitAmount();
                        //查询本人是否有本次团检下的登记记录
                        TeamCustomerLimitAmount customerLimitAmount = teamCustomerLimitAmountMapper.getLatestLimit(customerReg.getTeamId(), customerReg.getCustomerId(), null);
//                                    TeamCustomerLimitAmount customerLimitAmount = teamCustomerLimitAmountService.getOne(new LambdaQueryWrapper<TeamCustomerLimitAmount>()
//                                    .eq(TeamCustomerLimitAmount::getTeamId, customerReg.getTeamId()).eq(TeamCustomerLimitAmount::getCustomerId, customerReg.getCustomerId()).orderByDesc(TeamCustomerLimitAmount::getCreateTime).last("limit 1"));
                        if (Objects.isNull(customerLimitAmount)) {

                            customerLimitAmount = new TeamCustomerLimitAmount();
                            customerLimitAmount.setAmount(limitAmount);
                            customerLimitAmount.setCustomerId(customerReg.getCustomerId());
                            customerLimitAmount.setTeamId(customerReg.getTeamId());
                            customerLimitAmount.setTeamName(customerReg.getTeamName());
                            customerLimitAmount.setCompanyRegId(customerReg.getCompanyRegId());
                            customerLimitAmount.setCompanyRegName(customerReg.getCompanyRegName());
                            customerLimitAmount.setCompanyName(customerReg.getCompanyName());
                            customerLimitAmount.setName(customerReg.getName());
                            customerLimitAmount.setIdCard(customerReg.getIdCard());
                            customerLimitAmount.setCreateTime(new Date());
                            customerLimitAmount.setCreateBy(loginUser.getUsername());
                            teamCustomerLimitAmountService.save(customerLimitAmount);
                            //新增
                            if (StringUtils.isBlank(originTeamId)) {
                                LimitOperationRecord limitOperationRecord = new LimitOperationRecord();
                                limitOperationRecord.setCustomerRegId(customerReg.getId());
                                limitOperationRecord.setName(customerReg.getName());
                                limitOperationRecord.setExamNo(customerReg.getExamNo());
                                limitOperationRecord.setAmount(companyTeam.getLimitAmount());
                                limitOperationRecord.setOperation("获取额度");
                                limitOperationRecord.setBusinessDesc("获取额度");
                                limitOperationRecord.setCreateTime(new Date());
                                limitOperationRecord.setCreateBy(Objects.nonNull(loginUser) ? loginUser.getUsername() : "");
                                limitOperationRecord.setBizId(customerLimitAmount.getId());
                                limitOperationRecord.setLimitId(customerLimitAmount.getId());
                                limitOperationRecordMapper.insert(limitOperationRecord);
                            } else {
                                //编辑切换分组
                                if (!StringUtils.equals(originTeamId, customerReg.getTeamId())) {
                                    TeamCustomerLimitAmount originCustomerLimitAmount = teamCustomerLimitAmountMapper.getLatestLimit(originTeamId, customerReg.getCustomerId(), null);
//                                        TeamCustomerLimitAmount originCustomerLimitAmount = teamCustomerLimitAmountService.getOne(new LambdaQueryWrapper<TeamCustomerLimitAmount>()
//                                                .eq(TeamCustomerLimitAmount::getTeamId, originTeamId).eq(TeamCustomerLimitAmount::getCustomerId, customerReg.getCustomerId()).orderByDesc(TeamCustomerLimitAmount::getCreateTime).last("limit 1"));
                                    if (Objects.nonNull(originCustomerLimitAmount)) {
                                        LimitOperationRecord limitOperationRecord = new LimitOperationRecord();
                                        limitOperationRecord.setCustomerRegId(customerReg.getId());
                                        limitOperationRecord.setName(customerReg.getName());
                                        limitOperationRecord.setExamNo(customerReg.getExamNo());
                                        limitOperationRecord.setAmount(originCustomerLimitAmount.getAmount().negate());
                                        limitOperationRecord.setOperation("切换分组，额度变更");
                                        limitOperationRecord.setBusinessDesc("切换分组，额度变更");
                                        limitOperationRecord.setCreateTime(new Date());
                                        limitOperationRecord.setCreateBy(Objects.nonNull(loginUser) ? loginUser.getUsername() : "");
                                        limitOperationRecord.setBizId(customerLimitAmount.getId());
                                        limitOperationRecord.setLimitId(originCustomerLimitAmount.getId());
                                        limitOperationRecordMapper.insert(limitOperationRecord);
                                    }
                                    LimitOperationRecord limitOperationRecord2 = new LimitOperationRecord();
                                    limitOperationRecord2.setCustomerRegId(customerReg.getId());
                                    limitOperationRecord2.setName(customerReg.getName());
                                    limitOperationRecord2.setExamNo(customerReg.getExamNo());
                                    limitOperationRecord2.setAmount(companyTeam.getLimitAmount());
                                    limitOperationRecord2.setOperation("切换分组，额度变更");
                                    limitOperationRecord2.setBusinessDesc("切换分组，额度变更");
                                    limitOperationRecord2.setCreateTime(new Date());
                                    limitOperationRecord2.setCreateBy(Objects.nonNull(loginUser) ? loginUser.getUsername() : "");
                                    limitOperationRecord2.setBizId(customerLimitAmount.getId());
                                    limitOperationRecord2.setLimitId(customerLimitAmount.getId());
                                    limitOperationRecordMapper.insert(limitOperationRecord2);
                                } else {
                                    LimitOperationRecord limitOperationRecord = new LimitOperationRecord();
                                    limitOperationRecord.setCustomerRegId(customerReg.getId());
                                    limitOperationRecord.setName(customerReg.getName());
                                    limitOperationRecord.setExamNo(customerReg.getExamNo());
                                    limitOperationRecord.setAmount(customerLimitAmount.getAmount());
                                    limitOperationRecord.setOperation("获取额度");
                                    limitOperationRecord.setBusinessDesc("获取额度");
                                    limitOperationRecord.setCreateTime(new Date());
                                    limitOperationRecord.setCreateBy(Objects.nonNull(loginUser) ? loginUser.getUsername() : "");
                                    limitOperationRecord.setBizId(customerLimitAmount.getId());
                                    limitOperationRecord.setLimitId(customerLimitAmount.getId());
                                    limitOperationRecordMapper.insert(limitOperationRecord);
                                }
                            }
                        }
                    }
                }
            }
        }/*else{
            //切换后的分组没有额度
            if (StringUtils.isNotBlank(originTeamId)) {
                TeamCustomerLimitAmount originCustomerLimit = teamCustomerLimitAmountService.getOne(new LambdaQueryWrapper<TeamCustomerLimitAmount>().eq(TeamCustomerLimitAmount::getTeamId, originTeamId).eq(TeamCustomerLimitAmount::getCustomerId, customerReg.getCustomerId()));
                CompanyTeam originCompanyTeam = companyTeamMapper.selectById(originTeamId);
                if (Objects.nonNull(originCustomerLimit)){
                    teamCustomerLimitAmountService.update(new LambdaUpdateWrapper<TeamCustomerLimitAmount>().set(TeamCustomerLimitAmount::getAmount,originCustomerLimit.getAmount().subtract(originCompanyTeam.getLimitAmount())).eq(TeamCustomerLimitAmount::getId,originCustomerLimit.getId()));
                }
                LimitOperationRecord limitOperationRecord = new LimitOperationRecord();
                limitOperationRecord.setCustomerRegId(customerReg.getId());
                limitOperationRecord.setName(customerReg.getName());
                limitOperationRecord.setExamNo(customerReg.getExamNo());
                limitOperationRecord.setAmount(BigDecimal.ZERO);
                limitOperationRecord.setOperation("切换分组，额度变更");
                limitOperationRecord.setCreateTime(new Date());
                limitOperationRecord.setCreateBy(Objects.nonNull(loginUser) ? loginUser.getUsername() : "");
                limitOperationRecordMapper.insert(limitOperationRecord);
            }
        }*/


    }

    @Override
    public void setExamNo(CustomerReg customerReg) {
        if (StringUtils.isBlank(customerReg.getExamNo())) {
            customerReg.setExamNo(sequenceGenerator.getFormatedSerialNumBaseToday(ExConstants.SEQ_EXAMNO));
        }
        if (customerReg.getAppointmentSort() == null) {
            Date appointmentDate = customerReg.getAppointmentDate();
            appointmentDate = appointmentDate == null ? new Date() : appointmentDate;
            //将appointmentDate转换LocalDate
            LocalDate localDate = appointmentDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            customerReg.setAppointmentSort(sequenceGenerator.getSerialNoBaseDate(ExConstants.SEQ_APPOINTMENT_NO, localDate));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<CustomerRegItemGroup> getItemGroupByCustomerRegId(String customerRegId) {
        //1、获取customerReg，根据customerRegId获取CustomerRegItemGroup列表,如果列表为空，则进行初始化
        CustomerReg customerReg = getById(customerRegId);

        List<CustomerRegItemGroup> customerRegItemGroupList = customerRegMapper.getItemGroupOfCustomer(customerRegId);
        if (customerRegItemGroupList.isEmpty()) {
            String teamId = customerReg.getTeamId();
            if (StringUtils.isNotBlank(teamId)) {

                customerRegItemGroupList = getItemGroupByTeamId(teamId, customerReg);
                customerRegItemGroupService.saveBatch(customerRegItemGroupList);
            }
        }
        if (CollectionUtils.isNotEmpty(customerRegItemGroupList)) {
            //判断是否有附属项目
            Map<String, List<String>> attachGroupByBaseId = customerRegItemGroupList.stream().filter(i -> StringUtils.isNotBlank(i.getAttachBaseId())).collect(Collectors.groupingBy(CustomerRegItemGroup::getAttachBaseId, Collectors.mapping(CustomerRegItemGroup::getId, Collectors.toList())));
            Set<String> baseGroupIds = attachGroupByBaseId.keySet();
            customerRegItemGroupList.forEach(item -> {
                if (baseGroupIds.contains(item.getId())) {
                    item.setAttachGroupIds(attachGroupByBaseId.get(item.getId()));
                }
            });
            customerRegItemGroupList.sort((item1, item2) -> {
                boolean item1IsSpecial = StringUtils.equals(item1.getPayStatus(), "退款成功") || Objects.equals(item1.getAddMinusFlag(), -1);
                boolean item2IsSpecial = StringUtils.equals(item2.getPayStatus(), "退款成功") || Objects.equals(item2.getAddMinusFlag(), -1);
                if (item1IsSpecial && !item2IsSpecial) {
                    return 1; // item1 comes after item2
                } else if (!item1IsSpecial && item2IsSpecial) {
                    return -1; // item1 comes before item2
                }
                return 0; // keep original order
            });
        }
        return customerRegItemGroupList;
    }

    //@Transactional(rollbackFor = Exception.class)
    @Override
    public List<CustomerRegItemGroup> getItemGroupByTeamId(String teamId, CustomerReg customerReg) {
        List<CustomerRegItemGroup> customerRegItemGroupList = new ArrayList<>();
        if (StringUtils.isNotBlank(teamId) && customerReg != null) {
            CompanyTeam companyTeam = companyTeamMapper.selectById(teamId);
            //TODO: 2024-02-23 根据单位预约状态做一些逻辑处理
            List<CompanyTeamItemGroup> teamItemGroupList = companyRegMapper.getItemGroupOfTeam(teamId);
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            for (CompanyTeamItemGroup companyTeamItemGroup : teamItemGroupList) {
                CustomerRegItemGroup customerRegItemGroup = new CustomerRegItemGroup();
                customerRegItemGroup.setCustomerRegId(customerReg.getId());
                customerRegItemGroup.setExamNo(customerReg.getExamNo());
                customerRegItemGroup.setItemGroupId(companyTeamItemGroup.getItemGroupId());
                customerRegItemGroup.setItemGroupName(companyTeamItemGroup.getItemGroupName());
                customerRegItemGroup.setDepartmentId(companyTeamItemGroup.getDepartmentId());
                customerRegItemGroup.setType(companyTeamItemGroup.getItemGroupCategory());
                customerRegItemGroup.setItemSuitId(companyTeamItemGroup.getItemSuitId());
                customerRegItemGroup.setAddMinusFlag(companyTeamItemGroup.getAddMinusFlag());
                customerRegItemGroup.setPrice(companyTeamItemGroup.getPrice());
                customerRegItemGroup.setDisRate(companyTeamItemGroup.getDisRate());
                customerRegItemGroup.setMinDiscountRate(companyTeamItemGroup.getMinDiscountRate());
                customerRegItemGroup.setPriceAfterDis(companyTeamItemGroup.getPriceAfterDis());
                customerRegItemGroup.setPayerType(companyTeam.getPayerType());
                customerRegItemGroup.setPayStatus(ExConstants.PAY_STATUS_WAIT);
                customerRegItemGroup.setCheckTime(null);
                customerRegItemGroup.setCheckStatus(ExConstants.CHECK_STATUS_未检);
                customerRegItemGroup.setRegBy(sysUser.getUsername());
                customerRegItemGroup.setReceiptId(null);
                customerRegItemGroup.setDepartmentName(companyTeamItemGroup.getDepartmentName());
                customerRegItemGroup.setDepartmentId(companyTeamItemGroup.getDepartmentId());
                customerRegItemGroup.setDepartmentCode(companyTeamItemGroup.getDepartmentCode());
                customerRegItemGroup.setHisCode(companyTeamItemGroup.getHisCode());
                customerRegItemGroup.setHisName(companyTeamItemGroup.getHisName());
                customerRegItemGroup.setPlatCode(companyTeamItemGroup.getPlatCode());
                customerRegItemGroup.setPlatName(companyTeamItemGroup.getPlatName());
                customerRegItemGroup.setClassCode(companyTeamItemGroup.getClassCode());
                customerRegItemGroup.setMustCheckFlag(companyTeamItemGroup.getMustCheckFlag());
                customerRegItemGroup.setRiskFactorId(companyTeamItemGroup.getRiskFactorId());
                customerRegItemGroup.setCreateBy(sysUser.getUsername());
                customerRegItemGroup.setCreateName(sysUser.getRealname());
                customerRegItemGroup.setUpdateBy(sysUser.getUsername());
                customerRegItemGroup.setUpdateName(sysUser.getRealname());
                customerRegItemGroup.setCreateTime(new Date());
                customerRegItemGroup.setUpdateTime(new Date());

                customerRegItemGroup.setCheckPartId(companyTeamItemGroup.getCheckPartId());
                customerRegItemGroup.setCheckPartName(companyTeamItemGroup.getCheckPartName());
                customerRegItemGroup.setCheckPartCode(companyTeamItemGroup.getCheckPartCode());
                customerRegItemGroupList.add(customerRegItemGroup);
            }
        }

        return customerRegItemGroupList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addItemGroupBatch(List<CustomerRegItemGroup> customerRegItemGroupList) {
        customerRegItemGroupService.saveBatch(customerRegItemGroupList);
    }

    @Override
    public PaymentAnalysis handleAddItem(String id, String clientIp) throws Exception {

        CustomerReg customerReg = getById(id);
        if (customerReg == null) {
            throw new Exception("未找到登记记录");
        }
        //customerRegBarcodeService.generateBarcode(customerReg.getId());
        List<CustomerRegItemGroup> customerRegItemGroupList = customerRegItemGroupService.listByReg(id, ExConstants.PAY_STATUS_WAIT);

        String autoCharge = sysSettingService.getValueByCode("autoCharge");
        String autoChargeFlag = "0";
        if (StringUtils.isNotBlank(autoCharge)) {
            //自动收费参数格式：0:收费方式，需要解析，如果没有指定收费方式，默认为现金
            String[] autoChargeArr = autoCharge.split(":");
            autoChargeFlag = autoChargeArr[0];
            String payType = ExConstants.PAY_CHANNEL_现金;
            if (autoChargeArr.length > 1) {
                payType = autoChargeArr[1];
            }
            if (StringUtils.equals(autoChargeFlag, "1") && StringUtils.equals(payType, ExConstants.PAY_CHANNEL_现金)) {
                //自动收费
                try {
                    billService.generateBillAndPayByCash(customerRegItemGroupList, customerReg, clientIp);
                } catch (Exception e) {
                    log.error("自动收费失败，customerRegId：" + customerReg.getId(), e);
                    //throw new Exception("支付失败");
                }
                return null;
            }
        }
        if (StringUtils.equals(autoChargeFlag, "0")) {
            try {
                return billService.generateBillAndPayCompanyPart(customerRegItemGroupList, customerReg, clientIp);
            } catch (Exception e) {
                log.error("自动进行单位支付失败，customerRegId：" + customerReg.getId(), e);
                //throw new Exception("支付失败");
            }
        }

        return null;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    @Override
    public synchronized PaymentAnalysis reg(CustomerReg customerReg, String clientIp) throws Exception {
        // Check if already registered
        if (ExConstants.REG_STATUS_REGED.equals(customerReg.getStatus())) {
            throw new Exception("已经登记!"); // Custom exception for clarity
        }

        // Get CustomerRegItemGroup list
        List<CustomerRegItemGroup> customerRegItemGroupList = getItemGroupByCustomerRegId(customerReg.getId());
        if (customerRegItemGroupList.isEmpty()) {
            // Check if it is a team appointment
            if (StringUtils.isNotBlank(customerReg.getTeamId())) {
                customerRegItemGroupList = getItemGroupByTeamId(customerReg.getTeamId(), customerReg);
                if (customerRegItemGroupList.isEmpty()) {
                    throw new Exception("没有体检项目!");
                }
                customerRegItemGroupService.saveBatch(customerRegItemGroupList);
            } else {
                throw new Exception("没有体检项目!");
            }
        }
        customerReg.setItemGroupList(customerRegItemGroupList);
        //根据所在团检分组，设置团检限额
        if (StringUtils.isNotBlank(customerReg.getTeamId())) {
            CompanyTeam companyTeam = companyTeamMapper.selectById(customerReg.getTeamId());
            if (companyTeam != null) {
                customerReg.setTeamNum(companyTeam.getTeamNum());
                customerReg.setTeamName(companyTeam.getName());
                if (customerReg.getLimitAmount() == null) {
                    customerReg.setLimitAmount(companyTeam.getLimitAmount());
                }
            }
        }

        // Update registration status
        customerReg.setStatus(ExConstants.REG_STATUS_REGED);
        customerReg.setRegTime(new Date());
        if (StringUtils.isBlank(customerReg.getExamNo())) {
            customerReg.setExamNo(sequenceGenerator.getFormatedSerialNumBaseToday(ExConstants.SEQ_EXAMNO));
        }
        if (StringUtils.isBlank(customerReg.getCreatorBy())) {
            customerReg.setCreatorBy(((LoginUser) SecurityUtils.getSubject().getPrincipal()).getUsername());
        }
        customerReg.setSerialNo(sequenceGenerator.getSerialNoBaseToday(ExConstants.SEQ_REG_SERIAL_NO));
        updateById(customerReg);
        sendCustomerReg2Mq(customerReg);
        addCustomerReg2Interface(customerReg);
        //customerRegBarcodeService.generateBarcode(customerReg.getId());
        //判断项目中有无心理测评
        List<CustomerRegItemGroup> psyGroups = customerRegItemGroupList.stream().filter(g -> StringUtils.equals(g.getClassCode(), "心理")).collect(Collectors.toList());
        try {
            if (CollectionUtils.isNotEmpty(psyGroups)) {
                PsyCard psyCard = null;
                //生成体检卡
                if (StringUtils.isBlank(customerReg.getPsyCardId())) {
                    psyCard = psyCardService.generateAddCard(1);
                    String psyCardId = psyCard.getId();
                    //更新卡号到customer
                    customerReg.setPsyCardId(psyCardId);
                    update(new LambdaUpdateWrapper<CustomerReg>().set(CustomerReg::getPsyCardId, psyCardId).eq(CustomerReg::getId, customerReg.getId()));
                } else {
                    psyCard = psyCardService.getById(customerReg.getPsyCardId());
                }
                if (!StringUtils.equals(customerReg.getPsyNotifyFlag(), "已发送")) {
                    String account = psyCard.getAccount();
                    String pwd = psyCard.getPwd();
                    //发送短信
                    psyNotify(customerReg, account, pwd);
                }
            }
        } catch (Exception e) {
            log.error("心理测评相关操作失败：" + e.getMessage());
        }
        String autoCharge = sysSettingService.getValueByCode("autoCharge");
        String autoChargeFlag = "0";
        if (StringUtils.isNotBlank(autoCharge)) {
            //自动收费参数格式：0:收费方式，需要解析，如果没有指定收费方式，默认为现金
            String[] autoChargeArr = autoCharge.split(":");
            autoChargeFlag = autoChargeArr[0];
            String payType = ExConstants.PAY_CHANNEL_现金;
            if (autoChargeArr.length > 1) {
                payType = autoChargeArr[1];
            }
            if (StringUtils.equals(autoChargeFlag, "1") && StringUtils.equals(payType, ExConstants.PAY_CHANNEL_现金)) {
                //自动收费
                try {
                    billService.generateBillAndPayByCash(customerRegItemGroupList, customerReg, clientIp);
                } catch (Exception e) {
                    log.error("自动收费失败，customerRegId：" + customerReg.getId(), e);
                    //throw new Exception("支付失败");
                }
                return null;
            }
        }
        if (StringUtils.equals(autoChargeFlag, "0")) {
            try {
                return billService.generateBillAndPayCompanyPart(customerRegItemGroupList, customerReg, clientIp);
            } catch (Exception e) {
                log.error("自动进行单位支付失败，customerRegId：" + customerReg.getId(), e);
                //throw new Exception("支付失败");
            }
        }

        return null;
    }


    @Override
    public void sendPsyNotify(String customerRegId) throws Exception {
        Validate.notBlank(customerRegId, "登记id不能为空！");
        //判断项目中有无心理测评
        CustomerReg customerReg = customerRegMapper.selectById(customerRegId);
        Validate.notNull(customerReg, "未查询到登记记录！");
        Validate.isTrue(StringUtils.equals(customerReg.getStatus(), ExConstants.REG_STATUS_REGED), "检客未登记！");
        List<CustomerRegItemGroup> customerRegItemGroupList = getItemGroupByCustomerRegId(customerRegId);
        List<CustomerRegItemGroup> psyGroups = customerRegItemGroupList.stream().filter(g -> StringUtils.equals(g.getClassCode(), "心理")).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(psyGroups)) {
            PsyCard psyCard = null;
            //生成体检卡
            if (StringUtils.isBlank(customerReg.getPsyCardId())) {
                psyCard = psyCardService.generateAddCard(1);
                String psyCardId = psyCard.getId();
                //更新卡号到customer
                customerReg.setPsyCardId(psyCardId);
                update(new LambdaUpdateWrapper<CustomerReg>().set(CustomerReg::getPsyCardId, psyCardId).eq(CustomerReg::getId, customerReg.getId()));
            } else {
                psyCard = psyCardService.getById(customerReg.getPsyCardId());
            }
            String account = psyCard.getAccount();
            String pwd = psyCard.getPwd();
            //发送短信
            psyNotify(customerReg, account, pwd);
        } else {
            throw new Exception("该检客未添加心理测评项目");
        }
    }

    @Override
    public void sendReportNotify(String customerRegId) throws Exception {
        CustomerReg customerReg = customerRegMapper.selectById(customerRegId);
        if (customerReg == null) {
            throw new Exception("未找到登记记录");
        }
        List<AutoSmsSetting> reportSmsSetting = autoSmsSettingService.listAutoSmsSetting(ExConstants.SMS_BIZ_TYPE_报告通知);
        if (reportSmsSetting == null || reportSmsSetting.isEmpty()) {
            throw new Exception("未设置报告通知短信模板");
        }
        AutoSmsSetting smsSetting = reportSmsSetting.get(0);
        String mobileUrl = sysSettingService.getValueByCode("mobile_url");

        processCustomerRegForReportNotification(customerReg, smsSetting, mobileUrl);
    }

    @Override
    public void reGeneratePdf(String customerRegId) throws Exception {
        pdfGeneratorService.generatePdfByRegId(customerRegId, false);
    }

    @Override
    public void updateCustomerReportTemplate(String customerRegId, String templateId) throws Exception {
        jdbcTemplate.update("UPDATE customer_reg SET report_template_id=? WHERE id=?", templateId, customerRegId);
    }

    //心理答题短信
    public void psyNotify(CustomerReg reg, String psyAccount, String psyPwd) throws Exception {

        List<AutoSmsSetting> reportSmsSetting = autoSmsSettingService.listAutoSmsSetting(ExConstants.SMS_BIZ_TYPE_心理测评通知);
        if (reportSmsSetting == null || reportSmsSetting.isEmpty()) {
            return;
        }
        AutoSmsSetting smsSetting = reportSmsSetting.get(0);

        String msgTemplate = smsSetting.getTemplateContent();
        String psyUrl = sysSettingService.getValueByCode("psy_url");
        if (StringUtils.isBlank(psyUrl)) {
            return;
        }
        //拼接跳转路径，路径中带有账号和密码
        String psyUrlFormat = "{}?psyAccount={}&psyPwd={}";
        psyUrl = StrUtil.format(psyUrlFormat, psyUrl, psyAccount, psyPwd);
        try {
            String phone = reg.getPhone();
            //构造短信内容
            Map<String, Object> params = new HashMap<>();
            params.put("name", reg.getName());
            params.put("examNo", reg.getExamNo());
            params.put("genderTitle", StringUtils.isNotBlank(reg.getGender()) ? StringUtils.equals(reg.getGender(), "男") ? "先生" : "女士" : "");
            params.put("companyName", reg.getCompanyName());
            params.put("companyRegName", reg.getCompanyRegName());
            params.put("bookDate", DateUtil.format(reg.getRegTime(), "yyyy-MM-dd"));
            params.put("psyUrl", psyUrl);
            params.put("psyAccount", psyAccount);
            params.put("psyPwd", psyPwd);

//                params.put("mpShortUrl", shortUrl);
            //使用模板生成短信内容
            String content = MustacheUtil.render(msgTemplate, params);
            SmsResult smsResult = smsRecordsService.saveAndSendSmsUnicode(ExConstants.SMS_SEND_METHOD_IMMEDIATELY, "1", phone, content, reg.getId(), ExConstants.SMS_BIZ_TYPE_心理测评通知);
            if (smsResult != null) {
                if (smsResult.isSuccess()) {
                    reg.setPsyNotifyFlag("已发送");
                    updateById(reg);
                }
            }
        } catch (Exception e) {
            log.error("发送心理通知失败", e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public PaymentAnalysis regById(String id, String clientIp) throws Exception {

        CustomerReg customerReg = getById(id);
        if (customerReg == null) {
            throw new Exception("登记失败，未找到登记记录");
        }

        return reg(customerReg, clientIp);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BatchResult<CustomerReg> regBatch(List<CustomerReg> regList, String clientIp) throws Exception {
        List<BatchResult.FailureResult<CustomerReg>> failureResults = new ArrayList<>();
        List<CustomerReg> successResults = new ArrayList<>();

        for (CustomerReg customerReg : regList) {
            try {
                reg(customerReg, clientIp);
                successResults.add(customerReg);
            } catch (Exception e) {
                log.error("Registration failed for CustomerReg ID: " + customerReg.getId(), e);
                failureResults.add(new BatchResult.FailureResult<>(customerReg, e.getMessage()));
            }
        }

        BatchResult<CustomerReg> batchResult = new BatchResult<>();
        batchResult.setSuccessResults(successResults);
        batchResult.setFailureResults(failureResults);
        batchResult.setTotalProcessed(regList.size());

        return batchResult;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BatchResult<CustomerReg> regBatchByIds(List<String> idList, String clientIp) throws Exception {
        List<BatchResult.FailureResult<CustomerReg>> failureResults = new ArrayList<>();
        List<CustomerReg> successResults = new ArrayList<>();
        FeePayRecord feePayRecord = new FeePayRecord();
        for (String id : idList) {
            CustomerReg customerReg = getById(id);
            if (customerReg == null) {
                failureResults.add(new BatchResult.FailureResult<>(customerReg, "登记失败，未找到登记记录"));
                continue;
            }
            try {
                reg(customerReg, clientIp);
                successResults.add(customerReg);
            } catch (Exception e) {
                log.error("Registration failed for CustomerReg ID: " + customerReg.getId(), e);
                failureResults.add(new BatchResult.FailureResult<>(customerReg, e.getMessage()));
            }
        }

        BatchResult<CustomerReg> batchResult = new BatchResult<>();
        batchResult.setSuccessResults(successResults);
        batchResult.setFailureResults(failureResults);
        batchResult.setTotalProcessed(idList.size());

        return batchResult;
    }


    /*   @Transactional(rollbackFor = Exception.class)
       @Override
       public BatchResult<CustomerReg> regBatch(List<CustomerReg> regList) throws Exception {
           List<BatchResult.FailureResult<CustomerReg>> failureResults = new ArrayList<>();
           List<CustomerReg> successResults = new ArrayList<>();

           for (CustomerReg customerReg : regList) {
               try {
                   // Check if already registered
                   if (ExConstants.REG_STATUS_REGED.equals(customerReg.getStatus())) {
                       failureResults.add(new BatchResult.FailureResult<>(customerReg, "已经登记!"));
                       continue;
                   }

                   // Get CustomerRegItemGroup list
                   List<CustomerRegItemGroup> customerRegItemGroupList = getItemGroupByCustomerRegId(customerReg.getId());
                   if (customerRegItemGroupList.isEmpty()) {
                       // Check if it is a team appointment
                       if (StringUtils.isNotBlank(customerReg.getTeamId())) {
                           customerRegItemGroupList = getItemGroupByTeamId(customerReg.getTeamId(), customerReg);
                           if (customerRegItemGroupList.isEmpty()) {
                               failureResults.add(new BatchResult.FailureResult<>(customerReg, "没有体检项目!"));
                               continue;
                           }
                           customerRegItemGroupService.saveBatch(customerRegItemGroupList);
                       } else {
                           failureResults.add(new BatchResult.FailureResult<>(customerReg, "没有体检项目!"));
                           continue;
                       }
                   }
                   customerReg.setItemGroupList(customerRegItemGroupList);

                   // Update registration status
                   customerReg.setStatus(ExConstants.REG_STATUS_REGED);
                   customerReg.setRegTime(new Date());
                   customerReg.setSerialNo(sequenceGenerator.getSerialNoIBaseToday(ExConstants.SEQ_REG_SERIAL_NO));
                   if (StringUtils.isBlank(customerReg.getCreatorBy())) {
                       customerReg.setCreatorBy(((LoginUser) SecurityUtils.getSubject().getPrincipal()).getUsername());
                   }
                   updateById(customerReg);
                   sendCustomerReg2Mq(customerReg);
                   addCustomerReg2Interface(customerReg);

                   successResults.add(customerReg);
               } catch (Exception e) {
                   log.error("Registration failed", e);
                   failureResults.add(new BatchResult.FailureResult<>(customerReg, e.getMessage()));
               }
           }

           BatchResult<CustomerReg> batchResult = new BatchResult<>();
           batchResult.setSuccessResults(successResults);
           batchResult.setFailureResults(failureResults);
           batchResult.setTotalProcessed(regList.size());

           return batchResult;
       }
   */


    @Override
    public CompanyTeam getTeamByIdcrad(String idCard) throws Exception {

        //根据证件号取最近的一次体检记录
        String sql = "SELECT team_id, name,customer_id FROM customer_reg WHERE id_card = ? and (team_id is not null or team_id !='') ORDER BY reg_time DESC LIMIT 1";
        Map<String, Object> result = null;
        try {
            result = jdbcTemplate.queryForMap(sql, idCard);
        } catch (Exception ignored) {
        }

        if (result == null || result.isEmpty()) {
            throw new Exception("未查到原检人团检信息！");
        }
        String teamId = (String) result.get("team_id");
        String customerName = (String) result.get("name");
        String customerId = (String) result.get("customer_id");

        if (StringUtils.isBlank(teamId)) {
            throw new Exception("原检人不是团检客户！");
        }
        CompanyTeam companyTeam = companyTeamMapper.selectById(teamId);
        if (companyTeam == null) {
            throw new Exception("原检人团检分组不存在！");
        }
        companyTeam.setCustomerName(customerName);
        CompanyReg companyReg = companyRegMapper.selectById(companyTeam.getCompanyRegId());
        companyTeam.setCompanyReg(companyReg);

        //获取原检人的登记记录
      /*  List<String> relatedIdCards = billService.fetchRelatedIdcards(idCard);
        LambdaQueryWrapper<CustomerReg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CustomerReg::getIdCard, relatedIdCards);
        queryWrapper.eq(CustomerReg::getTeamId, teamId).orderByDesc(CustomerReg::getRegTime);
        List<CustomerReg> customerRegList = list(queryWrapper);*/
        List<CustomerReg> customerRegList = billService.fetchRelatedRegs(customerId, teamId);
        customerRegList.forEach(customerReg -> {
            //获取团检支付的金额
            BigDecimal teamPayAmount = null;
            try {
                teamPayAmount = jdbcTemplate.queryForObject("SELECT SUM(price_after_dis) FROM customer_reg_item_group WHERE give_away_flag = '0' AND payer_type = '单位支付' AND add_minus_flag != '-1' AND customer_reg_id = ?", BigDecimal.class, customerReg.getId());
                customerReg.setTeamPayAmount(teamPayAmount);
            } catch (Exception ignored) {
            }
            customerReg.setPaymentState(statPayStatus4Reg(customerReg.getId()));
        });

        companyTeam.setCustomerRegList(customerRegList);

        BigDecimal limitAmount = companyTeam.getLimitAmount();
        if (limitAmount != null) {
            TeamCustomerLimitAmount customerLimitAmount = teamCustomerLimitAmountMapper.getLatestLimit(teamId, customerId, null);
//            TeamCustomerLimitAmount customerLimitAmount = teamCustomerLimitAmountService.getOne(new LambdaQueryWrapper<TeamCustomerLimitAmount>().eq(TeamCustomerLimitAmount::getCustomerId, customerId).eq(TeamCustomerLimitAmount::getTeamId, teamId).orderByDesc(TeamCustomerLimitAmount::getCreateTime).last("limit 1"));
//            BigDecimal spendAmount = billService.getSpendAmountOfTeamByFeePayRecord(idCard, teamId);
//            companyTeam.setRemainLimitAmount(limitAmount.subtract(spendAmount));
            if (Objects.nonNull(customerLimitAmount)) {
                companyTeam.setRemainLimitAmount(customerLimitAmount.getAmount());
                companyTeam.setCustomerLimitAmountId(customerLimitAmount.getId());
            } else {
                companyTeam.setRemainLimitAmount(BigDecimal.ZERO);
            }
//            companyTeam.setRemainLimitAmount(Objects.nonNull(customerLimitAmount)?customerLimitAmount.getAmount():BigDecimal.ZERO);
//            companyTeam.setCustomerLimitAmountId(customerLimitAmount.getId());
        }
        return companyTeam;
    }

    public CompanyTeam getTeamByIdcrad2(String idCard) throws Exception {

        //根据证件号取最近的一次体检记录
        String sql = "SELECT team_id, name FROM customer_reg WHERE id_card = ? ORDER BY reg_time DESC LIMIT 1";
        Map<String, Object> result = null;
        try {
            result = jdbcTemplate.queryForMap(sql, idCard);
        } catch (Exception ignored) {
        }

        if (result == null || result.isEmpty()) {
            throw new Exception("未查到原检人信息！");
        }
        String teamId = (String) result.get("team_id");
        String customerName = (String) result.get("name");

        if (StringUtils.isBlank(teamId)) {
            throw new Exception("原检人不是团检客户！");
        }
        CompanyTeam companyTeam = companyTeamMapper.selectById(teamId);
        if (companyTeam == null) {
            throw new Exception("原检人不是团检客户！");
        }
        companyTeam.setCustomerName(customerName);
        CompanyReg companyReg = companyRegMapper.selectById(companyTeam.getCompanyRegId());
        companyTeam.setCompanyReg(companyReg);

        //获取原检人的登记记录
        List<String> relatedIdCards = billService.fetchRelatedIdcards(idCard);
        LambdaQueryWrapper<CustomerReg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CustomerReg::getIdCard, relatedIdCards);
        queryWrapper.eq(CustomerReg::getTeamId, teamId).orderByDesc(CustomerReg::getRegTime);
        List<CustomerReg> customerRegList = list(queryWrapper);
        customerRegList.forEach(customerReg -> {
            //获取团检支付的金额
            BigDecimal teamPayAmount = null;
            try {
                teamPayAmount = jdbcTemplate.queryForObject("SELECT SUM(price_after_dis) FROM customer_reg_item_group WHERE give_away_flag = '0' AND payer_type = '单位支付' AND add_minus_flag != '-1' AND customer_reg_id = ?", BigDecimal.class, customerReg.getId());
                customerReg.setTeamPayAmount(teamPayAmount);
            } catch (Exception ignored) {
            }
            customerReg.setPaymentState(statPayStatus4Reg(customerReg.getId()));
        });

        companyTeam.setCustomerRegList(customerRegList);

        BigDecimal limitAmount = companyTeam.getLimitAmount();
        if (limitAmount != null) {
            BigDecimal spendAmount = billService.getSpendAmountOfTeamByFeePayRecord(idCard, teamId);
            companyTeam.setRemainLimitAmount(limitAmount.subtract(spendAmount));
        }
        return companyTeam;
    }


    @Override
    public void updateHealthQuestId(String id, String healthQuestId) {
        LambdaUpdateWrapper<CustomerReg> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CustomerReg::getId, id);
        updateWrapper.set(CustomerReg::getHealthQuestId, healthQuestId);
        update(updateWrapper);
    }

    @Override
    public List<CustomerReg> listByIds(List<String> idList) {

        QueryWrapper<CustomerReg> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", idList);
        List<CustomerReg> list = list(queryWrapper);
        list.forEach(customerReg -> {
            customerReg.setPaymentState(statPayStatus4Reg(customerReg.getId()));
            if (customerReg.getAge() == null) {
                Date birthday = customerReg.getBirthday();
                if (birthday != null) {
                    customerReg.setAge(DateUtil.ageOfNow(birthday));
                }
            }

            if (StringUtils.isNotBlank(customerReg.getCompanyRegId())) {
                CompanyReg companyReg = companyRegMapper.selectById(customerReg.getCompanyRegId());
                if (companyReg != null) {
                    customerReg.setCompanyReg(companyReg);
                }
            }
        });
        return list;
    }

    @Override
    public void verifyAndSaveReport(String customerRegId, String reportUrl) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        LambdaUpdateWrapper<CustomerReg> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CustomerReg::getId, customerRegId);
        updateWrapper.set(CustomerReg::getEReportStatus, ExConstants.REPORT_STATE_待发送);
        updateWrapper.set(CustomerReg::getEReportUrl, reportUrl);
        updateWrapper.set(CustomerReg::getReportVerifiedBy, sysUser.getUsername());
        updateWrapper.set(CustomerReg::getReportVerifiedTime, new Date());
        update(updateWrapper);

        ReportActionRecord reportActionRecord = new ReportActionRecord();
        reportActionRecord.setCustomerRegId(customerRegId);
        reportActionRecord.setAction("审阅并生成电子报告");
        reportActionRecord.setCreateTime(new Date());
        reportActionRecord.setCreator(((LoginUser) SecurityUtils.getSubject().getPrincipal()).getRealname());
        reportActionRecordMapper.insert(reportActionRecord);
    }

    @Override
    public void verifyBatch(List<RegTemplate> list) throws Exception {

        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        for (RegTemplate regTemplate : list) {
            String id = regTemplate.getId();
            String reportTemplateId = regTemplate.getTemplateId();

            LambdaUpdateWrapper<CustomerReg> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(CustomerReg::getId, id);
            updateWrapper.set(CustomerReg::getEReportStatus, ExConstants.REPORT_STATE_待生成);
            updateWrapper.set(CustomerReg::getReportVerifiedBy, sysUser.getUsername());
            updateWrapper.set(CustomerReg::getReportVerifiedTime, new Date());
            if (StringUtils.isNotBlank(reportTemplateId)) {
                updateWrapper.set(CustomerReg::getReportTemplateId, reportTemplateId);
            }
            update(updateWrapper);

            ReportActionRecord reportActionRecord = new ReportActionRecord();
            reportActionRecord.setCustomerRegId(id);
            reportActionRecord.setAction("审阅并生成电子报告");
            reportActionRecord.setCreateTime(new Date());
            reportActionRecord.setCreator(sysUser.getRealname());
            reportActionRecordMapper.insert(reportActionRecord);
        }
    }

    @Override
    public void resetState4ReGeneratePdf(List<String> idList) throws Exception {
        LambdaUpdateWrapper<CustomerReg> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(CustomerReg::getId, idList);
        updateWrapper.set(CustomerReg::getEReportStatus, ExConstants.REPORT_STATE_待审阅);
        updateWrapper.set(CustomerReg::getEReportUrl, null);
        update(updateWrapper);
    }

    @Override
    public void markTakenBatch(List<String> idList, String signBase64) throws Exception {

        String signFileUrl = null;
        if (StringUtils.isNotBlank(signBase64)) {
            signFileUrl = MinioUtil.uploadBase64Image(signBase64);
        }

        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        LambdaUpdateWrapper<CustomerReg> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(CustomerReg::getId, idList);
        updateWrapper.set(CustomerReg::getPaperReportStatus, ExConstants.REPORT_STATE_已取走);
        updateWrapper.set(CustomerReg::getPaperReportTakenTime, new Date());
        updateWrapper.set(CustomerReg::getPaperReportSignPic, signFileUrl);
        update(updateWrapper);

        for (String customerRegId : idList) {
            ReportActionRecord reportActionRecord = new ReportActionRecord();
            reportActionRecord.setCustomerRegId(customerRegId);
            reportActionRecord.setAction("标记已取走");
            reportActionRecord.setCreateTime(new Date());
            reportActionRecord.setCreator(sysUser.getRealname());
            reportActionRecord.setPaperReportSignPic(signFileUrl);
            reportActionRecordMapper.insert(reportActionRecord);
        }
    }

    @Override
    public void assignSummaryDoctorBatch(List<String> idList, String summaryDoctor, String summaryDoctorName) {
        if (StringUtils.isBlank(summaryDoctorName) && StringUtils.isNotBlank(summaryDoctor)) {
            SysUser sysUser = sysUserService.getUserByName(summaryDoctor);
            if (sysUser != null) {
                summaryDoctorName = sysUser.getRealname();
            }
        }
        LambdaUpdateWrapper<CustomerReg> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(CustomerReg::getId, idList);
        updateWrapper.set(CustomerReg::getSummaryDoctorAssigned, StringUtils.stripToNull(summaryDoctor));
        updateWrapper.set(CustomerReg::getSummaryDoctorNameAssigned, StringUtils.stripToNull(summaryDoctorName));
        update(updateWrapper);
    }

    @Override
    public void updateReportPrintStatus(String customerRegId) {
        LambdaUpdateWrapper<CustomerReg> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CustomerReg::getId, customerRegId);
        updateWrapper.set(CustomerReg::getReportPrintTime, new Date());
        updateWrapper.set(CustomerReg::getPaperReportStatus, ExConstants.REPORT_STATE_已打印);
        update(updateWrapper);

        LambdaUpdateWrapper<CustomerRegSummary> updateWrapper2 = new LambdaUpdateWrapper<>();
        updateWrapper2.eq(CustomerRegSummary::getCustomerRegId, customerRegId);
        updateWrapper2.set(CustomerRegSummary::getReportPrintStatus, ExConstants.REPORT_STATE_已打印);
        updateWrapper2.set(CustomerRegSummary::getReportPrintTime, new Date());
        updateWrapper2.setSql("report_print_times = report_print_times + 1");

        customerRegSummaryMapper.update(null, updateWrapper2);

        ReportActionRecord reportActionRecord = new ReportActionRecord();
        reportActionRecord.setCustomerRegId(customerRegId);
        reportActionRecord.setAction("打印报告");
        reportActionRecord.setCreateTime(new Date());
        reportActionRecord.setCreator(((LoginUser) SecurityUtils.getSubject().getPrincipal()).getRealname());
        reportActionRecordMapper.insert(reportActionRecord);
    }

    @Override
    public void markReportAsTaken(String customerRegId, String signBase64) {
        String signFileUrl = null;
        if (StringUtils.isNotBlank(signBase64)) {
            signFileUrl = MinioUtil.uploadBase64Image(signBase64);
        }
        LambdaUpdateWrapper<CustomerReg> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CustomerReg::getId, customerRegId);
        updateWrapper.set(CustomerReg::getPaperReportStatus, ExConstants.REPORT_STATE_已取走);
        updateWrapper.set(CustomerReg::getPaperReportTakenTime, new Date());
        updateWrapper.set(CustomerReg::getPaperReportSignPic, signFileUrl);
        update(updateWrapper);

        ReportActionRecord reportActionRecord = new ReportActionRecord();
        reportActionRecord.setCustomerRegId(customerRegId);
        reportActionRecord.setAction("标记已取走");
        reportActionRecord.setCreateTime(new Date());
        reportActionRecord.setCreator(((LoginUser) SecurityUtils.getSubject().getPrincipal()).getRealname());
        reportActionRecord.setPaperReportSignPic(signFileUrl);
        reportActionRecordMapper.insert(reportActionRecord);
    }

    @Override
    public List<CustomerReg> listByEReportStatus(String eReportStatus, String paperReportStatus, Integer limit) {
        return customerRegMapper.listByEReportStatus(eReportStatus, paperReportStatus, limit);
    }

    @Override
    public CustomerReg getLiteById(String id) {
        return customerRegMapper.getLiteById(id);
    }

    @Override
    public void doSendReportNotifiyTask() throws Exception {

        List<AutoSmsSetting> reportSmsSetting = autoSmsSettingService.listAutoSmsSetting(ExConstants.SMS_BIZ_TYPE_报告通知);
        if (reportSmsSetting == null || reportSmsSetting.isEmpty()) {
            return;
        }
        AutoSmsSetting smsSetting = reportSmsSetting.get(0);
        String mobileUrl = sysSettingService.getValueByCode("mobile_url");
        String sendReportNotifyAfterPrint = sysSettingService.getValueByCode("sendReportNotifyAfterPrint");
        String eReportStatus = null;
        String paperReportStatus = null;
        if (StringUtils.equals(sendReportNotifyAfterPrint, "1")) {
            paperReportStatus = ExConstants.REPORT_STATE_已打印;
        } else {
            eReportStatus = ExConstants.REPORT_STATE_待发送;
        }
        List<CustomerReg> customerRegList = listByEReportStatus(eReportStatus, paperReportStatus, 1000);
        for (CustomerReg customerReg : customerRegList) {
            try {
                processCustomerRegForReportNotification(customerReg, smsSetting, mobileUrl);
            } catch (Exception e) {
                log.error("发送报告通知失败，CustomerReg ID: " + customerReg.getId(), e);
            }
        }
    }

    private void processCustomerRegForReportNotification(CustomerReg customerReg, AutoSmsSetting smsSetting, String mobileUrl) throws Exception {
        String phone = customerReg.getPhone();

        // 查询复查记录
        List<RecheckNotify> recheckNotifyList = recheckNotifyMapper.listByCustomerRegId(customerReg.getId());
        StringBuilder recheckNotifyItemGroups = new StringBuilder();
        for (RecheckNotify recheckNotify : recheckNotifyList) {
            Date targetDate = recheckNotify.getTargetDate();
            String targetDateText = DateFormatUtils.formatBetween(targetDate, new Date());
            recheckNotifyItemGroups.append(targetDateText).append("复查").append(recheckNotify.getItemGroup()).append(",");
        }
        String recheckNotifyItemGroupsStr = recheckNotifyItemGroups.isEmpty() ? "" : "建议您：" + recheckNotifyItemGroups;

        // 构造短信内容
        Map<String, Object> params = new HashMap<>();
        params.put("name", customerReg.getName());
        params.put("examNo", customerReg.getExamNo());
        params.put("genderTitle", StringUtils.isNotBlank(customerReg.getGender()) ? StringUtils.equals(customerReg.getGender(), "男") ? "先生" : "女士" : "");
        params.put("reportTime", DateUtil.format(DateUtil.parse(customerReg.getSummaryAuditTime()), "yyyy-MM-dd"));
        params.put("recheckNotifyItemGroups", recheckNotifyItemGroupsStr);
        params.put("mobileUrl", mobileUrl);

        String content = MustacheUtil.render(smsSetting.getTemplateContent(), params);
        SmsResult smsResult = smsRecordsService.saveAndSendSmsUnicode(ExConstants.SMS_SEND_METHOD_IMMEDIATELY, "1", phone, content, customerReg.getId(), ExConstants.SMS_BIZ_TYPE_报告通知);

        if (smsResult != null && smsResult.isSuccess()) {
            customerReg.setEReportStatus(ExConstants.REPORT_STATE_已发送);
            updateById(customerReg);
        }
    }

    public static void main(String[] args) {
     /*   String msgTemplate = "尊敬的{{name}}，您的体检报告已生成，点击{{reportUrl}}查看报告。建议您{{recheckNotify}}。";
        Map<String, Object> params = new HashMap<>();
        params.put("name", "张三");
        params.put("reportUrl", "http://www.baidu.com");
        params.put("recheckNotify", "1个月后复查 血常规,2年后 尿常规");
        String content = MustacheUtil.render(msgTemplate, params);

        Date target = DateUtil.parse("2026-01-01");
        String targetDateText = DateFormatUtils.formatBetween(target, new Date());
        System.out.println(targetDateText);
        System.out.println(content);*/

        System.out.println(PhoneUtil.isPhone("0471-2537660"));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateItemGroupBatch(List<CustomerRegItemGroup> customerRegItemGroupList) {
        customerRegItemGroupService.updateBatchById(customerRegItemGroupList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> removeItemGroupBatch(String customerRegId, List<String> idList) throws Exception {
        if (idList == null || idList.isEmpty()) {
            return Collections.emptyList();
        }
        List<CustomerRegItemGroup> customerRegItemGroupList = customerRegItemGroupService.listByIds(idList);
        List<String> successList = new ArrayList<>();
        for (CustomerRegItemGroup customerRegItemGroup : customerRegItemGroupList) {
            if (!ExConstants.PAY_STATUS_WAIT.equals(customerRegItemGroup.getPayStatus()) && !StringUtils.equals(customerRegItemGroup.getFirstCheckAfterPayFlag(), "1")) {
                throw new Exception("已缴费的项目不能删除！");
            }
        }

        for (String id : idList) {
            customerRegItemGroupService.removeById(id);
            successList.add(id);
        }
        //更新删除项目中涉及“先检后付”的支付单金额
        Map<String, List<CustomerRegItemGroup>> afterPayGroups = customerRegItemGroupList.stream().filter(g -> StringUtils.equals(g.getFirstCheckAfterPayFlag(), "1")).collect(Collectors.groupingBy(CustomerRegItemGroup::getBillId));
        afterPayGroups.forEach((billId, groups) -> {
            if (CollectionUtils.isNotEmpty(groups)) {
                BigDecimal refundAmount = groups.stream().map(CustomerRegItemGroup::getPriceAfterDis).reduce(BigDecimal.ZERO, BigDecimal::add);
                CustomerRegBill bill = customerRegBillService.getById(billId);
                BigDecimal billAmount = Objects.isNull(bill.getAmount()) ? BigDecimal.ZERO : bill.getAmount();
                BigDecimal finalAmount = billAmount.subtract(refundAmount);
                bill.setAmount(finalAmount);
                if (finalAmount.compareTo(BigDecimal.ZERO) == 0) {
                    bill.setStatus(ExConstants.PAY_STATE_支付成功);
                }
                customerRegBillService.updateById(bill);
            }
        });
        //customerRegBarcodeService.generateBarcode(customerRegId);

        return successList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> minusItemGroupBatch(String customerRegId, List<String> idList) throws Exception {
        if (idList == null || idList.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> successList = new ArrayList<>();
        List<CustomerRegItemGroup> customerRegItemGroupList = customerRegItemGroupService.listByIds(idList);

        for (CustomerRegItemGroup customerRegItemGroup : customerRegItemGroupList) {
            if (!ExConstants.PAY_STATUS_WAIT.equals(customerRegItemGroup.getPayStatus())) {
                throw new Exception("已缴费的项目请走退费流程！");
            }
            if (ExConstants.ADD_MINUS_FLAG_MINUS.equals(customerRegItemGroup.getAddMinusFlag())) {
                throw new Exception("减项不能再减！");
            }
            if (StringUtils.equals(customerRegItemGroup.getCheckStatus(), ExConstants.CHECK_STATUS_已检)) {
                throw new Exception("已检项目不能减项！");
            }
        }
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String updateBy = null;
        String updateName = null;
        if (Objects.nonNull(sysUser)) {
            updateBy = sysUser.getUsername();
            updateName = sysUser.getRealname();
        }

        for (String id : idList) {
            UpdateWrapper<CustomerRegItemGroup> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", id);
            updateWrapper.set("add_minus_flag", ExConstants.ADD_MINUS_FLAG_MINUS);
            updateWrapper.set("update_by", updateBy);
            updateWrapper.set("update_name", updateName);
            updateWrapper.set("update_time", new Date());
            customerRegItemGroupService.update(updateWrapper);

            CustomerRegItemGroup customerRegItemGroup = customerRegItemGroupService.getById(id);

            successList.add(id);
        }

        //customerRegBarcodeService.generateBarcode(customerRegId);
        //更新关联的customer_reg_barcode和customer_reg_

      /*  CustomerReg customerReg = getById(customerRegId);
        if (StringUtils.equals(customerReg.getStatus(), ExConstants.REG_STATUS_REGED)) {
            customerReg.setItemGroupList(customerRegItemGroupList);
            sendCustomerRegUpdate2Mq(customerReg);
        }
*/
        return successList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> undoMinusItemGroupBatch(String customerRegId, List<String> idList) throws Exception {
        if (idList == null || idList.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> successList = new ArrayList<>();
        List<CustomerRegItemGroup> customerRegItemGroupList = customerRegItemGroupService.listByIds(idList);
        for (CustomerRegItemGroup group : customerRegItemGroupList) {
            if (ExConstants.ADD_MINUS_FLAG_NORMAL.equals(group.getAddMinusFlag())) {
                throw new Exception("正常项目不能反减项！");
            }
        }

        for (String id : idList) {
            UpdateWrapper<CustomerRegItemGroup> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", id);
            updateWrapper.set("add_minus_flag", ExConstants.ADD_MINUS_FLAG_NORMAL);
            updateWrapper.set("update_time", new Date());
            customerRegItemGroupService.update(updateWrapper);
            successList.add(id);
        }

        //customerRegBarcodeService.generateBarcode(customerRegId);

        return successList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void setItemGroupBySuit(String customerRegId, List<CustomerRegItemGroup> groupList) throws Exception {
        jdbcTemplate.update("DELETE FROM customer_reg_item_group WHERE customer_reg_id = ?", customerRegId);
        addItemGroup(groupList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addItemGroup(List<CustomerRegItemGroup> groupList) throws Exception {
        // 调用重载方法，默认处理赠送和附属项目
        addItemGroup(groupList, false);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addItemGroup(List<CustomerRegItemGroup> groupList, boolean skipGiftAndAttach) throws Exception {
        //获取关联的CustomerReg
        if (groupList == null || groupList.isEmpty()) {
            return;
        }

        String customerRegId = groupList.get(0).getCustomerRegId();
        CustomerReg customerReg = getById(customerRegId);
        if (customerReg == null) {
            return;
        }

        // 检查是否启用智能子项目处理
        String enableSmartSubItems = sysSettingService.getValueByCode("enableSmartSubItems");
        if ("1".equals(enableSmartSubItems)) {
            log.info("使用智能子项目处理逻辑");
            addItemGroupWithAutoSubItems(groupList, skipGiftAndAttach);
            return;
        }

        log.info("使用传统子项目处理逻辑");
        /*Set<String> groupIds = groupList.stream().map(CustomerRegItemGroup::getItemGroupId).collect(Collectors.toSet());
        Set<String> addedGroupIds = customerRegItemGroupService.list(new LambdaQueryWrapper<CustomerRegItemGroup>().eq(CustomerRegItemGroup::getCustomerRegId, groupList.get(0).getCustomerRegId()).ne(CustomerRegItemGroup::getAddMinusFlag, "-1")).stream().map(CustomerRegItemGroup::getItemGroupId).collect(Collectors.toSet());
        //验证是否存在互斥项目
        List<ItemGroupRelation> mutexGroups = itemGroupRelationService.list(new LambdaQueryWrapper<ItemGroupRelation>().eq(ItemGroupRelation::getRelation, "互斥"));
        groupList.forEach(group -> {
            if (CollectionUtils.isNotEmpty(mutexGroups)) {
                mutexGroups.forEach(relation -> {
                        Set<String> relateGroupIdsSet = new HashSet<>(Arrays.asList(relation.getRelationGroupId()));
                        boolean hasCommonIds = !Collections.disjoint(groupIds, relateGroupIdsSet);
                        boolean hasAddedCommonIds = !Collections.disjoint(addedGroupIds, relateGroupIdsSet);
                        if (hasCommonIds || hasAddedCommonIds) {
                            throw new RuntimeException("新增项目存在冲突！");
                        }

                });
            }
        });*/
        itemGroupRelationService.checkIsHaveMutexes(groupList);

        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String operatorBy = Objects.nonNull(sysUser) ? sysUser.getUsername() : null;
        String operatorName = Objects.nonNull(sysUser) ? sysUser.getRealname() : null;

//        List<CustomerRegItemGroup> saveGroupList = Lists.newArrayList();
//        Map<String, List<ItemGroupRelation>> relationGroupMap = itemGroupRelationService.list(new LambdaQueryWrapper<ItemGroupRelation>().in(ItemGroupRelation::getGroupId, groupIds).eq(ItemGroupRelation::getRelation, "附属")).stream().collect(Collectors.groupingBy(ItemGroupRelation::getGroupId));
        groupList.forEach(group -> {
            group.setPayStatus(ExConstants.PAY_STATUS_WAIT);
            if (StringUtils.isBlank(group.getCheckStatus())) {
                group.setCheckStatus(ExConstants.CHECK_STATUS_未检);
            }
            group.setCreateTime(new Date());
            group.setUpdateTime(new Date());
            group.setCreateBy(operatorBy);
            group.setCreateName(operatorName);
            group.setUpdateBy(operatorBy);
            group.setUpdateName(operatorName);
         /*   List<ItemGroupRelation> itemGroupRelations = relationGroupMap.get(group.getItemGroupId());
            if (CollectionUtils.isNotEmpty(itemGroupRelations)) {
                itemGroupRelations.forEach(relation -> {
                    if (StringUtils.isNotBlank(relation.getRelationGroupId())) {
                        ItemGroup itemGroup = itemGroupMapper.selectById( relation.getRelationGroupId());
                        if (Objects.nonNull(itemGroup)) {
                            Integer quantity = Objects.nonNull(relation.getQuantity())?relation.getQuantity():1;
                            for (int i = 0; i < quantity; i++) {
                            CustomerRegItemGroup customerRegItemGroup = new CustomerRegItemGroup();
                                BeanUtils.copyProperties(group, customerRegItemGroup);
                                customerRegItemGroup.setItemGroupId(itemGroup.getId());
                                customerRegItemGroup.setItemGroupName(itemGroup.getName());
                                customerRegItemGroup.setHisCode(itemGroup.getHisCode());
                                customerRegItemGroup.setHisName(itemGroup.getHisName());
                                customerRegItemGroup.setPlatCode(itemGroup.getPlatCode());
                                customerRegItemGroup.setPlatName(itemGroup.getPlatName());
                                customerRegItemGroup.setDepartmentId(itemGroup.getDepartmentId());
                                customerRegItemGroup.setDepartmentCode(itemGroup.getDepartmentCode());
                                customerRegItemGroup.setDepartmentName(itemGroup.getDepartmentName());
                                customerRegItemGroup.setClassCode(itemGroup.getClassCode());
                                customerRegItemGroup.setMinDiscountRate(itemGroup.getMinDiscountRate());
                                customerRegItemGroup.setPrice(itemGroup.getPrice());
                                customerRegItemGroup.setPriceAfterDis(itemGroup.getPrice());
                                customerRegItemGroup.setMinDiscountRate(customerRegItemGroup.getPrice().subtract(customerRegItemGroup.getPriceAfterDis()));
                                customerRegItemGroup.setCreateBy(operatorBy);
                                customerRegItemGroup.setCreateName(operatorName);
                                customerRegItemGroup.setUpdateBy(operatorBy);
                                customerRegItemGroup.setUpdateName(operatorName);
                                saveGroupList.add(customerRegItemGroup);
                            }
                        }
                    }

                });
            }*/
//            saveGroupList.add(group);
        });
        customerRegItemGroupService.saveBatch(groupList);

        // 如果不跳过赠送和附属项目处理
        if (!skipGiftAndAttach) {
            //获取附属项目
            List<CustomerRegItemGroup> attachGroups = itemGroupRelationService.getAttachGroups(groupList);
            if (CollectionUtils.isNotEmpty(attachGroups)) {
                itemGroupRelationService.checkIsHaveMutexes(attachGroups);
                customerRegItemGroupService.saveBatch(attachGroups);
                groupList.addAll(attachGroups); // 将附属项目添加到主列表中，以便后续获取赠送项目
            }

            //获取赠送项目（需要考虑主项目和附属项目的赠送项目）
            List<CustomerRegItemGroup> giftGroups = itemGroupRelationService.getGiftGroups(groupList);
            if (CollectionUtils.isNotEmpty(giftGroups)) {
                itemGroupRelationService.checkIsHaveMutexes(giftGroups);
                customerRegItemGroupService.saveBatch(giftGroups);
            }
        }


        //customerRegBarcodeService.generateBarcode(customerReg.getId());
       /* if (StringUtils.equals(customerReg.getStatus(), ExConstants.REG_STATUS_REGED)) {
            customerReg.setItemGroupList(groupList);
            sendCustomerRegUpdate2Mq(customerReg);
        }*/
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeItemGroupByCompanyTeam(String customerRegId, String companyTeamId) throws Exception {
        Validate.notBlank(customerRegId, "请选择一条登记记录！");
        Validate.notBlank(companyTeamId, "不是团检人员，无法进行该操作！");
        List<CustomerRegItemGroup> regItemGroups = customerRegItemGroupService.list(new LambdaQueryWrapper<CustomerRegItemGroup>().eq(CustomerRegItemGroup::getCustomerRegId, customerRegId));
        boolean unPay = regItemGroups.stream().allMatch(g -> StringUtils.equalsAny(g.getPayStatus(), "待支付", "退款成功"));
        if (!unPay) {
            throw new RuntimeException("存在已支付项目，无法进行该操作！");
        }
        boolean unCheck = regItemGroups.stream().allMatch(g -> StringUtils.equalsAny(g.getCheckStatus(), "未检"));
        if (!unCheck) {
            throw new RuntimeException("存在已检项目，无法进行该操作！");
        }
        List<CompanyTeamItemGroup> teamItemGroups = companyTeamItemGroupMapper.selectList(new LambdaQueryWrapper<CompanyTeamItemGroup>().eq(CompanyTeamItemGroup::getTeamId, companyTeamId));
        Validate.notEmpty(teamItemGroups, "所选分组没有配置体检项目！");
        customerRegItemGroupService.remove(new LambdaQueryWrapper<CustomerRegItemGroup>().eq(CustomerRegItemGroup::getCustomerRegId, customerRegId));
        CustomerReg customerReg = getById(customerRegId);

        List<CustomerRegItemGroup> customerRegItemGroupList = getItemGroupByTeamId(companyTeamId, customerReg);
        customerRegItemGroupService.saveBatch(customerRegItemGroupList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addItemGroup4SmallApp(List<CustomerRegItemGroup> groupList) throws Exception {
        //获取关联的CustomerReg
        if (CollectionUtils.isNotEmpty(groupList)) {
            return;
        }
        String customerId = groupList.get(0).getCustomerId();
        Customer customer = customerMapper.selectById(customerId);
        if (Objects.isNull(customer)) {
            return;
        }
        //登记
        CustomerReg customerReg = new CustomerReg();
        BeanUtils.copyProperties(customer, customerReg);
        customerReg.setId(null);
//        customerReg.setCustomerId(customerId);
//        customerReg.setName(customer.getName());
//        customerReg.setBirthday(customer.getBirthday());
//        customerReg.setGender(customer.getGender());
//        customerReg.setPhone(customer.getPhone());
//        customerReg.setIdCard(customer.getIdCard());
//        customerReg.setAddress(customer.getAddress());
//        customerReg.setMarriageStatus(customer.getMarriageStatus());
//        customerReg.setPostCode(customer.getPostCode());

        CustomerReg reg = this.addCustomerReg(customerReg);
        //更新疾病调研答案表，关联regId
        List<DiseaseSurveyAnswer> answers = diseaseSurveyAnswerMapper.selectList(new LambdaQueryWrapper<DiseaseSurveyAnswer>().eq(DiseaseSurveyAnswer::getCustomerId, customerId).orderByDesc(DiseaseSurveyAnswer::getId));
        if (CollectionUtils.isNotEmpty(answers)) {
            DiseaseSurveyAnswer diseaseSurveyAnswer = answers.get(0);
            diseaseSurveyAnswer.setCustomerRegId(reg.getId());
            diseaseSurveyAnswerMapper.updateById(diseaseSurveyAnswer);
        }

        //开单
        groupList.forEach(item -> {
            item.setExamNo(reg.getExamNo());
            item.setCustomerRegId(reg.getId());
        });
        this.addItemGroup(groupList);

    }

    @Override
    public void updateGuidancePrintTimes(String id) {
        jdbcTemplate.update("UPDATE customer_reg SET guidance_print_times = guidance_print_times + 1 WHERE id = ?", id);
    }

    @Override
    public GuidanceSheet getGuidanceSheet(String customerRegId) {
        CustomerReg customerReg = getById(customerRegId);
        if (customerReg == null) {
            return null;
        }
        //查询岗位类别
        if (StringUtils.isNotBlank(customerReg.getJobStatus())) {
            SysDictItem jobStatusDictItem = sysDictItemMapper.selectOne(new LambdaQueryWrapper<SysDictItem>().eq(SysDictItem::getItemValue, customerReg.getJobStatus()).last("limit 1"));
            if (jobStatusDictItem != null) {
                customerReg.setJobStatus(jobStatusDictItem.getItemText());
            }
        }
        //查询拍摄图片
        Customer customer = customerService.getById(customerReg.getCustomerId());
        customerReg.setAvatar(Objects.nonNull(customer) ? customer.getAvatar() : "");

        //格式化登记时间
        customerReg.setRegDate(DateUtil.formatDate(customerReg.getCreateTime()));

        //获取CustomerRegItemGroup列表
        List<CustomerRegItemGroup> groupListOrigin = customerRegItemGroupService.listWithItemGroupByReg(customerRegId, null, true);
        String groupNames = groupListOrigin.stream().map(CustomerRegItemGroup::getItemGroupName).collect(Collectors.joining(","));
        customerReg.setItemGroupNames(groupNames);
        //将退费和减项的项目过滤掉
        List<CustomerRegItemGroup> groupList = new ArrayList<>();
        for (CustomerRegItemGroup group : groupListOrigin) {
            ItemGroup itemGroup = group.getItemGroup();
            if (group.getAddMinusFlag() != -1 && !StringUtils.equals(group.getPayStatus(), ExConstants.REFUND_STATE_退款成功) && !StringUtils.equals(group.getPayStatus(), "已退款") && !StringUtils.equals(group.getPayStatus(), ExConstants.REFUND_STATE_退款中) && (itemGroup.getGroupApplyFlag() != null && itemGroup.getGroupApplyFlag() == 1)) {
                itemGroup.setFastsFlagStr(Objects.equals(itemGroup.getFastsFlag(), 1) ? "空腹项目" : "非空腹项目");
                groupList.add(group);
            }
        }
        Set<String> itemSuitIds = groupListOrigin.stream().map(CustomerRegItemGroup::getItemSuitId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(itemSuitIds)) {
            List<String> suitNameList = getDistinctItemSuitNames(itemSuitIds);

            customerReg.setSuitName(StringUtils.join(suitNameList, ","));
        }
        Map<String, List<CustomerRegItemGroup>> groupByItemGroupIdAndCheckPartCode = groupList.stream().collect(Collectors.groupingBy(item -> item.getItemGroupId() + (StringUtils.isNotBlank(item.getCheckPartCode()) ? "-" + item.getCheckPartCode() : "")));
//        Map<String, List<CustomerRegItemGroup>> groupByItemGroupId = groupList.stream().collect(Collectors.groupingBy(CustomerRegItemGroup::getItemGroupId));
        //按照departmentId进行分组
        Map<String, List<CustomerRegItemGroup>> groupByDepartment = groupList.stream().collect(Collectors.groupingBy(CustomerRegItemGroup::getDepartmentId));
        //组装科室和项目列表,并提取科室列表
        List<SysDepart> departList = new ArrayList<>();
        List<DepartAndGroupBean> departAndGroupBeanList = new ArrayList<>();
        groupByDepartment.forEach((departmentId, itemGroupList) -> {
            SysDepart sysDepart = sysDepartMapper.selectById(departmentId);
            if (sysDepart != null) {
                departList.add(sysDepart);
                DepartAndGroupBean departAndGroupBean = new DepartAndGroupBean();
                departAndGroupBean.setDepart(sysDepart);
                //对itemGroupList按照其ItemGroup属性的sort排序
                itemGroupList.sort(Comparator.comparing(itemGroup -> itemGroup.getItemGroup().getSort()));
                departAndGroupBean.setGroupList(itemGroupList);
                departAndGroupBeanList.add(departAndGroupBean);
            }
        });
        //按照guideOrder排序
        departAndGroupBeanList.sort(Comparator.comparing(departAndGroupBean -> departAndGroupBean.getDepart().getGuideSort()));

        //提取功能列表
        List<String> functionList = departList.stream().map(SysDepart::getDepartFunCategory).distinct().toList();
        //给groupList补充departFunction属性值
        groupList.forEach(group -> {
            departList.stream().filter(depart -> depart.getId().equals(group.getDepartmentId())).findFirst().ifPresent(sysDepart -> group.setDepartFunction(sysDepart.getDepartFunCategory()));
        });
        //给groupList补充department属性值
        Map<String, List<SysDepart>> departmentByIdMap = departList.stream().collect(Collectors.groupingBy(SysDepart::getId));
        groupList.forEach(group -> {
            if (MapUtils.isNotEmpty(departmentByIdMap)) {
                group.setDepartment(CollectionUtils.isNotEmpty(departmentByIdMap.get(group.getDepartmentId())) ? departmentByIdMap.get(group.getDepartmentId()).get(0) : null);
            }
            if (MapUtils.isNotEmpty(groupByItemGroupIdAndCheckPartCode)) {
                String key = group.getItemGroupId() + (StringUtils.isNotBlank(group.getCheckPartCode()) ? "-" + group.getCheckPartCode() : "");
                group.setSameGroupQuantity(CollectionUtils.isNotEmpty(groupByItemGroupIdAndCheckPartCode.get(key)) ? groupByItemGroupIdAndCheckPartCode.get(key).size() : 1);
                String groupNameWithCheckPart = group.getItemGroupName() + (StringUtils.isNotBlank(group.getCheckPartName()) ? "-" + group.getCheckPartName() : "");
                if (group.getSameGroupQuantity() > 1) {
                    group.setGroupNameWithQuantity(groupNameWithCheckPart + " * " + group.getSameGroupQuantity());
                } else {
                    group.setGroupNameWithQuantity(groupNameWithCheckPart);

                }
            }
        });


        //组装功能和项目列表
        List<FunctionAndGroupBean> functionAndGroupBeanList = new ArrayList<>();
        functionList.forEach(function -> {
            FunctionAndGroupBean functionAndGroupBean = new FunctionAndGroupBean();
            List<CustomerRegItemGroup> groupListByFunction = groupList.stream().filter(group -> StringUtils.equals(function, group.getDepartFunction())).toList();
            functionAndGroupBean.setFunctionName(function);
            functionAndGroupBean.setGroupList(groupListByFunction);
            functionAndGroupBeanList.add(functionAndGroupBean);
        });

        //拷贝一份groupList
        List<CustomerRegItemGroup> concatByBarcodeItemGroupList = new ArrayList<>();
        List<CustomerRegItemGroup> groupListCopy = new ArrayList<>(groupList);

        List<CustomerRegBarcode> customerRegBarcodeList = customerRegBarcodeMapper.listByReg(customerRegId);
        //遍历customerRegBarcodeList

        for (CustomerRegBarcode barcode : customerRegBarcodeList) {
            List<CustomerRegItemGroup> itemGroupList = groupListCopy.stream().filter(group -> StringUtils.equals(group.getBarcodeId(), barcode.getId())).collect(Collectors.toList());
            //校验itemGroupList是否为空
            if (CollectionUtils.isEmpty(itemGroupList)) {
                continue;
            }
            //创建一个新的CustomerRegItemGroup，名称是itemGroupList的名称拼接
            CustomerRegItemGroup concatByBarcodeItemGroup = new CustomerRegItemGroup();
            CustomerRegItemGroup fisrt = itemGroupList.get(0);
            concatByBarcodeItemGroup.setItemGroupName(itemGroupList.stream().map(CustomerRegItemGroup::getItemGroupName).collect(Collectors.joining(",")));
            concatByBarcodeItemGroup.setDepartmentId(fisrt.getDepartmentId());
            concatByBarcodeItemGroup.setDepartmentName(fisrt.getDepartmentName());
            concatByBarcodeItemGroup.setDepartFunction(fisrt.getDepartFunction());
            concatByBarcodeItemGroup.setDepartment(fisrt.getDepartment());
            concatByBarcodeItemGroup.setItemGroup(fisrt.getItemGroup());
            concatByBarcodeItemGroupList.add(concatByBarcodeItemGroup);

            //从 groupListCopy中删除itemGroupList，不使用removeAll
            groupListCopy.removeAll(itemGroupList);
        }
        concatByBarcodeItemGroupList.addAll(groupListCopy);
        //对concatByBarcodeItemGroupList进行排序
        concatByBarcodeItemGroupList.sort(Comparator.comparing(group -> group.getDepartment().getGuideSort()));

        // 处理检验科项目按空腹标志分组
        List<CustomerRegItemGroup> itemGroupByFastsFlag = processLabItemsByFastsFlag(groupList);

        GuidanceSheet guidanceSheet = new GuidanceSheet();
        guidanceSheet.setReg(customerReg);
        guidanceSheet.setItemGroups(groupList);
        guidanceSheet.setItemGroupByFunction(functionAndGroupBeanList);
        guidanceSheet.setItemGroupByDepartment(departAndGroupBeanList);
        guidanceSheet.setConcatItemGroups(concatByBarcodeItemGroupList);
        guidanceSheet.setItemGroupByFastsFlag(itemGroupByFastsFlag);

        try {
            customerRegBarcodeService.generateBarcode(customerRegId);
        } catch (Exception ignored) {
        }

        return guidanceSheet;
    }

    @Override
    public Map<String, List<CustomerRegItemGroup>> getElecGuidanceSheet(String customerId) {
        List<CustomerReg> regs = customerRegMapper.selectList(new LambdaQueryWrapper<CustomerReg>().eq(CustomerReg::getCustomerId, customerId).orderByDesc(CustomerReg::getExamNo));
        String customerRegId = null;
        if (CollectionUtils.isNotEmpty(regs)) {
            customerRegId = regs.get(0).getId();
        }
        //获取CustomerRegItemGroup列表
        List<CustomerRegItemGroup> groupListOrigin = customerRegItemGroupService.listWithItemGroupByReg(customerRegId, null, true);
        //将退费和减项的项目过滤掉
        List<CustomerRegItemGroup> groupList = new ArrayList<>();
        for (CustomerRegItemGroup group : groupListOrigin) {
            if (group.getAddMinusFlag() != -1 && !StringUtils.equals(group.getPayStatus(), ExConstants.REFUND_STATE_退款成功)) {
                groupList.add(group);
            }
        }
        //按照departmentId进行分组
        Map<String, List<CustomerRegItemGroup>> groupByDepartment = groupList.stream().collect(Collectors.groupingBy(CustomerRegItemGroup::getDepartmentId));
        //组装科室和项目列表,并提取科室列表
        List<SysDepart> departList = new ArrayList<>();
        groupByDepartment.forEach((departmentId, itemGroupList) -> {
            SysDepart sysDepart = sysDepartMapper.selectById(departmentId);
            if (sysDepart != null) {
                departList.add(sysDepart);
            }
        });

        //给groupList补充department属性值
        Map<String, List<SysDepart>> departmentByIdMap = departList.stream().collect(Collectors.groupingBy(SysDepart::getId));
        groupList.forEach(group -> {
            if (MapUtils.isNotEmpty(departmentByIdMap)) {
                group.setDepartment(departmentByIdMap.get(group.getDepartmentId()).get(0));
            }
            group.setCheckStatus(!StringUtils.equals(group.getCheckStatus(), "未检") ? "已检" : "未检");
            String groupNameWithCheckPart = group.getItemGroupName() + (StringUtils.isNotBlank(group.getCheckPartName()) ? "-" + group.getCheckPartName() : "");
            group.setItemGroupName(groupNameWithCheckPart);
        });
        Map<String, List<CustomerRegItemGroup>> elecGuidanceSheet = groupList.stream().collect(Collectors.groupingBy(CustomerRegItemGroup::getCheckStatus));
        return elecGuidanceSheet;

    }

    @Override
    public List<CustomerRegItemGroup> list4GuidanceSheet(String customerRegId) {
        return customerRegMapper.list4GuidanceSheet(customerRegId);
    }

    @Override
    public void retrieve(String id, String retrieveImg) {

        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        UpdateWrapper<CustomerReg> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id);
        if (StringUtils.isNotBlank(retrieveImg)) {
            updateWrapper.set("retrieve_img", retrieveImg);
        }
        updateWrapper.set("retrieve_status", "1");
        updateWrapper.set("retrieve_img", retrieveImg);
        updateWrapper.set("retrieve_time", new Date());
        updateWrapper.set("retrieve_by", sysUser.getUsername());

        update(updateWrapper);
    }

    @Override
    public void unRetrieve(String id) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        UpdateWrapper<CustomerReg> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id);
        updateWrapper.set("retrieve_status", "0");
        updateWrapper.set("retrieve_time", new Date());
        updateWrapper.set("retrieve_by", sysUser.getUsername());
        updateWrapper.set("retrieve_img", null);
        update(updateWrapper);
    }

    @Override
    public void sendCustomerReg2Mq(CustomerReg reg) {
        try {
            String enable_jms = sysSettingService.getValueByCode("enable_jms_reg");
            if (!StringUtils.equals(enable_jms, "1")) {
                return;
            }

            String msg = JSON.toJSONString(reg);
            jmsMessageSender.sendPatientTopicMessage(msg);
        } catch (Exception e) {
            log.error("向门诊发送挂号消息异常", e);
        }
    }


    @Override
    public void sendCustomerRegUpdate2Mq(CustomerReg customerReg) {
        jmsMessageSender.updateApply(JSON.toJSONString(customerReg));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public CustomerReg addCustomerReg(CustomerReg customerReg) {

        fillNames(customerReg);
        fillPccaNameIfNull(customerReg);
        setExamNo(customerReg);
        if (StringUtils.startsWith(customerReg.getCustomerAvatar(), "data:image")) {
            String avatar = customerReg.getCustomerAvatar();
            String avatarPath = MinioUtil.uploadBase64Image(avatar);
            customerReg.setCustomerAvatar(avatarPath);
        }

        Customer customer = customerService.saveCustomerByCustomerReg(customerReg);
        customerReg.setCustomerId(customer.getId());
        customerReg.setArchivesNum(customer.getArchivesNum());
        customerReg.setStatus(ExConstants.REG_STATUS_WAIT);
        customerReg.setPaymentState(ExConstants.PAY_STATUS_WAIT);
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        customerReg.setCreator(loginUser.getRealname());
        customerReg.setCreatorBy(loginUser.getUsername());
        customerReg.setCreateTime(new Date());
        save(customerReg);
        dealLimitAmount(customerReg, null);
        //sendCustomerReg2Mq(customerReg);
        return customerReg;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public CustomerReg updateCustomerReg(CustomerReg customerReg) {
        fillNames(customerReg);
        fillPccaNameIfNull(customerReg);
        if (StringUtils.startsWith(customerReg.getCustomerAvatar(), "data:image")) {
            String avatar = customerReg.getCustomerAvatar();
            String avatarPath = MinioUtil.uploadBase64Image(avatar);
            customerReg.setCustomerAvatar(avatarPath);
        }
        String originTeamId = null;
        try {
            if (StringUtils.isNotBlank(customerReg.getId())) {
                originTeamId = jdbcTemplate.queryForObject("SELECT team_id FROM customer_reg WHERE id = ?  FOR UPDATE", String.class, customerReg.getId());
            }
        } catch (Exception ignored) {
            log.error("保存登记信息异常", ignored);
        }

     /*   if (StringUtils.isNotBlank(originTeamId)&&StringUtils.isNotBlank(customerReg.getTeamId())&&!StringUtils.equals(originTeamId,customerReg.getTeamId())&&StringUtils.equals(customerReg.getStatus(),ExConstants.ORDER_STATUS_已登记)
        ||StringUtils.isNotBlank(originTeamId)&&StringUtils.isBlank(customerReg.getTeamId())&&StringUtils.equals(customerReg.getStatus(),ExConstants.ORDER_STATUS_已登记)) {
           throw new RuntimeException("检客已登记，不允许切换所属分组！");
        }*/

        Customer customer = customerService.saveCustomerByCustomerReg(customerReg);
        customerReg.setCustomerId(customer.getId());
        customerReg.setArchivesNum(customer.getArchivesNum());
        updateById(customerReg);
        dealLimitAmount(customerReg, originTeamId);
        return customerReg;
    }

    @Override
    public CustomerReg addCustomerReg4Order(CustomerOrder customerOrder) throws Exception {
        Customer customer = customerMapper.selectById(customerOrder.getCustomerId());
        if (customer == null) {
            throw new Exception("档案信息不存在");
        }
        String wxOperatorCode = sysSettingService.getValueByCode("wx_operator_code");
        String operatorCode = StringUtils.isNotBlank(wxOperatorCode) ? wxOperatorCode : "";
        SysUser userByName = sysUserMapper.getUserByName(operatorCode);

        CustomerReg customerReg = new CustomerReg();
        setExamNo(customerReg);
        customerReg.setAppointmentDate(customerOrder.getBookTime());
        customerReg.setCustomerId(customerOrder.getCustomerId());
        customerReg.setArchivesNum(customer.getArchivesNum());
        customerReg.setStatus(ExConstants.REG_STATUS_WAIT);
        customerReg.setPaymentState(ExConstants.PAY_STATUS_WAIT);
        customerReg.setName(customer.getName());
        customerReg.setBirthday(customer.getBirthday());
        customerReg.setGender(customer.getGender());
        customerReg.setPhone(customer.getPhone());
        customerReg.setIdCard(customer.getIdCard());
        customerReg.setAddress(customer.getAddress());
        customerReg.setMarriageStatus(customer.getMarriageStatus());
        customerReg.setCompanyRegId(customerOrder.getCompanyRegId());
        fillNames(customerReg);
        customerReg.setProvince(customer.getProvince());
        customerReg.setProvinceCode(customer.getProvinceCode());
        customerReg.setCity(customer.getCity());
        customerReg.setCityCode(customer.getCityCode());
        customerReg.setArea(customer.getArea());
        customerReg.setAreaCode(customer.getAreaCode());
        customerReg.setExamCategory(customerOrder.getExamCategory());
        customerReg.setCardType("居民身份证");
        customerReg.setCreator(Objects.nonNull(userByName) ? userByName.getRealname() : "");
        customerReg.setCreatorBy(Objects.nonNull(userByName) ? userByName.getUsername() : "");
        customerReg.setCreateTime(new Date());
        save(customerReg);
        dealLimitAmount(customerReg, null);
        String enableHisPatientApi = sysSettingService.getValueByCode("enable_his_patient_api");

        if (StringUtils.equals(enableHisPatientApi, "1")) {
            //在his建档
            try {
                //规范建档人
                customer.setCreateBy(operatorCode);
                customer.setExamNo(customerReg.getExamNo());
                customerService.addCustomer2Interface(customer);
            } catch (Exception e) {
                throw new Exception("抱歉！HIS建档失败,请到体检中心前台进行选项。");
            }
        }

        return customerReg;
    }

    @Override
    public CustomerReg getByExamNo(String examNo) {
        QueryWrapper<CustomerReg> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("exam_no", examNo);
        queryWrapper.last("limit 1");
        return getOne(queryWrapper);
    }

    @Override
    public String statPayStatus4Reg(String regId) {
        List<String> statusList = jdbcTemplate.queryForList("select distinct pay_status from customer_reg_item_group where customer_reg_id = ?", String.class, regId);
        //如果只有一个状态，直接返回
        if (statusList.size() == 1) {
            return statusList.get(0);
        }
        //如果有多个状态，判断是否有已缴费状态，有则返回已缴费，否则返回未缴费
        if (statusList.contains(ExConstants.PAY_STATUS_PAYED)) {
            return ExConstants.PAY_STATUS_PART;
        }

        return "待支付";
    }

    @Override
    public CustomerReg addCustomerReg2Interface(CustomerReg customerReg) throws Exception {

        String enable_his_outpatient_api = sysSettingService.getValueByCode("enable_his_outpatient_api");
        if (!StringUtils.equals(enable_his_outpatient_api, "1")) {
            return customerReg;
        }

        String hipInterfaceUrl = sysSettingService.getValueByCode("hip_interface_url");
        if (StringUtils.isBlank(hipInterfaceUrl)) {
            throw new Exception("HIP接口地址未配置");
        }
        hipInterfaceUrl = StringUtils.removeEnd(hipInterfaceUrl, "/");
        String finalUrl = hipInterfaceUrl + ExApiConstants.OUTPATIENT_ADD_PATH;

        log.info("调用" + finalUrl + " 数据：" + JSONObject.toJSONString(customerReg));
        String resultStr = HttpClient.textBody(finalUrl).json(JSON.toJSONString(customerReg)).execute().asString();

        //String resultStr = HttpClientUtil.sendPost(finalUrl, JSONObject.toJSONString(customerReg));
        log.info("调用" + finalUrl + " 返回：" + resultStr);
        JSONObject resultJson = JSON.parseObject(resultStr);
        if (resultJson == null) {
            throw new Exception("HIP接口调用失败！");
        } else if (resultJson.getInteger("code") != 0) {
            throw new Exception("HIP接口调用失败！详情：" + resultJson.getString("msg"));
        }
        CustomerReg returnReg = JSON.parseObject(resultJson.getString("data"), CustomerReg.class);
        LambdaUpdateWrapper<CustomerReg> updateWrapper = new LambdaUpdateWrapper<>();
//        Customer customer = customerService.getById(customerReg.getCustomerId());
        // 设置更新字段和条件
        updateWrapper.set(CustomerReg::getInterfaceStatus, returnReg.getInterfaceStatus()).set(CustomerReg::getHisPid, returnReg.getHisPid()).set(CustomerReg::getHisVisitNo, returnReg.getHisVisitNo()) // 更新 interfaceStatus
                .eq(CustomerReg::getId, customerReg.getId()); // 根据 ID 条件更新
        update(updateWrapper);

        return returnReg;
    }

    public List<String> getDistinctItemSuitNames(Set<String> itemSuitIds) {
        List<ItemSuit> itemSuits = itemSuitMapper.selectList(new LambdaQueryWrapper<ItemSuit>().in(ItemSuit::getId, itemSuitIds).notIn(ItemSuit::getSuitType, "组单", "系统套餐"));
//        String sql = "SELECT DISTINCT name FROM item_suit WHERE id in (?)  and suit_type !='组单' and suit_type !='系统套餐'";
//        List<String> list = jdbcTemplate.queryForList(sql, String.class, itemSuitIds);
        //去掉空串
        if (CollectionUtils.isNotEmpty(itemSuits)) {
            return itemSuits.stream().filter(i -> StringUtils.isNotBlank(i.getName())).map(ItemSuit::getName).toList();
        }
        return Lists.newArrayList();
    }

    @Override
    public CompanyReg getCompanyItemGroups(String idCard) throws Exception {
        LambdaQueryWrapper<CustomerReg> queryWrapper = new LambdaQueryWrapper<CustomerReg>();
        queryWrapper.eq(CustomerReg::getIdCard, idCard).isNotNull(CustomerReg::getCompanyRegId).orderByAsc(CustomerReg::getExamNo);
        List<CustomerReg> regs = customerRegMapper.selectList(queryWrapper);
        Validate.notEmpty(regs, "未查询到相关登记信息！");
      /*  if (CollectionUtils.isEmpty(regs)) {
            throw new Exception("未查到登记信息！");
        }*/

        CustomerReg reg = regs.get(0);
        Validate.isTrue(StringUtils.equals(reg.getStatus(), "未登记"), "项目已登记，无需重复操作！");
        String companyRegId = reg.getCompanyRegId();
        CompanyReg companyReg = companyRegMapper.selectById(companyRegId);
        Validate.notNull(companyReg, "未查询到单位预约记录！");
        CompanyTeam companyTeam = companyTeamMapper.selectById(reg.getTeamId());
        Validate.notNull(companyTeam, "未查询到单位预约分组记录！");
        companyReg.setTeamPrice(companyTeam.getTeamDiscountPrice());
        LambdaQueryWrapper<CompanyTeamItemGroup> eq = new LambdaQueryWrapper<CompanyTeamItemGroup>().eq(CompanyTeamItemGroup::getCompanyRegId, companyRegId);
        if (StringUtils.isNotBlank(reg.getTeamId())) {
            eq.eq(CompanyTeamItemGroup::getTeamId, reg.getTeamId());
        }
        List<CompanyTeamItemGroup> companyTeamItemGroups = companyTeamItemGroupMapper.selectList(eq);

//        List<CustomerRegItemGroup> regItemGroups = customerRegItemGroupService.listWithItemGroupByReg(customerRegId, null, true);
        Validate.notEmpty(companyTeamItemGroups, "未指定体检项目，请到体检中心选项");
        companyReg.setCustomerReg(reg);
        List<String> groupIds = companyTeamItemGroups.stream().map(CompanyTeamItemGroup::getItemGroupId).collect(Collectors.toList());
        List<ItemInfo> itemInfos = itemInfoMapper.listItemByGroupIds(groupIds);
        Map<String, List<ItemInfo>> itemInfoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(itemInfos)) {
            itemInfoMap = itemInfos.stream().collect(Collectors.groupingBy(ItemInfo::getGroupId));
        }
        for (CompanyTeamItemGroup regItemGroup : companyTeamItemGroups) {
            List<ItemInfo> infos = itemInfoMap.get(regItemGroup.getItemGroupId());
            if (CollectionUtils.isNotEmpty(infos)) {
                String itemNames = infos.stream().map(ItemInfo::getName).collect(Collectors.joining(","));
                regItemGroup.setItemNames(itemNames);
            }
        }

        companyReg.setItemGroupList(companyTeamItemGroups);
        Set<String> itemSuiIds = companyTeamItemGroups.stream().map(CompanyTeamItemGroup::getItemSuitId).filter(i -> StringUtils.isNotBlank(i)).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(itemSuiIds)) {
            List<ItemSuit> itemSuits = itemSuitMapper.selectList(new LambdaQueryWrapper<ItemSuit>().in(ItemSuit::getId, itemSuiIds)
//                .ne(ItemSuit::getSuitType, "组单")
                    .notIn(ItemSuit::getSuitType, "系统套餐", "加项", "组单"));
            if (CollectionUtils.isNotEmpty(itemSuits)) {
                companyReg.setItemSuit(itemSuits);
            }
        }
        return companyReg;
    }

    @Override
    public void fillPccaNameIfNull(CustomerReg customerReg) {
        if (StringUtils.isBlank(customerReg.getProvince()) && StringUtils.isNotBlank(customerReg.getProvinceCode())) {
            customerReg.setProvince(pccaMapper.getName("0", customerReg.getProvinceCode()));
        }
        if (StringUtils.isBlank(customerReg.getCity())) {
            if (StringUtils.isNotBlank(customerReg.getCityCode()) && StringUtils.isNotBlank(customerReg.getProvinceCode())) {
                customerReg.setCity(pccaMapper.getName(customerReg.getProvinceCode(), customerReg.getCityCode()));
            }
        }
        if (StringUtils.isBlank(customerReg.getArea())) {
            if (StringUtils.isNotBlank(customerReg.getAreaCode()) && StringUtils.isNotBlank(customerReg.getCityCode())) {
                customerReg.setArea(pccaMapper.getName(customerReg.getCityCode(), customerReg.getAreaCode()));
            }
        }
        if (StringUtils.isBlank(customerReg.getStreet())) {
            if (StringUtils.isNotBlank(customerReg.getStreetCode()) && StringUtils.isNotBlank(customerReg.getAreaCode())) {
                customerReg.setStreet(pccaMapper.getName(customerReg.getAreaCode(), customerReg.getStreetCode()));
            }
        }
    }

    @Override
    public List<CustomerReg> getLastNYearsReg(String regId, int years) {
        years = Math.max(years, 1);
        CustomerReg customerReg = getById(regId);
        if (customerReg == null) {
            return Collections.emptyList();
        }
        List<CustomerReg> regList = customerRegMapper.getLastNYearsReg(customerReg.getIdCard(), years);

        for (CustomerReg reg : regList) {
            List<DepartGroupTree> list = customerRegItemGroupService.listDepartGroupTree(reg.getId());
            reg.setDepartGroupTreeList(list);
        }

        return regList;
    }

    @Override
    public List<CustomerReg> getLastNYearsRegByIdcard(String idcard, int years) {
        years = Math.max(years, 1);
        if (StringUtils.isBlank(idcard)) {
            return Collections.emptyList();
        }

        List<CustomerReg> regList = customerRegMapper.getLastNYearsReg(idcard, years);

        for (CustomerReg reg : regList) {
            List<DepartGroupTree> list = customerRegItemGroupService.listDepartGroupTree(reg.getId());
            reg.setDepartGroupTreeList(list);
        }

        return regList;
    }


    @Override
    public List<CustomerReg> getLastNYearsRegByCustomer(String customerId, String status, int years) {
        years = Math.max(years, 1);
        if (StringUtils.isBlank(customerId)) {
            return Collections.emptyList();
        }

        List<CustomerReg> regList = customerRegMapper.getLastNYearsRegByCustomer(customerId, status, years);
        //过滤掉总检状态为“审核通过”的记录
        regList = regList.stream().filter(reg -> !StringUtils.equals(reg.getSummaryStatus(), "审核通过")).collect(Collectors.toList());
        for (CustomerReg reg : regList) {
            List<DepartGroupTree> list = customerRegItemGroupService.listDepartGroupTree(reg.getId());
            reg.setDepartGroupTreeList(list);
        }

        return regList;
    }


    @Override
    public List<CustomerReg> getLastNYearsLiteRegByCustomer(String customerId, String status, int years) {
        years = Math.max(years, 1);
        if (StringUtils.isBlank(customerId)) {
            return Collections.emptyList();
        }

        return customerRegMapper.getLastNYearsRegByCustomer(customerId, status, years);
    }

    @Override
    public CustomerReg getRegWithItemGroup(String regId) {
        CustomerReg reg = getById(regId);
        if (reg == null) {
            return null;
        }
        List<DepartGroupTree> list = customerRegItemGroupService.listDepartGroupTree(reg.getId());
        reg.setDepartGroupTreeList(list);
        return reg;
    }


    @Override
    public void fixLimitAmount() {
        List<CustomerReg> regList = list(new QueryWrapper<CustomerReg>().eq("status", "已登记").isNotNull("team_id").isNull("limit_amount"));
        for (CustomerReg reg : regList) {
            BigDecimal limitAmount = reg.getLimitAmount();
            if (limitAmount == null) {
                CompanyTeam team = companyTeamMapper.selectById(reg.getTeamId());
                if (team != null) {
                    limitAmount = team.getLimitAmount();
                    reg.setLimitAmount(limitAmount);
                    updateById(reg);
                }
            }
        }
    }

    @Override
    public void companyNotify(CustomerReg customerReg) throws Exception {
        LambdaQueryWrapper<CustomerReg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerReg::getCompanyRegId, customerReg.getCompanyRegId());
        if (StringUtils.isNotBlank(customerReg.getName())) {
            queryWrapper.like(CustomerReg::getName, customerReg.getName());
        }
        if (StringUtils.isNotBlank(customerReg.getExamNo())) {
            queryWrapper.like(CustomerReg::getExamNo, customerReg.getExamNo());
        }
        if (StringUtils.isNotBlank(customerReg.getIdCard())) {
            queryWrapper.like(CustomerReg::getIdCard, customerReg.getIdCard());
        }
        if (StringUtils.isNotBlank(customerReg.getGender())) {
            queryWrapper.eq(CustomerReg::getGender, customerReg.getGender());
        }
        if (StringUtils.isNotBlank(customerReg.getPhone())) {
            queryWrapper.like(CustomerReg::getPhone, customerReg.getPhone());
        }
        if (StringUtils.isNotBlank(customerReg.getWorkShop())) {
            queryWrapper.eq(CustomerReg::getWorkShop, customerReg.getWorkShop());
        }
        if (StringUtils.isNotBlank(customerReg.getWorkType())) {
            queryWrapper.eq(CustomerReg::getWorkType, customerReg.getWorkType());
        }
        if (StringUtils.isNotBlank(customerReg.getStatus())) {
            queryWrapper.eq(CustomerReg::getStatus, customerReg.getStatus());
        }
        if (StringUtils.isNotBlank(customerReg.getCompanyNotifyFlag())) {
            queryWrapper.eq(CustomerReg::getCompanyNotifyFlag, customerReg.getCompanyNotifyFlag());
        }
        if (CollectionUtils.isNotEmpty(customerReg.getRegIds())) {
            queryWrapper.in(CustomerReg::getId, customerReg.getRegIds());
        }

        List<CustomerReg> regs = customerRegMapper.selectList(queryWrapper);
        boolean regFlag = regs.stream().allMatch(i -> StringUtils.equals(i.getStatus(), "已登记"));
        Validate.isTrue(regFlag, "选择的记录中存在未登记状态的人员，请核对后重新选择!");
        boolean notifyFlag = regs.stream().allMatch(i -> StringUtils.equals(i.getCompanyNotifyFlag(), ExConstants.COMPANY_NOTIFY_待发送));
        Validate.isTrue(notifyFlag, "选择的记录中存在已通知的人员，请核对后重新选择!");
        List<AutoSmsSetting> reportSmsSetting = autoSmsSettingService.listAutoSmsSetting(ExConstants.SMS_BIZ_TYPE_团检通知);
        if (reportSmsSetting == null || reportSmsSetting.isEmpty()) {
            return;
        }
        AutoSmsSetting smsSetting = reportSmsSetting.get(0);

        String msgTemplate = smsSetting.getTemplateContent();
        String mpIndexPage = sysSettingService.getValueByCode("mp_index_page");
        mpIndexPage = StringUtils.isBlank(mpIndexPage) ? "/pages/tabbar/index" : mpIndexPage;
        /*if (StringUtils.isBlank(open_service_url)) {
            return;
        }*/
        String accessToken = wxApiService.getAccessToken();

        for (CustomerReg reg : regs) {
            try {
                String phone = reg.getPhone();
//                ShortLinkRequest shortLinkRequest = new ShortLinkRequest();
//                shortLinkRequest.setPage_url(mpIndexPage + "?customerId=" + reg.getCustomerId());
//                shortLinkRequest.setPage_title("查看团检预约");
//                log.info("获取短链接请求参数：{}", JSONObject.toJSONString(shortLinkRequest));
//                String shortUrl = null;
           /*     try {
                    shortUrl = wxApiService.getShortUrl(shortLinkRequest, accessToken);
                } catch (Exception e) {
                    log.error("获取短链接失败", e);
                }
                if(StringUtils.isBlank(shortUrl)){
                    throw new RuntimeException("获取短链接失败");
                }*/
                //构造短信内容
                Map<String, Object> params = new HashMap<>();
                params.put("name", reg.getName());
                params.put("examNo", reg.getExamNo());
                params.put("genderTitle", StringUtils.isNotBlank(reg.getGender()) ? StringUtils.equals(reg.getGender(), "男") ? "先生" : "女士" : "");
                params.put("companyName", reg.getCompanyName());
                params.put("companyRegName", reg.getCompanyRegName());
                params.put("bookDate", DateUtil.format(reg.getRegTime(), "yyyy-MM-dd"));
//                params.put("mpShortUrl", shortUrl);
                //使用模板生成短信内容
                String content = MustacheUtil.render(msgTemplate, params);
                SmsResult smsResult = smsRecordsService.saveAndSendSms(ExConstants.SMS_SEND_METHOD_IMMEDIATELY, "1", phone, content, customerReg.getId(), ExConstants.SMS_BIZ_TYPE_团检通知);
                if (smsResult != null) {
                    if (smsResult.isSuccess()) {
                        reg.setCompanyNotifyFlag(ExConstants.COMPANY_NOTIFY_已发送);
                        updateById(reg);
                    }
                }
            } catch (Exception e) {
                log.error("发送报告通知失败", e);
            }
        }
    }

    @Override
    public Map<String, Integer> statByCustomer(String customerId, int years) {
        //统计一年以内的有效登记次数
        Integer regCount = jdbcTemplate.queryForObject("select count(1) from customer_reg where  customer_id=? and summary_status!=? and create_time >= DATE_SUB(NOW(), INTERVAL ? YEAR)", Integer.class, customerId, "审核通过", years);

        //统计所有报告数量
        Integer reportCount = jdbcTemplate.queryForObject("select count(1) from customer_reg where customer_id=? and summary_status=?", Integer.class, customerId, ExConstants.SUMMARY_STATUS_审核通过);

        Map<String, Integer> result = new HashMap<>();
        result.put("regCount", regCount);
        result.put("reportCount", reportCount);
        return result;
    }

    @Override
    public List<CustomerReg> getRegListByIdCardOrExamNo(String idCard, String examNo) {
        List<CustomerReg> regList = customerRegMapper.getRegListByIdCardOrExamNo(idCard, examNo);
        return regList;
    }

    @Override
    public List<CustomerReg> getRegList4ReportByIdCardOrExamNo(String idCard, String examNo) {
        List<CustomerReg> regList = customerRegMapper.getRegList4ReportByIdCardOrExamNo(idCard, examNo, null);
        return regList;
    }

    @Override
    public void updateReportPrintTimesByRegId(String id) {
        jdbcTemplate.update("update customer_reg_summary set report_print_times = report_print_times + 1,report_print_time=?,report_print_status=? where customer_reg_id =?", new Date(), ExConstants.PRINT_STATUS_已打印, id);
        jdbcTemplate.update("update customer_reg set report_print_time = ? where id  = ?", new Date(), id);

    }

    @Override
    public List<CustomerReg> getRegList4GroupByIdCardOrExamNo(String idCard, String examNo) {
        List<CustomerReg> regList = customerRegMapper.getRegList4ReportByIdCardOrExamNo(idCard, examNo, ExConstants.REG_STATUS_REGED);
        if (CollectionUtils.isNotEmpty(regList)) {
            List<String> regIdList = regList.stream().map(CustomerReg::getId).collect(Collectors.toList());
            List<CustomerRegItemGroup> groupList = customerRegItemGroupMapper.listWithItemGroupByRegIds(regIdList, false);
            Map<String, List<CustomerRegItemGroup>> groupMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(groupList)) {
                groupMap = groupList.stream().collect(Collectors.groupingBy(CustomerRegItemGroup::getCustomerRegId));
            }
            for (CustomerReg reg : regList) {
                reg.setItemGroupList(groupMap.get(reg.getId()));
            }

        }
        return regList;
    }

    @Override
    public List<CustomerRegItemGroup> addItemGroup4Machine(CustomerReg customerReg, List<ItemGroup> groupList, ItemSuit itemSuit) throws Exception {
        List<CustomerRegItemGroup> customerRegItemGroupList = com.google.common.collect.Lists.newArrayList();
        //保存大项
        if (Objects.nonNull(itemSuit)) {
            List<SuitGroup> suitGroupList = itemSuit.getSuitGroupList();
            List<String> groupId = suitGroupList.stream().map(SuitGroup::getGroupId).toList();
            List<ItemGroup> itemGroups = itemGroupMapper.selectBatchIds(groupId);
            Map<String, List<ItemGroup>> itemGroupMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(itemGroups)) {
                itemGroupMap = itemGroups.stream().collect(Collectors.groupingBy(ItemGroup::getId));
            }
            for (SuitGroup suitGroup : suitGroupList) {
                CustomerRegItemGroup customerRegItemGroup = new CustomerRegItemGroup();
                List<ItemGroup> itemGroupList = itemGroupMap.get(suitGroup.getGroupId());
                if (CollectionUtils.isEmpty(itemGroupList)) {
                    throw new Exception("未查询到相关检查项目，下单失败!");
                }
                customerRegItemGroup.setCustomerRegId(customerReg.getId());
                customerRegItemGroup.setIdCard(customerReg.getIdCard());
                customerRegItemGroup.setExamNo(customerReg.getExamNo());
                customerRegItemGroup.setCustomerId(customerReg.getCustomerId());
                customerRegItemGroup.setItemGroupId(itemGroupList.get(0).getId());
                customerRegItemGroup.setItemGroupName(itemGroupList.get(0).getName());
                customerRegItemGroup.setClassCode(itemGroupList.get(0).getClassCode());
                customerRegItemGroup.setDepartmentId(itemGroupList.get(0).getDepartmentId());
                customerRegItemGroup.setDepartmentName(itemGroupList.get(0).getDepartmentName());
                customerRegItemGroup.setDepartmentCode(itemGroupList.get(0).getDepartmentCode());
                customerRegItemGroup.setHisCode(itemGroupList.get(0).getHisCode());
                customerRegItemGroup.setHisName(itemGroupList.get(0).getHisName());
                customerRegItemGroup.setPayStatus(ExConstants.ORDER_STATUS_待支付);
                customerRegItemGroup.setItemSuitId(itemSuit.getId());
                customerRegItemGroup.setItemSuitName(itemSuit.getName());
                customerRegItemGroup.setAddMinusFlag(0);
                customerRegItemGroup.setPriceAfterDis(suitGroup.getPriceAfterDis());
                customerRegItemGroup.setPrice(suitGroup.getPrice());
                customerRegItemGroup.setMinDiscountRate(suitGroup.getMinDiscountRate());
                customerRegItemGroup.setDisRate(new BigDecimal(suitGroup.getDisRate()));
                customerRegItemGroup.setCheckPartCode(suitGroup.getCheckPartCode());
                customerRegItemGroup.setCheckPartName(suitGroup.getCheckPartName());
                customerRegItemGroup.setCheckPartId(suitGroup.getCheckPartId());
                if (StringUtils.isBlank(customerRegItemGroup.getCheckStatus())) {
                    customerRegItemGroup.setCheckStatus(ExConstants.CHECK_STATUS_未检);
                }
                customerRegItemGroup.setPayerType("个人支付");
                customerRegItemGroup.setCreateTime(new Date());
                customerRegItemGroup.setUpdateTime(new Date());
                customerRegItemGroup.setCreateBy("自助机订单");
                customerRegItemGroup.setCreateName("自助机订单");
                customerRegItemGroup.setUpdateBy("自助机订单");
                customerRegItemGroup.setUpdateName("自助机订单");
                customerRegItemGroupList.add(customerRegItemGroup);
            }

        } else {

            if (CollectionUtils.isEmpty(groupList)) {
                throw new Exception("未选择检查项目，下单失败!");
            }
            customerRegItemGroupList = groupList.stream().map(itemGroup -> {
                CustomerRegItemGroup customerRegItemGroup = new CustomerRegItemGroup();
                customerRegItemGroup.setCustomerRegId(customerReg.getId());
                customerRegItemGroup.setIdCard(customerReg.getIdCard());
                customerRegItemGroup.setExamNo(customerReg.getExamNo());
                customerRegItemGroup.setCustomerId(customerReg.getCustomerId());
                customerRegItemGroup.setItemGroupId(itemGroup.getId());
                customerRegItemGroup.setItemGroupName(itemGroup.getName());
                customerRegItemGroup.setClassCode(itemGroup.getClassCode());
                customerRegItemGroup.setDepartmentId(itemGroup.getDepartmentId());
                customerRegItemGroup.setDepartmentName(itemGroup.getDepartmentName());
                customerRegItemGroup.setDepartmentCode(itemGroup.getDepartmentCode());
                customerRegItemGroup.setHisCode(itemGroup.getHisCode());
                customerRegItemGroup.setHisName(itemGroup.getHisName());
                customerRegItemGroup.setPayStatus(ExConstants.PAY_STATUS_WAIT);
                customerRegItemGroup.setAddMinusFlag(0);
                customerRegItemGroup.setPayerType("个人支付");
                customerRegItemGroup.setPrice(itemGroup.getPrice());
                customerRegItemGroup.setPriceAfterDis(itemGroup.getPrice());
                customerRegItemGroup.setMinDiscountRate(itemGroup.getMinDiscountRate());
                customerRegItemGroup.setDisRate(BigDecimal.ONE);
                customerRegItemGroup.setCheckStatus(ExConstants.CHECK_STATUS_未检);
                customerRegItemGroup.setCreateTime(new Date());
                customerRegItemGroup.setUpdateTime(new Date());
                customerRegItemGroup.setCreateBy("自助机订单");
                customerRegItemGroup.setCreateName("自助机订单");
                customerRegItemGroup.setUpdateBy("自助机订单");
                customerRegItemGroup.setUpdateName("自助机订单");
                customerRegItemGroup.setCheckPartCode(itemGroup.getCheckPartCode());
                customerRegItemGroup.setCheckPartName(itemGroup.getCheckPartName());
                customerRegItemGroup.setCheckPartId(itemGroup.getCheckPartId());
                return customerRegItemGroup;
            }).toList();
        }
        //验证是否存在互斥项目
        itemGroupRelationService.checkIsHaveMutexes(customerRegItemGroupList);
        customerRegItemGroupService.saveBatch(customerRegItemGroupList);
        //获取附属项目
        List<CustomerRegItemGroup> attachItemGroups = itemGroupRelationService.getAttachGroups(customerRegItemGroupList);
        if (CollectionUtils.isNotEmpty(attachItemGroups)) {
            customerRegItemGroupList.addAll(attachItemGroups);
            //验证是否存在互斥项目
            itemGroupRelationService.checkIsHaveMutexes(attachItemGroups);
            customerRegItemGroupService.saveBatch(attachItemGroups);
        }

        //获取赠送项目（需要考虑主项目和附属项目的赠送项目）
        List<CustomerRegItemGroup> allItemGroups = new ArrayList<>(customerRegItemGroupList);
        List<CustomerRegItemGroup> giftItemGroups = itemGroupRelationService.getGiftGroups(allItemGroups);
        if (CollectionUtils.isNotEmpty(giftItemGroups)) {
            customerRegItemGroupList.addAll(giftItemGroups);
            //验证是否存在互斥项目
            itemGroupRelationService.checkIsHaveMutexes(giftItemGroups);
            customerRegItemGroupService.saveBatch(giftItemGroups);
        }

        return customerRegItemGroupList;

    }

    @Override
    public CompanyTeam getTeamAndLimitInfoByIdCard(String idCard) throws Exception {
        TeamCustomerLimitAmount teamCustomerLimitAmount = teamCustomerLimitAmountMapper.getLatestLimitByIdCard(idCard);
//        TeamCustomerLimitAmount teamCustomerLimitAmount = teamCustomerLimitAmountMapper.selectOne(new LambdaQueryWrapper<TeamCustomerLimitAmount>().eq(TeamCustomerLimitAmount::getIdCard, idCard).orderByDesc(TeamCustomerLimitAmount::getCreateTime).last("limit 1"));
        if (Objects.isNull(teamCustomerLimitAmount)) {
            throw new Exception("未查询到原检人团检额度记录！");
        }
        CompanyTeam team = companyTeamMapper.selectById(teamCustomerLimitAmount.getTeamId());
        if (Objects.isNull(team)) {
            throw new Exception("原检人团检分组不存在！");
        }
        CompanyReg companyReg = companyRegMapper.selectById(team.getCompanyRegId());
        team.setCompanyReg(companyReg);
        team.setCustomerName(teamCustomerLimitAmount.getName());
        team.setRemainLimitAmount(teamCustomerLimitAmount.getAmount());
        team.setCustomerLimitAmountId(teamCustomerLimitAmount.getId());
        return team;
    }

    @Override
    public List<DependentItemResultDTO> getDependentItemResults(String customerRegId, String groupId) {
        log.info("Query dependent item results for customerRegId: {}, groupId: {}", customerRegId, groupId);

        try {
            // 1. 查询项目的依赖关系
            List<ItemGroupRelation> dependencies = itemGroupRelationService.list(new LambdaQueryWrapper<ItemGroupRelation>().eq(ItemGroupRelation::getGroupId, groupId).eq(ItemGroupRelation::getRelation, "依赖"));

            if (CollectionUtils.isEmpty(dependencies)) {
                log.info("No dependencies found for groupId: {}", groupId);
                return new ArrayList<>();
            }

            List<DependentItemResultDTO> results = new ArrayList<>();

            // 2. 处理每个依赖关系
            for (ItemGroupRelation dependency : dependencies) {
                if ("GROUP".equals(dependency.getRelationItemType())) {
                    // 处理大项依赖
                    DependentItemResultDTO groupResult = buildDependentGroupResult(customerRegId, dependency);
                    if (groupResult != null) {
                        results.add(groupResult);
                    }
                } else if ("ITEM".equals(dependency.getRelationItemType())) {
                    // 处理小项依赖
                    DependentItemResultDTO itemResult = buildDependentItemResult(customerRegId, dependency);
                    if (itemResult != null) {
                        results.add(itemResult);
                    }
                }
            }

            log.info("Found {} dependent item results for groupId: {}", results.size(), groupId);
            return results;

        } catch (Exception e) {
            log.error("Failed to query dependent item results for customerRegId: {}, groupId: {}", customerRegId, groupId, e);
            throw new RuntimeException("Query dependent item results failed: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, List<DependentItemResultDTO>> getDependentItemResultsBatch(String customerRegId, List<String> groupIds) {
        log.info("Batch query dependent item results for customerRegId: {}, groupIds: {}", customerRegId, groupIds);

        Map<String, List<DependentItemResultDTO>> resultMap = new HashMap<>();

        if (CollectionUtils.isEmpty(groupIds)) {
            return resultMap;
        }

        try {
            for (String groupId : groupIds) {
                List<DependentItemResultDTO> results = getDependentItemResults(customerRegId, groupId);
                resultMap.put(groupId, results);
            }

            log.info("Batch query completed for {} groups", groupIds.size());
            return resultMap;

        } catch (Exception e) {
            log.error("Failed to batch query dependent item results for customerRegId: {}, groupIds: {}", customerRegId, groupIds, e);
            throw new RuntimeException("Batch query dependent item results failed: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, List<DependentItemResultDTO>> getAllDependentItemResults(String customerRegId) {
        log.info("Query all dependent item results for customerRegId: {}", customerRegId);

        try {
            // 1. 获取该体检人员的所有项目组
            List<CustomerRegItemGroup> itemGroups = customerRegItemGroupService.list(new LambdaQueryWrapper<CustomerRegItemGroup>().eq(CustomerRegItemGroup::getCustomerRegId, customerRegId).ne(CustomerRegItemGroup::getAddMinusFlag, "-1").ne(CustomerRegItemGroup::getPayStatus, ExConstants.REFUND_STATE_退款成功));

            if (CollectionUtils.isEmpty(itemGroups)) {
                log.info("No item groups found for customerRegId: {}", customerRegId);
                return new HashMap<>();
            }

            // 2. 提取所有项目组ID
            List<String> groupIds = itemGroups.stream().map(CustomerRegItemGroup::getItemGroupId).distinct().collect(Collectors.toList());

            // 3. 批量查询依赖项目结果
            Map<String, List<DependentItemResultDTO>> results = getDependentItemResultsBatch(customerRegId, groupIds);

            log.info("Query all dependent item results completed for customerRegId: {}, found {} groups with dependencies", customerRegId, results.size());
            return results;

        } catch (Exception e) {
            log.error("Failed to query all dependent item results for customerRegId: {}", customerRegId, e);
            throw new RuntimeException("Query all dependent item results failed: " + e.getMessage(), e);
        }
    }

    /**
     * 构建依赖大项结果
     */
    private DependentItemResultDTO buildDependentGroupResult(String customerRegId, ItemGroupRelation dependency) {
        try {
            String dependentGroupId = dependency.getRelationGroupId();

            // 查询大项基本信息
            ItemGroup itemGroup = itemGroupMapper.selectById(dependentGroupId);
            if (itemGroup == null) {
                log.warn("ItemGroup not found for id: {}", dependentGroupId);
                return null;
            }

            // 查询该体检人员的项目组记录
            CustomerRegItemGroup regItemGroup = customerRegItemGroupService.getOne(new LambdaQueryWrapper<CustomerRegItemGroup>().eq(CustomerRegItemGroup::getCustomerRegId, customerRegId).eq(CustomerRegItemGroup::getItemGroupId, dependentGroupId).ne(CustomerRegItemGroup::getAddMinusFlag, "-1").ne(CustomerRegItemGroup::getPayStatus, ExConstants.REFUND_STATE_退款成功).last("LIMIT 1"));

            // 构建结果DTO
            DependentItemResultDTO result = new DependentItemResultDTO();
            result.setItemType("GROUP");
            result.setCustomerRegId(customerRegId);
            result.setItemId(dependentGroupId);
            result.setItemName(itemGroup.getName());
            result.setHisCode(itemGroup.getHisCode());
            result.setHisName(itemGroup.getHisName());
            result.setDepartmentId(itemGroup.getDepartmentId());

            if (regItemGroup != null) {
                result.setCustomerRegItemGroupId(regItemGroup.getId());
                result.setCheckStatus(regItemGroup.getCheckStatus());
                result.setDepartmentName(regItemGroup.getDepartmentName());
                result.setIsCompleted(ExConstants.CHECK_STATUS_已检.equals(regItemGroup.getCheckStatus()));

                // 设置大项的值（检查状态的中文描述）
                result.setValue(getCheckStatusText(regItemGroup.getCheckStatus()));

                // 设置异常标志
                result.setAbnormalFlag("1".equals(regItemGroup.getAbnormalFlag()) ? "Y" : "N");
            } else {
                // 未登记该项目
                result.setCheckStatus(ExConstants.CHECK_STATUS_未检);
                result.setValue("未登记");
                result.setIsCompleted(false);
                result.setAbnormalFlag("N");
            }

            return result;

        } catch (Exception e) {
            log.error("Failed to build dependent group result for dependency: {}", dependency, e);
            return null;
        }
    }

    /**
     * 构建依赖小项结果
     */
    private DependentItemResultDTO buildDependentItemResult(String customerRegId, ItemGroupRelation dependency) {
        try {
            String dependentGroupId = dependency.getRelationGroupId();
            String dependentItemId = dependency.getRelationItemId();

            ItemInfo itemInfo = null;
            if (StringUtils.isNotBlank(dependentItemId)) {
                // 查询小项基本信息
                itemInfo = itemInfoMapper.selectById(dependentItemId);
            }
            if (itemInfo == null) {
                log.warn("ItemInfo not found for id: {}", dependentItemId);
                return null;
            }

            // 查询该体检人员的项目组记录
            CustomerRegItemGroup regItemGroup = customerRegItemGroupService.getOne(new LambdaQueryWrapper<CustomerRegItemGroup>().eq(CustomerRegItemGroup::getCustomerRegId, customerRegId).eq(CustomerRegItemGroup::getItemGroupId, dependentGroupId).ne(CustomerRegItemGroup::getAddMinusFlag, "-1").ne(CustomerRegItemGroup::getPayStatus, ExConstants.REFUND_STATE_退款成功).last("LIMIT 1"));

            // 构建结果DTO
            DependentItemResultDTO result = new DependentItemResultDTO();
            result.setItemType("ITEM");
            result.setCustomerRegId(customerRegId);
            result.setItemId(dependentItemId);
            result.setItemName(itemInfo.getName());
            result.setHisCode(itemInfo.getHisCode());
            result.setHisName(itemInfo.getHisName());
            result.setUnit(itemInfo.getUnit());
            result.setValueRefRange(itemInfo.getNormalRef());
            result.setDepartmentId(itemInfo.getDepartmentId());

            if (regItemGroup != null) {
                result.setCustomerRegItemGroupId(regItemGroup.getId());
                result.setDepartmentName(regItemGroup.getDepartmentName());

                // 查询小项结果
                CustomerRegItemResult itemResult = customerRegItemResultService.getOne(new LambdaQueryWrapper<CustomerRegItemResult>().eq(CustomerRegItemResult::getCustomerRegId, customerRegId).eq(CustomerRegItemResult::getItemHisCode, itemInfo.getHisCode()).last("LIMIT 1"));

                if (itemResult != null) {
                    result.setValue(itemResult.getValue());
                    result.setAbnormalFlag(itemResult.getAbnormalFlag());
                    result.setCheckStatus(itemResult.getAbandonFlag() == 1 ? ExConstants.CHECK_STATUS_放弃 : ExConstants.CHECK_STATUS_已检);
                    result.setIsCompleted(itemResult.getAbandonFlag() != 1);
                    result.setResultTime(itemResult.getCreateTime() != null ? DateUtil.formatDateTime(itemResult.getCreateTime()) : null);
                    result.setInputOperator(itemResult.getCreateBy());
                } else {
                    // 未录入结果
                    result.setValue("");
                    result.setAbnormalFlag("N");
                    result.setCheckStatus(ExConstants.CHECK_STATUS_未检);
                    result.setIsCompleted(false);
                }
            } else {
                // 未登记该项目
                result.setValue("");
                result.setAbnormalFlag("N");
                result.setCheckStatus(ExConstants.CHECK_STATUS_未检);
                result.setIsCompleted(false);
            }

            return result;

        } catch (Exception e) {
            log.error("Failed to build dependent item result for dependency: {}", dependency, e);
            return null;
        }
    }

    /**
     * 获取检查状态的中文描述
     */
    private String getCheckStatusText(String checkStatus) {
        if (StringUtils.isBlank(checkStatus)) {
            return "未开始";
        }

        switch (checkStatus) {
            case ExConstants.CHECK_STATUS_已检:
                return "已检查";
            case ExConstants.CHECK_STATUS_未检:
                return "未检查";
            case ExConstants.CHECK_STATUS_放弃:
                return "已放弃";
            default:
                return checkStatus;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addItemGroupWithCheckParts(AddItemGroupWithCheckPartsRequest request) {
        // 1. 验证参数
        if (request == null || StringUtils.isEmpty(request.getCustomerRegId()) || CollectionUtils.isEmpty(request.getItemGroups())) {
            throw new JeecgBootException("参数不能为空");
        }

        // 2. 获取体检登记信息
//        CustomerReg customerReg = getById(request.getCustomerRegId());
//        if (customerReg == null) {
//            throw new JeecgBootException("体检登记不存在");
//        }

        // 3. 使用前端传入的项目列表（前端已完成数据拼装）
        List<CustomerRegItemGroup> customerRegItemGroupList = request.getItemGroups();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        customerRegItemGroupList.forEach(g -> {
            g.setCreateBy(sysUser.getUsername());
            g.setCreateName(sysUser.getRealname());
            g.setUpdateBy(sysUser.getUsername());
            g.setUpdateName(sysUser.getRealname());
            if (StringUtils.isBlank(g.getCheckStatus())) {
                g.setCheckStatus(ExConstants.CHECK_STATUS_未检);
            }
        });

        // 4. 验证项目列表
        if (CollectionUtils.isEmpty(customerRegItemGroupList)) {
            throw new JeecgBootException("项目列表不能为空");
        }

        // 5. 验证是否存在互斥项目
        try {
            itemGroupRelationService.checkIsHaveMutexes(customerRegItemGroupList);
        } catch (Exception e) {
            throw new JeecgBootException("项目互斥验证失败：" + e.getMessage());
        }

        // 5.1 验证依赖项目
        try {
            itemGroupRelationService.checkDependentGroups(customerRegItemGroupList);
        } catch (Exception e) {
            throw new JeecgBootException("项目依赖验证失败：" + e.getMessage());
        }

        // 6. 批量保存主项目
        customerRegItemGroupService.saveBatch(customerRegItemGroupList);

        // 7. 获取附属项目
        List<CustomerRegItemGroup> attachItemGroups = itemGroupRelationService.getAttachGroups(customerRegItemGroupList);
        if (CollectionUtils.isNotEmpty(attachItemGroups)) {
            customerRegItemGroupList.addAll(attachItemGroups);
            //验证是否存在互斥项目
            try {
                itemGroupRelationService.checkIsHaveMutexes(attachItemGroups);
            } catch (Exception e) {
                throw new JeecgBootException("附属项目互斥验证失败：" + e.getMessage());
            }
            //验证附属项目的依赖关系
            try {
                itemGroupRelationService.checkDependentGroups(attachItemGroups);
            } catch (Exception e) {
                throw new JeecgBootException("附属项目依赖验证失败：" + e.getMessage());
            }
            customerRegItemGroupService.saveBatch(attachItemGroups);
        }

        // 8. 获取赠送项目（需要考虑主项目和附属项目的赠送项目）
        List<CustomerRegItemGroup> allItemGroups = new ArrayList<>(customerRegItemGroupList);
        List<CustomerRegItemGroup> giftItemGroups = itemGroupRelationService.getGiftGroups(allItemGroups);
        if (CollectionUtils.isNotEmpty(giftItemGroups)) {
            customerRegItemGroupList.addAll(giftItemGroups);
            //验证是否存在互斥项目
            try {
                itemGroupRelationService.checkIsHaveMutexes(giftItemGroups);
            } catch (Exception e) {
                throw new JeecgBootException("赠送项目互斥验证失败：" + e.getMessage());
            }
            //验证赠送项目的依赖关系
            try {
                itemGroupRelationService.checkDependentGroups(giftItemGroups);
            } catch (Exception e) {
                throw new JeecgBootException("赠送项目依赖验证失败：" + e.getMessage());
            }
            customerRegItemGroupService.saveBatch(giftItemGroups);
        }

        // 10. 异步更新使用频次（如果有部位信息）
        Set<String> itemGroupIds = customerRegItemGroupList.stream().map(CustomerRegItemGroup::getItemGroupId).collect(Collectors.toSet());
        Set<String> checkPartIds = customerRegItemGroupList.stream().filter(item -> StringUtils.isNotBlank(item.getCheckPartId())).map(CustomerRegItemGroup::getCheckPartId).collect(Collectors.toSet());

        for (String itemGroupId : itemGroupIds) {
            List<String> partIds = customerRegItemGroupList.stream().filter(item -> itemGroupId.equals(item.getItemGroupId()) && StringUtils.isNotBlank(item.getCheckPartId())).map(CustomerRegItemGroup::getCheckPartId).collect(Collectors.toList());
            if (!partIds.isEmpty()) {
                checkPartDictService.updateUsageFrequency(itemGroupId, partIds);
            }
        }

        log.info("成功添加{}个检查项目，包含{}个附属项目和{}个赠送项目", customerRegItemGroupList.size() - (attachItemGroups != null ? attachItemGroups.size() : 0) - (giftItemGroups != null ? giftItemGroups.size() : 0), attachItemGroups != null ? attachItemGroups.size() : 0, giftItemGroups != null ? giftItemGroups.size() : 0);
    }

    /**
     * 处理检验科项目按空腹标志分组
     * 如果是检验科项目，则根据是否空腹分成两组，多个检验项目的itemGroupName用逗号拼接
     * 其他科室的项目保持现状
     *
     * @param groupList 原始项目列表
     * @return 处理后的项目列表
     */
    private List<CustomerRegItemGroup> processLabItemsByFastsFlag(List<CustomerRegItemGroup> groupList) {
        if (CollectionUtils.isEmpty(groupList)) {
            return new ArrayList<>();
        }

        List<CustomerRegItemGroup> result = new ArrayList<>();

        // 分离检验科项目和其他科室项目
        List<CustomerRegItemGroup> labItems = new ArrayList<>();
        List<CustomerRegItemGroup> otherItems = new ArrayList<>();

        for (CustomerRegItemGroup group : groupList) {
            if (isLabItem(group)) {
                labItems.add(group);
            } else {
                otherItems.add(group);
            }
        }

        // 处理检验科项目按空腹标志分组
        if (CollectionUtils.isNotEmpty(labItems)) {
            Map<Integer, List<CustomerRegItemGroup>> labItemsByFastsFlag = labItems.stream().collect(Collectors.groupingBy(item -> {
                // 获取空腹标志，默认为0（非空腹）
                Integer fastsFlag = 0;
                if (item.getItemGroup() != null && item.getItemGroup().getFastsFlag() != null) {
                    fastsFlag = item.getItemGroup().getFastsFlag();
                }
                return fastsFlag;
            }));

            // 为每个空腹标志组创建合并的项目
            labItemsByFastsFlag.forEach((fastsFlag, items) -> {
                if (CollectionUtils.isNotEmpty(items)) {
                    CustomerRegItemGroup mergedItem = createMergedLabItem(items, fastsFlag);
                    result.add(mergedItem);
                }
            });
        }

        // 添加其他科室项目（保持原状）
        result.addAll(otherItems);

        return result;
    }

    /**
     * 判断是否为检验科项目
     * 通过项目类别classCode等于"检验"来判断
     *
     * @param group 项目组
     * @return 是否为检验科项目
     */
    private boolean isLabItem(CustomerRegItemGroup group) {
        // 通过项目类别判断
        if (StringUtils.isNotBlank(group.getClassCode()) && StringUtils.equals(group.getClassCode(), "检验")) {
            return true;
        }

        // 通过ItemGroup的classCode判断
        if (group.getItemGroup() != null && StringUtils.isNotBlank(group.getItemGroup().getClassCode()) && StringUtils.equals(group.getItemGroup().getClassCode(), "检验")) {
            return true;
        }

        return false;
    }

    /**
     * 创建合并的检验科项目
     * 将同一空腹标志的多个检验项目合并为一个，项目名称用逗号拼接
     *
     * @param items     同一空腹标志的检验项目列表
     * @param fastsFlag 空腹标志
     * @return 合并后的项目
     */
    private CustomerRegItemGroup createMergedLabItem(List<CustomerRegItemGroup> items, Integer fastsFlag) {
        if (CollectionUtils.isEmpty(items)) {
            return null;
        }

        // 使用第一个项目作为基础
        CustomerRegItemGroup baseItem = items.get(0);
        CustomerRegItemGroup mergedItem = new CustomerRegItemGroup();

        // 复制基础信息
        mergedItem.setId(baseItem.getId());
        mergedItem.setCustomerRegId(baseItem.getCustomerRegId());
        mergedItem.setExamNo(baseItem.getExamNo());
        mergedItem.setDepartmentId(baseItem.getDepartmentId());
        mergedItem.setDepartmentName(baseItem.getDepartmentName());
        mergedItem.setDepartmentCode(baseItem.getDepartmentCode());
        mergedItem.setClassCode(baseItem.getClassCode());
        mergedItem.setDepartFunction(baseItem.getDepartFunction());
        mergedItem.setDepartment(baseItem.getDepartment());
        mergedItem.setItemGroup(baseItem.getItemGroup());

        // 拼接项目名称
        String mergedItemGroupName = items.stream().map(CustomerRegItemGroup::getItemGroupName).filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
        mergedItem.setItemGroupName(mergedItemGroupName);

        // 设置空腹标志相关信息
        if (mergedItem.getItemGroup() != null) {
            mergedItem.getItemGroup().setFastsFlagStr(Objects.equals(fastsFlag, 1) ? "空腹项目" : "非空腹项目");
        }

        return mergedItem;
    }

    /**
     * 获取危害因素缓存映射
     * 使用双重检查锁定模式确保线程安全和性能
     */
    private void ensureRiskFactorCacheLoaded() {
        long currentTime = System.currentTimeMillis();

        // 检查缓存是否需要刷新
        if (riskFactorNameToCodeCache == null || riskFactorCodeToCodeCache == null || (currentTime - riskFactorCacheTimestamp) > CACHE_EXPIRE_TIME) {

            synchronized (this) {
                // 双重检查
                if (riskFactorNameToCodeCache == null || riskFactorCodeToCodeCache == null || (currentTime - riskFactorCacheTimestamp) > CACHE_EXPIRE_TIME) {

                    log.info("刷新危害因素缓存...");

                    try {
                        // 查询所有危害因素
                        List<ZyRiskFactor> allRiskFactors = riskFactorService.list();

                        Map<String, String> nameToCodeMap = new HashMap<>();
                        Map<String, String> codeToCodeMap = new HashMap<>();

                        for (ZyRiskFactor factor : allRiskFactors) {
                            if (StringUtils.isNotBlank(factor.getName())) {
                                nameToCodeMap.put(factor.getName().trim(), factor.getCode());
                            }
                            if (StringUtils.isNotBlank(factor.getCode())) {
                                codeToCodeMap.put(factor.getCode().trim(), factor.getCode());
                            }
                        }

                        // 原子性更新缓存
                        this.riskFactorNameToCodeCache = nameToCodeMap;
                        this.riskFactorCodeToCodeCache = codeToCodeMap;
                        this.riskFactorCacheTimestamp = currentTime;

                        log.info("危害因素缓存刷新完成，共加载 {} 个危害因素", allRiskFactors.size());

                    } catch (Exception e) {
                        log.error("刷新危害因素缓存失败", e);
                        // 如果刷新失败，保持旧缓存继续使用
                    }
                }
            }
        }
    }

    /**
     * 清除危害因素缓存
     * 当危害因素基础数据发生变更时调用此方法
     */
    public void clearRiskFactorCache() {
        synchronized (this) {
            this.riskFactorNameToCodeCache = null;
            this.riskFactorCodeToCodeCache = null;
            this.riskFactorCacheTimestamp = 0;
            log.info("危害因素缓存已清除");
        }
    }

    /**
     * 标准化和验证危害因素字符串
     * 兼容riskFactor自动处理失败的情况，没有将危害因素名称自动解析成代码
     *
     * @param originalRiskFactor 原始危害因素字符串
     * @return 标准化后的危害因素代码字符串
     */
    private String normalizeAndValidateRiskFactors(String originalRiskFactor) {
        if (StringUtils.isBlank(originalRiskFactor)) {
            return originalRiskFactor;
        }

        try {
            // 1. 预处理：统一分隔符，支持多种分隔符
            String normalizedInput = originalRiskFactor.replaceAll("[，；;、|\\s]+", ",")  // 统一转换为英文逗号
                    .replaceAll("^,+|,+$", "")        // 去除首尾逗号
                    .replaceAll(",+", ",");           // 合并多个连续逗号

            if (StringUtils.isBlank(normalizedInput)) {
                log.warn("危害因素标准化后为空: {}", originalRiskFactor);
                return originalRiskFactor;
            }

            // 2. 分割危害因素
            String[] riskFactorNames = StringUtils.split(normalizedInput, ",");
            if (riskFactorNames.length == 0) {
                log.warn("危害因素分割后为空: {}", originalRiskFactor);
                return originalRiskFactor;
            }

            // 3. 快速检查：如果输入已经是标准格式，直接返回
            if (isAlreadyStandardFormat(riskFactorNames)) {
                log.debug("危害因素已是标准格式，无需转换: {}", originalRiskFactor);
                return normalizedInput;
            }

            // 4. 确保危害因素缓存已加载
            ensureRiskFactorCacheLoaded();

            // 使用缓存的映射关系
            Map<String, String> nameToCodeMap = this.riskFactorNameToCodeCache;
            Map<String, String> codeToCodeMap = this.riskFactorCodeToCodeCache;

            if (nameToCodeMap == null || codeToCodeMap == null) {
                log.warn("危害因素缓存未加载，保持原始值: {}", originalRiskFactor);
                return originalRiskFactor;
            }

            // 5. 转换危害因素名称为代码
            List<String> validCodes = new ArrayList<>();
            List<String> invalidFactors = new ArrayList<>();

            for (String factorName : riskFactorNames) {
                String trimmedName = factorName.trim();
                if (StringUtils.isBlank(trimmedName)) {
                    continue;
                }

                // 首先检查是否已经是代码
                if (codeToCodeMap.containsKey(trimmedName)) {
                    validCodes.add(trimmedName);
                    continue;
                }

                // 然后检查是否是名称，可以转换为代码
                if (nameToCodeMap.containsKey(trimmedName)) {
                    validCodes.add(nameToCodeMap.get(trimmedName));
                    continue;
                }

                // 模糊匹配：去除空格、特殊字符后再匹配
                String cleanedName = trimmedName.replaceAll("[\\s\\-_]+", "");
                boolean found = false;

                for (Map.Entry<String, String> entry : nameToCodeMap.entrySet()) {
                    String cleanedDbName = entry.getKey().replaceAll("[\\s\\-_]+", "");
                    if (cleanedDbName.equals(cleanedName) || cleanedDbName.contains(cleanedName) || cleanedName.contains(cleanedDbName)) {
                        validCodes.add(entry.getValue());
                        found = true;
                        break;
                    }
                }

                if (!found) {
                    invalidFactors.add(trimmedName);
                }
            }

            // 6. 记录处理结果
            if (!invalidFactors.isEmpty()) {
                log.warn("以下危害因素无法识别: {} (原始输入: {})", invalidFactors, originalRiskFactor);
            }

            if (validCodes.isEmpty()) {
                log.warn("没有找到有效的危害因素代码，保持原始值: {}", originalRiskFactor);
                return originalRiskFactor;
            }

            // 7. 返回标准化的代码字符串
            String result = String.join(",", validCodes);
            if (!result.equals(originalRiskFactor)) {
                log.info("危害因素已标准化: {} -> {}", originalRiskFactor, result);
            }

            return result;
        } catch (Exception e) {
            log.error("危害因素标准化处理失败，保持原始值: {}", originalRiskFactor, e);
            return originalRiskFactor;
        }
    }

    /**
     * 检查危害因素是否已经是标准格式
     * 标准格式的特征：
     * 1. 只包含英文字母、数字、下划线
     * 2. 通常为大写字母
     * 3. 不包含中文字符
     *
     * @param riskFactorNames 危害因素名称数组
     * @return true-已是标准格式，false-需要转换
     */
    private boolean isAlreadyStandardFormat(String[] riskFactorNames) {
        if (riskFactorNames == null || riskFactorNames.length == 0) {
            return false;
        }

        try {
            // 确保缓存已加载
            ensureRiskFactorCacheLoaded();
            Map<String, String> codeToCodeMap = this.riskFactorCodeToCodeCache;

            if (codeToCodeMap == null) {
                return false;
            }

            // 检查所有因素是否都是有效的代码
            for (String factorName : riskFactorNames) {
                String trimmedName = factorName.trim();
                if (StringUtils.isBlank(trimmedName)) {
                    continue;
                }

                // 检查是否在代码映射中存在
                if (!codeToCodeMap.containsKey(trimmedName)) {
                    return false;
                }

                // 额外检查：标准代码通常不包含中文字符
                if (containsChinese(trimmedName)) {
                    return false;
                }
            }

            return true;

        } catch (Exception e) {
            log.warn("检查危害因素标准格式时发生异常", e);
            return false;
        }
    }

    /**
     * 检查字符串是否包含中文字符
     *
     * @param str 待检查的字符串
     * @return true-包含中文，false-不包含中文
     */
    private boolean containsChinese(String str) {
        if (StringUtils.isBlank(str)) {
            return false;
        }

        for (char c : str.toCharArray()) {
            if (c >= 0x4e00 && c <= 0x9fa5) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取带完整依赖关系分析的项目列表
     */
    @Override
    public CustomerRegItemGroupAnalysisVO getItemGroupWithDependencyAnalysis(String customerRegId) {
        log.info("开始获取登记ID: {} 的完整依赖关系分析", customerRegId);

        CustomerRegItemGroupAnalysisVO result = new CustomerRegItemGroupAnalysisVO();

        // 1. 获取基础项目列表（使用现有方法）
        List<CustomerRegItemGroup> baseItems = getItemGroupByCustomerRegId(customerRegId);
        result.setItems(baseItems);

        if (CollectionUtils.isEmpty(baseItems)) {
            log.info("登记ID: {} 没有项目数据", customerRegId);
            result.setSummary(new DependencySummaryVO());
            return result;
        }

        log.info("获取到 {} 个基础项目", baseItems.size());

        // 2. 提取所有项目ID（去重）
        List<String> itemGroupIds = baseItems.stream().map(CustomerRegItemGroup::getItemGroupId).distinct().collect(Collectors.toList());

        // 3. 批量分析依赖关系
        List<DependencyAnalysisVO> dependencyAnalyses = itemGroupRelationService.analyzeDependencies(itemGroupIds, customerRegId);

        // 4. 将分析结果合并到项目数据中（处理可能的重复key）
        Map<String, DependencyAnalysisVO> analysisMap = dependencyAnalyses.stream().collect(Collectors.toMap(DependencyAnalysisVO::getItemGroupId, Function.identity(), (existing, replacement) -> {
            log.warn("发现重复的项目ID: {}, 使用第一个分析结果", existing.getItemGroupId());
            return existing;
        }));

        for (CustomerRegItemGroup item : baseItems) {
            DependencyAnalysisVO analysis = analysisMap.get(item.getItemGroupId());
            if (analysis != null) {
                // 设置依赖关系信息
                item.setDependentGroups(analysis.getDependentGroups());
                item.setAttachGroups(analysis.getAttachGroups());
                item.setGiftGroups(analysis.getGiftGroups());
                item.setExclusiveGroups(analysis.getExclusiveGroups());
                item.setMissingDependencies(analysis.getMissingDependencies());
                item.setSourceType(analysis.getSourceType());

                // 设置关系标识
                RelationBadgeVO badge = createRelationBadge(analysis.getSourceType(), item);
                item.setRelationBadge(badge);
            }
        }

        // 5. 生成依赖关系摘要
        DependencySummaryVO summary = generateDependencySummary(baseItems, dependencyAnalyses);
        result.setSummary(summary);

        log.info("完成依赖关系分析，返回 {} 个项目", baseItems.size());

        return result;
    }

    /**
     * 创建关系标识
     */
    private RelationBadgeVO createRelationBadge(String sourceType, CustomerRegItemGroup item) {
        RelationBadgeVO badge = new RelationBadgeVO();

        switch (sourceType) {
            case "dependent":
                badge.setText("依赖");
                badge.setColor("#1890ff");
                badge.setBackgroundColor("#e6f7ff");
                badge.setTitle("此项目是依赖项目");
                break;
            case "gift":
                badge.setText("赠送");
                badge.setColor("#52c41a");
                badge.setBackgroundColor("#f6ffed");
                badge.setTitle("此项目是赠送项目");
                break;
            case "attach":
                badge.setText("附属");
                badge.setColor("#fa8c16");
                badge.setBackgroundColor("#fff7e6");
                badge.setTitle("此项目是附属项目");
                break;
            case "suit":
                badge.setText("套餐");
                badge.setColor("#722ed1");
                badge.setBackgroundColor("#f9f0ff");
                badge.setTitle("此项目来自套餐: " + (StringUtils.isNotBlank(item.getItemSuitName()) ? item.getItemSuitName() : ""));
                break;
            default:
                badge.setText("主项");
                badge.setColor("#595959");
                badge.setBackgroundColor("#f5f5f5");
                badge.setTitle("此项目是主项目");
                break;
        }

        return badge;
    }

    /**
     * 生成依赖关系摘要
     */
    private DependencySummaryVO generateDependencySummary(List<CustomerRegItemGroup> items, List<DependencyAnalysisVO> analyses) {
        DependencySummaryVO summary = new DependencySummaryVO();
        summary.setTotalItems(items.size());

        // 统计各类型项目数量
        Map<String, Long> sourceTypeCount = analyses.stream().collect(Collectors.groupingBy(DependencyAnalysisVO::getSourceType, Collectors.counting()));

        summary.setMainItems(sourceTypeCount.getOrDefault("main", 0L).intValue());
        summary.setDependentItems(sourceTypeCount.getOrDefault("dependent", 0L).intValue());
        summary.setGiftItems(sourceTypeCount.getOrDefault("gift", 0L).intValue());
        summary.setAttachItems(sourceTypeCount.getOrDefault("attach", 0L).intValue());
        summary.setSuitItems(sourceTypeCount.getOrDefault("suit", 0L).intValue());

        // 收集所有缺失依赖
        List<MissingDependencyVO> allMissingDeps = analyses.stream().flatMap(analysis -> analysis.getMissingDependencies().stream()).collect(Collectors.toList());

        summary.setMissingDependencies(allMissingDeps);
        summary.setHasConflicts(!allMissingDeps.isEmpty());

        return summary;
    }

    /**
     * 智能添加项目组合（自动处理子项目）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addItemGroupWithAutoSubItems(List<CustomerRegItemGroup> groupList) throws Exception {
        addItemGroupWithAutoSubItems(groupList, false);
    }

    /**
     * 智能添加项目组合（自动处理子项目，支持跳过某些类型）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addItemGroupWithAutoSubItems(List<CustomerRegItemGroup> groupList, boolean skipGiftAndAttach) throws Exception {
        if (groupList == null || groupList.isEmpty()) {
            return;
        }

        log.info("开始智能添加项目组合，项目数量: {}, 跳过赠送和附属: {}", groupList.size(), skipGiftAndAttach);

        String customerRegId = groupList.get(0).getCustomerRegId();
        CustomerReg customerReg = getById(customerRegId);
        if (customerReg == null) {
            throw new Exception("未找到登记记录");
        }

        // 1. 检查互斥关系
        itemGroupRelationService.checkIsHaveMutexes(groupList);

        // 2. 获取现有项目列表
        List<CustomerRegItemGroup> existingItems = customerRegItemGroupService.list(new LambdaQueryWrapper<CustomerRegItemGroup>().eq(CustomerRegItemGroup::getCustomerRegId, customerRegId).ne(CustomerRegItemGroup::getAddMinusFlag, -1).ne(CustomerRegItemGroup::getPayStatus, "退款成功"));

        Set<String> existingItemIds = existingItems.stream().map(CustomerRegItemGroup::getItemGroupId).collect(Collectors.toSet());

        // 3. 分析需要添加的项目的依赖关系（去重）
        List<String> addingItemIds = groupList.stream().map(CustomerRegItemGroup::getItemGroupId).distinct().collect(Collectors.toList());

        List<DependencyAnalysisVO> dependencyAnalyses = itemGroupRelationService.analyzeDependencies(addingItemIds, customerRegId);

        // 4. 收集所有需要自动添加的子项目
        List<CustomerRegItemGroup> allItemsToAdd = new ArrayList<>(groupList);
        List<CustomerRegItemGroup> autoAddedItems = new ArrayList<>();

        if (!skipGiftAndAttach) {
            // 4.1 添加缺失的依赖项目
            autoAddedItems.addAll(addMissingDependentItems(groupList, dependencyAnalyses, existingItemIds));

            // 4.2 添加附属项目
            autoAddedItems.addAll(addAttachItems(groupList, dependencyAnalyses));

            // 4.3 添加赠送项目（包括主项目和附属项目的赠送项目）
            List<CustomerRegItemGroup> allMainAndAttachItems = new ArrayList<>(groupList);
            allMainAndAttachItems.addAll(autoAddedItems.stream().filter(item -> "attach".equals(getItemSourceType(item, dependencyAnalyses))).collect(Collectors.toList()));

            autoAddedItems.addAll(addGiftItems(allMainAndAttachItems, dependencyAnalyses));
        }

        allItemsToAdd.addAll(autoAddedItems);

        // 5. 设置通用属性
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String operatorBy = Objects.nonNull(sysUser) ? sysUser.getUsername() : null;
        String operatorName = Objects.nonNull(sysUser) ? sysUser.getRealname() : null;

        allItemsToAdd.forEach(group -> {
            if (StringUtils.isBlank(group.getPayStatus())) {
                group.setPayStatus(ExConstants.PAY_STATUS_WAIT);
            }
            if (StringUtils.isBlank(group.getCheckStatus())) {
                group.setCheckStatus(ExConstants.CHECK_STATUS_未检);
            }
            if (group.getCreateTime() == null) {
                group.setCreateTime(new Date());
            }
            group.setUpdateTime(new Date());
            group.setCreateBy(operatorBy);
            group.setCreateName(operatorName);
            group.setUpdateBy(operatorBy);
            group.setUpdateName(operatorName);
        });

        // 6. 最终的互斥检查（包括自动添加的项目）
        itemGroupRelationService.checkIsHaveMutexes(allItemsToAdd);

        // 7. 批量保存所有项目
        customerRegItemGroupService.saveBatch(allItemsToAdd);

        log.info("智能添加项目完成，主项目: {}, 自动添加子项目: {}, 总计: {}", groupList.size(), autoAddedItems.size(), allItemsToAdd.size());
    }

    /**
     * 添加缺失的依赖项目
     */
    private List<CustomerRegItemGroup> addMissingDependentItems(List<CustomerRegItemGroup> mainItems, List<DependencyAnalysisVO> dependencyAnalyses, Set<String> existingItemIds) {
        List<CustomerRegItemGroup> dependentItems = new ArrayList<>();

        for (DependencyAnalysisVO analysis : dependencyAnalyses) {
            if (CollectionUtils.isNotEmpty(analysis.getMissingDependencies())) {
                CustomerRegItemGroup mainItem = findItemById(mainItems, analysis.getItemGroupId());
                if (mainItem != null) {
                    for (MissingDependencyVO missingDep : analysis.getMissingDependencies()) {
                        if (!existingItemIds.contains(missingDep.getDependentId())) {
                            CustomerRegItemGroup dependentItem = createDependentItem(mainItem, missingDep.getDependentId());
                            if (dependentItem != null) {
                                dependentItems.add(dependentItem);
                                existingItemIds.add(missingDep.getDependentId()); // 避免重复添加
                                log.info("自动添加依赖项目: {} -> {}", mainItem.getItemGroupName(), dependentItem.getItemGroupName());
                            }
                        }
                    }
                }
            }
        }

        return dependentItems;
    }

    /**
     * 添加附属项目
     */
    private List<CustomerRegItemGroup> addAttachItems(List<CustomerRegItemGroup> mainItems, List<DependencyAnalysisVO> dependencyAnalyses) {
        List<CustomerRegItemGroup> attachItems = new ArrayList<>();

        for (DependencyAnalysisVO analysis : dependencyAnalyses) {
            if (CollectionUtils.isNotEmpty(analysis.getAttachGroups())) {
                CustomerRegItemGroup mainItem = findItemById(mainItems, analysis.getItemGroupId());
                if (mainItem != null) {
                    for (RelationItemVO attachGroup : analysis.getAttachGroups()) {
                        List<CustomerRegItemGroup> attachItemsForGroup = createRelationItems(mainItem, attachGroup, "attach");
                        attachItems.addAll(attachItemsForGroup);
                        log.info("自动添加附属项目: {} -> {} (数量: {})", mainItem.getItemGroupName(), attachGroup.getRelationGroupName(), attachItemsForGroup.size());
                    }
                }
            }
        }

        return attachItems;
    }

    /**
     * 添加赠送项目
     */
    private List<CustomerRegItemGroup> addGiftItems(List<CustomerRegItemGroup> sourceItems, List<DependencyAnalysisVO> dependencyAnalyses) {
        List<CustomerRegItemGroup> giftItems = new ArrayList<>();

        for (CustomerRegItemGroup sourceItem : sourceItems) {
            DependencyAnalysisVO analysis = findAnalysisById(dependencyAnalyses, sourceItem.getItemGroupId());
            if (analysis != null && CollectionUtils.isNotEmpty(analysis.getGiftGroups())) {
                for (RelationItemVO giftGroup : analysis.getGiftGroups()) {
                    List<CustomerRegItemGroup> giftItemsForGroup = createRelationItems(sourceItem, giftGroup, "gift");
                    giftItems.addAll(giftItemsForGroup);
                    log.info("自动添加赠送项目: {} -> {} (数量: {})", sourceItem.getItemGroupName(), giftGroup.getRelationGroupName(), giftItemsForGroup.size());
                }
            }
        }

        return giftItems;
    }

    /**
     * 根据ID查找项目
     */
    private CustomerRegItemGroup findItemById(List<CustomerRegItemGroup> items, String itemGroupId) {
        return items.stream().filter(item -> itemGroupId.equals(item.getItemGroupId())).findFirst().orElse(null);
    }

    /**
     * 根据ID查找依赖分析结果
     */
    private DependencyAnalysisVO findAnalysisById(List<DependencyAnalysisVO> analyses, String itemGroupId) {
        return analyses.stream().filter(analysis -> itemGroupId.equals(analysis.getItemGroupId())).findFirst().orElse(null);
    }

    /**
     * 创建依赖项目
     */
    private CustomerRegItemGroup createDependentItem(CustomerRegItemGroup mainItem, String dependentItemId) {
        try {
            ItemGroup itemGroup = itemGroupMapper.selectById(dependentItemId);
            if (itemGroup == null) {
                log.warn("找不到依赖项目: {}", dependentItemId);
                return null;
            }

            CustomerRegItemGroup dependentItem = new CustomerRegItemGroup();
            BeanUtils.copyProperties(mainItem, dependentItem);
            dependentItem.setId(null);
            dependentItem.setItemGroupId(itemGroup.getId());
            dependentItem.setItemGroupName(itemGroup.getName());
            dependentItem.setHisCode(itemGroup.getHisCode());
            dependentItem.setHisName(itemGroup.getHisName());
            dependentItem.setPlatCode(itemGroup.getPlatCode());
            dependentItem.setPlatName(itemGroup.getPlatName());
            dependentItem.setDepartmentId(itemGroup.getDepartmentId());
            dependentItem.setDepartmentCode(itemGroup.getDepartmentCode());
            dependentItem.setDepartmentName(itemGroup.getDepartmentName());
            dependentItem.setClassCode(itemGroup.getClassCode());
            dependentItem.setMinDiscountRate(itemGroup.getMinDiscountRate());
            dependentItem.setPrice(itemGroup.getPrice());
            dependentItem.setPriceAfterDis(itemGroup.getPrice());
            dependentItem.setDisRate(BigDecimal.ZERO);
            dependentItem.setParentGroupId(mainItem.getItemGroupId());
            dependentItem.setAttachBaseId(null);
            dependentItem.setGiveAwayFlag("0");

            return dependentItem;
        } catch (Exception e) {
            log.error("创建依赖项目失败: {}", dependentItemId, e);
            return null;
        }
    }

    /**
     * 创建关系项目（附属或赠送）
     */
    private List<CustomerRegItemGroup> createRelationItems(CustomerRegItemGroup sourceItem, RelationItemVO relationGroup, String relationType) {
        List<CustomerRegItemGroup> relationItems = new ArrayList<>();

        try {
            ItemGroup itemGroup = itemGroupMapper.selectById(relationGroup.getRelationGroupId());
            if (itemGroup == null) {
                log.warn("找不到{}项目: {}", relationType, relationGroup.getRelationGroupId());
                return relationItems;
            }

            Integer quantity = relationGroup.getQuantity() != null ? relationGroup.getQuantity() : 1;

            for (int i = 0; i < quantity; i++) {
                CustomerRegItemGroup relationItem = new CustomerRegItemGroup();
                BeanUtils.copyProperties(sourceItem, relationItem);
                relationItem.setId(null);
                relationItem.setItemGroupId(itemGroup.getId());
                relationItem.setItemGroupName(itemGroup.getName());
                relationItem.setHisCode(itemGroup.getHisCode());
                relationItem.setHisName(itemGroup.getHisName());
                relationItem.setPlatCode(itemGroup.getPlatCode());
                relationItem.setPlatName(itemGroup.getPlatName());
                relationItem.setDepartmentId(itemGroup.getDepartmentId());
                relationItem.setDepartmentCode(itemGroup.getDepartmentCode());
                relationItem.setDepartmentName(itemGroup.getDepartmentName());
                relationItem.setClassCode(itemGroup.getClassCode());
                relationItem.setMinDiscountRate(itemGroup.getMinDiscountRate());
                relationItem.setPrice(itemGroup.getPrice());
                relationItem.setPriceAfterDis(itemGroup.getPrice());
                relationItem.setDisRate(BigDecimal.ZERO);

                if ("attach".equals(relationType)) {
                    relationItem.setAttachBaseId(sourceItem.getItemGroupId());
                    relationItem.setGiveAwayFlag("0");
                } else if ("gift".equals(relationType)) {
                    relationItem.setGiveAwayFlag("1");
                    relationItem.setAttachBaseId(null);
                    relationItem.setPrice(BigDecimal.ZERO);
                    relationItem.setPriceAfterDis(BigDecimal.ZERO);
                }

                relationItems.add(relationItem);
            }
        } catch (Exception e) {
            log.error("创建{}项目失败: {}", relationType, relationGroup.getRelationGroupId(), e);
        }

        return relationItems;
    }

    /**
     * 获取项目来源类型
     */
    private String getItemSourceType(CustomerRegItemGroup item, List<DependencyAnalysisVO> dependencyAnalyses) {
        DependencyAnalysisVO analysis = findAnalysisById(dependencyAnalyses, item.getItemGroupId());
        return analysis != null ? analysis.getSourceType() : "main";
    }
}