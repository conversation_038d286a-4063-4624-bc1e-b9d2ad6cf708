# ZyIndustry 叶子节点API文档

## 概述

本文档描述了ZyIndustry（行业类别）模块新增的叶子节点API接口，该接口专门用于前端下拉选择组件，提供高性能的数据查询和完备的Redis缓存机制。

## 新增API接口

### 1. 获取所有叶子节点

**接口地址：** `GET /occu/zyIndustry/leafNodes`

**接口描述：** 获取所有行业类别的叶子节点，支持关键词搜索，带Redis缓存

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| keyword | String | 否 | 搜索关键词，支持按名称、代码、助记码、父级路径搜索 |

**请求示例：**
```http
GET /occu/zyIndustry/leafNodes
GET /occu/zyIndustry/leafNodes?keyword=制造
```

**响应数据结构：**
```json
{
  "success": true,
  "message": "操作成功",
  "code": 200,
  "result": [
    {
      "id": "1234567890",
      "name": "汽车制造",
      "code": "C36",
      "helpChar": "QCZZ",
      "pid": "1234567889",
      "parentNames": ["制造业", "交通运输设备制造业"],
      "fullPath": "汽车制造 - 制造业 > 交通运输设备制造业",
      "parentPath": "制造业 > 交通运输设备制造业",
      "level": 3,
      "sortWeight": 300
    }
  ],
  "timestamp": 1703404800000
}
```

**响应字段说明：**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | String | 行业ID |
| name | String | 行业名称 |
| code | String | 行业代码 |
| helpChar | String | 助记码 |
| pid | String | 父级节点ID |
| parentNames | Array | 父级节点名称列表（从根节点到直接父节点） |
| fullPath | String | 完整路径（用于tooltip显示） |
| parentPath | String | 父级路径（用于副标题显示） |
| level | Integer | 层级深度 |
| sortWeight | Integer | 排序权重 |

### 2. 刷新叶子节点缓存

**接口地址：** `POST /occu/zyIndustry/refreshLeafNodesCache`

**接口描述：** 手动刷新叶子节点Redis缓存

**权限要求：** `occu:zy_industry:edit`

**请求示例：**
```http
POST /occu/zyIndustry/refreshLeafNodesCache
```

**响应示例：**
```json
{
  "success": true,
  "message": "缓存刷新成功！",
  "code": 200,
  "timestamp": 1703404800000
}
```

## 缓存机制

### 缓存策略
- **缓存名称：** `zyIndustryLeafNodes`
- **缓存键：** `all`
- **过期时间：** 24小时
- **序列化方式：** GenericJackson2JsonRedisSerializer
- **缓存注解：** 使用Spring Boot的`@Cacheable`、`@CacheEvict`注解

### 缓存触发场景
1. **首次查询：** 使用`@Cacheable`注解，缓存不存在时从数据库查询并自动缓存
2. **数据变更：** 使用`@CacheEvict`注解，增加、修改、删除行业数据时自动清除缓存
3. **手动刷新：** 调用刷新缓存API，使用`@CacheEvict`清除所有缓存
4. **缓存过期：** 24小时后自动过期，下次查询时重新缓存

### 性能优化
- **声明式缓存：** 使用Spring Boot缓存注解，代码更简洁，维护性更好
- **自动管理：** Spring自动处理缓存的存储、获取、清除操作
- **容错机制：** 缓存异常时自动降级到数据库查询
- **配置灵活：** 通过配置类可以灵活调整缓存策略

## 数据结构设计

### 叶子节点识别
- 通过 `hasChild` 字段判断是否为叶子节点
- `hasChild = "0"` 表示叶子节点
- `hasChild = "1"` 表示有子节点

### 父级路径构建
- 递归查询父级节点，构建完整的父级路径
- 支持多层级嵌套结构
- 按层级和名称排序

### 搜索功能
支持以下字段的模糊搜索：
- 行业名称 (name)
- 行业代码 (code)
- 助记码 (helpChar)
- 父级路径 (parentPath)
- 父级名称列表 (parentNames)

## 使用示例

### 前端调用示例

```javascript
// 获取所有叶子节点
const getAllLeafNodes = async () => {
  const response = await axios.get('/occu/zyIndustry/leafNodes');
  return response.data.result;
};

// 搜索叶子节点
const searchLeafNodes = async (keyword) => {
  const response = await axios.get('/occu/zyIndustry/leafNodes', {
    params: { keyword }
  });
  return response.data.result;
};

// 刷新缓存
const refreshCache = async () => {
  const response = await axios.post('/occu/zyIndustry/refreshLeafNodesCache');
  return response.data;
};
```

### 前端组件集成

```vue
<template>
  <ZyIndustrySelect
    v-model:value="selectedIndustry"
    placeholder="请选择行业类别"
    @change="handleChange"
  />
</template>

<script setup>
import ZyIndustrySelect from '/@/components/occu/ZyIndustrySelect.vue';

const selectedIndustry = ref();

const handleChange = (value, option) => {
  console.log('选择的行业:', value, option);
};
</script>
```

## 注意事项

1. **Redis依赖：** 确保Redis服务正常运行
2. **权限控制：** 刷新缓存接口需要相应权限
3. **数据一致性：** 数据变更时会自动刷新缓存，保证数据一致性
4. **性能监控：** 建议监控缓存命中率和查询性能
5. **容错处理：** 缓存异常时会降级到数据库查询

## 错误处理

### 常见错误码
- `500`：服务器内部错误
- `403`：权限不足（刷新缓存接口）
- `400`：请求参数错误

### 错误处理建议
- 缓存异常时自动降级到数据库查询
- 记录详细的错误日志便于排查
- 前端做好错误提示和重试机制
