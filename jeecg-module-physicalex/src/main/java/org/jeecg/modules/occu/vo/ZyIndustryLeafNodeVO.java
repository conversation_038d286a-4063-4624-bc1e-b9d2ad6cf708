package org.jeecg.modules.occu.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 行业类别叶子节点VO
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
@Data
@ApiModel(value = "ZyIndustryLeafNodeVO", description = "行业类别叶子节点VO")
public class ZyIndustryLeafNodeVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "代码")
    private String code;

    @ApiModelProperty(value = "助记码")
    private String helpChar;

    @ApiModelProperty(value = "父级节点ID")
    private String pid;

    @ApiModelProperty(value = "父级节点名称列表（从根节点到直接父节点）")
    private List<String> parentNames;

    @ApiModelProperty(value = "完整路径（用于显示）")
    private String fullPath;

    @ApiModelProperty(value = "父级路径（用于副标题显示）")
    private String parentPath;

    @ApiModelProperty(value = "层级深度")
    private Integer level;

    @ApiModelProperty(value = "排序权重（基于使用频次和层级）")
    private Integer sortWeight;
}
