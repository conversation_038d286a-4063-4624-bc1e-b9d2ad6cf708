package org.jeecg.modules.summary.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.summary.entity.AccountReport;
import org.jeecg.modules.summary.entity.CustomerRegSummary;

import java.util.List;

/**
 * @Description: 总检
 * @Author: jeecg-boot
 * @Date: 2024-05-20
 * @Version: V1.0
 */
public interface CustomerRegSummaryMapper extends BaseMapper<CustomerRegSummary> {

    Page<CustomerReg> pageCustomerReg(Page<CustomerReg> page, @Param("status") String status, @Param("examCategory") String examCatory, @Param("qualified") String qualified, @Param("name") String name, @Param("gender") String gender, @Param("idCard") String idCard, @Param("phone") String phone, @Param("regDateStart") String regDateStart, @Param("regDateEnd") String regDateEnd, @Param("preDateStart") String preDateStart, @Param("preDateEnd") String preDateEnd, @Param("chiefDateStart") String chiefDateStart, @Param("chiefDateEnd") String chiefDateEnd, @Param("auditDateStart") String auditDateStart, @Param("auditDateEnd") String auditDateEnd, @Param("printDateStart") String printDateStart, @Param("printDateEnd") String printDateEnd, @Param("examNo") String examNo, @Param("examCardNo") String examCardNo, @Param("checkState") String checkState, @Param("companyRegId") String companyRegId, @Param("teamId") String teamId, @Param("printStatus") String printStatus, @Param("initialDoctor") String initialDoctor, @Param("chiefDoctor") String chiefDoctor, @Param("auditeBy") String auditeBy, @Param("retrieveStatus") String retrieveStatus, @Param("preSummaryStatus") String preSummaryStatus, @Param("summaryStatus") String summaryStatus, @Param("preSummaryMethod") String preSummaryMethod, @Param("initailSummaryMethod") String initailSummaryMethod, @Param("sortOrder") String sortOrder, @Param("groupIds") List<String> groupIds, @Param("eReportStatus") String eReportStatus, @Param("paperReportStatus") String paperReportStatus,@Param("assignedSummaryDoctor") String assignedSummaryDoctor,@Param("daysFromReg") int daysFromReg,@Param("examCatoryOperator") String examCatoryOperator);

    String getStatusByReg(@Param("customerRegId") String customerRegId);

    CustomerRegSummary getByRegId(@Param("customerRegId") String customerRegId);

    CustomerRegSummary getSummaryById(@Param("id") String id);

    List<AccountReport> getAccountReportByIdcard(@Param("idCard") String idCard);

    CustomerRegSummary selectLatestSummary(@Param("customerRegId") String customerRegId);
}
