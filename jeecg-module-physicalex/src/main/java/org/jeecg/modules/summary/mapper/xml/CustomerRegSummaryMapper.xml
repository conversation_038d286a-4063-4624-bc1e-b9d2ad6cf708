<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.summary.mapper.CustomerRegSummaryMapper">

    <select id="pageCustomerReg" resultType="org.jeecg.modules.reg.entity.CustomerReg"
            parameterType="java.lang.String">
        select reg.*,DATEDIFF(CURDATE(), reg.reg_time) as daysFromReg
        from customer_reg reg
        <where>
            reg.del_flag = 0
            <if test="name!=null">and reg.name  like concat('%',#{name},'%')</if>
            <if test="gender!=null">and reg.gender = #{gender}</if>
            <if test="phone!=null">and reg.phone = #{phone}</if>
            <if test="idCard!=null">and reg.id_card = #{idCard}</if>
            <if test="regDateStart!=null">and reg.reg_time &gt;= #{regDateStart}</if>
            <if test="regDateEnd!=null">and reg.reg_time &lt;= #{regDateEnd}</if>
            <if test="examNo!=null">and reg.exam_no like concat('%',#{examNo},'%') </if>
            <if test="examCardNo!=null">and reg.exam_card_no = #{examCardNo}</if>
            <if test="companyRegId!=null">and reg.company_reg_id = #{companyRegId}</if>
            <if test="teamId!=null">and reg.team_id = #{teamId}</if>
            <if test="checkState!=null">and reg.check_state = #{checkState}</if>

            <if test="retrieveStatus!=null">and reg.retrieve_status = #{retrieveStatus}</if>
            <if test="preSummaryStatus!=null">and reg.pre_summary_status = #{preSummaryStatus}</if>
            <if test="status!=null">and reg.status = #{status}</if>
            <if test="summaryStatus!=null">and reg.summary_status = #{summaryStatus}</if>
            <if test="preDateStart!=null">and reg.pre_summary_time &gt;= #{preDateStart}</if>
            <if test="preDateEnd!=null">and reg.pre_summary_time &lt;= #{preDateEnd}</if>
            <if test="chiefDateStart!=null">and reg.summary_time &gt;= #{chiefDateStart}</if>
            <if test="chiefDateEnd!=null">and reg.summary_time &lt;= #{chiefDateEnd}</if>
            <if test="auditDateStart!=null">and reg.summary_audit_time &gt;= #{auditDateStart}</if>
            <if test="auditDateEnd!=null">and reg.summary_audit_time &lt;= #{auditDateEnd}</if>
            <if test="printDateStart!=null">and reg.report_print_time &gt;= #{printDateStart}</if>
            <if test="printDateEnd!=null">and reg.report_print_time &lt;= #{printDateEnd}</if>
            <if test="preSummaryMethod!=null"> and reg.pre_summary_method=#{preSummaryMethod}</if>
            <if test="initailSummaryMethod!=null"> and reg.initail_summary_method=#{initailSummaryMethod}</if>
            <if test="eReportStatus!=null"> and reg.e_report_status=#{eReportStatus}</if>
            <if test="paperReportStatus!=null"> and reg.paper_report_status=#{paperReportStatus}</if>
            <if test="assignedSummaryDoctor!=null">and reg.summary_doctor_assigned = #{assignedSummaryDoctor}</if>
            <if test="daysFromReg!=0">
                and DATEDIFF(CURDATE(), reg.reg_time) &gt;= #{daysFromReg}
            </if>
            <if test="examCatoryOperator!=null and examCatoryOperator=='EQ'">
                <if test="examCategory!=null and examCategory!=''">and reg.exam_category=#{examCategory}</if>
            </if>
            <if test="examCatoryOperator!=null and examCatoryOperator=='NE'">
                <if test="examCategory!=null and examCategory!=''">and reg.exam_category!=#{examCategory}</if>
            </if>
            <if test="initialDoctor!=null or chiefDoctor!=null or auditeBy!=null or qualified!=null or printStatus!=null">
                and exists (
                select g.id from customer_reg_summary g
                where reg.id = g.customer_reg_id
                <if test="initialDoctor!=null">and g.pre_audit_by = #{initialDoctor}</if>
                <if test="chiefDoctor!=null">and g.create_by = #{chiefDoctor}</if>
                <if test="auditeBy!=null">and g.audite_by = #{auditeBy}</if>
                <if test="qualified!=null">and g.qualified_flag =#{qualified} </if>

                <if test="printStatus!=null">and g.report_print_status = #{printStatus}</if>
                <!--<if test="preDateStart!=null">and g.pre_audit_Time &gt;= #{preDateStart}</if>
                <if test="preDateEnd!=null">and g.pre_audit_Time &lt;= #{preDateEnd}</if>
                <if test="chiefDateStart!=null">and g.create_time &gt;= #{chiefDateStart}</if>
                <if test="chiefDateEnd!=null">and g.create_time &lt;= #{chiefDateEnd}</if>
                <if test="auditDateStart!=null">and g.confirm_time &gt;= #{auditDateStart}</if>
                <if test="auditDateEnd!=null">and g.confirm_time &lt;= #{auditDateEnd}</if>-->
                )
            </if>
            <if test="groupIds!=null and groupIds.size()>0">
                and exists (
                select g.id from customer_reg_item_group g
                where reg.id = g.customer_reg_id
                and (g.pay_status='已支付' or payer_type='单位支付') and g.add_minus_flag!=-1 and g.item_group_id in
                <foreach collection="groupIds" index="index" item="groupId" open="(" separator="," close=")">
                    #{groupId}
                </foreach>
                )

            </if>
        </where>
        <choose>
            <when test="daysFromReg!=0">
                    order by DATEDIFF(CURDATE(), reg.reg_time) desc
            </when>
            <otherwise>
                <choose>
                <when test="sortOrder!=null">
                    order by ${sortOrder}
                </when>
                <otherwise>
                    order by reg.reg_time desc
                </otherwise>
                </choose>
            </otherwise>
        </choose>
    </select>

    <select id="getStatusByReg" resultType="java.lang.String">
        select status from customer_reg_summary where customer_reg_id = #{customerRegId}
    </select>

    <select id="getByRegId" resultMap="CustomerRegSummaryResultMap">
        select * from customer_reg_summary where customer_reg_id = #{customerRegId} limit 1
    </select>

    <select id="getSummaryById" resultMap="CustomerRegSummaryResultMap">
        select * from customer_reg_summary where id = #{id}
    </select>

    <select id="getAccountReportByIdcard" resultType="org.jeecg.modules.summary.entity.AccountReport">
        select c.id as customer_reg_id,crg.id as summary_id from customer_reg c join customer_reg_summary crg on c.id = crg.customer_reg_id where c.id_card = #{idCard}  and (c.e_report_status='待发送' or c.e_report_status='已发送')
    </select>
    <select id="selectLatestSummary" resultMap="CustomerRegSummaryResultMap">
        select * from customer_reg_summary where customer_reg_id = #{customerRegId}  limit 1
    </select>


    <resultMap id="CustomerRegSummaryResultMap" type="org.jeecg.modules.summary.entity.CustomerRegSummary">
        <id property="id" column="id"/>
        <result property="customerRegId" column="customer_reg_id"/>
        <result property="characterSummary" column="character_summary"/>
        <result property="summaryJson" column="summary_json" javaType="java.util.List"  typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result property="diagnosisSummary" column="diagnosis_summary"/>
        <result property="advice" column="advice"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="creatorName" column="creator_name"/>
        <result property="confirmTime" column="confirm_time"/>
        <result property="auditeBy" column="audite_by"/>
        <result property="auditorName" column="auditor_name"/>
        <result property="preAuditor" column="pre_auditor"/>
        <result property="preAuditBy" column="pre_audit_by"/>
        <result property="preAuditTime" column="pre_audit_time"/>
        <result property="hisDoctorId" column="his_doctor_id"/>
        <result property="status" column="status"/>
    </resultMap>


</mapper>