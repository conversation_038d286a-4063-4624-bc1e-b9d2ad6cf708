package org.jeecg.modules.summary.service.impl;


import org.jeecg.modules.ai.model.ApplicationResult;
import org.jeecg.modules.ai.model.Message;
import org.jeecg.modules.ai.service.StreamResponseCallback;
import org.jeecg.modules.ai.config.DashScopeConfig;
import org.jeecg.excommons.utils.ProxyOkHttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.github.difflib.DiffUtils;
import com.github.difflib.patch.Patch;
import io.reactivex.Flowable;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.util.ThreadContext;
import org.jeecg.modules.common.util.ShiroAsyncUtils;
import org.jeecg.common.api.CommonAPI;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.excommons.BatchResult;
import org.jeecg.excommons.ExConstants;
import org.jeecg.modules.ai.bo.AIMessage;
import org.jeecg.modules.ai.entity.AiMod;
import org.jeecg.modules.ai.entity.AiSetting;
import org.jeecg.modules.ai.mapper.AiModMapper;
import org.jeecg.modules.ai.mapper.AiSettingMapper;
import org.jeecg.modules.ai.service.AIService;
import org.jeecg.modules.ai.service.DashScopeService;
import org.jeecg.modules.ai.service.DashscopeStreamResponseCallback;
import org.jeecg.modules.basicinfo.entity.AutoPreSummarySetting;
import org.jeecg.modules.basicinfo.entity.ItemGroup;
import org.jeecg.modules.basicinfo.mapper.ItemGroupMapper;
import org.jeecg.modules.basicinfo.service.IAutoPreSummarySettingService;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecg.modules.occu.entity.ZyRiskFactor;
import org.jeecg.modules.occu.mapper.ZyRiskFactorMapper;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.reg.mapper.CustomerRegItemGroupMapper;
import org.jeecg.modules.reg.mapper.CustomerRegMapper;
import org.jeecg.modules.station.dto.GroupSummaryBean;
import org.jeecg.modules.station.dto.AbnormalSummary;
import org.jeecg.modules.station.entity.CustomerRegDepartSummary;
import org.jeecg.modules.station.service.ICustomerRegDepartSummaryService;
import org.jeecg.modules.summary.bo.AbnormalSummaryAndAdvice;
import org.jeecg.modules.summary.entity.*;
import org.jeecg.modules.summary.mapper.CustomerRegSummaryExtraMapper;
import org.jeecg.modules.summary.mapper.CustomerRegSummaryHistoryMapper;
import org.jeecg.modules.summary.mapper.CustomerRegSummaryMapper;
import org.jeecg.modules.summary.mapper.SummaryAdviceMapper;
import org.jeecg.modules.summary.service.ICustomerRegSummaryService;
import org.jeecg.modules.summary.service.ISummaryAdviceService;
import org.jeecg.modules.summary.service.StreamingDiagnoseJsonParser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * @Description: 总检
 * @Author: jeecg-boot
 * @Date: 2024-05-20
 * @Version: V1.0
 */
@Service
@Slf4j
public class CustomerRegSummaryServiceImpl extends ServiceImpl<CustomerRegSummaryMapper, CustomerRegSummary> implements ICustomerRegSummaryService {

    @Resource
    private CommonAPI commonAPI;
    @Resource
    private CustomerRegSummaryMapper customerRegSummaryMapper;
    @Resource
    private ZyRiskFactorMapper zyRiskFactorMapper;
    @Resource
    private JdbcTemplate jdbcTemplate;
    @Resource
    private CustomerRegMapper customerRegMapper;
    @Resource
    private CustomerRegSummaryHistoryMapper customerRegSummaryHistoryMapper;
    @Resource
    private ItemGroupMapper itemGroupMapper;
    @Resource
    private IAutoPreSummarySettingService autoPreSummarySettingService;
    @Resource
    private CustomerRegItemGroupMapper customerRegItemGroupMapper;
    @Resource
    private ISysSettingService sysSettingService;
    @Resource
    private ICustomerRegDepartSummaryService customerRegDepartSummaryService;
    @Resource
    private ISummaryAdviceService summaryAdviceService;
    @Resource
    private AIService aiService;
    @Resource
    private DashScopeService dashScopeService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private AiModMapper aiModMapper;
    @Resource
    private AiSettingMapper aiSettingMapper;
    @Resource
    private CustomerRegSummaryExtraMapper customerRegSummaryExtraMapper;
    @Resource
    private SummaryAdviceMapper summaryAdviceMapper;
    @Resource
    private ProxyOkHttpUtil proxyOkHttpUtil;
    @Resource
    private DashScopeConfig dashScopeConfig;

    @Override
    public void pageCustomerReg(Page<CustomerReg> page, String examCatory, String qualified, String name, String gender, String idCard, String phone, String dateType, String dateStart, String dateEnd, String examNo, String examCardNo, String checkState, String companyRegId, String teamId, String printStatus, String doctorType, String doctor, String filterStatus, String status, String summaryStatus, String preSummaryMethod, String initailSummaryMethod, String eReportStatus, String paperReportStatus, String sortOrder, String daysFromReg,String examCatoryOperator) {
        String initialDoctor = null;
        String chiefDoctor = null;
        String auditeBy = null;
        String assignedSummaryDoctor = null;
        String retrieveStatus = null;
        String preSummaryStatus = null;
        if (StringUtils.equals(doctorType, "初检")) {
            initialDoctor = doctor;
        } else if (StringUtils.equals(doctorType, "主检")) {
            chiefDoctor = doctor;
        } else if (StringUtils.equals(doctorType, "审核")) {
            auditeBy = doctor;
        } else if (StringUtils.equals(doctorType, "指定的主检")) {
            assignedSummaryDoctor = doctor;
        }

        if (StringUtils.equals(filterStatus, "已交表")) {
            retrieveStatus = "1";
        } else if (StringUtils.equals(filterStatus, "已预检")) {
            preSummaryStatus = "1";
        }
        if (StringUtils.equals(sortOrder, "主检升序")) {
            sortOrder = "reg.summary_time asc";
        } else if (StringUtils.equals(sortOrder, "主检降序")) {
            sortOrder = "reg.summary_time desc";
        } else if (StringUtils.equals(sortOrder, "初检升序")) {
            sortOrder = "reg.initail_summary_time asc";
        } else if (StringUtils.equals(sortOrder, "初检降序")) {
            sortOrder = "reg.initail_summary_time desc";
        } else if (StringUtils.equals(sortOrder, "审核升序")) {
            sortOrder = "reg.summary_audit_time asc";
        } else if (StringUtils.equals(sortOrder, "审核降序")) {
            sortOrder = "reg.summary_audit_time desc";
        } else if (StringUtils.equals(sortOrder, "打印升序")) {
            sortOrder = "reg.report_print_time asc";
        } else if (StringUtils.equals(sortOrder, "打印降序")) {
            sortOrder = "reg.report_print_time desc";
        } else if (StringUtils.equals(sortOrder, "登记降序")) {
            sortOrder = "reg.reg_time desc";
        } else if (StringUtils.equals(sortOrder, "登记升序")) {
            sortOrder = "reg.reg_time asc";
        } else if (StringUtils.equals(sortOrder, "总检降序")) {
            sortOrder = "reg.summary_time desc";
        } else if (StringUtils.equals(sortOrder, "总检升序")) {
            sortOrder = "reg.summary_time desc";
        }

        sortOrder = StringUtils.equals("升序", sortOrder) ? "asc" : "desc";
        String orderSql = null;
        String regDateStart = null;
        String regDateEnd = null;
        String preDateStart = null;
        String preDateEnd = null;
        String chiefDateStart = null;
        String chiefDateEnd = null;
        String auditDateStart = null;
        String auditDateEnd = null;
        String printDateStart = null;
        String printDateEnd = null;
        if (StringUtils.equals(dateType, "初检日期")) {
            preDateStart = dateStart;
            preDateEnd = dateEnd;
            orderSql = "reg.pre_summary_time";
        } else if (StringUtils.equals(dateType, "主检日期")) {
            chiefDateStart = dateStart;
            chiefDateEnd = dateEnd;
            orderSql = "reg.summary_time";
        } else if (StringUtils.equals(dateType, "审核日期")) {
            auditDateStart = dateStart;
            auditDateEnd = dateEnd;
            orderSql = "reg.summary_audit_time";
        } else if (StringUtils.equals(dateType, "登记日期")) {
            regDateStart = dateStart;
            regDateEnd = dateEnd;
            orderSql = "reg.reg_time";
        } else if (StringUtils.equals(dateType, "打印日期")) {
            printDateStart = dateStart;
            printDateEnd = dateEnd;
            orderSql = "reg.report_print_time";
        }

        int days = 0;
        if (StringUtils.isNotBlank(daysFromReg)) {
            try {
                days = Integer.parseInt(daysFromReg);
            } catch (Exception e) {
                days = 0;
            }
        }

        orderSql = StringUtils.isNotBlank(orderSql) ? orderSql + " " + sortOrder : null;
        List<ItemGroup> summaryGroups = itemGroupMapper.selectList(new LambdaQueryWrapper<ItemGroup>().eq(ItemGroup::getSummaryFlag, 1));
        List<String> groupIds = summaryGroups.stream().map(ItemGroup::getId).collect(Collectors.toList());
        customerRegSummaryMapper.pageCustomerReg(page, status, examCatory, qualified, name, gender, idCard, phone, regDateStart, regDateEnd, preDateStart, preDateEnd, chiefDateStart, chiefDateEnd, auditDateStart, auditDateEnd, printDateStart, printDateEnd, examNo, examCardNo, checkState, companyRegId, teamId, printStatus, initialDoctor, chiefDoctor, auditeBy, retrieveStatus, preSummaryStatus, summaryStatus, preSummaryMethod, initailSummaryMethod, orderSql, groupIds, eReportStatus, paperReportStatus, assignedSummaryDoctor, days,examCatoryOperator);

        if (page.getRecords().isEmpty()) {
            return;
        }
        List<DictModel> dictModelList = commonAPI.queryDictItemsByCode("summary_status");
        page.getRecords().forEach(customerReg -> {

            if (StringUtils.isNotBlank(customerReg.getRiskFactor())) {
                List<String> riskFactorIds = Arrays.asList(customerReg.getRiskFactor().split(","));
                List<ZyRiskFactor> zyRiskFactors = zyRiskFactorMapper.selectByIdsOrCodes(riskFactorIds);
                customerReg.setRiskFactorList(zyRiskFactors);
            }

            String summaryStatusValue = customerReg.getSummaryStatus();
            if (StringUtils.isNotBlank(summaryStatusValue)) {
                Optional<DictModel> optionalDictModel = dictModelList.stream().filter(dictModel -> summaryStatusValue.equals(dictModel.getValue())).findFirst();

                if (optionalDictModel.isPresent()) {
                    String color = optionalDictModel.get().getColor();
                    customerReg.setSummaryStatusColor(color);
                }
            }
        });
    }


    @Override
    public String getStatusByReg(String customerRegId) {
        return null;
    }

    @Override
    public CustomerRegSummary getByRegId(String customerRegId) {
        return customerRegSummaryMapper.getByRegId(customerRegId);
    }

    @Override
    public CustomerRegSummary getOrGenerateByRedId(String customerRegId) {
        CustomerRegSummary summary = customerRegSummaryMapper.getByRegId(customerRegId);
        if (summary == null) {
            summary = new CustomerRegSummary();
            summary.setCustomerRegId(customerRegId);
            summary.setExamNo(customerRegId);
            summary.setStatus(ExConstants.SUMMARY_STATUS_未总检);
            summary.setCreateTime(new Date());
            save(summary);
        } else {
            QueryWrapper<CustomerRegSummaryExtra> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("summary_id", summary.getId());
            queryWrapper.last("limit 1");
            CustomerRegSummaryExtra extra = customerRegSummaryExtraMapper.selectOne(queryWrapper);
            if (extra != null) {
                JSONArray abnormalStructContent = JSONArray.parseArray(extra.getAbnormalStructContent());
                JSONArray diagnoseStructContent = JSONArray.parseArray(extra.getDiagnoseStructContent());
                JSONArray summaryStructContent = JSONArray.parseArray(extra.getSummaryStructContent());
                summary.setAbnormalSummaryData(abnormalStructContent);
                summary.setDiagnosisList(diagnoseStructContent);
                summary.setAdviceStructuredData(summaryStructContent);
            }
        }

        if (summary.getAbnormalSummaryData() == null) {
            List<AbnormalSummary> abnormalSummaryList = customerRegDepartSummaryService.generateAbnormalSummaryBeanList(customerRegId);
            summary.setAbnormalSummaryData(JSONArray.parseArray(JSONObject.toJSONString(abnormalSummaryList)));
        }
        return summary;
    }

    @Override
    public CustomerRegSummary getSummaryById(String id) {

        return customerRegSummaryMapper.getSummaryById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CustomerRegSummary saveOrUpdateCustomerSummary(CustomerRegSummary customerRegSummary) throws Exception {
        if (customerRegSummary == null) {
            return null;
        }
        if (StringUtils.isBlank(customerRegSummary.getId())) {
            String customerRegId = customerRegSummary.getCustomerRegId();
            String existingSummaryId = null;
            try {
                existingSummaryId = jdbcTemplate.queryForObject("SELECT id FROM customer_reg_summary WHERE customer_reg_id = ? LIMIT 1", String.class, customerRegId);
            } catch (Exception e) {
            }
            if (existingSummaryId != null && !existingSummaryId.equals(customerRegSummary.getId())) {
                throw new Exception("体检号：" + customerRegSummary.getExamNo() + "已经存在总检记录，不能新增！");
            }
        }

        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String id = customerRegSummary.getId();
        List<AdviceBean> summaryJson = customerRegSummary.getSummaryJson();
        if (summaryJson != null && summaryJson.isEmpty()) {
            customerRegSummary.setSummaryJson(null);
        }
        String type = customerRegSummary.getType();
        String summaryStatus = StringUtils.equals(type, "pre") ? ExConstants.SUMMARY_STATUS_已初检 : ExConstants.SUMMARY_STATUS_已总检;
        customerRegSummary.setStatus(summaryStatus);

        if (StringUtils.isNotBlank(customerRegSummary.getId())) {
            CustomerRegSummary originalCustomerRegSummary = getSummaryById(id);

            if (StringUtils.equals(type, "pre") && StringUtils.isBlank(originalCustomerRegSummary.getPreAuditBy())) {
                customerRegSummary.setPreAuditBy(sysUser.getUsername());
                customerRegSummary.setPreAuditor(sysUser.getRealname());
                customerRegSummary.setPreAuditTime(new Date());
                customerRegSummary.setInitailTime(new Date());
            } else if (StringUtils.equals(type, "normal") && StringUtils.isBlank(originalCustomerRegSummary.getCreatorName())) {
                customerRegSummary.setCreateBy(sysUser.getUsername());
                customerRegSummary.setCreatorName(sysUser.getRealname());
                customerRegSummary.setCreateTime(new Date());
                customerRegSummary.setChiefTime(new Date());
            }

            customerRegSummary.setUpdateBy(sysUser.getUsername());
            customerRegSummary.setUpdator(sysUser.getRealname());
            customerRegSummary.setUpdateTime(new Date());
            updateById(customerRegSummary);
            CustomerRegSummaryHistory customerRegSummaryHistory = new CustomerRegSummaryHistory();
            customerRegSummaryHistory.setCustomerRegId(customerRegSummary.getCustomerRegId());
            customerRegSummaryHistory.setSummaryId(customerRegSummary.getId());

            if (originalCustomerRegSummary != null) {

                customerRegSummaryHistory.setSummaryOrigin(originalCustomerRegSummary.getCharacterSummary());
                customerRegSummaryHistory.setSummaryAfter(customerRegSummary.getCharacterSummary());
                customerRegSummaryHistory.setAdviceOrigin(JSONArray.toJSONString(originalCustomerRegSummary.getSummaryJson()));
                customerRegSummaryHistory.setAdviceAfter(JSONArray.toJSONString(customerRegSummary.getSummaryJson()));
            }

            customerRegSummaryHistory.setType("修改");
            customerRegSummaryHistory.setSummaryType(StringUtils.equals(type, "pre") ? "初检" : "总检");
            customerRegSummaryHistory.setCreator(sysUser.getRealname());
            customerRegSummaryHistoryMapper.insert(customerRegSummaryHistory);
        } else {
            if (StringUtils.equals(type, "pre")) {
                customerRegSummary.setPreAuditBy(sysUser.getUsername());
                customerRegSummary.setPreAuditor(sysUser.getRealname());
                customerRegSummary.setPreAuditTime(new Date());

                customerRegSummary.setInitailDoctor(sysUser.getRealname());
                customerRegSummary.setInitailDoctorUsername(sysUser.getUsername());
                customerRegSummary.setInitailTime(new Date());
            } else if (StringUtils.equals(type, "normal")) {
                customerRegSummary.setCreateBy(sysUser.getUsername());
                customerRegSummary.setCreatorName(sysUser.getRealname());
                customerRegSummary.setCreateTime(new Date());

                customerRegSummary.setChiefDoctor(sysUser.getRealname());
                customerRegSummary.setChiefDoctorUsername(sysUser.getUsername());
                customerRegSummary.setChiefTime(new Date());
            }
            save(customerRegSummary);
            CustomerRegSummaryHistory customerRegSummaryHistory = new CustomerRegSummaryHistory();
            customerRegSummaryHistory.setCustomerRegId(customerRegSummary.getCustomerRegId());
            customerRegSummaryHistory.setSummaryId(customerRegSummary.getId());
            customerRegSummaryHistory.setType("新增");
            customerRegSummaryHistory.setSummaryType(StringUtils.equals(type, "pre") ? "初检" : "总检");
            customerRegSummaryHistory.setCreateBy(sysUser.getUsername());
            customerRegSummaryHistory.setCreator(sysUser.getRealname());
            customerRegSummaryHistoryMapper.insert(customerRegSummaryHistory);
        }

        UpdateWrapper<CustomerReg> customerRegUpdateWrapper = new UpdateWrapper<>();
        customerRegUpdateWrapper.set("summary_status", summaryStatus);
        if (StringUtils.equals(type, "pre")) {
            customerRegUpdateWrapper.set("initail_summary_time", new Date());
            customerRegUpdateWrapper.set("initail_summary_doctor", sysUser.getRealname());
            customerRegUpdateWrapper.set("initail_summary_by", sysUser.getUsername());
            customerRegUpdateWrapper.set("initail_summary_method", ExConstants.INITAIL_SUMMARY_TYPE_手动);
        } else {
            customerRegUpdateWrapper.set("summary_time", new Date());
        }
        customerRegUpdateWrapper.set("report_edit_lock_flag", "0");
        customerRegUpdateWrapper.eq("id", customerRegSummary.getCustomerRegId());
        customerRegMapper.update(null, customerRegUpdateWrapper);

        //先查询，后保存
        CustomerRegSummaryExtra extra = customerRegSummaryExtraMapper.selectOne(new LambdaQueryWrapper<CustomerRegSummaryExtra>().eq(CustomerRegSummaryExtra::getSummaryId, customerRegSummary.getId()).last("limit 1"));
        if (extra == null) {
            extra = new CustomerRegSummaryExtra();
            extra.setSummaryId(customerRegSummary.getId());
            extra.setCustomerRegId(customerRegSummary.getCustomerRegId());
            extra.setCreateBy(sysUser.getUsername());
            extra.setCreateTime(new Date());
            extra.setUpdateBy(sysUser.getUsername());
            extra.setUpdateTime(new Date());
            extra.setAbnormalStructContent(JSONArray.toJSONString(customerRegSummary.getAbnormalSummaryData()));
            extra.setDiagnoseStructContent(JSONArray.toJSONString(customerRegSummary.getDiagnosisList()));
            extra.setSummaryStructContent(JSONArray.toJSONString(customerRegSummary.getAdviceStructuredData()));
            customerRegSummaryExtraMapper.insert(extra);
        } else {
            extra.setUpdateBy(sysUser.getUsername());
            extra.setUpdateTime(new Date());
            extra.setAbnormalStructContent(JSONArray.toJSONString(customerRegSummary.getAbnormalSummaryData()));
            extra.setDiagnoseStructContent(JSONArray.toJSONString(customerRegSummary.getDiagnosisList()));
            extra.setSummaryStructContent(JSONArray.toJSONString(customerRegSummary.getAdviceStructuredData()));
            customerRegSummaryExtraMapper.updateById(extra);
        }

        saveSummaryAdvice(summaryJson);

        return customerRegSummary;
    }

    @Async
    protected void saveSummaryAdvice(List<AdviceBean> adviceBeans) {
        //遍历adviceBeans，将不包含具体数字的建议保存到summary_advice表中，仅在keywords不存在时保存
        if (adviceBeans == null || adviceBeans.isEmpty()) {
            return;
        }
        for (AdviceBean adviceBean : adviceBeans) {
            String keywords = adviceBean.getName();
            String content = adviceBean.getContent();
            if (StringUtils.isBlank(keywords) || StringUtils.isBlank(content)) {
                continue;
            }
            if (keywords.contains(":") || keywords.contains("：")) {
                continue;
            }
            //过滤包含具体数字的建议
            if (keywords.matches(".*\\d+.*")) {
                continue;
            }
            List<String> existIds = jdbcTemplate.queryForList("SELECT id FROM summary_advice WHERE keywords = ?", String.class, keywords);
            if (!existIds.isEmpty()) {
                continue;
            }
            SummaryAdvice summaryAdvice = new SummaryAdvice();
            summaryAdvice.setKeywords(keywords);
            summaryAdvice.setAdviceContent("");
            summaryAdvice.setIndexTime(new Date());
            summaryAdvice.setCreateType("总检建议");
            summaryAdviceMapper.insert(summaryAdvice);
        }
    }

    @Override
    public CustomerReg getRegById(String regId) {

        CustomerReg customerReg = customerRegMapper.selectById(regId);
        if (customerReg == null) {
            return null;
        }
        if (StringUtils.isNotBlank(customerReg.getRiskFactor())) {
            List<String> riskFactorIds = Arrays.asList(customerReg.getRiskFactor().split(","));
            List<ZyRiskFactor> zyRiskFactors = zyRiskFactorMapper.selectByIdsOrCodes(riskFactorIds);
            customerReg.setRiskFactorList(zyRiskFactors);
        }

        List<DictModel> dictModelList = commonAPI.queryDictItemsByCode("summary_status");
        String summaryStatus = customerReg.getSummaryStatus();
        if (StringUtils.isNotBlank(summaryStatus)) {
            Optional<DictModel> optionalDictModel = dictModelList.stream().filter(dictModel -> summaryStatus.equals(dictModel.getValue())).findFirst();

            if (optionalDictModel.isPresent()) {
                String color = optionalDictModel.get().getColor();
                customerReg.setSummaryStatusColor(color);
            }
        }

        return customerReg;
    }

    @Override
    public void saveHealthCardResult(String customerRegId, String summaryId, String healthCardResult) {
        CustomerRegSummary customerRegSummary = null;
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (StringUtils.isNotBlank(summaryId)) {
            customerRegSummary = getById(summaryId);
            if (customerRegSummary != null) {
                customerRegSummary.setHealthCardResult(healthCardResult);
                customerRegSummary.setAuditeBy(loginUser.getUsername());
                customerRegSummary.setAuditorName(loginUser.getRealname());
                customerRegSummary.setStatus(ExConstants.SUMMARY_STATUS_审核通过);
                updateById(customerRegSummary);
            } else {
                customerRegSummary = new CustomerRegSummary();
                customerRegSummary.setCustomerRegId(customerRegId);
                customerRegSummary.setHealthCardResult(healthCardResult);
                customerRegSummary.setCreateTime(new Date());
                customerRegSummary.setCreateBy(loginUser.getUsername());
                customerRegSummary.setAuditeBy(loginUser.getUsername());
                customerRegSummary.setAuditorName(loginUser.getRealname());
                customerRegSummary.setStatus(ExConstants.SUMMARY_STATUS_审核通过);
                save(customerRegSummary);
            }
        }
    }

    @Override
    public void updateReportPrintTimes(String summaryId) {
        jdbcTemplate.update("update customer_reg_summary set report_print_times = report_print_times + 1,report_print_time=?,report_print_status=? where id = ?", new Date(), ExConstants.PRINT_STATUS_已打印, summaryId);
        jdbcTemplate.update("update customer_reg set report_print_time = ? where id in (select customer_reg_id from customer_reg_summary where id = ?)", new Date(), summaryId);
    }

    @Override
    public boolean isSummaryAudited(String customerRegId) {
        String summaryStatus = null;
        try {
            summaryStatus = jdbcTemplate.queryForObject("select status from customer_reg_summary where customer_reg_id=? limit 1", String.class, customerRegId);
        } catch (Exception ignored) {
        }

        return StringUtils.equals(summaryStatus, ExConstants.SUMMARY_STATUS_审核通过);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BatchResult<CustomerReg> preSummaryByIds(List<String> ids, String preSummaryType) {
        BatchResult<CustomerReg> batchResult = new BatchResult<>();
        if (null == ids || ids.isEmpty()) {
            return batchResult;
        }
        List<CustomerReg> customerRegList = customerRegMapper.selectBatchIds(ids);

        List<BatchResult.FailureResult<CustomerReg>> failureResults = new ArrayList<>();
        List<CustomerReg> successResults = new ArrayList<>();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        for (CustomerReg customerReg : customerRegList) {
            //如果不是登记状态，不能预检
            if (!StringUtils.equals(customerReg.getStatus(), ExConstants.REG_STATUS_REGED)) {
                BatchResult.FailureResult<CustomerReg> failureResult = new BatchResult.FailureResult<>(customerReg, "体检号：" + customerReg.getExamNo() + "登记状态：" + customerReg.getStatus() + "，不能预检！");
                failureResults.add(failureResult);
                continue;
            }
            if (!StringUtils.equals(customerReg.getSummaryStatus(), ExConstants.SUMMARY_STATUS_未总检)) {
                BatchResult.FailureResult<CustomerReg> failureResult = new BatchResult.FailureResult<>(customerReg, "体检号：" + customerReg.getExamNo() + "总检状态：" + customerReg.getSummaryStatus() + "，不能预检！");
                failureResults.add(failureResult);
                continue;
            }
            customerReg.setSummaryStatus(ExConstants.SUMMARY_STATUS_已预检);
            customerReg.setPreSummaryTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            if (StringUtils.equals(preSummaryType, ExConstants.PRE_SUMMARY_TYPE_手动)) {
                customerReg.setPreSummaryBy(sysUser.getUsername());
                customerReg.setPreSummaryMethod(ExConstants.PRE_SUMMARY_TYPE_手动);
            } else {
                customerReg.setPreSummaryBy("系统");
                customerReg.setPreSummaryMethod(ExConstants.PRE_SUMMARY_TYPE_自动);
            }
            successResults.add(customerReg);
        }
        if (!successResults.isEmpty()) {
            //使用jdbctemplate批量更新
            jdbcTemplate.batchUpdate("update customer_reg set summary_status=?,pre_summary_time=?,pre_summary_by=?,pre_summary_method=? where id = ?", successResults, 1000, (ps, argument) -> {
                ps.setString(1, argument.getSummaryStatus());
                ps.setString(2, argument.getPreSummaryTime());
                ps.setString(3, argument.getPreSummaryBy());
                ps.setString(4, argument.getPreSummaryMethod());
                ps.setString(5, argument.getId());
            });
        }
        batchResult.setFailureResults(failureResults);
        batchResult.setSuccessResults(successResults);

        return batchResult;
    }

    @Override
    public BatchResult<CustomerReg> unPreSummaryByIds(List<String> ids) {
        BatchResult<CustomerReg> batchResult = new BatchResult<>();
        if (null == ids || ids.isEmpty()) {
            return batchResult;
        }
        List<CustomerReg> customerRegList = customerRegMapper.selectBatchIds(ids);

        List<BatchResult.FailureResult<CustomerReg>> failureResults = new ArrayList<>();
        List<CustomerReg> successResults = new ArrayList<>();
        for (CustomerReg customerReg : customerRegList) {
            if (!StringUtils.equals(customerReg.getSummaryStatus(), ExConstants.SUMMARY_STATUS_已预检)) {
                BatchResult.FailureResult<CustomerReg> failureResult = new BatchResult.FailureResult<>(customerReg, "体检号：" + customerReg.getExamNo() + "总检状态：" + customerReg.getSummaryStatus() + "，不能取消预检！");
                failureResults.add(failureResult);
                continue;
            }
            customerReg.setSummaryStatus(ExConstants.SUMMARY_STATUS_未总检);
            customerReg.setPreSummaryTime(null);
            customerReg.setPreSummaryBy(null);
            customerReg.setPreSummaryMethod(null);
            successResults.add(customerReg);
        }
        if (!successResults.isEmpty()) {
            //使用jdbctemplate批量更新
            jdbcTemplate.batchUpdate("update customer_reg set summary_status=?,pre_summary_time=?,pre_summary_by=?,pre_summary_method=? where id = ?", successResults, 1000, (ps, argument) -> {
                ps.setString(1, argument.getSummaryStatus());
                ps.setString(2, argument.getPreSummaryTime());
                ps.setString(3, argument.getPreSummaryBy());
                ps.setString(4, argument.getPreSummaryMethod());
                ps.setString(5, argument.getId());
            });
        }
        batchResult.setFailureResults(failureResults);
        batchResult.setSuccessResults(successResults);

        return batchResult;
    }

    @Override
    public BatchResult<CustomerReg> preSummaryByCondition(QueryWrapper<CustomerReg> queryWrapper, String preSummaryType) {
        return null;
    }

    @Override
    public BatchResult<CustomerReg> unPreSummaryByCondition(QueryWrapper<CustomerReg> queryWrapper) {
        return null;
    }

    @Override
    public void doAutoPreSummary() {

        // The existing implementation of doAutoPreSummary
        AutoPreSummarySetting setting = autoPreSummarySettingService.getSetting();
        if (setting == null) {
            return;
        }
        String enable = setting.getEnableFlag();
        if (!StringUtils.equals(enable, "1")) {
            return;
        }
        String allowExamCategory = setting.getAllowExamCategory();
        List<String> allowNoCheckDepartIdList = StringUtils.isNotBlank(setting.getAllowNocheckDepart()) ? Arrays.asList(setting.getAllowNocheckDepart().split(",")) : new ArrayList<>();
        List<String> allowNoCheckItemGroupIdList = StringUtils.isNotBlank(setting.getAllowNocheckItemgroup()) ? Arrays.asList(setting.getAllowNocheckItemgroup().split(",")) : new ArrayList<>();

        LambdaQueryWrapper<CustomerReg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerReg::getSummaryStatus, ExConstants.SUMMARY_STATUS_未总检);
        queryWrapper.eq(CustomerReg::getStatus, ExConstants.REG_STATUS_REGED);
        queryWrapper.last("limit 1000");
        List<CustomerReg> records = customerRegMapper.selectList(queryWrapper);

        List<CustomerReg> preAbleCustomerRegList = new ArrayList<>();
        for (CustomerReg customerReg : records) {
            if (StringUtils.isBlank(allowExamCategory) || !StringUtils.contains(allowExamCategory, customerReg.getExamCategory())) {
                continue;
            }

            List<CustomerRegItemGroup> allItemGroupList = customerRegItemGroupMapper.listLiteByRegId(customerReg.getId());
            //过滤掉只收费项目和未支付项目
            allItemGroupList = allItemGroupList.stream().filter(customerRegItemGroup -> !StringUtils.equals(customerRegItemGroup.getChargeItemOnlyFlag(), "1") && StringUtils.equals(customerRegItemGroup.getPayStatus(), ExConstants.PAY_STATE_已支付)).toList();

            if (allItemGroupList.isEmpty()) {
                continue;
            }
            List<CustomerRegItemGroup> noCheckItemGroupList = allItemGroupList.stream().filter(customerRegItemGroup -> StringUtils.equals(customerRegItemGroup.getCheckStatus(), ExConstants.CHECK_STATUS_未检)).toList();
            if (noCheckItemGroupList.isEmpty()) {
                preAbleCustomerRegList.add(customerReg);
            } else {
                boolean preAble = noCheckItemGroupList.stream().allMatch(customerRegItemGroup -> allowNoCheckDepartIdList.contains(customerRegItemGroup.getDepartmentId()) || allowNoCheckItemGroupIdList.contains(customerRegItemGroup.getItemGroupId()));
                if (preAble) {
                    preAbleCustomerRegList.add(customerReg);
                }
            }
        }

        if (!preAbleCustomerRegList.isEmpty()) {
            jdbcTemplate.batchUpdate("update customer_reg set summary_status=?,pre_summary_time=?,pre_summary_by=?,pre_summary_method=? where id = ?", preAbleCustomerRegList, 1000, (ps, argument) -> {
                ps.setString(1, ExConstants.SUMMARY_STATUS_已预检);
                ps.setString(2, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                ps.setString(3, "系统");
                ps.setString(4, ExConstants.PRE_SUMMARY_TYPE_自动);
                ps.setString(5, argument.getId());
            });
        }
    }

    @Override
    public void doAutoInitialSummary() {
        // Query customer registrations eligible for auto initial summary
        LambdaQueryWrapper<CustomerReg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerReg::getSummaryStatus, ExConstants.SUMMARY_STATUS_已预检);
        queryWrapper.last("limit 1000");
        List<CustomerReg> records = customerRegMapper.selectList(queryWrapper);

        // 创建固定大小的线程池
        int threadPoolSize = Math.min(10, Runtime.getRuntime().availableProcessors() * 2);
        ExecutorService executorService = Executors.newFixedThreadPool(threadPoolSize);
        List<Future<?>> futures = new ArrayList<>();

        try {
            // 并行处理每条记录
            for (CustomerReg customerReg : records) {
                Future<?> future = executorService.submit(() -> {
                    processCustomerRegForAutoInitialSummary(customerReg);
                    return null;
                });
                futures.add(future);
            }

            // 等待所有任务完成
            for (Future<?> future : futures) {
                try {
                    future.get();
                } catch (Exception e) {
                    log.error("Error processing customer reg in thread pool", e);
                }
            }
        } finally {
            // 关闭线程池
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * Processes a single CustomerReg for automatic initial summary.
     *
     * @param customerReg
     */
    private void processCustomerRegForAutoInitialSummary(CustomerReg customerReg) {
        try {
            String customerRegId = customerReg.getId();
            // Generate abnormal summary
            CustomerRegSummary summary = getByRegId(customerRegId);
            if (summary == null) {
                summary = new CustomerRegSummary();

                // Generate abnormal summaries and advice
                AbnormalSummaryAndAdvice abnormalSummaryAndAdvice = generateAbnormalSummaryAndAdvice(customerReg, true);

                List<AdviceBean> adviceBeanList = abnormalSummaryAndAdvice.getAdviceList();
                summary.setSummaryJson(adviceBeanList);
                summary.setStatus(ExConstants.SUMMARY_STATUS_已初检);
                summary.setCustomerRegId(customerRegId);
                summary.setInitailTime(new Date());
                summary.setExamNo(customerReg.getExamNo());
                summary.setCharacterSummary(abnormalSummaryAndAdvice.getAbnormalSummary());
                summary.setInitailDoctor("AI");
                save(summary);

                //同步保存CustomerRegSummaryExtra
                CustomerRegSummaryExtra extra = new CustomerRegSummaryExtra();
                extra.setCustomerRegId(customerRegId);
                extra.setSummaryId(summary.getId());
                extra.setCreateTime(new Date());
                extra.setCreateBy("AI");
                extra.setAbnormalStructContent(JSONArray.toJSONString(abnormalSummaryAndAdvice.getAbnormalSummaryList()));
                extra.setSummaryStructContent(JSONArray.toJSONString(adviceBeanList));
                customerRegSummaryExtraMapper.insert(extra);


                // Update customer registration status
                jdbcTemplate.update("update customer_reg set summary_status=?,initail_summary_time=?,initail_summary_doctor=?,initail_summary_method=? where id = ?", ExConstants.SUMMARY_STATUS_已初检, new Date(), "AI", ExConstants.INITAIL_SUMMARY_TYPE_AI, customerRegId);

                // Record summary history
                CustomerRegSummaryHistory customerRegSummaryHistory = new CustomerRegSummaryHistory();
                customerRegSummaryHistory.setCustomerRegId(customerRegId);
                customerRegSummaryHistory.setSummaryId(summary.getId());
                customerRegSummaryHistory.setType("新增");
                customerRegSummaryHistory.setSummaryType("初检");
                customerRegSummaryHistory.setCreator("AI");
                customerRegSummaryHistoryMapper.insert(customerRegSummaryHistory);
            }
        } catch (Exception e) {
            log.error("自动生成AI初检异常 for CustomerReg ID: " + customerReg.getId(), e);
        }
    }

    @Override
    public AbnormalSummaryAndAdvice generateAbnormalSummaryAndAdvice(CustomerReg customerReg, Boolean useAi) throws Exception {
        String customerRegId = customerReg.getId();
        AbnormalSummaryAndAdvice abnormalSummaryAndAdvice = new AbnormalSummaryAndAdvice();
        //1、获取系统参数depart_summary_abnormal_only
        String departSummaryAbnormalOnly = sysSettingService.getValueByCode("depart_summary_abnormal_only");
        departSummaryAbnormalOnly = StringUtils.isBlank(departSummaryAbnormalOnly) ? "1" : departSummaryAbnormalOnly;

        //2、整理异常汇总列表
        List<AbnormalSummary> abnormalSummaryList;
        if (StringUtils.equals(departSummaryAbnormalOnly, "1")) {
            abnormalSummaryList = customerRegDepartSummaryService.generateAbnormalSummaryBeanList(customerRegId);
        } else {
            List<CustomerRegDepartSummary> departSummaryList = customerRegDepartSummaryService.listByCustomerReg(customerRegId, departSummaryAbnormalOnly);
            abnormalSummaryList = departSummaryList.stream().map(departSummary -> {
                AbnormalSummary abnormalSummary = new AbnormalSummary();
                abnormalSummary.setTitle(departSummary.getDepartmentName());
                abnormalSummary.setText(departSummary.getCharacterSummary());
                return abnormalSummary;
            }).collect(Collectors.toList());
        }

        if (useAi) {
            //3、调用AI服务生成异常汇总和建议
            //4、将异常汇总按照严重程度排序
           /* List<AbnormalSummary> abnormalSummaryByAis = aiService.adjustAbnormalList(abnormalSummaryList);
            if(abnormalSummaryByAis == null){
                abnormalSummaryByAis = abnormalSummaryList;
            }
            abnormalSummaryByAis.sort(Comparator.comparing(AbnormalSummary::getSeverityOrder));

            abnormalSummaryAndAdvice.setAbnormalSummaryList(abnormalSummaryByAis);

            //5、将异常汇总按照格式进行拼接
            String abnormalSummaryFormat = sysSettingService.getValueByCode("abnormalSummaryFormat");
            if (StringUtils.isBlank(abnormalSummaryFormat)) {
                abnormalSummaryFormat = "{{no}}、{{text}}";
            } else {
                abnormalSummaryFormat = abnormalSummaryFormat.replace("No", "no").replace("NO", "no");
            }

            //生成异常汇总文本
            List<String> abnormalSummaryTextList = new ArrayList<>();
            for (int i = 0; i < abnormalSummaryByAis.size(); i++) {
                AbnormalSummary abnormalSummary = abnormalSummaryByAis.get(i);
                String text = abnormalSummaryFormat.replace("{{no}}", String.valueOf(i + 1)).replace("{{text}}", abnormalSummary.getText());
                abnormalSummaryTextList.add(text);
            }
            abnormalSummaryAndAdvice.setAbnormalSummary(StringUtils.join(abnormalSummaryTextList, "\n"));*/


            List<String> abnormalSummaryTextList = abnormalSummaryList.stream().map(AbnormalSummary::getSummaryText).collect(Collectors.toList());
            abnormalSummaryAndAdvice.setAbnormalSummary(String.join("\n", abnormalSummaryList.stream().map(AbnormalSummary::getSummaryText).collect(Collectors.toList())));

            List<AdviceBean> advice = aiService.generateAdvice(abnormalSummaryTextList, customerReg);
            abnormalSummaryAndAdvice.setAdviceList(advice);
        } else {
            abnormalSummaryAndAdvice = new AbnormalSummaryAndAdvice();
            List<AbnormalSummary> abnormalSummaryList2 = new ArrayList<>();
            List<String> keywordListStr = new ArrayList<>();
            abnormalSummaryList.forEach(summaryBean -> {

                AbnormalSummary abnormalSummary = new AbnormalSummary();
                abnormalSummary.setTitle(summaryBean.getTitle());
                abnormalSummary.setText(summaryBean.getText());
                abnormalSummary.setSummaryText(summaryBean.getSummaryText());
                abnormalSummary.setSeverityOrder(0);
                abnormalSummary.setOrderReason("");
                abnormalSummaryList2.add(abnormalSummary);
                keywordListStr.add(summaryBean.getSummaryText());
            });
            abnormalSummaryAndAdvice.setAbnormalSummaryList(abnormalSummaryList2);
            abnormalSummaryAndAdvice.setAbnormalSummary(String.join("\n", keywordListStr));

            List<AdviceBean> adviceBeanList = summaryAdviceService.generateAdviceByDepartSummaryList(false, false, keywordListStr, customerReg);
            abnormalSummaryAndAdvice.setAdviceList(adviceBeanList);
        }

        return abnormalSummaryAndAdvice;
    }

    @Override
    public List<AdviceBean> generateAdviceByDepartSummary(Boolean aiSummary, String abnormalText, String customerRegId) throws Exception {

        //根据customerRegId获取总检建议
        if (StringUtils.isBlank(abnormalText)) {
            return new ArrayList<>();
        }

        CustomerReg customerReg = customerRegMapper.selectById(customerRegId);

        List<String> departSummaryTextList = Arrays.asList(abnormalText.split("\n"));

        List<AdviceBean> adviceBeanList = summaryAdviceService.generateAdviceByDepartSummaryList(aiSummary, false, departSummaryTextList, customerReg);
      /*  if (summary != null) {
            summary.setCharacterSummary(abnormalText);
            summary.setSummaryJson(adviceBeanList);
            customerRegSummaryMapper.updateById(summary);
        }*/
        return adviceBeanList;
    }

    @Async("sseTaskExecutor")
    public void generateAiSummaryAsync(List<AIMessage> messages, SseEmitter emitter, AtomicBoolean completed, String modId, String clientId) {
        log.info("Starting AI summary generation for SSE using DashScopeService.");
        AtomicBoolean errorOccurred = new AtomicBoolean(false); // 标志是否发生错误

        // 在异步线程中设置Shiro上下文，避免SecurityManager异常
        ShiroAsyncUtils.bindShiroContext();

        try {
            List<AiSetting> aiSettings = aiSettingMapper.getAiSettingByModule(ExConstants.AI_MODULE_总检, ExConstants.AI_FUNC_总检建议);

            if (aiSettings.isEmpty()) {
                throw new Exception("未配置AI模型设置！");
            }
            //筛选出dashscope模型
           List<AiSetting> aiSettingsFilted = aiSettings.stream().filter(setting -> StringUtils.equals(setting.getAiMod().getCode(), ExConstants.AI_MOD_DASHSCOPE)).toList();
            if (aiSettingsFilted.isEmpty()) {
                throw new Exception("未配置AI模型设置！");
            }

            AiSetting aiSetting = aiSettingsFilted.get(0);
            AiMod mod = aiSetting.getAiMod();
            if (mod == null) {
                throw new Exception("未配置AI模型 (ID: " + modId + ")");
            }
            log.info("Using AI Model: {} ({})", mod.getName(), mod.getCode()); // 添加模型日志

            if (StringUtils.equals(mod.getCode(), ExConstants.AI_MOD_DASHSCOPE)) {
                String config = aiSetting.getConfig();
                if (org.apache.commons.lang.StringUtils.isBlank(config)) {
                    throw new Exception("AI模型配置为空！");
                }

                JSONObject configObj = JSONObject.parseObject(config);
                if (configObj == null) {
                    throw new Exception("AI模型配置格式错误！");
                }

                String apiKey = configObj.getString("apiKey");
                String appId = configObj.getString("appId");
                if (org.apache.commons.lang.StringUtils.isBlank(apiKey) || org.apache.commons.lang.StringUtils.isBlank(appId)) {
                    throw new Exception("未配置阿里云百炼API Key或App ID");
                }

                // 将消息列表转换为单个prompt（简化处理）
                String prompt = buildPromptFromMessages(messages);
                log.info("Generated prompt for AI: {}", prompt.length() > 200 ? prompt.substring(0, 200) + "..." : prompt);

                // 使用客户端ID作为memoryId，实现多轮对话会话管理
                String memoryId = "summary_" + (StringUtils.isNotBlank(clientId) ? clientId : System.currentTimeMillis());
                log.info("Using memoryId: {}, clientId: {}", memoryId, clientId);

                // 使用 try-finally 确保即使在循环中出错，也能尝试关闭 emitter
                try {
                    StringBuilder response = new StringBuilder();
                    // 1. 创建parser对象
                    StreamingDiagnoseJsonParser parser = new StreamingDiagnoseJsonParser();

                    // 先测试非流式调用验证API配置
                    try {
                        log.info("Testing non-stream API call first...");
                        ApplicationResult testResult = dashScopeService.doCall(appId, apiKey, memoryId, "测试消息");
                        if (testResult != null && testResult.getOutput() != null) {
                            log.info("Non-stream API test successful, response length: {}", testResult.getOutput().getText().length());
                        } else {
                            log.warn("Non-stream API test returned null or empty result");
                        }
                    } catch (Exception e) {
                        log.error("Non-stream API test failed: {}", e.getMessage(), e);
                    }

                    // 2. 使用DashScopeService执行流式调用
                    dashScopeService.doStreamCall(appId, memoryId, clientId, prompt, new DashscopeStreamResponseCallback() {
                        @Override
                        public void onMessage(org.jeecg.modules.ai.model.ApplicationResult message) {
                            try {
                                log.debug("Received AI stream message: {}", message != null ? "not null" : "null");
                                if (message == null || message.getOutput() == null) {
                                    log.warn("Received null message or null output from AI stream");
                                    return;
                                }

                                // 获取AI响应内容
                                String content = message.getOutput().getText();
                                log.debug("AI stream content: {}", content != null ? content.length() + " chars" : "null");

                                if (StringUtils.isNotBlank(content)) {
                                    response.append(content);
                                    log.debug("Appended content to response, total length: {}", response.length());

                                    parser.append(content, diagnoseObj -> {
                                        try {
                                            log.debug("Parsed diagnose object, sending to frontend");
                                            // 这里拿到一个完整的对象，直接发送SSE前端
                                            emitter.send(SseEmitter.event().name("update").data(JSONObject.toJSONString(diagnoseObj)));
                                        } catch (IOException e) {
                                            log.error("发送SSE消息时出错", e);
                                        }
                                    });
                                } else {
                                    log.debug("Received empty content from AI stream");
                                }
                            } catch (Exception e) {
                                log.error("处理百炼应用流式响应消息时出错", e);
                            }
                        }

                        @Override
                        public void onComplete() {
                            // 流式响应完成
                            log.info("百炼应用流式调用完成，总响应长度: {} 字符", response.length());

                            // 如果没有收到任何内容，记录警告
                            if (response.length() == 0) {
                                log.warn("AI流式调用完成但没有收到任何内容，可能的原因：1.API配置错误 2.prompt为空 3.模型无响应");
                            }

                            // 发送完成事件
                            if (completed.compareAndSet(false, true)) {
                                log.info("DashScope stream finished. Sending 'done' event and completing SSE stream normally.");
                                try {
                                    emitter.send(SseEmitter.event().name("done").data("Generation completed."));
                                    emitter.complete();
                                } catch (IOException ex) {
                                    log.error("IOException while sending 'done' event or completing emitter: {}", ex.getMessage());
                                } catch (IllegalStateException ex) {
                                    log.warn("Emitter already completed when trying to send 'done' event or complete normally: {}", ex.getMessage());
                                }
                            }
                        }

                        @Override
                        public void onError(Throwable throwable) {
                            log.error("百炼应用流式调用出错", throwable);
                            errorOccurred.set(true);
                            sendErrorEvent(emitter, "AI模型流式处理错误: " + throwable.getMessage());
                        }
                    });
                } catch (Exception e) {
                    // 处理HTTP调用可能抛出的异常
                    log.error("Error during DashScope stream processing: {}", e.getMessage(), e);
                    errorOccurred.set(true);
                    sendErrorEvent(emitter, "AI模型流式处理错误: " + e.getMessage());
                    throw e; // 重新抛出以便外层捕获并调用 completeWithError
                }

                // 完成逻辑已在onComplete回调中处理
            } else {
                throw new Exception("暂不支持该模型！");
            }
        } catch (IOException e) {
            // IO异常通常发生在发送数据时客户端断开连接
            errorOccurred.set(true); // 标记发生错误
            if (completed.compareAndSet(false, true)) {
                log.error("IOException during SSE processing, likely client disconnected: {}", e.getMessage());
                // emitter.completeWithError(e); // 通常 IOException 后 emitter 状态可能不允许再调用，依赖 onError 回调
            } else {
                log.warn("IOException occurred, but 'completed' flag was already true.");
            }
        } catch (Exception e) {
            // 其他在生成过程中可能发生的异常
            errorOccurred.set(true); // 标记发生错误
            if (completed.compareAndSet(false, true)) {
                log.error("Unexpected error during AI summary generation for SSE: {}", e.getMessage(), e);
                // **** 关键修改：优先发送错误事件 ****
                sendErrorEvent(emitter, "AI服务内部错误: " + e.getMessage());
                emitter.completeWithError(e); // 使用 completeWithError 触发 onError 回调
            } else {
                log.warn("Unexpected error occurred, but 'completed' flag was already true.");
            }
        } finally {
            log.debug("Exiting asynchronous AI summary generation method. Error occurred: {}", errorOccurred.get());

            // 清理异步线程中的Shiro上下文
            ShiroAsyncUtils.unbindShiroContext();
        }
    }

    // 辅助方法发送错误事件
    private void sendErrorEvent(SseEmitter emitter, String errorMessage) {
        try {
            // 前端在处理 custom_error
            emitter.send(SseEmitter.event().name("custom_error").data(errorMessage));
            log.info("Sent 'custom_error' event to client.");
        } catch (Exception sendEx) {
            log.error("Failed to send error event ('custom_error') to client: {}", sendEx.getMessage());
        }
    }

    /**
     * 将消息列表转换为单个prompt
     * @param messages 消息列表
     * @return 合并后的prompt字符串
     */
    private String buildPromptFromMessages(List<AIMessage> messages) {
        if (messages == null || messages.isEmpty()) {
            return "";
        }

        StringBuilder promptBuilder = new StringBuilder();
        for (AIMessage message : messages) {
            if (StringUtils.isNotBlank(message.getContent())) {
                if (promptBuilder.length() > 0) {
                    promptBuilder.append("\n");
                }
                // 根据角色添加前缀
                if ("system".equals(message.getRole())) {
                    promptBuilder.append("[系统]: ").append(message.getContent());
                } else if ("user".equals(message.getRole())) {
                    promptBuilder.append("[用户]: ").append(message.getContent());
                } else if ("assistant".equals(message.getRole())) {
                    promptBuilder.append("[助手]: ").append(message.getContent());
                } else {
                    promptBuilder.append(message.getContent());
                }
            }
        }

        return promptBuilder.toString();
    }

}
