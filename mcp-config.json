{"mcpServers": {"physicalex-project-manager": {"command": "python", "args": ["-m", "mcp_servers.project_manager"], "env": {"PROJECT_PATH": "D:\\IdeaProjects\\physicalex", "CONFIG_PATH": "D:\\IdeaProjects\\physicalex\\agents\\project-manager-agent.yaml"}, "capabilities": {"requirement_analysis": {"description": "需求分析和管理", "tools": ["analyze_requirement", "create_user_story", "estimate_effort", "assess_risk"]}, "project_planning": {"description": "项目计划和进度管理", "tools": ["create_milestone", "track_progress", "resource_allocation", "timeline_adjustment"]}, "team_coordination": {"description": "团队协调和沟通", "tools": ["schedule_meeting", "send_notification", "resolve_conflict", "status_report"]}}}, "physicalex-ux-designer": {"command": "python", "args": ["-m", "mcp_servers.ux_designer"], "env": {"PROJECT_PATH": "D:\\IdeaProjects\\physicalex", "DESIGN_ASSETS_PATH": "D:\\IdeaProjects\\physicalex\\design"}, "capabilities": {"user_research": {"description": "用户研究和分析", "tools": ["create_user_persona", "analyze_user_journey", "conduct_usability_test", "gather_feedback"]}, "interaction_design": {"description": "交互设计和原型", "tools": ["create_wireframe", "design_user_flow", "build_prototype", "define_interaction"]}, "medical_ux_optimization": {"description": "医疗场景用户体验优化", "tools": ["optimize_medical_workflow", "design_error_prevention", "improve_data_entry", "enhance_accessibility"]}}}, "physicalex-ui-designer": {"command": "python", "args": ["-m", "mcp_servers.ui_designer"], "env": {"PROJECT_PATH": "D:\\IdeaProjects\\physicalex", "DESIGN_SYSTEM_PATH": "D:\\IdeaProjects\\physicalex\\design-system"}, "capabilities": {"visual_design": {"description": "视觉设计和界面美化", "tools": ["create_design_mockup", "generate_color_palette", "design_icons", "create_illustrations"]}, "design_system": {"description": "设计系统和组件库", "tools": ["maintain_design_tokens", "create_component_specs", "update_style_guide", "ensure_consistency"]}, "medical_ui_theming": {"description": "医疗主题设计", "tools": ["apply_medical_themes", "design_health_indicators", "create_medical_charts", "optimize_readability"]}}}, "physicalex-frontend-engineer": {"command": "python", "args": ["-m", "mcp_servers.frontend_engineer"], "env": {"PROJECT_PATH": "D:\\IdeaProjects\\physicalex\\admin-front", "NODE_ENV": "development", "VUE_APP_API_BASE_URL": "http://localhost:8080"}, "capabilities": {"vue_development": {"description": "Vue3 + TypeScript开发", "tools": ["create_vue_component", "implement_composition_api", "setup_reactive_system", "handle_lifecycle"]}, "ui_implementation": {"description": "UI界面实现", "tools": ["implement_ant_design", "create_responsive_layout", "handle_form_validation", "optimize_performance"]}, "api_integration": {"description": "API接口集成", "tools": ["setup_axios_client", "handle_api_requests", "manage_error_handling", "implement_caching"]}, "medical_components": {"description": "医疗专用组件开发", "tools": ["create_patient_forms", "build_medical_charts", "implement_workflow_ui", "develop_report_viewer"]}}}, "physicalex-backend-engineer": {"command": "python", "args": ["-m", "mcp_servers.backend_engineer"], "env": {"PROJECT_PATH": "D:\\IdeaProjects\\physicalex\\physicalex-lkd", "JAVA_HOME": "path/to/java17", "SPRING_PROFILES_ACTIVE": "dev"}, "capabilities": {"api_development": {"description": "Spring Boot API开发", "tools": ["create_rest_controller", "implement_service_layer", "design_data_mapper", "handle_exceptions"]}, "database_operations": {"description": "数据库设计和操作", "tools": ["design_database_schema", "write_sql_queries", "optimize_performance", "manage_migrations"]}, "business_logic": {"description": "医疗业务逻辑实现", "tools": ["implement_medical_workflows", "handle_patient_data", "process_examination_results", "generate_reports"]}, "security_compliance": {"description": "安全和合规实现", "tools": ["implement_authentication", "encrypt_sensitive_data", "audit_operations", "ensure_compliance"]}}}, "physicalex-database-designer": {"command": "python", "args": ["-m", "mcp_servers.database_designer"], "env": {"PROJECT_PATH": "D:\\IdeaProjects\\physicalex", "CONFIG_PATH": "D:\\IdeaProjects\\physicalex\\agents\\database-designer-agent.yaml", "MYSQL_HOST": "**************", "MYSQL_PORT": "3306", "MYSQL_USER": "pes", "MYSQL_PASSWORD": "Pes123!@#", "MYSQL_DATABASE": "physicalex-lkd"}, "capabilities": {"business_table_design": {"description": "基于业务逻辑的表设计", "tools": ["create_business_table", "modify_table_schema", "add_business_columns", "create_table_relationships"]}, "table_management": {"description": "表结构管理和变更", "tools": ["create_table_sql", "alter_table_sql", "add_remove_columns", "manage_table_indexes"]}, "business_logic_support": {"description": "支持后端业务逻辑", "tools": ["map_entity_to_table", "design_data_constraints", "create_migration_scripts", "validate_business_rules"]}, "schema_evolution": {"description": "数据库架构演进", "tools": ["version_control_schema", "generate_migration_sql", "backward_compatibility_check", "impact_analysis"]}}}, "physicalex-test-engineer": {"command": "python", "args": ["-m", "mcp_servers.test_engineer"], "env": {"PROJECT_PATH": "D:\\IdeaProjects\\physicalex", "TEST_ENV": "development", "COVERAGE_THRESHOLD": "80"}, "capabilities": {"functional_testing": {"description": "功能测试和回归测试", "tools": ["create_test_cases", "execute_unit_tests", "execute_integration_tests", "generate_test_reports"]}, "performance_testing": {"description": "性能和压力测试", "tools": ["load_testing", "stress_testing", "benchmark_testing", "performance_monitoring"]}, "automated_testing": {"description": "自动化测试管理", "tools": ["e2e_testing", "api_testing", "ui_testing", "regression_testing"]}, "quality_assurance": {"description": "质量保证和合规测试", "tools": ["code_coverage_analysis", "security_testing", "accessibility_testing", "medical_compliance_testing"]}}}, "physicalex-acceptance-engineer": {"command": "python", "args": ["-m", "mcp_servers.acceptance_engineer"], "env": {"PROJECT_PATH": "D:\\IdeaProjects\\physicalex", "ACCEPTANCE_ENV": "staging", "BUSINESS_DOMAIN": "medical"}, "capabilities": {"business_validation": {"description": "业务需求验收和确认", "tools": ["validate_business_requirements", "confirm_user_stories", "verify_medical_workflows", "assess_user_experience"]}, "quality_assessment": {"description": "交付质量评估", "tools": ["evaluate_deliverables", "check_documentation", "verify_compliance", "assess_readiness"]}, "user_acceptance": {"description": "用户验收测试管理", "tools": ["coordinate_uat", "collect_user_feedback", "manage_acceptance_criteria", "sign_off_delivery"]}, "deployment_validation": {"description": "生产环境验证", "tools": ["validate_production_env", "verify_deployment", "check_system_stability", "confirm_data_integrity"]}}}, "mysql-database": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-mysql"], "env": {"MYSQL_HOST": "**************", "MYSQL_PORT": "3306", "MYSQL_USER": "pes", "MYSQL_PASSWORD": "Pes123!@#", "MYSQL_DATABASE": "physicalex-lkd", "MYSQL_SSL": "false", "MYSQL_CONNECTION_LIMIT": "10", "MYSQL_TIMEOUT": "60000"}, "description": "MySQL数据库直接连接服务", "capabilities": {"direct_sql": {"description": "直接SQL执行", "tools": ["execute_query", "execute_update", "execute_batch", "get_table_schema"]}, "schema_management": {"description": "数据库模式管理", "tools": ["list_tables", "describe_table", "show_indexes", "explain_query"]}}}}, "workflows": {"feature_development": {"description": "完整功能开发流程", "agents": ["physicalex-project-manager", "physicalex-ux-designer", "physicalex-ui-designer", "physicalex-database-designer", "physicalex-backend-engineer", "<PERSON>ex-frontend-engineer", "physicalex-test-engineer", "physicalex-acceptance-engineer"], "steps": [{"name": "需求分析", "agent": "physicalex-project-manager", "actions": ["analyze_requirement", "create_user_story"]}, {"name": "用户体验设计", "agent": "physicalex-ux-designer", "actions": ["create_user_journey", "design_user_flow"]}, {"name": "界面设计", "agent": "physicalex-ui-designer", "actions": ["create_design_mockup", "update_style_guide"]}, {"name": "数据库设计", "agent": "physicalex-database-designer", "actions": ["create_business_table", "map_entity_to_table", "create_table_relationships"]}, {"name": "后端开发", "agent": "physicalex-backend-engineer", "actions": ["create_rest_controller", "implement_service_layer"]}, {"name": "前端开发", "agent": "<PERSON>ex-frontend-engineer", "actions": ["create_vue_component", "implement_api_integration"]}, {"name": "功能测试", "agent": "physicalex-test-engineer", "actions": ["create_test_cases", "execute_functional_tests", "performance_testing"]}, {"name": "业务验收", "agent": "physicalex-acceptance-engineer", "actions": ["validate_business_requirements", "user_acceptance_testing", "sign_off_delivery"]}]}, "bug_fixing": {"description": "问题修复流程", "agents": ["physicalex-project-manager", "<PERSON>ex-frontend-engineer", "physicalex-backend-engineer"], "steps": [{"name": "问题分析", "agent": "physicalex-project-manager", "actions": ["assess_risk", "estimate_effort"]}, {"name": "定位问题", "agent": "<PERSON>ex-frontend-engineer", "actions": ["debug_frontend", "check_api_calls"]}, {"name": "修复问题", "agent": "physicalex-backend-engineer", "actions": ["fix_backend_logic", "test_api_endpoints"]}]}, "performance_optimization": {"description": "性能优化流程", "agents": ["physicalex-database-designer", "physicalex-backend-engineer", "<PERSON>ex-frontend-engineer"], "steps": [{"name": "数据库业务优化", "agent": "physicalex-database-designer", "actions": ["modify_table_schema", "add_business_columns", "create_migration_scripts"]}, {"name": "后端性能优化", "agent": "physicalex-backend-engineer", "actions": ["optimize_sql_queries", "improve_caching"]}, {"name": "前端性能优化", "agent": "<PERSON>ex-frontend-engineer", "actions": ["optimize_performance", "implement_caching"]}]}, "business_table_creation": {"description": "业务逻辑建表流程", "agents": ["physicalex-backend-engineer", "physicalex-database-designer"], "steps": [{"name": "业务实体分析", "agent": "physicalex-backend-engineer", "actions": ["define_business_entity", "specify_data_requirements"]}, {"name": "数据表设计", "agent": "physicalex-database-designer", "actions": ["create_business_table", "map_entity_to_table", "design_data_constraints"]}, {"name": "表结构实现", "agent": "physicalex-database-designer", "actions": ["create_table_sql", "add_business_columns", "create_table_relationships"]}, {"name": "ORM映射验证", "agent": "physicalex-backend-engineer", "actions": ["validate_orm_mapping", "test_business_logic"]}]}, "data_migration": {"description": "数据迁移流程", "agents": ["physicalex-project-manager", "physicalex-database-designer", "physicalex-backend-engineer"], "steps": [{"name": "迁移计划", "agent": "physicalex-project-manager", "actions": ["assess_risk", "create_migration_plan"]}, {"name": "数据库准备", "agent": "physicalex-database-designer", "actions": ["design_migration_schema", "prepare_migration_scripts"]}, {"name": "应用程序适配", "agent": "physicalex-backend-engineer", "actions": ["update_data_models", "modify_business_logic"]}, {"name": "执行迁移", "agent": "physicalex-database-designer", "actions": ["execute_migration", "validate_data_integrity", "performance_testing"]}]}}, "communication_channels": {"daily_standup": {"frequency": "daily", "participants": "all_agents", "format": "status_update"}, "weekly_review": {"frequency": "weekly", "participants": "all_agents", "format": "progress_review"}, "milestone_planning": {"frequency": "milestone", "participants": ["physicalex-project-manager"], "format": "planning_session"}}, "quality_gates": {"code_quality": {"frontend": {"typescript_check": true, "eslint_check": true, "test_coverage": ">= 80%", "performance_budget": "< 3s first load"}, "backend": {"unit_tests": ">= 80% coverage", "integration_tests": "all APIs tested", "security_scan": "no high/critical issues", "performance": "< 200ms API response"}}, "medical_compliance": {"data_privacy": "HIPAA compliant", "audit_logging": "all operations logged", "access_control": "role-based permissions", "data_encryption": "sensitive data encrypted"}}, "monitoring": {"performance_metrics": ["api_response_time", "frontend_load_time", "database_query_time", "error_rates"], "business_metrics": ["user_satisfaction", "system_availability", "data_accuracy", "workflow_efficiency"]}}