# 单位选择组件使用指南

## 🎯 项目概述

基于现有的Company和CompanyCategory实体类，创建了一个支持树形结构的单位下拉选择器组件，让用户能够在不脱离当前认知的情况下完成单位和部门的选择。

## 📁 文件结构

```
src/components/Form/src/jeecg/components/
├── JSelectCompany.vue              # 主要的选择器组件
├── CompanyTreeSelect.vue           # 核心树形选择组件
└── README-CompanySelect.md         # 详细文档

src/views/basicinfo/
└── Company.api.ts                  # 扩展的API接口

src/views/test/
├── CompanySelectTest.vue           # 功能测试页面
└── CompanyFormExample.vue          # 表单使用示例
```

## 🚀 快速开始

### 1. 基础使用

```vue
<template>
  <JSelectCompany
    v-model:value="companyId"
    placeholder="请选择单位"
    @change="handleChange"
  />
</template>

<script setup>
import { ref } from 'vue';
import JSelectCompany from '/@/components/Form/src/jeecg/components/JSelectCompany.vue';

const companyId = ref();

function handleChange(value, label, extra) {
  console.log('选择的单位:', value);
}
</script>
```

### 2. 多选模式

```vue
<JSelectCompany
  v-model:value="companyIds"
  :multiple="true"
  placeholder="请选择多个单位"
/>
```

### 3. 表单中使用

```vue
<a-form-item label="所属单位" name="companyId">
  <JSelectCompany
    v-model:value="form.companyId"
    :leaf-only="true"
    placeholder="请选择所属单位"
  />
</a-form-item>
```

## ✨ 主要特性

- ✅ **树形结构**: 支持单位层级关系展示
- ✅ **下拉选择**: 用户无需离开当前页面
- ✅ **实时搜索**: 快速定位目标单位
- ✅ **异步加载**: 大数据量时性能优秀
- ✅ **单选/多选**: 灵活的选择模式
- ✅ **分类显示**: 显示单位分类信息
- ✅ **叶子节点**: 可限制只选择最底层单位
- ✅ **响应式**: 适配不同屏幕尺寸

## 🛠 配置选项

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `multiple` | `boolean` | `false` | 是否多选 |
| `leafOnly` | `boolean` | `false` | 只能选择叶子节点 |
| `showCategory` | `boolean` | `true` | 显示分类信息 |
| `labelInValue` | `boolean` | `false` | 返回标签值对象 |
| `showSearch` | `boolean` | `true` | 显示搜索框 |

## 🧪 测试页面

访问以下页面进行测试：

1. **功能测试**: `src/views/test/CompanySelectTest.vue`
   - 测试各种配置选项
   - 验证事件处理
   - 查看数据格式

2. **表单示例**: `src/views/test/CompanyFormExample.vue`
   - 表单验证
   - 实际使用场景
   - 数据提交

## 🔧 后端接口

组件依赖以下后端接口：

```
GET /basicinfo/company/loadTreeRoot        # 加载根节点
GET /basicinfo/company/loadTreeChildren    # 加载子节点  
GET /basicinfo/companyCategory/list        # 加载分类信息
```

## 📝 使用建议

1. **表单验证**: 配合Ant Design Vue的表单验证使用
2. **性能优化**: 大数据量时启用异步加载
3. **用户体验**: 合理设置placeholder和提示信息
4. **数据处理**: 根据业务需求选择合适的返回格式

## 🎨 样式定制

```css
/* 自定义组件宽度 */
.JSelectCompany {
  width: 100%;
}

/* 分类信息样式 */
.company-category {
  color: #999;
  font-size: 12px;
}
```

## 🔄 与现有组件对比

| 特性 | JSelectCompany | JSelectDept | 传统Select |
|------|----------------|-------------|------------|
| 树形结构 | ✅ | ✅ | ❌ |
| 下拉展示 | ✅ | ❌ (弹窗) | ✅ |
| 搜索功能 | ✅ | ✅ | ❌ |
| 异步加载 | ✅ | ✅ | ❌ |
| 认知连续性 | ✅ | ❌ | ✅ |

## 📞 技术支持

如有问题，请查看：
1. 详细文档: `README-CompanySelect.md`
2. 测试页面: 验证功能是否正常
3. 控制台日志: 查看错误信息
4. 网络请求: 检查API接口状态

---

**开发完成时间**: 2025-08-26  
**版本**: v1.0.0  
**兼容性**: Vue 3 + Ant Design Vue
