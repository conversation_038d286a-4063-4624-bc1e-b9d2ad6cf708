-- 项目依赖关系数据验证SQL脚本
-- 用于排查和修复依赖关系数据中的错误

-- =====================================================
-- 1. 数据完整性检查
-- =====================================================

-- 1.1 检查无效的关联项目ID
SELECT 
    '无效关联项目ID' as error_type,
    igr.id,
    igr.group_id,
    igr.group_name,
    igr.relation,
    igr.relation_group_id,
    igr.relation_group_name,
    '关联项目不存在' as error_description
FROM item_group_relation igr 
LEFT JOIN item_group ig ON igr.relation_group_id = ig.id 
WHERE ig.id IS NULL
ORDER BY igr.group_id, igr.relation;

-- 1.2 检查无效的主项目ID
SELECT 
    '无效主项目ID' as error_type,
    igr.id,
    igr.group_id,
    igr.group_name,
    igr.relation,
    '主项目不存在' as error_description
FROM item_group_relation igr 
LEFT JOIN item_group ig ON igr.group_id = ig.id 
WHERE ig.id IS NULL
ORDER BY igr.group_id, igr.relation;

-- 1.3 检查关系类型规范性
SELECT 
    '非标准关系类型' as error_type,
    igr.id,
    igr.group_id,
    igr.group_name,
    igr.relation,
    CONCAT('关系类型"', igr.relation, '"不在标准范围内') as error_description
FROM item_group_relation igr 
WHERE igr.relation NOT IN ('依赖', '附属', '赠送', '互斥')
ORDER BY igr.relation, igr.group_id;

-- 1.4 检查无效的部位ID
SELECT 
    '无效部位ID' as error_type,
    igr.id,
    igr.group_id,
    igr.group_name,
    igr.main_check_part_id,
    igr.main_check_part_name,
    '主项目部位不存在' as error_description
FROM item_group_relation igr 
LEFT JOIN check_part cp ON igr.main_check_part_id = cp.id
WHERE igr.main_check_part_id IS NOT NULL 
AND igr.main_check_part_id != ''
AND cp.id IS NULL
ORDER BY igr.group_id;

-- 1.5 检查关联项目部位ID
SELECT 
    '无效关联部位ID' as error_type,
    igr.id,
    igr.group_id,
    igr.group_name,
    igr.relation_check_part_id,
    igr.relation_check_part_name,
    '关联项目部位不存在' as error_description
FROM item_group_relation igr 
LEFT JOIN check_part cp ON igr.relation_check_part_id = cp.id
WHERE igr.relation_check_part_id IS NOT NULL 
AND igr.relation_check_part_id != ''
AND cp.id IS NULL
ORDER BY igr.group_id;

-- =====================================================
-- 2. 业务逻辑检查
-- =====================================================

-- 2.1 检查循环依赖（直接循环）
SELECT 
    '直接循环依赖' as error_type,
    igr1.group_id as project_a,
    igr1.group_name as project_a_name,
    igr1.relation_group_id as project_b,
    igr1.relation_group_name as project_b_name,
    '两个项目互相依赖' as error_description
FROM item_group_relation igr1
JOIN item_group_relation igr2 ON igr1.group_id = igr2.relation_group_id 
    AND igr1.relation_group_id = igr2.group_id
WHERE igr1.relation = '依赖' 
AND igr2.relation = '依赖'
AND igr1.group_id < igr1.relation_group_id -- 避免重复记录
ORDER BY igr1.group_id;

-- 2.2 检查自依赖
SELECT 
    '自依赖错误' as error_type,
    igr.id,
    igr.group_id,
    igr.group_name,
    igr.relation,
    '项目不能依赖自己' as error_description
FROM item_group_relation igr 
WHERE igr.group_id = igr.relation_group_id
ORDER BY igr.group_id;

-- 2.3 检查重复的依赖关系
SELECT 
    '重复依赖关系' as error_type,
    igr.group_id,
    igr.group_name,
    igr.relation,
    igr.relation_group_id,
    igr.relation_group_name,
    COUNT(*) as duplicate_count,
    '存在重复的依赖关系定义' as error_description
FROM item_group_relation igr 
GROUP BY igr.group_id, igr.relation, igr.relation_group_id, 
         igr.relation_item_type, igr.relation_item_id
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC, igr.group_id;

-- 2.4 检查互斥项目的依赖关系冲突
SELECT 
    '互斥依赖冲突' as error_type,
    igr1.group_id as main_project,
    igr1.group_name as main_project_name,
    igr1.relation_group_id as conflict_project,
    igr1.relation_group_name as conflict_project_name,
    '项目既互斥又有依赖关系' as error_description
FROM item_group_relation igr1
JOIN item_group_relation igr2 ON igr1.group_id = igr2.group_id 
    AND igr1.relation_group_id = igr2.relation_group_id
WHERE igr1.relation = '互斥' 
AND igr2.relation IN ('依赖', '附属', '赠送')
ORDER BY igr1.group_id;

-- =====================================================
-- 3. 数据统计分析
-- =====================================================

-- 3.1 依赖关系统计
SELECT 
    '依赖关系统计' as report_type,
    igr.relation,
    COUNT(*) as total_count,
    COUNT(DISTINCT igr.group_id) as unique_main_projects,
    COUNT(DISTINCT igr.relation_group_id) as unique_related_projects
FROM item_group_relation igr 
GROUP BY igr.relation
ORDER BY total_count DESC;

-- 3.2 项目依赖复杂度分析
SELECT 
    '项目依赖复杂度' as report_type,
    igr.group_id,
    igr.group_name,
    COUNT(CASE WHEN igr.relation = '依赖' THEN 1 END) as dependency_count,
    COUNT(CASE WHEN igr.relation = '附属' THEN 1 END) as attachment_count,
    COUNT(CASE WHEN igr.relation = '赠送' THEN 1 END) as gift_count,
    COUNT(CASE WHEN igr.relation = '互斥' THEN 1 END) as mutex_count,
    COUNT(*) as total_relations
FROM item_group_relation igr 
GROUP BY igr.group_id, igr.group_name
HAVING COUNT(*) > 5 -- 关系数量超过5的项目
ORDER BY total_relations DESC;

-- 3.3 被依赖项目分析
SELECT 
    '被依赖项目分析' as report_type,
    igr.relation_group_id as project_id,
    igr.relation_group_name as project_name,
    COUNT(DISTINCT igr.group_id) as dependent_by_count,
    GROUP_CONCAT(DISTINCT igr.group_name SEPARATOR '; ') as dependent_by_projects
FROM item_group_relation igr 
WHERE igr.relation = '依赖'
GROUP BY igr.relation_group_id, igr.relation_group_name
HAVING COUNT(DISTINCT igr.group_id) > 3 -- 被超过3个项目依赖
ORDER BY dependent_by_count DESC;

-- =====================================================
-- 4. 修复建议SQL
-- =====================================================

-- 4.1 删除无效关联项目的关系记录
-- DELETE FROM item_group_relation 
-- WHERE id IN (
--     SELECT igr.id FROM item_group_relation igr 
--     LEFT JOIN item_group ig ON igr.relation_group_id = ig.id 
--     WHERE ig.id IS NULL
-- );

-- 4.2 删除自依赖记录
-- DELETE FROM item_group_relation 
-- WHERE group_id = relation_group_id;

-- 4.3 删除重复的依赖关系（保留最新的）
-- DELETE igr1 FROM item_group_relation igr1
-- JOIN item_group_relation igr2 ON igr1.group_id = igr2.group_id 
--     AND igr1.relation = igr2.relation 
--     AND igr1.relation_group_id = igr2.relation_group_id
--     AND igr1.relation_item_type = igr2.relation_item_type
--     AND COALESCE(igr1.relation_item_id, '') = COALESCE(igr2.relation_item_id, '')
--     AND igr1.id < igr2.id;

-- =====================================================
-- 5. 数据验证存储过程
-- =====================================================

DELIMITER //

CREATE PROCEDURE ValidateDependencyData()
BEGIN
    DECLARE error_count INT DEFAULT 0;
    DECLARE warning_count INT DEFAULT 0;
    
    -- 创建临时表存储验证结果
    DROP TEMPORARY TABLE IF EXISTS validation_results;
    CREATE TEMPORARY TABLE validation_results (
        error_type VARCHAR(50),
        severity ENUM('ERROR', 'WARNING', 'INFO'),
        count INT,
        description TEXT
    );
    
    -- 检查无效关联项目ID
    SELECT COUNT(*) INTO @invalid_relations FROM item_group_relation igr 
    LEFT JOIN item_group ig ON igr.relation_group_id = ig.id 
    WHERE ig.id IS NULL;
    
    IF @invalid_relations > 0 THEN
        INSERT INTO validation_results VALUES ('无效关联项目ID', 'ERROR', @invalid_relations, '存在关联到不存在项目的依赖关系');
        SET error_count = error_count + @invalid_relations;
    END IF;
    
    -- 检查循环依赖
    SELECT COUNT(*) INTO @circular_deps FROM item_group_relation igr1
    JOIN item_group_relation igr2 ON igr1.group_id = igr2.relation_group_id 
        AND igr1.relation_group_id = igr2.group_id
    WHERE igr1.relation = '依赖' AND igr2.relation = '依赖';
    
    IF @circular_deps > 0 THEN
        INSERT INTO validation_results VALUES ('循环依赖', 'ERROR', @circular_deps, '存在循环依赖关系');
        SET error_count = error_count + @circular_deps;
    END IF;
    
    -- 检查重复关系
    SELECT COUNT(*) INTO @duplicate_relations FROM (
        SELECT group_id, relation, relation_group_id, relation_item_type, relation_item_id
        FROM item_group_relation 
        GROUP BY group_id, relation, relation_group_id, relation_item_type, relation_item_id
        HAVING COUNT(*) > 1
    ) AS duplicates;
    
    IF @duplicate_relations > 0 THEN
        INSERT INTO validation_results VALUES ('重复关系', 'WARNING', @duplicate_relations, '存在重复的依赖关系定义');
        SET warning_count = warning_count + @duplicate_relations;
    END IF;
    
    -- 返回验证结果
    SELECT * FROM validation_results;
    
    -- 返回汇总信息
    SELECT 
        error_count as total_errors,
        warning_count as total_warnings,
        CASE 
            WHEN error_count = 0 AND warning_count = 0 THEN 'PASS'
            WHEN error_count = 0 THEN 'PASS_WITH_WARNINGS'
            ELSE 'FAIL'
        END as validation_status;
        
END //

DELIMITER ;
