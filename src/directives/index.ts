/**
 * Configure and register global directives
 */
import type { App } from 'vue';
import { setupPermissionDirective } from './permission';
import { setupLoadingDirective } from './loading';
import { setupCleaveDirective } from './cleave-directive';
import { enterKey } from './enterKey';

export function setupGlobDirectives(app: App) {
  setupPermissionDirective(app);
  setupLoadingDirective(app);
  setupCleaveDirective(app);

  // 注册回车键指令
  app.directive('enter-key', enterKey);
}
