import { App, Directive, DirectiveBinding } from 'vue';
import Cleave from 'cleave.js';

function applyMask(el: HTMLInputElement, binding: DirectiveBinding<any>) {
  el.cleave = new Cleave(el, binding.value || {});
}

function updateMask(el: HTMLInputElement) {
  if (el.cleave && el.cleave.properties.result && el.cleave.properties.result !== el.value) {
    const event = new Event('input', { bubbles: true });
    setTimeout(() => {
      el.value = el.cleave.properties.result;
      el.dispatchEvent(event);
    }, 100);
  }
}

const mounted = (el: HTMLInputElement, binding: DirectiveBinding<any>) => {
  applyMask(el, binding);
};

const beforeUpdate = (el: HTMLInputElement) => {
  updateMask(el);
};

const cleaveDirective: Directive = {
  mounted,
  beforeUpdate,
};

export function setupCleaveDirective(app: App) {
  app.directive('cleave', cleaveDirective);
}

export default cleaveDirective;
