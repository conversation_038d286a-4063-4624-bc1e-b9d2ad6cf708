import { DirectiveBinding } from 'vue';

/**
 * 自定义指令：处理回车键按下事件
 * 用法：v-enter-key="handleEnterPress"
 */
export const enterKey = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const handler = binding.value;
    if (typeof handler !== 'function') {
      console.warn('v-enter-key directive requires a function value');
      return;
    }

    // 查找输入元素
    const findInput = () => {
      // 先尝试查找input元素
      let input = el.querySelector('input');
      
      // 如果没有找到，可能是因为组件还没有完全渲染
      // 尝试查找ant-design的输入框
      if (!input) {
        input = el.querySelector('.ant-select-selector input');
      }
      
      return input;
    };

    // 初始尝试查找输入元素
    let input = findInput();
    
    // 如果没有找到，设置一个MutationObserver来监听DOM变化
    if (!input) {
      const observer = new MutationObserver(() => {
        input = findInput();
        if (input) {
          setupListener();
          observer.disconnect();
        }
      });
      
      observer.observe(el, { childList: true, subtree: true });
    } else {
      setupListener();
    }

    // 设置事件监听器
    function setupListener() {
      if (!input) return;
      
      console.log('enterKey directive: Setting up keydown listener on input element');
      
      input.addEventListener('keydown', (e: KeyboardEvent) => {
        if (e.key === 'Enter') {
          // 检查下拉菜单是否打开
          const dropdown = document.querySelector('.ant-select-dropdown:not(.ant-select-dropdown-hidden)');
          if (dropdown) {
            console.log('enterKey directive: Dropdown is open, not handling enter key');
            return;
          }
          
          console.log('enterKey directive: Enter key pressed, calling handler');
          e.preventDefault();
          e.stopPropagation();
          handler(e);
        }
      });
    }
  }
};

export default enterKey;
