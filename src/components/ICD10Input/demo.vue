<template>
  <div class="icd10-input-demo">
    <h2>ICD10诊断输入组件示例</h2>

    <div class="demo-section">
      <h3>基本使用（自由文本输入为主）</h3>
      <div class="demo-item">
        <ICD10Input v-model="singleValue" @change="handleSingleChange" />
        <div class="result">
          <p>输入/选择的值: {{ singleValue }}</p>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h3>多选模式</h3>
      <div class="demo-item">
        <ICD10Input v-model="multipleValue" multiple @change="handleMultipleChange" />
        <div class="result">
          <p>已选择的值: {{ multipleValue }}</p>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h3>带初始值</h3>
      <div class="demo-item">
        <ICD10Input
          v-model="initialValue"
          @change="handleInitialChange"
        />
        <div class="result">
          <p>输入/选择的值: {{ initialValue }}</p>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h3>使用说明</h3>
      <div class="instructions">
        <p><strong>主要功能：</strong>自由文本输入，辅以搜索建议</p>
        <p><strong>使用方法：</strong></p>
        <ul>
          <li>直接输入任意文本作为诊断信息</li>
          <li>输入时会自动搜索匹配的ICD10编码，可以从下拉列表中选择</li>
          <li>多选模式下，选择后会以标签形式显示，可以继续输入</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import ICD10Input from './index.vue';

// 单选值
const singleValue = ref('');
const handleSingleChange = (value) => {
  console.log('单选值变化:', value);
};

// 多选值
const multipleValue = ref([]);
const handleMultipleChange = (value) => {
  console.log('多选值变化:', value);
};

// 带初始值
const initialValue = ref('高血压');
const handleInitialChange = (value) => {
  console.log('初始值变化:', value);
};
</script>

<style scoped>
.icd10-input-demo {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 30px;
  border: 1px solid #eee;
  padding: 20px;
  border-radius: 4px;
}

.demo-item {
  margin-bottom: 15px;
}

.result {
  margin-top: 10px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.instructions {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid #1890ff;
}

.instructions ul {
  padding-left: 20px;
  margin: 10px 0;
}

h2 {
  margin-bottom: 20px;
  color: #1890ff;
}

h3 {
  margin-bottom: 15px;
  color: #333;
}
</style>
