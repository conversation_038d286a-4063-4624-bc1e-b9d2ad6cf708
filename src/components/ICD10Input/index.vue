<template>
  <div class="icd10-input-container" ref="containerRef">
    <a-auto-complete
      v-model:value="selectedValue"
      :placeholder="placeholder"
      :style="{ width: '100%' }"
      :options="options"
      :dropdown-match-select-width="dropdownMatchSelectWidth"
      @search="handleSearch"
      @select="handleSelect"
      @change="handleInputChange"
      @popup-scroll="handlePopupScroll"
      :autofocus="true"
      :allow-clear="true"
      :bordered="false"
    />

    <!-- 多选模式下的标签展示 -->
    <div v-if="multiple && selectedTags.length > 0" class="selected-tags">
      <a-tag v-for="tag in selectedTags" :key="tag.value" :closable="true" @close="removeTag(tag)" class="tag-item">
        {{ tag.label }}
      </a-tag>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { defineEmits, defineProps, onMounted, ref, watch } from 'vue';
  import { getICD10Detail, searchICD10 } from '/@/api/icd';
  import lodash from 'lodash';

  const props = defineProps({
    modelValue: {
      type: [String, Number, Object, Array],
      default: undefined,
    },
    placeholder: {
      type: String,
      default: '请输入ICD10诊断信息',
    },
    noMatchText: {
      type: String,
      default: '未找到匹配结果',
    },
    fieldMapping: {
      type: Object,
      default: () => ({
        value: 'name',
        text: 'name',
      }),
    },
    pageSize: {
      type: Number,
      default: 20,
    },
    params: {
      type: Object,
      default: () => ({}),
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    dropdownMatchSelectWidth: {
      type: Boolean,
      default: true,
    },
  });

  const emit = defineEmits(['update:modelValue', 'change', 'create']);

  // 容器引用
  const containerRef = ref<HTMLElement | null>(null);

  const selectedValue = ref('');
  const options = ref<any[]>([]);
  const loading = ref<boolean>(false);
  const loadingMore = ref<boolean>(false);
  const searchValue = ref('');
  const selectedTags = ref<any[]>([]);
  const hasMore = ref<boolean>(true);
  let currentSearchValue = '';
  let pageNo = 1;
  let totalCount = 0;

  // 初始化值
  if (props.modelValue) {
    if (props.multiple && Array.isArray(props.modelValue)) {
      // 多选模式下，初始化标签列表
      selectedTags.value = props.modelValue.map((value) => ({
        value,
        label: value,
      }));
    } else {
      // 单选模式下，直接设置输入值
      selectedValue.value = props.modelValue;
    }
  }

  // 监听外部modelValue变化
  watch(
    () => props.modelValue,
    (val) => {
      if (props.multiple && Array.isArray(val)) {
        // 多选模式下，更新标签列表
        selectedTags.value = val.map((value) => ({
          value,
          label: value,
        }));
        // 清空输入框
        selectedValue.value = '';
      } else if (val !== selectedValue.value) {
        // 单选模式下，更新输入值
        selectedValue.value = val;
      }
    }
  );

  // 监听标签列表变化，更新modelValue
  watch(
    () => selectedTags.value,
    (tags) => {
      if (props.multiple) {
        const values = tags.map((tag) => tag.value);
        emit('update:modelValue', values);
      }
    }
  );

  // 处理搜索
  const handleSearch = (value: string) => {
    currentSearchValue = value;
    searchValue.value = value;

    // 如果搜索值为空，清空选项
    if (!value) {
      options.value = [];
      hasMore.value = true;
      return;
    }

    // 如果是用户主动输入（搜索值不为空），才执行搜索并显示下拉菜单
    console.log('ICD10Input: User input detected, searching for:', value);

    // 重置总数
    totalCount = 0;

    // 设置加载状态
    loading.value = true;

    // 防抖处理，300ms后执行搜索
    debouncedFetchOptions(value);
  };

  // 处理下拉列表滚动事件
  const handlePopupScroll = (e: UIEvent) => {
    // 如果没有更多数据或者正在加载，则不处理
    if (!hasMore.value || loadingMore.value || loading.value || !currentSearchValue) return;

    const { target } = e;
    if (target) {
      const { scrollTop, scrollHeight, clientHeight } = target as HTMLElement;
      // 当滚动到底部时，加载更多数据
      if (scrollTop + clientHeight >= scrollHeight - 20) {
        console.log('加载更多数据...');
        // 加载更多数据
        fetchOptions(currentSearchValue, true);
      }
    }
  };

  // 获取远程数据
  const fetchOptions = async (keyword: string, isLoadMore = false) => {
    if (!isLoadMore) {
      pageNo = 1; // 重置页码
      hasMore.value = true;
      totalCount = 0; // 重置总数
    }

    try {
      const params = {
        keyword,
        pageSize: props.pageSize,
        pageNo,
        ...props.params,
      };

      if (isLoadMore) {
        loadingMore.value = true;
        console.log(`加载更多数据: 页码=${pageNo}, 关键词=${keyword}`);
      } else {
        loading.value = true;
      }

      const res = await searchICD10(params);

      // 如果当前搜索值已经改变，不更新结果
      if (currentSearchValue !== keyword) return;

      // 获取记录数据
      const records = res.records || [];
      const total = res.total || 0;
      const pages = res.pages || 1;
      const currentPage = res.current || 1;

      // 转换字段结构
      const newOptions = records.map((item: any) => ({
        value: item[props.fieldMapping.value],
        label: item[props.fieldMapping.text],
        // 保存原始数据，以便在需要时使用
        raw: item,
      }));

      // 判断是否还有更多数据
      if (currentPage >= pages || records.length === 0) {
        hasMore.value = false;
        if (isLoadMore) {
          console.log(`已加载全部数据，共${total}条`);
        }
      } else {
        hasMore.value = true;
      }

      if (isLoadMore) {
        // 追加新的选项
        options.value = [...options.value, ...newOptions];
      } else {
        // 替换选项
        options.value = newOptions;
      }

      // 更新总数和页码
      if (records.length) {
        if (isLoadMore) {
          totalCount += records.length;
        } else {
          totalCount = records.length;
        }

        if (hasMore.value) {
          pageNo += 1;
        }
      }

      // 记录总数
      totalCount = total;
    } catch (error) {
      console.error('ICD10数据加载失败:', error);
      if (!isLoadMore) {
        options.value = [];
      }
    } finally {
      if (isLoadMore) {
        loadingMore.value = false;
      } else {
        loading.value = false;
      }
    }
  };

  const debouncedFetchOptions = lodash.debounce(fetchOptions, 300);

  // 处理选择事件
  const handleSelect = (value: string, option: any) => {
    if (props.multiple) {
      // 多选模式下，添加到标签列表
      const newTag = {
        value: option.value,
        label: option.label || value,
      };

      // 检查是否已存在
      const exists = selectedTags.value.some((tag) => tag.value === newTag.value);
      if (!exists) {
        selectedTags.value = [...selectedTags.value, newTag];
      }

      // 清空输入框
      selectedValue.value = '';
    } else {
      // 单选模式下，更新值并触发事件
      selectedValue.value = value;
      emit('update:modelValue', value);
      emit('change', value);
    }
  };

  // 处理输入变化
  const handleInputChange = (value: string) => {
    if (!props.multiple) {
      // 单选模式下，直接更新modelValue
      emit('update:modelValue', value);
      emit('change', value);

      // 如果值被清空，确保输入框保持聚焦
      if (value === '') {
        // 使用微任务确保在DOM更新后执行
        setTimeout(() => {
          const input = containerRef.value?.querySelector('input');
          if (input) {
            console.log('ICD10Input: Refocusing input after content cleared');
            input.focus();
          }
        }, 0);
      }
    }
  };

  // 移除标签
  const removeTag = (tag: any) => {
    selectedTags.value = selectedTags.value.filter((item) => item.value !== tag.value);
  };

  // 初始化已选值的标签
  const initSelectedLabel = async () => {
    if (!props.modelValue) return;

    try {
      // 处理不同类型的值
      if (props.multiple && Array.isArray(props.modelValue)) {
        // 多选模式
        const values = props.modelValue;
        if (values.length === 0) return;

        // 获取每个值的详情
        const detailsResponses = await Promise.all(values.map((value) => getICD10Detail(value)));

        // 构建选项
        options.value = detailsResponses.map((response, index) => {
          // 检查响应是否成功并包含记录
          if (response?.success && response.result?.records && response.result.records.length > 0) {
            const item = response.result.records[0];
            return {
              value: item[props.fieldMapping.value],
              label: item[props.fieldMapping.text],
              raw: item,
            };
          } else {
            // 如果没有找到详情，使用原始值
            return {
              value: values[index],
              label: values[index],
              isCustom: true,
            };
          }
        });
      } else {
        // 单选模式
        const value = props.modelValue;
        const response = await getICD10Detail(value);

        if (response?.success && response.result?.records && response.result.records.length > 0) {
          const item = response.result.records[0];
          options.value = [
            {
              value: item[props.fieldMapping.value],
              label: item[props.fieldMapping.text],
              raw: item,
            },
          ];
        } else {
          // 如果没有找到详情，使用原始值
          options.value = [
            {
              value,
              label: value,
              isCustom: true,
            },
          ];
        }
      }
    } catch (error) {
      console.error('初始化选项失败:', error);
    }
  };

  // 组件挂载时，如果有初始值，初始化选项
  onMounted(() => {
    if (props.modelValue) {
      //initSelectedLabel();
    }
  });
</script>

<style scoped>
  .icd10-input-container {
    width: 100%;
  }

  .selected-tags {
    margin-top: 8px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .tag-item {
    margin-right: 0;
    display: inline-flex;
    align-items: center;
  }
</style>
