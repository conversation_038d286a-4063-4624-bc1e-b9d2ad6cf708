<template>
  <div class="diagnosis-readonly-viewer">
    <div class="readonly-content">
      <div v-if="diagnosisList.length === 0" class="readonly-empty">
        <a-empty description="暂无诊断信息" />
      </div>
      <div v-else class="readonly-list">
        <div v-for="(diagnosis, index) in diagnosisList" :key="index" class="readonly-item">
          <div class="readonly-item-number">{{ index + 1 }}.</div>
          <div class="readonly-item-content">
            <div class="readonly-text">{{ diagnosis || '暂无内容' }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed } from 'vue';

  const props = defineProps({
    modelValue: {
      type: Array as () => string[],
      default: () => [],
    },
  });

  // 计算诊断列表
  const diagnosisList = computed(() => {
    return props.modelValue || [];
  });
</script>

<style scoped>
  .diagnosis-readonly-viewer {
    background: #fff;
    border-radius: 6px;
    overflow: hidden;
  }

  .readonly-header {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;
  }

  .readonly-title {
    margin: 0;
    font-size: 14px;
    font-weight: 500;
    color: #262626;
  }

  .readonly-content {
    padding: 16px;
  }

  .readonly-empty {
    text-align: center;
    padding: 40px 0;
  }

  .readonly-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .readonly-item {
    display: flex;
    gap: 12px;
    padding: 12px;
    background: #fafafa;
    border-radius: 6px;
    border-left: 3px solid #52c41a;
  }

  .readonly-item-number {
    font-weight: 500;
    color: #52c41a;
    min-width: 20px;
    flex-shrink: 0;
  }

  .readonly-item-content {
    flex: 1;
  }

  .readonly-text {
    font-size: 14px;
    line-height: 1.6;
    color: #262626;
  }
</style>
