<template>
  <a-cascader
    :options="options"
    @change="onChange"
    :loadData="loadData"
    :placeholder="placeholder"
    :fieldNames="{ label: 'name', value: 'code', children: 'children' }"
    :value="value"
    :key="type"
    :changeOnSelect="false"
    :disabled="disabled"
  />
</template>

<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import { defHttp } from '/@/utils/http/axios';
  import { useMessage } from '/@/hooks/web/useMessage';
  const emit = defineEmits(['change']);

  const props = defineProps<{
    placeholder?: string;
    value?: Array<any>;
    level?: number;
    disabled?: boolean;
  }>();

  const { createMessage } = useMessage();
  const options = ref<Array<any>>([]);
  const type = ref(true);

  const init = () => {
    defHttp.get({ url: '/basicinfo/pcca/queryByPid', params: { pid: '0' } }, { isTransformResponse: false }).then((res) => {
      if (res.success) {
        res.result.forEach((item: any) => {
          item.isLeaf = item.hasChild == '0' || props.level == 1;
        });
        options.value = res.result;
      } else {
        createMessage.error(res.message);
      }
    });
  };

  const onChange = (value: any, selectedOptions: any) => {
    console.log('pcasv onChange', selectedOptions);
    emit('change', selectedOptions);
  };

  const loadData = (selectedOptions: any) => {
    const targetOption = selectedOptions[selectedOptions.length - 1];
    if (selectedOptions.length >= props.level!) {
      return;
    }

    targetOption.loading = true;
    console.log('targetOption', targetOption);

    const pid = targetOption.id;
    defHttp
      .get({ url: '/basicinfo/pcca/queryByPid', params: { pid: pid } }, { isTransformResponse: false })
      .then((res) => {
        if (res.success) {
          res.result.forEach((item: any) => {
            item.isLeaf = item.hasChild == '0' || selectedOptions.length == props.level! - 1;
          });
          targetOption.children = res.result;
          options.value = [...options.value];
        } else {
          createMessage.error(res.message);
        }
      })
      .finally(() => {
        targetOption.loading = false;
      });
  };

  onMounted(() => {
    init();
  });
</script>
