<template>
  <a-modal v-model:open="visible" :footer="null" :closable="false" :maskClosable="true" :keyboard="true">
    <div style="min-height: 30vh; padding: 10px">
      <a-card style="margin-bottom: 5px" bordered v-for="pay in state.payResults">
        <div :key="pay.channel" style="width: 100%; padding: 20px; display: flex; justify-content: space-between; align-items: center">
          <!-- 左侧文案 -->
          <div style="display: flex; align-items: center">
            <span class="typography-title typography-title-level-3"> {{ pay.channel }} </span>
            <span class="typography-title typography-title-level-3 typography-title-strong" style="margin-left: 20px"> ￥{{ pay.amount }} </span>
          </div>

          <div style="display: flex; justify-content: flex-end; align-items: center">
            <span
              class="typography-title typography-title-level-4"
              :class="pay.status === '支付成功' ? 'typography-title-success' : 'typography-title-danger'"
            >
              {{ pay.message }}
            </span>
            <!-- 右侧图标 -->
            <CheckCircleTwoTone two-tone-color="#52c41a" v-if="pay.status === '支付成功'" style="font-size: 42px" />
            <ExclamationCircleTwoTone two-tone-color="#f5222d" v-else style="font-size: 42px" />
          </div>
        </div>
      </a-card>
      <!--      <a-result :status="state.status" :title="state.title">
        <template #subTitle>
          <a-typography-text :type="state.status === 'error' ? 'danger' : ''" strong>{{ state.subTitle }}</a-typography-text>
        </template>

        <template #extra>
          <slot name="extra"> </slot>
        </template>
      </a-result>-->
    </div>
  </a-modal>
</template>

<script setup>
  import { onBeforeUnmount, reactive, ref } from 'vue';
  import { CheckCircleTwoTone, ExclamationCircleTwoTone } from '@ant-design/icons-vue';

  const state = reactive({
    visible: false,
    timer: null,
    delay: 0,
    defaultDelay: 5,
    payResult: [],
  });

  const visible = ref(false);
  const timer = ref(null);

  const show = (
    options = {
      payResults: [],
      delay: 3,
    }
  ) => {
    Object.assign(state, options);
    visible.value = true;
    startTimer();
  };

  const hide = () => {
    visible.value = false;
    clearTimer();
  };

  const startTimer = () => {
    const delaySeconds = state.delay ?? state.defaultDelay;
    if (delaySeconds > 0) {
      timer.value = setTimeout(() => {
        hide();
      }, delaySeconds * 1000);
    }
  };

  const clearTimer = () => {
    if (timer.value) {
      clearTimeout(timer.value);
      timer.value = null;
    }
  };

  onBeforeUnmount(() => {
    clearTimer();
  });

  defineExpose({
    show,
    hide,
  });
</script>
<style scoped>
  /* 基础标题样式模拟（原 <a-typography-title>） */
  .typography-title {
    margin: 0;
    display: inline-block;
    /* 你可以根据需要做一些默认的行高、间距等 */
    line-height: 1.3;
    margin-right: 10px; /* 让两段文字之间留出一点空隙 */
  }

  /* level-x 模拟 antd Typography.Title 里的字号差异：
   - level=1 ~ 4 的字号只是参考，实际数值可按项目需求微调
*/
  .typography-title-level-1 {
    font-size: 38px;
  }
  .typography-title-level-2 {
    font-size: 30px;
  }
  .typography-title-level-3 {
    font-size: 24px;
  }
  .typography-title-level-4 {
    font-size: 20px;
  }

  /* strong 模拟加粗 */
  .typography-title-strong {
    font-weight: 600;
  }

  /* type="success"/"danger" 等对应的颜色 */
  .typography-title-success {
    color: #52c41a;
  }
  .typography-title-danger {
    color: #f5222d;
  }
</style>
