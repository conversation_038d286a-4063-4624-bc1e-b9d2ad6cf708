<template>
  <a-modal
    v-model:open="visible"
    title="依赖项目快捷添加"
    width="700px"
    @ok="handleConfirm"
    @cancel="handleCancel"
    :ok-text="confirmText"
    :cancel-text="cancelText"
    :confirm-loading="loading"
  >
    <div class="dependency-quick-add-content">
      <a-alert
        message="检测到依赖项目缺失"
        :description="alertDescription"
        type="warning"
        show-icon
        style="margin-bottom: 20px"
      />

      <div class="missing-projects-section">
        <h4 style="margin-bottom: 12px;">
          <ExclamationCircleOutlined style="color: #faad14; margin-right: 8px;" />
          缺失的依赖项目
        </h4>
        
        <a-list :data-source="missingProjects" size="small" class="missing-projects-list">
          <template #renderItem="{ item }">
            <a-list-item class="missing-project-item">
              <a-list-item-meta>
                <template #title>
                  <div class="project-info">
                    <FolderOutlined style="color: #1890ff; margin-right: 8px;" />
                    <span class="project-name">{{ item.name }}</span>
                    <a-tag color="blue" size="small" style="margin-left: 8px;">大项</a-tag>
                  </div>
                </template>
                <template #description>
                  <span class="project-id">项目ID: {{ item.id }}</span>
                </template>
              </a-list-item-meta>

            </a-list-item>
          </template>
        </a-list>
      </div>

      <div class="actions-section" style="margin-top: 24px;">
        <a-space>
          <a-button
            type="primary"
            @click="handleQuickAddAll"
            :loading="loading"
            :disabled="missingProjects.length === 0"
            title="自动查找并添加所有缺失的依赖项目"
          >
            <PlusOutlined />
            一键添加所有依赖项目
          </a-button>
        </a-space>
      </div>

      <div class="help-section" style="margin-top: 20px;">
        <a-alert
          message="操作说明"
          description="• 一键添加：自动查找并添加所有缺失的依赖项目（推荐）
• 忽略并继续：跳过依赖检查，直接完成当前操作"
          type="info"
          show-icon
        />
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import {
  ExclamationCircleOutlined,
  FolderOutlined,
  PlusOutlined
} from '@ant-design/icons-vue';

const visible = ref(false);
const missingProjects = ref([]);
const loading = ref(false);
const originalItems = ref([]);

const emit = defineEmits(['quickAdd', 'confirm', 'cancel']);

// 计算属性
const alertDescription = computed(() => {
  const count = missingProjects.value.length;
  return `以下 ${count} 个依赖项目尚未添加到项目列表中，建议先添加这些依赖项目以确保检查流程完整。`;
});

const confirmText = computed(() => {
  return missingProjects.value.length > 0 ? '忽略并继续' : '确定';
});

const cancelText = computed(() => {
  return '取消';
});

/**
 * 打开弹窗
 * @param {Array} dependencies 缺失的依赖项目列表
 * @param {Array} items 原始添加的项目列表
 */
function open(dependencies = [], items = []) {
  missingProjects.value = dependencies;
  originalItems.value = items;
  visible.value = true;
  loading.value = false;
}

/**
 * 一键添加所有依赖项目
 */
async function handleQuickAddAll() {
  if (missingProjects.value.length === 0) {
    message.warn('没有需要添加的依赖项目');
    return;
  }

  loading.value = true;
  try {
    emit('quickAdd', {
      dependencies: missingProjects.value,
      originalItems: originalItems.value
    });
    
    // 不在这里关闭弹窗，让父组件处理成功后再关闭
  } catch (error) {
    console.error('快捷添加失败:', error);
    message.error('快捷添加失败，请重试');
    loading.value = false;
  }
}



/**
 * 确认按钮处理（忽略并继续）
 */
function handleConfirm() {
  emit('confirm', {
    dependencies: missingProjects.value,
    originalItems: originalItems.value,
    action: 'ignore'
  });
  close();
}

/**
 * 取消按钮处理
 */
function handleCancel() {
  emit('cancel', {
    dependencies: missingProjects.value,
    originalItems: originalItems.value
  });
  close();
}

/**
 * 关闭弹窗
 */
function close() {
  visible.value = false;
  loading.value = false;
}

/**
 * 设置loading状态
 */
function setLoading(isLoading) {
  loading.value = isLoading;
}

// 暴露方法给父组件
defineExpose({
  open,
  close,
  setLoading
});
</script>

<style scoped>
.dependency-quick-add-content {
  max-height: 500px;
  overflow-y: auto;
}

.missing-projects-section h4 {
  color: #262626;
  font-weight: 600;
}

.missing-projects-list {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background: #fafafa;
}

.missing-project-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.missing-project-item:last-child {
  border-bottom: none;
}

.project-info {
  display: flex;
  align-items: center;
}

.project-name {
  font-weight: 500;
  color: #262626;
}

.project-id {
  color: #8c8c8c;
  font-size: 12px;
}

.actions-section {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.help-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}
</style>
