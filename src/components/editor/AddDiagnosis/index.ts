import { API, BlockAPI, ToolConfig } from '@editorjs/editorjs';
import { IconPlus } from '@codexteam/icons';
import { showPlusButton } from '../utils/showPlusButton';

interface AddDiagnosisParams {
  api: API;
  block: BlockAPI;
  readOnly: boolean;
  config?: ToolConfig;
}

class AddDiagnosis {
  private api: API;
  private block: BlockAPI;
  private readOnly: boolean;

  static get toolbox() {
    return {
      icon: IconPlus,
      title: '添加诊断',
    };
  }

  constructor({ api, block, readOnly }: AddDiagnosisParams) {
    this.api = api;
    this.block = block;
    this.readOnly = readOnly;

    // 确保加号按钮可见
    showPlusButton();
  }

  render() {
    // 在只读模式下，不渲染任何内容
    if (this.readOnly) {
      return null;
    }

    // 创建一个空节点
    const element = document.createElement('div');

    // 非只读模式下，延迟到下一个宏任务，确保 Block 插入完毕后再发事件 & 删除
    setTimeout(() => {
      // 1. 拿到正确的 EventBus（兼容新版和旧版）
      const bus = (this.api.events as any) || (this.api.listeners as any);

      // 2. 发射事件
      if (typeof bus.emit === 'function') {
        bus.emit('add:diagnosis');
      } else if (typeof bus.dispatch === 'function') {
        bus.dispatch('add:diagnosis');
      }

      // 3. 删除自己
      const index = this.api.blocks.getCurrentBlockIndex();
      this.api.blocks.delete(index);
    }, 0);

    return element;
  }

  save() {
    // 没有数据需要保存
    return {};
  }

  /**
   * 指示该工具支持只读模式
   */
  static get isReadOnlySupported(): boolean {
    return true;
  }
}

export default AddDiagnosis;
