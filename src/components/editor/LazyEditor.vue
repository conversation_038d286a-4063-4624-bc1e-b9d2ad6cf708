<template>
  <component :is="Editor" v-bind="$attrs" v-model="modelValue" />
</template>

<script lang="ts" setup>
  import { defineAsyncComponent, toRefs } from 'vue';
  // 默认按 Tinymce 懒加载，如需切换 Quill，可替换实现
  const Editor = defineAsyncComponent(async () => {
    const mod = await import('/@/components/Tinymce/src/Editor.vue');
    return mod.default;
  });
  const props = defineProps<{ modelValue?: string }>();
  const emit = defineEmits(['update:modelValue']);
  const { modelValue } = toRefs(props);
</script>


