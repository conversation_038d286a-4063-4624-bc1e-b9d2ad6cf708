/**
 * 显示 EditorJS 加号按钮的工具函数
 * 移除全局样式表并恢复加号按钮的显示
 */
export const showPlusButton = () => {
  try {
    // 1. 移除全局样式表
    const styleId = 'hide-plus-button-style';
    const styleElement = document.getElementById(styleId);
    if (styleElement) {
      styleElement.remove();
      console.log('已移除隐藏加号按钮的全局样式表');
    }

    // 2. 恢复所有加号按钮的样式
    const plusButtons = document.querySelectorAll('.ce-toolbar__plus');
    if (plusButtons && plusButtons.length > 0) {
      console.log('找到加号按钮，正在恢复显示...', plusButtons.length);
      plusButtons.forEach((button) => {
        // 重置样式
        button.removeAttribute('style');
      });
    }

    return true;
  } catch (error) {
    console.error('恢复加号按钮显示失败:', error);
    return false;
  }
};
