import './index.css';
import { IconText } from '@codexteam/icons';
import makeFragment from './utils/makeFragment';

import type { API, ConversionConfig, HTMLPasteEvent, PasteConfig, SanitizerConfig, ToolboxConfig, ToolConfig } from '@editorjs/editorjs';
import { ParagraphExtra, EditorJSParagraphData } from '#/types';

export interface ParagraphConfig extends ToolConfig {
  placeholder?: string;
  preserveBlank?: boolean;
  defaultExtraType?: '异常汇总' | '总检建议';
  defaultAbnormalType?: '诊断' | '异常';
}

// Use the interface from types.d.ts
export type ParagraphData = EditorJSParagraphData;

interface ParagraphParams {
  data: ParagraphData;
  config: ParagraphConfig;
  api: API;
  readOnly: boolean;
}

interface ParagraphCSS {
  block: string;
  wrapper: string;
}

export default class Paragraph {
  static get DEFAULT_PLACEHOLDER() {
    return '';
  }

  // 默认的 extra 设置
  private static _defaultExtra: ParagraphExtra = {
    type: '总检建议',
    abnormalType: '诊断',
    abnormalItem: [],
  };

  // 设置默认的 extra 属性 - 供外部调用
  static setDefaultExtra(extra: ParagraphExtra) {
    this._defaultExtra = extra;
  }

  // 获取默认的 extra 属性
  static getDefaultExtra(): ParagraphExtra {
    return this._defaultExtra;
  }

  api: API;
  readOnly: boolean;
  private config: any;
  private _CSS: ParagraphCSS;
  private _placeholder: string;
  private _data: ParagraphData;
  private _element: HTMLElement | null;
  private _preserveBlank: boolean;

  // 新增：用于挂载Vue实例
  private _vueApp: any = null;
  private _vueTagMount: HTMLSpanElement | null = null;
  private _wrapper: HTMLDivElement | null = null;

  constructor({ data, config, api, readOnly }: ParagraphParams) {
    this.api = api;
    this.readOnly = readOnly;
    this.config = config;

    this._CSS = {
      block: this.api.styles.block,
      wrapper: 'ce-paragraph',
    };

    if (!this.readOnly) {
      this.onKeyUp = this.onKeyUp.bind(this);
    }

    this._placeholder = config.placeholder ? config.placeholder : Paragraph.DEFAULT_PLACEHOLDER;

    // 初始化数据，应用默认的 extra 值
    this._data = data || { text: '' };

    // 如果配置中指定了默认的 extra 类型，优先使用配置值
    if (!this._data.extra) {
      this._data.extra = { ...Paragraph._defaultExtra };

      // 如果配置中指定了默认值，则覆盖静态默认值
      if (config.defaultExtraType) {
        this._data.extra.type = config.defaultExtraType;
      }
      if (config.defaultAbnormalType) {
        this._data.extra.abnormalType = config.defaultAbnormalType;
      }
    }

    this._element = null;
    this._preserveBlank = config.preserveBlank ?? false;
  }

  onKeyUp(e: KeyboardEvent): void {
    if (e.code !== 'Backspace' && e.code !== 'Delete') {
      return;
    }
    if (!this._element) {
      return;
    }
    const { textContent } = this._element;
    if (textContent === '') {
      this._element.innerHTML = '';
    }
  }

  /**
   * 创建带有状态标签的视图
   */
  drawView(): HTMLDivElement {
    // 1. 段落内容
    const div = document.createElement('DIV') as HTMLElement;
    div.classList.add(this._CSS.wrapper, this._CSS.block);
    div.contentEditable = 'false';
    div.dataset.placeholderActive = this.api.i18n.t(this._placeholder);
    if (this._data.text) {
      div.innerHTML = this._data.text;
    }

    // 根据extra属性设置文字颜色
    const { extra } = this._data;
    if (extra && extra.type === '异常汇总' && extra.abnormalType === '诊断') {
      div.style.color = '#1890ff'; // 医疗蓝色
    }

    if (!this.readOnly) {
      div.contentEditable = 'true';
      div.addEventListener('keyup', this.onKeyUp);
    }

    // 2. 状态标签挂载点
    const vueTagMount = document.createElement('span');
    vueTagMount.className = 'vue-tag-mount';

    // 3. 外层容器
    const wrapper = document.createElement('div');
    wrapper.className = 'custom-paragraph-wrapper';
    wrapper.appendChild(div);
    wrapper.appendChild(vueTagMount);

    // 4. 记录引用
    this._element = div;
    this._vueTagMount = vueTagMount;
    this._wrapper = wrapper;

    return wrapper;
  }

  render(): HTMLDivElement {
    return this.drawView();
  }

  merge(data: ParagraphData): void {
    if (!this._element) {
      return;
    }
    this._data.text += data.text;
    const fragment = makeFragment(data.text);
    this._element.appendChild(fragment);
    this._element.normalize();
  }

  validate(savedData: ParagraphData): boolean {
    if (savedData.text.trim() === '' && !this._preserveBlank) {
      return false;
    }
    return true;
  }

  save(toolsContent: HTMLDivElement): ParagraphData {
    // 获取段落内容（第一个div）
    const paragraphDiv = toolsContent.querySelector('div.ce-paragraph');
    return {
      text: paragraphDiv ? paragraphDiv.innerHTML : '',
      extra: this._data.extra, // 保存extra数据
    };
  }

  onPaste(event: HTMLPasteEvent): void {
    const data = {
      text: event.detail.data.innerHTML,
      extra: this._data.extra, // 保留extra数据
    };
    this._data = data;
    window.requestAnimationFrame(() => {
      if (!this._element) {
        return;
      }
      this._element.innerHTML = this._data.text || '';
    });
  }

  static get conversionConfig(): ConversionConfig {
    return {
      export: 'text',
      import: 'text',
    };
  }

  static get sanitize(): SanitizerConfig {
    return {
      text: {
        br: true,
      },
    };
  }

  static get isReadOnlySupported(): boolean {
    return true;
  }

  static get pasteConfig(): PasteConfig {
    return {
      tags: ['P'],
    };
  }

  static get toolbox(): ToolboxConfig {
    return {
      icon: IconText,
      title: 'Text',
    };
  }
}
