class SelectWordTool {
  static get isInline() {
    return true;
  }
  static get sanitize() {
    return {};
  }

  constructor({ api }) {
    this.api = api;
  }

  render() {
    const btn = document.createElement('button');
    btn.type = 'button';
    btn.className = 'ce-inline-tool';
    //btn.title = '🔍 选择查询建议';
    btn.innerHTML = '🔍';

    // 阻止 Editor.js 自己的 mousedown 行为
    btn.addEventListener('mousedown', (e) => e.preventDefault());

    btn.addEventListener('click', (e) => {
      e.preventDefault();

      const selectedText = window.getSelection()?.toString().trim();
      if (!selectedText) {
        this.api.inlineToolbar.close();
        return;
      }

      // 1. 拿到正确的 EventBus
      const bus = this.api.events || this.api.listeners;

      // 2. 优先 emit，否则 dispatch
      if (bus.emit) {
        bus.emit('word:selected', selectedText);
      } else if (bus.dispatch) {
        bus.dispatch('word:selected', selectedText);
      }

      // 3. 关掉工具栏
      this.api.inlineToolbar.close();
    });

    return btn;
  }

  surround() {} // 不包裹任何标签
  checkState() {
    // 不高亮
    return false;
  }
}

export default SelectWordTool;
