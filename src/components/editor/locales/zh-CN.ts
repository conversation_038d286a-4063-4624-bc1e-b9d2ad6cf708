export default {
  /**
   * @type {I18nDictionary}
   */
  messages: {
    /**
     * Other below: translation of different UI components of the editor.js core
     */
    ui: {
      blockTunes: {
        toggler: {
          'Click to tune': '点击调整',
          'or drag to move': '拖拽排序',
        },
      },
      inlineToolbar: {
        converter: {
          'Convert to': '转换为',
        },
      },
      toolbar: {
        toolbox: {
          Add: '添加',
        },
      },
    },

    /**
     * Section for translation Tool Names: both block and inline tools
     */
    toolNames: {
      Text: '文本',
      Heading: '标题',
      List: '列表',
      Warning: '警告',
      Checklist: '任务列表',
      Quote: '引用',
      Code: '代码',
      Delimiter: '分隔符',
      'Raw HTML': '原始HTML',
      Table: '表格',
      Link: '链接',
      Marker: '高亮',
      Bold: '加粗',
      Italic: '斜体',
      InlineCode: '内联',
    },

    /**
     * Section for passing translations to the external tools classes
     */
    tools: {
      /**
       * Each subsection is the i18n dictionary that will be passed to the corresponded plugin
       * The name of a plugin should be equal the name you specify in the 'tool' section for that plugin
       */
      warning: {
        // <-- 'Warning' tool will accept this dictionary section
        Title: '标题',
        Message: '消息',
      },

      /**
       * Link is the internal Inline Tool
       */
      link: {
        'Add a link': '添加链接',
      },
      /**
       * The "stub" is an internal block tool, used to fit blocks that does not have the corresponded plugin
       */
      stub: {
        'The block can not be displayed correctly.': '不能正常显示该块。',
      },
    },

    /**
     * Section allows to translate Block Tunes
     */
    blockTunes: {
      /**
       * Each subsection is the i18n dictionary that will be passed to the corresponded Block Tune plugin
       * The name of a plugin should be equal the name you specify in the 'tunes' section for that plugin
       *
       * Also, there are few internal block tunes: "delete", "moveUp" and "moveDown"
       */
      delete: {
        Delete: '删除',
        'Click to delete': '点击删除',
      },
      moveUp: {
        'Move up': '上移',
        'Click to move up': '点击上移',
      },
      moveDown: {
        'Move down': '下移',
        'Click to move down': '点击下移',
      },
    },
  },
};
