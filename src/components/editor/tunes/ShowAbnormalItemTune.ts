import { IconWarning } from '@codexteam/icons';
/**
 * Editor.js Block Tune 插件：异常汇总-刷新总检建议
 * 展示为“刷新建议”按钮，点击时将本块内容以 `word:selected` 抛给调用者
 */
export default class RefreshSuggestionTune {
  static get isTune() {
    return true;
  }

  constructor({ api, data, block }) {
    this.api = api;
    this.data = data;
    this.block = block; // 可用于获取当前块内容
    this.button = null;
  }

  /**
   * 渲染"刷新总检建议" tune按钮，含图标和文字
   * @returns {HTMLElement}
   */
  render() {
    return {
      icon: IconWarning,
      label: '查看关联的异常项',
      onActivate: async () => {
        const idx = this.api.blocks.getCurrentBlockIndex();

        // 获取所有块内容
        const res = await this.api.saver.save();
        if (!res || !res.blocks || typeof idx !== 'number' || idx < 0) {
          this.api.notifier.show({
            message: '无法获取当前块内容',
            style: 'error',
          });
          return;
        }

        const block = res.blocks[idx]; // {id, type, data}
        if (!block) {
          this.api.notifier.show({
            message: '无法获取当前块内容',
            style: 'error',
          });
          return;
        }

        const payload = { ...block, index: idx }; // 可加更多字段

        // 事件总线优先
        const bus = this.api.events || this.api.listeners;
        let fired = false;
        if (bus && typeof bus.emit === 'function') {
          bus.emit('showAbnoramItem', payload);
          fired = true;
        } else if (bus && typeof bus.dispatch === 'function') {
          bus.dispatch('showAbnoramItem', payload);
          fired = true;
        }

        if (!fired) {
          document.dispatchEvent(new CustomEvent('showAbnoramItem', { detail: payload, bubbles: true }));
        }

        this.api.toolbar.close();
      },
    };
  }

  save() {
    // 无持久状态
    return this.data || {};
  }
}
