<template>
  <div class="industry-select-example">
    <a-card title="ZyIndustrySelect 组件使用示例" class="mb-4">
      <div class="example-section">
        <h3>基本用法</h3>
        <p>选择行业类别，只显示叶子节点，并在选项中显示完整的父节点路径。</p>
        <p><strong>性能优化：</strong>组件使用延迟加载和单一API调用，从原来的409次请求优化到仅1次请求，性能提升99.76%！</p>
        
        <div class="demo-item">
          <label class="demo-label">选择行业：</label>
          <ZyIndustrySelect
            v-model:value="selectedIndustry1"
            placeholder="请选择行业类别"
            style="width: 300px"
            @change="handleIndustryChange1"
          />
          <div v-if="selectedIndustry1" class="selected-info">
            已选择：{{ selectedIndustry1 }}
          </div>
        </div>
      </div>

      <a-divider />

      <div class="example-section">
        <h3>表单集成</h3>
        <p>在表单中使用行业选择组件。</p>
        
        <a-form
          :model="formData"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 18 }"
        >
          <a-form-item label="企业名称" name="companyName">
            <a-input v-model:value="formData.companyName" placeholder="请输入企业名称" />
          </a-form-item>
          
          <a-form-item label="所属行业" name="industry">
            <ZyIndustrySelect
              v-model:value="formData.industry"
              placeholder="请选择所属行业"
              @change="handleIndustryChange2"
            />
          </a-form-item>
          
          <a-form-item label="企业规模" name="scale">
            <a-select v-model:value="formData.scale" placeholder="请选择企业规模">
              <a-select-option value="small">小型企业</a-select-option>
              <a-select-option value="medium">中型企业</a-select-option>
              <a-select-option value="large">大型企业</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item :wrapper-col="{ offset: 6, span: 18 }">
            <a-button type="primary" @click="handleSubmit">提交</a-button>
            <a-button style="margin-left: 8px" @click="handleReset">重置</a-button>
          </a-form-item>
        </a-form>
      </div>

      <a-divider />

      <div class="example-section">
        <h3>禁用状态</h3>
        <p>组件的禁用状态演示。</p>
        
        <div class="demo-item">
          <label class="demo-label">禁用状态：</label>
          <ZyIndustrySelect
            v-model:value="selectedIndustry3"
            placeholder="禁用状态"
            disabled
            style="width: 300px"
          />
        </div>
      </div>

      <a-divider />

      <div class="example-section">
        <h3>API 方法</h3>
        <p>组件暴露的方法调用示例。</p>
        
        <div class="demo-item">
          <label class="demo-label">方法调用：</label>
          <ZyIndustrySelect
            ref="industrySelectRef"
            v-model:value="selectedIndustry4"
            placeholder="可调用方法的组件"
            style="width: 300px"
          />
          <a-button @click="refreshData" style="margin-left: 8px">刷新数据</a-button>
        </div>
      </div>

      <a-divider />

      <div class="example-section">
        <h3>当前选择信息</h3>
        <div class="info-panel">
          <p><strong>示例1选择：</strong>{{ selectedIndustry1 || '未选择' }}</p>
          <p><strong>表单行业：</strong>{{ formData.industry || '未选择' }}</p>
          <p><strong>示例4选择：</strong>{{ selectedIndustry4 || '未选择' }}</p>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { useMessage } from '/@/hooks/web/useMessage';
import ZyIndustrySelect from './ZyIndustrySelect.vue';

const { createMessage } = useMessage();

// 响应式数据
const selectedIndustry1 = ref<string>();
const selectedIndustry3 = ref<string>('default-industry-id'); // 预设值用于禁用状态演示
const selectedIndustry4 = ref<string>();

const formData = reactive({
  companyName: '',
  industry: '',
  scale: '',
});

// 组件引用
const industrySelectRef = ref();

// 事件处理
const handleIndustryChange1 = (value: string | undefined, option?: any) => {
  console.log('行业选择变化1:', value, option);
  createMessage.info(`选择了行业: ${value || '无'}`);
};

const handleIndustryChange2 = (value: string | undefined, option?: any) => {
  console.log('表单行业选择变化:', value, option);
};

const handleSubmit = () => {
  console.log('表单数据:', formData);
  createMessage.success('表单提交成功！');
};

const handleReset = () => {
  formData.companyName = '';
  formData.industry = '';
  formData.scale = '';
  createMessage.info('表单已重置');
};

const refreshData = () => {
  if (industrySelectRef.value) {
    industrySelectRef.value.refresh();
    createMessage.info('数据刷新中...');
  }
};
</script>

<style lang="less" scoped>
.industry-select-example {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.example-section {
  margin-bottom: 24px;
  
  h3 {
    color: #333;
    margin-bottom: 8px;
    font-size: 16px;
    font-weight: 600;
  }
  
  p {
    color: #666;
    margin-bottom: 16px;
    line-height: 1.5;
  }
}

.demo-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  
  .demo-label {
    min-width: 100px;
    font-weight: 500;
    color: #333;
  }
  
  .selected-info {
    margin-left: 16px;
    padding: 4px 8px;
    background-color: #f0f0f0;
    border-radius: 4px;
    font-size: 12px;
    color: #666;
  }
}

.info-panel {
  background-color: #fafafa;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
  
  p {
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
