<template>
  <a-select
    v-model:value="selectedValue"
    show-search
    :placeholder="placeholder"
    :loading="loading"
    :filter-option="false"
    :not-found-content="loading ? undefined : '暂无数据'"
    allow-clear
    @search="handleSearch"
    @change="handleChange"
    @clear="handleClear"
    @dropdown-visible-change="handleDropdownVisibleChange"
    v-bind="$attrs"
  >
    <template #notFoundContent>
      <div v-if="loading" class="text-center py-2">
        <a-spin size="small" />
        <span class="ml-2">加载中...</span>
      </div>
      <div v-else class="text-center py-2 text-gray-500">
        暂无数据
      </div>
    </template>
    
    <a-select-option
      v-for="item in filteredOptions"
      :key="item.id"
      :value="item.id"
      :title="item.fullPath"
    >
      <div class="industry-option">
        <div class="industry-name">{{ item.name }}</div>
        <div v-if="item.parentPath" class="industry-path">
          {{ item.parentPath }}
        </div>
      </div>
    </a-select-option>
  </a-select>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch } from 'vue';
import { getLeafNodes } from '/@/views/occu/ZyIndustry.api';
import { useMessage } from '/@/hooks/web/useMessage';

// 定义组件属性
interface Props {
  value?: string;
  placeholder?: string;
  disabled?: boolean;
}

// 定义事件
interface Emits {
  (e: 'update:value', value: string | undefined): void;
  (e: 'change', value: string | undefined, option?: any): void;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择行业类别',
  disabled: false,
});

const emit = defineEmits<Emits>();

// 响应式数据
const loading = ref(false);
const searchKeyword = ref('');
const allOptions = ref<any[]>([]); // 存储所有叶子节点数据
const selectedValue = ref<string | undefined>(props.value);
const dataLoaded = ref(false); // 标记数据是否已加载

const { createMessage } = useMessage();

// 计算属性：过滤后的选项（前端过滤，避免重复请求）
const filteredOptions = computed(() => {
  if (!searchKeyword.value) {
    return allOptions.value;
  }

  const keyword = searchKeyword.value.toLowerCase();
  return allOptions.value.filter(item =>
    item.name?.toLowerCase().includes(keyword) ||
    item.code?.toLowerCase().includes(keyword) ||
    item.helpChar?.toLowerCase().includes(keyword) ||
    item.parentPath?.toLowerCase().includes(keyword) ||
    item.fullPath?.toLowerCase().includes(keyword) ||
    (item.parentNames && item.parentNames.some((name: string) =>
      name.toLowerCase().includes(keyword)
    ))
  );
});

// 监听value变化
watch(() => props.value, (newValue) => {
  selectedValue.value = newValue;
});

// 监听selectedValue变化
watch(selectedValue, (newValue) => {
  emit('update:value', newValue);
});

// 加载数据 - 使用优化后的单个API调用
const loadData = async () => {
  // 如果数据已加载，直接返回
  if (dataLoaded.value) {
    return;
  }

  try {
    loading.value = true;

    // 调用后端优化的叶子节点API，只需要一次请求，不传keyword参数
    const response = await getLeafNodes();

    if (response && response.success) {
      // 后端已经处理好了叶子节点数据和路径信息
      allOptions.value = response.result || [];
      dataLoaded.value = true; // 标记数据已加载
      console.log(`✅ 叶子节点数据加载完成，共 ${allOptions.value.length} 条记录`);
    } else {
      createMessage.error(response?.message || '加载数据失败');
      allOptions.value = [];
    }
  } catch (error) {
    console.error('加载行业数据失败:', error);
    createMessage.error('加载数据失败');
    allOptions.value = [];
  } finally {
    loading.value = false;
  }
};

// 构建完整路径（用于tooltip显示）
const buildFullPath = (item: any) => {
  if (!item.parentNames || item.parentNames.length === 0) {
    return item.name;
  }
  return `${item.name} - ${item.parentNames.join(' > ')}`;
};

// 构建父级路径（用于副标题显示）
const buildParentPath = (item: any) => {
  if (!item.parentNames || item.parentNames.length === 0) {
    return '';
  }
  return item.parentNames.join(' > ');
};

// 搜索处理 - 只更新搜索关键词，使用前端过滤
const handleSearch = (value: string) => {
  searchKeyword.value = value;
  // 使用计算属性filteredOptions进行前端过滤，不需要重新请求数据
};

// 选择变化处理
const handleChange = (value: string | undefined, option?: any) => {
  selectedValue.value = value;
  emit('change', value, option);
};

// 清空处理
const handleClear = () => {
  selectedValue.value = undefined;
  searchKeyword.value = '';
  emit('change', undefined);
};

// 下拉框可见性变化处理 - 延迟加载优化
const handleDropdownVisibleChange = (visible: boolean) => {
  if (visible && !dataLoaded.value) {
    // 只有在下拉框打开且数据未加载时才加载数据
    loadData();
  }
};

// 组件挂载时不自动加载数据，改为延迟加载（用户点击时才加载）
// 这样可以避免页面初始化时的不必要请求
onMounted(() => {
  // 如果有初始值，则需要加载数据来显示对应的选项
  if (props.value) {
    loadData();
  }
});

// 暴露方法供外部调用
defineExpose({
  loadData,
  refresh: () => {
    dataLoaded.value = false; // 重置加载状态
    return loadData();
  },
});
</script>

<style lang="less" scoped>
.industry-option {
  padding: 4px 0;

  .industry-name {
    font-weight: 500;
    color: #333;
    font-size: 14px;
    line-height: 1.4;
  }

  .industry-path {
    font-size: 12px;
    color: #999;
    margin-top: 2px;
    line-height: 1.2;
    opacity: 0.8;
  }
}

// 下拉框样式优化
:deep(.ant-select) {
  .ant-select-selector {
    border-radius: 6px;
    transition: all 0.3s ease;

    &:hover {
      border-color: #40a9ff;
    }
  }

  &.ant-select-focused .ant-select-selector {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

:deep(.ant-select-dropdown) {
  border-radius: 6px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);

  .ant-select-item {
    padding: 8px 12px;

    &.ant-select-item-option-active {
      background-color: #f5f5f5;
    }

    &.ant-select-item-option-selected {
      background-color: #e6f7ff;
      font-weight: 500;
    }
  }

  .ant-select-item-option-content {
    white-space: normal;
    word-break: break-word;
    line-height: 1.4;
  }
}

// 加载状态样式
.text-center {
  text-align: center;
}

.py-2 {
  padding-top: 8px;
  padding-bottom: 8px;
}

.ml-2 {
  margin-left: 8px;
}

.text-gray-500 {
  color: #999;
}
</style>
