<template>
  <div class="form-layout-designer">
    <!-- 工具栏 -->
    <div class="designer-toolbar">
      <a-space>
        <a-button type="primary" @click="saveLayout">
          <SaveOutlined />
          保存布局
        </a-button>
        <a-button @click="previewLayout">
          <EyeOutlined />
          预览布局
        </a-button>
        <a-button @click="resetLayout">
          <ReloadOutlined />
          重置布局
        </a-button>
        <a-divider type="vertical" />
        <a-select v-model:value="selectedTemplate" placeholder="选择布局模板" style="width: 200px" @change="applyTemplate">
          <a-select-option v-for="template in layoutTemplates" :key="template.id" :value="template.id">
            {{ template.name }}
          </a-select-option>
        </a-select>
      </a-space>
    </div>

    <!-- 主要设计区域 -->
    <div class="designer-content">
      <!-- 左侧字段面板 -->
      <div class="fields-panel">
        <a-card title="可用字段" size="small">
          <div class="field-list">
            <div
              v-for="field in availableFields"
              :key="field.fieldCode"
              class="field-item"
              draggable="true"
              @dragstart="handleFieldDragStart($event, field)"
            >
              <div class="field-info">
                <div class="field-name">{{ field.fieldName }}</div>
                <div class="field-type">{{ getFieldTypeLabel(field.fieldType) }}</div>
              </div>
              <a-tag :color="getFieldTypeColor(field.fieldType)" size="small">
                {{ field.fieldType }}
              </a-tag>
            </div>
          </div>
        </a-card>
      </div>

      <!-- 中间布局设计区域 -->
      <div class="layout-canvas">
        <a-card title="表单布局设计" size="small" class="canvas-card">
          <template #extra>
            <a-space>
              <span>列数：</span>
              <a-select v-model:value="layoutConfig.columns" style="width: 80px" @change="updateColumns">
                <a-select-option :value="1">1列</a-select-option>
                <a-select-option :value="2">2列</a-select-option>
                <a-select-option :value="3">3列</a-select-option>
                <a-select-option :value="4">4列</a-select-option>
              </a-select>
            </a-space>
          </template>

          <div class="form-canvas" ref="canvasRef">
            <div class="canvas-grid" :style="gridStyle">
              <div
                v-for="(row, rowIndex) in layoutRows"
                :key="rowIndex"
                class="grid-row"
                @drop="handleDrop($event, rowIndex)"
                @dragover="handleDragOver"
              >
                <div
                  v-for="(cell, colIndex) in row"
                  :key="colIndex"
                  class="grid-cell"
                  :class="{ 'has-field': cell.field, 'drop-zone': isDragOver }"
                  @click="selectCell(rowIndex, colIndex)"
                >
                  <div v-if="cell.field" class="field-component" :class="{ selected: isSelected(rowIndex, colIndex) }">
                    <div class="field-header">
                      <span class="field-label">{{ cell.field.fieldName }}</span>
                      <a-space class="field-actions">
                        <a-button type="text" size="small" @click="configureField(rowIndex, colIndex)">
                          <SettingOutlined />
                        </a-button>
                        <a-button type="text" size="small" danger @click="removeField(rowIndex, colIndex)">
                          <DeleteOutlined />
                        </a-button>
                      </a-space>
                    </div>
                    <div class="field-preview">
                      <component :is="getFieldComponent(cell.field)" v-bind="getFieldProps(cell.field)" />
                    </div>
                  </div>
                  <div v-else class="empty-cell">
                    <PlusOutlined />
                    <span>拖拽字段到此处</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a-card>
      </div>

      <!-- 右侧属性配置面板 -->
      <div class="properties-panel">
        <a-card title="属性配置" size="small">
          <div v-if="selectedCell">
            <a-form layout="vertical" size="small">
              <a-form-item label="字段标签">
                <a-input v-model:value="selectedCell.field.fieldName" />
              </a-form-item>
              <a-form-item label="占用列数">
                <a-select v-model:value="selectedCell.layout.colspan" style="width: 100%">
                  <a-select-option :value="1">1列</a-select-option>
                  <a-select-option :value="2">2列</a-select-option>
                  <a-select-option :value="3">3列</a-select-option>
                  <a-select-option :value="4">4列</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="标签宽度">
                <a-input-number v-model:value="selectedCell.layout.labelWidth" :min="80" :max="200" />
              </a-form-item>
              <a-form-item label="字段宽度">
                <a-select v-model:value="selectedCell.layout.width" style="width: 100%">
                  <a-select-option value="100%">100%</a-select-option>
                  <a-select-option value="200px">200px</a-select-option>
                  <a-select-option value="300px">300px</a-select-option>
                  <a-select-option value="400px">400px</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="对齐方式">
                <a-radio-group v-model:value="selectedCell.layout.align">
                  <a-radio value="left">左对齐</a-radio>
                  <a-radio value="center">居中</a-radio>
                  <a-radio value="right">右对齐</a-radio>
                </a-radio-group>
              </a-form-item>
              <a-form-item label="样式类名">
                <a-input v-model:value="selectedCell.layout.className" placeholder="自定义CSS类名" />
              </a-form-item>
            </a-form>
          </div>
          <div v-else class="no-selection">
            <a-empty description="请选择一个字段进行配置" />
          </div>
        </a-card>
      </div>
    </div>

    <!-- 预览弹窗 -->
    <a-modal v-model:visible="previewVisible" title="表单布局预览" width="800px" :footer="null">
      <div class="layout-preview">
        <DynamicFormRenderer :layout-config="layoutConfig" :form-data="previewData" />
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, onMounted } from 'vue';
  import { message } from 'ant-design-vue';
  import {
    SaveOutlined,
    EyeOutlined,
    ReloadOutlined,
    SettingOutlined,
    DeleteOutlined,
    PlusOutlined,
  } from '@ant-design/icons-vue';
  import DynamicFormRenderer from './DynamicFormRenderer.vue';

  // Props
  interface Props {
    formCode: string;
    availableFields: any[];
  }

  const props = defineProps<Props>();
  const emit = defineEmits(['save', 'preview']);

  // 响应式数据
  const selectedTemplate = ref('');
  const previewVisible = ref(false);
  const isDragOver = ref(false);
  const selectedCell = ref<any>(null);
  const canvasRef = ref();

  // 布局配置
  const layoutConfig = reactive({
    formCode: props.formCode,
    columns: 2,
    rows: [],
    globalStyle: {
      labelWidth: 120,
      gutter: 16,
    },
  });

  // 预览数据
  const previewData = reactive({});

  // 布局行数据
  const layoutRows = ref<any[][]>([]);

  // 布局模板
  const layoutTemplates = ref([
    { id: 'single-column', name: '单列布局' },
    { id: 'two-column', name: '双列布局' },
    { id: 'three-column', name: '三列布局' },
    { id: 'mixed-layout', name: '混合布局' },
  ]);

  // 计算属性
  const gridStyle = computed(() => ({
    gridTemplateColumns: `repeat(${layoutConfig.columns}, 1fr)`,
    gap: `${layoutConfig.globalStyle.gutter}px`,
  }));

  // 方法
  const initializeLayout = () => {
    layoutRows.value = Array.from({ length: 10 }, () =>
      Array.from({ length: layoutConfig.columns }, () => ({
        field: null,
        layout: {
          colspan: 1,
          labelWidth: layoutConfig.globalStyle.labelWidth,
          width: '100%',
          align: 'left',
          className: '',
        },
      }))
    );
  };

  const handleFieldDragStart = (event: DragEvent, field: any) => {
    if (event.dataTransfer) {
      event.dataTransfer.setData('application/json', JSON.stringify(field));
    }
  };

  const handleDragOver = (event: DragEvent) => {
    event.preventDefault();
    isDragOver.value = true;
  };

  const handleDrop = (event: DragEvent, rowIndex: number) => {
    event.preventDefault();
    isDragOver.value = false;

    const fieldData = event.dataTransfer?.getData('application/json');
    if (fieldData) {
      const field = JSON.parse(fieldData);
      // 找到第一个空的单元格
      const row = layoutRows.value[rowIndex];
      const emptyIndex = row.findIndex(cell => !cell.field);
      if (emptyIndex !== -1) {
        row[emptyIndex].field = field;
        message.success(`字段 ${field.fieldName} 已添加到布局`);
      }
    }
  };

  const selectCell = (rowIndex: number, colIndex: number) => {
    const cell = layoutRows.value[rowIndex][colIndex];
    if (cell.field) {
      selectedCell.value = cell;
    }
  };

  const isSelected = (rowIndex: number, colIndex: number) => {
    const cell = layoutRows.value[rowIndex][colIndex];
    return selectedCell.value === cell;
  };

  const removeField = (rowIndex: number, colIndex: number) => {
    layoutRows.value[rowIndex][colIndex].field = null;
    if (selectedCell.value === layoutRows.value[rowIndex][colIndex]) {
      selectedCell.value = null;
    }
  };

  const configureField = (rowIndex: number, colIndex: number) => {
    selectCell(rowIndex, colIndex);
  };

  const updateColumns = () => {
    initializeLayout();
    selectedCell.value = null;
  };

  const getFieldComponent = (field: any) => {
    const componentMap = {
      string: 'a-input',
      number: 'a-input-number',
      date: 'a-date-picker',
      select: 'a-select',
      radio: 'a-radio-group',
      checkbox: 'a-checkbox-group',
      textarea: 'a-textarea',
      switch: 'a-switch',
    };
    return componentMap[field.fieldType] || 'a-input';
  };

  const getFieldProps = (field: any) => {
    return {
      placeholder: `请输入${field.fieldName}`,
      disabled: true, // 预览模式下禁用
    };
  };

  const getFieldTypeLabel = (fieldType: string) => {
    const labelMap = {
      string: '文本',
      number: '数字',
      date: '日期',
      select: '下拉',
      radio: '单选',
      checkbox: '多选',
      textarea: '多行文本',
      switch: '开关',
    };
    return labelMap[fieldType] || fieldType;
  };

  const getFieldTypeColor = (fieldType: string) => {
    const colorMap = {
      string: 'blue',
      number: 'green',
      date: 'orange',
      select: 'purple',
      radio: 'cyan',
      checkbox: 'magenta',
      textarea: 'geekblue',
      switch: 'lime',
    };
    return colorMap[fieldType] || 'default';
  };

  const saveLayout = () => {
    const layoutData = {
      formCode: props.formCode,
      layout: {
        columns: layoutConfig.columns,
        rows: layoutRows.value.filter(row => row.some(cell => cell.field)),
        globalStyle: layoutConfig.globalStyle,
      },
    };
    emit('save', layoutData);
    message.success('布局保存成功');
  };

  const previewLayout = () => {
    previewVisible.value = true;
  };

  const resetLayout = () => {
    initializeLayout();
    selectedCell.value = null;
    message.success('布局已重置');
  };

  const applyTemplate = (templateId: string) => {
    // 根据模板ID应用预设布局
    switch (templateId) {
      case 'single-column':
        layoutConfig.columns = 1;
        break;
      case 'two-column':
        layoutConfig.columns = 2;
        break;
      case 'three-column':
        layoutConfig.columns = 3;
        break;
      default:
        layoutConfig.columns = 2;
    }
    updateColumns();
    message.success('模板应用成功');
  };

  // 生命周期
  onMounted(() => {
    initializeLayout();
  });
</script>

<style scoped>
  .form-layout-designer {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .designer-toolbar {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;
  }

  .designer-content {
    flex: 1;
    display: flex;
    min-height: 0;
  }

  .fields-panel {
    width: 280px;
    border-right: 1px solid #f0f0f0;
    padding: 16px;
  }

  .field-list {
    max-height: 600px;
    overflow-y: auto;
  }

  .field-item {
    padding: 12px;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    margin-bottom: 8px;
    cursor: move;
    transition: all 0.3s;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .field-item:hover {
    border-color: #1890ff;
    background-color: #f6ffed;
  }

  .field-info {
    flex: 1;
  }

  .field-name {
    font-weight: 500;
    margin-bottom: 4px;
  }

  .field-type {
    font-size: 12px;
    color: #8c8c8c;
  }

  .layout-canvas {
    flex: 1;
    padding: 16px;
  }

  .canvas-card {
    height: 100%;
  }

  .form-canvas {
    height: 600px;
    overflow-y: auto;
  }

  .canvas-grid {
    display: grid;
    gap: 16px;
  }

  .grid-row {
    display: grid;
    grid-template-columns: inherit;
    gap: inherit;
  }

  .grid-cell {
    min-height: 80px;
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
  }

  .grid-cell.has-field {
    border-style: solid;
    border-color: #1890ff;
  }

  .grid-cell.drop-zone {
    border-color: #52c41a;
    background-color: #f6ffed;
  }

  .field-component {
    width: 100%;
    padding: 12px;
    border-radius: 4px;
    background: #fff;
  }

  .field-component.selected {
    box-shadow: 0 0 0 2px #1890ff;
  }

  .field-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  .field-label {
    font-weight: 500;
  }

  .field-actions {
    opacity: 0;
    transition: opacity 0.3s;
  }

  .field-component:hover .field-actions {
    opacity: 1;
  }

  .empty-cell {
    color: #8c8c8c;
    text-align: center;
  }

  .properties-panel {
    width: 300px;
    border-left: 1px solid #f0f0f0;
    padding: 16px;
  }

  .no-selection {
    text-align: center;
    padding: 40px 0;
  }

  .layout-preview {
    max-height: 600px;
    overflow-y: auto;
  }
</style>
