<template>
  <div class="dynamic-form-renderer">
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-col="{ style: { width: `${layoutConfig.globalStyle?.labelWidth || 120}px` } }"
      :wrapper-col="{ span: 16 }"
      layout="horizontal"
      @finish="handleSubmit"
      @finishFailed="handleSubmitFailed"
    >
      <div class="form-grid" :style="gridStyle">
        <template v-for="(row, rowIndex) in visibleRows" :key="rowIndex">
          <template v-for="(cell, colIndex) in row" :key="`${rowIndex}-${colIndex}`">
            <div
              v-if="cell.field && isFieldVisible(cell.field.fieldCode)"
              class="form-item-wrapper"
              :style="getCellStyle(cell)"
            >
              <a-form-item
                :label="cell.field.fieldName"
                :name="cell.field.fieldCode"
                :required="isFieldRequired(cell.field.fieldCode)"
                :class="cell.layout.className"
              >
                <!-- 文本输入 -->
                <a-input
                  v-if="cell.field.fieldType === 'string'"
                  v-model:value="formData[cell.field.fieldCode]"
                  :placeholder="`请输入${cell.field.fieldName}`"
                  :disabled="isFieldDisabled(cell.field.fieldCode)"
                  :style="{ width: cell.layout.width }"
                />

                <!-- 多行文本 -->
                <a-textarea
                  v-else-if="cell.field.fieldType === 'textarea'"
                  v-model:value="formData[cell.field.fieldCode]"
                  :placeholder="`请输入${cell.field.fieldName}`"
                  :disabled="isFieldDisabled(cell.field.fieldCode)"
                  :rows="4"
                  :style="{ width: cell.layout.width }"
                />

                <!-- 数字输入 -->
                <a-input-number
                  v-else-if="cell.field.fieldType === 'number'"
                  v-model:value="formData[cell.field.fieldCode]"
                  :placeholder="`请输入${cell.field.fieldName}`"
                  :disabled="isFieldDisabled(cell.field.fieldCode)"
                  :style="{ width: cell.layout.width }"
                />

                <!-- 日期选择 -->
                <a-date-picker
                  v-else-if="cell.field.fieldType === 'date'"
                  v-model:value="formData[cell.field.fieldCode]"
                  :placeholder="`请选择${cell.field.fieldName}`"
                  :disabled="isFieldDisabled(cell.field.fieldCode)"
                  :style="{ width: cell.layout.width }"
                />

                <!-- 日期时间选择 -->
                <a-date-picker
                  v-else-if="cell.field.fieldType === 'datetime'"
                  v-model:value="formData[cell.field.fieldCode]"
                  :placeholder="`请选择${cell.field.fieldName}`"
                  :disabled="isFieldDisabled(cell.field.fieldCode)"
                  :show-time="true"
                  :style="{ width: cell.layout.width }"
                />

                <!-- 下拉选择 -->
                <a-select
                  v-else-if="cell.field.fieldType === 'select'"
                  v-model:value="formData[cell.field.fieldCode]"
                  :placeholder="`请选择${cell.field.fieldName}`"
                  :disabled="isFieldDisabled(cell.field.fieldCode)"
                  :style="{ width: cell.layout.width }"
                >
                  <a-select-option
                    v-for="option in getFieldOptions(cell.field)"
                    :key="option.value"
                    :value="option.value"
                    :disabled="option.disabled"
                  >
                    {{ option.label }}
                  </a-select-option>
                </a-select>

                <!-- 单选按钮 -->
                <a-radio-group
                  v-else-if="cell.field.fieldType === 'radio'"
                  v-model:value="formData[cell.field.fieldCode]"
                  :disabled="isFieldDisabled(cell.field.fieldCode)"
                >
                  <a-radio
                    v-for="option in getFieldOptions(cell.field)"
                    :key="option.value"
                    :value="option.value"
                    :disabled="option.disabled"
                  >
                    {{ option.label }}
                  </a-radio>
                </a-radio-group>

                <!-- 复选框组 -->
                <a-checkbox-group
                  v-else-if="cell.field.fieldType === 'checkbox'"
                  v-model:value="formData[cell.field.fieldCode]"
                  :disabled="isFieldDisabled(cell.field.fieldCode)"
                >
                  <a-checkbox
                    v-for="option in getFieldOptions(cell.field)"
                    :key="option.value"
                    :value="option.value"
                    :disabled="option.disabled"
                  >
                    {{ option.label }}
                  </a-checkbox>
                </a-checkbox-group>

                <!-- 开关 -->
                <a-switch
                  v-else-if="cell.field.fieldType === 'switch'"
                  v-model:checked="formData[cell.field.fieldCode]"
                  :disabled="isFieldDisabled(cell.field.fieldCode)"
                />

                <!-- 默认文本输入 -->
                <a-input
                  v-else
                  v-model:value="formData[cell.field.fieldCode]"
                  :placeholder="`请输入${cell.field.fieldName}`"
                  :disabled="isFieldDisabled(cell.field.fieldCode)"
                  :style="{ width: cell.layout.width }"
                />
              </a-form-item>
            </div>
          </template>
        </template>
      </div>

      <!-- 表单操作按钮 -->
      <div class="form-actions" v-if="showActions">
        <a-space>
          <a-button type="primary" html-type="submit" :loading="submitting">
            {{ submitText }}
          </a-button>
          <a-button @click="handleReset">重置</a-button>
          <a-button @click="handleCancel" v-if="showCancel">取消</a-button>
        </a-space>
      </div>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, watch, onMounted } from 'vue';
  import { message } from 'ant-design-vue';
  import { useFormRule } from '/@/utils/formRule/FormRuleComposer';

  // Props
  interface Props {
    layoutConfig: any;
    formData: Record<string, any>;
    formCode?: string;
    showActions?: boolean;
    showCancel?: boolean;
    submitText?: string;
    readonly?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    showActions: true,
    showCancel: false,
    submitText: '提交',
    readonly: false,
  });

  const emit = defineEmits(['submit', 'reset', 'cancel', 'change']);

  // 响应式数据
  const formRef = ref();
  const submitting = ref(false);
  const formRules = ref<Record<string, any>>({});

  // 表单规则引擎
  const {
    isLoading,
    isReady,
    getFieldValidationRules,
    isFieldVisible,
    isFieldDisabled,
    isFieldRequired,
  } = useFormRule({
    formCode: props.formCode || 'dynamic_form',
    formData: props.formData,
    autoInit: true,
  });

  // 计算属性
  const gridStyle = computed(() => ({
    display: 'grid',
    gridTemplateColumns: `repeat(${props.layoutConfig.columns || 2}, 1fr)`,
    gap: `${props.layoutConfig.globalStyle?.gutter || 16}px`,
  }));

  const visibleRows = computed(() => {
    if (!props.layoutConfig.layout?.rows) return [];
    return props.layoutConfig.layout.rows.filter((row: any[]) =>
      row.some((cell: any) => cell.field && isFieldVisible(cell.field.fieldCode))
    );
  });

  // 方法
  const getCellStyle = (cell: any) => {
    const style: Record<string, any> = {
      gridColumn: `span ${cell.layout.colspan || 1}`,
    };

    if (cell.layout.align) {
      style.textAlign = cell.layout.align;
    }

    return style;
  };

  const getFieldOptions = (field: any) => {
    // 从字段配置中获取选项，或者从字典中获取
    if (field.options && Array.isArray(field.options)) {
      return field.options;
    }

    // 默认选项
    const defaultOptions = {
      gender: [
        { value: '男', label: '男' },
        { value: '女', label: '女' },
      ],
      examCategory: [
        { value: '健康体检', label: '健康体检' },
        { value: '入职体检', label: '入职体检' },
        { value: '年度体检', label: '年度体检' },
      ],
      cardType: [
        { value: '居民身份证', label: '居民身份证' },
        { value: '护照', label: '护照' },
        { value: '军官证', label: '军官证' },
      ],
    };

    return defaultOptions[field.fieldCode] || [];
  };

  const buildFormRules = () => {
    const rules: Record<string, any> = {};

    if (!props.layoutConfig.layout?.rows) return rules;

    props.layoutConfig.layout.rows.forEach((row: any[]) => {
      row.forEach((cell: any) => {
        if (cell.field) {
          const fieldRules = getFieldValidationRules(cell.field.fieldCode);
          if (fieldRules.length > 0) {
            rules[cell.field.fieldCode] = fieldRules;
          }
        }
      });
    });

    return rules;
  };

  const handleSubmit = (values: any) => {
    submitting.value = true;
    try {
      emit('submit', values);
      message.success('表单提交成功');
    } catch (error) {
      message.error('表单提交失败');
    } finally {
      submitting.value = false;
    }
  };

  const handleSubmitFailed = (errorInfo: any) => {
    console.error('表单验证失败:', errorInfo);
    message.error('请检查表单填写是否正确');
  };

  const handleReset = () => {
    formRef.value?.resetFields();
    emit('reset');
  };

  const handleCancel = () => {
    emit('cancel');
  };

  const validateForm = async () => {
    try {
      const values = await formRef.value?.validate();
      return { valid: true, values };
    } catch (error) {
      return { valid: false, error };
    }
  };

  // 监听表单数据变化
  watch(
    () => props.formData,
    (newData) => {
      emit('change', newData);
    },
    { deep: true }
  );

  // 监听规则引擎就绪状态
  watch(isReady, (ready) => {
    if (ready) {
      formRules.value = buildFormRules();
    }
  });

  // 暴露方法给父组件
  defineExpose({
    validateForm,
    resetForm: handleReset,
    getFormData: () => props.formData,
  });

  // 生命周期
  onMounted(() => {
    if (isReady.value) {
      formRules.value = buildFormRules();
    }
  });
</script>

<style scoped>
  .dynamic-form-renderer {
    width: 100%;
  }

  .form-grid {
    margin-bottom: 24px;
  }

  .form-item-wrapper {
    width: 100%;
  }

  .form-actions {
    text-align: center;
    padding-top: 24px;
    border-top: 1px solid #f0f0f0;
  }

  /* 响应式布局 */
  @media (max-width: 768px) {
    .form-grid {
      grid-template-columns: 1fr !important;
    }

    .form-item-wrapper {
      grid-column: span 1 !important;
    }
  }
</style>
