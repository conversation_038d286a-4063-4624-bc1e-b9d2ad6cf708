<template>
  <a-modal v-model:open="visible" title="联动规则编辑" width="900px" :confirm-loading="confirmLoading" @ok="handleSave" @cancel="handleCancel">
    <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
      <a-form-item label="规则ID" name="id">
        <a-input v-model:value="formData.id" placeholder="自动生成或手动输入唯一ID" :disabled="isEdit" />
      </a-form-item>

      <a-divider orientation="left">触发条件</a-divider>

      <a-form-item label="源字段" name="sourceField">
        <a-select v-model:value="formData.sourceField" placeholder="选择触发联动的字段" show-search :filter-option="filterOption">
          <a-select-option v-for="option in fieldOptions" :key="option.value" :value="option.value">
            {{ option.label }} ({{ option.value }})
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="条件类型" name="conditionType">
        <a-select v-model:value="formData.conditionType" placeholder="选择条件判断类型">
          <a-select-option value="equals">等于</a-select-option>
          <a-select-option value="not_equals">不等于</a-select-option>
          <a-select-option value="in">包含于</a-select-option>
          <a-select-option value="not_in">不包含于</a-select-option>
          <a-select-option value="greater">大于</a-select-option>
          <a-select-option value="less">小于</a-select-option>
          <a-select-option value="empty">为空</a-select-option>
          <a-select-option value="not_empty">不为空</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="条件值" name="conditionValue" v-if="needConditionValue">
        <div class="condition-value-input">
          <a-input v-if="isSimpleCondition" v-model:value="formData.conditionValue" placeholder="请输入条件值" />
          <a-select
            v-else-if="isArrayCondition"
            v-model:value="conditionValueArray"
            mode="tags"
            placeholder="请输入多个值，按回车添加"
            style="width: 100%"
          >
          </a-select>
          <a-input-number v-else-if="isNumberCondition" v-model:value="formData.conditionValue" placeholder="请输入数值" style="width: 100%" />
        </div>
      </a-form-item>

      <a-divider orientation="left">执行动作</a-divider>

      <a-form-item label="目标字段" name="targetField">
        <a-select v-model:value="formData.targetField" placeholder="选择被联动的字段" show-search :filter-option="filterOption">
          <a-select-option v-for="option in targetFieldOptions" :key="option.value" :value="option.value">
            {{ option.label }} ({{ option.value }})
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="联动类型" name="dependencyType">
        <a-select v-model:value="formData.dependencyType" placeholder="选择联动效果类型">
          <a-select-option value="required">控制必填</a-select-option>
          <a-select-option value="visible">控制可见</a-select-option>
          <a-select-option value="disabled">控制禁用</a-select-option>
          <a-select-option value="options">控制选项</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="动作值" name="actionValue" v-if="needActionValue">
        <div class="action-value-input">
          <a-switch
            v-if="isBooleanAction"
            v-model:checked="formData.actionValue"
            :checked-children="getActionTrueText()"
            :un-checked-children="getActionFalseText()"
          />
          <a-textarea v-else-if="isOptionsAction" v-model:value="actionValueText" placeholder="请输入选项配置（JSON格式）" :rows="4" />
        </div>
      </a-form-item>

      <a-form-item label="优先级" name="priority">
        <a-input-number v-model:value="formData.priority" placeholder="数值越大优先级越高" :min="0" :max="999" style="width: 100%" />
      </a-form-item>

      <a-form-item label="规则描述">
        <a-textarea v-model:value="formData.description" placeholder="可选：描述这个规则的作用" :rows="2" />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="handleCancel">取消</a-button>
        <a-button @click="testRule" :loading="testLoading">测试规则</a-button>
        <a-button type="primary" @click="handleSave" :loading="confirmLoading"> 保存 </a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import type { DependencyRule } from '/@/utils/formRule/FormRuleEngine';

  interface FieldOption {
    label: string;
    value: string;
  }

  interface Props {
    dependencyRule?: DependencyRule | null;
    fieldOptions?: FieldOption[];
  }

  const props = defineProps<Props>();
  const emit = defineEmits<{
    save: [rule: DependencyRule];
  }>();

  // 状态管理
  const visible = ref(false);
  const confirmLoading = ref(false);
  const testLoading = ref(false);
  const formRef = ref();
  const isEdit = ref(false);

  // 表单数据
  const formData = reactive<DependencyRule & { description?: string }>({
    id: '',
    sourceField: '',
    targetField: '',
    dependencyType: 'required',
    conditionType: 'equals',
    conditionValue: '',
    actionValue: true,
    priority: 0,
    description: '',
  });

  // 辅助数据
  const conditionValueArray = ref<string[]>([]);
  const actionValueText = ref('');

  // 表单验证规则
  const rules = {
    id: [
      { required: true, message: '请输入规则ID' },
      { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: 'ID必须以字母开头，只能包含字母、数字和下划线' },
    ],
    sourceField: [{ required: true, message: '请选择源字段' }],
    targetField: [{ required: true, message: '请选择目标字段' }],
    conditionType: [{ required: true, message: '请选择条件类型' }],
    dependencyType: [{ required: true, message: '请选择联动类型' }],
  };

  // 计算属性
  const targetFieldOptions = computed(() => {
    // 排除源字段，避免自己联动自己
    return props.fieldOptions?.filter((option) => option.value !== formData.sourceField) || [];
  });

  const needConditionValue = computed(() => {
    return !['empty', 'not_empty'].includes(formData.conditionType);
  });

  const isSimpleCondition = computed(() => {
    return ['equals', 'not_equals'].includes(formData.conditionType);
  });

  const isArrayCondition = computed(() => {
    return ['in', 'not_in'].includes(formData.conditionType);
  });

  const isNumberCondition = computed(() => {
    return ['greater', 'less'].includes(formData.conditionType);
  });

  const needActionValue = computed(() => {
    return ['required', 'visible', 'disabled', 'options'].includes(formData.dependencyType);
  });

  const isBooleanAction = computed(() => {
    return ['required', 'visible', 'disabled'].includes(formData.dependencyType);
  });

  const isOptionsAction = computed(() => {
    return formData.dependencyType === 'options';
  });

  // 监听props变化
  watch(
    () => props.dependencyRule,
    (newRule) => {
      if (newRule) {
        isEdit.value = true;
        Object.assign(formData, {
          ...newRule,
          description: newRule.description || '',
        });

        // 处理数组类型的条件值
        if (isArrayCondition.value && Array.isArray(newRule.conditionValue)) {
          conditionValueArray.value = [...newRule.conditionValue];
        }

        // 处理选项类型的动作值
        if (isOptionsAction.value && newRule.actionValue) {
          actionValueText.value = JSON.stringify(newRule.actionValue, null, 2);
        }
      } else {
        isEdit.value = false;
        resetForm();
      }
    },
    { immediate: true, deep: true }
  );

  // 监听数组条件值变化
  watch(
    conditionValueArray,
    (newArray) => {
      if (isArrayCondition.value) {
        formData.conditionValue = newArray;
      }
    },
    { deep: true }
  );

  // 监听选项动作值变化
  watch(actionValueText, (newText) => {
    if (isOptionsAction.value) {
      try {
        formData.actionValue = JSON.parse(newText);
      } catch (error) {
        // JSON解析失败时保持原值
      }
    }
  });

  // 工具方法
  const filterOption = (input: string, option: any) => {
    return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };

  const getActionTrueText = () => {
    const map = {
      required: '必填',
      visible: '显示',
      disabled: '禁用',
    };
    return map[formData.dependencyType] || '是';
  };

  const getActionFalseText = () => {
    const map = {
      required: '非必填',
      visible: '隐藏',
      disabled: '启用',
    };
    return map[formData.dependencyType] || '否';
  };

  // 生成唯一ID
  const generateId = () => {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    return `dep_${timestamp}_${random}`;
  };

  // 测试规则
  const testRule = async () => {
    try {
      testLoading.value = true;
      await formRef.value.validate();

      // 这里可以实现规则测试逻辑
      message.success('规则配置正确，可以保存');
    } catch (error) {
      message.error('规则配置有误，请检查');
    } finally {
      testLoading.value = false;
    }
  };

  // 保存
  const handleSave = async () => {
    try {
      await formRef.value.validate();
      confirmLoading.value = true;

      // 如果是新建且没有ID，自动生成
      if (!isEdit.value && !formData.id) {
        formData.id = generateId();
      }

      // 构建最终的规则对象
      const ruleToSave: DependencyRule = {
        id: formData.id,
        sourceField: formData.sourceField,
        targetField: formData.targetField,
        dependencyType: formData.dependencyType,
        conditionType: formData.conditionType,
        conditionValue: formData.conditionValue,
        actionValue: formData.actionValue,
        priority: formData.priority,
      };

      emit('save', ruleToSave);
      message.success('保存成功');
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      confirmLoading.value = false;
    }
  };

  // 取消
  const handleCancel = () => {
    visible.value = false;
  };

  // 重置表单
  const resetForm = () => {
    Object.assign(formData, {
      id: '',
      sourceField: '',
      targetField: '',
      dependencyType: 'required',
      conditionType: 'equals',
      conditionValue: '',
      actionValue: true,
      priority: 0,
      description: '',
    });
    conditionValueArray.value = [];
    actionValueText.value = '';
    formRef.value?.resetFields();
  };

  defineExpose({
    resetForm,
  });
</script>

<style lang="less" scoped>
  .condition-value-input,
  .action-value-input {
    width: 100%;
  }

  :deep(.ant-form-item) {
    margin-bottom: 16px;
  }

  :deep(.ant-divider) {
    margin: 16px 0;
  }
</style>
