<template>
  <div class="form-layout-manager">
    <!-- 布局配置标签页 -->
    <a-tabs v-model:activeKey="activeTab" type="card">
      <!-- 布局设计 -->
      <a-tab-pane key="design" tab="布局设计">
        <FormLayoutDesigner
          :form-code="formCode"
          :available-fields="availableFields"
          @save="handleSaveLayout"
          @preview="handlePreviewLayout"
        />
      </a-tab-pane>

      <!-- 字段配置 -->
      <a-tab-pane key="fields" tab="字段配置">
        <div class="fields-config">
          <a-card title="字段布局配置" size="small">
            <template #extra>
              <a-space>
                <a-button type="primary" @click="saveFieldsConfig">
                  <SaveOutlined />
                  保存配置
                </a-button>
                <a-button @click="resetFieldsConfig">
                  <ReloadOutlined />
                  重置配置
                </a-button>
              </a-space>
            </template>

            <a-table
              :columns="fieldsColumns"
              :data-source="fieldsConfigData"
              :pagination="false"
              size="small"
              row-key="fieldCode"
            >
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.key === 'fieldName'">
                  <a-input v-model:value="record.fieldName" size="small" />
                </template>
                <template v-else-if="column.key === 'colspan'">
                  <a-select v-model:value="record.layout.colspan" size="small" style="width: 80px">
                    <a-select-option :value="1">1</a-select-option>
                    <a-select-option :value="2">2</a-select-option>
                    <a-select-option :value="3">3</a-select-option>
                    <a-select-option :value="4">4</a-select-option>
                  </a-select>
                </template>
                <template v-else-if="column.key === 'labelWidth'">
                  <a-input-number v-model:value="record.layout.labelWidth" size="small" :min="60" :max="300" />
                </template>
                <template v-else-if="column.key === 'width'">
                  <a-select v-model:value="record.layout.width" size="small" style="width: 100px">
                    <a-select-option value="100%">100%</a-select-option>
                    <a-select-option value="200px">200px</a-select-option>
                    <a-select-option value="300px">300px</a-select-option>
                    <a-select-option value="400px">400px</a-select-option>
                  </a-select>
                </template>
                <template v-else-if="column.key === 'align'">
                  <a-select v-model:value="record.layout.align" size="small" style="width: 80px">
                    <a-select-option value="left">左</a-select-option>
                    <a-select-option value="center">中</a-select-option>
                    <a-select-option value="right">右</a-select-option>
                  </a-select>
                </template>
                <template v-else-if="column.key === 'visible'">
                  <a-switch v-model:checked="record.layout.visible" size="small" />
                </template>
                <template v-else-if="column.key === 'sortOrder'">
                  <a-input-number v-model:value="record.layout.sortOrder" size="small" :min="1" />
                </template>
                <template v-else-if="column.key === 'actions'">
                  <a-space>
                    <a-button type="link" size="small" @click="moveUp(index)" :disabled="index === 0">
                      <UpOutlined />
                    </a-button>
                    <a-button type="link" size="small" @click="moveDown(index)" :disabled="index === fieldsConfigData.length - 1">
                      <DownOutlined />
                    </a-button>
                  </a-space>
                </template>
              </template>
            </a-table>
          </a-card>
        </div>
      </a-tab-pane>

      <!-- 样式配置 -->
      <a-tab-pane key="styles" tab="样式配置">
        <div class="styles-config">
          <a-card title="全局样式配置" size="small">
            <a-form layout="vertical">
              <a-row :gutter="16">
                <a-col :span="8">
                  <a-form-item label="表单列数">
                    <a-select v-model:value="globalStyles.columns">
                      <a-select-option :value="1">1列</a-select-option>
                      <a-select-option :value="2">2列</a-select-option>
                      <a-select-option :value="3">3列</a-select-option>
                      <a-select-option :value="4">4列</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="标签宽度">
                    <a-input-number v-model:value="globalStyles.labelWidth" :min="80" :max="300" />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="字段间距">
                    <a-input-number v-model:value="globalStyles.gutter" :min="8" :max="32" />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="16">
                <a-col :span="8">
                  <a-form-item label="表单宽度">
                    <a-select v-model:value="globalStyles.formWidth">
                      <a-select-option value="100%">100%</a-select-option>
                      <a-select-option value="800px">800px</a-select-option>
                      <a-select-option value="1000px">1000px</a-select-option>
                      <a-select-option value="1200px">1200px</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="表单对齐">
                    <a-select v-model:value="globalStyles.formAlign">
                      <a-select-option value="left">左对齐</a-select-option>
                      <a-select-option value="center">居中</a-select-option>
                      <a-select-option value="right">右对齐</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="表单布局">
                    <a-select v-model:value="globalStyles.layout">
                      <a-select-option value="horizontal">水平布局</a-select-option>
                      <a-select-option value="vertical">垂直布局</a-select-option>
                      <a-select-option value="inline">内联布局</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>

              <a-form-item label="自定义CSS">
                <a-textarea
                  v-model:value="globalStyles.customCSS"
                  :rows="6"
                  placeholder="输入自定义CSS样式"
                />
              </a-form-item>

              <a-form-item>
                <a-space>
                  <a-button type="primary" @click="saveGlobalStyles">保存样式配置</a-button>
                  <a-button @click="resetGlobalStyles">重置样式</a-button>
                  <a-button @click="previewStyles">预览效果</a-button>
                </a-space>
              </a-form-item>
            </a-form>
          </a-card>
        </div>
      </a-tab-pane>

      <!-- 模板管理 -->
      <a-tab-pane key="templates" tab="模板管理">
        <div class="templates-config">
          <a-card title="布局模板管理" size="small">
            <template #extra>
              <a-space>
                <a-button type="primary" @click="showCreateTemplateModal">
                  <PlusOutlined />
                  新建模板
                </a-button>
                <a-button @click="importTemplate">
                  <ImportOutlined />
                  导入模板
                </a-button>
              </a-space>
            </template>

            <a-list :data-source="layoutTemplates" item-layout="horizontal">
              <template #renderItem="{ item }">
                <a-list-item>
                  <template #actions>
                    <a-space>
                      <a-button type="link" size="small" @click="applyTemplate(item)">应用</a-button>
                      <a-button type="link" size="small" @click="editTemplate(item)">编辑</a-button>
                      <a-button type="link" size="small" @click="exportTemplate(item)">导出</a-button>
                      <a-button type="link" size="small" danger @click="deleteTemplate(item)">删除</a-button>
                    </a-space>
                  </template>
                  <a-list-item-meta>
                    <template #title>{{ item.name }}</template>
                    <template #description>{{ item.description }}</template>
                    <template #avatar>
                      <a-avatar :src="item.preview" />
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </div>
      </a-tab-pane>
    </a-tabs>

    <!-- 预览弹窗 -->
    <a-modal v-model:visible="previewVisible" title="布局预览" width="1000px" :footer="null">
      <div class="layout-preview">
        <DynamicFormRenderer
          :layout-config="previewLayoutConfig"
          :form-data="previewFormData"
          :show-actions="false"
        />
      </div>
    </a-modal>

    <!-- 创建模板弹窗 -->
    <a-modal
      v-model:visible="createTemplateVisible"
      title="创建布局模板"
      @ok="handleCreateTemplate"
      @cancel="createTemplateVisible = false"
    >
      <a-form layout="vertical">
        <a-form-item label="模板名称" required>
          <a-input v-model:value="newTemplate.name" placeholder="请输入模板名称" />
        </a-form-item>
        <a-form-item label="模板描述">
          <a-textarea v-model:value="newTemplate.description" :rows="3" placeholder="请输入模板描述" />
        </a-form-item>
        <a-form-item label="模板分类">
          <a-select v-model:value="newTemplate.category" placeholder="请选择模板分类">
            <a-select-option value="basic">基础模板</a-select-option>
            <a-select-option value="business">业务模板</a-select-option>
            <a-select-option value="custom">自定义模板</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, onMounted } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import {
    SaveOutlined,
    ReloadOutlined,
    UpOutlined,
    DownOutlined,
    PlusOutlined,
    ImportOutlined,
  } from '@ant-design/icons-vue';
  import FormLayoutDesigner from './FormLayoutDesigner.vue';
  import DynamicFormRenderer from './DynamicFormRenderer.vue';

  // Props
  interface Props {
    formCode: string;
    availableFields: any[];
  }

  const props = defineProps<Props>();
  const emit = defineEmits(['save', 'change']);

  // 响应式数据
  const activeTab = ref('design');
  const previewVisible = ref(false);
  const createTemplateVisible = ref(false);
  const previewLayoutConfig = ref({});
  const previewFormData = reactive({});

  // 字段配置数据
  const fieldsConfigData = ref<any[]>([]);

  // 全局样式配置
  const globalStyles = reactive({
    columns: 2,
    labelWidth: 120,
    gutter: 16,
    formWidth: '100%',
    formAlign: 'left',
    layout: 'horizontal',
    customCSS: '',
  });

  // 新建模板数据
  const newTemplate = reactive({
    name: '',
    description: '',
    category: 'custom',
  });

  // 布局模板列表
  const layoutTemplates = ref([
    {
      id: 'basic-form',
      name: '基础表单模板',
      description: '适用于简单的信息录入表单',
      category: 'basic',
      preview: '/images/template-basic.png',
    },
    {
      id: 'customer-reg',
      name: '客户登记模板',
      description: '专为客户登记设计的表单布局',
      category: 'business',
      preview: '/images/template-customer.png',
    },
  ]);

  // 字段配置表格列定义
  const fieldsColumns = [
    { title: '字段名称', dataIndex: 'fieldName', key: 'fieldName', width: 150 },
    { title: '字段代码', dataIndex: 'fieldCode', key: 'fieldCode', width: 120 },
    { title: '占用列数', key: 'colspan', width: 100 },
    { title: '标签宽度', key: 'labelWidth', width: 100 },
    { title: '字段宽度', key: 'width', width: 100 },
    { title: '对齐方式', key: 'align', width: 80 },
    { title: '是否显示', key: 'visible', width: 80 },
    { title: '排序', key: 'sortOrder', width: 80 },
    { title: '操作', key: 'actions', width: 100 },
  ];

  // 方法
  const initializeFieldsConfig = () => {
    fieldsConfigData.value = props.availableFields.map((field, index) => ({
      ...field,
      layout: {
        colspan: 1,
        labelWidth: globalStyles.labelWidth,
        width: '100%',
        align: 'left',
        visible: true,
        sortOrder: index + 1,
      },
    }));
  };

  const handleSaveLayout = (layoutData: any) => {
    emit('save', { type: 'layout', data: layoutData });
  };

  const handlePreviewLayout = (layoutData: any) => {
    previewLayoutConfig.value = layoutData;
    previewVisible.value = true;
  };

  const saveFieldsConfig = () => {
    emit('save', { type: 'fields', data: fieldsConfigData.value });
    message.success('字段配置保存成功');
  };

  const resetFieldsConfig = () => {
    initializeFieldsConfig();
    message.success('字段配置已重置');
  };

  const moveUp = (index: number) => {
    if (index > 0) {
      const temp = fieldsConfigData.value[index];
      fieldsConfigData.value[index] = fieldsConfigData.value[index - 1];
      fieldsConfigData.value[index - 1] = temp;
      
      // 更新排序号
      fieldsConfigData.value[index].layout.sortOrder = index + 1;
      fieldsConfigData.value[index - 1].layout.sortOrder = index;
    }
  };

  const moveDown = (index: number) => {
    if (index < fieldsConfigData.value.length - 1) {
      const temp = fieldsConfigData.value[index];
      fieldsConfigData.value[index] = fieldsConfigData.value[index + 1];
      fieldsConfigData.value[index + 1] = temp;
      
      // 更新排序号
      fieldsConfigData.value[index].layout.sortOrder = index + 1;
      fieldsConfigData.value[index + 1].layout.sortOrder = index + 2;
    }
  };

  const saveGlobalStyles = () => {
    emit('save', { type: 'styles', data: globalStyles });
    message.success('样式配置保存成功');
  };

  const resetGlobalStyles = () => {
    Object.assign(globalStyles, {
      columns: 2,
      labelWidth: 120,
      gutter: 16,
      formWidth: '100%',
      formAlign: 'left',
      layout: 'horizontal',
      customCSS: '',
    });
    message.success('样式配置已重置');
  };

  const previewStyles = () => {
    previewLayoutConfig.value = {
      globalStyle: globalStyles,
      layout: { rows: [] },
    };
    previewVisible.value = true;
  };

  const showCreateTemplateModal = () => {
    createTemplateVisible.value = true;
  };

  const handleCreateTemplate = () => {
    if (!newTemplate.name) {
      message.error('请输入模板名称');
      return;
    }

    const template = {
      id: `template_${Date.now()}`,
      ...newTemplate,
      createTime: new Date().toISOString(),
    };

    layoutTemplates.value.push(template);
    createTemplateVisible.value = false;
    
    // 重置表单
    Object.assign(newTemplate, {
      name: '',
      description: '',
      category: 'custom',
    });

    message.success('模板创建成功');
  };

  const applyTemplate = (template: any) => {
    Modal.confirm({
      title: '确认应用模板',
      content: `确定要应用模板"${template.name}"吗？这将覆盖当前的布局配置。`,
      onOk: () => {
        // 应用模板逻辑
        message.success(`模板"${template.name}"应用成功`);
      },
    });
  };

  const editTemplate = (template: any) => {
    message.info('编辑模板功能开发中...');
  };

  const exportTemplate = (template: any) => {
    const templateData = JSON.stringify(template, null, 2);
    const blob = new Blob([templateData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${template.name}.json`;
    a.click();
    URL.revokeObjectURL(url);
    message.success('模板导出成功');
  };

  const deleteTemplate = (template: any) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除模板"${template.name}"吗？`,
      onOk: () => {
        const index = layoutTemplates.value.findIndex(t => t.id === template.id);
        if (index > -1) {
          layoutTemplates.value.splice(index, 1);
          message.success('模板删除成功');
        }
      },
    });
  };

  const importTemplate = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e: any) => {
      const file = e.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (event: any) => {
          try {
            const template = JSON.parse(event.target.result);
            layoutTemplates.value.push({
              ...template,
              id: `imported_${Date.now()}`,
            });
            message.success('模板导入成功');
          } catch (error) {
            message.error('模板文件格式错误');
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  // 生命周期
  onMounted(() => {
    initializeFieldsConfig();
  });
</script>

<style scoped>
  .form-layout-manager {
    height: 100%;
  }

  .fields-config,
  .styles-config,
  .templates-config {
    padding: 16px;
  }

  .layout-preview {
    max-height: 600px;
    overflow-y: auto;
    padding: 16px;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
  }
</style>
