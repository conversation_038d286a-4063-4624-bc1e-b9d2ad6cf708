<template>
  <a-modal v-model:open="visible" title="表单规则配置" width="1200px" :confirm-loading="confirmLoading" @ok="handleSave" @cancel="handleCancel">
    <div class="form-rule-config">
      <a-tabs v-model:activeKey="activeTab" type="card">
        <!-- 字段规则配置 -->
        <a-tab-pane key="fields" tab="字段规则">
          <div class="field-rules-panel">
            <div class="toolbar">
              <a-space>
                <a-button type="primary" @click="addFieldRule">
                  <PlusOutlined />
                  添加字段规则
                </a-button>
                <a-button @click="importFromForm">
                  <ImportOutlined />
                  从表单导入
                </a-button>
              </a-space>
            </div>

            <a-table :columns="fieldRuleColumns" :data-source="fieldRules" :pagination="false" size="small" bordered>
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.key === 'isRequired'">
                  <a-switch v-model:checked="record.isRequired" size="small" />
                </template>
                <template v-else-if="column.key === 'visible'">
                  <a-switch v-model:checked="record.visible" size="small" :default-checked="true" />
                </template>
                <template v-else-if="column.key === 'disabled'">
                  <a-switch v-model:checked="record.disabled" size="small" />
                </template>
                <template v-else-if="column.key === 'actions'">
                  <a-space>
                    <a-button type="link" size="small" @click="editFieldRule(record, index)"> 编辑 </a-button>
                    <a-button type="link" size="small" danger @click="deleteFieldRule(index)"> 删除 </a-button>
                  </a-space>
                </template>
              </template>
            </a-table>
          </div>
        </a-tab-pane>

        <!-- 联动规则配置 -->
        <a-tab-pane key="dependencies" tab="联动规则">
          <div class="dependency-rules-panel">
            <div class="toolbar">
              <a-space>
                <a-button type="primary" @click="addDependencyRule">
                  <PlusOutlined />
                  添加联动规则
                </a-button>
                <a-button @click="previewRules">
                  <EyeOutlined />
                  预览效果
                </a-button>
              </a-space>
            </div>

            <a-table :columns="dependencyRuleColumns" :data-source="dependencyRules" :pagination="false" size="small" bordered>
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.key === 'condition'">
                  <a-tag color="blue"> {{ record.sourceField }} {{ getConditionText(record.conditionType) }} {{ record.conditionValue }} </a-tag>
                </template>
                <template v-else-if="column.key === 'action'">
                  <a-tag color="green"> {{ record.targetField }} {{ getDependencyTypeText(record.dependencyType) }} </a-tag>
                </template>
                <template v-else-if="column.key === 'actions'">
                  <a-space>
                    <a-button type="link" size="small" @click="editDependencyRule(record, index)"> 编辑 </a-button>
                    <a-button type="link" size="small" danger @click="deleteDependencyRule(index)"> 删除 </a-button>
                  </a-space>
                </template>
              </template>
            </a-table>
          </div>
        </a-tab-pane>

        <!-- 预览测试 -->
        <a-tab-pane key="preview" tab="预览测试">
          <div class="preview-panel">
            <a-alert
              message="预览模式"
              description="在此可以测试配置的规则效果，修改下方字段值观察联动效果"
              type="info"
              show-icon
              style="margin-bottom: 16px"
            />

            <a-form ref="previewFormRef" :model="previewFormData" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <a-row :gutter="16">
                <a-col v-for="field in fieldRules" :key="field.fieldCode" :span="12" v-show="isPreviewFieldVisible(field.fieldCode)">
                  <a-form-item :label="field.fieldName" :name="field.fieldCode" :rules="getPreviewFieldRules(field.fieldCode)">
                    <a-input
                      v-model:value="previewFormData[field.fieldCode]"
                      :placeholder="`请输入${field.fieldName}`"
                      :disabled="isPreviewFieldDisabled(field.fieldCode)"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 字段规则编辑弹窗 -->
    <FieldRuleEditModal v-model:open="fieldRuleEditVisible" :field-rule="currentFieldRule" @save="handleFieldRuleSave" />

    <!-- 联动规则编辑弹窗 -->
    <DependencyRuleEditModal
      v-model:open="dependencyRuleEditVisible"
      :dependency-rule="currentDependencyRule"
      :field-options="fieldOptions"
      @save="handleDependencyRuleSave"
    />
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import { PlusOutlined, ImportOutlined, EyeOutlined } from '@ant-design/icons-vue';
  import type { FormRuleConfig, FieldRule, DependencyRule } from '/@/utils/formRule/FormRuleEngine';
  import { FormRuleEngine } from '/@/utils/formRule/FormRuleEngine';
  import FieldRuleEditModal from './FieldRuleEditModal.vue';
  import DependencyRuleEditModal from './DependencyRuleEditModal.vue';

  interface Props {
    formCode: string;
    formName?: string;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<{
    save: [config: FormRuleConfig];
    cancel: [];
  }>();

  // 状态管理
  const visible = ref(false);
  const confirmLoading = ref(false);
  const activeTab = ref('fields');

  // 规则数据
  const fieldRules = ref<FieldRule[]>([]);
  const dependencyRules = ref<DependencyRule[]>([]);
  const ruleVersion = ref(1);

  // 编辑状态
  const fieldRuleEditVisible = ref(false);
  const dependencyRuleEditVisible = ref(false);
  const currentFieldRule = ref<FieldRule | null>(null);
  const currentDependencyRule = ref<DependencyRule | null>(null);
  const currentEditIndex = ref(-1);

  // 预览相关
  const previewFormData = reactive<Record<string, any>>({});
  const previewFormRef = ref();
  const previewEngine = ref<FormRuleEngine | null>(null);

  // 表格列定义
  const fieldRuleColumns = [
    { title: '字段代码', dataIndex: 'fieldCode', key: 'fieldCode', width: 120 },
    { title: '字段名称', dataIndex: 'fieldName', key: 'fieldName', width: 120 },
    { title: '必填', dataIndex: 'isRequired', key: 'isRequired', width: 80, align: 'center' },
    { title: '可见', dataIndex: 'visible', key: 'visible', width: 80, align: 'center' },
    { title: '禁用', dataIndex: 'disabled', key: 'disabled', width: 80, align: 'center' },
    { title: '必填提示', dataIndex: 'requiredMessage', key: 'requiredMessage', ellipsis: true },
    { title: '操作', key: 'actions', width: 120, align: 'center' },
  ];

  const dependencyRuleColumns = [
    { title: 'ID', dataIndex: 'id', key: 'id', width: 100 },
    { title: '条件', key: 'condition', width: 200 },
    { title: '动作', key: 'action', width: 200 },
    { title: '优先级', dataIndex: 'priority', key: 'priority', width: 80, align: 'center' },
    { title: '操作', key: 'actions', width: 120, align: 'center' },
  ];

  // 计算属性
  const fieldOptions = computed(() => {
    return fieldRules.value.map((field) => ({
      label: field.fieldName,
      value: field.fieldCode,
    }));
  });

  // 打开弹窗
  const open = async (config?: FormRuleConfig) => {
    visible.value = true;

    if (config) {
      fieldRules.value = [...config.fieldRules];
      dependencyRules.value = [...config.dependencyRules];
      ruleVersion.value = config.version;
    } else {
      fieldRules.value = [];
      dependencyRules.value = [];
      ruleVersion.value = 1;
    }

    // 初始化预览数据
    initPreviewData();
  };

  // 初始化预览数据
  const initPreviewData = () => {
    fieldRules.value.forEach((field) => {
      previewFormData[field.fieldCode] = '';
    });

    // 创建预览引擎
    const previewConfig: FormRuleConfig = {
      formCode: props.formCode + '_preview',
      formName: props.formName + '(预览)',
      version: ruleVersion.value,
      fieldRules: fieldRules.value,
      dependencyRules: dependencyRules.value,
      lastUpdated: Date.now(),
    };

    previewEngine.value = new FormRuleEngine(previewConfig.formCode, previewFormData);
    previewEngine.value.updateRuleConfig(previewConfig);
  };

  // 字段规则操作
  const addFieldRule = () => {
    currentFieldRule.value = {
      fieldCode: '',
      fieldName: '',
      isRequired: false,
      visible: true,
      disabled: false,
      validationRules: [],
    };
    currentEditIndex.value = -1;
    fieldRuleEditVisible.value = true;
  };

  const editFieldRule = (rule: FieldRule, index: number) => {
    currentFieldRule.value = { ...rule };
    currentEditIndex.value = index;
    fieldRuleEditVisible.value = true;
  };

  const deleteFieldRule = (index: number) => {
    fieldRules.value.splice(index, 1);
    initPreviewData();
  };

  const handleFieldRuleSave = (rule: FieldRule) => {
    if (currentEditIndex.value >= 0) {
      fieldRules.value[currentEditIndex.value] = rule;
    } else {
      fieldRules.value.push(rule);
    }
    fieldRuleEditVisible.value = false;
    initPreviewData();
  };

  // 联动规则操作
  const addDependencyRule = () => {
    currentDependencyRule.value = {
      id: `dep_${Date.now()}`,
      sourceField: '',
      targetField: '',
      dependencyType: 'required',
      conditionType: 'equals',
      conditionValue: '',
      priority: 0,
    };
    currentEditIndex.value = -1;
    dependencyRuleEditVisible.value = true;
  };

  const editDependencyRule = (rule: DependencyRule, index: number) => {
    currentDependencyRule.value = { ...rule };
    currentEditIndex.value = index;
    dependencyRuleEditVisible.value = true;
  };

  const deleteDependencyRule = (index: number) => {
    dependencyRules.value.splice(index, 1);
    initPreviewData();
  };

  const handleDependencyRuleSave = (rule: DependencyRule) => {
    if (currentEditIndex.value >= 0) {
      dependencyRules.value[currentEditIndex.value] = rule;
    } else {
      dependencyRules.value.push(rule);
    }
    dependencyRuleEditVisible.value = false;
    initPreviewData();
  };

  // 预览相关方法
  const isPreviewFieldVisible = (fieldCode: string): boolean => {
    return previewEngine.value?.isFieldVisible(fieldCode) ?? true;
  };

  const isPreviewFieldDisabled = (fieldCode: string): boolean => {
    return previewEngine.value?.isFieldDisabled(fieldCode) ?? false;
  };

  const getPreviewFieldRules = (fieldCode: string): any[] => {
    return previewEngine.value?.getFieldValidationRules(fieldCode) ?? [];
  };

  // 工具方法
  const getConditionText = (conditionType: string): string => {
    const map = {
      equals: '等于',
      not_equals: '不等于',
      in: '包含于',
      not_in: '不包含于',
      greater: '大于',
      less: '小于',
      empty: '为空',
      not_empty: '不为空',
    };
    return map[conditionType] || conditionType;
  };

  const getDependencyTypeText = (dependencyType: string): string => {
    const map = {
      required: '必填',
      visible: '可见',
      disabled: '禁用',
      options: '选项',
    };
    return map[dependencyType] || dependencyType;
  };

  // 保存配置
  const handleSave = async () => {
    try {
      confirmLoading.value = true;

      const config: FormRuleConfig = {
        formCode: props.formCode,
        formName: props.formName || props.formCode,
        version: ruleVersion.value + 1,
        fieldRules: fieldRules.value,
        dependencyRules: dependencyRules.value,
        lastUpdated: Date.now(),
      };

      emit('save', config);
      visible.value = false;
      message.success('保存成功');
    } catch (error) {
      message.error('保存失败');
      console.error(error);
    } finally {
      confirmLoading.value = false;
    }
  };

  const handleCancel = () => {
    visible.value = false;
    emit('cancel');
  };

  // 从表单导入字段
  const importFromForm = () => {
    // 这里可以实现从现有表单组件中自动提取字段信息的逻辑
    message.info('从表单导入功能开发中...');
  };

  // 预览规则效果
  const previewRules = () => {
    activeTab.value = 'preview';
    initPreviewData();
  };

  // 监听预览数据变化
  watch(
    previewFormData,
    () => {
      if (previewEngine.value) {
        // 触发规则重新计算
        previewEngine.value.evaluateAllRules?.();
      }
    },
    { deep: true }
  );

  defineExpose({
    open,
  });
</script>

<style lang="less" scoped>
  .form-rule-config {
    .toolbar {
      margin-bottom: 16px;
      padding: 12px;
      background: #fafafa;
      border-radius: 6px;
    }

    .field-rules-panel,
    .dependency-rules-panel {
      min-height: 400px;
    }

    .preview-panel {
      min-height: 400px;
      padding: 16px;
      background: #fafafa;
      border-radius: 6px;
    }
  }

  :deep(.ant-table-tbody > tr > td) {
    padding: 8px;
  }

  :deep(.ant-form-item) {
    margin-bottom: 16px;
  }
</style>
