<template>
  <a-modal v-model:open="visible" title="字段规则编辑" width="800px" :confirm-loading="confirmLoading" @ok="handleSave" @cancel="handleCancel">
    <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
      <a-form-item label="字段代码" name="fieldCode">
        <a-input v-model:value="formData.fieldCode" placeholder="请输入字段代码，如：name, phone" />
      </a-form-item>

      <a-form-item label="字段名称" name="fieldName">
        <a-input v-model:value="formData.fieldName" placeholder="请输入字段显示名称，如：姓名, 电话" />
      </a-form-item>

      <a-form-item label="基础设置">
        <a-space direction="vertical" style="width: 100%">
          <a-checkbox v-model:checked="formData.isRequired"> 必填字段 </a-checkbox>
          <a-checkbox v-model:checked="formData.visible"> 默认可见 </a-checkbox>
          <a-checkbox v-model:checked="formData.disabled"> 默认禁用 </a-checkbox>
        </a-space>
      </a-form-item>

      <a-form-item label="必填提示信息" name="requiredMessage">
        <a-input v-model:value="formData.requiredMessage" placeholder="请输入必填时的提示信息" />
      </a-form-item>

      <a-form-item label="验证规则">
        <div class="validation-rules">
          <div v-for="(rule, index) in formData.validationRules" :key="index" class="validation-rule-item">
            <a-row :gutter="8" align="middle">
              <a-col :span="6">
                <a-select v-model:value="rule.type" placeholder="规则类型" size="small">
                  <a-select-option value="required">必填</a-select-option>
                  <a-select-option value="pattern">正则</a-select-option>
                  <a-select-option value="min">最小长度</a-select-option>
                  <a-select-option value="max">最大长度</a-select-option>
                  <a-select-option value="custom">自定义</a-select-option>
                </a-select>
              </a-col>
              <a-col :span="6">
                <a-input v-if="rule.type === 'pattern'" v-model:value="rule.value" placeholder="正则表达式" size="small" />
                <a-input-number
                  v-else-if="rule.type === 'min' || rule.type === 'max'"
                  v-model:value="rule.value"
                  placeholder="数值"
                  size="small"
                  style="width: 100%"
                />
                <a-input v-else-if="rule.type === 'custom'" v-model:value="rule.value" placeholder="自定义函数名" size="small" />
              </a-col>
              <a-col :span="10">
                <a-input v-model:value="rule.message" placeholder="错误提示信息" size="small" />
              </a-col>
              <a-col :span="2">
                <a-button type="text" danger size="small" @click="removeValidationRule(index)">
                  <DeleteOutlined />
                </a-button>
              </a-col>
            </a-row>
          </div>

          <a-button type="dashed" block size="small" @click="addValidationRule">
            <PlusOutlined />
            添加验证规则
          </a-button>
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, reactive, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue';
  import type { FieldRule, ValidationRule } from '/@/utils/formRule/FormRuleEngine';

  interface Props {
    fieldRule?: FieldRule | null;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<{
    save: [rule: FieldRule];
  }>();

  // 状态管理
  const visible = ref(false);
  const confirmLoading = ref(false);
  const formRef = ref();

  // 表单数据
  const formData = reactive<FieldRule>({
    fieldCode: '',
    fieldName: '',
    isRequired: false,
    visible: true,
    disabled: false,
    requiredMessage: '',
    validationRules: [],
  });

  // 表单验证规则
  const rules = {
    fieldCode: [
      { required: true, message: '请输入字段代码' },
      { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '字段代码必须以字母开头，只能包含字母、数字和下划线' },
    ],
    fieldName: [{ required: true, message: '请输入字段名称' }],
  };

  // 监听props变化
  watch(
    () => props.fieldRule,
    (newRule) => {
      if (newRule) {
        Object.assign(formData, {
          fieldCode: newRule.fieldCode || '',
          fieldName: newRule.fieldName || '',
          isRequired: newRule.isRequired || false,
          visible: newRule.visible !== false,
          disabled: newRule.disabled || false,
          requiredMessage: newRule.requiredMessage || '',
          validationRules: newRule.validationRules ? [...newRule.validationRules] : [],
        });
      }
    },
    { immediate: true, deep: true }
  );

  // 添加验证规则
  const addValidationRule = () => {
    formData.validationRules = formData.validationRules || [];
    formData.validationRules.push({
      type: 'required',
      value: '',
      message: '',
    });
  };

  // 删除验证规则
  const removeValidationRule = (index: number) => {
    formData.validationRules?.splice(index, 1);
  };

  // 保存
  const handleSave = async () => {
    try {
      await formRef.value.validate();
      confirmLoading.value = true;

      // 清理空的验证规则
      const cleanedRules = formData.validationRules?.filter((rule) => rule.type && rule.message) || [];

      const ruleToSave: FieldRule = {
        ...formData,
        validationRules: cleanedRules,
      };

      emit('save', ruleToSave);
      message.success('保存成功');
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      confirmLoading.value = false;
    }
  };

  // 取消
  const handleCancel = () => {
    visible.value = false;
  };

  // 重置表单
  const resetForm = () => {
    Object.assign(formData, {
      fieldCode: '',
      fieldName: '',
      isRequired: false,
      visible: true,
      disabled: false,
      requiredMessage: '',
      validationRules: [],
    });
    formRef.value?.resetFields();
  };

  defineExpose({
    resetForm,
  });
</script>

<style lang="less" scoped>
  .validation-rules {
    .validation-rule-item {
      margin-bottom: 8px;
      padding: 8px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      background: #fafafa;
    }
  }

  :deep(.ant-form-item) {
    margin-bottom: 16px;
  }
</style>
