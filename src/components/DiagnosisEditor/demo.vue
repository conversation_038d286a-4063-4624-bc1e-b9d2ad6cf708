<template>
  <div class="diagnosis-editor-demo">
    <h2>诊断编辑器示例</h2>
    
    <div class="demo-section">
      <h3>基本使用</h3>
      <DiagnosisEditor v-model="diagnosisList" @change="handleChange" />
      
      <div class="result-panel">
        <h4>当前诊断列表：</h4>
        <a-list
          size="small"
          bordered
          :data-source="diagnosisList"
        >
          <template #renderItem="{ item, index }">
            <a-list-item>
              <span class="list-index">{{ index + 1 }}.</span>
              <span>{{ item || '(空)' }}</span>
            </a-list-item>
          </template>
          <template #header>
            <div>共 {{ diagnosisList.length }} 项诊断</div>
          </template>
        </a-list>
      </div>
    </div>
    
    <div class="demo-section">
      <h3>带初始值</h3>
      <DiagnosisEditor v-model="initialDiagnosisList" @change="handleInitialChange" />
    </div>
    
    <div class="demo-section">
      <h3>功能说明</h3>
      <div class="instructions">
        <p><strong>主要功能：</strong></p>
        <ul>
          <li>每行一个诊断，支持自由文本输入和ICD10编码搜索</li>
          <li>拖拽排序：通过左侧拖动图标可以调整诊断顺序</li>
          <li>添加行：点击底部"添加诊断"按钮添加新行</li>
          <li>插入行：点击行右侧"+"按钮在当前行后插入新行</li>
          <li>删除行：点击行右侧删除按钮删除当前行</li>
          <li>自动维护行号：拖拽排序后自动更新行号</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import DiagnosisEditor from './index.vue';

// 空白诊断列表
const diagnosisList = ref<string[]>([]);

// 带初始值的诊断列表
const initialDiagnosisList = ref<string[]>([
  '高血压',
  '2型糖尿病',
  '冠心病',
  '高脂血症'
]);

// 处理变化事件
const handleChange = (list: string[]) => {
  console.log('诊断列表变化:', list);
};

// 处理初始值变化事件
const handleInitialChange = (list: string[]) => {
  console.log('初始诊断列表变化:', list);
};
</script>

<style scoped>
.diagnosis-editor-demo {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 30px;
  border: 1px solid #eee;
  padding: 20px;
  border-radius: 4px;
}

h2 {
  margin-bottom: 20px;
  color: #1890ff;
}

h3 {
  margin-bottom: 15px;
  color: #333;
}

h4 {
  margin-bottom: 10px;
  color: #666;
}

.result-panel {
  margin-top: 20px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 4px;
}

.list-index {
  margin-right: 8px;
  color: #999;
}

.instructions {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid #1890ff;
}

.instructions ul {
  padding-left: 20px;
  margin: 10px 0;
}
</style>
