<template>
  <div class="diagnosis-editor">
    <div class="editor-header">
      <div class="header-line-number">序号</div>
      <div class="header-content">诊断内容</div>
      <div class="header-actions">
        <span class="header-action-item">操作</span>
      </div>
    </div>
    <div class="editor-body">
      <draggable
        v-model="diagnosisList"
        item-key="id"
        ghost-class="ghost-item"
        chosen-class="chosen-item"
        drag-class="drag-item"
        animation="200"
        @start="handleDragStart"
        @end="handleDragEnd"
        @change="(e) => (dragCurrentIndex = e.moved ? e.moved.newIndex : dragCurrentIndex)"
      >
        <template #item="{ element, index }">
          <div class="diagnosis-item drag-handle">
            <div class="line-number">
              <span class="number-badge">{{ index + 1 }}</span>
            </div>
            <div class="diagnosis-content" :data-index="index">
              <ICD10Input v-model="element.content" @change="(val) => handleContentChange(index, val)" :readOnly="readOnly" />
            </div>
            <div class="diagnosis-actions" v-if="!readOnly">
              <a-dropdown :trigger="['click']">
                <span class="action-icon more-actions">
                  <EllipsisOutlined />
                </span>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="insertLine(index)"> <PlusOutlined /> 插入 </a-menu-item>
                    <a-menu-item @click="removeLine(index)" class="delete-menu-item"> <MinusOutlined /> 删除 </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
          </div>
        </template>
      </draggable>

      <div v-if="diagnosisList.length === 0" class="empty-state">
        <a-empty description="暂无诊断信息" />
      </div>

      <div class="add-line-container" v-if="!readOnly">
        <a-button type="dashed" block @click="addLine">
          <template #icon><PlusOutlined /></template>
          添加诊断
        </a-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, watch, defineProps, defineEmits, onMounted, h } from 'vue';
  import { PlusOutlined, MinusOutlined, EllipsisOutlined } from '@ant-design/icons-vue';
  import draggable from 'vuedraggable';
  import ICD10Input from '../ICD10Input/index.vue';

  interface DiagnosisItem {
    id: string;
    content: string;
  }

  const props = defineProps({
    modelValue: {
      type: Array as () => string[],
      default: () => [],
    },
    readOnly: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['update:modelValue', 'change']);

  // 生成唯一ID
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  };

  // 内部诊断列表数据
  const diagnosisList = ref<DiagnosisItem[]>([]);

  // 初始化数据
  onMounted(() => {
    if (props.modelValue && props.modelValue.length > 0) {
      diagnosisList.value = props.modelValue.map((content) => ({
        id: generateId(),
        content,
      }));
    } else {
      // 默认添加一个空行
      addLine();
    }

    // 添加回车键事件监听器
    const setupEnterKeyListeners = () => {
      // 查找所有诊断内容容器并添加事件监听器
      const contentDivs = document.querySelectorAll('.diagnosis-content');
      contentDivs.forEach((contentDiv, idx) => {
        // 检查是否已经添加了事件监听器
        if (!(contentDiv as any).__hasKeyListener) {
          console.log(`DiagnosisEditor: Adding keydown listener to diagnosis-content at index ${idx}`);

          // 获取索引
          const index = parseInt(contentDiv.getAttribute('data-index') || '0', 10);

          // 添加事件监听器到内容容器
          contentDiv.addEventListener(
            'keydown',
            (e: KeyboardEvent) => {
              // 如果事件来自下拉选择器，且下拉菜单打开，则不处理箭头键
              const dropdown = document.querySelector('.ant-select-dropdown:not(.ant-select-dropdown-hidden)');

              // 处理回车键
              if (e.key === 'Enter') {
                // 如果下拉菜单打开，先选中当前项，然后在下一个事件循环中添加新行
                if (dropdown) {
                  console.log('DiagnosisEditor: Dropdown is open, handling enter key anyway');
                  // 先让默认行为发生（选中项目），然后在下一个事件循环中添加新行
                  setTimeout(() => {
                    console.log(`DiagnosisEditor: Adding new line after dropdown selection at index ${index}`);
                    handleEnterPress(index);
                  }, 0);
                  return;
                }

                // 阻止默认行为
                e.preventDefault();
                e.stopPropagation();

                console.log(`DiagnosisEditor: Enter key pressed on input at index ${index}`);
                handleEnterPress(index);
              }
              // 处理删除键
              else if (e.key === 'Delete' || e.key === 'Backspace') {
                // 获取当前行的内容
                const content = diagnosisList.value[index]?.content || '';

                // 获取输入元素
                const input = contentDiv.querySelector('input') as HTMLInputElement;
                if (!input) return;

                // 如果内容为空
                if (content.trim() === '') {
                  // 如果是Backspace键，需要检查光标位置
                  /* if (e.key === 'Backspace') {
                    // 获取光标位置，如果不在开头，则不处理
                    const selectionStart = input.selectionStart;
                    if (selectionStart !== 0) {
                      return;
                    }
                  }*/

                  // 如果只有一行，不删除
                  /* if (diagnosisList.value.length <= 1) {
                    return;
                  }*/

                  // 阻止默认行为
                  e.preventDefault();
                  e.stopPropagation();

                  console.log(`DiagnosisEditor: Deleting empty line at index ${index} with ${e.key} key`);

                  // 保存要聚焦的索引，在删除行之前计算
                  const focusIndex = e.key === 'Backspace' && index > 0 ? index - 1 : index < diagnosisList.value.length - 1 ? index : index - 1;

                  // 删除当前行
                  diagnosisList.value.splice(index, 1);

                  // 确保聚焦索引有效
                  const validFocusIndex = Math.max(0, Math.min(focusIndex, diagnosisList.value.length - 1));

                  // 使用更短的延迟来更快地重新聚焦
                  setTimeout(() => {
                    console.log(`DiagnosisEditor: Refocusing to index ${validFocusIndex} after deletion`);
                    focusInput(validFocusIndex);
                  }, 0);
                }
              }
            },
            true
          ); // 使用捕获阶段，确保在子元素处理前捕获事件

          // 标记该容器已添加事件监听器
          (contentDiv as any).__hasKeyListener = true;
        }
      });
    };

    // 初始设置监听器
    setupEnterKeyListeners();

    // 使用MutationObserver监听内容容器的变化
    const observer = new MutationObserver((mutations) => {
      // 只在有相关变化时才设置监听器
      const hasRelevantChanges = mutations.some((mutation) => {
        // 检查是否有新的诊断内容容器添加
        return Array.from(mutation.addedNodes).some((node) => {
          return (node as HTMLElement).classList?.contains('diagnosis-content') || (node as HTMLElement).querySelector?.('.diagnosis-content');
        });
      });

      if (hasRelevantChanges) {
        console.log('DiagnosisEditor: Detected new diagnosis-content elements, setting up listeners');
        setupEnterKeyListeners();
      }
    });

    // 开始监听组件内的变化
    const editorElement = document.querySelector('.diagnosis-editor');
    if (editorElement) {
      observer.observe(editorElement, {
        childList: true,
        subtree: true,
      });
    }

    // 监听数据变化，当数据变化时重新设置监听器
    watch(
      diagnosisList,
      () => {
        setTimeout(setupEnterKeyListeners, 50);
      },
      { deep: true }
    );

    // 组件卸载时停止监听
    return () => {
      observer.disconnect();
    };
  });

  // 监听外部数据变化
  watch(
    () => props.modelValue,
    (newVal) => {
      if (newVal && JSON.stringify(newVal) !== JSON.stringify(diagnosisList.value.map((item) => item.content))) {
        diagnosisList.value = newVal.map((content) => ({
          id: generateId(),
          content,
        }));
      }
    },
    { deep: true }
  );

  // 监听内部数据变化，更新modelValue
  watch(
    diagnosisList,
    (newVal) => {
      const contentList = newVal.map((item) => item.content);
      emit('update:modelValue', contentList);
      emit('change', contentList);
    },
    { deep: true }
  );

  // 添加新行
  const addLine = () => {
    diagnosisList.value.push({
      id: generateId(),
      content: '',
    });
  };

  // 在指定位置插入行
  const insertLine = (index: number) => {
    diagnosisList.value.splice(index + 1, 0, {
      id: generateId(),
      content: '',
    });
  };

  // 删除行
  const removeLine = (index: number) => {
    diagnosisList.value.splice(index, 1);

    // 如果删除后没有行了，自动添加一个空行
    if (diagnosisList.value.length === 0) {
      addLine();
    }
  };

  // 处理内容变化
  const handleContentChange = (index: number, value: string) => {
    // 更新内容
    diagnosisList.value[index].content = value;

    // 注意：我们不再在这里自动删除空行
    // 而是让用户通过删除键来明确删除空行
    // 这样可以避免在用户删除内容时意外失去焦点
  };

  // 处理回车键按下事件
  const handleEnterPress = (index: number) => {
    console.log('DiagnosisEditor: handleEnterPress called for index', index);

    // 检查下一行是否存在且为空
    const hasEmptyNextLine =
      index + 1 < diagnosisList.value.length && (!diagnosisList.value[index + 1].content || diagnosisList.value[index + 1].content.trim() === '');

    if (hasEmptyNextLine) {
      console.log('DiagnosisEditor: Next line is already empty, focusing it instead of adding new line');
      // 直接聚焦到下一行
      focusInput(index + 1);
      return;
    }

    // 在当前行后插入新行
    insertLine(index);
    console.log('DiagnosisEditor: New line inserted after index', index);

    // 聚焦到新插入的行
    focusInput(index + 1);
  };

  // 聚焦到指定索引的输入框
  const focusInput = (index: number, position?: 'start' | 'end') => {
    // 确保索引有效
    if (index < 0 || index >= diagnosisList.value.length) {
      console.log(`DiagnosisEditor: Invalid focus index ${index}, adjusting to valid range`);
      index = Math.max(0, Math.min(index, diagnosisList.value.length - 1));
    }

    const focusInputAtIndex = () => {
      const inputs = document.querySelectorAll('.diagnosis-content .ant-select-selector input');
      console.log('DiagnosisEditor: Found inputs:', inputs.length, 'trying to focus index:', index);
      if (inputs && inputs[index]) {
        console.log('DiagnosisEditor: Focusing input at index', index, 'position:', position);
        const input = inputs[index] as HTMLInputElement;
        input.focus();

        // 如果指定了光标位置
        if (position) {
          try {
            // 设置光标位置
            if (position === 'start') {
              input.setSelectionRange(0, 0);
            } else if (position === 'end') {
              const length = input.value.length;
              input.setSelectionRange(length, length);
            }
          } catch (error) {
            console.error('DiagnosisEditor: Error setting cursor position:', error);
          }
        }

        return true;
      } else {
        console.log('DiagnosisEditor: Could not find input at index', index);
        return false;
      }
    };

    // 尝试聚焦，如果失败，则重试几次
    let attempts = 0;
    const maxAttempts = 5;
    const tryFocus = () => {
      setTimeout(
        () => {
          if (!focusInputAtIndex() && attempts < maxAttempts) {
            attempts++;
            console.log(`DiagnosisEditor: Retrying focus, attempt ${attempts}`);
            tryFocus();
          }
        },
        10 * (attempts + 1) // 减少重试间隔，加快响应速度
      );
    };

    tryFocus();
  };

  // 拖拽状态
  const isDragging = ref(false);
  const dragStartIndex = ref(-1);
  const dragCurrentIndex = ref(-1);

  // 处理拖拽开始
  const handleDragStart = (e) => {
    isDragging.value = true;
    document.body.classList.add('diagnosis-dragging');
    // 记录开始拖拽的索引
    dragStartIndex.value = e.oldIndex;
    dragCurrentIndex.value = e.oldIndex;

    // 添加声音反馈
    playDragSound('start');
  };

  // 处理拖拽结束
  const handleDragEnd = (e) => {
    isDragging.value = false;
    document.body.classList.remove('diagnosis-dragging');

    // 如果位置发生变化，添加声音反馈
    if (e.oldIndex !== e.newIndex) {
      playDragSound('drop');
    }

    // 重置拖拽索引
    dragStartIndex.value = -1;
    dragCurrentIndex.value = -1;

    // 拖拽结束后，更新行号不需要额外处理
    // 因为行号是根据数组索引动态计算的
  };

  // 模拟声音反馈
  const playDragSound = (type) => {
    // 在真实应用中，可以使用真实的声音文件
    // 这里仅作为示例，不实际播放声音
    console.log(`Playing ${type} sound`);
    // 如果需要实际声音，可以使用以下代码：
    // const audio = new Audio(type === 'start' ? '/sounds/drag-start.mp3' : '/sounds/drag-drop.mp3');
    // audio.play();
  };
</script>

<style scoped>
  .diagnosis-editor {
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  }

  .editor-header {
    display: flex;
    background-color: #fafafa;
    padding: 6px 4px;
    font-weight: 500;
    border-bottom: 1px solid #f0f0f0;
    font-size: 11px;
    color: #666;
  }

  .header-line-number {
    width: 30px;
    flex-shrink: 0;
    padding-left: 4px;
    font-size: 11px;
  }

  .header-content {
    flex: 1;
  }

  .header-actions {
    width: 30px;
    display: flex;
    justify-content: flex-end;
    padding-right: 4px;
  }

  .header-action-item {
    font-size: 10px;
    color: #999;
    width: 20px;
    text-align: center;
  }

  .editor-body {
    padding: 6px 6px 10px;
  }

  .diagnosis-item {
    display: flex;
    align-items: center;
    padding: 0;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.2s;
    margin-bottom: 1px;
    position: relative;
    cursor: grab;
    user-select: none; /* 防止拖拽时选中文本 */
  }

  /* 添加拖拽指示器 */
  .diagnosis-item::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 2px;
    background-color: transparent;
    transition: background-color 0.2s;
  }

  .diagnosis-item:hover {
    background-color: #fafafa;
  }

  /* 在拖拽状态下才添加阴影效果，避免影响操作按钮 */
  :global(.diagnosis-dragging) .diagnosis-item:hover {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
  }

  .line-number {
    width: 24px;
    padding: 0 2px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
  }

  .number-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 18px;
    height: 18px;
    padding: 0 2px;
    font-size: 11px;
    line-height: 18px;
    background-color: #f5f5f5;
    border-radius: 9px;
    color: #666;
    border: 1px solid #e8e8e8;
  }

  .drag-handle {
    cursor: grab;
    color: #bfbfbf;
  }

  :global(.diagnosis-dragging .drag-handle) {
    cursor: grabbing;
  }

  .drag-handle:hover {
    color: #1890ff;
    //transform: scale(1.05);
    background-color: #f0f0f0;
    border-color: #e0e0e0;
  }

  .diagnosis-content {
    flex: 1;
  }

  .diagnosis-actions {
    width: 24px;
    display: flex;
    justify-content: flex-end;
    color: #aaa;
    z-index: 20;
    position: relative;
  }

  .action-icon {
    cursor: pointer;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 4px;
    background-color: #f9f9f9;
    border: 1px solid #e8e8e8;
    font-size: 12px;
  }

  .more-actions {
    color: #bfbfbf;
  }

  .more-actions:hover {
    color: #1890ff;
  }

  .action-icon:hover {
    transform: scale(1.05);
    background-color: #f0f0f0;
    color: #1890ff;
    border-color: #e0e0e0;
    z-index: 25;
  }

  /* 为删除菜单项添加特殊样式 */
  :deep(.ant-dropdown-menu) {
    min-width: 120px;
  }

  :deep(.ant-dropdown-menu-item) {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  :deep(.delete-menu-item) {
    color: #ff4d4f;
  }

  :deep(.delete-menu-item:hover) {
    background-color: #fff1f0;
  }

  .ghost-item {
    opacity: 0.7;
    background: #f0f7ff;
    border: 1px dashed #1890ff;
    border-radius: 4px;
    box-shadow: 0 0 5px rgba(24, 144, 255, 0.2);
    position: relative;
    z-index: 5;
  }

  .ghost-item::before {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #1890ff;
    top: -3px;
    opacity: 0.8;
  }

  .chosen-item {
    background-color: #fafafa;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
    z-index: 10;
    border: 1px solid #e6f7ff;
    border-radius: 4px;
  }

  .chosen-item .number-badge {
    background-color: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
    min-width: 18px;
    height: 18px;
  }

  .chosen-item.drag-handle {
    color: #1890ff;
    cursor: grabbing;
  }

  .drag-item {
    opacity: 0.8;
    transform: scale(1.02);
    background-color: #fff;
  }

  .add-line-container {
    margin-top: 16px;
  }

  .add-line-container :deep(.ant-btn) {
    transition: all 0.2s;
    height: 32px;
    font-size: 13px;
  }

  .add-line-container :deep(.ant-btn:hover) {
    transform: scale(1.02);
    border-color: #1890ff;
    color: #1890ff;
  }

  .empty-state {
    padding: 24px 0;
    text-align: center;
  }
  /* 全局拖拽样式 */
  :global(.diagnosis-dragging) {
    cursor: grabbing !important;
  }

  :global(.diagnosis-dragging .diagnosis-item:not(.chosen-item):not(.ghost-item)) {
    position: relative;
    transition: transform 0.2s;
  }

  :global(.diagnosis-dragging .diagnosis-item:not(.chosen-item):not(.ghost-item):hover) {
    transform: translateY(2px);
  }

  /* 确保操作按钮始终保持在原位置 */
  :global(.diagnosis-dragging .diagnosis-actions) {
    transform: none !important;
    pointer-events: none; /* 拖拽时禁用操作按钮交互，避免干扰 */
  }

  /* 拖拽结束后恢复操作按钮交互 */
  .diagnosis-actions {
    pointer-events: auto;
  }

  :global(.diagnosis-dragging .diagnosis-item:not(.chosen-item):not(.ghost-item):hover::before) {
    content: '';
    position: absolute;
    top: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #1890ff;
    animation: pulse 1.5s infinite;
    z-index: 20;
  }

  /* 拖拽时的插入指示器 */
  :global(.diagnosis-dragging .diagnosis-item:not(.chosen-item):not(.ghost-item):hover::after) {
    content: '▼';
    position: absolute;
    left: -20px;
    top: 50%;
    transform: translateY(-50%);
    color: #1890ff;
    font-size: 12px;
    animation: slideIn 0.3s ease-out;
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translate(-10px, -50%);
    }
    to {
      opacity: 1;
      transform: translateY(-50%);
    }
  }

  @keyframes pulse {
    0% {
      opacity: 0.6;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.6;
    }
  }
</style>
