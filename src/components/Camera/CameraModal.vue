<template>
  <a-modal v-bind="$attrs" title="拍照" :open="visible" @cancel="handleCancel" :footer="null" :z-index="999999999999999999999999">
    <div style="max-height: 80vh; min-height: 50vh; width: 100%; overflow: scroll; position: relative">
      <web-cam
        ref="webcam"
        :constraints="constraints"
        @photo-taken="photoTaken"
        @init="webcamInit"
        @unsupported="handleUnsupport"
        @error="handleCamError"
      />

      <div style="display: flex; width: 100%; justify-content: center; margin: 10px; padding: 10px; position: absolute; bottom: 0">
        <a-select @change="setCamera" v-model:value="currentDeviceId" style="width: 250px">
          <a-select-option value="">请选择设备</a-select-option>
          <a-select-option v-for="camera in cameras" :key="camera.deviceId" :value="camera.deviceId">{{ camera.label }} </a-select-option>
        </a-select>
        <a-button type="primary" @click="takePhoto" style="margin-left: 10px">拍照</a-button>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { defineEmits, onMounted, ref, defineExpose } from 'vue';
  import WebCam from './Webcam.vue';
  import { message } from 'ant-design-vue';

  const visible = ref(false);
  const emits = defineEmits(['onPhotoTaken']);
  const cameras = ref([]);
  const currentDeviceId = ref('');
  const webcam = ref(null);
  let reloadCamInterval: ReturnType<typeof setInterval> | null = null;

  //定义属性
  const props = defineProps({
    constraints: {
      type: Object,
      default: () => ({ video: { width: { ideal: 720 }, height: { ideal: 1280 } }, facingMode: 'environment' }),
    },
  });
  interface PhotoTakenEvent {
    image_data_url: string;
    blob: string;
  }

  const takePhoto = async () => {
    try {
      webcam.value.takePhoto();
    } catch (err) {
      console.log(err);
      message.error('拍照失败');
    }
  };

  const photoTaken = (data: PhotoTakenEvent) => {
    //let imageSrc = data.image_data_url;
    emits('onPhotoTaken', data);
    close();
  };

  const loadCameras = () => {
    webcam.value?.loadCameras().then(() => {
      cameras.value = webcam.value.cameras;
      if (cameras.value.length > 0) {
        clearInterval(reloadCamInterval);
        if (!currentDeviceId.value) {
          currentDeviceId.value = cameras.value[0].deviceId;
          webcam.value.changeCamera(currentDeviceId);
        }
      }
      console.log('Cameras loaded', cameras.value);
    });
  };

  const webcamInit = (deviceId: string) => {
    console.log('Webcam initialized', deviceId);
    currentDeviceId.value = deviceId;
  };

  const handleCamError = (err) => {
    console.log(err);
    message.error('摄像头初始化失败,' + err);
  };
  const setCamera = () => {
    webcam.value.changeCamera(currentDeviceId.value === '' ? null : currentDeviceId.value);
  };

  const handleCancel = () => {
    visible.value = false;
    webcam.value?.stop();
  };

  const handleUnsupport = (m) => {
    console.log(m);
    message.error('当前浏览器不支持拍照功能，请更换浏览器后重试');
  };

  onMounted(() => {
    cameras.value = webcam.value?.cameras || [];
    if (cameras.value.length === 0) {
      reloadCamInterval = setInterval(() => {
        loadCameras();
      }, 1000);
    }
  });

  const open = () => {
    visible.value = true;
    console.log('open', webcam.value);
    webcam.value?.start();
  };

  const close = () => {
    webcam.value?.stop();
    visible.value = false;
  };

  defineExpose({
    open,
    close,
  });
</script>
