// useJSign.js
import { ref, onBeforeUnmount } from 'vue';

// ------------------------------
// 如果你把 createNet、jSign 这些原始函数也想放在同一个文件里
// 可以直接复制到这里来使用，也可以单独拆分为其它工具模块。
// 这里只演示整合思路。
// ------------------------------

// 1. 先把 createNet 搬过来
function createNet(url: string) {
  return {
    ws: null,
    url: url,
    data: { mgr: null, callback: null },
    status: { mgr: null, callback: null },
    reconn_timer: null,
    timeout: 2000,
    connect: function () {
      const wsUrl = 'ws://' + this.url;
      this.ws = new WebSocket(wsUrl);
      this.ws.owner = this;
      this.ws.onopen = function () {
        const owner = this.owner;
        if (owner.status.callback) {
          owner.status.callback(owner.status.mgr, true);
        }
      };
      this.ws.onclose = function () {
        const owner = this.owner;
        if (owner.status.callback) {
          owner.status.callback(owner.status.mgr, false);
        }
      };
      this.ws.onmessage = function (e) {
        const owner = this.owner;
        if (owner.data.callback) {
          owner.data.callback(owner.data.mgr, e.data);
        }
      };
      this.ws.onerror = function () {
        const owner = this.owner;
        owner.disconnect();
      };
    },
    disconnect: function () {
      if (this.ws != null) {
        this.ws.close();
        this.ws = null;
      }
    },
    reconnect: function () {
      this.disconnect();
      this.connect();
    },
    do_reconnect: function (activate) {
      if (activate !== true && this.reconn_timer != null) {
        clearInterval(this.reconn_timer);
        this.reconn_timer = null;
      }
      if (activate === true && this.reconn_timer == null) {
        this.reconn_timer = setInterval(function (net) {
          console.log('net reconnect');
        }, this.timeout);
      }
    },
    getNetStatus: function () {
      if (this.ws) {
        return this.ws.readyState === WebSocket.OPEN;
      }
      return false;
    },
    setDataCallback: function (mgr, callback) {
      this.data.mgr = mgr;
      this.data.callback = callback;
    },
    setStatusCallback: function (mgr, callback) {
      this.status.mgr = mgr;
      this.status.callback = callback;
    },
    write: function (data) {
      if (this.getNetStatus()) {
        try {
          this.ws.send(data);
        } catch (e) {
          return false;
        }
        return true;
      }
      return false;
    },
  };
}

// 2. 定义一些常量
const SS_MSG_TYPE = {
  Sign: 1,
  Pdf: 2,
  Finger: 3,
  Camera: 4,
  IDCard: 5,
  IDFace: 6,
};

const SS_ETYPE = {
  Click: 'click',
  Key: 'key',
  Custom: 'custom',
};

const SS_EIDS = {
  SignCancel: 1,
  SignClear: 2,
  SignConfirm: 3,

  PdfCancel: 11,
  PdfConfirm: 16,

  FingerCancel: 21,
  FingerRescan: 22,
  FingerConfirm: 23,

  CameraCancel: 31,
  CameraCaptured: 32,
  CameraConfirm: 33,

  IDFaceVerify: 51,
  PdfError: 61,

  Status: 100,
};

const SS_IIDS = {
  Status: 1001,
  BeginSign: 1002,
  EndSign: 1003,
  ClearSign: 1004,
  SetPenSize: 1005,
  SetPenColor: 1006,
  SetBorderColor: 1007,
  SetBKColor: 1008,
  SaveSignToFile: 1009,
  GetSignBase64: 1010,
  ActivateMouse: 1011,
  MoveSignWindow: 1012,
  ResizeSignView: 1013,

  ShowWebPage: 1051,
  CloseWebPage: 1052,

  PdfBeginSign: 1101,
  PdfEndSign: 1102,
  PdfGotoPage: 1103,
  PdfWriteImage: 1104,
  PdfBeginSignFromURL: 1105,

  FingerBeginCapture: 1201,
  FingerEndCapture: 1202,
  FingerCaptureImage: 1203,

  CameraBeginCapture: 1301,
  CameraEndCapture: 1302,
  CameraCaptureImage: 1303,

  IDCardBeginCapture: 1401,
  IDCardEndCapture: 1402,
  IDCardGetInfo: 1403,

  IDFaceBeginVerify: 1501,
  IDFaceEndVerify: 1502,
};

// 3. 定义 jSign 构造函数
function jSign(url: string = '127.0.0.1:12267') {
  if (!!window.WebSocket && window.WebSocket.prototype.send) {
    this.net = createNet(url);
    return this;
  } else {
    return null;
  }
}

// 4. 给 jSign.prototype 挂载各种原始方法
jSign.prototype.Init = function (callback) {
  if (!this.net) {
    callback(0);
    return null;
  }
  this.funcs = [];
  this.rets = [];
  this.rcb = callback;
  this.cid = 1;

  this.net.setStatusCallback(this, this.NetStatusCallback);
  this.net.setDataCallback(this, this.NetDataCallback);
  // 这里默认连接 127.0.0.1:12267，你也可以改成你想要的地址
  this.net.connect('127.0.0.1:12267');
  return this;
};

jSign.prototype.Destroy = function (callback) {
  this.funcs = [];
  if (this.net) {
    this.net.disconnect();
    this.net.setStatusCallback(null, null);
    this.net.setDataCallback(null, null);
  }
  if (typeof callback === 'function') {
    callback(true);
  }
};

// 常用接口举例
jSign.prototype.Status = function (callback) {
  this.Emit(SS_MSG_TYPE.Sign, SS_IIDS.Status, {}, callback);
};

jSign.prototype.BeginSign = function (callback) {
  this.Emit(SS_MSG_TYPE.Sign, SS_IIDS.BeginSign, {}, callback);
};

jSign.prototype.EndSign = function (callback) {
  this.Emit(SS_MSG_TYPE.Sign, SS_IIDS.EndSign, {}, callback);
};

jSign.prototype.ClearSign = function (callback) {
  this.Emit(SS_MSG_TYPE.Sign, SS_IIDS.ClearSign, {}, callback);
};

jSign.prototype.SaveSignToFile = function (path, w, h, transparent, callback) {
  const args = { path, w, h, transparent };
  this.Emit(SS_MSG_TYPE.Sign, SS_IIDS.SaveSignToFile, args, callback);
};

jSign.prototype.GetSignBase64 = function (w, h, transparent, callback) {
  const args = { w, h, transparent };
  this.Emit(SS_MSG_TYPE.Sign, SS_IIDS.GetSignBase64, args, callback);
};

// ===============================
// 事件回调
// ===============================
jSign.prototype.NetStatusCallback = function (mgr, status) {
  if (mgr.onStatusChanged) {
    mgr.onStatusChanged({ type: 'net', status });
  }
  if (mgr.rcb) {
    mgr.rcb(status);
    mgr.rcb = null;
  }
};

jSign.prototype.NetDataCallback = function (mgr, respData) {
  const packet = JSON.parse(respData);
  if (packet.hasOwnProperty('status') && packet.hasOwnProperty('data')) {
    // 接口调用返回
    const status = packet.status === 1;
    const cid = packet.cid;
    const iid = packet.iid;
    const data = packet.data;

    if (mgr.funcs.hasOwnProperty(iid)) {
      if (Object.getOwnPropertyNames(data).length > 0) mgr.funcs[iid](status, data);
      else mgr.funcs[iid](status);
    }
    mgr.funcs[iid] = null;
    if (mgr.rets.hasOwnProperty(cid)) {
      clearTimeout(mgr.rets[cid].timer);
      mgr.rets[cid] = null;
    }
  } else if (packet.hasOwnProperty('event') && packet.hasOwnProperty('data')) {
    // 事件响应
    const data = packet.data;
    const eid = packet.id;
    switch (packet.event) {
      case SS_ETYPE.Click: {
        switch (eid) {
          case SS_EIDS.SignCancel: {
            if (mgr.onSignCancel) {
              mgr.onSignCancel();
            }
            break;
          }
          case SS_EIDS.SignClear: {
            if (mgr.onSignClear) {
              mgr.onSignClear();
            }
            break;
          }
          case SS_EIDS.SignConfirm: {
            if (mgr.onSignConfirm) {
              mgr.onSignConfirm(data);
            }
            break;
          }
          // 其他事件省略...
        }
        break;
      }
      case SS_ETYPE.Custom: {
        switch (eid) {
          case SS_EIDS.Status: {
            if (mgr.onStatusChanged) {
              mgr.onStatusChanged(data);
            }
            break;
          }
          // 其他自定义事件省略...
        }
        break;
      }
    }
  }
  console.log(packet);
};

jSign.prototype.emitTimeout = function (sender, cid) {
  if (sender && sender.rets.hasOwnProperty(cid.toString())) {
    const callback = sender.rets[cid.toString()].func;
    if (callback) {
      callback(false, ['function request timeout']);
    }
  }
};

jSign.prototype.Emit = function (type, iid, args, callback, timeout) {
  if (!this.hasOwnProperty('funcs')) {
    callback(false, 'jSign object uninitialized');
    return;
  }
  if (typeof callback === 'function') {
    this.funcs[iid] = callback;
  }
  if (this.cid > 1024) {
    this.cid = 1;
  }
  const cid = ++this.cid;
  const msg = { cid, type, iid, args };
  this.net.write(JSON.stringify(msg));

  const t = timeout || 2000;
  this.rets[cid.toString()] = {
    func: callback,
    timer: setTimeout(this.emitTimeout, t, this, cid),
  };
};

// ------------------------------
// 额外的工具函数，比如 image2Base64, dataURLtoBlob 等
// ------------------------------
function image2Base64(img) {
  const canvas = document.createElement('canvas');
  canvas.width = img.width;
  canvas.height = img.height;
  const ctx = canvas.getContext('2d');
  ctx.drawImage(img, 0, 0, img.width, img.height);
  return canvas.toDataURL('image/png');
}

function dataURLtoBlob(base64) {
  const bytes = window.atob(base64);
  const ab = new ArrayBuffer(bytes.length);
  const ia = new Uint8Array(ab);

  for (let i = 0; i < bytes.length; i++) {
    ia[i] = bytes.charCodeAt(i);
  }
  return new Blob([ab], { type: 'pdf' });
}

function fake_click(obj) {
  const ev = document.createEvent('MouseEvents');
  ev.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
  obj.dispatchEvent(ev);
}

function download(name, data) {
  const urlObject = window.URL || window.webkitURL || window;
  const downloadData = new Blob([data]);
  const save_link = document.createElementNS('http://www.w3.org/1999/xhtml', 'a');
  save_link.href = urlObject.createObjectURL(downloadData);
  save_link.download = name;
  fake_click(save_link);
}

function blobToFile(theBlob, fileName) {
  theBlob.lastModifiedDate = new Date();
  theBlob.name = fileName;
  return theBlob;
}

function blend_img(canvas, sign, finger) {
  const ctx = canvas.getContext('2d');
  const w = canvas.width;
  const h = canvas.height;
  // 先清空
  ctx.clearRect(0, 0, w, h);
  // 画签名
  ctx.globalAlpha = 1.0;
  ctx.drawImage(sign, 0, 0);
  // 设置混合透明度
  ctx.globalAlpha = 0.6;
  // 计算指纹缩放后居中的位置
  const scale = finger.height / h;
  const fw = finger.width / scale;
  const fh = finger.height / scale;
  const offset_x = (w - fw) / 2;
  ctx.drawImage(finger, offset_x, 0, fw, fh);
  return canvas.toDataURL('image/png');
}

// ------------------------------
// 最后，导出一个真正的 “组合式 API”
// ------------------------------
export function useJSign() {
  // 1. 用一个 ref 来保存 jSign 实例
  const jSignInstance = ref(null);
  // 2. 标记网络是否已连接成功
  const isConnected = ref(false);

  // 3. 初始化 jSign
  function initJSign(onInitCallback, onSignConfirm, url) {
    const _jSign = new jSign(url);
    // 如果不支持 WebSocket 则返回 null
    if (!_jSign) {
      console.error('当前浏览器不支持 WebSocket 或版本过低，jSign 初始化失败。');
      return;
    }

    // 这里可以按需挂载你想要的事件回调
    _jSign.onSignCancel = () => {
      console.log('签名取消');
    };
    _jSign.onSignClear = () => {
      console.log('画板清空');
    };
    _jSign.onSignConfirm = (data) => {
      console.log('签名确认: ', data);
      if (typeof onSignConfirm === 'function') {
        onSignConfirm(data);
      }
    };
    _jSign.onStatusChanged = (info) => {
      // info => {type:"net", status: true/false} 或 {type:"device", status:0/1} 等
      if (info.type === 'net') {
        isConnected.value = !!info.status;
      }
      console.log('设备/网络状态改变: ', info);
    };

    // 真正执行初始化
    _jSign.Init((status) => {
      // status = true / false
      if (status) {
        jSignInstance.value = _jSign;
      }
      if (typeof onInitCallback === 'function') {
        onInitCallback(status);
      }
    });
  }

  // 4. 销毁 jSign
  function destroyJSign(callback) {
    if (jSignInstance.value) {
      jSignInstance.value.Destroy(() => {
        jSignInstance.value = null;
        isConnected.value = false;
        callback && callback();
      });
    } else {
      callback && callback();
    }
  }

  // 5. 封装常用函数
  function beginSign(callback) {
    if (!jSignInstance.value) return;
    jSignInstance.value.BeginSign((status) => {
      callback && callback(status);
    });
  }

  function endSign(callback) {
    if (!jSignInstance.value) return;
    jSignInstance.value.EndSign((status) => {
      callback && callback(status);
    });
  }

  function clearSign(callback) {
    if (!jSignInstance.value) return;
    jSignInstance.value.ClearSign((status) => {
      callback && callback(status);
    });
  }

  function getSignBase64(w, h, transparent, callback) {
    if (!jSignInstance.value) return;
    jSignInstance.value.GetSignBase64(w, h, transparent, (status, data) => {
      callback && callback(status, data);
    });
  }

  // 6. 在组件卸载时，自动清理
  onBeforeUnmount(() => {
    destroyJSign();
  });

  // 7. 返回给外部使用
  return {
    // state
    jSignInstance,
    isConnected,
    // methods
    initJSign,
    destroyJSign,
    beginSign,
    endSign,
    clearSign,
    getSignBase64,
    // 如果你需要使用 image2Base64、download 等工具函数，也可以在这里return
    image2Base64,
    dataURLtoBlob,
    download,
    blobToFile,
    blend_img,
  };
}
