<template>
  <div class="sign-board-container">
    <!-- 顶部区域：签字板状态、打开设置弹窗按钮 -->
    <div style="display: flex; justify-content: space-between; align-items: center">
      <div>
        签字板状态：
        <a-tag :color="isConnected ? 'green' : 'red'">
          {{ isConnected ? '已连接' : '未连接' }}
        </a-tag>
        <span style="margin-left: 12px">
          设备型号：
          <a-tag :color="deviceModel ? 'green' : 'blue'">
            {{ deviceModel || '未知' }}
          </a-tag>
        </span>
      </div>
      <a-space>
        <SettingOutlined @click="openSetting" />
        <ReloadOutlined @click="autoReconnect" />
      </a-space>
    </div>

    <!-- 签字板设置弹窗 -->
    <a-modal v-model:open="settingVisible" title="签字板设置" @ok="handleSettingDone" @cancel="closeSetting" ok-text="保存" cancel-text="取消">
      <a-form :model="wsForm" :labelCol="labelCol" :wrapperCol="wrapperCol">
        <a-form-item label="WS 地址">
          <a-input v-model:value="wsForm.wsUrl" placeholder="127.0.0.1:12276" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted, onBeforeUnmount, defineEmits, defineExpose } from 'vue';
  import { message } from 'ant-design-vue';
  import { useJSign } from './useJSign';
  import { SettingOutlined, ReloadOutlined } from '@ant-design/icons-vue';

  const emit = defineEmits<{
    (e: 'sign-done', base64: string): void;
    (e: 'connected'): void;
  }>();

  // 从 useJSign 中获取对象及方法
  const { jSignInstance, isConnected, initJSign, destroyJSign, beginSign, endSign, clearSign, getSignBase64 } = useJSign();

  // 组件内部状态
  const deviceModel = ref('');
  const signBase64 = ref('');
  const settingVisible = ref(false);

  // WS 配置参数（格式：host:port）
  const wsForm = reactive({
    wsUrl: localStorage.getItem('signboardWsUrl') || '127.0.0.1:12276',
  });

  // AntD Form 布局配置
  const labelCol = { xs: { span: 24 }, sm: { span: 5 } };
  const wrapperCol = { xs: { span: 24 }, sm: { span: 16 } };

  /**
   * 将 initJSign 包装成 Promise 形式，以便于自动重连时方便使用 async/await
   * 在成功初始化后调用 onGetStatus，并传入当前尝试的 url 作为参数
   */
  function tryInit(url: string): Promise<boolean> {
    return new Promise((resolve) => {
      // 每次尝试前先销毁上一次的实例
      destroyJSign(() => {
        initJSign(
          (ok) => {
            if (ok) {
              // 当初始化成功后，通过获取设备型号来确认连接是否正确
              onGetStatus(url, (hasModel) => {
                resolve(true);
              });
            } else {
              resolve(false);
            }
          },
          // onSignConfirm 回调——签名确认时会自动向外抛出结果
          (data) => {
            signBase64.value = data;
            emit('sign-done', data);
          },
          url
        );
      });
    });
  }

  /**
   * 获取设备状态
   * @param attemptUrl 当前尝试的 URL
   * @param cb 回调函数：参数为 true 表示设备型号存在，认为连接成功；否则为 false
   */
  function onGetStatus(attemptUrl: string, cb: (hasModel: boolean) => void) {
    jSignInstance.value?.Status((ok, data) => {
      if (ok && data && data.model) {
        deviceModel.value = data.model;
        message.success(`设备已连接, 型号: ${data.model}`);
        // 确认连接有效后，更新当前的 URL 到 wsForm 及 localStorage
        wsForm.wsUrl = attemptUrl;
        localStorage.setItem('signboardWsUrl', attemptUrl);
        emit('connected');
        cb(true);
      } else {
        message.warning('无法获取设备信息');
        cb(false);
      }
    });
  }

  /**
   * 自动重连逻辑：
   *  1. 优先尝试预设端口 [12276, 12268]
   *  2. 若未成功，则轮询整个 12000 ~ 13000 范围（排除已尝试的预设端口）
   */
  async function autoReconnect() {
    const host = wsForm.wsUrl.split(':')[0] || '127.0.0.1';
    const preferredPorts = [12267, 12276, 12268];
    let success = false;

    // 先尝试预设端口
    for (const port of preferredPorts) {
      const url = `${host}:${port}`;
      const ok = await tryInit(url);
      if (ok) {
        success = true;
        message.success(`签字板已连接，端口：${port}`);
        break;
      }
    }

    // 若预设端口均不成功，则在范围内扫描（排除已尝试的预设端口）
    if (!success) {
      /*for (let port = 12000; port <= 13000; port++) {
        if (preferredPorts.includes(port)) continue;
        const url = `${host}:${port}`;
        const ok = await tryInit(url);
        if (ok) {
          success = true;
          message.success(`签字板已连接，端口：${port}`);
          break;
        }
      }*/
      message.error('使用默认端口无法连接到签字板，请检查设备或重新配置连接地址！');
    }
  }

  /**
   * 打开设置弹窗
   */
  function openSetting() {
    settingVisible.value = true;
  }

  /**
   * 关闭设置弹窗
   */
  function closeSetting() {
    settingVisible.value = false;
  }

  /**
   * 开始签名
   */
  function startSign() {
    beginSign((ok) => {
      if (!ok) {
        message.error('开始签名失败');
      }
    });
  }

  /**
   * 组件挂载时，根据保存的 wsUrl 尝试初始化连接
   */
  onMounted(async () => {
    const url = wsForm.wsUrl;
    const ok = await tryInit(url);
    if (!ok) {
      message.warning('当前配置连接失败，开始自动重试');
      await autoReconnect();
    } else {
      message.success('签字板已连接');
    }
  });

  /**
   * 组件卸载时，销毁 jSign 实例
   */
  onBeforeUnmount(() => {
    destroyJSign();
  });

  // 向外暴露所需方法
  defineExpose({
    openSetting,
    startSign,
  });

  /**
   * 设置弹窗 - 保存配置并尝试连接
   */
  async function handleSettingDone() {
    localStorage.setItem('signboardWsUrl', wsForm.wsUrl);
    settingVisible.value = false;
    const ok = await tryInit(wsForm.wsUrl);
    if (!ok) {
      message.warning('当前配置连接失败，开始自动重试');
      await autoReconnect();
    }
  }
</script>

<style scoped>
  .sign-board-container {
    padding: 5px;
    background: #fff;
  }
</style>
