# AutoComplete 组件表单验证最佳实践

## 问题描述

在使用 AutoComplete 或 WorktypeAutoComplete 组件时，可能会遇到以下问题：
- 用户输入并选择选项后，表单必填校验仍然提示"请输入xxx"
- 明明有值，但表单验证显示为空值

## 问题原因

这是由于 **AutoComplete 组件的事件触发时机** 与 **表单验证触发时机** 之间的竞态条件导致的：

1. 用户点击下拉选项
2. 输入框失去焦点，触发 `blur` 事件
3. 表单验证立即执行（如果规则设置为 `trigger: 'blur'`）
4. 此时 `select` 事件可能还没有完成，值还没有更新
5. 表单验证获取到旧值（空值），导致验证失败

## 解决方案

### 方案一：修改表单验证触发时机（推荐）

对于 AutoComplete 组件，建议使用 `trigger: 'change'` 而不是 `trigger: 'blur'`：

```javascript
// ❌ 不推荐：使用 blur 触发
const formRules = {
  worktype: [{ required: true, message: '请选择工种', trigger: 'blur' }]
};

// ✅ 推荐：使用 change 触发
const formRules = {
  worktype: [{ required: true, message: '请选择工种', trigger: 'change' }]
};

// ✅ 也可以同时使用两种触发方式
const formRules = {
  worktype: [{ required: true, message: '请选择工种', trigger: ['change', 'blur'] }]
};
```

### 方案二：使用组件的延迟验证功能

组件已内置延迟验证功能，默认启用：

```vue
<template>
  <!-- 默认启用延迟验证，解决竞态条件问题 -->
  <AutoComplete
    v-model:value="form.department"
    category="department"
    placeholder="请输入部门名称"
  />
  
  <!-- 如需禁用延迟验证 -->
  <AutoComplete
    v-model:value="form.department"
    category="department"
    :delayBlurValidation="false"
    placeholder="请输入部门名称"
  />
</template>
```

### 方案三：组合使用

最佳实践是同时使用方案一和方案二：

```vue
<template>
  <a-form :model="form" :rules="rules" @finish="handleSubmit">
    <a-form-item label="工种" name="worktype">
      <WorktypeAutoComplete
        v-model:value="form.worktype"
        placeholder="请选择工种"
        :delayBlurValidation="true"
      />
    </a-form-item>
  </a-form>
</template>

<script setup>
const form = reactive({
  worktype: ''
});

const rules = {
  // 使用 change 触发，避免与 blur 事件的竞态条件
  worktype: [{ required: true, message: '请选择工种', trigger: 'change' }]
};
</script>
```

## 不同场景的最佳配置

### 1. 普通表单场景
```javascript
const rules = {
  field: [{ required: true, message: '请选择xxx', trigger: 'change' }]
};
```

### 2. 实时验证场景
```javascript
const rules = {
  field: [{ required: true, message: '请选择xxx', trigger: ['change', 'blur'] }]
};
```

### 3. 延迟验证场景
```javascript
const rules = {
  field: [{ required: true, message: '请选择xxx', trigger: 'blur' }]
};
// 同时在组件上设置 :delayBlurValidation="true"
```

## 技术原理

### 延迟验证机制

组件内部通过以下方式解决竞态条件：

```javascript
const handleBlur = () => {
  if (props.delayBlurValidation) {
    // 延迟 100ms 触发 blur 事件，确保 select 事件先完成
    setTimeout(() => {
      emit('blur');
    }, 100);
  } else {
    emit('blur');
  }
};
```

### 值同步机制

在选择时立即同步值：

```javascript
const handleSelect = (value, option) => {
  // 确保选择的值立即同步到 inputValue
  inputValue.value = value;
  // ... 其他逻辑
};
```

## 常见问题

### Q: 为什么不能直接修改 Ant Design Vue 的行为？
A: 这是 AutoComplete 组件的固有特性，需要在应用层面处理事件时序问题。

### Q: 延迟 100ms 会影响用户体验吗？
A: 不会，这个延迟对用户来说是不可感知的，但能有效解决验证时机问题。

### Q: 可以调整延迟时间吗？
A: 目前是固定的 100ms，这个时间经过测试是最优的平衡点。

## 升级指南

如果你的项目中已经在使用这些组件，建议按以下步骤升级：

1. **检查现有表单规则**：找出所有使用 `trigger: 'blur'` 的 AutoComplete 字段
2. **修改触发方式**：改为 `trigger: 'change'` 或 `trigger: ['change', 'blur']`
3. **测试验证**：确保修改后的表单验证正常工作
4. **逐步迁移**：可以逐个表单进行修改，不需要一次性全部修改

## 总结

通过合理配置表单验证触发时机和使用组件的延迟验证功能，可以完全解决 AutoComplete 组件的表单验证问题。推荐的最佳实践是：

1. 对 AutoComplete 字段使用 `trigger: 'change'`
2. 保持组件的 `delayBlurValidation` 默认开启
3. 在复杂场景下可以同时使用多种触发方式
