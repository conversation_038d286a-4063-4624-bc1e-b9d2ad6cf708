import { defHttp } from '/@/utils/http/axios';

enum Api {
  search = '/basicinfo/autoCompleteItem/search',
  popular = '/basicinfo/autoCompleteItem/popular',
  autoCreate = '/basicinfo/autoCompleteItem/autoCreate',
  updateUseCount = '/basicinfo/autoCompleteItem/updateUseCount',
  batchUpdateUseCount = '/basicinfo/autoCompleteItem/batchUpdateUseCount',
  categoryStats = '/basicinfo/autoCompleteItem/categoryStats',
  clearCache = '/basicinfo/autoCompleteItem/clearCache',
  refreshCache = '/basicinfo/autoCompleteItem/refreshCache',
}

/**
 * 自动补全搜索参数
 */
export interface AutoCompleteSearchParams {
  category: string;
  keyword?: string;
  searchType?: 'name' | 'helpChar' | 'both';
  limit?: number;
  includePopular?: boolean;
}

/**
 * 自动补全结果
 */
export interface AutoCompleteResult {
  id?: string;
  category: string;
  name: string;
  helpChar?: string;
  pinyin?: string;
  useCount?: number;
  sortOrder?: number;
  isNew?: boolean;
  matchType?: string;
  matchScore?: number;
}

/**
 * 自动补全创建参数
 */
export interface AutoCompleteCreateParams {
  category: string;
  name: string;
  helpChar?: string;
  pinyin?: string;
  sortOrder?: number;
  remark?: string;
}

/**
 * 自动补全搜索
 * @param params 搜索参数
 */
export const autoCompleteSearch = (params: AutoCompleteSearchParams) => {
  return defHttp.get<AutoCompleteResult[]>({ url: Api.search, params }, { isTransformResponse: false });
};

/**
 * 获取热门推荐
 * @param category 分类
 * @param limit 限制数量
 */
export const getPopularItems = (category: string, limit: number = 10) => {
  return defHttp.get<AutoCompleteResult[]>({ 
    url: Api.popular, 
    params: { category, limit } 
  }, { isTransformResponse: false });
};

/**
 * 自动创建项目
 * @param params 创建参数
 */
export const autoCreateItem = (params: AutoCompleteCreateParams) => {
  return defHttp.post<any>({ url: Api.autoCreate, params }, { isTransformResponse: false });
};

/**
 * 更新使用频次
 * @param id 项目ID
 */
export const updateUseCount = (id: string) => {
  return defHttp.post<string>({ 
    url: Api.updateUseCount, 
    params: { id } 
  }, { isTransformResponse: false });
};

/**
 * 批量更新使用频次
 * @param ids 项目ID列表
 */
export const batchUpdateUseCount = (ids: string[]) => {
  return defHttp.post<string>({ url: Api.batchUpdateUseCount, data: ids }, { isTransformResponse: false });
};

/**
 * 获取分类统计信息
 * @param category 分类
 */
export const getCategoryStats = (category: string) => {
  return defHttp.get<any>({ 
    url: Api.categoryStats, 
    params: { category } 
  }, { isTransformResponse: false });
};

/**
 * 清除分类缓存
 * @param category 分类
 */
export const clearCategoryCache = (category: string) => {
  return defHttp.post<string>({ 
    url: Api.clearCache, 
    params: { category } 
  }, { isTransformResponse: false });
};

/**
 * 刷新分类缓存
 * @param category 分类
 */
export const refreshCategoryCache = (category: string) => {
  return defHttp.post<string>({ 
    url: Api.refreshCache, 
    params: { category } 
  }, { isTransformResponse: false });
};
