# 基础信息组件

这个目录包含了与基础信息相关的Vue组件，主要用于单位和部门的选择和管理。

## 组件列表

### 1. CompanyAutoComplete.vue
单位自动完成组件，支持搜索和选择单位。

**特性：**
- 支持关键字搜索（单位名称、助记码）
- 防抖搜索，避免频繁请求
- 自动完成下拉选择
- 支持清空操作
- 显示单位详细信息（名称、编码、电话等）

**使用示例：**
```vue
<template>
  <CompanyAutoComplete
    v-model:value="companyId"
    placeholder="请选择单位"
    @select="handleCompanySelect"
    @change="handleCompanyChange"
  />
</template>

<script setup>
import CompanyAutoComplete from '@/components/basicinfo/CompanyAutoComplete.vue';

const companyId = ref('');

const handleCompanySelect = (value, option) => {
  console.log('选中单位:', value, option);
};

const handleCompanyChange = (value, option) => {
  console.log('单位变化:', value, option);
};
</script>
```

### 2. CompanyDeptTreeSelect.vue
部门树形选择组件，支持异步加载和树形展示。

**特性：**
- 树形结构展示部门层级
- 异步加载子节点数据
- 支持搜索过滤
- 可配置只显示部门（不显示单位）
- 自动响应单位ID变化
- 支持清空操作
- 显示节点类型标识（单位/部门）

**Props：**
- `value?: string` - 选中的部门ID
- `companyId?: string` - 所属单位ID（必需）
- `placeholder?: string` - 占位符文本
- `allowClear?: boolean` - 是否允许清空
- `disabled?: boolean` - 是否禁用
- `showSearch?: boolean` - 是否显示搜索
- `onlyDepartments?: boolean` - 是否只显示部门

**事件：**
- `@update:value` - 值更新事件
- `@change` - 值变化事件
- `@select` - 选择事件

**使用示例：**
```vue
<template>
  <CompanyDeptTreeSelect
    v-model:value="deptId"
    :company-id="companyId"
    placeholder="请选择部门"
    :only-departments="true"
    @change="handleDeptChange"
    @select="handleDeptSelect"
  />
</template>

<script setup>
import CompanyDeptTreeSelect from '@/components/basicinfo/CompanyDeptTreeSelect.vue';

const companyId = ref('');
const deptId = ref('');

const handleDeptChange = (value, node) => {
  console.log('部门变化:', value, node);
};

const handleDeptSelect = (value, node) => {
  console.log('选择部门:', value, node);
};
</script>
```

### 3. CompanyDeptTreeSelectExample.vue
使用示例组件，展示了各种使用场景和配置。

## API接口

### 单位相关API (`src/api/basicinfo/company.ts`)
- `getCompanyAutoComplete(keyword?, pageSize?)` - 单位自动完成接口
- `searchCompanyByKeyword(keyword, onlyRootCompanies?)` - 搜索单位
- `queryById(id)` - 根据ID查询单位

### 部门相关API (`src/api/basicinfo/department.ts`)
- `getDepartmentsByPid(pid, pageSize?)` - 根据父ID获取部门列表
- `getDepartmentTreeRoot(params?)` - 获取部门树根节点
- `loadDepartmentTreeChildren(params)` - 异步加载部门树子节点
- `searchDepartments(keyword?, companyId?, pageSize?)` - 搜索部门
- `getDepartmentPath(departmentId)` - 获取部门完整路径

## 类型定义

### 单位类型 (`src/types/basicinfo/company.ts`)
- `Company` - 单位基础信息
- `CompanyAutoCompleteDTO` - 单位自动完成DTO
- `CompanyAutoCompleteOption` - 自动完成选项

### 部门类型 (`src/types/basicinfo/department.ts`)
- `Department` - 部门基础信息
- `DepartmentTreeNode` - 部门树节点
- `DepartmentSelectOption` - 部门选择器选项
- `DepartmentQueryParams` - 部门查询参数

## 注意事项

1. **依赖关系**：CompanyDeptTreeSelect组件依赖于companyId，必须先选择单位才能加载部门数据。

2. **性能优化**：
   - 使用防抖搜索避免频繁请求
   - 异步加载树节点数据
   - 支持数据缓存

3. **错误处理**：
   - API请求失败时有降级处理
   - 显示友好的错误提示
   - 支持重试机制

4. **兼容性**：
   - 兼容现有的JTreeSelect组件
   - 支持多种数据格式
   - 向后兼容旧版本API

## 迁移指南

### 从JTreeSelect迁移到CompanyDeptTreeSelect

**旧代码：**
```vue
<JTreeSelect
  v-model:value="formData.companyDeptId"
  :dict="`company_dept,name,id`"
  :condition="deptCondition"
  :reload="companyDeptReloadKey"
  pid-field="pid"
  :pid-value="formData.companyId || ''"
  @change="handleDeptChange"
/>
```

**新代码：**
```vue
<CompanyDeptTreeSelect
  v-model:value="formData.companyDeptId"
  :company-id="formData.companyId"
  :only-departments="true"
  @change="handleDeptChange"
  @select="handleDeptSelect"
/>
```

**主要变化：**
1. 移除了`dict`、`condition`、`reload`等复杂配置
2. 使用`company-id`属性直接绑定单位ID
3. 组件内部自动处理数据加载和更新
4. 简化了事件处理函数
5. 避免了SQL注入风险

## 故障排除

### 常见问题

1. **部门数据不加载**
   - 检查`company-id`是否正确设置
   - 确认API接口返回数据格式正确
   - 查看浏览器控制台错误信息

2. **搜索功能不工作**
   - 确认`show-search`属性为true
   - 检查搜索关键字是否正确传递
   - 验证后端搜索接口是否正常

3. **树形结构显示异常**
   - 检查数据中的`hasChild`字段
   - 确认父子关系（pid字段）正确
   - 验证`orgType`字段值

### 调试技巧

1. 开启浏览器控制台查看详细日志
2. 使用Vue DevTools检查组件状态
3. 检查网络请求和响应数据
4. 使用示例组件进行对比测试
