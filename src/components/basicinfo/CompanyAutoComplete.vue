<template>
  <a-auto-complete
    v-model:value="inputValue"
    :options="options"
    :placeholder="placeholder"
    :allowClear="allowClear"
    :disabled="disabled"
    :loading="loading"
    @search="handleSearch"
    @select="handleSelect"
    @change="handleChange"
    @clear="handleClear"
    :filterOption="false"
    :notFoundContent="loading ? '搜索中...' : '暂无数据'"
  >
    <template #option="{ value: val, label, data }">
      <div class="company-option">
        <div class="company-name">{{ data.name }}</div>
        <div class="company-details">
          <span v-if="data.helpChar" class="company-help-char">{{ data.helpChar }}</span>
          <span v-if="data.orgCode" class="company-code">编码: {{ data.orgCode }}</span>
          <span v-if="data.telephone" class="company-phone">电话: {{ data.telephone }}</span>
        </div>
      </div>
    </template>
  </a-auto-complete>
</template>

<script lang="ts" setup>
  import { ref, watch, computed, onMounted } from 'vue';
  import { debounce } from 'lodash-es';
  import { getCompanyAutoComplete } from '@/api/basicinfo/company';
  import type { CompanyAutoCompleteDTO, CompanyAutoCompleteOption } from '@/types/basicinfo/company';

  interface Props {
    value?: string;
    placeholder?: string;
    allowClear?: boolean;
    disabled?: boolean;
  }

  interface Emits {
    (e: 'update:value', value: string): void;
    (e: 'change', value: string, option?: CompanyAutoCompleteDTO): void;
    (e: 'select', value: string, option: CompanyAutoCompleteDTO): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    placeholder: '请输入单位名称或助记码',
    allowClear: true,
    disabled: false,
  });

  const emit = defineEmits<Emits>();

  const inputValue = ref<string>('');
  const options = ref<CompanyAutoCompleteOption[]>([]);
  const loading = ref<boolean>(false);
  const companyList = ref<CompanyAutoCompleteDTO[]>([]);

  // 监听外部传入的value变化
  watch(
    () => props.value,
    (newVal) => {
      if (newVal !== inputValue.value) {
        inputValue.value = newVal || '';
      }
    },
    { immediate: true }
  );

  // 监听输入值变化，向外部发送更新
  watch(inputValue, (newVal) => {
    emit('update:value', newVal);
  });

  /**
   * 获取公司列表数据
   */
  const fetchCompanyList = async (keyword?: string) => {
    try {
      loading.value = true;
      console.log('CompanyAutoComplete: 开始获取单位列表，关键词:', keyword);

      const response = await getCompanyAutoComplete(keyword, 50);
      console.log('CompanyAutoComplete: API响应:', response);

      if (response && response.success && response.result) {
        companyList.value = response.result;
        updateOptions(keyword);
        console.log('CompanyAutoComplete: 成功加载', response.result.length, '条数据');

        // 如果是降级模式，显示提示信息
        if (response.message && response.message.includes('降级模式')) {
          console.info('CompanyAutoComplete: 使用降级模式加载数据');
        }
      } else {
        console.warn('CompanyAutoComplete: API响应格式异常:', response);
        companyList.value = [];
        options.value = [];
      }
    } catch (error: any) {
      console.error('CompanyAutoComplete: 获取单位列表失败:', error);

      // 检查是否是缓存相关错误
      if (error?.message && error.message.includes('cache')) {
        console.warn('CompanyAutoComplete: 检测到缓存错误，这是后端配置问题');
      }

      companyList.value = [];
      options.value = [];
    } finally {
      loading.value = false;
    }
  };

  /**
   * 更新选项列表
   */
  const updateOptions = (keyword?: string) => {
    // 后端已经做了过滤，直接使用返回的数据
    options.value = companyList.value.map((item) => ({
      value: item.id,
      label: item.name,
      data: item,
    }));
    console.log('CompanyAutoComplete: 更新选项列表，共', options.value.length, '个选项');
  };

  /**
   * 防抖搜索处理
   */
  const debouncedSearch = debounce(async (keyword: string) => {
    await fetchCompanyList(keyword);
  }, 300);

  /**
   * 搜索处理
   */
  const handleSearch = (value: string) => {
    if (!value || value.trim().length === 0) {
      // 如果搜索为空，显示默认的父级单位
      fetchCompanyList();
    } else {
      debouncedSearch(value);
    }
  };

  /**
   * 选择处理
   */
  const handleSelect = (value: string, option: any) => {
    const selectedCompany = option.data as CompanyAutoCompleteDTO;
    inputValue.value = selectedCompany.name;
    emit('select', value, selectedCompany);
    emit('change', value, selectedCompany);
  };

  /**
   * 值变化处理
   */
  const handleChange = (value: string) => {
    if (!value) {
      emit('change', '', undefined);
    }
  };

  /**
   * 清空处理
   */
  const handleClear = () => {
    inputValue.value = '';
    emit('change', '', undefined);
    // 清空后重新加载父级单位
    fetchCompanyList();
  };

  // 组件挂载时加载初始数据
  onMounted(() => {
    fetchCompanyList();
  });
</script>

<style lang="less" scoped>
  .company-option {
    display: flex;
    flex-direction: column;
    padding: 6px 0;

    .company-name {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 1.4;
      font-weight: 500;
    }

    .company-details {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-top: 4px;

      .company-help-char {
        font-size: 12px;
        color: #1890ff;
        background: #f0f8ff;
        padding: 2px 6px;
        border-radius: 4px;
        line-height: 1.2;
      }

      .company-code,
      .company-phone {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
        line-height: 1.2;
      }
    }
  }

  :deep(.ant-select-dropdown) {
    .ant-select-item-option-content {
      white-space: normal;
    }
  }
</style>
