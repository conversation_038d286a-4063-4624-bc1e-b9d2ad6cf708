<template>
  <div class="debug-container">
    <a-card title="危害因素自动完成组件调试工具" class="debug-card">
      
      <!-- 测试数据输入 -->
      <div class="debug-section">
        <h3>测试数据</h3>
        <a-textarea
          v-model:value="testDataInput"
          placeholder="输入测试数据（JSON格式）"
          :rows="6"
          class="test-data-input"
        />
        <div class="debug-actions">
          <a-button @click="loadTestData" type="primary">加载测试数据</a-button>
          <a-button @click="generateSampleData">生成示例数据</a-button>
          <a-button @click="clearTestData">清空数据</a-button>
        </div>
      </div>

      <a-divider />

      <!-- 组件测试 -->
      <div class="debug-section">
        <h3>组件测试</h3>
        <ZyRiskFactorAutoComplete
          v-model:value="testValue"
          :config="testConfig"
          @change="handleChange"
          @validation-complete="handleValidation"
          ref="autoCompleteRef"
        />
        
        <div class="test-controls">
          <a-space>
            <a-input v-model:value="searchKeyword" placeholder="搜索关键词" />
            <a-button @click="testSearch">测试搜索</a-button>
            <a-button @click="testValidation">测试验证</a-button>
            <a-button @click="analyzeData">分析数据</a-button>
          </a-space>
        </div>
      </div>

      <a-divider />

      <!-- 数据分析结果 -->
      <div class="debug-section" v-if="analysisResult">
        <h3>数据分析结果</h3>
        <a-alert
          v-if="analysisResult.issues.length > 0"
          :message="`发现 ${analysisResult.issues.length} 个数据质量问题`"
          type="warning"
          show-icon
          class="analysis-alert"
        />
        
        <a-tabs>
          <a-tab-pane key="overview" tab="概览">
            <a-descriptions :column="2" bordered size="small">
              <a-descriptions-item label="总数据量">{{ analysisResult.totalCount }}</a-descriptions-item>
              <a-descriptions-item label="有效数据">{{ analysisResult.validCount }}</a-descriptions-item>
              <a-descriptions-item label="重复名称">{{ analysisResult.duplicateNames.length }}</a-descriptions-item>
              <a-descriptions-item label="可疑名称">{{ analysisResult.suspiciousNames.length }}</a-descriptions-item>
              <a-descriptions-item label="缺失字段">{{ analysisResult.missingFields.length }}</a-descriptions-item>
              <a-descriptions-item label="过滤数据">{{ analysisResult.filteredCount }}</a-descriptions-item>
            </a-descriptions>
          </a-tab-pane>
          
          <a-tab-pane key="issues" tab="问题列表">
            <a-list
              :dataSource="analysisResult.issues"
              size="small"
              bordered
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-icon type="warning" style="color: #fa8c16; margin-right: 8px;" />
                  {{ item }}
                </a-list-item>
              </template>
            </a-list>
          </a-tab-pane>
          
          <a-tab-pane key="suspicious" tab="可疑数据" v-if="analysisResult.suspiciousNames.length > 0">
            <a-table
              :dataSource="analysisResult.suspiciousNames"
              :columns="suspiciousColumns"
              :pagination="false"
              size="small"
              bordered
            />
          </a-tab-pane>
          
          <a-tab-pane key="filtered" tab="被过滤数据" v-if="analysisResult.filteredData.length > 0">
            <a-table
              :dataSource="analysisResult.filteredData"
              :columns="filteredColumns"
              :pagination="false"
              size="small"
              bordered
            />
          </a-tab-pane>
        </a-tabs>
      </div>

      <!-- 日志输出 -->
      <div class="debug-section">
        <h3>调试日志</h3>
        <div class="log-container">
          <div
            v-for="(log, index) in debugLogs"
            :key="index"
            :class="['log-item', `log-${log.level}`]"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-level">{{ log.level.toUpperCase() }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
        <a-button @click="clearLogs" size="small">清空日志</a-button>
      </div>

    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import ZyRiskFactorAutoComplete from './ZyRiskFactorAutoComplete.vue';
  import { cleanRiskFactorData, detectDataIssues } from '/@/utils/zyRiskFactorUtils';
  import type {
    ZyRiskFactorAutoCompleteDTO,
    ZyRiskFactorChangeEvent,
    ZyRiskFactorValidationResult,
  } from '/@/types/basicinfo/zyRiskFactor';

  const { createMessage } = useMessage();

  // 测试数据
  const testDataInput = ref<string>('');
  const testValue = ref<string>('');
  const searchKeyword = ref<string>('');
  const autoCompleteRef = ref();

  // 测试配置
  const testConfig = {
    allowMultiple: false,
    enableManualValidation: true,
    showCode: true,
    showHelpChar: true,
    showWubiChar: true,
    showHarmLevel: true,
  };

  // 分析结果
  const analysisResult = ref<any>(null);

  // 调试日志
  const debugLogs = ref<Array<{ time: string; level: string; message: string }>>([]);

  // 表格列定义
  const suspiciousColumns = [
    { title: 'ID', dataIndex: 'id', key: 'id', width: 100 },
    { title: '名称', dataIndex: 'name', key: 'name' },
    { title: '编码', dataIndex: 'code', key: 'code' },
    { title: '助记码', dataIndex: 'helpChar', key: 'helpChar' },
    { title: '五笔码', dataIndex: 'wubiChar', key: 'wubiChar' },
  ];

  const filteredColumns = [
    { title: '名称', dataIndex: 'factor.name', key: 'name' },
    { title: '原因', dataIndex: 'reason', key: 'reason' },
    { title: 'ID', dataIndex: 'factor.id', key: 'id' },
  ];

  /**
   * 添加调试日志
   */
  const addLog = (level: string, message: string) => {
    debugLogs.value.unshift({
      time: new Date().toLocaleTimeString(),
      level,
      message,
    });
    
    // 限制日志数量
    if (debugLogs.value.length > 100) {
      debugLogs.value = debugLogs.value.slice(0, 100);
    }
  };

  /**
   * 生成示例数据
   */
  const generateSampleData = () => {
    const sampleData = [
      {
        id: '1',
        name: '噪声',
        code: 'ZS001',
        helpChar: 'ZS',
        wubiChar: 'JQRN',
        harmLevel: '中等'
      },
      {
        id: '2',
        name: '粉尘',
        code: 'FC002',
        helpChar: 'FC',
        wubiChar: 'OWFI',
        harmLevel: '高'
      },
      {
        id: '3',
        name: 'ZS', // 这是一个问题数据：名称就是助记码
        code: 'ZS003',
        helpChar: 'ZS',
        wubiChar: 'ZS',
        harmLevel: '低'
      },
      {
        id: '4',
        name: '高温',
        code: 'GW004',
        helpChar: 'GW',
        wubiChar: 'YMJY',
        harmLevel: '中等'
      },
      {
        id: '5',
        name: 'FC', // 另一个问题数据：名称就是助记码
        code: 'FC005',
        helpChar: 'FC',
        wubiChar: 'FC',
        harmLevel: '高'
      },
      {
        id: '1', // 重复ID
        name: '噪声重复',
        code: 'ZS006',
        helpChar: 'ZS',
        wubiChar: 'JQRN',
        harmLevel: '中等'
      }
    ];

    testDataInput.value = JSON.stringify(sampleData, null, 2);
    addLog('info', '生成了示例测试数据');
  };

  /**
   * 加载测试数据
   */
  const loadTestData = () => {
    try {
      const data = JSON.parse(testDataInput.value);
      if (!Array.isArray(data)) {
        throw new Error('数据必须是数组格式');
      }
      
      addLog('info', `加载了 ${data.length} 条测试数据`);
      createMessage.success('测试数据加载成功');
    } catch (error: any) {
      addLog('error', `加载测试数据失败: ${error.message}`);
      createMessage.error('测试数据格式错误');
    }
  };

  /**
   * 清空测试数据
   */
  const clearTestData = () => {
    testDataInput.value = '';
    analysisResult.value = null;
    addLog('info', '清空了测试数据');
  };

  /**
   * 测试搜索
   */
  const testSearch = () => {
    if (!searchKeyword.value.trim()) {
      createMessage.warning('请输入搜索关键词');
      return;
    }
    
    addLog('info', `测试搜索关键词: ${searchKeyword.value}`);
    // 这里可以模拟搜索逻辑
  };

  /**
   * 测试验证
   */
  const testValidation = () => {
    if (autoCompleteRef.value) {
      autoCompleteRef.value.validateManualInput();
      addLog('info', '触发了手动验证');
    }
  };

  /**
   * 分析数据
   */
  const analyzeData = () => {
    try {
      const data = JSON.parse(testDataInput.value || '[]');
      
      // 使用工具函数分析数据
      const { cleaned, filtered } = cleanRiskFactorData(data, searchKeyword.value);
      const issues = detectDataIssues(data);
      
      analysisResult.value = {
        totalCount: data.length,
        validCount: cleaned.length,
        filteredCount: filtered.length,
        filteredData: filtered,
        ...issues,
      };
      
      addLog('info', `分析完成: 总数据 ${data.length}, 有效数据 ${cleaned.length}, 问题数据 ${filtered.length}`);
      createMessage.success('数据分析完成');
    } catch (error: any) {
      addLog('error', `数据分析失败: ${error.message}`);
      createMessage.error('数据分析失败');
    }
  };

  /**
   * 清空日志
   */
  const clearLogs = () => {
    debugLogs.value = [];
  };

  /**
   * 处理组件变化
   */
  const handleChange = (event: ZyRiskFactorChangeEvent) => {
    addLog('info', `组件值变化: ${event.value}`);
    if (event.selectedFactor) {
      addLog('info', `选中危害因素: ${event.selectedFactor.name}`);
    }
  };

  /**
   * 处理验证完成
   */
  const handleValidation = (results: ZyRiskFactorValidationResult[]) => {
    const successCount = results.filter(r => r.found).length;
    addLog('info', `验证完成: ${successCount}/${results.length} 成功`);
  };
</script>

<style lang="less" scoped>
  .debug-container {
    padding: 20px;
    
    .debug-card {
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .debug-section {
      margin-bottom: 24px;
      
      h3 {
        margin-bottom: 16px;
        color: #1890ff;
      }
    }
    
    .test-data-input {
      margin-bottom: 12px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    }
    
    .debug-actions,
    .test-controls {
      margin-top: 12px;
      display: flex;
      gap: 8px;
    }
    
    .analysis-alert {
      margin-bottom: 16px;
    }
    
    .log-container {
      max-height: 300px;
      overflow-y: auto;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      padding: 8px;
      background: #fafafa;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 12px;
      
      .log-item {
        display: flex;
        margin-bottom: 4px;
        
        .log-time {
          color: #666;
          margin-right: 8px;
          min-width: 80px;
        }
        
        .log-level {
          margin-right: 8px;
          min-width: 50px;
          font-weight: bold;
        }
        
        .log-message {
          flex: 1;
        }
        
        &.log-info .log-level {
          color: #1890ff;
        }
        
        &.log-warn .log-level {
          color: #fa8c16;
        }
        
        &.log-error .log-level {
          color: #ff4d4f;
        }
      }
    }
  }
</style>
