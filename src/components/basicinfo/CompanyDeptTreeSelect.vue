<template>
  <a-tree-select
    v-model:value="selectedValue"
    :tree-data="treeData"
    :placeholder="placeholder"
    :allowClear="allowClear"
    :disabled="disabled"
    :loading="loading"
    :load-data="loadData"
    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
    tree-node-filter-prop="title"
    :show-search="showSearch"
    :filter-tree-node="filterTreeNode"
    :not-found-content="loading ? '加载中...' : '暂无数据'"
    @change="handleChange"
    @select="handleSelect"
    @clear="handleClear"
  >
    <template #title="{ title, key, orgType, isLeaf }">
      <div class="dept-tree-node">
        <Icon 
          :icon="orgType === '1' ? 'ant-design:bank-outlined' : 'ant-design:apartment-outlined'" 
          class="node-icon"
        />
        <span class="node-title">{{ title }}</span>
        <a-tag 
          v-if="orgType === '1'" 
          size="small" 
          color="blue" 
          class="node-tag"
        >
          单位
        </a-tag>
        <a-tag 
          v-else 
          size="small" 
          color="green" 
          class="node-tag"
        >
          部门
        </a-tag>
      </div>
    </template>
  </a-tree-select>
</template>

<script lang="ts" setup>
  import { ref, watch, computed, onMounted, nextTick } from 'vue';
  import { Icon } from '@/components/Icon';
  import { getCompanyDeptListByPid } from '@/views/reg/CompanyReg.api';
  import { queryCompanyDepartTreeSync, loadTreeChildren } from '@/views/companyDepart/CompanyDepart.api';
  import { message } from 'ant-design-vue';

  interface TreeNode {
    key: string;
    value: string;
    title: string;
    isLeaf?: boolean;
    children?: TreeNode[];
    orgType?: string;
    pid?: string;
    hasChild?: string;
  }

  interface Props {
    value?: string;
    companyId?: string;
    placeholder?: string;
    allowClear?: boolean;
    disabled?: boolean;
    showSearch?: boolean;
    onlyDepartments?: boolean; // 是否只显示部门，不显示单位
  }

  interface Emits {
    (e: 'update:value', value: string | undefined): void;
    (e: 'change', value: string | undefined, node?: TreeNode): void;
    (e: 'select', value: string, node: TreeNode): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    placeholder: '请选择部门',
    allowClear: true,
    disabled: false,
    showSearch: true,
    onlyDepartments: false,
  });

  const emit = defineEmits<Emits>();

  const selectedValue = ref<string | undefined>();
  const treeData = ref<TreeNode[]>([]);
  const loading = ref<boolean>(false);

  // 监听外部传入的value变化
  watch(
    () => props.value,
    (newVal) => {
      if (newVal !== selectedValue.value) {
        selectedValue.value = newVal;
      }
    },
    { immediate: true }
  );

  // 监听选中值变化，向外部发送更新
  watch(selectedValue, (newVal) => {
    emit('update:value', newVal);
  });

  // 监听companyId变化，重新加载数据
  watch(
    () => props.companyId,
    (newCompanyId, oldCompanyId) => {
      if (newCompanyId !== oldCompanyId) {
        console.log('CompanyDeptTreeSelect: 单位ID变化', oldCompanyId, '->', newCompanyId);
        selectedValue.value = undefined;
        if (newCompanyId) {
          loadInitialData();
        } else {
          treeData.value = [];
        }
      }
    },
    { immediate: true }
  );

  /**
   * 加载初始数据
   */
  const loadInitialData = async () => {
    if (!props.companyId) {
      treeData.value = [];
      return;
    }

    try {
      loading.value = true;
      console.log('CompanyDeptTreeSelect: 开始加载部门数据，companyId:', props.companyId);

      const response = await getCompanyDeptListByPid({
        pid: props.companyId,
        pageSize: 10000
      });

      console.log('CompanyDeptTreeSelect: API响应:', response);

      if (response && Array.isArray(response)) {
        const nodes = response
          .filter(item => props.onlyDepartments ? item.orgType === '2' : true)
          .map(item => transformToTreeNode(item));

        treeData.value = nodes;
        console.log('CompanyDeptTreeSelect: 成功加载', nodes.length, '个节点');
      } else {
        console.warn('CompanyDeptTreeSelect: API响应格式异常:', response);
        treeData.value = [];
      }
    } catch (error) {
      console.error('CompanyDeptTreeSelect: 加载部门数据失败:', error);
      message.error('加载部门数据失败');
      treeData.value = [];
    } finally {
      loading.value = false;
    }
  };

  /**
   * 转换数据为树节点格式
   */
  const transformToTreeNode = (item: any): TreeNode => {
    // 确定是否为叶子节点
    const hasChild = item.hasChild;
    const isLeaf = hasChild === '0' || hasChild === 0 || hasChild === false || !hasChild;

    return {
      key: item.id,
      value: item.id,
      title: item.name || item.departName || item.title || '未命名',
      isLeaf: isLeaf,
      orgType: String(item.orgType || '2'), // 确保为字符串
      pid: item.pid,
      hasChild: String(hasChild || '0'), // 统一转换为字符串
    };
  };

  /**
   * 异步加载子节点数据
   */
  const loadData = async (treeNode: any): Promise<void> => {
    const { key } = treeNode.dataRef || treeNode;

    try {
      console.log('CompanyDeptTreeSelect: 异步加载子节点，parentId:', key);

      const response = await getCompanyDeptListByPid({
        pid: key,
        pageSize: 10000
      });

      if (response && Array.isArray(response)) {
        const childNodes = response
          .filter(item => props.onlyDepartments ? item.orgType === '2' : true)
          .map(item => transformToTreeNode(item));

        // 更新树数据
        updateTreeData(treeData.value, key, childNodes);
        console.log('CompanyDeptTreeSelect: 成功加载子节点', childNodes.length, '个');
      }
    } catch (error) {
      console.error('CompanyDeptTreeSelect: 加载子节点失败:', error);
      message.error('加载子节点失败');
    }
  };

  /**
   * 更新树数据中的子节点
   */
  const updateTreeData = (tree: TreeNode[], key: string, children: TreeNode[]) => {
    for (let i = 0; i < tree.length; i++) {
      if (tree[i].key === key) {
        tree[i].children = children;
        tree[i].isLeaf = children.length === 0;
        break;
      } else if (tree[i].children) {
        updateTreeData(tree[i].children, key, children);
      }
    }
  };

  /**
   * 搜索过滤函数
   */
  const filterTreeNode = (inputValue: string, treeNode: any) => {
    const title = treeNode.title || '';
    return title.toLowerCase().includes(inputValue.toLowerCase());
  };

  /**
   * 值变化处理
   */
  const handleChange = (value: string | undefined, label?: any, extra?: any) => {
    console.log('CompanyDeptTreeSelect: 值变化', value, label, extra);
    selectedValue.value = value;
    
    // 查找对应的节点信息
    const node = findNodeByValue(treeData.value, value);
    emit('change', value, node);
  };

  /**
   * 选择处理
   */
  const handleSelect = (value: string, node: any, extra?: any) => {
    console.log('CompanyDeptTreeSelect: 选择节点', value, node, extra);
    const treeNode = node.node || node;
    emit('select', value, treeNode);
  };

  /**
   * 清空处理
   */
  const handleClear = () => {
    console.log('CompanyDeptTreeSelect: 清空选择');
    selectedValue.value = undefined;
    emit('change', undefined, undefined);
  };

  /**
   * 根据值查找节点
   */
  const findNodeByValue = (nodes: TreeNode[], value?: string): TreeNode | undefined => {
    if (!value) return undefined;
    
    for (const node of nodes) {
      if (node.value === value) {
        return node;
      }
      if (node.children) {
        const found = findNodeByValue(node.children, value);
        if (found) return found;
      }
    }
    return undefined;
  };

  // 暴露方法给父组件
  defineExpose({
    loadInitialData,
    clearSelection: () => {
      selectedValue.value = undefined;
    },
    getSelectedNode: () => findNodeByValue(treeData.value, selectedValue.value),
  });
</script>

<style scoped>
.dept-tree-node {
  display: flex;
  align-items: center;
  gap: 6px;
  width: 100%;
}

.node-icon {
  font-size: 14px;
  color: #666;
}

.node-title {
  flex: 1;
  font-size: 14px;
}

.node-tag {
  font-size: 10px;
  margin-left: auto;
}
</style>
