/**
 * 单位信息接口
 */
export interface Company {
  /** 主键ID */
  id: string;
  /** 单位名称 */
  name: string;
  /** 助记码 */
  helpChar?: string;
  /** 父单位ID */
  pid?: string;
  /** 单位编码 */
  code?: string;
  /** 联系人 */
  contact?: string;
  /** 联系电话 */
  phone?: string;
  /** 地址 */
  address?: string;
  /** 邮箱 */
  email?: string;
  /** 状态 */
  status?: string;
  /** 删除标志 */
  delFlag?: string;
  /** 备注 */
  remark?: string;
  /** 创建人 */
  createBy?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新人 */
  updateBy?: string;
  /** 更新时间 */
  updateTime?: string;
}

/**
 * 单位搜索参数
 */
export interface CompanySearchParams {
  /** 页码 */
  pageNo?: number;
  /** 页大小 */
  pageSize?: number;
  /** 单位名称（模糊查询） */
  name_LIKE?: string;
  /** 助记码（模糊查询） */
  helpChar_LIKE?: string;
  /** 父单位ID为空 */
  pid_NULL?: boolean;
  /** 搜索模式 */
  _searchMode?: 'AND' | 'OR';
  /** 状态 */
  status?: string;
  /** 删除标志 */
  delFlag?: string;
}

/**
 * 单位选项（用于下拉选择）
 */
export interface CompanyOption {
  /** 值 */
  value: string;
  /** 标签 */
  label: string;
  /** 原始数据 */
  data: Company;
}

/**
 * 单位自动完成DTO
 */
export interface CompanyAutoCompleteDTO {
  /** 单位ID */
  id: string;
  /** 单位名称 */
  name: string;
  /** 单位简称 */
  shortName?: string;
  /** 助记码 */
  helpChar?: string;
  /** 单位编码 */
  orgCode?: string;
  /** 联系电话 */
  telephone?: string;
  /** 地址 */
  address?: string;
}

/**
 * 自动完成选项（用于下拉选择）
 */
export interface CompanyAutoCompleteOption {
  /** 值 */
  value: string;
  /** 标签 */
  label: string;
  /** 原始数据 */
  data: CompanyAutoCompleteDTO;
}

/**
 * API响应结果
 */
export interface CompanyListResult {
  /** 记录列表 */
  records: Company[];
  /** 总数 */
  total: number;
  /** 当前页 */
  current: number;
  /** 页大小 */
  size: number;
}
