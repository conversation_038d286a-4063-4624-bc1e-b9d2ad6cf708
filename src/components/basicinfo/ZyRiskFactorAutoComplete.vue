<template>
  <div class="zy-risk-factor-autocomplete">
    <a-auto-complete
      v-model:value="inputValue"
      :options="options"
      :placeholder="placeholder"
      :allowClear="allowClear"
      :disabled="disabled"
      :loading="loading"
      @search="handleSearch"
      @select="handleSelect"
      @change="handleChange"
      @clear="handleClear"
      @blur="handleBlur"
      :filterOption="false"
      :notFoundContent="loading ? '搜索中...' : '暂无数据'"
    >
      <template #option="{ value: val, label, data }">
        <div class="risk-factor-option">
          <div class="risk-factor-name">{{ data.name }}</div>
          <div class="risk-factor-details">
            <span v-if="data.code && config.showCode" class="risk-factor-code">编码: {{ data.code }}</span>
            <span v-if="data.helpChar && config.showHelpChar" class="risk-factor-help-char">{{ data.helpChar }}</span>
            <span v-if="data.wubiChar && config.showWubiChar" class="risk-factor-wubi-char">五笔: {{ data.wubiChar }}</span>
          </div>
        </div>
      </template>
    </a-auto-complete>

    <!-- 多值显示区域 -->
    <div v-if="config.allowMultiple && selectedFactors.length > 0" class="selected-factors">
      <a-tag v-for="factor in selectedFactors" :key="factor.id" closable @close="removeSelectedFactor(factor.id)" class="selected-factor-tag">
        {{ factor.name }}
        <span v-if="factor.code" class="factor-code">({{ factor.code }})</span>
      </a-tag>
    </div>

    <!-- 验证状态显示 -->
    <div v-if="validationResults.length > 0" class="validation-results">
      <div v-for="result in validationResults" :key="result.inputName" :class="['validation-item', result.found ? 'success' : 'error']">
        <a-icon :type="result.found ? 'check-circle' : 'close-circle'" />
        <span class="validation-text">
          {{ result.inputName }}
          {{ result.found ? '✓' : '✗' }}
          {{ result.errorMessage || '' }}
        </span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch, computed, onMounted, nextTick } from 'vue';
  import { debounce } from 'lodash-es';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getZyRiskFactorAutoComplete, batchValidateZyRiskFactorNames, parseMultiValueInput } from '/@/api/basicinfo/zyRiskFactor';
  import { cleanRiskFactorData, detectDataIssues } from '/@/utils/zyRiskFactorUtils';
  import type {
    ZyRiskFactorAutoCompleteDTO,
    ZyRiskFactorAutoCompleteOption,
    ZyRiskFactorAutoCompleteConfig,
    ZyRiskFactorChangeEvent,
    ZyRiskFactorValidationResult,
  } from '/@/types/basicinfo/zyRiskFactor';

  interface Props {
    value?: string;
    placeholder?: string;
    allowClear?: boolean;
    disabled?: boolean;
    config?: Partial<ZyRiskFactorAutoCompleteConfig>;
  }

  interface Emits {
    (e: 'update:value', value: string): void;
    (e: 'change', event: ZyRiskFactorChangeEvent): void;
    (e: 'select', value: string, option: ZyRiskFactorAutoCompleteDTO): void;
    (e: 'validation-complete', results: ZyRiskFactorValidationResult[]): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    placeholder: '请输入危害因素名称、编码或助记码',
    allowClear: true,
    disabled: false,
    config: () => ({}),
  });

  const emit = defineEmits<Emits>();
  const { createMessage } = useMessage();

  // 默认配置
  const defaultConfig: ZyRiskFactorAutoCompleteConfig = {
    allowMultiple: false,
    separator: ' ',
    enableManualValidation: true,
    searchDebounce: 300,
    maxOptions: 50,
    showCode: true,
    showHelpChar: true,
    showWubiChar: false,
    showHarmLevel: true,
  };

  // 合并配置
  const config = computed(() => ({ ...defaultConfig, ...props.config }));

  const inputValue = ref<string>('');
  const options = ref<ZyRiskFactorAutoCompleteOption[]>([]);
  const loading = ref<boolean>(false);
  const riskFactorList = ref<ZyRiskFactorAutoCompleteDTO[]>([]);
  const selectedFactors = ref<ZyRiskFactorAutoCompleteDTO[]>([]);
  const validationResults = ref<ZyRiskFactorValidationResult[]>([]);
  const isManualInput = ref<boolean>(false);

  // 监听外部传入的value变化
  watch(
    () => props.value,
    (newVal) => {
      if (newVal !== inputValue.value) {
        inputValue.value = newVal || '';
      }
    },
    { immediate: true }
  );

  // 监听输入值变化，向外部发送更新
  watch(inputValue, (newVal) => {
    emit('update:value', newVal);
  });

  /**
   * 获取危害因素列表数据
   */
  const fetchRiskFactorList = async (keyword?: string, searchType: 'name' | 'code' | 'helpChar' | 'wubiChar' | 'all' = 'all') => {
    try {
      loading.value = true;
      console.log('ZyRiskFactorAutoComplete: 开始获取危害因素列表，关键词:', keyword, '搜索类型:', searchType);

      const response = await getZyRiskFactorAutoComplete(keyword, config.value.maxOptions, searchType);
      console.log('ZyRiskFactorAutoComplete: API响应:', response);

      if (response && response.success && response.result) {
        riskFactorList.value = response.result;
        updateOptions(keyword);
        console.log('ZyRiskFactorAutoComplete: 成功加载', response.result.length, '条数据');

        // 如果是降级模式，显示提示信息
        if (response.message && response.message.includes('降级模式')) {
          console.info('ZyRiskFactorAutoComplete: 使用降级模式加载数据');
        }
      } else {
        console.warn('ZyRiskFactorAutoComplete: API响应格式异常:', response);
        riskFactorList.value = [];
        options.value = [];
      }
    } catch (error: any) {
      console.error('ZyRiskFactorAutoComplete: 获取危害因素列表失败:', error);

      // 检查是否是缓存相关错误
      if (error?.message && error.message.includes('cache')) {
        console.warn('ZyRiskFactorAutoComplete: 检测到缓存错误，这是后端配置问题');
      }

      riskFactorList.value = [];
      options.value = [];
    } finally {
      loading.value = false;
    }
  };

  /**
   * 更新选项列表
   */
  const updateOptions = (keyword?: string) => {
    // 使用工具函数清理数据
    const { cleaned, filtered } = cleanRiskFactorData(riskFactorList.value, keyword);

    // 记录被过滤的数据
    if (filtered.length > 0) {
      console.warn('ZyRiskFactorAutoComplete: 过滤了以下无效数据:', filtered);
      filtered.forEach(({ factor, reason }) => {
        console.warn(`- ${factor.name} (ID: ${factor.id}): ${reason}`);
      });
    }

    // 检测数据问题
    const dataIssues = detectDataIssues(cleaned);
    if (dataIssues.issues.length > 0) {
      console.warn('ZyRiskFactorAutoComplete: 检测到数据质量问题:', dataIssues.issues);

      // 如果有可疑名称，额外记录
      if (dataIssues.suspiciousNames.length > 0) {
        console.warn(
          'ZyRiskFactorAutoComplete: 可疑名称列表:',
          dataIssues.suspiciousNames.map((f) => f.name)
        );
      }
    }

    // 生成选项列表
    options.value = cleaned.map((item) => ({
      value: item.id,
      label: item.name,
      data: item,
    }));

    console.log('ZyRiskFactorAutoComplete: 更新选项列表，原始数据', riskFactorList.value.length, '条，清理后', options.value.length, '个选项');

    // 如果过滤后数据明显减少，显示用户友好的提示
    if (riskFactorList.value.length > 0 && filtered.length > riskFactorList.value.length * 0.2) {
      console.warn('ZyRiskFactorAutoComplete: 大量数据被过滤，建议检查后端数据质量');

      // 在开发环境下，可以显示更详细的信息
      if (process.env.NODE_ENV === 'development') {
        createMessage.warning(`数据质量提醒：${filtered.length}条数据被过滤，请检查控制台日志`);
      }
    }
  };

  /**
   * 防抖搜索处理
   */
  const debouncedSearch = debounce(async (keyword: string) => {
    await fetchRiskFactorList(keyword);
  }, config.value.searchDebounce);

  /**
   * 搜索处理
   */
  const handleSearch = (value: string) => {
    isManualInput.value = true;
    validationResults.value = []; // 清空之前的验证结果

    if (!value || value.trim().length === 0) {
      // 如果搜索为空，显示默认数据
      fetchRiskFactorList();
    } else {
      // 在多选模式下，提取最后一个词组作为搜索关键字
      let searchKeyword = value;
      if (config.value.allowMultiple && value.includes(config.value.separator!)) {
        const parts = value.split(config.value.separator!);
        const lastPart = parts[parts.length - 1].trim();
        searchKeyword = lastPart || value; // 如果最后一部分为空，使用原始值
      }

      debouncedSearch(searchKeyword);
    }
  };

  /**
   * 选择处理
   */
  const handleSelect = (value: string, option: any) => {
    const selectedFactor = option.data as ZyRiskFactorAutoCompleteDTO;
    isManualInput.value = false;
    validationResults.value = [];

    if (config.value.allowMultiple) {
      // 多选模式
      if (!selectedFactors.value.find((f) => f.id === selectedFactor.id)) {
        selectedFactors.value.push(selectedFactor);
      }

      // 在多选模式下，等待 a-auto-complete 完成其原始行为后再清空输入框
      /*nextTick(() => {
        inputValue.value = '';
      });*/

      const event: ZyRiskFactorChangeEvent = {
        value: selectedFactors.value.map((f) => f.name).join(config.value.separator),
        selectedFactors: [...selectedFactors.value],
        isManualInput: false,
      };

      inputValue.value = event.value;
      emit('change', event);
    } else {
      // 单选模式 - 完全保持 a-auto-complete 原始行为
      // 让组件自己处理输入框的值，不做任何干预

      const event: ZyRiskFactorChangeEvent = {
        value: selectedFactor.name,
        selectedFactor,
        isManualInput: false,
      };
      emit('change', event);
    }

    emit('select', value, selectedFactor);
  };

  /**
   * 值变化处理
   */
  const handleChange = (value: string) => {
    if (!value) {
      selectedFactors.value = [];
      validationResults.value = [];
      const event: ZyRiskFactorChangeEvent = {
        value: '',
        isManualInput: false,
      };
      emit('change', event);
    }
  };

  /**
   * 清空处理
   */
  const handleClear = () => {
    inputValue.value = '';
    selectedFactors.value = [];
    validationResults.value = [];
    isManualInput.value = false;

    const event: ZyRiskFactorChangeEvent = {
      value: '',
      isManualInput: false,
    };
    emit('change', event);

    // 清空后重新加载默认数据
    fetchRiskFactorList();
  };

  /**
   * 失焦处理 - 处理手动输入验证
   */
  const handleBlur = async () => {
    if (!config.value.enableManualValidation || !isManualInput.value || !inputValue.value.trim()) {
      return;
    }

    await validateManualInput();
  };

  /**
   * 验证手动输入
   */
  const validateManualInput = async () => {
    const input = inputValue.value.trim();
    if (!input) return;

    try {
      loading.value = true;

      if (config.value.allowMultiple && input.includes(config.value.separator!)) {
        // 多值输入验证
        const parseResult = parseMultiValueInput(input, config.value.separator);
        if (parseResult.validCount > 0) {
          const response = await batchValidateZyRiskFactorNames(parseResult.names);

          if (response.success) {
            validationResults.value = response.result.results;

            // 更新选中的危害因素
            const newFactors = response.result.matchedFactors.filter((factor) => !selectedFactors.value.find((f) => f.id === factor.id));
            selectedFactors.value.push(...newFactors);

            // 清空输入框
            //inputValue.value = '';

            const event: ZyRiskFactorChangeEvent = {
              value: selectedFactors.value.map((f) => f.name).join(config.value.separator),
              selectedFactors: [...selectedFactors.value],
              isManualInput: true,
              validationResults: validationResults.value,
            };
            emit('change', event);
            emit('validation-complete', validationResults.value);

            // 显示验证结果提示
            const successCount = response.result.successCount;
            const failureCount = response.result.failureCount;
            if (successCount > 0 && failureCount > 0) {
              createMessage.warning(`验证完成：${successCount}个匹配，${failureCount}个未找到`);
            } else if (successCount > 0) {
              createMessage.success(`验证完成：${successCount}个危害因素匹配成功`);
            } else {
              createMessage.error('未找到匹配的危害因素');
            }
          }
        }
      } else {
        // 单值输入验证
        const response = await batchValidateZyRiskFactorNames([input]);

        if (response.success && response.result.results.length > 0) {
          const result = response.result.results[0];
          validationResults.value = [result];

          if (result.found && result.matchedFactor) {
            if (config.value.allowMultiple) {
              if (!selectedFactors.value.find((f) => f.id === result.matchedFactor!.id)) {
                selectedFactors.value.push(result.matchedFactor);
              }
              //inputValue.value = '';

              const event: ZyRiskFactorChangeEvent = {
                value: selectedFactors.value.map((f) => f.name).join(config.value.separator),
                selectedFactors: [...selectedFactors.value],
                isManualInput: true,
                validationResults: validationResults.value,
              };
              emit('change', event);
            } else {
              inputValue.value = result.matchedFactor.name;

              const event: ZyRiskFactorChangeEvent = {
                value: result.matchedFactor.name,
                selectedFactor: result.matchedFactor,
                isManualInput: true,
                validationResults: validationResults.value,
              };
              emit('change', event);
            }

            createMessage.success('危害因素验证成功');
          } else {
            createMessage.error(`未找到匹配的危害因素：${input}`);
          }

          emit('validation-complete', validationResults.value);
        }
      }
    } catch (error) {
      console.error('验证手动输入失败:', error);
      createMessage.error('验证失败，请重试');
    } finally {
      loading.value = false;
      isManualInput.value = false;
    }
  };

  /**
   * 移除选中的危害因素
   */
  const removeSelectedFactor = (factorId: string) => {
    selectedFactors.value = selectedFactors.value.filter((f) => f.id !== factorId);
    inputValue.value = selectedFactors.value.map((f) => f.name).join(config.value.separator);

    const event: ZyRiskFactorChangeEvent = {
      value: selectedFactors.value.map((f) => f.name).join(config.value.separator),
      selectedFactors: [...selectedFactors.value],
      isManualInput: false,
    };
    emit('change', event);
  };

  // 组件挂载时加载初始数据
  onMounted(() => {
    fetchRiskFactorList();
  });

  // 暴露方法给父组件
  defineExpose({
    validateManualInput,
    clearSelection: handleClear,
    getSelectedFactors: () => selectedFactors.value,
    getValidationResults: () => validationResults.value,
  });
</script>

<style lang="less" scoped>
  .zy-risk-factor-autocomplete {
    .risk-factor-option {
      display: flex;
      flex-direction: column;
      padding: 6px 0;

      .risk-factor-name {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 1.4;
        font-weight: 500;
      }

      .risk-factor-details {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-top: 4px;

        .risk-factor-code {
          font-size: 12px;
          color: #1890ff;
          background: #f0f8ff;
          padding: 2px 6px;
          border-radius: 4px;
          line-height: 1.2;
        }

        .risk-factor-help-char {
          font-size: 12px;
          color: #52c41a;
          background: #f6ffed;
          padding: 2px 6px;
          border-radius: 4px;
          line-height: 1.2;
        }

        .risk-factor-wubi-char {
          font-size: 12px;
          color: #722ed1;
          background: #f9f0ff;
          padding: 2px 6px;
          border-radius: 4px;
          line-height: 1.2;
        }

        .risk-factor-harm-level {
          font-size: 12px;
          color: #fa8c16;
          background: #fff7e6;
          padding: 2px 6px;
          border-radius: 4px;
          line-height: 1.2;
        }
      }
    }

    .selected-factors {
      margin-top: 8px;
      display: flex;
      flex-wrap: wrap;
      gap: 6px;

      .selected-factor-tag {
        display: inline-flex;
        align-items: center;
        max-width: 200px;

        .factor-code {
          margin-left: 4px;
          font-size: 11px;
          color: rgba(0, 0, 0, 0.45);
        }
      }
    }

    .validation-results {
      margin-top: 8px;
      padding: 8px;
      background: #fafafa;
      border-radius: 4px;
      border: 1px solid #d9d9d9;

      .validation-item {
        display: flex;
        align-items: center;
        margin-bottom: 4px;
        font-size: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        &.success {
          color: #52c41a;

          .anticon {
            color: #52c41a;
            margin-right: 6px;
          }
        }

        &.error {
          color: #ff4d4f;

          .anticon {
            color: #ff4d4f;
            margin-right: 6px;
          }
        }

        .validation-text {
          flex: 1;
        }
      }
    }
  }

  :deep(.ant-select-dropdown) {
    .ant-select-item-option-content {
      white-space: normal;
    }
  }
</style>
