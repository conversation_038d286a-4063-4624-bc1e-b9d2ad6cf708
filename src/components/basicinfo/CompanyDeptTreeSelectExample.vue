<template>
  <div class="company-dept-tree-select-example">
    <h3>部门树形选择组件使用示例</h3>
    
    <a-space direction="vertical" size="large" style="width: 100%">
      <!-- 基础用法 -->
      <a-card title="基础用法" size="small">
        <a-form layout="vertical">
          <a-form-item label="选择单位">
            <CompanyAutoComplete
              v-model:value="selectedCompanyId"
              placeholder="请先选择单位"
              @select="handleCompanySelect"
              @change="handleCompanyChange"
            />
          </a-form-item>
          
          <a-form-item label="选择部门">
            <CompanyDeptTreeSelect
              v-model:value="selectedDeptId"
              :company-id="selectedCompanyId"
              placeholder="请选择部门"
              @change="handleDeptChange"
              @select="handleDeptSelect"
            />
          </a-form-item>
          
          <a-form-item label="选中的信息">
            <a-textarea 
              :value="JSON.stringify({ 
                companyId: selectedCompanyId, 
                companyName: selectedCompany?.name,
                deptId: selectedDeptId,
                deptInfo: selectedDept 
              }, null, 2)" 
              :rows="6" 
              readonly 
            />
          </a-form-item>
        </a-form>
      </a-card>

      <!-- 只显示部门 -->
      <a-card title="只显示部门（不显示单位）" size="small">
        <a-form layout="vertical">
          <a-form-item label="选择单位">
            <CompanyAutoComplete
              v-model:value="selectedCompanyId2"
              placeholder="请先选择单位"
              @select="handleCompanySelect2"
            />
          </a-form-item>
          
          <a-form-item label="选择部门">
            <CompanyDeptTreeSelect
              v-model:value="selectedDeptId2"
              :company-id="selectedCompanyId2"
              :only-departments="true"
              placeholder="请选择部门（仅部门）"
              @change="handleDeptChange2"
            />
          </a-form-item>
          
          <a-form-item label="选中的部门ID">
            <a-input :value="selectedDeptId2" readonly />
          </a-form-item>
        </a-form>
      </a-card>

      <!-- 禁用状态 -->
      <a-card title="禁用状态" size="small">
        <a-form layout="vertical">
          <a-form-item label="禁用的部门选择器">
            <CompanyDeptTreeSelect
              v-model:value="disabledDeptId"
              :company-id="selectedCompanyId"
              :disabled="true"
              placeholder="禁用状态"
            />
          </a-form-item>
        </a-form>
      </a-card>

      <!-- 不允许清空 -->
      <a-card title="不允许清空" size="small">
        <a-form layout="vertical">
          <a-form-item label="选择单位">
            <CompanyAutoComplete
              v-model:value="selectedCompanyId3"
              placeholder="请先选择单位"
              @select="handleCompanySelect3"
            />
          </a-form-item>
          
          <a-form-item label="选择部门（不可清空）">
            <CompanyDeptTreeSelect
              v-model:value="selectedDeptId3"
              :company-id="selectedCompanyId3"
              :allow-clear="false"
              placeholder="请选择部门（不可清空）"
            />
          </a-form-item>
        </a-form>
      </a-card>

      <!-- 表单集成示例 -->
      <a-card title="表单集成示例" size="small">
        <a-form 
          ref="formRef"
          :model="formData"
          :rules="formRules"
          layout="vertical"
        >
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="客户姓名" name="customerName">
                <a-input v-model:value="formData.customerName" placeholder="请输入客户姓名" />
              </a-form-item>
            </a-col>
            
            <a-col :span="12">
              <a-form-item label="所属单位" name="companyId">
                <CompanyAutoComplete
                  v-model:value="formData.companyId"
                  placeholder="请选择单位"
                  @select="handleFormCompanySelect"
                />
              </a-form-item>
            </a-col>
            
            <a-col :span="12">
              <a-form-item label="所属部门" name="deptId">
                <CompanyDeptTreeSelect
                  v-model:value="formData.deptId"
                  :company-id="formData.companyId"
                  placeholder="请选择部门"
                />
              </a-form-item>
            </a-col>
            
            <a-col :span="12">
              <a-form-item label="联系电话" name="phone">
                <a-input v-model:value="formData.phone" placeholder="请输入联系电话" />
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-form-item>
            <a-space>
              <a-button type="primary" @click="handleSubmit">提交</a-button>
              <a-button @click="handleReset">重置</a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>

      <!-- 调试信息 -->
      <a-card title="调试信息" size="small">
        <a-descriptions :column="2" bordered size="small">
          <a-descriptions-item label="选中单位ID">{{ selectedCompanyId || '未选择' }}</a-descriptions-item>
          <a-descriptions-item label="选中部门ID">{{ selectedDeptId || '未选择' }}</a-descriptions-item>
          <a-descriptions-item label="单位名称">{{ selectedCompany?.name || '未选择' }}</a-descriptions-item>
          <a-descriptions-item label="部门名称">{{ selectedDept?.title || '未选择' }}</a-descriptions-item>
        </a-descriptions>
      </a-card>
    </a-space>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import { message } from 'ant-design-vue';
  import CompanyAutoComplete from './CompanyAutoComplete.vue';
  import CompanyDeptTreeSelect from './CompanyDeptTreeSelect.vue';
  import type { CompanyAutoCompleteDTO } from '/@/types/basicinfo/company';
  import type { DepartmentTreeNode } from '/@/types/basicinfo/department';

  // 基础用法数据
  const selectedCompanyId = ref<string>('');
  const selectedDeptId = ref<string>('');
  const selectedCompany = ref<CompanyAutoCompleteDTO | null>(null);
  const selectedDept = ref<DepartmentTreeNode | null>(null);

  // 只显示部门
  const selectedCompanyId2 = ref<string>('');
  const selectedDeptId2 = ref<string>('');

  // 禁用状态
  const disabledDeptId = ref<string>('');

  // 不允许清空
  const selectedCompanyId3 = ref<string>('');
  const selectedDeptId3 = ref<string>('');

  // 表单数据
  const formRef = ref();
  const formData = reactive({
    customerName: '',
    companyId: '',
    deptId: '',
    phone: '',
  });

  // 表单验证规则
  const formRules = {
    customerName: [
      { required: true, message: '请输入客户姓名' }
    ],
    companyId: [
      { required: true, message: '请选择所属单位' }
    ],
    deptId: [
      { required: true, message: '请选择所属部门' }
    ],
    phone: [
      { required: true, message: '请输入联系电话' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
    ],
  };

  // 基础用法事件处理
  const handleCompanySelect = (value: string, option: CompanyAutoCompleteDTO) => {
    console.log('选中单位:', value, option);
    selectedCompany.value = option;
    selectedDeptId.value = ''; // 清空部门选择
    selectedDept.value = null;
  };

  const handleCompanyChange = (value: string, option?: CompanyAutoCompleteDTO) => {
    if (!value) {
      selectedCompany.value = null;
      selectedDeptId.value = '';
      selectedDept.value = null;
    }
  };

  const handleDeptChange = (value: string | undefined, node?: DepartmentTreeNode) => {
    console.log('部门变化:', value, node);
    selectedDept.value = node || null;
  };

  const handleDeptSelect = (value: string, node: DepartmentTreeNode) => {
    console.log('选择部门:', value, node);
    message.success(`已选择部门: ${node.title}`);
  };

  // 只显示部门事件处理
  const handleCompanySelect2 = (value: string, option: CompanyAutoCompleteDTO) => {
    selectedDeptId2.value = ''; // 清空部门选择
  };

  const handleDeptChange2 = (value: string | undefined, node?: DepartmentTreeNode) => {
    console.log('部门变化2:', value, node);
  };

  // 不允许清空事件处理
  const handleCompanySelect3 = (value: string, option: CompanyAutoCompleteDTO) => {
    selectedDeptId3.value = ''; // 清空部门选择
  };

  // 表单事件处理
  const handleFormCompanySelect = (value: string, option: CompanyAutoCompleteDTO) => {
    formData.deptId = ''; // 清空部门选择
  };

  const handleSubmit = async () => {
    try {
      await formRef.value?.validate();
      console.log('表单数据:', formData);
      message.success('提交成功！');
    } catch (error) {
      console.error('表单验证失败:', error);
      message.error('请检查表单填写是否正确');
    }
  };

  const handleReset = () => {
    formRef.value?.resetFields();
    Object.assign(formData, {
      customerName: '',
      companyId: '',
      deptId: '',
      phone: '',
    });
  };
</script>

<style scoped>
.company-dept-tree-select-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

:deep(.ant-card-head-title) {
  font-size: 16px;
  font-weight: 600;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}
</style>
