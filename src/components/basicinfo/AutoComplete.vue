<template>
  <div class="auto-complete-wrapper">
    <a-auto-complete
      v-model:value="inputValue"
      :options="options"
      :placeholder="placeholder"
      :disabled="disabled"
      :allowClear="allowClear"
      :size="size"
      :filterOption="false"
      :getPopupContainer="getPopupContainer"
      :dropdownMatchSelectWidth="dropdownMatchSelectWidth"
      @search="handleSearch"
      @select="handleSelect"
      @change="handleChange"
      @focus="handleFocus"
      @blur="handleBlur"
    >
      <template #option="{ value, label, helpChar, useCount, isNew, matchType }">
        <div class="option-item">
          <div class="option-main">
            <span class="option-text" :title="label">{{ label }}</span>
            <!--            <span v-if="helpChar && showHelpChar" class="help-char">{{ helpChar }}</span>-->
            <!--            <span v-if="isNew" class="new-tag">新建</span>-->
          </div>
          <!--          <div v-if="showUseCount && useCount > 0" class="option-meta">-->
          <!--            <span class="use-count">{{ useCount }}次</span>-->
          <!--          </div>-->
        </div>
      </template>
    </a-auto-complete>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch, computed, onMounted, nextTick } from 'vue';
  import { message } from 'ant-design-vue';
  import { useDebounceFn } from '@vueuse/core';
  import {
    autoCompleteSearch,
    getPopularItems,
    autoCreateItem,
    updateUseCount,
    type AutoCompleteResult,
    type AutoCompleteSearchParams,
    type AutoCompleteCreateParams,
  } from './AutoComplete.api';

  interface OptionItem {
    value: string;
    label: string;
    helpChar?: string;
    useCount?: number;
    isNew?: boolean;
    matchType?: string;
    raw?: AutoCompleteResult;
  }

  const props = defineProps({
    value: {
      type: String,
      default: '',
    },
    category: {
      type: String,
      required: true,
    },
    placeholder: {
      type: String,
      default: '请输入内容',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    allowClear: {
      type: Boolean,
      default: true,
    },
    size: {
      type: String,
      default: 'middle',
    },
    limit: {
      type: Number,
      default: 10,
    },
    searchType: {
      type: String as () => 'name' | 'helpChar' | 'both',
      default: 'both',
    },
    allowAutoCreate: {
      type: Boolean,
      default: true,
    },
    showHelpChar: {
      type: Boolean,
      default: true,
    },
    showUseCount: {
      type: Boolean,
      default: true,
    },
    dropdownMatchSelectWidth: {
      type: Boolean,
      default: true,
    },
    getPopupContainer: {
      type: Function,
      default: undefined,
    },
    // 是否在获得焦点时显示热门推荐
    showPopularOnFocus: {
      type: Boolean,
      default: true,
    },
    // 是否自动更新使用频次
    autoUpdateUseCount: {
      type: Boolean,
      default: true,
    },
  });

  const emit = defineEmits(['update:value', 'change', 'select', 'create', 'focus', 'blur']);

  const inputValue = ref(props.value);
  const options = ref<OptionItem[]>([]);
  const loading = ref(false);
  const focused = ref(false);

  // 监听外部value变化
  watch(
    () => props.value,
    (newVal) => {
      inputValue.value = newVal;
    }
  );

  // 监听内部value变化
  watch(inputValue, (newVal) => {
    emit('update:value', newVal);
  });

  // 防抖搜索
  const debouncedSearch = useDebounceFn(async (keyword: string) => {
    if (!props.category) {
      console.warn('AutoComplete: category is required');
      return;
    }

    try {
      loading.value = true;
      const params: AutoCompleteSearchParams = {
        category: props.category,
        keyword: keyword,
        limit: props.limit,
        searchType: props.searchType,
        includePopular: !keyword, // 没有关键词时包含热门推荐
      };

      const response = await autoCompleteSearch(params);

      if (response.success) {
        const records = response.result || [];

        // 转换为选项格式
        const newOptions = records.map((item: AutoCompleteResult) => ({
          value: item.name,
          label: item.name,
          helpChar: item.helpChar,
          useCount: item.useCount || 0,
          isNew: item.isNew || false,
          matchType: item.matchType,
          raw: item,
        }));

        options.value = newOptions;
      } else {
        message.error(response.message || '搜索失败');
      }
    } catch (error) {
      console.error('自动补全搜索失败:', error);
      message.error('搜索失败');
    } finally {
      loading.value = false;
    }
  }, 300);

  // 加载热门选项
  const loadPopularOptions = async () => {
    if (!props.category) return;

    try {
      loading.value = true;
      const response = await getPopularItems(props.category, props.limit);

      if (response.success) {
        const records = response.result || [];
        options.value = records.map((item: AutoCompleteResult) => ({
          value: item.name,
          label: item.name,
          helpChar: item.helpChar,
          useCount: item.useCount || 0,
          isNew: false,
          matchType: 'popular',
          raw: item,
        }));
      }
    } catch (error) {
      console.error('加载热门选项失败:', error);
    } finally {
      loading.value = false;
    }
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    debouncedSearch(value);
  };

  // 处理选择
  const handleSelect = async (value: string, option: OptionItem) => {
    const selectedItem = option.raw;

    // 确保选中的值正确设置到输入框中
    inputValue.value = option.label || value;

    // 如果是新建项，自动创建
    if (option.isNew && props.allowAutoCreate) {
      try {
        const createParams: AutoCompleteCreateParams = {
          category: props.category,
          name: value,
        };

        const response = await autoCreateItem(createParams);
        if (response.success) {
          emit('create', response.result);
          message.success('新项目创建成功');
        }
      } catch (error) {
        console.error('自动创建失败:', error);
        message.error('创建失败');
      }
    }

    // 更新使用频次
    if (selectedItem?.id && props.autoUpdateUseCount) {
      try {
        await updateUseCount(selectedItem.id);
      } catch (error) {
        console.error('更新使用频次失败:', error);
      }
    }

    emit('select', value, option);
  };

  // 处理值变化
  const handleChange = (value: string) => {
    emit('change', value);
  };

  // 处理获得焦点
  const handleFocus = () => {
    focused.value = true;

    // 如果没有选项且启用了焦点显示热门推荐，加载热门选项
    if (props.showPopularOnFocus && options.value.length === 0) {
      loadPopularOptions();
    }

    emit('focus');
  };

  // 处理失去焦点
  const handleBlur = () => {
    focused.value = false;
    emit('blur');
  };

  // 组件挂载时的初始化
  onMounted(() => {
    // 如果有初始值，触发搜索
    if (inputValue.value) {
      nextTick(() => {
        debouncedSearch(inputValue.value);
      });
    }
  });

  // 暴露方法给父组件
  defineExpose({
    loadPopularOptions,
    clearOptions: () => {
      options.value = [];
    },
    refresh: () => {
      if (inputValue.value) {
        debouncedSearch(inputValue.value);
      } else {
        loadPopularOptions();
      }
    },
  });
</script>

<style lang="less" scoped>
  .auto-complete-wrapper {
    width: 100%;

    .option-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 4px 0;

      .option-main {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;

        .option-text {
          flex: 1;
          min-width: 0;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .help-char {
          margin-left: 8px;
          padding: 2px 6px;
          background-color: #f0f0f0;
          border-radius: 2px;
          font-size: 12px;
          color: #666;
          flex-shrink: 0;
        }

        .new-tag {
          margin-left: 8px;
          padding: 2px 6px;
          background-color: #52c41a;
          color: white;
          border-radius: 2px;
          font-size: 12px;
          flex-shrink: 0;
        }
      }

      .option-meta {
        margin-left: 8px;
        flex-shrink: 0;

        .use-count {
          font-size: 12px;
          color: #999;
        }
      }
    }
  }
</style>
