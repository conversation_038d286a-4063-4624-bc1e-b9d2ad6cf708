# WorktypeAutoComplete 工种自动补全组件

## 概述

WorktypeAutoComplete 是专门为工种选择场景设计的自动补全组件，基于通用的 AutoComplete 组件进行封装，提供了工种特有的功能和样式优化。

## 功能特性

- ✅ **专业工种库**：内置常用工种数据，支持智能匹配
- ✅ **助记码支持**：支持拼音首字母快速输入（如：电工 -> DG）
- ✅ **自动创建**：输入不存在的工种时可自动创建
- ✅ **使用统计**：记录工种使用频次，热门工种优先显示
- ✅ **输入验证**：支持工种名称格式验证
- ✅ **响应式设计**：适配不同屏幕尺寸
- ✅ **缓存优化**：后端缓存机制，提升查询性能

## 基本用法

### 简单使用

```vue
<template>
  <div>
    <WorktypeAutoComplete
      v-model:value="worktype"
      placeholder="请输入工种名称"
      @select="handleWorktypeSelect"
    />
    <p>选择的工种：{{ worktype }}</p>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import WorktypeAutoComplete from '@/components/basicinfo/WorktypeAutoComplete.vue';

const worktype = ref('');

const handleWorktypeSelect = (value, option) => {
  console.log('选择的工种:', value, option);
};
</script>
```

### 表单中使用

```vue
<template>
  <a-form :model="form" :rules="rules" @finish="handleSubmit">
    <a-form-item label="工种" name="worktype">
      <WorktypeAutoComplete
        v-model:value="form.worktype"
        placeholder="请输入工种名称或助记码"
        :validateWorktype="true"
        @select="handleWorktypeSelect"
      />
    </a-form-item>
    
    <a-form-item>
      <a-button type="primary" html-type="submit">提交</a-button>
    </a-form-item>
  </a-form>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';
import WorktypeAutoComplete from '@/components/basicinfo/WorktypeAutoComplete.vue';

const form = reactive({
  worktype: ''
});

const rules = {
  worktype: [
    { required: true, message: '请选择工种', trigger: 'blur' }
  ]
};

const handleWorktypeSelect = (value, option) => {
  console.log('选择的工种:', value, option);
  if (option.isNew) {
    message.success(`新工种"${value}"已创建`);
  }
};

const handleSubmit = (values) => {
  console.log('表单数据:', values);
};
</script>
```

### 高级配置

```vue
<template>
  <WorktypeAutoComplete
    v-model:value="worktype"
    placeholder="请输入工种名称（支持拼音首字母）"
    :limit="15"
    searchType="both"
    :allowAutoCreate="true"
    :showHelpChar="true"
    :showUseCount="true"
    :validateWorktype="true"
    :worktypeRules="customRules"
    @select="handleSelect"
    @create="handleCreate"
    @change="handleChange"
    ref="worktypeRef"
  />
</template>

<script setup>
import { ref } from 'vue';
import WorktypeAutoComplete from '@/components/basicinfo/WorktypeAutoComplete.vue';

const worktype = ref('');
const worktypeRef = ref();

// 自定义验证规则
const customRules = [
  (value) => {
    if (value.includes('测试')) {
      return '工种名称不能包含"测试"';
    }
    return true;
  },
  (value) => {
    if (value.length < 2) {
      return '工种名称至少2个字符';
    }
    return true;
  }
];

const handleSelect = (value, option) => {
  console.log('选择工种:', value, option);
};

const handleCreate = (newItem) => {
  console.log('创建新工种:', newItem);
};

const handleChange = (value) => {
  console.log('工种变化:', value);
};

// 使用组件方法
const clearWorktype = () => {
  worktypeRef.value?.clear();
};

const focusWorktype = () => {
  worktypeRef.value?.focus();
};
</script>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 | 必填 |
|------|------|------|--------|------|
| value | 绑定值 | string | '' | 否 |
| placeholder | 占位符 | string | '请输入工种名称或助记码' | 否 |
| disabled | 是否禁用 | boolean | false | 否 |
| allowClear | 是否显示清除按钮 | boolean | true | 否 |
| size | 输入框大小 | 'large' \| 'middle' \| 'small' | 'middle' | 否 |
| limit | 最大显示数量 | number | 10 | 否 |
| searchType | 搜索类型 | 'name' \| 'helpChar' \| 'both' | 'both' | 否 |
| allowAutoCreate | 是否允许自动创建 | boolean | true | 否 |
| showHelpChar | 是否显示助记码 | boolean | true | 否 |
| showUseCount | 是否显示使用次数 | boolean | true | 否 |
| dropdownMatchSelectWidth | 下拉菜单和选择器同宽 | boolean | true | 否 |
| getPopupContainer | 菜单渲染父节点 | Function | - | 否 |
| showPopularOnFocus | 获得焦点时显示热门推荐 | boolean | true | 否 |
| autoUpdateUseCount | 自动更新使用频次 | boolean | true | 否 |
| validateWorktype | 是否启用工种验证 | boolean | false | 否 |
| worktypeRules | 自定义验证规则 | Function[] | [] | 否 |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| update:value | 值变化时触发 | (value: string) |
| change | 值变化时触发 | (value: string) |
| select | 选择工种时触发 | (value: string, option: OptionItem) |
| create | 创建新工种时触发 | (newItem: any) |
| focus | 获得焦点时触发 | () |
| blur | 失去焦点时触发 | () |

### Methods

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|--------|
| loadPopularOptions | 加载热门工种 | () | void |
| clearOptions | 清空选项 | () | void |
| refresh | 刷新选项 | () | void |
| validateWorktype | 验证工种 | (value: string) | boolean |
| getWorktypeSuggestions | 获取工种建议 | () | string[] |
| getValue | 获取当前值 | () | string |
| setValue | 设置值 | (value: string) | void |
| clear | 清空值 | () | void |
| focus | 获取焦点 | () | void |
| blur | 失去焦点 | () | void |

## 常用工种列表

组件内置了常用工种数据，包括但不限于：

### 技术类工种
- 电工 (DG)
- 焊工 (HG)  
- 机械工 (JXG)
- 维修工 (WXG)
- 技术员 (JSY)

### 操作类工种
- 操作工 (CZG)
- 装配工 (ZPG)
- 包装工 (BZG)
- 搬运工 (BYG)
- 司机 (SJ)

### 管理类工种
- 管理员 (GLY)
- 质检员 (ZJY)
- 安全员 (AQY)
- 会计 (KJ)
- 文员 (WY)

### 服务类工种
- 清洁工 (QJG)
- 保安 (BA)
- 厨师 (CS)
- 服务员 (FWY)
- 销售员 (XSY)

## 助记码规则

助记码采用拼音首字母大写的方式生成：

- 电工 → DG (Dian Gong)
- 焊工 → HG (Han Gong)
- 司机 → SJ (Si Ji)
- 操作工 → CZG (Cao Zuo Gong)
- 维修工 → WXG (Wei Xiu Gong)

## 样式定制

```less
.worktype-auto-complete {
  // 自定义工种组件样式
  
  :deep(.ant-select-dropdown) {
    .option-item {
      .help-char {
        background-color: #e6f7ff; // 助记码背景色
        color: #1890ff;            // 助记码文字色
        border: 1px solid #91d5ff; // 助记码边框
      }
      
      .new-tag {
        background-color: #52c41a;  // 新建标签背景色
        color: white;               // 新建标签文字色
      }
    }
  }
}
```

## 最佳实践

### 1. 表单验证集成

```vue
<a-form-item 
  label="工种" 
  name="worktype"
  :rules="[
    { required: true, message: '请选择工种' },
    { validator: validateWorktypeRule }
  ]"
>
  <WorktypeAutoComplete
    v-model:value="form.worktype"
    :validateWorktype="true"
  />
</a-form-item>
```

### 2. 批量操作

```vue
<script setup>
const selectedWorktypes = ref([]);

const handleBatchSelect = (worktypes) => {
  // 批量更新使用频次
  const ids = worktypes.map(w => w.id).filter(Boolean);
  if (ids.length > 0) {
    batchUpdateWorktypeUseCount(ids);
  }
};
</script>
```

### 3. 数据联动

```vue
<script setup>
const worktype = ref('');
const relatedData = ref([]);

watch(worktype, async (newWorktype) => {
  if (newWorktype) {
    // 根据工种加载相关数据
    relatedData.value = await loadRelatedData(newWorktype);
  }
});
</script>
```

## 注意事项

1. **数据同步**：组件会自动同步工种数据到后端，确保数据一致性
2. **缓存机制**：首次加载后会缓存常用工种，提升后续查询速度
3. **输入验证**：启用 validateWorktype 后会进行基本格式验证
4. **响应式适配**：在小屏幕设备上会自动隐藏助记码显示
5. **性能优化**：大量数据场景下建议适当调整 limit 参数
