<template>
  <div class="zy-risk-factor-example">
    <a-card title="危害因素自动完成组件示例" class="example-card">
      
      <!-- 单选模式示例 -->
      <div class="example-section">
        <h3>单选模式</h3>
        <p>支持汉字、助记码、五笔码搜索，支持手动输入验证</p>
        
        <ZyRiskFactorAutoComplete
          v-model:value="singleValue"
          :config="singleConfig"
          @change="handleSingleChange"
          @select="handleSingleSelect"
          @validation-complete="handleValidationComplete"
        />
        
        <div v-if="singleSelectedFactor" class="selected-info">
          <h4>选中的危害因素：</h4>
          <a-descriptions :column="2" size="small" bordered>
            <a-descriptions-item label="ID">{{ singleSelectedFactor.id }}</a-descriptions-item>
            <a-descriptions-item label="名称">{{ singleSelectedFactor.name }}</a-descriptions-item>
            <a-descriptions-item label="编码">{{ singleSelectedFactor.code }}</a-descriptions-item>
            <a-descriptions-item label="助记码">{{ singleSelectedFactor.helpChar }}</a-descriptions-item>
            <a-descriptions-item label="五笔码">{{ singleSelectedFactor.wubiChar }}</a-descriptions-item>
            <a-descriptions-item label="危害等级">{{ singleSelectedFactor.harmLevel }}</a-descriptions-item>
          </a-descriptions>
        </div>
      </div>

      <a-divider />

      <!-- 多选模式示例 -->
      <div class="example-section">
        <h3>多选模式</h3>
        <p>支持空格分隔多个危害因素名称，自动验证和匹配</p>
        
        <ZyRiskFactorAutoComplete
          v-model:value="multiValue"
          :config="multiConfig"
          @change="handleMultiChange"
          @validation-complete="handleValidationComplete"
        />
        
        <div v-if="multiSelectedFactors.length > 0" class="selected-info">
          <h4>选中的危害因素列表：</h4>
          <a-table
            :dataSource="multiSelectedFactors"
            :columns="tableColumns"
            :pagination="false"
            size="small"
            bordered
          />
        </div>
      </div>

      <a-divider />

      <!-- 手动验证示例 -->
      <div class="example-section">
        <h3>手动批量验证</h3>
        <p>输入多个危害因素名称（用空格分隔），点击验证按钮进行批量验证</p>
        
        <a-textarea
          v-model:value="manualInput"
          placeholder="请输入危害因素名称，用空格分隔，例如：噪声 粉尘 高温 振动"
          :rows="3"
          class="manual-input"
        />
        
        <div class="manual-actions">
          <a-button type="primary" @click="handleManualValidation" :loading="validating">
            批量验证
          </a-button>
          <a-button @click="clearManualInput">清空</a-button>
          <a-button v-if="validationResults.length > 0" @click="exportResults">
            导出结果
          </a-button>
        </div>
        
        <div v-if="validationResults.length > 0" class="validation-summary">
          <a-alert
            :message="`验证完成：共${validationSummary.total}个，成功${validationSummary.success}个，失败${validationSummary.failure}个`"
            :type="validationSummary.failure === 0 ? 'success' : 'warning'"
            show-icon
            class="summary-alert"
          />
          
          <a-table
            :dataSource="validationResults"
            :columns="validationColumns"
            :pagination="false"
            size="small"
            bordered
            class="validation-table"
          />
        </div>
      </div>

      <a-divider />

      <!-- 配置选项 -->
      <div class="example-section">
        <h3>配置选项</h3>
        <a-form layout="inline">
          <a-form-item label="显示编码">
            <a-switch v-model:checked="configOptions.showCode" />
          </a-form-item>
          <a-form-item label="显示助记码">
            <a-switch v-model:checked="configOptions.showHelpChar" />
          </a-form-item>
          <a-form-item label="显示五笔码">
            <a-switch v-model:checked="configOptions.showWubiChar" />
          </a-form-item>
          <a-form-item label="显示危害等级">
            <a-switch v-model:checked="configOptions.showHarmLevel" />
          </a-form-item>
        </a-form>
      </div>

    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, reactive } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import ZyRiskFactorAutoComplete from './ZyRiskFactorAutoComplete.vue';
  import { zyRiskFactorValidationService } from '/@/services/zyRiskFactorValidationService';
  import { parseMultiValueInputAdvanced } from '/@/utils/zyRiskFactorUtils';
  import type {
    ZyRiskFactorAutoCompleteDTO,
    ZyRiskFactorChangeEvent,
    ZyRiskFactorValidationResult,
    ZyRiskFactorAutoCompleteConfig,
  } from '/@/types/basicinfo/zyRiskFactor';

  const { createMessage } = useMessage();

  // 单选模式
  const singleValue = ref<string>('');
  const singleSelectedFactor = ref<ZyRiskFactorAutoCompleteDTO | null>(null);

  // 多选模式
  const multiValue = ref<string>('');
  const multiSelectedFactors = ref<ZyRiskFactorAutoCompleteDTO[]>([]);

  // 手动验证
  const manualInput = ref<string>('');
  const validating = ref<boolean>(false);
  const validationResults = ref<ZyRiskFactorValidationResult[]>([]);

  // 配置选项
  const configOptions = reactive({
    showCode: true,
    showHelpChar: true,
    showWubiChar: false,
    showHarmLevel: true,
  });

  // 单选配置
  const singleConfig = computed<Partial<ZyRiskFactorAutoCompleteConfig>>(() => ({
    allowMultiple: false,
    enableManualValidation: true,
    ...configOptions,
  }));

  // 多选配置
  const multiConfig = computed<Partial<ZyRiskFactorAutoCompleteConfig>>(() => ({
    allowMultiple: true,
    separator: ' ',
    enableManualValidation: true,
    ...configOptions,
  }));

  // 表格列定义
  const tableColumns = [
    { title: 'ID', dataIndex: 'id', key: 'id', width: 100 },
    { title: '名称', dataIndex: 'name', key: 'name' },
    { title: '编码', dataIndex: 'code', key: 'code' },
    { title: '助记码', dataIndex: 'helpChar', key: 'helpChar' },
    { title: '危害等级', dataIndex: 'harmLevel', key: 'harmLevel' },
  ];

  const validationColumns = [
    { title: '输入名称', dataIndex: 'inputName', key: 'inputName' },
    {
      title: '验证结果',
      dataIndex: 'found',
      key: 'found',
      customRender: ({ record }: { record: ZyRiskFactorValidationResult }) => (
        record.found ? '✅ 成功' : '❌ 失败'
      ),
    },
    {
      title: '匹配名称',
      dataIndex: 'matchedFactor',
      key: 'matchedName',
      customRender: ({ record }: { record: ZyRiskFactorValidationResult }) => (
        record.matchedFactor?.name || '-'
      ),
    },
    {
      title: '匹配编码',
      dataIndex: 'matchedFactor',
      key: 'matchedCode',
      customRender: ({ record }: { record: ZyRiskFactorValidationResult }) => (
        record.matchedFactor?.code || '-'
      ),
    },
    { title: '错误信息', dataIndex: 'errorMessage', key: 'errorMessage' },
  ];

  // 验证结果摘要
  const validationSummary = computed(() => {
    return zyRiskFactorValidationService.getValidationSummary(validationResults.value);
  });

  /**
   * 单选变化处理
   */
  const handleSingleChange = (event: ZyRiskFactorChangeEvent) => {
    console.log('单选变化:', event);
    singleSelectedFactor.value = event.selectedFactor || null;
    
    if (event.isManualInput && event.validationResults) {
      createMessage.info('手动输入验证完成');
    }
  };

  /**
   * 单选选择处理
   */
  const handleSingleSelect = (value: string, factor: ZyRiskFactorAutoCompleteDTO) => {
    console.log('单选选择:', value, factor);
    createMessage.success(`选择了危害因素：${factor.name}`);
  };

  /**
   * 多选变化处理
   */
  const handleMultiChange = (event: ZyRiskFactorChangeEvent) => {
    console.log('多选变化:', event);
    multiSelectedFactors.value = event.selectedFactors || [];
    
    if (event.isManualInput && event.validationResults) {
      const summary = zyRiskFactorValidationService.getValidationSummary(event.validationResults);
      createMessage.info(`批量验证完成：${summary.success}个成功，${summary.failure}个失败`);
    }
  };

  /**
   * 验证完成处理
   */
  const handleValidationComplete = (results: ZyRiskFactorValidationResult[]) => {
    console.log('验证完成:', results);
    validationResults.value = results;
  };

  /**
   * 手动验证处理
   */
  const handleManualValidation = async () => {
    if (!manualInput.value.trim()) {
      createMessage.warning('请输入要验证的危害因素名称');
      return;
    }

    try {
      validating.value = true;
      
      const { parseResult, validationResponse } = await zyRiskFactorValidationService.validateMultiValueInput(
        manualInput.value,
        ' ',
        true
      );

      validationResults.value = validationResponse.results;
      
      console.log('手动验证结果:', { parseResult, validationResponse });
    } catch (error) {
      console.error('手动验证失败:', error);
      createMessage.error('验证失败，请重试');
    } finally {
      validating.value = false;
    }
  };

  /**
   * 清空手动输入
   */
  const clearManualInput = () => {
    manualInput.value = '';
    validationResults.value = [];
  };

  /**
   * 导出验证结果
   */
  const exportResults = () => {
    zyRiskFactorValidationService.downloadValidationResultsCSV(validationResults.value);
    createMessage.success('验证结果已导出');
  };
</script>

<style lang="less" scoped>
  .zy-risk-factor-example {
    padding: 20px;

    .example-card {
      max-width: 1200px;
      margin: 0 auto;
    }

    .example-section {
      margin-bottom: 24px;

      h3 {
        margin-bottom: 8px;
        color: #1890ff;
      }

      p {
        margin-bottom: 16px;
        color: rgba(0, 0, 0, 0.65);
      }
    }

    .selected-info {
      margin-top: 16px;
      padding: 16px;
      background: #fafafa;
      border-radius: 6px;

      h4 {
        margin-bottom: 12px;
        color: #1890ff;
      }
    }

    .manual-input {
      margin-bottom: 12px;
    }

    .manual-actions {
      margin-bottom: 16px;
      display: flex;
      gap: 8px;
    }

    .validation-summary {
      .summary-alert {
        margin-bottom: 16px;
      }

      .validation-table {
        :deep(.ant-table-tbody) {
          tr.ant-table-row {
            &:nth-child(odd) {
              background-color: #fafafa;
            }
          }
        }
      }
    }
  }
</style>
