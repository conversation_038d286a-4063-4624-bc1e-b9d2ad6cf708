# AutoComplete 通用自动补全组件

## 概述

AutoComplete 是一个基于 Ant Design Vue 的 a-auto-complete 组件开发的通用自动补全组件，支持名称和助记码匹配、自动创建新项目、使用频次统计等功能。

## 功能特性

- ✅ **智能搜索**：支持按名称和助记码进行模糊搜索
- ✅ **自动完成**：实时显示匹配的选项
- ✅ **自动创建**：当输入的内容不存在时，可自动创建新项目
- ✅ **使用频次**：显示项目使用频次，自动统计使用次数
- ✅ **热门推荐**：初始化时显示热门选项
- ✅ **缓存机制**：后端支持Redis缓存，提升查询性能
- ✅ **美观界面**：精美的下拉选项样式和动画效果
- ✅ **配置灵活**：支持多种配置选项，适应不同场景

## 基本用法

### 简单使用

```vue
<template>
  <AutoComplete
    v-model:value="selectedValue"
    category="worktype"
    placeholder="请输入工种名称"
    @select="handleSelect"
  />
</template>

<script setup>
import { ref } from 'vue';
import AutoComplete from '@/components/basicinfo/AutoComplete.vue';

const selectedValue = ref('');

const handleSelect = (value, option) => {
  console.log('选择的项目:', value, option);
};
</script>
```

### 高级配置

```vue
<template>
  <AutoComplete
    v-model:value="selectedValue"
    category="worktype"
    placeholder="请输入工种名称或助记码"
    :limit="15"
    searchType="both"
    :allowAutoCreate="true"
    :showHelpChar="true"
    :showUseCount="true"
    :autoUpdateUseCount="true"
    :showPopularOnFocus="true"
    @select="handleSelect"
    @create="handleCreate"
    @change="handleChange"
  />
</template>

<script setup>
import { ref } from 'vue';
import AutoComplete from '@/components/basicinfo/AutoComplete.vue';

const selectedValue = ref('');

const handleSelect = (value, option) => {
  console.log('选择的项目:', value, option);
};

const handleCreate = (newItem) => {
  console.log('创建的新项目:', newItem);
};

const handleChange = (value) => {
  console.log('值变化:', value);
};
</script>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 | 必填 |
|------|------|------|--------|------|
| value | 绑定值 | string | '' | 否 |
| category | 分类标识 | string | - | 是 |
| placeholder | 占位符 | string | '请输入内容' | 否 |
| disabled | 是否禁用 | boolean | false | 否 |
| allowClear | 是否显示清除按钮 | boolean | true | 否 |
| size | 输入框大小 | 'large' \| 'middle' \| 'small' | 'middle' | 否 |
| limit | 最大显示数量 | number | 10 | 否 |
| searchType | 搜索类型 | 'name' \| 'helpChar' \| 'both' | 'both' | 否 |
| allowAutoCreate | 是否允许自动创建 | boolean | true | 否 |
| showHelpChar | 是否显示助记码 | boolean | true | 否 |
| showUseCount | 是否显示使用次数 | boolean | true | 否 |
| dropdownMatchSelectWidth | 下拉菜单和选择器同宽 | boolean | true | 否 |
| getPopupContainer | 菜单渲染父节点 | Function | - | 否 |
| showPopularOnFocus | 获得焦点时显示热门推荐 | boolean | true | 否 |
| autoUpdateUseCount | 自动更新使用频次 | boolean | true | 否 |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| update:value | 值变化时触发 | (value: string) |
| change | 值变化时触发 | (value: string) |
| select | 选择选项时触发 | (value: string, option: OptionItem) |
| create | 创建新项目时触发 | (newItem: any) |
| focus | 获得焦点时触发 | () |
| blur | 失去焦点时触发 | () |

### Methods

| 方法名 | 说明 | 参数 |
|--------|------|------|
| loadPopularOptions | 加载热门选项 | () |
| clearOptions | 清空选项 | () |
| refresh | 刷新选项 | () |

## 数据结构

### OptionItem

```typescript
interface OptionItem {
  value: string;        // 选项值
  label: string;        // 显示文本
  helpChar?: string;    // 助记码
  useCount?: number;    // 使用次数
  isNew?: boolean;      // 是否为新建项
  matchType?: string;   // 匹配类型
  raw?: AutoCompleteResult; // 原始数据
}
```

### AutoCompleteResult

```typescript
interface AutoCompleteResult {
  id?: string;          // 项目ID
  category: string;     // 分类标识
  name: string;         // 名称
  helpChar?: string;    // 助记码
  pinyin?: string;      // 全拼
  useCount?: number;    // 使用次数
  sortOrder?: number;   // 排序号
  isNew?: boolean;      // 是否为新建项
  matchType?: string;   // 匹配类型
  matchScore?: number;  // 匹配得分
}
```

## 样式定制

组件提供了丰富的CSS类名，可以通过覆盖样式来定制外观：

```less
.auto-complete-wrapper {
  // 组件容器样式
  
  .option-item {
    // 选项容器样式
    
    .option-main {
      // 选项主要内容样式
      
      .option-text {
        // 选项文本样式
      }
      
      .help-char {
        // 助记码样式
      }
      
      .new-tag {
        // 新建标签样式
      }
    }
    
    .option-meta {
      // 选项元数据样式
      
      .use-count {
        // 使用次数样式
      }
    }
  }
}
```

## 使用场景

### 1. 工种选择

```vue
<AutoComplete
  v-model:value="worktype"
  category="worktype"
  placeholder="请输入工种名称"
/>
```

### 2. 部门选择

```vue
<AutoComplete
  v-model:value="department"
  category="department"
  placeholder="请输入部门名称"
/>
```

### 3. 职位选择

```vue
<AutoComplete
  v-model:value="position"
  category="position"
  placeholder="请输入职位名称"
/>
```

### 4. 公司选择

```vue
<AutoComplete
  v-model:value="company"
  category="company"
  placeholder="请输入公司名称"
/>
```

## 注意事项

1. **分类标识**：category 参数是必填的，用于区分不同类型的数据
2. **缓存机制**：组件会自动使用后端缓存，提升查询性能
3. **自动创建**：当 allowAutoCreate 为 true 时，输入不存在的内容会显示"新建"选项
4. **使用频次**：选择项目后会自动更新使用频次，用于热门推荐排序
5. **搜索类型**：支持按名称、助记码或两者进行搜索
6. **响应式**：组件支持响应式布局，会根据容器大小自动调整

## 最佳实践

1. **合理设置 limit**：根据实际需求设置合适的显示数量，避免选项过多影响用户体验
2. **使用助记码**：为常用项目设置助记码，提升输入效率
3. **定期清理缓存**：在数据更新频繁的场景下，可以定期清理缓存确保数据实时性
4. **错误处理**：监听相关事件，做好错误处理和用户提示
5. **性能优化**：在大量数据场景下，可以适当调整防抖时间和缓存策略
