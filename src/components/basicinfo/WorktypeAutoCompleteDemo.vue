<template>
  <div class="worktype-demo">
    <a-card title="工种自动补全组件示例" class="demo-card">
      
      <!-- 基本使用 -->
      <a-divider orientation="left">基本使用</a-divider>
      <div class="demo-section">
        <a-row :gutter="16">
          <a-col :span="12">
            <div class="demo-item">
              <h4>简单使用</h4>
              <WorktypeAutoComplete
                v-model:value="basicValue"
                placeholder="请输入工种名称"
                @select="handleBasicSelect"
              />
              <p class="result-text">选择的工种：{{ basicValue }}</p>
            </div>
          </a-col>
          
          <a-col :span="12">
            <div class="demo-item">
              <h4>支持助记码</h4>
              <WorktypeAutoComplete
                v-model:value="helpCharValue"
                placeholder="支持拼音首字母，如：DG(电工)"
                :showHelpChar="true"
                @select="handleHelpCharSelect"
              />
              <p class="result-text">选择的工种：{{ helpCharValue }}</p>
            </div>
          </a-col>
        </a-row>
      </div>

      <!-- 表单集成 -->
      <a-divider orientation="left">表单集成</a-divider>
      <div class="demo-section">
        <a-form :model="formData" :rules="formRules" @finish="handleFormSubmit" layout="vertical">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="员工姓名" name="name">
                <a-input v-model:value="formData.name" placeholder="请输入员工姓名" />
              </a-form-item>
            </a-col>
            
            <a-col :span="8">
              <a-form-item label="工种" name="worktype">
                <WorktypeAutoComplete
                  v-model:value="formData.worktype"
                  placeholder="请选择工种"
                  :validateWorktype="true"
                  @select="handleFormWorktypeSelect"
                />
              </a-form-item>
            </a-col>
            
            <a-col :span="8">
              <a-form-item label="部门" name="department">
                <a-input v-model:value="formData.department" placeholder="请输入部门" />
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-form-item>
            <a-button type="primary" html-type="submit">提交</a-button>
            <a-button style="margin-left: 8px" @click="resetForm">重置</a-button>
          </a-form-item>
        </a-form>
      </div>

      <!-- 高级功能 -->
      <a-divider orientation="left">高级功能</a-divider>
      <div class="demo-section">
        <a-row :gutter="16">
          <a-col :span="12">
            <div class="demo-item">
              <h4>自定义验证规则</h4>
              <WorktypeAutoComplete
                v-model:value="validationValue"
                placeholder="工种名称不能包含数字"
                :validateWorktype="true"
                :worktypeRules="customRules"
                @select="handleValidationSelect"
              />
              <p class="result-text">选择的工种：{{ validationValue }}</p>
            </div>
          </a-col>
          
          <a-col :span="12">
            <div class="demo-item">
              <h4>禁用自动创建</h4>
              <WorktypeAutoComplete
                v-model:value="noCreateValue"
                placeholder="只能选择已有工种"
                :allowAutoCreate="false"
                @select="handleNoCreateSelect"
              />
              <p class="result-text">选择的工种：{{ noCreateValue }}</p>
            </div>
          </a-col>
        </a-row>
      </div>

      <!-- 操作按钮 -->
      <a-divider orientation="left">组件操作</a-divider>
      <div class="demo-section">
        <a-space>
          <a-button @click="loadPopular">加载热门工种</a-button>
          <a-button @click="clearAll">清空所有</a-button>
          <a-button @click="refreshCache">刷新缓存</a-button>
          <a-button @click="showStats">查看统计</a-button>
        </a-space>
        
        <div v-if="statsData" class="stats-display">
          <h4>工种统计信息：</h4>
          <pre>{{ JSON.stringify(statsData, null, 2) }}</pre>
        </div>
      </div>

      <!-- 批量操作示例 -->
      <a-divider orientation="left">批量操作</a-divider>
      <div class="demo-section">
        <div class="batch-demo">
          <h4>批量选择工种</h4>
          <a-row :gutter="8">
            <a-col :span="6" v-for="(item, index) in batchItems" :key="index">
              <WorktypeAutoComplete
                v-model:value="item.worktype"
                :placeholder="`工种${index + 1}`"
                size="small"
                @select="(value, option) => handleBatchSelect(index, value, option)"
              />
            </a-col>
          </a-row>
          
          <div class="batch-result">
            <h5>选择结果：</h5>
            <a-tag v-for="(item, index) in batchItems" :key="index" v-show="item.worktype">
              {{ item.worktype }}
            </a-tag>
          </div>
          
          <a-space style="margin-top: 16px">
            <a-button @click="addBatchItem">添加</a-button>
            <a-button @click="clearBatch">清空</a-button>
            <a-button type="primary" @click="submitBatch">提交批量数据</a-button>
          </a-space>
        </div>
      </div>

    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';
import WorktypeAutoComplete from './WorktypeAutoComplete.vue';
import { getWorktypeStats, refreshWorktypeCache } from './WorktypeAutoComplete.api';

// 基本使用
const basicValue = ref('');
const helpCharValue = ref('');

// 表单数据
const formData = reactive({
  name: '',
  worktype: '',
  department: ''
});

const formRules = {
  name: [{ required: true, message: '请输入员工姓名', trigger: 'blur' }],
  worktype: [{ required: true, message: '请选择工种', trigger: 'blur' }],
  department: [{ required: true, message: '请输入部门', trigger: 'blur' }]
};

// 高级功能
const validationValue = ref('');
const noCreateValue = ref('');

// 自定义验证规则
const customRules = [
  (value: string) => {
    if (/\d/.test(value)) {
      return '工种名称不能包含数字';
    }
    return true;
  },
  (value: string) => {
    if (value.length > 20) {
      return '工种名称不能超过20个字符';
    }
    return true;
  }
];

// 统计数据
const statsData = ref(null);

// 批量操作
const batchItems = ref([
  { worktype: '' },
  { worktype: '' },
  { worktype: '' },
  { worktype: '' }
]);

// 事件处理
const handleBasicSelect = (value: string, option: any) => {
  console.log('基本选择:', value, option);
  message.success(`选择了工种: ${value}`);
};

const handleHelpCharSelect = (value: string, option: any) => {
  console.log('助记码选择:', value, option);
  if (option.matchType === 'helpChar') {
    message.info(`通过助记码"${option.helpChar}"找到工种: ${value}`);
  }
};

const handleFormWorktypeSelect = (value: string, option: any) => {
  console.log('表单工种选择:', value, option);
  if (option.isNew) {
    message.success(`新工种"${value}"已创建并选择`);
  }
};

const handleValidationSelect = (value: string, option: any) => {
  console.log('验证选择:', value, option);
};

const handleNoCreateSelect = (value: string, option: any) => {
  console.log('无创建选择:', value, option);
};

const handleBatchSelect = (index: number, value: string, option: any) => {
  console.log(`批量选择 ${index}:`, value, option);
};

// 表单提交
const handleFormSubmit = (values: any) => {
  console.log('表单提交:', values);
  message.success('表单提交成功！');
};

const resetForm = () => {
  Object.keys(formData).forEach(key => {
    formData[key] = '';
  });
  message.info('表单已重置');
};

// 操作方法
const loadPopular = async () => {
  message.info('加载热门工种...');
  // 这里可以调用组件的方法
};

const clearAll = () => {
  basicValue.value = '';
  helpCharValue.value = '';
  validationValue.value = '';
  noCreateValue.value = '';
  message.info('已清空所有输入');
};

const refreshCache = async () => {
  try {
    await refreshWorktypeCache();
    message.success('缓存刷新成功');
  } catch (error) {
    message.error('缓存刷新失败');
  }
};

const showStats = async () => {
  try {
    const response = await getWorktypeStats();
    if (response.success) {
      statsData.value = response.result;
      message.success('统计信息加载成功');
    }
  } catch (error) {
    message.error('获取统计信息失败');
  }
};

const addBatchItem = () => {
  batchItems.value.push({ worktype: '' });
};

const clearBatch = () => {
  batchItems.value.forEach(item => {
    item.worktype = '';
  });
  message.info('批量数据已清空');
};

const submitBatch = () => {
  const selectedWorktypes = batchItems.value
    .filter(item => item.worktype)
    .map(item => item.worktype);
  
  if (selectedWorktypes.length === 0) {
    message.warning('请至少选择一个工种');
    return;
  }
  
  console.log('批量提交:', selectedWorktypes);
  message.success(`批量提交成功，共${selectedWorktypes.length}个工种`);
};
</script>

<style lang="less" scoped>
.worktype-demo {
  padding: 24px;
  
  .demo-card {
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .demo-section {
    margin-bottom: 32px;
  }
  
  .demo-item {
    margin-bottom: 16px;
    
    h4 {
      margin-bottom: 8px;
      color: #1890ff;
    }
    
    .result-text {
      margin-top: 8px;
      padding: 8px;
      background-color: #f6ffed;
      border: 1px solid #b7eb8f;
      border-radius: 4px;
      color: #52c41a;
      font-size: 14px;
    }
  }
  
  .stats-display {
    margin-top: 16px;
    padding: 16px;
    background-color: #fafafa;
    border-radius: 4px;
    
    pre {
      margin: 0;
      font-size: 12px;
    }
  }
  
  .batch-demo {
    .batch-result {
      margin-top: 16px;
      
      h5 {
        margin-bottom: 8px;
      }
    }
  }
}
</style>
