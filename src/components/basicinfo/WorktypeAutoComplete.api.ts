import { defHttp } from '/@/utils/http/axios';

enum Api {
  search = '/basicinfo/worktypeAutoComplete/search',
  popular = '/basicinfo/worktypeAutoComplete/popular',
  autoCreate = '/basicinfo/worktypeAutoComplete/autoCreate',
  updateUseCount = '/basicinfo/worktypeAutoComplete/updateUseCount',
  batchUpdateUseCount = '/basicinfo/worktypeAutoComplete/batchUpdateUseCount',
  stats = '/basicinfo/worktypeAutoComplete/stats',
  refreshCache = '/basicinfo/worktypeAutoComplete/refreshCache',
}

/**
 * 工种自动补全搜索参数
 */
export interface WorktypeSearchParams {
  keyword?: string;
  searchType?: 'name' | 'helpChar' | 'both';
  limit?: number;
}

/**
 * 工种自动补全结果
 */
export interface WorktypeResult {
  id?: string;
  category: string;
  name: string;
  helpChar?: string;
  pinyin?: string;
  useCount?: number;
  sortOrder?: number;
  isNew?: boolean;
  matchType?: string;
  matchScore?: number;
}

/**
 * 工种创建参数
 */
export interface WorktypeCreateParams {
  name: string;
  helpChar?: string;
  remark?: string;
}

/**
 * 工种自动补全搜索
 * @param params 搜索参数
 */
export const worktypeSearch = (params: WorktypeSearchParams) => {
  return defHttp.get<WorktypeResult[]>({ url: Api.search, params }, { isTransformResponse: false });
};

/**
 * 获取热门工种
 * @param limit 限制数量
 */
export const getPopularWorktypes = (limit: number = 10) => {
  return defHttp.get<WorktypeResult[]>({ 
    url: Api.popular, 
    params: { limit } 
  }, { isTransformResponse: false });
};

/**
 * 自动创建工种
 * @param params 创建参数
 */
export const autoCreateWorktype = (params: WorktypeCreateParams) => {
  return defHttp.post<any>({ url: Api.autoCreate, params }, { isTransformResponse: false });
};

/**
 * 更新工种使用频次
 * @param id 工种ID
 */
export const updateWorktypeUseCount = (id: string) => {
  return defHttp.post<string>({ 
    url: Api.updateUseCount, 
    params: { id } 
  }, { isTransformResponse: false });
};

/**
 * 批量更新工种使用频次
 * @param ids 工种ID列表
 */
export const batchUpdateWorktypeUseCount = (ids: string[]) => {
  return defHttp.post<string>({ url: Api.batchUpdateUseCount, data: ids }, { isTransformResponse: false });
};

/**
 * 获取工种统计信息
 */
export const getWorktypeStats = () => {
  return defHttp.get<any>({ url: Api.stats }, { isTransformResponse: false });
};

/**
 * 刷新工种缓存
 */
export const refreshWorktypeCache = () => {
  return defHttp.post<string>({ url: Api.refreshCache }, { isTransformResponse: false });
};
