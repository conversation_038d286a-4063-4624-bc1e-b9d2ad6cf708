<template>
  <div class="ant-form-item-control-input">
    <div class="ant-form-item-control-input-content">
      <input
        v-cleave="{ date: true, delimiter: '-', datePattern: ['Y', 'm', 'd'] }"
        v-model="inputValue"
        class="ant-input css-dev-only-do-not-override-udyjmm"
        :placeholder="placeholder"
        :disabled="disabled"
        @change="handleChange"
        @blur="handleBlur"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch, inject } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';

  interface Props {
    value?: string;
    placeholder?: string;
    disabled?: boolean;
    required?: boolean;
    validateOnChange?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    placeholder: '年-月-日',
    disabled: false,
    required: false,
    validateOnChange: true,
  });

  const emit = defineEmits(['update:value', 'change']);

  const inputValue = ref(props.value || '');
  const { createMessage } = useMessage();
  const dayjs = inject('$dayjs');

  // 监听外部value变化
  watch(
    () => props.value,
    (newValue) => {
      inputValue.value = newValue || '';
    }
  );

  // 监听内部输入值变化
  watch(inputValue, (newValue) => {
    emit('update:value', newValue);
  });

  /**
   * 验证日期格式和有效性
   */
  function validateDate(dateStr: string): { isValid: boolean; message?: string } {
    if (!dateStr) {
      if (props.required) {
        return { isValid: false, message: '请输入日期' };
      }
      return { isValid: true };
    }

    // 检查日期格式是否完整 (YYYY-MM-DD)
    const datePattern = /^\d{4}-\d{2}-\d{2}$/;
    if (!datePattern.test(dateStr)) {
      return { isValid: false, message: '日期格式不正确，请输入完整的年-月-日' };
    }

    // 使用dayjs验证日期有效性
    if (!dayjs || !dayjs(dateStr).isValid()) {
      return { isValid: false, message: '请输入有效的日期' };
    }

    // 检查日期是否超出合理范围
    const inputDate = dayjs(dateStr);
    const currentDate = dayjs();
    const minDate = dayjs('1900-01-01');

    if (inputDate.isBefore(minDate)) {
      return { isValid: false, message: '日期不能早于1900年' };
    }

    if (inputDate.isAfter(currentDate)) {
      return { isValid: false, message: '日期不能晚于当前日期' };
    }

    return { isValid: true };
  }

  /**
   * 处理输入变化
   */
  function handleChange() {
    if (props.validateOnChange) {
      validateInput();
    }
    emit('change', inputValue.value);
  }

  /**
   * 处理失焦事件
   */
  function handleBlur() {
    validateInput();
  }

  /**
   * 验证输入
   */
  function validateInput() {
    const validation = validateDate(inputValue.value);
    if (!validation.isValid && validation.message) {
      createMessage.error(validation.message);
    }
  }

  /**
   * 外部调用的验证方法
   */
  function validate(): Promise<boolean> {
    return new Promise((resolve) => {
      const validation = validateDate(inputValue.value);
      if (!validation.isValid && validation.message) {
        createMessage.error(validation.message);
        resolve(false);
      } else {
        resolve(true);
      }
    });
  }

  // 暴露验证方法给父组件
  defineExpose({
    validate,
    validateDate,
  });
</script>

<style scoped>
  .ant-input {
    width: 100%;
  }
</style>
