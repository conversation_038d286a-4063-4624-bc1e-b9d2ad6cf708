<template>
  <Teleport to="body">
    <div v-if="visible" :class="['follow-mouse-popover', 'ant-popover', `ant-popover-placement-${placement}`]" :style="popoverStyle" ref="popoverRef">
      <div class="ant-popover-content">
        <!-- Arrow (optional, might need fine-tuning or removal depending on look) -->
        <!-- <div class="ant-popover-arrow"></div> -->
        <div class="ant-popover-inner" role="tooltip">
          <div v-if="title || $slots.title" class="ant-popover-title">
            <slot name="title">{{ title }}</slot>
          </div>
          <div class="ant-popover-inner-content">
            <slot name="content"></slot>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup>
  import { ref, reactive, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';

  // --- Props ---
  const props = defineProps({
    // Control visibility externally using v-model:visible
    visible: {
      type: Boolean,
      default: false,
    },
    // Optional title
    title: {
      type: String,
      default: '',
    },
    // Offset from the mouse cursor
    offsetX: {
      type: Number,
      default: 15, // Default offset to the right
    },
    offsetY: {
      type: Number,
      default: 15, // Default offset downwards
    },
    // Base placement class for styling (mainly affects arrow if shown)
    placement: {
      type: String,
      default: 'top', // Doesn't strongly affect position, more for style consistency
    },
    // Optional: Boundary element selector (e.g., '#app') to constrain within
    // If not provided, uses viewport
    boundarySelector: {
      type: String,
      default: null,
    },
  });

  // --- State ---
  const mousePosition = reactive({ x: 0, y: 0 });
  const popoverRef = ref(null); // Ref for the popover element itself
  const popoverSize = reactive({ width: 0, height: 0 });
  const boundaryRect = reactive({ top: 0, left: 0, right: window.innerWidth, bottom: window.innerHeight });

  // --- Mouse Tracking ---
  const handleMouseMove = (event) => {
    mousePosition.x = event.clientX;
    mousePosition.y = event.clientY;
  };

  // --- Update Popover Size ---
  // We need the size to prevent it from going off-screen
  const updatePopoverSize = async () => {
    if (props.visible && popoverRef.value) {
      // Ensure DOM is updated before measuring
      await nextTick();
      if (popoverRef.value) {
        popoverSize.width = popoverRef.value.offsetWidth;
        popoverSize.height = popoverRef.value.offsetHeight;
      }
    }
  };

  // --- Update Boundary Rect ---
  const updateBoundaryRect = () => {
    if (props.boundarySelector) {
      const boundaryEl = document.querySelector(props.boundarySelector);
      if (boundaryEl) {
        const rect = boundaryEl.getBoundingClientRect();
        boundaryRect.top = rect.top;
        boundaryRect.left = rect.left;
        boundaryRect.right = rect.right;
        boundaryRect.bottom = rect.bottom;
        return; // Use element boundary
      } else {
        console.warn(`Boundary element "${props.boundarySelector}" not found. Using viewport.`);
      }
    }
    // Default to viewport
    boundaryRect.top = 0;
    boundaryRect.left = 0;
    boundaryRect.right = window.innerWidth;
    boundaryRect.bottom = window.innerHeight;
  };

  // --- Computed Style ---
  const popoverStyle = computed(() => {
    let x = mousePosition.x + props.offsetX;
    let y = mousePosition.y + props.offsetY;

    // Boundary collision detection
    // Check right boundary
    if (x + popoverSize.width > boundaryRect.right) {
      x = mousePosition.x - props.offsetX - popoverSize.width; // Flip to left
      // Optional: Add a check if flipping left also goes out of bounds
      if (x < boundaryRect.left) {
        x = boundaryRect.right - popoverSize.width; // Stick to right edge
      }
    }
    // Check left boundary (if flipped or original offset makes it go left)
    if (x < boundaryRect.left) {
      x = boundaryRect.left; // Stick to left edge
      // Alternative: could flip back to right if needed
      // x = mousePosition.x + props.offsetX;
    }

    // Check bottom boundary
    if (y + popoverSize.height > boundaryRect.bottom) {
      y = mousePosition.y - props.offsetY - popoverSize.height; // Flip above
      // Optional: Check if flipping above also goes out of bounds
      if (y < boundaryRect.top) {
        y = boundaryRect.bottom - popoverSize.height; // Stick to bottom edge
      }
    }
    // Check top boundary (if flipped or original offset makes it go up)
    if (y < boundaryRect.top) {
      y = boundaryRect.top; // Stick to top edge
      // Alternative: could flip back below if needed
      // y = mousePosition.y + props.offsetY;
    }

    return {
      position: 'fixed', // Use fixed positioning relative to viewport
      left: `${x}px`,
      top: `${y}px`,
      // Ant Design popovers often have a default z-index
      zIndex: 1060, // Ensure it's above most content (Antd Popover z-index)
      // Prevent the popover itself from capturing mouse events if needed
      pointerEvents: 'none',
      // Ensure initial visibility state allows measurement
      // visibility: props.visible ? 'visible' : 'hidden', // Manage via v-if/v-show
    };
  });

  // --- Lifecycle Hooks ---
  onMounted(() => {
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('resize', updateBoundaryRect); // Update bounds on resize
    updateBoundaryRect(); // Initial boundary calculation
  });

  onUnmounted(() => {
    window.removeEventListener('mousemove', handleMouseMove);
    window.removeEventListener('resize', updateBoundaryRect);
  });

  // --- Watchers ---
  // Watch visibility to update popover size when it becomes visible
  watch(
    () => props.visible,
    (newVal) => {
      if (newVal) {
        // Update position immediately in case mouse hasn't moved
        handleMouseMove({ clientX: mousePosition.x, clientY: mousePosition.y });
        // Measure size after it renders
        updatePopoverSize();
        // Re-calculate boundaries in case they changed while hidden
        updateBoundaryRect();
      }
    },
    { immediate: false }
  ); // Don't run immediately on mount unless needed

  // Watch content changes (via slots) that might affect size
  // Note: Directly watching slots isn't straightforward.
  // If content size changes dynamically *while visible*, you might need
  // a ResizeObserver on the popoverRef or trigger updatePopoverSize manually.
  // For simplicity, we update size mainly when visibility changes.
  watch(popoverRef, (newEl) => {
    if (newEl && props.visible) {
      updatePopoverSize(); // Try measuring if ref changes while visible
    }
  });
</script>

<style>
  /* Optional: Add specific styles if needed, but rely mostly on Ant Design's global CSS */
  .follow-mouse-popover {
    /* Ensure it doesn't inherit weird positioning */
    transform: none !important;
    /* Set a default max-width if desired */
    /* max-width: 300px; */
  }

  /* Hide the default arrow if it looks out of place, as placement is dynamic */
  /* You might need to adjust this based on the specific placement class used */
  /* .follow-mouse-popover.ant-popover-placement-top .ant-popover-arrow {
  display: none;
} */
  /* Add similar rules for other placements if you remove the arrow */
</style>
