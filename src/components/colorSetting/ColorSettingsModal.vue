<template>
  <a-modal
    title="设置表格行颜色"
    v-model:open="visible"
    @ok="handleOk"
    @cancel="handleCancel"
    ok-text="确定"
    cancel-text="取消"
    :destroy-on-close="true"
  >
    <div style="padding: 10px">
      <a-form layout="vertical">
        <!-- 奇数行颜色设置 -->
        <a-form-item label="表格斑马纹颜色 (奇数行)">
          <a-select
            v-model:value="localColors.odd"
            mode="tags"
            style="width: 100%"
            placeholder="请选择或输入颜色"
            @change="() => onColorChange('odd')"
          >
            <a-select-option v-for="color in colorOptions" :key="color.value" :value="color.value">
              <span :style="colorStyle(color.value)"></span>
              {{ color.label }}
            </a-select-option>
          </a-select>
          <a-form-item v-if="errors.odd" :validate-status="'error'" :help="errors.odd" />
        </a-form-item>

        <!-- 偶数行颜色设置 -->
        <a-form-item label="表格斑马纹颜色 (偶数行)">
          <a-select
            v-model:value="localColors.even"
            mode="tags"
            style="width: 100%"
            placeholder="请选择或输入颜色"
            @change="() => onColorChange('even')"
          >
            <a-select-option v-for="color in colorOptions" :key="color.value" :value="color.value">
              <span :style="colorStyle(color.value)"></span>
              {{ color.label }}
            </a-select-option>
          </a-select>
          <a-form-item v-if="errors.even" :validate-status="'error'" :help="errors.even" />
        </a-form-item>

        <!-- 推荐字体颜色显示 -->
        <a-form-item label="推荐字体颜色 (奇数行)">
          <a-input v-model:value="recommendedFontColors.odd" :style="{ backgroundColor: localColors.odd, color: recommendedFontColors.odd }" />
        </a-form-item>
        <a-form-item label="推荐字体颜色 (偶数行)">
          <a-input v-model:value="recommendedFontColors.even" :style="{ backgroundColor: localColors.even, color: recommendedFontColors.even }" />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { defineEmits, defineProps, reactive, ref } from 'vue';
  import { message } from 'ant-design-vue';

  // 定义组件接收的 props
  const props = defineProps({
    initialColors: {
      type: Object,
      default: () => ({
        odd: '#f9f9f9',
        even: '#ffffff',
        fontOdd: '#000000',
        fontEven: '#000000',
      }),
    },
  });

  // 定义组件发出的事件
  const emit = defineEmits(['apply-colors']);
  const colorOptions = [
    { label: '浅灰色', value: '#FCFCFC', fontColor: '#000000' }, // 黑色
    { label: '灰色', value: '#CCCCCC', fontColor: '#000000' }, // 黑色
    { label: '深灰色', value: '#989898', fontColor: '#000000' }, // 黑色
    { label: '浅红色', value: '#FFA5B4', fontColor: '#000000' }, // 黑色
    { label: '红色', value: '#f5222d', fontColor: '#FFFFFF' }, // 白色
    { label: '深红', value: '#bb0606', fontColor: '#FFFFFF' }, // 白色
    { label: '浅橙色', value: '#FBCA72', fontColor: '#000000' }, // 黑色
    { label: '橙色', value: '#FF8A0C', fontColor: '#000000' }, // 黑色
    { label: '深橙色', value: '#FF6100', fontColor: '#FFFFFF' }, // 白色
    { label: '浅黄色', value: '#FDF161', fontColor: '#000000' }, // 黑色
    { label: '黄色', value: '#FAC450', fontColor: '#000000' }, // 黑色
    { label: '深黄色', value: '#D9B416', fontColor: '#000000' }, // 黑色
    { label: '浅绿色', value: '#D0EE9C', fontColor: '#000000' }, // 黑色
    { label: '绿色', value: '#60BE29', fontColor: '#FFFFFF' }, // 白色
    { label: '深绿色', value: '#39A30E', fontColor: '#FFFFFF' }, // 白色
    { label: '浅蓝色', value: '#95CCF5', fontColor: '#000000' }, // 黑色
    { label: '蓝色', value: '#00A0E8', fontColor: '#FFFFFF' }, // 白色
    { label: '深蓝色', value: '#1D80D3', fontColor: '#FFFFFF' }, // 白色
    { label: '浅紫色', value: '#B196EE', fontColor: '#000000' }, // 黑色
    { label: '紫色', value: '#5E30B5', fontColor: '#FFFFFF' }, // 白色
    { label: '深紫色', value: '#581CB6', fontColor: '#FFFFFF' }, // 白色
    { label: '浅玫瑰色', value: '#F5A6D3', fontColor: '#000000' }, // 黑色
    { label: '玫瑰色', value: '#E9259E', fontColor: '#FFFFFF' }, // 白色
    { label: '深玫瑰色', value: '#BC0F69', fontColor: '#FFFFFF' }, // 白色
    { label: '浅青色', value: '#B2EBF2', fontColor: '#000000' }, // 黑色
    { label: '青色', value: '#01BAD2', fontColor: '#FFFFFF' }, // 白色
    { label: '深青色', value: '#00ACC2', fontColor: '#FFFFFF' }, // 白色
    { label: '白色', value: '#ffffff', fontColor: '#000000' }, // 黑色
  ];

  // 定义本地响应式颜色对象
  const localColors = reactive({
    odd: props.initialColors.odd,
    even: props.initialColors.even,
    fontOdd: props.initialColors.fontOdd,
    fontEven: props.initialColors.fontEven,
  });

  // 定义错误对象
  const errors = reactive({
    odd: '',
    even: '',
  });

  // 定义推荐字体颜色的响应式对象
  const recommendedFontColors = reactive({
    odd: '#000000',
    even: '#000000',
  });

  // 打开模态框的方法
  const visible = ref<boolean>(false);
  function openModal() {
    // 初始化推荐字体颜色
    recommendedFontColors.odd = getBestFontColor(localColors.odd);
    recommendedFontColors.even = getBestFontColor(localColors.even);
    visible.value = true;
  }

  // 处理确定按钮点击
  function handleOk() {
    // 验证颜色
    const oddValid = validateColor('odd', false);
    const evenValid = validateColor('even', false);

    if (oddValid && evenValid) {
      // 计算推荐字体颜色
      const fontOdd = getBestFontColor(localColors.odd);
      const fontEven = getBestFontColor(localColors.even);

      emit('apply-colors', {
        odd: localColors.odd,
        even: localColors.even,
        fontOdd: fontOdd,
        fontEven: fontEven,
      });
      visible.value = false;
    } else {
      message.error('请输入有效的十六进制颜色值（例如：#444444）');
    }
  }

  // 处理取消按钮点击
  function handleCancel() {
    visible.value = false;
  }

  // 定义颜色样式函数
  function colorStyle(color: string) {
    return {
      backgroundColor: color,
      display: 'inline-block',
      width: '14px',
      height: '14px',
      marginRight: '8px',
      border: '1px solid #d9d9d9',
      borderRadius: '2px',
    };
  }

  // 颜色验证函数
  function validateColor(field: 'odd' | 'even', showError: boolean = true): boolean {
    const color = localColors[field];
    const hexRegex = /^#([0-9A-F]{3}){1,2}$/i;
    const isValid = hexRegex.test(color);

    if (!isValid && showError) {
      errors[field] = '请输入有效的十六进制颜色值（例如：#444444）';
    } else {
      errors[field] = '';
    }

    return isValid;
  }

  // 监听颜色变化并验证
  function onColorChange(field: 'odd' | 'even') {
    validateColor(field);
    // 更新推荐字体颜色
    recommendedFontColors[field] = getBestFontColor(localColors[field]);
  }

  // 计算最佳字体颜色（黑色或白色）
  function getBestFontColor(backgroundColor: string): string {
    //找到对应的颜色对象
    const color = colorOptions.find((c) => c.value == backgroundColor);
    if (color) {
      return color.fontColor;
    }
  }

  defineExpose({
    openModal,
  });
</script>

<style scoped>
  /* 根据需要自定义样式 */

  a-select .ant-select-selector {
    display: flex;
    align-items: center;
  }

  a-select-option span {
    margin-right: 8px;
  }
</style>
