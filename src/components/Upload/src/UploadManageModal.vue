<template>
  <a-modal width="60%" v-bind="$attrs" :open="openStatus" @cancel="handleCancle" @ok="handleOk" ok-text="完成">
    <div style="padding: 10px; min-height: 40vh; max-height: 80vh; overflow-y: scroll">
      <a-upload
        v-model:file-list="fileList"
        :action="uploadUrl"
        :headers="headers"
        name="file"
        list-type="picture"
        @preview="handlePreview"
        @change="onFileChange"
      >
        <a-button>
          <upload-outlined />
          上传
        </a-button>
        <!--        <div v-if="fileList.length < 8">
          <plus-outlined />
          <div style="margin-top: 8px">上传</div>
        </div>-->
      </a-upload>
    </div>
  </a-modal>
</template>
<script lang="ts" setup>
  import { computed, ref, watchEffect } from 'vue';
  import type { UploadProps } from 'ant-design-vue';
  import { getFileAccessHttpUrl, getHeaders } from '/@/utils/common/compUtils';
  import { uploadUrl } from '/@/api/common/api';
  import { UploadOutlined } from '@ant-design/icons-vue';
  import { createImgPreview } from '@/components/Preview';
  import { useMessage } from '@/hooks/web/useMessage';
  import { buildUUID } from '/@/utils/uuid';

  const props = defineProps<{
    previewFileList: {
      type: String[];
      default: () => [];
    };
  }>();

  const emit = defineEmits(['close', 'ok']);
  const openStatus = ref(false);
  const headers = computed(() => {
    return getHeaders();
  });
  const { createMessage, createConfirm } = useMessage();
  function getBase64(file: File) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = (error) => reject(error);
    });
  }

  function open() {
    openStatus.value = true;
  }
  const fileList = ref<UploadProps['fileList']>([]);

  function onFileChange(info) {
    //console.log('=========================', info);
    let fileListTemp = info.fileList;
    if (info.file.status === 'done') {
      let successFileList = [];
      if (info.file.response.success) {
        successFileList = fileListTemp.map((file) => {
          if (file.response) {
            file.url = file.response.message;
            file.name = file.response.message;
          }
          return file;
        });
      } else {
        successFileList = fileListTemp.filter((item) => {
          return item.url != info.file.url;
        });
        createMessage.error(`${info.file.name} 上传失败.`);
      }
      fileListTemp = successFileList;
    } else if (info.file.status === 'error') {
      createMessage.error(`${info.file.name} 上传失败.`);
    }
    fileList.value = fileListTemp;
  }

  function openImg(index) {
    const onImgLoad = ({ index, url, dom }) => {
      console.log(`第${index + 1}张图片已加载，URL为：${url}`, dom);
    };
    // 可以使用createImgPreview返回的 PreviewActions 来控制预览逻辑，实现类似幻灯片、自动旋转之类的骚操作
    let imageList = fileList.value.map<string>((i) => i.url);
    createImgPreview({ imageList: imageList, defaultWidth: 700, rememberState: true, onImgLoad, index });
  }

  const handlePreview = async (file: UploadProps['fileList'][number]) => {
    if (!file.url && !file.preview) {
      file.preview = (await getBase64(file.originFileObj)) as string;
    }
    openImg(fileList.value.indexOf(file));
  };
  function handleOk() {
    if (fileList.value?.some((item) => item.status === 'uploading')) {
      createConfirm({
        title: '提示',
        content: '文件正在上传中，确定要关闭吗？',
        iconType: 'warning',
        onOk: () => {
          emit('ok', getFileName());
          openStatus.value = false;
        },
      });
    } else {
      emit('ok', getFileName());
      openStatus.value = false;
    }
  }

  function handleCancle() {
    emit('close');
    openStatus.value = false;
  }

  function getFileName(): String[] {
    let fileUrls = fileList.value
      .map<String>((file) => {
        if (file.name) {
          return file.name;
        }
      })
      .filter(Boolean); // This will remove undefined values

    return fileUrls;
  }
  watchEffect(() => {
    if (props.previewFileList) {
      fileList.value = props.previewFileList.map((url) => {
        return {
          uid: buildUUID(),
          name: url,
          status: 'done',
          url: getFileAccessHttpUrl(url),
        };
      });
    }
  });
  defineExpose({
    open,
  });
</script>
<style scoped>
  /* you can make up upload button and sample style by using stylesheets */
  .ant-upload-select-picture-card i {
    font-size: 32px;
    color: #999;
  }

  .ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;
    color: #666;
  }
</style>
