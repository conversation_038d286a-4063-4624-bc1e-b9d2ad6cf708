<template>
  <a-modal v-model:visible="openFlag" :title="title" destroy-on-close width="70%">
    <template #footer>
      <a-button @click="openFlag = false">关闭</a-button>
    </template>
    <div style="height: 70vh; padding: 10px; display: flex; justify-content: center">
      <iframe :src="srcWithToken" style="width: 100%; height: 100%; border: none" v-if="openFlag"></iframe>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { getToken } from '@/utils/auth';

  const props = defineProps<{
    title: string;
    width: string;
    reportId: string;
  }>();
  const openFlag = ref(false);
  const reportParams = ref({});

  const srcWithToken = computed(() => {
    let src = `${window._CONFIG['domianURL']}/jmreport/view/${props.reportId}?token=${getToken()}`;
    if (reportParams.value) {
      // Convert reportParams to URL parameters
      const params = new URLSearchParams(reportParams.value).toString();
      src += `&${params}`;
      console.log("历史数据积木报表打开路径",src)
    }
    return src;
  });

  function open(reportParamsObj: Recordable) {
    reportParams.value = reportParamsObj;
    openFlag.value = true;
  }

  defineExpose({
    open,
  });
</script>
