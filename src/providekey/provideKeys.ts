import { InjectionKey } from 'vue';

// 定义类型
interface ProvideType {
  value: string | object;
  extra?: object;
  setValue: (val: string) => void;
  setExtra?: (val: object) => void;
}

// 创建并导出 key
export const companyRegIdKey: InjectionKey<ProvideType> = Symbol() as InjectionKey<ProvideType>;

export const companyRegKey: InjectionKey<ProvideType> = Symbol() as InjectionKey<ProvideType>;

export const companyTeamIdKey: InjectionKey<ProvideType> = Symbol() as InjectionKey<ProvideType>;

export const companyTeamKey: InjectionKey<ProvideType> = Symbol() as InjectionKey<ProvideType>;

export const idCardKey: InjectionKey<ProvideType> = Symbol() as InjectionKey<ProvideType>;

export const selectedCustomerRegKey: InjectionKey<ProvideType> = Symbol() as InjectionKey<ProvideType>;

export const selectedCustomerStationKey: InjectionKey<ProvideType> = Symbol() as InjectionKey<ProvideType>;

export const selectedCustomerSummaryKey: InjectionKey<ProvideType> = Symbol() as InjectionKey<ProvideType>;
