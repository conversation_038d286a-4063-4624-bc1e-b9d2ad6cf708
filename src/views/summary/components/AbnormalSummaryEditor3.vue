<template>
  <a-spin :spinning="loading">
    <div class="abnormal-editor">
      <div class="editor-header">
        <div class="header-line-number">序号</div>
        <div class="header-content">内容</div>
        <div class="header-actions">
          <a-button v-if="!readOnly" type="primary" size="small" @click="handleGetAbnormalSummary"> 获取汇总</a-button>
          <span v-else class="header-action-item">操作</span>
        </div>
      </div>
      <div class="editor-body">
        <!-- 移除拖拽功能，使用普通列表 -->
        <div v-if="!readOnly">
          <div v-for="(element, index) in abnormalList" :key="element.id"
               class="abnormal-item"
               :class="{
                 'has-annotation': hasAnnotation(index),
                 'has-review': hasReviewInfo(index)
               }"
          >
              <div class="line-number">
                <!-- 移除拖拽手柄图标 -->

                <!-- 序号气泡 -->
                <a-popover
                  v-model:open="popoverVisibleMap[element.id]"
                  trigger="hover"
                  placement="rightTop"
                  :overlayStyle="{ maxWidth: '500px' }"
                  @open-change="(visible) => handlePopoverChange(element.id, visible)"
                >
                  <template #content>
                    <div class="item-summary-list">
                      <div class="item-summary-header">
                        <span class="header-title">异常项目列表</span>
                        <span class="item-count">({{ getItemSummaryListCached(element.content).length }}项)</span>
                      </div>
                      <div class="item-summary-content">
                        <div
                          v-for="(item, itemIndex) in getItemSummaryListCached(element.content)"
                          :key="itemIndex"
                          class="item-summary-item"
                          @click.stop="handleItemClick(item, element.content)"
                          @mousedown.stop
                          @mouseup.stop
                        >
                          <span class="item-text">{{ item }}</span>
                          <search-outlined class="search-icon" />
                        </div>
                        <div v-if="getItemSummaryListCached(element.content).length === 0" class="empty-items"> 暂无异常项目 </div>
                      </div>
                    </div>
                  </template>
                  <span class="number-badge clickable" :class="{ 'has-items': hasItemSummary(element.content) }">
                    {{ index + 1 }}
                  </span>
                </a-popover>

                <a-badge
                  v-if="hasAnnotation(index)"
                  :count="getAnnotationCount(index)"
                  :number-style="{ backgroundColor: getAnnotationColor(index) }"
                />
                <!-- 审阅状态指示器 -->
                <div v-if="hasReviewInfo(index)" class="review-status-indicator">
                  <a-tooltip :title="getReviewStatusText(element.content.reviewInfo?.reviewStatus)">
                    <span class="review-status-icon" :class="`status-${element.content.reviewInfo?.reviewStatus}`">
                      <CheckCircleOutlined v-if="element.content.reviewInfo?.reviewStatus === 'corrected'" />
                      <ExclamationCircleOutlined v-else-if="element.content.reviewInfo?.reviewStatus === 'reviewed'" />
                      <ClockCircleOutlined v-else />
                    </span>
                  </a-tooltip>
                </div>
              </div>
              <div class="abnormal-content" :data-index="index">
                <AbnormalSummaryInput
                  v-model="element.content.text"
                  @change="(val) => handleContentChange(index, val)"
                  @select="(advice) => handleOptionSelect(index, advice)"
                  :ref="
                    (el) => {
                      if (el) inputRefs[index] = el;
                    }
                  "
                  :readonly="readOnly"
                />

                <!-- 显示护士标注 -->
                <div v-if="showAnnotations && getItemAnnotations(index).length > 0" class="nurse-annotations">
                  <div class="annotations-header">
                    <ExclamationCircleOutlined style="color: #faad14; margin-right: 4px" />
                    <span class="annotations-title">护士标注</span>
                  </div>
                  <div
                    v-for="annotation in getItemAnnotations(index)"
                    :key="annotation.id"
                    class="annotation-item"
                    :class="`annotation-${annotation.severity}`"
                  >
                    <div class="annotation-header">
                      <a-tag :color="getAnnotationTagColor(annotation.type)" size="small">
                        {{ getAnnotationTypeText(annotation.type) }}
                      </a-tag>
                      <a-tag :color="getSeverityColor(annotation.severity)" size="small">
                        {{ getSeverityText(annotation.severity) }}
                      </a-tag>
                      <span class="annotation-time">{{ formatTime(annotation.createTime) }}</span>
                    </div>
                    <div class="annotation-content">{{ annotation.content }}</div>
                    <div v-if="annotation.selectedText" class="annotation-selected-text">
                      <span class="selected-label">选中文本：</span>
                      <span class="selected-text">{{ annotation.selectedText }}</span>
                    </div>
                    <!-- 处理状态和操作 -->
                    <div class="annotation-actions" v-if="!readOnly && annotation.status === 'pending'">
                      <a-button size="small" type="link" @click="handleAnnotation(annotation, 'handled')"> 标记已处理 </a-button>
                      <a-button size="small" type="link" @click="showHandleModal(annotation)"> 添加处理意见</a-button>
                    </div>
                    <div v-if="annotation.status === 'handled'" class="annotation-handled">
                      <a-tag color="green" size="small">已处理</a-tag>
                      <span v-if="annotation.handleComment" class="handle-comment">{{ annotation.handleComment }}</span>
                      <span class="handle-time">{{ formatTime(annotation.handleTime) }}</span>
                    </div>
                  </div>
                </div>

                <!-- 显示审阅信息 -->
                <div v-if="hasReviewInfo(index)" class="review-info">
                  <div class="review-header">
                    <AuditOutlined style="color: #1890ff; margin-right: 4px" />
                    <span class="review-title">审阅信息</span>
                    <a-tag :color="getReviewStatusColor(element.content.reviewInfo?.reviewStatus)" size="small">
                      {{ getReviewStatusText(element.content.reviewInfo?.reviewStatus) }}
                    </a-tag>
                  </div>

                  <div class="review-details">
                    <div class="review-meta">
                      <span class="reviewer">审阅人：{{ element.content.reviewInfo?.reviewer }}</span>
                      <span class="review-time">时间：{{ formatTime(element.content.reviewInfo?.reviewTime) }}</span>
                    </div>

                    <div v-if="element.content.reviewInfo?.reviewComment" class="review-comment">
                      <div class="comment-label">审阅意见：</div>
                      <div class="comment-content">{{ element.content.reviewInfo.reviewComment }}</div>
                    </div>

                    <div v-if="element.content.reviewInfo?.correctedContent" class="corrected-content">
                      <div class="corrected-label">更正内容：</div>
                      <div class="corrected-text">{{ element.content.reviewInfo.correctedContent }}</div>
                      <a-button size="small" type="link" @click="applyCorrectedContent(index)"> 应用更正</a-button>
                    </div>

                    <div v-if="element.content.reviewInfo?.annotations?.length > 0" class="review-annotations">
                      <div class="annotations-count"> 标注数量：{{ element.content.reviewInfo.annotations.length }}</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="abnormal-actions" v-if="!readOnly">
                <a-dropdown :trigger="['click']" v-model:visible="dropdownVisibleMap[element.id]">
                  <span class="action-icon more-actions">
                    <EllipsisOutlined />
                  </span>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="moveItemUp(index)" :disabled="index === 0" class="move-up-menu-item">
                        <UpOutlined />
                        上移
                      </a-menu-item>
                      <a-menu-item @click="moveItemDown(index)" :disabled="index === abnormalList.length - 1" class="move-down-menu-item">
                        <DownOutlined />
                        下移
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item @click="insertLine(index)">
                        <PlusOutlined />
                        插入
                      </a-menu-item>
                      <a-menu-item @click="removeLine(index)" class="delete-menu-item">
                        <MinusOutlined />
                        删除
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </div>
            </div>
          </template>
        </draggable>

        <!-- 只读模式下的静态列表 -->
        <div v-else>
          <div
            v-for="(element, index) in abnormalList"
            :key="element.id"
            class="abnormal-item"
            :class="{ 'has-annotation': hasAnnotation(index), 'has-review': hasReviewInfo(index) }"
          >
            <div class="line-number">
              <!-- 序号气泡 -->
              <a-popover
                v-model:open="popoverVisibleMap[element.id]"
                trigger="hover"
                placement="rightTop"
                :overlayStyle="{ maxWidth: '500px' }"
                @open-change="(visible) => handlePopoverChange(element.id, visible)"
              >
                <template #content>
                  <div class="item-summary-list">
                    <div class="item-summary-header">
                      <span class="header-title">异常项目列表</span>
                      <span class="item-count">({{ getItemSummaryListCached(element.content).length }}项)</span>
                    </div>
                    <div class="item-summary-content">
                      <div
                        v-for="(item, itemIndex) in getItemSummaryListCached(element.content)"
                        :key="itemIndex"
                        class="item-summary-item"
                        @click.stop="handleItemClick(item, element.content)"
                        @mousedown.stop
                        @mouseup.stop
                      >
                        <span class="item-text">{{ item }}</span>
                        <search-outlined class="search-icon" />
                      </div>
                      <div v-if="getItemSummaryListCached(element.content).length === 0" class="empty-items"> 暂无异常项目 </div>
                    </div>
                  </div>
                </template>
                <span class="number-badge clickable" :class="{ 'has-items': hasItemSummary(element.content) }">
                  {{ index + 1 }}
                </span>
              </a-popover>

              <a-badge
                v-if="hasAnnotation(index)"
                :count="getAnnotationCount(index)"
                :number-style="{ backgroundColor: getAnnotationColor(index) }"
              />
              <!-- 审阅状态指示器 -->
              <div v-if="hasReviewInfo(index)" class="review-status-indicator">
                <a-tooltip :title="getReviewStatusText(element.content.reviewInfo?.reviewStatus)">
                  <span class="review-status-icon" :class="`status-${element.content.reviewInfo?.reviewStatus}`">
                    <CheckCircleOutlined v-if="element.content.reviewInfo?.reviewStatus === 'corrected'" />
                    <ExclamationCircleOutlined v-else-if="element.content.reviewInfo?.reviewStatus === 'reviewed'" />
                    <ClockCircleOutlined v-else />
                  </span>
                </a-tooltip>
              </div>
            </div>
            <div class="abnormal-content" :data-index="index">
              <AbnormalSummaryInput
                v-model="element.content.text"
                @change="(val) => handleContentChange(index, val)"
                @select="(advice) => handleOptionSelect(index, advice)"
                :ref="
                  (el) => {
                    if (el) inputRefs[index] = el;
                  }
                "
                :readonly="readOnly"
              />

              <!-- 显示护士标注 -->
              <div v-if="showAnnotations && getItemAnnotations(index).length > 0" class="nurse-annotations">
                <div class="annotations-header">
                  <ExclamationCircleOutlined style="color: #faad14; margin-right: 4px" />
                  <span class="annotations-title">护士标注</span>
                </div>
                <div
                  v-for="annotation in getItemAnnotations(index)"
                  :key="annotation.id"
                  class="annotation-item"
                  :class="`annotation-${annotation.severity}`"
                >
                  <div class="annotation-header">
                    <a-tag :color="getAnnotationTagColor(annotation.type)" size="small">
                      {{ getAnnotationTypeText(annotation.type) }}
                    </a-tag>
                    <a-tag :color="getSeverityColor(annotation.severity)" size="small">
                      {{ getSeverityText(annotation.severity) }}
                    </a-tag>
                    <span class="annotation-time">{{ formatTime(annotation.createTime) }}</span>
                  </div>
                  <div class="annotation-content">{{ annotation.content }}</div>
                  <div v-if="annotation.selectedText" class="annotation-selected-text">
                    <span class="selected-label">选中文本：</span>
                    <span class="selected-text">{{ annotation.selectedText }}</span>
                  </div>
                  <!-- 处理状态和操作 -->
                  <div class="annotation-actions" v-if="!readOnly && annotation.status === 'pending'">
                    <a-button size="small" type="link" @click="handleAnnotation(annotation, 'handled')"> 标记已处理 </a-button>
                    <a-button size="small" type="link" @click="showHandleModal(annotation)"> 添加处理意见</a-button>
                  </div>
                  <div v-if="annotation.status === 'handled'" class="annotation-handled">
                    <a-tag color="green" size="small">已处理</a-tag>
                    <span v-if="annotation.handleComment" class="handle-comment">{{ annotation.handleComment }}</span>
                    <span class="handle-time">{{ formatTime(annotation.handleTime) }}</span>
                  </div>
                </div>
              </div>

              <!-- 显示审阅信息 -->
              <div v-if="hasReviewInfo(index)" class="review-info">
                <div class="review-header">
                  <AuditOutlined style="color: #1890ff; margin-right: 4px" />
                  <span class="review-title">审阅信息</span>
                  <a-tag :color="getReviewStatusColor(element.content.reviewInfo?.reviewStatus)" size="small">
                    {{ getReviewStatusText(element.content.reviewInfo?.reviewStatus) }}
                  </a-tag>
                </div>

                <div class="review-details">
                  <div class="review-meta">
                    <span class="reviewer">审阅人：{{ element.content.reviewInfo?.reviewer }}</span>
                    <span class="review-time">时间：{{ formatTime(element.content.reviewInfo?.reviewTime) }}</span>
                  </div>

                  <div v-if="element.content.reviewInfo?.reviewComment" class="review-comment">
                    <div class="comment-label">审阅意见：</div>
                    <div class="comment-content">{{ element.content.reviewInfo.reviewComment }}</div>
                  </div>

                  <div v-if="element.content.reviewInfo?.correctedContent" class="corrected-content">
                    <div class="corrected-label">更正内容：</div>
                    <div class="corrected-text">{{ element.content.reviewInfo.correctedContent }}</div>
                    <a-button size="small" type="link" @click="applyCorrectedContent(index)"> 应用更正</a-button>
                  </div>

                  <div v-if="element.content.reviewInfo?.annotations?.length > 0" class="review-annotations">
                    <div class="annotations-count"> 标注数量：{{ element.content.reviewInfo.annotations.length }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 只读模式下的静态列表 -->
        <div v-else>
          <div v-for="(element, index) in abnormalList" :key="element.id" class="abnormal-item">
            <!-- 这里需要添加只读模式的内容，暂时留空 -->
            <div class="line-number">
              <span class="number-badge">{{ index + 1 }}</span>
            </div>
            <div class="abnormal-content">
              <!-- 只读内容 -->
              {{ element.content.text }}
            </div>
          </div>
        </div>

        <div v-if="abnormalList.length === 0" class="empty-state">
          <a-empty description="暂无汇总信息" />
        </div>

        <div class="add-line-container" v-if="!readOnly">
          <a-button type="dashed" block @click="addLine">
            <template #icon>
              <PlusOutlined />
            </template>
            添加汇总
          </a-button>
        </div>
      </div>
    </div>

    <!-- 建议选择对话框 -->
    <a-modal v-model:open="showAdviceSelector" title="选择建议" @cancel="handleAdviceCancel" @ok="handleAdviceCancel" width="50%" :footer="null">
      <div class="advice-modal-content">
        <div class="advice-search-section">
          <a-input
            v-model:value="adviceKeywords"
            placeholder="输入关键词搜索建议..."
            allow-clear
            @input="handleAdviceSearch"
            @clear="handleAdviceClear"
            @keydown.enter="performAdviceSearch"
          >
            <template #suffix>
              <a-button type="text" size="small" @click="performAdviceSearch" :loading="adviceLoading">
                <search-outlined />
              </a-button>
            </template>
          </a-input>
        </div>

        <div class="advice-results-section">
          <a-spin :spinning="adviceLoading">
            <div class="advice-options-container" v-if="adviceOptions.length > 0">
              <div
                v-for="(option, index) in adviceOptions"
                :key="`advice-${index}-${option.id}`"
                class="advice-option"
                :class="{ 'advice-option-active': selectedAdviceIndex === index }"
                @click="handleAdviceSelect(option)"
                @mouseenter="selectedAdviceIndex = index"
              >
                <div class="advice-option-content">
                  <div class="advice-option-header">
                    <span class="advice-keywords">{{ option.keywords }}</span>
                    <span class="advice-score" v-if="option.matchScore !== undefined">
                      匹配度: {{ Math.round((option.matchScore / 25) * 100) }}%
                    </span>
                  </div>
                  <div class="advice-option-body">{{ option.adviceContent }}</div>
                </div>
              </div>
            </div>
            <div v-else-if="!adviceLoading && adviceKeywords" class="no-advice-data">
              <inbox-outlined style="margin-right: 8px; font-size: 16px" />
              未找到匹配的建议
            </div>
            <div v-else-if="!adviceKeywords" class="advice-placeholder"> 请输入关键词搜索建议</div>
          </a-spin>
        </div>
      </div>
    </a-modal>
  </a-spin>
</template>

<script lang="ts" setup>
  import { defineEmits, defineProps, nextTick, onMounted, ref, watch } from 'vue';
  import {
    AuditOutlined,
    CheckCircleOutlined,
    ClockCircleOutlined,
    DownOutlined,
    EllipsisOutlined,
    ExclamationCircleOutlined,
    InboxOutlined,
    MinusOutlined,
    PlusOutlined,
    SearchOutlined,
    UpOutlined,
  } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import { debounce } from 'lodash';
  // 移除拖拽功能
  // import { VueDraggableNext as draggable } from 'vue-draggable-next';
  import AbnormalSummaryInput from '@/views/summary/components/AbnormalSummaryInput.vue';
  import { listAdviceWithLimit } from '@/views/summary/SummaryAdvice.api';

  import { AbnormalSummary } from '#/types';

  interface AbnormalItem {
    id: string;
    content: AbnormalSummary;
  }

  const props = defineProps({
    modelValue: {
      type: Array as () => AbnormalSummary[],
      default: () => [],
    },
    // 护士标注数据
    nurseAnnotations: {
      type: Array as () => any[],
      default: () => [],
    },
    // 是否显示标注
    showAnnotations: {
      type: Boolean,
      default: false,
    },
    // 是否只读模式
    readOnly: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['update:modelValue', 'change', 'addAdvice', 'getAbnormalSummary']);

  const handleGetAbnormalSummary = () => {
    emit('getAbnormalSummary');
  };

  // 生成唯一ID
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  };

  // 内部异常列表数据
  const abnormalList = ref<AbnormalItem[]>([]);

  // 下拉菜单可见性映射
  const dropdownVisibleMap = ref<Record<string, boolean>>({});

  // 输入组件引用数组
  const inputRefs = ref<any[]>([]);

  // 气泡可见性映射
  const popoverVisibleMap = ref<Record<string, boolean>>({});

  // 建议选择器相关状态
  const showAdviceSelector = ref(false);
  const adviceKeywords = ref('');
  const adviceLoading = ref(false);
  const adviceOptions = ref<any[]>([]);
  const selectedAdviceIndex = ref(0);
  const currentAbnormalSummary = ref<AbnormalSummary | null>(null);

  // 拖拽相关状态
  const isDragging = ref(false);
  const dragStartIndex = ref(-1);
  const dragTargetIndex = ref(-1);

  // 初始化数据
  onMounted(() => {
    // 初始化输入组件引用数组
    if (props.modelValue && props.modelValue.length > 0) {
      inputRefs.value = new Array(props.modelValue.length);
      console.log(`AbnormalEditor: Initialized inputRefs array with length ${props.modelValue.length}`);

      // 初始化内部数据结构
      abnormalList.value = props.modelValue.map((summary) => ({
        id: generateId(),
        content: summary,
      }));
    } else {
      inputRefs.value = [];
    }

    if (!props.modelValue || props.modelValue.length == 0) {
      addLine();
    } else {
      // 如果有初始数据，聚焦到第一行
      console.log('AbnormalEditor: Initializing with existing data, will focus on first line');
      // 使用nextTick确保DOM已更新
      nextTick(() => {
        // 使用更长的延迟确保组件已完全渲染
        setTimeout(() => {
          focusInput(0);
        }, 200); // 初始化时使用更长的延迟
      });
    }

    // 使用MutationObserver监听内容容器的变化
    const observer = new MutationObserver((mutations) => {
      // 只在有相关变化时才设置监听器
      const hasRelevantChanges = mutations.some((mutation) => {
        // 检查是否有新的诊断内容容器添加
        return Array.from(mutation.addedNodes).some((node) => {
          return (node as HTMLElement).classList?.contains('abnormal-content') || (node as HTMLElement).querySelector?.('.abnormal-content');
        });
      });

      if (hasRelevantChanges) {
        console.log('AbnormalEditor: Detected new abnormal-content elements, setting up listeners');
        setupEnterKeyListeners();
      }
    });

    // 开始监听组件内的变化
    const editorElement = document.querySelector('.abnormal-editor');
    if (editorElement) {
      observer.observe(editorElement, {
        childList: true,
        subtree: true,
      });
    }

    // 监听数据变化，当数据变化时重新设置监听器
    watch(
      abnormalList,
      () => {
        setTimeout(setupEnterKeyListeners, 50);
      },
      { deep: true }
    );

    // 组件卸载时停止监听
    return () => {
      observer.disconnect();
    };
  });

  // 监听外部数据变化
  watch(
    () => props.modelValue,
    (newVal) => {
      // 创建一个函数来比较两个数组是否相等
      const areArraysEqual = (arr1: AbnormalSummary[], arr2: AbnormalItem[]) => {
        if (arr1.length !== arr2.length) return false;

        for (let i = 0; i < arr1.length; i++) {
          const item1 = arr1[i];
          const item2 = arr2[i].content;

          // 比较关键属性
          if (item1.text !== item2.text) return false;
        }

        return true;
      };

      if (newVal && !areArraysEqual(newVal, abnormalList.value)) {
        console.log('AbnormalEditor: External data changed, updating internal data');

        // 数据变化时清除缓存
        clearItemSummaryCache();

        abnormalList.value = newVal.map((summary) => ({
          id: generateId(),
          content: summary,
        }));

        // 如果有数据，聚焦到第一行
        if (abnormalList.value.length > 0) {
          console.log('AbnormalEditor: External data updated, will focus on first line');
          // 使用nextTick确保DOM已更新
          nextTick(() => {
            // 使用更长的延迟确保组件已完全渲染
            setTimeout(() => {
              focusInput(0);
            }, 200); // 使用更长的延迟
          });
        }
      }
    },
    { deep: true }
  );

  // 监听内部数据变化，更新modelValue
  watch(
    abnormalList,
    (newVal) => {
      const contentList = newVal.map((item) => item.content);
      emit('update:modelValue', contentList);
      emit('change', contentList);

      // 重置输入组件引用数组，确保其长度与异常列表一致
      inputRefs.value = new Array(newVal.length);
      console.log(`AbnormalEditor: Reset inputRefs array to length ${newVal.length}`);
    },
    { deep: true }
  );

  // 监听建议弹窗的打开状态，自动搜索
  watch(showAdviceSelector, (isOpen) => {
    if (isOpen && adviceKeywords.value) {
      console.log('建议弹窗打开，当前关键词:', adviceKeywords.value);
      // 自动执行搜索
      performAdviceSearch();
    }
  });

  // 添加新行
  const addLine = () => {
    // 保存当前长度，用于计算新行的索引
    const newIndex = abnormalList.value.length;

    // 创建新的AbnormalSummary对象
    const newSummary: AbnormalSummary = {
      text: '',
      title: '',
      summaryText: '',
      format: '',
      itemSummaryTextList: [],
      relatedItemResults: [],
      relatedItemGroupList: [],
      seq: (newIndex + 1).toString(),
    };

    // 添加新行
    abnormalList.value.push({
      id: generateId(),
      content: newSummary,
    });

    console.log(`AbnormalEditor: Added new line at index ${newIndex}`);

    // 使用nextTick确保DOM已更新，然后聚焦到新添加的行
    nextTick(() => {
      console.log(`AbnormalEditor: DOM updated after adding line, focusing index ${newIndex}`);
      // 使用更长的延迟确保组件已完全渲染
      setTimeout(() => {
        focusInput(newIndex);
      }, 100);
    });
  };

  // 在指定位置插入行
  const insertLine = (index: number) => {
    // 保存当前项目的ID
    const currentId = abnormalList.value[index].id;

    // 计算新行的索引
    const newIndex = index + 1;

    // 创建新的AbnormalSummary对象
    const newSummary: AbnormalSummary = {
      text: '',
      title: '',
      summaryText: '',
      format: '',
      itemSummaryTextList: [],
      relatedItemResults: [],
      relatedItemGroupList: [],
      seq: (newIndex + 1).toString(),
    };

    // 插入新行
    const newItem = {
      id: generateId(),
      content: newSummary,
    };
    abnormalList.value.splice(newIndex, 0, newItem);

    console.log(`AbnormalEditor: Inserted new line at index ${newIndex}`);

    // 保持下拉菜单打开状态
    keepDropdownOpen(currentId);

    // 使用nextTick确保DOM已更新，然后聚焦到新插入的行
    nextTick(() => {
      console.log(`AbnormalEditor: DOM updated after inserting line, focusing index ${newIndex}`);
      // 使用更长的延迟确保组件已完全渲染
      setTimeout(() => {
        focusInput(newIndex);
      }, 100);
    });
  };

  // 删除行
  const removeLine = (index: number) => {
    // 如果只有一行，不删除
    if (abnormalList.value.length <= 1) {
      return;
    }

    // 确定要聚焦的行
    const focusIndex = index < abnormalList.value.length - 1 ? index : index - 1;
    const focusId = abnormalList.value[focusIndex].id;

    console.log(`AbnormalEditor: Removing line at index ${index}, will focus on index ${focusIndex} after`);

    // 删除行
    abnormalList.value.splice(index, 1);

    // 如果删除后没有行了，自动添加一个空行
    if (abnormalList.value.length === 0) {
      addLine();
    } else {
      // 保持下拉菜单打开状态
      keepDropdownOpen(focusId);

      // 使用nextTick确保DOM已更新，然后聚焦到相应的行
      nextTick(() => {
        console.log(`AbnormalEditor: DOM updated after removing line, focusing index ${focusIndex}`);
        // 使用更长的延迟确保组件已完全渲染
        setTimeout(() => {
          focusInput(focusIndex);
        }, 100);
      });
    }
  };

  // 处理内容变化
  const handleContentChange = (index: number, value: string) => {
    // 获取当前的AbnormalSummary对象
    const currentSummary = abnormalList.value[index].content;

    // 更新text属性
    currentSummary.text = value;

    // 更新summaryText属性（如果需要）
    if (currentSummary.summaryText === undefined || currentSummary.summaryText === '') {
      currentSummary.summaryText = value;
    }

    // 内容变化时清除相关缓存
    clearItemSummaryCache();
  };

  // 处理选项选择
  const handleOptionSelect = (index: number, advice: { label: string; value: string }) => {
    console.log(`选中选项，行号: ${index}, 值: ${advice}`);

    if (advice) {
      const currentSummary = abnormalList.value[index].content;
      emit('addAdvice', { index, abnormalSummary: currentSummary, advice });
    }
  };

  // 加载异常汇总数据
  const loadAbnormalSummaries = (summaries: any[]) => {
    console.log('AbnormalEditor: Loading abnormal summaries:', summaries);
    if (Array.isArray(summaries) && summaries.length > 0) {
      // 将数据转换为内部格式
      abnormalList.value = summaries.map((summary) => ({
        id: generateId(),
        content: summary,
      }));
    } else {
      // 如果没有数据，添加一个空行
      clearContent();
    }
  };

  // 清空内容
  const clearContent = () => {
    console.log('AbnormalEditor: Clearing content');
    abnormalList.value = [];
    addLine(); // 添加一个空行
  };

  // 获取结构化内容
  const getStructuredContent = () => {
    return abnormalList.value.map((item) => item.content);
  };

  // 标注相关方法
  const hasAnnotation = (itemIndex: number) => {
    return props.nurseAnnotations.some((annotation) => annotation.itemIndex === itemIndex);
  };

  const getAnnotationCount = (itemIndex: number) => {
    return props.nurseAnnotations.filter((annotation) => annotation.itemIndex === itemIndex).length;
  };

  const getAnnotationColor = (itemIndex: number) => {
    const annotations = props.nurseAnnotations.filter((annotation) => annotation.itemIndex === itemIndex);
    if (annotations.some((a) => a.severity === 'high')) return '#ff4d4f';
    if (annotations.some((a) => a.severity === 'medium')) return '#faad14';
    return '#52c41a';
  };

  const getItemAnnotations = (itemIndex: number) => {
    return props.nurseAnnotations.filter((annotation) => annotation.itemIndex === itemIndex);
  };

  const getAnnotationTypeText = (type: string) => {
    const typeMap = {
      error: '错误',
      warning: '警告',
      suggestion: '建议',
    };
    return typeMap[type] || type;
  };

  const getAnnotationTagColor = (type: string) => {
    const colorMap = {
      error: 'red',
      warning: 'orange',
      suggestion: 'blue',
    };
    return colorMap[type] || 'default';
  };

  const getSeverityColor = (severity: string) => {
    const colorMap = {
      high: 'red',
      medium: 'orange',
      low: 'green',
    };
    return colorMap[severity] || 'default';
  };

  const getSeverityText = (severity: string) => {
    const textMap = {
      high: '严重',
      medium: '中等',
      low: '轻微',
    };
    return textMap[severity] || severity;
  };

  const formatTime = (time: string) => {
    return time ? new Date(time).toLocaleString() : '';
  };

  // 审阅相关方法
  const hasReviewInfo = (itemIndex: number) => {
    return abnormalList.value[itemIndex]?.content?.reviewInfo?.hasReview || false;
  };

  const getReviewStatusText = (status?: string) => {
    const statusMap = {
      pending: '待审阅',
      reviewed: '已审阅',
      corrected: '已更正',
    };
    return statusMap[status] || '未知状态';
  };

  const getReviewStatusColor = (status?: string) => {
    const colorMap = {
      pending: 'orange',
      reviewed: 'blue',
      corrected: 'green',
    };
    return colorMap[status] || 'default';
  };

  // 处理标注
  const handleAnnotation = async (annotation: any, action: string) => {
    try {
      // 这里应该调用API来处理标注
      annotation.status = action;
      annotation.handleTime = new Date().toISOString();
      annotation.handleBy = '当前用户'; // 实际应该从用户信息获取
      message.success('标注处理成功');
    } catch (error) {
      message.error('标注处理失败');
    }
  };

  // 显示处理意见弹窗
  const showHandleModal = (annotation: any) => {
    // 这里可以显示一个弹窗让用户输入处理意见
    // 暂时简化处理
    const comment = prompt('请输入处理意见：');
    if (comment) {
      annotation.handleComment = comment;
      handleAnnotation(annotation, 'handled');
    }
  };

  // 应用更正内容
  const applyCorrectedContent = (index: number) => {
    const item = abnormalList.value[index];
    if (item?.content?.reviewInfo?.correctedContent) {
      item.content.text = item.content.reviewInfo.correctedContent;
      item.content.reviewInfo.reviewStatus = 'corrected';
      message.success('已应用更正内容');
    }
  };

  // 气泡相关方法
  const handlePopoverChange = (itemId: string, visible: boolean) => {
    if (!visible) {
      // 关闭气泡时清理状态
      popoverVisibleMap.value[itemId] = false;
    }
  };

  // 缓存异常项目列表，避免重复计算
  const itemSummaryCache = ref(new Map<string, string[]>());

  // 获取异常项目列表 - 优化版本，添加缓存
  const getItemSummaryList = (content: AbnormalSummary): string[] => {
    // 生成缓存键，只包含影响结果的字段
    const cacheKey = `${content.id || 'no-id'}_${JSON.stringify(content.relatedItemResults || [])}_${content.text || ''}`;

    // 检查缓存
    if (itemSummaryCache.value.has(cacheKey)) {
      return itemSummaryCache.value.get(cacheKey)!;
    }

    const items: string[] = [];

    // 1. 优先从 relatedItemGroupList 获取到数据，则从 relatedItemResults 获取 checkConclusion
    if (content.relatedItemResults && content.relatedItemResults.length > 0) {
      content.relatedItemResults.forEach((result) => {
        if (result.checkConclusion && result.checkConclusion.trim() !== '') {
          items.push(result.checkConclusion.trim());
        }
      });
    }

    // 2. 最后降级使用 text 字段（手动添加的异常条目）
    if (items.length === 0 && content.text && content.text.trim() !== '') {
      items.push(content.text.trim());
    }

    // 缓存结果，限制缓存大小防止内存泄漏
    if (itemSummaryCache.value.size > 100) {
      // 清除最旧的缓存项
      const firstKey = itemSummaryCache.value.keys().next().value;
      itemSummaryCache.value.delete(firstKey);
    }
    itemSummaryCache.value.set(cacheKey, items);

    return items;
  };

  // 优化的缓存版本，用于模板中频繁调用
  const getItemSummaryListCached = (content: AbnormalSummary): string[] => {
    return getItemSummaryList(content);
  };

  // 检查是否有异常项目
  const hasItemSummary = (content: AbnormalSummary): boolean => {
    return getItemSummaryList(content).length > 0;
  };

  // 清除缓存的方法
  const clearItemSummaryCache = () => {
    itemSummaryCache.value.clear();
  };

  // 处理异常项目点击
  const handleItemClick = (item: string, content: AbnormalSummary) => {
    console.log('点击异常项目:', item, content);

    // 防止重复点击
    if (showAdviceSelector.value) {
      return;
    }

    // 关闭所有气泡
    Object.keys(popoverVisibleMap.value).forEach((key) => {
      popoverVisibleMap.value[key] = false;
    });

    // 保存当前的异常汇总内容，用于后续传递给父组件
    currentAbnormalSummary.value = content;

    // 先设置搜索关键词
    adviceKeywords.value = item;

    // 延迟打开建议选择器，确保状态更新完成
    nextTick(() => {
      showAdviceSelector.value = true;
    });
  };

  // 处理建议搜索输入
  const handleAdviceSearch = debounce(() => {
    if (adviceKeywords.value && adviceKeywords.value.trim() !== '') {
      performAdviceSearch();
    } else {
      adviceOptions.value = [];
    }
  }, 300);

  // 处理建议搜索清空
  const handleAdviceClear = () => {
    adviceOptions.value = [];
    selectedAdviceIndex.value = 0;
  };

  // 执行建议搜索
  const performAdviceSearch = async () => {
    const keyword = adviceKeywords.value?.trim();
    if (!keyword) {
      adviceOptions.value = [];
      return;
    }

    try {
      adviceLoading.value = true;
      const params = { keywords: keyword };
      const res = await listAdviceWithLimit(params);

      // 转换数据格式
      adviceOptions.value = res || [];
      selectedAdviceIndex.value = 0;

      console.log('搜索到建议:', adviceOptions.value.length, '条');
    } catch (error) {
      console.error('搜索建议失败:', error);
      adviceOptions.value = [];
    } finally {
      adviceLoading.value = false;
    }
  };

  // 处理建议选择
  const handleAdviceSelect = (option: any) => {
    console.log('选择建议:', option);

    if (!option || !option.keywords || !option.adviceContent) {
      console.warn('Invalid advice selected:', option);
      return;
    }

    // 触发父组件的建议选择事件，传递正确的数据结构
    emit('addAdvice', {
      abnormalSummary: currentAbnormalSummary.value,
      advice: {
        label: option.keywords,
        value: option.adviceContent,
      },
    });

    // 关闭对话框
    showAdviceSelector.value = false;
    adviceKeywords.value = '';
    adviceOptions.value = [];
    currentAbnormalSummary.value = null;
    //message.success(`已添加建议: ${option.keywords}`);
  };

  // 处理建议选择器取消
  const handleAdviceCancel = () => {
    showAdviceSelector.value = false;
    adviceKeywords.value = '';
    adviceOptions.value = [];
    selectedAdviceIndex.value = 0;
    currentAbnormalSummary.value = null;
  };

  // 聚焦到指定索引的输入框
  const focusInput = (index: number, position?: 'start' | 'end') => {
    console.log(`AbnormalEditor: Attempting to focus on index ${index}`);

    // 确保索引有效
    if (index < 0 || index >= abnormalList.value.length) {
      console.log(`AbnormalEditor: Invalid focus index ${index}, adjusting to valid range`);
      index = Math.max(0, Math.min(index, abnormalList.value.length - 1));
    }

    const focusInputAtIndex = () => {
      // 先尝试使用组件引用数组
      if (inputRefs.value && inputRefs.value[index]) {
        const inputComponent = inputRefs.value[index];
        console.log(`AbnormalEditor: Found input component at index ${index} using refs`);

        if (typeof inputComponent.focus === 'function') {
          console.log('AbnormalEditor: Calling focus method on component');
          const result = inputComponent.focus();
          if (result) {
            console.log('AbnormalEditor: Successfully focused using component ref method');
            return true;
          }
        }
      }

      // 如果使用组件引用失败，尝试使用DOM
      console.log('AbnormalEditor: Falling back to DOM-based focus method');

      // 先尝试获取所有的内容容器
      const contentDivs = document.querySelectorAll('.abnormal-content');
      console.log('AbnormalEditor: Found content divs:', contentDivs.length);

      if (contentDivs.length <= index) {
        console.log(`AbnormalEditor: Not enough content divs (${contentDivs.length}) for index ${index}`);
        return false;
      }

      // 获取当前索引的内容容器
      const currentContentDiv = contentDivs[index];

      // 尝试使用AbnormalSummaryInput组件的focus方法
      try {
        // 获取组件实例
        const componentInstance = currentContentDiv.querySelector('.icd10-input-container')?.__vueParentComponent?.ctx;

        if (componentInstance && typeof componentInstance.focus === 'function') {
          console.log('AbnormalEditor: Found component instance with focus method, calling it');
          const result = componentInstance.focus();
          if (result) {
            console.log('AbnormalEditor: Successfully focused using component method');
            return true;
          }
        }
      } catch (error) {
        console.error('AbnormalEditor: Error trying to use component focus method:', error);
      }

      // 如果上面的方法失败，尝试直接操作DOM
      // 尝试找到输入元素 - 先尝试textarea，然后尝试input
      let inputElement = currentContentDiv.querySelector('textarea.auto-resize-textarea');

      if (!inputElement) {
        // 如果没有找到textarea，尝试找到input
        inputElement = currentContentDiv.querySelector('.ant-select-selector input');
      }

      if (!inputElement) {
        // 如果还是没有找到，尝试其他可能的选择器
        inputElement = currentContentDiv.querySelector('input') || currentContentDiv.querySelector('textarea');
      }

      console.log('AbnormalEditor: Found input element:', !!inputElement, inputElement ? `(type: ${inputElement.tagName})` : '');

      if (inputElement) {
        console.log('AbnormalEditor: Focusing element at index', index, 'position:', position);

        // 聚焦到元素
        inputElement.focus();

        // 如果是输入框并且指定了光标位置
        if (position && (inputElement instanceof HTMLInputElement || inputElement instanceof HTMLTextAreaElement)) {
          try {
            // 设置光标位置
            if (position === 'start') {
              inputElement.setSelectionRange(0, 0);
            } else if (position === 'end') {
              const length = inputElement.value.length;
              inputElement.setSelectionRange(length, length);
            }
          } catch (error) {
            console.error('AbnormalEditor: Error setting cursor position:', error);
          }
        }

        return true;
      } else {
        console.log('AbnormalEditor: Could not find input element in content div at index', index);
        return false;
      }
    };

    // 尝试聚焦，如果失败，则重试几次
    let attempts = 0;
    const maxAttempts = 10; // 增加重试次数
    const tryFocus = () => {
      setTimeout(
        () => {
          if (!focusInputAtIndex() && attempts < maxAttempts) {
            attempts++;
            console.log(`AbnormalEditor: Retrying focus, attempt ${attempts}`);
            tryFocus();
          }
        },
        50 * (attempts + 1) // 增加重试间隔，确保DOM已更新
      );
    };

    // 使用nextTick确保DOM已更新
    nextTick(() => {
      tryFocus();
    });
  };

  // 向上移动项目
  const moveItemUp = (index) => {
    // 如果已经是第一项，不能再向上移动
    if (index === 0) return;

    // 保存当前项目和其ID
    const currentItem = { ...abnormalList.value[index] };
    const currentId = currentItem.id;

    // 计算新的索引
    const newIndex = index - 1;

    // 保存上一项
    const prevItem = { ...abnormalList.value[newIndex] };

    console.log(`AbnormalEditor: Moving item from index ${index} to ${newIndex}`);

    // 更新seq属性
    currentItem.content.seq = (newIndex + 1).toString();
    prevItem.content.seq = (newIndex + 2).toString();

    // 交换位置
    abnormalList.value.splice(newIndex, 2, currentItem, prevItem);

    // 添加高亮效果
    highlightMovedItem(newIndex);

    // 保持下拉菜单打开状态
    keepDropdownOpen(currentId);

    // 使用nextTick确保DOM已更新，然后聚焦到移动后的行
    nextTick(() => {
      console.log(`AbnormalEditor: DOM updated after moving item up, focusing index ${newIndex}`);
      // 使用更长的延迟确保组件已完全渲染
      setTimeout(() => {
        focusInput(newIndex);
      }, 100);
    });
  };

  // 向下移动项目
  const moveItemDown = (index) => {
    // 如果已经是最后一项，不能再向下移动
    if (index === abnormalList.value.length - 1) return;

    // 保存当前项目和其ID
    const currentItem = { ...abnormalList.value[index] };
    const currentId = currentItem.id;

    // 计算新的索引
    const newIndex = index + 1;

    // 保存下一项
    const nextItem = { ...abnormalList.value[newIndex] };

    console.log(`AbnormalEditor: Moving item from index ${index} to ${newIndex}`);

    // 更新seq属性
    currentItem.content.seq = (newIndex + 1).toString();
    nextItem.content.seq = (index + 1).toString();

    // 交换位置
    abnormalList.value.splice(index, 2, nextItem, currentItem);

    // 添加高亮效果
    highlightMovedItem(newIndex);

    // 保持下拉菜单打开状态
    keepDropdownOpen(currentId);

    // 使用nextTick确保DOM已更新，然后聚焦到移动后的行
    nextTick(() => {
      console.log(`AbnormalEditor: DOM updated after moving item down, focusing index ${newIndex}`);
      // 使用更长的延迟确保组件已完全渲染
      setTimeout(() => {
        focusInput(newIndex);
      }, 100);
    });
  };

  // 保持下拉菜单打开状态
  const keepDropdownOpen = (itemId) => {
    // 先关闭所有下拉菜单
    Object.keys(dropdownVisibleMap.value).forEach((key) => {
      dropdownVisibleMap.value[key] = false;
    });

    // 然后打开当前项目的下拉菜单
    // 使用nextTick确保在DOM更新后执行
    nextTick(() => {
      dropdownVisibleMap.value[itemId] = true;
    });
  };

  // 处理移动后的项目 - 已移除高亮效果
  const highlightMovedItem = (index) => {
    // 移除高亮效果逻辑，保留函数以避免引用错误
    console.log(`Item moved to index ${index}`);
  };

  // 拖拽处理函数
  const handleDragStart = (e) => {
    isDragging.value = true;
    dragStartIndex.value = e.oldIndex;
    console.log('AbnormalEditor: Drag started at index', e.oldIndex);
  };

  const handleDragEnd = (e) => {
    isDragging.value = false;
    dragStartIndex.value = -1;
    dragTargetIndex.value = -1;

    // 如果位置发生变化，更新seq属性
    if (e.oldIndex !== e.newIndex) {
      console.log(`AbnormalEditor: Item moved from ${e.oldIndex} to ${e.newIndex}`);

      // 更新所有项目的seq属性
      abnormalList.value.forEach((item, index) => {
        item.content.seq = (index + 1).toString();
      });

      // 聚焦到移动后的位置
      nextTick(() => {
        setTimeout(() => {
          focusInput(e.newIndex);
        }, 100);
      });
    }

    console.log('AbnormalEditor: Drag ended');
  };

  const handleDragChange = (e) => {
    if (e.moved) {
      dragTargetIndex.value = e.moved.newIndex;
      console.log('AbnormalEditor: Drag target changed to index', e.moved.newIndex);
    }
  };

  // 设置回车键事件监听器
  const setupEnterKeyListeners = () => {
    // 查找所有异常内容容器并添加事件监听器
    const contentDivs = document.querySelectorAll('.abnormal-content');
    contentDivs.forEach((contentDiv, idx) => {
      // 检查是否已经添加了事件监听器
      if (!(contentDiv as any).__hasKeyListener) {
        console.log(`AbnormalEditor: Adding keydown listener to abnormal-content at index ${idx}`);

        // 获取索引
        const index = parseInt(contentDiv.getAttribute('data-index') || '0', 10);

        // 添加事件监听器到内容容器
        contentDiv.addEventListener(
          'keydown',
          (e: KeyboardEvent) => {
            // 检查事件是否来自 CustomAutoComplete 组件
            const isFromCustomAutoComplete = e.target && (e.target as HTMLElement).closest('.custom-auto-complete');

            // 如果事件来自 CustomAutoComplete，则不处理
            if (isFromCustomAutoComplete) {
              console.log('AbnormalEditor: Event from CustomAutoComplete, not handling');
              return;
            }

            // 如果事件来自下拉选择器，且下拉菜单打开，则不处理箭头键
            const dropdown = document.querySelector('.ant-select-dropdown:not(.ant-select-dropdown-hidden)');

            // 处理回车键
            if (e.key === 'Enter') {
              // 如果下拉菜单打开，先选中当前项，然后在下一个事件循环中添加新行
              if (dropdown) {
                console.log('AbnormalEditor: Dropdown is open, handling enter key anyway');
                // 先让默认行为发生（选中项目），然后在下一个事件循环中添加新行
                setTimeout(() => {
                  console.log(`AbnormalEditor: Adding new line after dropdown selection at index ${index}`);
                  handleEnterPress(index);
                }, 0);
                return;
              }

              // 阻止默认行为
              e.preventDefault();
              e.stopPropagation();

              console.log(`AbnormalEditor: Enter key pressed on input at index ${index}`);
              handleEnterPress(index);
            }
          },
          true
        ); // 使用捕获阶段，确保在子元素处理前捕获事件

        // 标记该容器已添加事件监听器
        (contentDiv as any).__hasKeyListener = true;
      }
    });
  };

  // 处理回车键按下事件
  const handleEnterPress = (index: number) => {
    console.log('AbnormalEditor: handleEnterPress called for index', index);

    // 检查下一行是否存在且为空
    const hasEmptyNextLine =
      index + 1 < abnormalList.value.length &&
      (!abnormalList.value[index + 1].content.text || abnormalList.value[index + 1].content.text.trim() === '');

    if (hasEmptyNextLine) {
      console.log('AbnormalEditor: Next line is already empty, focusing it instead of adding new line');
      // 直接聚焦到下一行
      // 使用nextTick确保DOM已更新
      nextTick(() => {
        setTimeout(() => {
          focusInput(index + 1);
        }, 100);
      });
      return;
    }

    // 在当前行后插入新行
    insertLine(index);
    console.log('AbnormalEditor: New line inserted after index', index);

    // insertLine方法已经包含了聚焦逻辑，不需要再次调用focusInput
  };

  // 向父组件暴露方法
  defineExpose({
    loadAbnormalSummaries,
    clearContent,
    getStructuredContent,
  });
</script>

<style scoped>
  .abnormal-editor {
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  }

  .editor-header {
    display: flex;
    background-color: #fafafa;
    padding: 6px 4px;
    font-weight: 500;
    border-bottom: 1px solid #f0f0f0;
    font-size: 11px;
    color: #666;
  }

  .header-line-number {
    width: 30px;
    flex-shrink: 0;
    padding-left: 4px;
    font-size: 11px;
  }

  .header-content {
    flex: 1;
  }

  .header-actions {
    width: 30px;
    display: flex;
    justify-content: flex-end;
    padding-right: 4px;
  }

  .header-action-item {
    font-size: 10px;
    color: #999;
    width: 20px;
    text-align: center;
  }

  .editor-body {
    padding: 6px 6px 10px;
  }

  .abnormal-item {
    display: flex;
    align-items: flex-start;
    padding: 4px 0;
    transition:
      background-color 0.2s ease,
      border-color 0.2s ease;
    margin-bottom: 2px;
    position: relative;
    user-select: none; /* 防止拖拽时选中文本 */
    border-radius: 6px;
    border: 1px solid transparent;
  }

  .abnormal-item:hover {
    background-color: #fafafa;
    border-color: #f0f0f0;
  }

  /* 拖拽时禁用过渡效果 */
  :global(.abnormal-dragging) .abnormal-item {
    transition: none;
  }

  /* Removed hover background style */

  .line-number {
    width: 24px;
    padding: 0 2px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    border-radius: 4px;
    transition: background-color 0.2s;
    position: relative;
  }

  .line-number:hover {
    background-color: #f5f5f5;
  }

  /* 拖拽手柄样式 */
  .drag-handle {
    cursor: grab;
  }

  .drag-handle:active {
    cursor: grabbing;
  }

  .drag-handle-icon {
    position: absolute;
    left: -20px;
    top: 50%;
    transform: translateY(-50%);
    width: 18px;
    height: 24px;
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 4px;
    border: 1px solid #e8e8e8;
    color: #bfbfbf;
    backdrop-filter: blur(4px);
    pointer-events: none;
  }

  .abnormal-item:hover .drag-handle-icon {
    opacity: 1;
  }

  .drag-handle-icon:hover {
    background-color: #f0f9ff;
    border-color: #91d5ff;
    color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  }

  .drag-handle-active {
    opacity: 1 !important;
    background-color: #e6f7ff;
    border-color: #1890ff;
    color: #1890ff;
  }

  .number-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 18px;
    height: 18px;
    padding: 0 2px;
    font-size: 11px;
    line-height: 18px;
    background-color: #f5f5f5;
    border-radius: 9px;
    color: #666;
    border: 1px solid #e8e8e8;
    transition: all 0.2s;
  }

  .number-badge.clickable {
    cursor: pointer;
  }

  .number-badge.clickable:hover {
    background-color: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
    transform: scale(1.1);
  }

  .number-badge.has-items {
    background-color: #fff7e6;
    border-color: #ffd591;
    color: #fa8c16;
    font-weight: 600;
  }

  .number-badge.has-items:hover {
    background-color: #fff2e8;
    border-color: #ffbb96;
    color: #d4380d;
  }

  .abnormal-content {
    flex: 1;
    cursor: text;
  }

  /* Ensure no borders on inputs */
  .abnormal-content :deep(.ant-auto-complete),
  .abnormal-content :deep(.ant-select-selector),
  .abnormal-content :deep(.ant-input),
  .abnormal-content :deep(textarea) {
    border: none !important;
    box-shadow: none !important;
    outline: none !important;
  }

  .abnormal-actions {
    width: 30px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    color: #aaa;
    z-index: 20;
    position: relative;
    border-radius: 4px;
    transition: background-color 0.2s;
  }

  .action-icon {
    cursor: pointer;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 4px;
    background-color: #f9f9f9;
    border: 1px solid #e8e8e8;
    font-size: 12px;
  }

  :deep(.move-up-menu-item),
  :deep(.move-down-menu-item) {
    color: rgba(0, 0, 0, 0.85);
  }

  :deep(.move-up-menu-item:hover),
  :deep(.move-down-menu-item:hover) {
    color: #1890ff;
  }

  :deep(.move-up-menu-item.ant-menu-item-disabled),
  :deep(.move-down-menu-item.ant-menu-item-disabled) {
    color: rgba(0, 0, 0, 0.25);
  }

  .more-actions {
    color: #bfbfbf;
  }

  .more-actions:hover {
    color: #1890ff;
  }

  .action-icon:hover {
    transform: scale(1.05);
    background-color: #f0f0f0;
    color: #1890ff;
    border-color: #e0e0e0;
    z-index: 25;
  }

  /* 为删除菜单项添加特殊样式 */
  :deep(.ant-dropdown-menu) {
    min-width: 120px;
  }

  :deep(.ant-dropdown-menu-item) {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  :deep(.delete-menu-item) {
    color: #ff4d4f;
  }

  :deep(.delete-menu-item:hover) {
    background-color: #fff1f0;
  }

  .add-line-container {
    margin-top: 16px;
  }

  .add-line-container :deep(.ant-btn) {
    transition: all 0.2s;
    height: 32px;
    font-size: 13px;
  }

  .add-line-container :deep(.ant-btn:hover) {
    transform: scale(1.02);
    border-color: #1890ff;
    color: #1890ff;
  }

  .empty-state {
    padding: 24px 0;
    text-align: center;
  }

  /* 标注相关样式 */
  .abnormal-item.has-annotation {
    background-color: #fff7e6;
    border-left: 3px solid #faad14;
  }

  .abnormal-item.has-review {
    background-color: #f0f9ff;
    border-left: 3px solid #1890ff;
  }

  .abnormal-item.has-annotation.has-review {
    background: linear-gradient(to right, #fff7e6 50%, #f0f9ff 50%);
    border-left: 3px solid #722ed1;
  }

  .nurse-annotations {
    margin-top: 12px;
    padding: 8px 12px;
    background-color: #fafafa;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
  }

  .annotations-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 12px;
    font-weight: 600;
    color: #666;
  }

  .annotations-title {
    color: #faad14;
  }

  .annotation-item {
    margin-bottom: 8px;
    padding: 8px;
    background-color: #fff;
    border-radius: 4px;
    border-left: 3px solid #e8e8e8;
  }

  .annotation-item.annotation-high {
    border-left-color: #ff4d4f;
  }

  .annotation-item.annotation-medium {
    border-left-color: #faad14;
  }

  .annotation-item.annotation-low {
    border-left-color: #52c41a;
  }

  .annotation-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
  }

  .annotation-time {
    font-size: 11px;
    color: #999;
    margin-left: auto;
  }

  .annotation-content {
    font-size: 13px;
    color: #666;
    line-height: 1.4;
  }

  .annotation-selected-text {
    margin-top: 4px;
    font-size: 12px;
  }

  .selected-label {
    color: #999;
  }

  .selected-text {
    color: #666;
    background-color: #f0f0f0;
    padding: 2px 4px;
    border-radius: 2px;
    font-family: monospace;
  }

  /* 审阅状态指示器 */
  .review-status-indicator {
    margin-top: 4px;
    display: flex;
    justify-content: center;
  }

  .review-status-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    font-size: 12px;
  }

  .review-status-icon.status-pending {
    color: #faad14;
    background-color: #fff7e6;
  }

  .review-status-icon.status-reviewed {
    color: #1890ff;
    background-color: #e6f7ff;
  }

  .review-status-icon.status-corrected {
    color: #52c41a;
    background-color: #f6ffed;
  }

  /* 审阅信息样式 */
  .review-info {
    margin-top: 12px;
    padding: 8px 12px;
    background-color: #f0f9ff;
    border: 1px solid #d6e4ff;
    border-radius: 4px;
  }

  .review-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 12px;
    font-weight: 600;
  }

  .review-title {
    color: #1890ff;
    margin-right: 8px;
  }

  .review-details {
    font-size: 12px;
  }

  .review-meta {
    display: flex;
    gap: 16px;
    margin-bottom: 8px;
    color: #666;
  }

  .review-comment {
    margin-bottom: 8px;
  }

  .comment-label {
    color: #666;
    margin-bottom: 4px;
    font-weight: 500;
  }

  .comment-content {
    background-color: #fff;
    padding: 6px 8px;
    border-radius: 4px;
    border-left: 3px solid #1890ff;
    color: #333;
  }

  .corrected-content {
    margin-bottom: 8px;
  }

  .corrected-label {
    color: #666;
    margin-bottom: 4px;
    font-weight: 500;
  }

  .corrected-text {
    background-color: #f6ffed;
    padding: 6px 8px;
    border-radius: 4px;
    border-left: 3px solid #52c41a;
    color: #333;
    margin-bottom: 4px;
  }

  .review-annotations {
    color: #666;
  }

  .annotations-count {
    font-size: 11px;
  }

  /* 标注操作样式 */
  .annotation-actions {
    margin-top: 4px;
    display: flex;
    gap: 8px;
  }

  .annotation-handled {
    margin-top: 4px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 11px;
    color: #666;
  }

  .handle-comment {
    color: #333;
  }

  .handle-time {
    color: #999;
  }

  /* 移动后的项目样式 - 已移除背景色和动画样式 */

  /* 气泡内容样式 */
  .item-summary-list {
    min-width: 200px;
    max-width: 300px;
    overflow: hidden; /* 防止内容溢出 */
  }

  .item-summary-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    padding-bottom: 6px;
    border-bottom: 1px solid #f0f0f0;
  }

  .header-title {
    font-weight: 600;
    color: #262626;
    font-size: 13px;
  }

  .item-count {
    font-size: 11px;
    color: #8c8c8c;
  }

  .item-summary-content {
    max-height: 200px;
    overflow-y: auto;
    overflow-x: hidden; /* 防止水平滚动条 */
    padding-right: 2px; /* 为hover效果预留空间 */
    margin-right: -2px; /* 抵消padding的影响 */
  }

  .item-summary-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 8px;
    margin-bottom: 4px;
    margin-right: 2px; /* 为hover效果预留空间 */
    background-color: #fafafa;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    position: relative;
    overflow: hidden; /* 确保内容不会溢出 */
  }

  .item-summary-item:hover {
    background-color: #e6f7ff;
    border-color: #91d5ff;
    /* 移除 translateX，改用其他视觉效果 */
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.15);
    transform: scale(1.02); /* 使用轻微的缩放效果替代平移 */
  }

  .item-summary-item:last-child {
    margin-bottom: 0;
  }

  .item-text {
    flex: 1;
    font-size: 12px;
    color: #595959;
    line-height: 1.4;
    word-break: break-all;
    margin-right: 8px;
    overflow: hidden; /* 防止文本溢出 */
    text-overflow: ellipsis; /* 长文本显示省略号 */
  }

  .search-icon {
    color: #8c8c8c;
    font-size: 12px;
    flex-shrink: 0;
    transition: all 0.2s ease;
  }

  .item-summary-item:hover .search-icon {
    color: #1890ff;
    transform: scale(1.1); /* 图标轻微放大 */
  }

  .empty-items {
    text-align: center;
    color: #bfbfbf;
    font-size: 12px;
    padding: 16px 0;
  }

  /* 拖拽状态样式 */
  .ghost-item {
    opacity: 0.5;
    background: #f0f9ff !important;
    border: 2px dashed #1890ff !important;
    border-radius: 6px;
  }

  .chosen-item {
    background-color: #e6f7ff !important;
    border: 1px solid #1890ff !important;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15) !important;
  }

  .drag-item {
    background-color: #fff !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    border: 1px solid #1890ff !important;
    border-radius: 6px;
    opacity: 0.9;
  }

  .dragging {
    opacity: 0.6;
    background-color: #f0f9ff;
  }

  /* 全局拖拽状态 */
  :global(.abnormal-dragging) {
    user-select: none;
    cursor: grabbing;
  }

  :global(.abnormal-dragging *) {
    cursor: grabbing !important;
  }

  /* 拖拽时隐藏其他交互元素 */
  :global(.abnormal-dragging) .abnormal-actions,
  :global(.abnormal-dragging) .number-badge {
    pointer-events: none;
    opacity: 0.5;
  }

  /* 建议弹窗样式 */
  .advice-modal-content {
    padding: 0;
  }

  .advice-search-section {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;
  }

  .advice-results-section {
    height: 50vh;
    overflow-y: auto;
    padding: 8px;
  }

  .advice-options-container {
    max-height: 100%;
  }

  .advice-option {
    padding: 12px;
    margin-bottom: 8px;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #fff;
  }

  .advice-option:hover,
  .advice-option-active {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
    transform: translateY(-1px);
  }

  .advice-option:last-child {
    margin-bottom: 0;
  }

  .advice-option-content {
    width: 100%;
  }

  .advice-option-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  .advice-keywords {
    font-weight: 600;
    color: #1890ff;
    font-size: 14px;
  }

  .advice-score {
    font-size: 12px;
    color: #52c41a;
    background: #f6ffed;
    padding: 2px 6px;
    border-radius: 10px;
    border: 1px solid #b7eb8f;
  }

  .advice-option-body {
    color: #595959;
    line-height: 1.5;
    font-size: 13px;
  }

  .no-advice-data,
  .advice-placeholder {
    text-align: center;
    color: #bfbfbf;
    font-size: 14px;
    padding: 40px 20px;
  }

  .advice-placeholder {
    color: #8c8c8c;
  }
</style>
