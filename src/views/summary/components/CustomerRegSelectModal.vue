<template>
  <a-modal
    title="选择体检记录"
    :width="800"
    :open="visible"
    @ok="handleOk"
    @cancel="handleCancel"
    :okButtonProps="{ disabled: !selectedRecord || selectedRecord.examCategory !== '职业病体检' }"
    okText="确定选择"
    cancelText="取消"
  >
    <div style="margin-bottom: 16px;">
      <a-alert
        message="找到多条匹配记录，请选择要处理的体检记录"
        type="info"
        show-icon
      />
      <a-alert
        v-if="hasNonOccuRecords"
        message="注意：列表中包含非职业病体检记录，这些记录无法在当前页面处理，已禁用选择"
        type="warning"
        show-icon
        style="margin-top: 8px;"
      />
    </div>

    <a-table
      :columns="columns"
      :data-source="recordList"
      :pagination="false"
      size="small"
      :row-selection="rowSelection"
      :scroll="{ y: 400 }"
      row-key="id"
      :row-class-name="getRowClassName"
    >
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex === 'examCategory'">
          <a-tag :color="getExamCategoryColor(text)">
            {{ text }}
            <ExclamationCircleOutlined v-if="text !== '职业病体检'" style="margin-left: 4px;" />
          </a-tag>
        </template>
        <template v-if="column.dataIndex === 'summaryStatus'">
          <a-tag :color="getSummaryStatusColor(text)">{{ text || '未总检' }}</a-tag>
        </template>
        <template v-if="column.dataIndex === 'gender'">
          <a-tag :color="text === '男' ? 'blue' : 'pink'">{{ text }}</a-tag>
        </template>
        <template v-if="column.dataIndex === 'age'">
          {{ text }}{{ record.ageUnit || '岁' }}
        </template>
      </template>
    </a-table>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, defineExpose, computed } from 'vue';
  import { message } from 'ant-design-vue';
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
  import type { ICustomerReg } from '#/types';

  const visible = ref<boolean>(false);
  const recordList = ref<ICustomerReg[]>([]);
  const selectedRecord = ref<ICustomerReg | null>(null);
  const selectedRowKeys = ref<string[]>([]);

  const emit = defineEmits(['select', 'cancel']);

  // 检查是否有非职业病体检记录
  const hasNonOccuRecords = computed(() => {
    return recordList.value.some(record => record.examCategory !== '职业病体检');
  });

  // 表格列定义
  const columns = [
    {
      title: '体检号',
      dataIndex: 'examNo',
      width: 120,
      fixed: 'left',
    },
    {
      title: '姓名',
      dataIndex: 'name',
      width: 100,
    },
    {
      title: '性别',
      dataIndex: 'gender',
      width: 60,
    },
    {
      title: '年龄',
      dataIndex: 'age',
      width: 80,
    },
    {
      title: '证件号',
      dataIndex: 'idCard',
      width: 150,
      ellipsis: true,
    },
    {
      title: '体检类型',
      dataIndex: 'examCategory',
      width: 120,
    },
    {
      title: '总检状态',
      dataIndex: 'summaryStatus',
      width: 100,
    },
    {
      title: '登记时间',
      dataIndex: 'regTime',
      width: 120,
    },
    {
      title: '单位名称',
      dataIndex: 'companyName',
      ellipsis: true,
    },
  ];

  // 行选择配置
  const rowSelection = computed(() => ({
    type: 'radio',
    selectedRowKeys: selectedRowKeys.value,
    onChange: (keys: string[], rows: ICustomerReg[]) => {
      selectedRowKeys.value = keys;
      selectedRecord.value = rows[0] || null;
    },
    onSelect: (record: ICustomerReg, selected: boolean) => {
      if (selected) {
        selectedRecord.value = record;
      }
    },
    getCheckboxProps: (record: ICustomerReg) => ({
      disabled: record.examCategory !== '职业病体检',
    }),
  }));

  // 获取体检类型颜色
  function getExamCategoryColor(category: string): string {
    const colorMap: Record<string, string> = {
      '职业病体检': 'orange',
      '健康体检': 'green',
      '入职体检': 'blue',
      '离职体检': 'purple',
      '年度体检': 'cyan',
    };
    return colorMap[category] || 'default';
  }

  // 获取总检状态颜色
  function getSummaryStatusColor(status: string): string {
    const colorMap: Record<string, string> = {
      '未总检': 'default',
      '初检完成': 'processing',
      '总检完成': 'success',
      '审核通过': 'success',
      '驳回': 'error',
    };
    return colorMap[status] || 'default';
  }

  // 获取表格行样式类名
  function getRowClassName(record: ICustomerReg): string {
    return record.examCategory !== '职业病体检' ? 'disabled-row' : '';
  }

  /**
   * 打开模态框
   * @param records 搜索结果记录列表
   */
  function open(records: ICustomerReg[]) {
    recordList.value = records || [];
    selectedRecord.value = null;
    selectedRowKeys.value = [];
    visible.value = true;
  }

  /**
   * 确定按钮点击事件
   */
  function handleOk() {
    if (!selectedRecord.value) {
      message.warning('请选择一条记录');
      return;
    }

    if (selectedRecord.value.examCategory !== '职业病体检') {
      message.warning('只能选择职业病体检记录');
      return;
    }

    emit('select', selectedRecord.value);
    handleCancel();
  }

  /**
   * 取消按钮点击事件
   */
  function handleCancel() {
    visible.value = false;
    recordList.value = [];
    selectedRecord.value = null;
    selectedRowKeys.value = [];
    emit('cancel');
  }

  defineExpose({
    open,
  });
</script>

<style scoped>
  :deep(.ant-table-tbody > tr:hover > td) {
    background-color: #f5f5f5 !important;
  }

  :deep(.ant-table-tbody > tr.ant-table-row-selected > td) {
    background-color: #e6f7ff !important;
  }

  /* 禁用行样式 */
  :deep(.ant-table-tbody > tr.disabled-row > td) {
    background-color: #f5f5f5 !important;
    color: #bfbfbf !important;
    cursor: not-allowed !important;
  }

  :deep(.ant-table-tbody > tr.disabled-row:hover > td) {
    background-color: #f5f5f5 !important;
  }

  :deep(.ant-table-tbody > tr.disabled-row .ant-radio-wrapper) {
    cursor: not-allowed !important;
  }
</style>
