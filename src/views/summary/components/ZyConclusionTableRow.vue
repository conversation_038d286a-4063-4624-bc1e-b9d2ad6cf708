<template>
  <tr
    :class="[
      'conclusion-row',
      {
        editing: record.editable,
        'main-factor': record.mainFlag === '1',
        'new-record': record.isNew,
        saving: saving,
      },
    ]"
  >
    <!-- 主要/次要列 -->
    <td class="text-center">
      <template v-if="record.editable">
        <a-radio-group v-model:value="record.mainFlag" button-style="solid" size="small">
          <a-radio-button value="1">主要</a-radio-button>
          <a-radio-button value="0">次要</a-radio-button>
        </a-radio-group>
      </template>
      <template v-else>
        <span :class="['simple-tag', record.mainFlag === '1' ? 'tag-primary' : 'tag-secondary']">
          {{ record.mainFlag === '1' ? '主要' : '次要' }}
        </span>
      </template>
    </td>

    <!-- 危害因素列 -->
    <td class="dropdown-cell">
      <template v-if="record.editable">
        <JDictSelectTag
          v-model:value="record.riskCode"
          dictCode="zy_risk_factor,name,code"
          placeholder="请选择结论"
          size="small"
          style="width: 100%"
          :class="{ 'required-field': !record.conclusion }"
          :getPopupContainer="getPopupContainer"
        />
      </template>
      <template v-else>
        <span class="field-text">{{ record.riskFactorText || record.riskFactor || '-' }}</span>
      </template>
    </td>

    <!-- 结论列 -->
    <td class="dropdown-cell">
      <template v-if="record.editable">
        <JDictSelectTag
          v-model:value="record.conclusion"
          dictCode="zy_conclusion_dict,dict_text,code"
          placeholder="请选择结论"
          size="small"
          style="width: 100%"
          :class="{ 'required-field': !record.conclusion }"
          :getPopupContainer="getPopupContainer"
        />
      </template>
      <template v-else>
        <span v-if="record.conclusion" class="simple-tag tag-success">
          {{ record.conclusionText || record.conclusion }}
        </span>
        <span v-else class="empty-field">-</span>
      </template>
    </td>

    <!-- 职业病列 -->
    <td class="dropdown-cell">
      <template v-if="record.editable">
        <JDictSelectTag
          v-model:value="record.zyDisease"
          dictCode="zy_disease_dict,dict_text,code"
          placeholder="请选择职业病"
          :triggerChange="false"
          size="small"
          style="width: 100%"
          :getPopupContainer="getPopupContainer"
        />
      </template>
      <template v-else>
        <div v-if="record.zyDisease" class="tag-container">
          <template v-if="record.zyDiseaseText">
            <span v-for="diseaseText in record.zyDiseaseText.split('，')" :key="diseaseText" class="simple-tag tag-warning">
              {{ diseaseText }}
            </span>
          </template>
          <template v-else>
            <span v-for="disease in (record.zyDisease || '').split(',')" :key="disease" class="simple-tag tag-warning">
              {{ disease }}
            </span>
          </template>
        </div>
        <span v-else class="empty-field">-</span>
      </template>
    </td>

    <!-- 禁忌证列 -->
    <td class="dropdown-cell">
      <template v-if="record.editable">
        <JDictSelectTag
          v-model:value="record.zySymptom"
          dictCode="zy_taboo_symptom,name,code"
          placeholder="请选择禁忌证"
          :triggerChange="false"
          size="small"
          style="width: 100%"
          :getPopupContainer="getPopupContainer"
        />
      </template>
      <template v-else>
        <div v-if="record.zySymptom" class="tag-container">
          <template v-if="record.zySymptomText">
            <span v-for="symptomText in record.zySymptomText.split('，')" :key="symptomText" class="simple-tag tag-info">
              {{ symptomText }}
            </span>
          </template>
          <template v-else>
            <span v-for="symptom in (record.zySymptom || '').split(',')" :key="symptom" class="simple-tag tag-info">
              {{ symptom }}
            </span>
          </template>
        </div>
        <span v-else class="empty-field">-</span>
      </template>
    </td>

    <!-- 结论依据列 -->
    <td class="dropdown-cell">
      <template v-if="record.editable">
        <JDictSelectTag
          v-model:value="record.according"
          dictCode="zy_conclusion_according,content,content"
          :autoSetDefault="true"
          defaultIndex="0"
          placeholder="请选择依据"
          size="small"
          style="width: 100%"
          :class="{ 'required-field': !record.according }"
          :getPopupContainer="getPopupContainer"
        />
      </template>
      <template v-else>
        <span class="field-text">{{ record.according || '-' }}</span>
      </template>
    </td>

    <!-- 处理意见列 -->
    <td>
      <template v-if="record.editable">
        <a-textarea v-model:value="record.advice" placeholder="请输入处理意见" :rows="2" size="small" style="width: 100%" />
      </template>
      <template v-else>
        <div class="advice-text" :title="record.advice">
          {{ record.advice || '-' }}
        </div>
      </template>
    </td>

    <!-- 操作列 -->
    <td class="text-center">
      <div class="action-buttons">
        <!-- 非编辑状态：显示编辑和删除按钮 -->
        <template v-if="!record.editable">
          <a-space size="small">
            <a-button type="primary" size="small" @click="$emit('edit', record)" :loading="loading">
              <template #icon><EditOutlined /></template>
              编辑
            </a-button>

            <a-popconfirm title="确定要删除这条记录吗？" ok-text="确定" cancel-text="取消" @confirm="$emit('delete', record)" placement="topLeft">
              <a-button type="primary" size="small" danger>
                <template #icon><DeleteOutlined /></template>
                删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>

        <!-- 编辑状态：显示保存和取消按钮 -->
        <template v-else>
          <a-space size="small">
            <a-button type="primary" size="small" @click="$emit('save', record)" :loading="saving">
              <template #icon><CheckOutlined /></template>
              保存
            </a-button>

            <a-button size="small" @click="$emit('cancel', record)" :disabled="saving">
              <template #icon><CloseOutlined /></template>
              取消
            </a-button>
          </a-space>
        </template>

        <!-- 保存状态指示器 -->
        <div v-if="saving" class="saving-indicator">
          <a-spin size="small" />
        </div>
      </div>
    </td>
  </tr>
</template>

<script lang="ts" setup>
  import { EditOutlined, CheckOutlined, CloseOutlined, DeleteOutlined } from '@ant-design/icons-vue';
  import { JAsyncSearchSelect } from '@/components/Form';
  import JDictSelectTag from '@/components/Form/src/jeecg/components/JDictSelectTag.vue';

  interface Props {
    record: any;
    index: number;
    selected?: boolean;
    loading?: boolean;
    saving?: boolean;
  }

  interface Emits {
    (e: 'edit', record: any): void;
    (e: 'save', record: any): void;
    (e: 'cancel', record: any): void;
    (e: 'delete', record: any): void;
    (e: 'select', record: any, checked: boolean): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    selected: false,
    loading: false,
    saving: false,
  });

  const emit = defineEmits<Emits>();

  // 获取下拉框容器，确保下拉框显示在正确位置
  const getPopupContainer = (triggerNode: HTMLElement) => {
    // 查找最近的表格容器
    let container = triggerNode.closest('.conclusion-table-container');
    if (!container) {
      container = triggerNode.closest('.table-wrapper');
    }
    if (!container) {
      container = document.body;
    }
    return container as HTMLElement;
  };
</script>

<style lang="less" scoped>
  .conclusion-row {
    position: relative;

    &:hover {
      background-color: #e9ecef !important;

      .action-buttons {
        opacity: 1;
      }
    }

    &.editing {
      background-color: #fff3cd !important;

      td {
        border-bottom-color: #ffeaa7;
      }
    }

    &.new-record {
      background-color: #e7f3ff !important;

      &.editing {
        background-color: #cce7ff !important;
      }
    }

    &.main-factor {
      border-left: 2px solid #dc3545;

      .row-index {
        color: #dc3545;
        font-weight: 500;
      }
    }

    &.saving {
      opacity: 0.7;
      pointer-events: none;
    }

    td {
      padding: 8px 6px;
      border-bottom: 1px solid #dee2e6;
      vertical-align: middle;
      font-size: 12px;

      &.text-center {
        text-align: center;
      }

      // 下拉框单元格特殊处理
      &.dropdown-cell {
        position: relative;
        z-index: 1;

        // 编辑状态时提升z-index
        .conclusion-row.editing & {
          z-index: 100;
        }
      }

      // 表单控件样式优化
      :deep(.ant-select),
      :deep(.ant-input),
      :deep(.ant-textarea) {
        font-size: 12px;
      }

      :deep(.ant-radio-group) {
        .ant-radio-button-wrapper {
          font-size: 11px;
          height: 24px;
          line-height: 22px;
          padding: 0 6px;
        }
      }
    }
  }

  .row-index {
    font-weight: 500;
    color: #666;
    font-size: 14px;
  }

  .status-indicators {
    margin-top: 4px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 2px;

    .ant-tag {
      margin: 0;
      font-size: 10px;
      padding: 0 4px;
      line-height: 16px;
    }
  }

  .field-text {
    color: #262626;
    font-size: 13px;
    line-height: 1.4;
  }

  .empty-field {
    color: #bfbfbf;
    font-style: italic;
    font-size: 12px;
  }

  .tag-container {
    display: flex;
    flex-wrap: wrap;
    gap: 2px;

    .simple-tag {
      margin: 0;
      font-size: 10px;
      padding: 0 4px;
      line-height: 16px;
      border-radius: 2px;
      border: 1px solid #dee2e6;
      background: #f8f9fa;
      color: #495057;
    }
  }

  .simple-tag {
    display: inline-block;
    font-size: 10px;
    padding: 2px 6px;
    line-height: 16px;
    border-radius: 2px;
    border: 1px solid #dee2e6;
    background: #f8f9fa;
    color: #495057;
    margin: 0 2px 2px 0;

    &.tag-primary {
      background: #dc3545;
      color: white;
      border-color: #dc3545;
    }

    &.tag-secondary {
      background: #6c757d;
      color: white;
      border-color: #6c757d;
    }

    &.tag-success {
      background: #28a745;
      color: white;
      border-color: #28a745;
    }

    &.tag-warning {
      background: #ffc107;
      color: #212529;
      border-color: #ffc107;
    }

    &.tag-info {
      background: #17a2b8;
      color: white;
      border-color: #17a2b8;
    }
  }

  .advice-text {
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 12px;
    color: #495057;
    line-height: 1.4;
    cursor: help;

    &:hover {
      color: #007bff;
    }
  }

  .action-buttons {
    position: relative;
    opacity: 0.9;

    .ant-btn {
      font-size: 11px;
      height: 24px;
      padding: 0 6px;
      border-radius: 2px;

      &.ant-btn-sm {
        height: 20px;
        padding: 0 4px;
        font-size: 10px;
      }
    }

    .saving-indicator {
      position: absolute;
      top: -20px;
      right: 0;
      font-size: 10px;
      color: #007bff;
      background: white;
      padding: 1px 4px;
      border-radius: 2px;
      border: 1px solid #dee2e6;
    }
  }

  .required-field {
    :deep(.ant-select-selector) {
      border-color: #ff4d4f !important;
      box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
    }

    :deep(.ant-select-focused .ant-select-selector) {
      border-color: #ff4d4f !important;
      box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .conclusion-row td {
      padding: 8px 4px;
      font-size: 12px;
    }

    .advice-text {
      max-width: 120px;
    }

    .action-buttons .ant-btn {
      font-size: 11px;
      height: 24px;
      padding: 0 4px;
    }
  }

  @media (max-width: 768px) {
    .conclusion-row td {
      padding: 6px 2px;
      font-size: 11px;
    }

    .tag-container .ant-tag {
      font-size: 10px;
      padding: 0 4px;
      line-height: 16px;
    }

    .status-indicators .ant-tag {
      font-size: 9px;
      padding: 0 3px;
      line-height: 14px;
    }
  }
</style>
