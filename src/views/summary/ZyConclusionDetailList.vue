<template>
  <div class="p-2">
    <!--危害因素自动管理提示-->
    <a-alert
      v-if="riskFactorList.length > 0"
      :message="`检测到 ${riskFactorList.length} 个危害因素，系统将自动为每个因素创建对应的结论行`"
      type="info"
      show-icon
      closable
      class="mb-4"
    >
      <template #action>
        <a-button size="small" type="primary" @click="handleManualCreateCards" :disabled="loading"> 手动创建行</a-button>
      </template>
    </a-alert>

    <!--工具栏-->
    <div class="toolbar-container">
      <a-space size="small">
        <!-- 新增按钮 -->
        <a-button type="primary" size="small" @click="handleAdd" :disabled="loading">
          <template #icon>
            <PlusOutlined />
          </template>
          新增结论
        </a-button>

        <!-- 批量保存按钮 -->
        <a-button v-if="hasUnsavedChanges" type="primary" ghost size="small" @click="saveAllEditing" :disabled="loading">
          <template #icon>
            <CheckOutlined />
          </template>
          保存全部 ({{ editingRows.size }})
        </a-button>

        <!-- 取消全部编辑按钮 -->
        <a-button v-if="hasUnsavedChanges" size="small" @click="cancelAllEditing" :disabled="loading"> 取消全部</a-button>

        <!-- 批量删除按钮 -->
        <a-dropdown v-if="selectedRowKeys.length > 0 && !hasUnsavedChanges" :disabled="loading">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete"> 删除选中项</a-menu-item>
            </a-menu>
          </template>
          <a-button size="small" :disabled="loading">
            批量操作 ({{ selectedRowKeys.length }})
            <template #icon>
              <PlusOutlined />
            </template>
          </a-button>
        </a-dropdown>
      </a-space>

      <!-- 状态提示 -->
      <div v-if="hasUnsavedChanges" class="status-alert">
        <a-alert
          :message="`有 ${editingRows.size} 条记录正在编辑中，请保存或取消后再进行其他操作`"
          type="warning"
          size="small"
          show-icon
          closable
          @close="cancelAllEditing"
        />
      </div>
    </div>

    <!--表格列表-->
    <div class="conclusion-table-container">
      <!-- 数据加载状态 -->
      <a-spin :spinning="loading" tip="正在加载数据...">
        <!-- 批量选择工具栏 -->
        <div v-if="selectedRowKeys.length > 0 && !hasUnsavedChanges" class="batch-selection-bar">
          <a-space>
            <span>已选择 {{ selectedRowKeys.length }} 项</span>
            <a-button size="small" @click="selectedRowKeys = []">取消选择</a-button>
            <a-button size="small" danger @click="batchHandleDelete">批量删除</a-button>
          </a-space>
        </div>

        <!-- 表格 -->
        <div class="table-wrapper">
          <table class="conclusion-table">
            <!-- 表头 -->
            <thead>
              <tr>
                <th width="100">主/次</th>
                <th width="150">危害因素</th>
                <th width="120">结论 <span class="required">*</span></th>
                <th width="150">职业病</th>
                <th width="150">禁忌证</th>
                <th width="120">结论依据 <span class="required">*</span></th>
                <th width="200">处理意见</th>
                <th width="140">操作</th>
              </tr>
            </thead>
            <!-- 表体 -->
            <tbody>
              <ZyConclusionTableRow
                v-for="(record, index) in dataSource"
                :key="record.uuid"
                :record="record"
                :index="index"
                :selected="selectedRowKeys.includes(record.id)"
                :loading="savingRows.has(record.uuid)"
                :saving="savingRows.has(record.uuid)"
                @edit="handleEditRow"
                @save="handleSaveRow"
                @cancel="handleCancelRow"
                @delete="handleDeleteRow"
                @select="handleRowSelect"
              />
            </tbody>
          </table>
        </div>

        <!-- 空状态 -->
        <a-empty v-if="!loading && (!dataSource || dataSource.length === 0)" description="暂无数据" :image="Empty.PRESENTED_IMAGE_SIMPLE">
          <a-button type="primary" @click="handleAdd">
            <template #icon>
              <PlusOutlined />
            </template>
            新增结论
          </a-button>
        </a-empty>

        <!-- 加载更多 -->
        <div v-if="dataSource && dataSource.length > 0" class="load-more-container">
          <a-button v-if="hasMore" @click="loadMore" :loading="loadingMore" block type="dashed"> 加载更多</a-button>
          <div v-else class="no-more-data">
            <a-divider>
              <span style="color: #8c8c8c; font-size: 12px">已显示全部数据</span>
            </a-divider>
          </div>
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script lang="ts" name="zy-conclusion-detail-inline-edit" setup>
  import { computed, inject, reactive, ref, watch, onMounted, onUnmounted } from 'vue';
  import { batchDelete, deleteOne, list, saveOrUpdate } from './ZyConclusionDetail.api';
  import { CheckOutlined, PlusOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { message, Empty } from 'ant-design-vue';
  import { buildUUID } from '/@/utils/uuid';

  // 导入表格行组件
  import ZyConclusionTableRow from './components/ZyConclusionTableRow.vue';

  const customerReg4Summary = inject('customerReg4Summary');
  const customerSummary = inject('customerSummary');
  const riskFactorList = computed(() => {
    return customerReg4Summary.value?.riskFactorList || [];
  });
  const { createErrorModal } = useMessage();

  // 编辑状态管理
  const editingRows = ref<Set<string>>(new Set());
  const savingRows = ref<Set<string>>(new Set());
  const hasUnsavedChanges = computed(() => editingRows.value.size > 0);

  const queryParam = reactive<any>({});

  // 数据管理
  const dataSource = ref<any[]>([]);
  const loading = ref(false);
  const loadingMore = ref(false);
  const hasMore = ref(true);
  const selectedRowKeys = ref<string[]>([]);

  // 加载数据
  const loadData = async (append = false) => {
    if (!customerReg4Summary.value?.id) {
      dataSource.value = [];
      return;
    }

    try {
      if (!append) {
        loading.value = true;
      } else {
        loadingMore.value = true;
      }
      const params = {
        customerRegId: customerReg4Summary.value.id,
        ...queryParam,
      };

      // 1. 先获取已保存的记录
      const result = await list(params);
      console.log('==========已保存的记录==============', result);
      const savedRecords = (result.records || []).map((record) => {
        console.log('加载的记录包含Text字段:', {
          id: record.id,
          riskFactorText: record.riskFactorText,
          conclusionText: record.conclusionText,
          zyDiseaseText: record.zyDiseaseText,
          zySymptomText: record.zySymptomText,
        });
        return {
          ...record,
          uuid: record.uuid || buildUUID(), // 为每条记录确保有UUID
        };
      });

      if (append) {
        // 加载更多时，直接追加
        dataSource.value = [...dataSource.value, ...savedRecords];
      } else {
        // 2. 保留当前正在编辑且对应危害因素仍存在的未保存记录
        const validUnsavedRecords = preserveValidUnsavedRecords();

        // 3. 合并已保存记录和有效的未保存记录
        dataSource.value = [...savedRecords, ...validUnsavedRecords];

        // 4. 自动为缺失的危害因素创建对应的行
        console.log('📋 开始补充缺失的危害因素行');
        await autoCreateRiskFactorCards();
      }

      // 检查是否还有更多数据
      hasMore.value = savedRecords.length > 0 && savedRecords.length >= 10;
    } catch (error) {
      console.error('加载数据失败:', error);
      message.error('加载数据失败');
    } finally {
      loading.value = false;
      loadingMore.value = false;
    }
  };

  // 加载更多数据
  const loadMore = () => {
    loadData(true);
  };

  // 手动创建危害因素行
  const handleManualCreateCards = async () => {
    console.log('🔧 手动触发创建危害因素行');
    await autoCreateRiskFactorCards();
  };

  // 保留有效的未保存记录
  const preserveValidUnsavedRecords = () => {
    console.log('🔍 开始检查需要保留的未保存记录');

    if (!dataSource.value || dataSource.value.length === 0) {
      console.log('ℹ️ 当前没有数据，无需保留');
      return [];
    }

    // 获取当前危害因素ID列表
    const currentRiskFactorIds = new Set(riskFactorList.value?.map((risk) => risk.id) || []);
    console.log('当前危害因素ID列表:', Array.from(currentRiskFactorIds));

    // 找出需要保留的未保存记录
    const validUnsavedRecords = dataSource.value.filter((record) => {
      // 只保留未保存的记录
      if (!record.isNew) {
        return false;
      }

      // 手动创建的记录总是保留（用户可能正在编辑自定义结论）
      if (record.manualCreated) {
        console.log(`✅ 保留手动创建的记录:`, record);
        return true;
      }

      // 自动创建的记录，只有当对应的危害因素仍然存在时才保留
      if (record.autoCreated && record.riskFactorId) {
        const shouldKeep = currentRiskFactorIds.has(record.riskFactorId);
        if (shouldKeep) {
          console.log(`✅ 保留自动创建的记录 (危害因素${record.riskFactorId}仍存在):`, record);
        } else {
          console.log(`❌ 移除自动创建的记录 (危害因素${record.riskFactorId}已不存在):`, record);
          // 从编辑状态中移除
          editingRows.value.delete(record.uuid);
        }
        return shouldKeep;
      }

      return false;
    });

    console.log(`📋 保留了 ${validUnsavedRecords.length} 个有效的未保存记录`);
    return validUnsavedRecords;
  };

  // 清理无效的未保存行（当客户切换时使用）
  const cleanupInvalidUnsavedCards = () => {
    console.log('🧹 开始清理无效的未保存行');

    // 找出所有未保存的行
    const unsavedCards = dataSource.value.filter((item) => item.isNew);

    if (unsavedCards.length > 0) {
      console.log(`发现 ${unsavedCards.length} 个未保存的行，准备清理:`, unsavedCards);

      // 从数据源中移除这些行
      dataSource.value = dataSource.value.filter((item) => !item.isNew);

      // 从编辑状态中移除这些行
      unsavedCards.forEach((card) => {
        editingRows.value.delete(card.uuid);
      });

      console.log('✅ 未保存的行已清理完成');
    } else {
      console.log('ℹ️ 没有发现未保存的行');
    }
  };

  // 自动为危害因素创建对应的行
  const autoCreateRiskFactorCards = async () => {
    console.log('🔍 开始补充缺失的危害因素行');
    console.log('危害因素列表:', riskFactorList.value);
    console.log('当前数据源:', dataSource.value);

    if (!riskFactorList.value || riskFactorList.value.length === 0) {
      console.log('❌ 没有危害因素，跳过自动创建');
      return;
    }

    // 获取当前所有记录中已存在的危害因素ID（包括已保存的和未保存的）
    const existingRiskFactorIds = new Set(dataSource.value.map((item) => item.riskFactorId).filter((id) => id !== null && id !== undefined));
    console.log('已存在的危害因素ID:', Array.from(existingRiskFactorIds));

    // 找出需要补充的危害因素
    const missingRiskFactors = riskFactorList.value.filter((risk) => !existingRiskFactorIds.has(risk.id));
    console.log('需要补充的危害因素:', missingRiskFactors);

    if (missingRiskFactors.length === 0) {
      console.log('ℹ️ 所有危害因素都已有对应行，无需创建新行');
      return;
    }

    // 为缺失的危害因素创建行
    const newCards = [];
    missingRiskFactors.forEach((risk, index) => {
      console.log(`✅ 为危害因素 ${risk.name} 创建新行`);
      const uuid = buildUUID();
      const newRecord = {
        // 不设置id字段，让后端生成
        uuid: uuid, // 使用UUID作为唯一标识
        customerRegId: customerReg4Summary.value?.id, // 设置登记ID
        summaryId: customerSummary.value?.id, // 设置总检ID
        riskFactorId: risk.id,
        riskCode: risk.code,
        riskFactor: risk.name,
        // 根据危害因素在原列表中的位置决定主要/次要
        mainFlag: riskFactorList.value.findIndex((r) => r.id === risk.id) === 0 ? '1' : '0',
        workType: '',
        conclusion: '',
        advice: '',
        zyDisease: '',
        zySymptom: '',
        according: '',
        editable: true, // 默认进入编辑状态
        isNew: true,
        autoCreated: true, // 标记为自动创建
      };
      newCards.push(newRecord);
    });

    console.log(`准备创建 ${newCards.length} 个新行:`, newCards);

    // 将新行添加到数据源
    dataSource.value = [...dataSource.value, ...newCards];
    console.log('✅ 新行已添加到数据源');

    // 将所有新创建的行都标记为编辑状态
    newCards.forEach((card) => {
      editingRows.value.add(card.uuid);
    });

    // 显示提示信息
    if (newCards.length > 0) {
      //message.success(`已自动为 ${newCards.length} 个危害因素创建结论行，请完善相关信息后保存`);
      //console.log('🔧 所有新创建的行已进入编辑状态，编辑中的行:', Array.from(editingRows.value));
    }
  };

  /**
   * 新增事件 - 创建自定义结论行
   */
  function handleAdd() {
    const uuid = buildUUID();
    const newRecord = {
      // 不设置id字段，让后端生成
      uuid: uuid, // 使用UUID作为唯一标识
      customerRegId: customerReg4Summary.value?.id, // 设置登记ID
      summaryId: customerSummary.value?.id, // 设置总检ID
      riskFactorId: null, // 手动创建的卡片不绑定特定危害因素
      riskCode: null,
      riskFactor: '', // 用户可以手动选择或输入
      mainFlag: '1',
      workType: '',
      conclusion: '',
      advice: '',
      zyDisease: '',
      zySymptom: '',
      according: '',
      editable: true,
      isNew: true,
      manualCreated: true, // 标记为手动创建
    };

    dataSource.value.unshift(newRecord);

    // 标记为编辑状态
    editingRows.value.add(newRecord.uuid);
  }

  /**
   * 编辑行
   */
  function handleEditRow(record: any) {
    record.editable = true;
    editingRows.value.add(record.uuid);
  }

  /**
   * 保存行
   */
  async function handleSaveRow(record: any) {
    try {
      savingRows.value.add(record.uuid);

      // 数据验证
      if (!record.conclusion) {
        message.error('请选择职业检结论');
        return false;
      }

      if (!record.according) {
        message.error('请选择结论依据');
        return false;
      }

      // 处理意见改为非必填，移除验证

      // 准备保存数据
      const saveData = { ...record };
      delete saveData.editable;
      delete saveData.isNew;
      delete saveData.onEdit;
      delete saveData.uuid; // 不发送UUID到后端
      delete saveData.autoCreated; // 删除临时标记
      delete saveData.manualCreated; // 删除临时标记

      // 确保必要的关联字段存在
      if (!saveData.customerRegId && customerReg4Summary.value?.id) {
        saveData.customerRegId = customerReg4Summary.value.id;
      }
      if (!saveData.summaryId && customerSummary.value?.id) {
        saveData.summaryId = customerSummary.value.id;
      }

      console.log('准备保存的数据:', saveData);

      // 调用保存接口
      const isUpdate = !record.isNew && record.id; // 有ID且不是新记录就是更新
      const result = await saveOrUpdate(saveData, isUpdate);

      if (result.success) {
        // 保存成功，用后端返回的完整对象更新前端记录
        const savedData = result.result;
        console.log('后端返回的完整数据:', savedData);

        if (savedData) {
          // 保留前端的UUID和编辑状态相关字段
          const frontendFields = {
            uuid: record.uuid,
            editable: false,
            isNew: false,
          };

          // 用后端返回的数据更新记录，包括xxxText字段
          Object.assign(record, savedData, frontendFields);

          console.log('更新后的记录:', record);
        } else {
          // 如果后端没有返回完整数据，按原逻辑处理
          if (record.isNew) {
            const newId = result.result?.id || result.result;
            if (newId) {
              record.id = newId;
            }
            record.isNew = false;
          }
          record.editable = false;
        }

        // 退出编辑状态
        editingRows.value.delete(record.uuid);

        // 清理临时标记
        delete record.manualCreated;
        delete record.autoCreated;

        message.success('保存成功');
        return true;
      } else {
        message.error(result.message || '保存失败');
        return false;
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
      return false;
    } finally {
      savingRows.value.delete(record.uuid);
    }
  }

  /**
   * 取消编辑行
   */
  function handleCancelRow(record: any) {
    if (record.isNew) {
      // 删除新增的临时记录
      const index = dataSource.value.findIndex((item) => item.uuid === record.uuid);
      if (index > -1) {
        dataSource.value.splice(index, 1);
      }
    } else {
      // 恢复原始数据
      record.editable = false;
    }

    editingRows.value.delete(record.uuid);
  }

  /**
   * 删除行
   */
  async function handleDeleteRow(record: any) {
    try {
      if (record.isNew || !record.id) {
        // 删除临时记录（未保存的记录）
        const index = dataSource.value.findIndex((item) => item.uuid === record.uuid);
        if (index > -1) {
          dataSource.value.splice(index, 1);
        }
        editingRows.value.delete(record.uuid);
        message.success('删除成功');
      } else {
        // 删除已保存的记录
        await deleteOne({ id: record.id }, () => {
          // 删除成功的回调
          const index = dataSource.value.findIndex((item) => item.uuid === record.uuid);
          if (index > -1) {
            dataSource.value.splice(index, 1);
          }
          editingRows.value.delete(record.uuid);
          message.success('删除成功');
        });
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  }

  /**
   * 批量删除事件
   */
  function batchHandleDelete() {
    try {
      batchDelete({ ids: selectedRowKeys.value }, () => {
        // 批量删除成功的回调
        selectedRowKeys.value.forEach((id) => {
          const record = dataSource.value.find((item) => item.id === id);
          if (record) {
            const index = dataSource.value.findIndex((item) => item.id === id);
            if (index > -1) {
              dataSource.value.splice(index, 1);
            }
            // 使用UUID从编辑状态中移除
            editingRows.value.delete(record.uuid);
          }
        });
        selectedRowKeys.value = [];
        message.success('批量删除成功');
      });
    } catch (error) {
      console.error('批量删除失败:', error);
      message.error('批量删除失败');
    }
  }

  /**
   * 刷新数据并清理无效记录
   * @param forceCleanup 是否强制清理未保存的行，默认为false
   */
  const refreshDataAndCleanup = async (forceCleanup = false) => {
    console.log('🔄 刷新数据并清理无效记录', { forceCleanup });

    // 如果强制清理或者没有客户ID，清理所有未保存的行
    if (forceCleanup || !customerReg4Summary.value?.id) {
      cleanupInvalidUnsavedCards();
    }

    // 重新加载数据
    if (customerReg4Summary.value?.id) {
      await loadData();
    } else {
      dataSource.value = [];
    }
  };

  /**
   * 处理客户切换逻辑
   * @param newValue 新的客户登记ID
   * @param oldValue 旧的客户登记ID
   */
  const handleCustomerChange = (newValue?: string, oldValue?: string) => {
    console.log('👀 客户登记ID变化处理', { newValue, oldValue });

    // 如果是切换到不同的客户，强制清理所有未保存的行
    const shouldForceCleanup = oldValue && newValue && oldValue !== newValue;

    refreshDataAndCleanup(shouldForceCleanup);
  };

  /**
   * 批量保存所有编辑中的行
   * @returns {Promise<boolean>} 返回保存是否完全成功
   */
  async function saveAllEditing() {
    const editingRowUuids = Array.from(editingRows.value);
    if (editingRowUuids.length === 0) {
      message.info('没有需要保存的数据');
      return true; // 没有数据需要保存，视为成功
    }

    let successCount = 0;
    let failCount = 0;

    // 创建一个副本，避免在遍历过程中修改原数组
    const editingRowUuidsCopy = [...editingRowUuids];

    for (const uuid of editingRowUuidsCopy) {
      const record = dataSource.value.find((item) => item.uuid === uuid);
      if (record) {
        console.log(`正在保存记录: ${record.uuid}`, record);
        const success = await handleSaveRow(record);
        if (success) {
          successCount++;
        } else {
          failCount++;
        }
      } else {
        console.warn(`未找到UUID为 ${uuid} 的记录`);
        failCount++;
      }
    }

    const allSuccess = failCount === 0;

    if (allSuccess) {
      //message.success(`批量保存成功，共保存 ${successCount} 条记录`);
    } else {
      message.warning(`批量保存完成，成功 ${successCount} 条，失败 ${failCount} 条`);
    }

    return allSuccess;
  }

  /**
   * 取消所有编辑
   */
  function cancelAllEditing() {
    const editingRowUuids = Array.from(editingRows.value);

    // 创建副本避免在遍历过程中修改原数组
    editingRowUuids.forEach((uuid) => {
      const record = dataSource.value.find((item) => item.uuid === uuid);
      if (record) {
        handleCancelRow(record);
      }
    });

    message.info('已取消所有编辑');
  }

  /**
   * 检查指定行是否正在保存
   */
  const isRowSaving = (recordId: string): boolean => {
    // 对于新记录，recordId可能为undefined，需要通过uuid查找
    if (!recordId) {
      return false;
    }
    // 先尝试通过ID查找
    const recordById = dataSource.value.find((item) => item.id === recordId);
    if (recordById) {
      return savingRows.value.has(recordById.uuid);
    }
    // 如果没找到，可能是传入的就是uuid
    return savingRows.value.has(recordId);
  };

  /**
   * 处理行选择
   */
  function handleRowSelect(record: any, checked: boolean) {
    if (checked) {
      if (!selectedRowKeys.value.includes(record.id)) {
        selectedRowKeys.value.push(record.id);
      }
    } else {
      const index = selectedRowKeys.value.indexOf(record.id);
      if (index > -1) {
        selectedRowKeys.value.splice(index, 1);
      }
    }
  }

  // 键盘快捷键支持
  const handleKeydown = (event: KeyboardEvent) => {
    if (event.ctrlKey && event.key === 's') {
      event.preventDefault();
      if (hasUnsavedChanges.value) {
        saveAllEditing();
      }
    } else if (event.key === 'Escape') {
      if (hasUnsavedChanges.value) {
        cancelAllEditing();
      }
    }
  };

  onMounted(() => {
    document.addEventListener('keydown', handleKeydown);
  });

  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown);
  });

  watch(
    () => customerReg4Summary.value?.id,
    (newValue, oldValue) => {
      handleCustomerChange(newValue, oldValue);
    }
  );

  // 暴露方法给父组件
  defineExpose({
    handleCustomerChange,
    saveAllEditing,
    refreshDataAndCleanup,
  });
</script>

<style lang="less" scoped>
  .conclusion-table-container {
    .batch-selection-bar {
      background: #f8f9fa;
      padding: 8px 12px;
      border-radius: 2px;
      margin-bottom: 12px;
      border: 1px solid #e9ecef;
    }

    .table-wrapper {
      border: 1px solid #e9ecef;
      border-radius: 2px;
      overflow: visible;
      margin-bottom: 16px;
      position: relative;

      // 为下拉框预留空间
      &::after {
        content: '';
        display: block;
        height: 200px;
        width: 100%;
        position: absolute;
        bottom: -200px;
        left: 0;
        pointer-events: none;
        z-index: -1;
      }
    }

    .conclusion-table {
      width: 100%;
      border-collapse: collapse;
      background: white;

      thead {
        background: #f8f9fa;
        position: sticky;
        top: 0;
        z-index: 5;

        th {
          padding: 10px 6px;
          text-align: center;
          font-weight: 500;
          color: #495057;
          border-bottom: 1px solid #dee2e6;
          border-right: 1px solid #dee2e6;
          font-size: 12px;
          white-space: nowrap;
          position: relative;

          &:last-child {
            border-right: none;
          }

          .required {
            color: #dc3545;
            font-weight: 500;
            font-size: 12px;
          }

          // 添加排序指示器样式
          &.sortable {
            cursor: pointer;
            user-select: none;

            &::after {
              content: '⇅';
              position: absolute;
              right: 4px;
              top: 50%;
              transform: translateY(-50%);
              opacity: 0.4;
              font-size: 10px;
            }
          }
        }
      }

      tbody {
        tr {
          &:nth-child(even) {
            background-color: #f8f9fa;
          }

          &:hover {
            background-color: #e9ecef !important;
          }

          td {
            border-right: 1px solid #dee2e6;
            border-bottom: 1px solid #dee2e6;

            &:last-child {
              border-right: none;
            }
          }

          // 编辑状态的行样式
          &.editing {
            background-color: #fff3cd !important;

            td {
              border-right-color: #ffeaa7;
              border-bottom-color: #ffeaa7;
            }
          }
        }
      }
    }

    .load-more-container {
      text-align: center;
      margin-top: 24px;

      .no-more-data {
        margin: 20px 0;
      }
    }
  }

  // 工具栏样式
  .toolbar-container {
    margin-bottom: 12px;
    padding: 8px 0;

    .ant-space {
      width: 100%;
      justify-content: flex-start;
    }

    .status-alert {
      margin-top: 8px;

      .ant-alert {
        border-radius: 4px;
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .conclusion-table-container {
      .table-wrapper {
        overflow-x: auto;
      }

      .conclusion-table {
        min-width: 1000px;
      }
    }
  }

  @media (max-width: 768px) {
    .toolbar-container {
      padding: 6px 0;

      .ant-space {
        flex-direction: column;
        align-items: stretch;
        gap: 6px !important;
      }

      .status-alert {
        margin-top: 6px;
      }
    }

    .conclusion-table-container {
      .conclusion-table {
        min-width: 800px;

        th,
        td {
          padding: 8px 4px;
          font-size: 12px;
        }
      }
    }
  }

  /* 全局下拉框样式，确保下拉框显示正确 */
  :deep(.ant-select-dropdown),
  :deep(.ant-cascader-dropdown),
  :deep(.ant-picker-dropdown) {
    z-index: 1050 !important; /* 确保下拉框在最上层 */
  }

  /* 编辑行的下拉框特殊处理 */
  :deep(.conclusion-row.editing) {
    .ant-select-dropdown,
    .ant-cascader-dropdown,
    .ant-picker-dropdown {
      z-index: 1100 !important;
    }
  }
</style>
