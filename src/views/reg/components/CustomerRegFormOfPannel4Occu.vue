<template>
  <a-spin :spinning="confirmLoading">
    <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol">
      <a-row justify="space-between" :gutter="0">
        <a-col :span="5">
          <div style="width: 100px; height: 140px; display: flex; flex-direction: column; align-items: center">
            <a-image
              :height="80"
              :src="formData.customerAvatar ? getFileAccessHttpUrl(formData.customerAvatar) : defaultAvatar"
              :fallback="defaultAvatar"
            />
            <a-button size="middle" type="dashed" @click="takePhone" danger style="margin-top: 4px">拍照 </a-button>
          </div>
        </a-col>
        <a-col :span="19">
          <a-descriptions size="small" :column="2">
            <!--            <template #title> <a-typography-title :level="5">概览</a-typography-title> </template>-->
            <a-descriptions-item label="体检类型">
              <a-typography-text strong>{{ formData.examCategory ? formData.examCategory : '' }}</a-typography-text>
            </a-descriptions-item>
            <a-descriptions-item label="支付状态">
              <a-tag color="success" v-if="formData.paymentState == '已支付'">已支付</a-tag>
              <a-tag color="error" v-else>{{ formData.paymentState || '未知' }}</a-tag>
              <!--              <a-typography-text strong>{{ formModel.paymentState ? formModel.paymentState : '未支付' }}</a-typography-text>-->
            </a-descriptions-item>
            <a-descriptions-item label="登记状态">
              <a-tag color="success" v-if="formData.status == '已登记'">已登记</a-tag>
              <a-tag color="error" v-else>{{ formData.status || '未知' }}</a-tag>
              <!--              <a-typography-text strong>{{ formModel.status ? formModel.status : '未登记' }}</a-typography-text>-->
            </a-descriptions-item>
            <a-descriptions-item label="检查状态">
              <a-tag color="success" v-if="formData.checkState == '已检查'">已检查</a-tag>
              <a-tag color="error" v-else>{{ formData.checkState || '未知' }}</a-tag>
              <template v-if="formData.retrieveStatus">
                <a-tag color="success" v-if="formData.retrieveStatus == '1'">已交表</a-tag>
                <a-tag color="error" v-else>未交表</a-tag>
              </template>
              <!--              <a-typography-text strong>{{ formModel.checkState ? formModel.checkState : '未检查' }}</a-typography-text>
              <a-typography-text style="margin-left: 5px">{{ formModel.retrieveStatus == '1' ? '已交表' : '未交表' }}</a-typography-text>-->
            </a-descriptions-item>
            <a-descriptions-item label="总检状态">
              <a-tag color="success" v-if="formData.summaryStatus == '审核通过'">审核通过</a-tag>
              <a-tag color="error" v-else>{{ formData.summaryStatus || '未知' }}</a-tag>
              <!--              <a-typography-text strong>{{ formModel.status ? formModel.status : '未登记' }}</a-typography-text>-->
            </a-descriptions-item>
            <!--            <a-descriptions-item label="登记号">
              {{ formModel.customerRegNum ? formModel.customerRegNum : '待生成' }}
            </a-descriptions-item>-->
            <a-descriptions-item label="体检号">{{ formData.examNo ? formData.examNo : '' }}</a-descriptions-item>
            <a-descriptions-item label="预约时间">
              {{ formData.bookingDate ? formData.bookingDate : '未预约' }}
            </a-descriptions-item>
            <a-descriptions-item label="预约员工">{{ formData.creatorBy_dictText ? formData.creatorBy_dictText : '' }} </a-descriptions-item>
            <a-descriptions-item label="登记时间">{{ formData.regTime ? formData.regTime : '' }}</a-descriptions-item>
            <a-descriptions-item label="所属单位" :span="2">
              {{ formData.companyName ? formData.companyName : '' }}
            </a-descriptions-item>
            <a-descriptions-item label="所属预约" :span="2">
              <a-flex justify="between">
                <span>{{ formData.companyRegName ? formData.companyRegName + formData.teamName : '未预约' }}</span>
                <a style="margin-left: 5px" @click="showCompanyTeam" v-if="formData.companyRegName">详情</a>
              </a-flex>
            </a-descriptions-item>
          </a-descriptions>
        </a-col>
      </a-row>
      <!--      <a-divider orientation="left"><a-typography-title :level="5">档案信息</a-typography-title></a-divider>-->
      <!--      <a-row>
        <a-col :span="12">
          <a-form-item label="HIS编码" v-bind="validateInfos.hisPid">
            <a-input v-model:value="formModel.hisPid" placeholder="请输入HIS编码" :disabled="disabled" size="middle" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="住院号" v-bind="validateInfos.hisInpatientId">
            <a-input v-model:value="formModel.hisInpatientId" placeholder="请输入住院号" :disabled="disabled" size="middle" />
          </a-form-item>
        </a-col>
      </a-row>-->
      <div class="row-title">
        <span>基本信息+职业检信息</span>
        <a-space>
          <a-button size="middle" type="primary" @click="submitForm"><SaveOutlined />保存</a-button>
        </a-space>
      </div>
      <a-row>
        <a-col :span="12">
          <a-form-item label="预约日期" v-bind="validateInfos.appointmentDate">
            <a-date-picker
              placeholder="请选择预约日期"
              v-model:value="formData.appointmentDate"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              :disabled="disabled"
              size="middle"
            />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="姓名" v-bind="validateInfos.name">
            <a-input v-model:value="formData.name" placeholder="请输入姓名" :disabled="disabled" size="middle" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="证件类型" v-bind="validateInfos.cardType">
            <j-dict-select-tag
              v-model:value="formData.cardType"
              dictCode="idcard_type"
              placeholder="请选择证件类型"
              :disabled="disabled"
              :useDicColor="true"
              size="middle"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="证件号" v-bind="validateInfos.idCard">
            <a-input
              v-model:value="formData.idCard"
              :placeholder="idcDataSource.action == 'addReg' ? idcData.msg : '请输入证件号'"
              :disabled="disabled"
              size="middle"
              @change="handleIdCard"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="性别" v-bind="validateInfos.gender">
            <j-dict-select-tag
              v-model:value="formData.gender"
              :useDicColor="true"
              dictCode="sex"
              placeholder="请选择性别"
              :disabled="disabled"
              size="middle"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="出生日期" v-bind="validateInfos.birthday">
            <a-date-picker
              placeholder="请选择出生日期"
              v-model:value="formData.birthday"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              :disabled="disabled"
              size="middle"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="电话" v-bind="validateInfos.phone">
            <a-input v-model:value="formData.phone" placeholder="请输入电话" :disabled="disabled" size="middle" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="婚姻状况" v-bind="validateInfos.marriageStatus">
            <j-dict-select-tag
              :useDicColor="true"
              v-model:value="formData.marriageStatus"
              dictCode="material_type"
              placeholder="请选择婚姻状况"
              :disabled="disabled"
              size="middle"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="单位名称" v-bind="validateInfos.companyName">
            <CompanyAutoComplete
              v-model:value="formData.companyName"
              placeholder="请选择单位"
              :disabled="disabled"
              @select="handleCompanySelect"
              @change="handleCompanyAutoCompleteChange"
            />
          </a-form-item>
        </a-col>
        <template v-if="hasPermission('reg:selectCompanyTeam')">
          <a-col :lg="12">
            <a-form-item name="companyRegId" label="所属预约">
              <j-async-search-select
                size="middle"
                placeholder="所属预约"
                @change="getTeamList"
                v-model:value="formData.companyRegId"
                :api="searchCompanyReg"
                :field-mapping="{
                  value: 'id',
                  text: 'regName',
                }"
              />
            </a-form-item>
          </a-col>
          <a-col :lg="12">
            <a-form-item name="teamId" label="所属分组">
              <a-select size="middle" placeholder="所属分组" v-model:value="formData.teamId" :allow-clear="true">
                <a-select-option :value="item.id" v-for="item in teamList">{{ item.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </template>
      </a-row>

      <a-row>
        <a-col :span="24">
          <a-form-item label="危害因素" v-bind="validateInfos.riskFactor" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
            <ZyRiskFactorAutoComplete
              v-model:value="formData.riskFactorName"
              :config="{ allowMultiple: true, separator: ' ' }"
              @change="handleRiskFactorChange"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="岗位类别" v-bind="validateInfos.jobStatus">
            <j-dict-select-tag v-model:value="formData.jobStatus" dictCode="job_status" placeholder="请选择岗位类别" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="行业" v-bind="validateInfos.industry">
            <zy-industry-select v-model:value="formData.industry" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="工种" v-bind="validateInfos.workType">
            <worktype-auto-complete v-model:value="formData.workType" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="车间" v-bind="validateInfos.workShop">
            <a-input v-model:value="formData.workShop" placeholder="请输入车间" :disabled="disabled" size="middle" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="工龄辅助输入" extra="例如；5.5代表5年零5个月">
            <a-input v-model:value="workTime" placeholder="工龄辅助输入" :disabled="disabled" size="middle" @blur="handleWorkTimeChange" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="开始接害" v-bind="validateInfos.riskStartTime">
            <a-date-picker
              placeholder="请选择开始接触危害因素时间"
              v-model:value="formData.riskStartTime"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              :disabled="disabled"
              size="middle"
              :allow-clear="true"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="总工龄(年）" v-bind="validateInfos.workYears">
            <a-input v-model:value="formData.workYears" placeholder="请输入总工龄" :disabled="disabled" size="middle" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="总工龄（月）" v-bind="validateInfos.workMonths">
            <a-input v-model:value="formData.workMonths" placeholder="请输入总工龄月数" :disabled="disabled" size="middle" />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="接害工龄（年）" v-bind="validateInfos.riskYears">
            <a-input v-model:value="formData.riskYears" placeholder="请输入接害工龄（年）" :disabled="disabled" size="middle" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="接害工龄（月）" v-bind="validateInfos.riskMonths">
            <a-input v-model:value="formData.riskMonths" placeholder="请输入接害工龄月数" :disabled="disabled" size="middle" />
          </a-form-item>
        </a-col>
        <!--        <a-col :span="12">
          <a-form-item label="照射种类" v-bind="validateInfos.occIrradiation">
            <j-dict-select-tag
              :useDicColor="true"
              v-model:value="formData.occIrradiation"
              dictCode="irradiationType"
              placeholder="请选择照射种类"
              :disabled="disabled"
              size="middle"
            />
          </a-form-item>
        </a-col>-->
        <!--        <a-col :span="12">
          <a-form-item label="工号" v-bind="validateInfos.workNo">
            <a-input v-model:value="formData.workNo" placeholder="请输入工号" :disabled="disabled" size="middle" />
          </a-form-item>
        </a-col>-->
        <a-col :span="12">
          <a-form-item label="备注" v-bind="validateInfos.remark">
            <a-textarea v-model:value="formData.remark" placeholder="请输入备注" :disabled="disabled" size="middle" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-spin>
  <camera-modal
    ref="cameraModal"
    @on-photo-taken="handleCameraOk"
    :constraints="{ video: { width: { ideal: 720 }, height: { ideal: 1280 } }, facingMode: 'environment' }"
  />
  <company-team-detail-modal ref="companyTeamDetailModal" />
  <!--  <CustomerRegTeamDetailModal
    ref="customerTeamDetailModal"
    @share="handleShareTeamLimitAmount(true)"
    @cancel-share="handleShareTeamLimitAmount(false)"
  /> -->
</template>

<script lang="ts" setup>
  import { computed, defineExpose, defineProps, inject, nextTick, reactive, Ref, ref, toRaw, watch, watchEffect } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import CompanyTeamDetailModal from '/@/views/reg/components/CompanyTeamDetailModal.vue';
  import { getValueType } from '/@/utils';
  import { changeItemGroupByCompanyTeam, getItemGroupOfTeam, regOne, saveOrUpdate } from '../CustomerReg.api';
  import { Form, message, theme } from 'ant-design-vue';
  import CameraModal from '/src/components/Camera/CameraModal.vue';
  import { uploadFile } from '@/utils/upload';
  import defaultAvatar from '/@/assets/images/defaultAvatar.png';
  import IDCardUtil from '@/utils/IDCardUtil';
  import { JAsyncSearchSelect } from '@/components/Form';
  import { getFileAccessHttpUrl } from '@/utils/common/compUtils';
  import { companyTeamList, searchCompanyReg } from '@/views/reg/CompanyReg.api';
  import dayjs from 'dayjs';
  import { IdcData } from '#/utils';
  import { queryCustomerByIdCard } from '@/views/reg/Customer.api';
  import { SaveOutlined } from '@ant-design/icons-vue';
  import { usePermission } from '@/hooks/web/usePermission';
  import { setFormData2Null } from '@/utils/common/formUtils';
  import CompanyAutoComplete from '@/components/basicinfo/CompanyAutoComplete.vue';
  import type { CompanyAutoCompleteDTO } from '@/types/basicinfo/company';
  import ZyIndustrySelect from '@/components/occu/ZyIndustrySelect.vue';
  import ZyRiskFactorAutoComplete from '@/components/basicinfo/ZyRiskFactorAutoComplete.vue';
  import WorktypeAutoComplete from '@/components/basicinfo/WorktypeAutoComplete.vue';

  const { createConfirm, createErrorModal } = useMessage();
  const { token } = theme.useToken();
  const { hasPermission } = usePermission();
  const companyDeptReloadKey = ref<number>(1);

  /**接收并监听身份证信息，据此进行查询*/
  const idcData = inject<IdcData>('idCardDataKey', {
    data: {},
    ok: false,
    msg: '',
    state: '',
    action: '',
  });
  const { notification } = useMessage();
  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: {
      type: Object,
      default: () => {},
    },
  });

  const IDCardUtils = new IDCardUtil();
  const companyTeamDetailModal = ref(null);
  const cameraModal: Ref<InstanceType<typeof CameraModal> | null> = ref(null);
  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok', 'regOk', 'changeItemOk', 'changeItemCancel']);
  const originCompanyTeamId = ref<any>(null);
  const formData = reactive<Record<string, any>>({
    id: '',
    examNo: '',
    customerAvatar: '',
    customerId: '',
    archivesNum: '',
    customerRegNum: '',
    hisInpatientId: '',
    name: '',
    gender: '',
    age: null,
    ageUnit: '',
    birthday: null,
    phone: '',
    marriageStatus: null,
    pregnancyFlag: null,
    cardType: '居民身份证',
    idCard: '',
    examCategory: '职业病体检',
    teamId: null,
    teamName: null,
    teamNum: null,
    bookingDate: null,
    industry: null,
    workNo: null,
    career: null,
    jobStatus: null,
    workType: null,
    workShop: null,
    riskFactor: null,
    workYears: null,
    workMonths: null,
    riskYears: null,
    riskMonths: null,
    occIrradiation: null,
    remark: null,
    companyRegId: null,
    companyRegName: null,
    checkDate: null,
    creatorBy: null,
    creator: null,
    checkState: null,
    infoSource: null,
    paymentState: null,
    operId: null,
    operName: null,
    operTime: null,
    auditDate: null,

    webQueryCode: '',
    companyName: null,
    companyId: null,
    createTime: null,
    regTime: null,
    guidancePrintTimes: null,
    serialNo: null,
    retrieveStatus: null,
    retrieveTime: null,
    retrieveBy: null,
    retrieveImg: null,
    companyTeam: null,
    companyReg: null,
    suitName: null,
    summaryStatus: null,
    totalPrice: null,
    payedAmount: null,
    remainAmount: null,
    emergencyContact: null,
    emergencyPhone: null,
    regDate: null,
    genderDesc: null,
    personCompanyName: null,
    breakfirstFlag: null,
    preSummaryStatus: null,
    summaryTime: null,
    preSummaryTime: null,
    summaryAuditTime: null,
    reportPrintTime: null,
    reportEditLockFlag: null,
    reportEditLockBy: null,
    reportEditLocker: null,
    reportEditLockTime: null,
    status: null,
    creatorBy_dictText: null,
    pca: '',
    province: '',
    city: '',
    area: '',
    provinceCode: '',
    cityCode: '',
    areaCode: '',

    address: '',
    addressDetail: '',
    riskStartTime: null,
    limitAmount: null,
    appointmentDate: dayjs().format('YYYY-MM-DD'),
  });
  function setDefaultFormData() {
    setFormData2Null(formData);
    formData.appointmentDate = dayjs().format('YYYY-MM-DD');
    formData.cardType = '居民身份证';
    formData.examCategory = '职业病体检';
  }
  const age = computed(() => {
    //根据出生日期计算年龄
    if (formData.birthday) {
      let birth = new Date(formData.birthday);
      let now = new Date();
      let diff = now.getTime() - birth.getTime();
      return Math.floor(diff / (365.25 * 24 * 60 * 60 * 1000));
    }
  });
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 9 }, md: { span: 9 }, lg: { span: 9 }, xl: { span: 9 }, xxl: { span: 9 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 15 }, md: { span: 15 }, lg: { span: 15 }, xl: { span: 15 }, xxl: { span: 15 } });
  const confirmLoading = ref<boolean>(false);
  const teamList = ref<any[]>([]);

  // 工龄计算相关变量
  const isCalculatingRiskTime = ref<boolean>(false); // 防止接害工龄循环计算的标志
  const isCalculatingWorkTime = ref<boolean>(false); // 防止总工龄循环计算的标志
  //表单验证
  const validatorRules = reactive({
    riskFactor: [{ required: true, message: '请输入危害因素!' }],
    jobStatus: [{ required: true, message: '请输入岗位类别!' }],
    examCategory: [{ required: true, message: '请输入体检分类!' }],
    appointmentDate: [{ required: true, message: '请输入预约日期!' }],
    name: [{ required: true, message: '请输入姓名!' }],
    cardType: [{ required: true, message: '请输入证件类型!' }],
    idCard: [
      { required: true, message: '请输入证件号!' },
      {
        validator: (_, value) => {
          if (!value) {
            return Promise.reject('');
          } else {
            if (formData.cardType == '居民身份证' && !IDCardUtils.isValid(value)) {
              return Promise.reject('请输入正确的身份证号!');
            } else {
              return Promise.resolve();
            }
          }
        },
        trigger: 'change',
      },
    ],
    gender: [{ required: true, message: '请输入性别!' }],
    birthday: [{ required: true, message: '请输入出生日期!' }],
    phone: [
      { required: true, message: '请输入电话!' },
      {
        pattern: /^(?:\d{11}|(?:\d{3,4}-)?\d{7,8})$/,
        message: '电话必须是11位数字或固定电话（如：0471-2537660）!',
      },
    ],
    career: [{ required: false, message: '请输入职业!' }],
    emergencyContact: [{ required: false, message: '请输入紧急联系人!' }],
    emergencyPhone: [
      { required: false, message: '请输入紧急联系人电话!' },
      { pattern: /^\d{11}$/, message: '电话必须是11位数字!' },
    ],
    jobStatus: [],
  });
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

  watch(
    () => formData.riskFactor,
    (newVal) => {
      if (newVal && newVal.length > 0) {
        validatorRules.jobStatus = [{ required: true, message: '请输入岗位类别!' }];
      } else {
        validatorRules.jobStatus = [];
      }
    }
  );

  // 监听开始接害日期变化，反向计算接害工龄
  watch(
    () => formData.riskStartTime,
    (newVal, oldVal) => {
      console.log('🔍 riskStartTime watch triggered:', { newVal, oldVal, isCalculating: isCalculatingRiskTime.value });
      if (newVal && !isCalculatingRiskTime.value) {
        console.log('✅ 触发接害工龄计算');
        calculateRiskYearsFromStartTime();
      }
    }
  );

  // 监听接害工龄变化，计算开始接害日期
  watch(
    () => formData.riskYears,
    (newVal, oldVal) => {
      console.log('🔍 riskYears watch triggered:', { newVal, oldVal, isCalculating: isCalculatingRiskTime.value });
      if (newVal && !isCalculatingRiskTime.value) {
        console.log('✅ 触发开始接害日期计算');
        calculateRiskStartTime();
      }
    }
  );

  // 监听总工龄变化，进行相关验证和计算
  watch(
    () => formData.workYears,
    (newVal, oldVal) => {
      console.log('🔍 workYears watch triggered:', {
        newVal,
        oldVal,
        isCalculatingRisk: isCalculatingRiskTime.value,
        isCalculatingWork: isCalculatingWorkTime.value,
      });
      if (newVal && !isCalculatingWorkTime.value && !isCalculatingRiskTime.value) {
        console.log('✅ 总工龄变化，触发相关计算');
        // 验证工龄关系（如果接害工龄已存在）
        if (formData.riskYears) {
          validateWorkYearsRelation();
        }
        // 根据总工龄计算接害开始时间和接害工龄
        calculateWorkStartTimeFromWorkYears();
      } else {
        console.log('⚠️ 总工龄变化被跳过:', {
          hasValue: !!newVal,
          isCalculatingWork: isCalculatingWorkTime.value,
          isCalculatingRisk: isCalculatingRiskTime.value,
        });
      }
    }
  );

  // CompanyAutoComplete事件处理函数
  const handleCompanySelect = (value: string, option: CompanyAutoCompleteDTO) => {
    console.log('选中单位:', value, option);

    // 更新表单数据
    formData.companyId = value;
    formData.companyName = option.name;

    // 自动填充相关信息
    if (option.telephone && !formData.phone) {
      formData.phone = option.telephone;
    }
    if (option.address && !formData.address) {
      formData.address = option.address;
    }

    // 清空部门选择，触发部门树重新加载
    formData.companyDeptId = null;
    formData.companyDeptName = null;
    companyDeptReloadKey.value += 1; // 触发JTreeSelect重新加载
    getCompanyDeptList(value); // 保留原有逻辑以兼容其他地方的使用

    message.success(`已选择单位: ${option.name}`);
  };

  const handleRiskFactorChange = (event) => {
    if (event.selectedFactors?.length > 0) {
      console.log('选中的危害因素:', event.selectedFactors);
      //给riskFactor赋值id字符串，多个用英文逗号分隔
      formData.riskFactor = event.selectedFactors.map((item) => item.id).join(',');
    }
  };

  const handleCompanyAutoCompleteChange = (value: string, option?: CompanyAutoCompleteDTO) => {
    console.log('单位变化:', value, option);

    if (!value) {
      // 清空时，清空所有相关字段
      formData.companyId = null;
      formData.companyName = null;
      formData.companyDeptId = null;
      formData.companyDeptName = null;
      companyDeptList.value = [];
      companyDeptReloadKey.value += 1; // 触发部门树重新加载
      message.info('已清空单位选择');
    } else if (!option) {
      // 纯文本输入（未从列表选择），需要检查是否匹配数据库记录
      formData.companyName = value;
      // 延迟检查，避免频繁查询
      checkCompanyMatch(value);
    }
    // 注意：如果option存在，说明是从列表选择的，这种情况由handleCompanySelect处理
  };

  function handleChangeItemByCompanyTeam(customerRegId, companyTeamId) {
    createConfirm({
      iconType: 'warning',
      title: '变更所属分组同步更换分组下检查项目确认',
      content: '变更所属分组同步更换项目后无法恢复！确认要更换为所选预约分组的项目？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        changeItemGroupByCompanyTeam({ customerRegId: customerRegId, companyTeamId: companyTeamId ? companyTeamId : '' })
          .then((res) => {
            if (res.success) {
              message.success(`项目更换成功`);
              emit('changeItemOk');
            } else {
              message.error(res.message);
              emit('changeItemOk');
            }
          })
          .catch((err) => {
            console.log('同步更换所属项目分组错误', err);
          });
      },
      onCancel: () => {
        message.warn(`取消更换`);
        emit('changeItemCancel');
      },
    });
  }

  // 表单禁用
  const disabled = computed(() => {
    return props.formDisabled;
  });

  function showCompanyTeam() {
    //console.log('showCompanyTeam');
    companyTeamDetailModal.value?.open(formData.companyRegId, formData.teamId);
  }

  const showAddrComp = ref<boolean>(false);
  function handleArea(data) {
    console.log('HealthRecordForm.vue handleArea', data);
    formData.province = data[0]?.name;
    formData.provinceCode = data[0]?.code;
    formData.city = data[1]?.name;
    formData.cityCode = data[1]?.code;
    formData.area = data[2]?.name;
    formData.areaCode = data[2]?.code;
    formData.street = data[3]?.name;
    formData.streetCode = data[3]?.code;
    formData.addressDetail = `${formData.province ?? ''}${formData.city ?? ''}${formData.area ?? ''}${formData.street ?? ''}`;
  }

  function getTeamList(companyRegId) {
    if (!companyRegId) {
      teamList.value = [];
      formData.teamId = '';
      return;
    }

    companyTeamList({ companyRegId: companyRegId, pageSize: 10000 }).then((res) => {
      //teamList.value = [];
      //formModel.teamId = '';
      teamList.value = res.records || [];
      if (formData.teamId) {
        let team = teamList.value.find((item) => item.id == formData.teamId);
        if (!team) {
          formData.teamId = null;
        }
      }
      formData.companyName = teamList.value[0].companyName;
      formData.companyId = teamList.value[0].companyId;
    });
  }

  async function handleCameraOk(data) {
    //cameraVisible.value = false;
    //console.log('handleCameraOk', data);

    let uploadRes = await uploadFile(data.blob, 'jpg');
    //console.log('uploadRes', uploadRes);
    formData.customerAvatar = uploadRes.message;
    if (formData.id) {
      saveOrUpdate(formData, true);
    }
  }

  function takePhone() {
    cameraModal.value?.open();
  }

  // Watcher for idCard field
  watch(
    () => formData.idCard,
    (newIdCard) => {
      if (newIdCard && IDCardUtils.isValid(newIdCard)) {
        queryCustomerByIdCard({ idCard: newIdCard }).then((res) => {
          if (res.success) {
            let customer = res.result;
            if (customer) {
              //formModel.idCard = newIdCard;
              if (customer.avatar) {
                formData.customerAvatar = customer.avatar;
              }
              formData.customerId = customer.id;
              formData.archivesNum = customer.archivesNum;
              formData.phone = customer.phone;
              formData.name = customer.name;
              if (customer.address) {
                formData.address = customer.address;
              }
              formData.age = customer.age;
              formData.gender = customer.gender;
              formData.career = customer.career;
              formData.province = customer.province;
              formData.city = customer.city;
              formData.area = customer.area;
              formData.provinceCode = customer.provinceCode;
              formData.cityCode = customer.cityCode;
              formData.areaCode = customer.areaCode;
              formData.addressDetail = customer.addressDetail;
            }
          }
        });
      }
    }
  );

  /**
   * 新增
   */
  function add() {
    setDefaultFormData();
    edit({});
  }

  /**
   * 编辑
   */
  function edit(record) {
    showAddrComp.value = false;
    nextTick(() => {
      resetFields();
      //赋值
      Object.assign(formData, record);
      originCompanyTeamId.value = formData.teamId;

      if (formData.companyRegId) {
        getTeamList(formData.companyRegId);
      }
    });
  }

  function handleIdCard(e) {
    let value = e.target.value;
    if (IDCardUtils.isValid(value)) {
      let idInfo = IDCardUtils.getIDInfo(value);
      if (formData.birthDay != idInfo.birth) {
        formData.birthday = idInfo.birth;
      }
      if (formData.gender != idInfo.gender) {
        formData.gender = idInfo.gender;
      }
      if (!formData.address) {
        formData.address = idInfo.addr;
      }
      formData.age = idInfo.age;
    }
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    // 触发表单验证
    try {
      await validate();
    } catch (e) {
      const errorMessages = e.errorFields
        .map((field) => {
          return `${field.errors.join('')}`;
        })
        .join('<br/>');

      createErrorModal({
        title: '表单验证失败',
        content: `${errorMessages}`,
      });

      //console.log(e);
      //给出更详细的提示，能够提示出哪个字段的错误
      //message.error('请检查表单是否填写完整！');
      return;
    }

    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    let model = toRaw(formData);
    if (model.id) {
      isUpdate.value = true;
    }
    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    model.age = age.value;
    await saveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          const flag = originCompanyTeamId.value == res.result.teamId;
          Object.assign(formData, res.result);
          createMessage.success(res.message);
          if (originCompanyTeamId.value && !flag && formData.teamId) {
            getItemGroupOfTeam({ teamId: formData.teamId }).then((res) => {
              if (res.success && res.result.length) {
                handleChangeItemByCompanyTeam(formData.id, formData.teamId);
              }
            });
          }
          emit('ok', res.result);
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }

  function reg() {
    let model = formData;
    if (!model.id) {
      message.error('请先保存基本信息！');
      return;
    }
    if (model.status != '未登记') {
      message.error('不能重复登记！');
      return;
    }

    confirmLoading.value = true;
    regOne({ id: model.id })
      .then((res) => {
        if (res.success) {
          message.success(res.message);
          emit('regOk', res.result);
        } else {
          message.error(res.message);
        }
      })
      .finally((e) => {
        confirmLoading.value = false;
      });
  }

  watchEffect(() => {
    if (props.formData) {
      Object.assign(formData, props.formData);
    }
  });

  const idcDataSource = computed(() => ({
    data: idcData.data,
    action: idcData.action,
  }));

  watch(idcDataSource, (val) => {
    if (val.data.idCardNo && val.action == 'addReg') {
      if (val.data.idCardNo != formData.idCard) {
        setDefaultFormData();
      }
      formData.idCard = val.data.idCardNo;
      formData.name = val.data.idName;
      formData.gender = val.data.idSex;
      formData.nation = val.data.idNation;
      formData.birthday = dayjs(val.data.idBorn, 'YYYYMMDD').format('YYYY-MM-DD');
      //formModel.address = val.data.idAddress;
      formData.customerAvatar = 'data:image/jepg;base64,' + val.data.idPictureBase64;
      formData.cardType = val.data.CardTypeName;
      /* let pccaCode = IDCardUtils.getPccaCode(val.data.idCardNo);
      formModel.provinceCode = pccaCode.provinceCode;
      formModel.cityCode = pccaCode.cityCode;
      formModel.areaCode = pccaCode.districtCode;*/
    }
  });

  /**
   * 根据接害工龄计算开始接害日期
   * 计算逻辑：当前日期 - 接害工龄年数
   */
  function calculateRiskStartTime() {
    console.log('🚀 calculateRiskStartTime 开始执行');
    console.log('📊 当前状态:', {
      isCalculating: isCalculatingRiskTime.value,
      riskYears: formData.riskYears,
    });

    if (isCalculatingRiskTime.value) {
      console.log('⚠️ 正在计算中，跳过执行');
      return; // 防止循环计算
    }

    // 解析接害工龄（年），支持小数格式如5.5年
    const riskYearsInput = formData.riskYears;
    console.log('📝 接害工龄输入值:', riskYearsInput);

    if (!riskYearsInput) {
      console.log('🧹 工龄为空，清空开始接害日期');
      // 如果工龄为空，清空开始接害日期
      isCalculatingRiskTime.value = true;
      formData.riskStartTime = null;
      setTimeout(() => {
        isCalculatingRiskTime.value = false;
      }, 100);
      return;
    }

    const riskYearsFloat = parseFloat(riskYearsInput);
    console.log('🔢 解析后的工龄数值:', riskYearsFloat);

    if (isNaN(riskYearsFloat) || riskYearsFloat <= 0) {
      console.log('❌ 工龄数值无效');
      return;
    }

    // 设置标志，防止循环计算
    console.log('🔒 设置计算标志');
    isCalculatingRiskTime.value = true;

    try {
      // 使用当前日期作为基准
      const currentDate = dayjs();
      console.log('📅 当前日期:', currentDate.format('YYYY-MM-DD'));

      // 将小数年份转换为年和月
      const years = Math.floor(riskYearsFloat);
      const months = Math.round((riskYearsFloat - years) * 12);
      console.log('📊 计算参数:', { years, months });

      // 减去年数和月数
      const calculatedDate = currentDate.subtract(years, 'year').subtract(months, 'month');
      console.log('📅 计算出的开始接害日期:', calculatedDate.format('YYYY-MM-DD'));

      // 设置计算出的开始接害日期
      formData.riskStartTime = calculatedDate.format('YYYY-MM-DD');
      console.log('✅ 已设置 formData.riskStartTime:', formData.riskStartTime);

      // 提示用户计算结果
      //createMessage.info(`根据接害工龄自动计算开始接害日期为：${formData.riskStartTime}`);
    } finally {
      // 重置标志
      console.log('🔓 重置计算标志');
      setTimeout(() => {
        isCalculatingRiskTime.value = false;
        console.log('🔓 计算标志已重置');
      }, 100);
    }
  }

  /**
   * 根据开始接害日期反向计算接害工龄和总工龄
   * 计算逻辑：当前日期 - 开始接害日期 = 接害工龄
   * 同时强制更新总工龄（假设总工龄 = 接害工龄 + 额外工作年限，这里简化为等于接害工龄）
   */
  function calculateRiskYearsFromStartTime() {
    console.log('🚀 calculateRiskYearsFromStartTime 开始执行');
    console.log('📊 当前状态:', {
      isCalculating: isCalculatingRiskTime.value,
      riskStartTime: formData.riskStartTime,
    });

    if (!formData.riskStartTime) {
      console.log('❌ 开始接害日期为空');
      return;
    }

    // 设置标志，防止循环计算
    console.log('🔒 设置计算标志');
    isCalculatingRiskTime.value = true;

    try {
      const startDate = dayjs(formData.riskStartTime);
      const currentDate = dayjs();

      // 计算总月数差
      const totalMonths = currentDate.diff(startDate, 'month');

      if (totalMonths < 0) {
        createMessage.warning('开始接害日期不能晚于当前日期');
        return;
      }

      // 计算年数和月数
      const years = Math.floor(totalMonths / 12);
      const months = totalMonths % 12;
      const yearsDecimal = (totalMonths / 12).toFixed(1);
      console.log('📊 计算结果:', { totalMonths, years, months, yearsDecimal });

      // 更新接害工龄年份（显示为小数）和月份（隐藏字段）
      formData.riskYears = yearsDecimal;
      formData.riskMonths = months.toString();
      console.log('✅ 已更新接害工龄:', {
        riskYears: formData.riskYears,
        riskMonths: formData.riskMonths,
        显示: `${yearsDecimal}年`,
        实际: `${years}年${months}个月`,
      });

      // 强制更新总工龄（假设总工龄至少等于接害工龄）
      const currentWorkYears = parseFloat(formData.workYears) || 0;
      const currentWorkMonths = parseFloat(formData.workMonths) || 0;
      const totalCurrentWorkMonths = currentWorkYears * 12 + currentWorkMonths;

      // 如果当前总工龄小于计算出的接害工龄，则更新总工龄
      if (totalCurrentWorkMonths < totalMonths) {
        console.log('🔄 需要强制更新总工龄');

        // 设置总工龄计算标志，防止循环
        isCalculatingWorkTime.value = true;

        // 更新总工龄年份（显示字段）
        const totalYearsDecimal = (totalMonths / 12).toFixed(1);
        formData.workYears = totalYearsDecimal;
        // 更新总工龄月份（隐藏字段，用于后台计算）
        formData.workMonths = months.toString();

        console.log('🔄 强制更新总工龄为:', {
          workYears: formData.workYears,
          workMonths: formData.workMonths,
          显示: `${totalYearsDecimal}年`,
          实际: `${years}年${months}个月`,
        });

        // 重置总工龄计算标志
        setTimeout(() => {
          isCalculatingWorkTime.value = false;
          console.log('🔓 总工龄计算标志已重置');
        }, 100);

        //createMessage.info(`已自动更新总工龄为：${totalYearsDecimal}年（不能小于接害工龄）`);
      }

      // 提示用户计算结果
      if (years > 0 || months > 0) {
        //createMessage.info(`根据开始接害日期自动计算接害工龄为：${yearsDecimal}年（${years}年${months}个月）`);
      }
    } finally {
      // 重置标志
      setTimeout(() => {
        isCalculatingRiskTime.value = false;
      }, 100);
    }
  }

  /**
   * 接害工龄变化处理
   */
  function handleRiskYearsChange() {
    console.log('🎯 handleRiskYearsChange 被调用');
    console.log('📝 当前接害工龄值:', formData.riskYears);
    // 延迟执行，确保数据已更新
    nextTick(() => {
      console.log('⏰ nextTick 执行，isCalculating:', isCalculatingRiskTime.value);
      if (!isCalculatingRiskTime.value) {
        console.log('✅ 调用 calculateRiskStartTime');
        calculateRiskStartTime();
      } else {
        console.log('⚠️ 正在计算中，跳过');
      }
    });
  }

  /**
   * 开始接害日期输入框失焦处理
   * 验证手动输入的日期格式并进行计算
   */
  function handleRiskStartTimeBlur(event) {
    const inputValue = event.target.value;
    if (!inputValue) {
      return;
    }

    // 验证日期格式
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(inputValue)) {
      createMessage.warning('请输入正确的日期格式（YYYY-MM-DD）');
      return;
    }

    // 验证日期有效性
    const inputDate = dayjs(inputValue);
    if (!inputDate.isValid()) {
      createMessage.warning('请输入有效的日期');
      return;
    }

    // 验证日期不能晚于当前日期
    if (inputDate.isAfter(dayjs())) {
      createMessage.warning('开始接害日期不能晚于当前日期');
      return;
    }

    // 验证日期不能早于1900年
    if (inputDate.isBefore(dayjs('1900-01-01'))) {
      createMessage.warning('开始接害日期不能早于1900年');
      return;
    }

    // 如果验证通过，更新表单数据并触发计算
    formData.riskStartTime = inputValue;
    nextTick(() => {
      if (!isCalculatingRiskTime.value) {
        calculateRiskYearsFromStartTime();
      }
    });
  }

  const workTime = ref<any>(null);
  /**
   * 总工龄变化处理
   */
  function handleWorkTimeChange() {}

  /**
   * 验证总工龄与接害工龄的关系
   * 总工龄应该大于等于接害工龄
   */
  function validateWorkYearsRelation() {
    console.log('🔍 validateWorkYearsRelation 开始验证');

    const workYears = parseFloat(formData.workYears);
    const riskYears = parseFloat(formData.riskYears);

    console.log('📊 工龄对比:', { workYears, riskYears });

    if (isNaN(workYears) || isNaN(riskYears)) {
      console.log('❌ 工龄数值无效');
      return;
    }

    if (workYears < riskYears) {
      console.log('⚠️ 总工龄小于接害工龄');
      createMessage.warning('总工龄不能小于接害工龄！');
    } else {
      console.log('✅ 工龄关系验证通过');
      //createMessage.success('工龄关系验证通过');
    }
  }

  /**
   * 根据总工龄计算接害开始时间和接害工龄
   * 逻辑：假设接害工龄等于总工龄（可根据业务需求调整）
   */
  function calculateWorkStartTimeFromWorkYears() {
    console.log('🚀 calculateWorkStartTimeFromWorkYears 开始执行');

    const workYearsInput = formData.workYears;
    console.log('📝 总工龄输入值:', workYearsInput);

    if (!workYearsInput) {
      console.log('❌ 总工龄为空');
      return;
    }

    const workYearsFloat = parseFloat(workYearsInput);
    if (isNaN(workYearsFloat) || workYearsFloat <= 0) {
      console.log('❌ 总工龄数值无效:', workYearsFloat);
      return;
    }

    // 设置标志，防止循环计算
    console.log('🔒 设置计算标志');
    isCalculatingRiskTime.value = true;
    isCalculatingWorkTime.value = true;

    try {
      // 1. 如果接害工龄没有值，设置为总工龄（可根据业务需求调整比例）
      if (!formData.riskYears) {
        // 默认接害工龄等于总工龄（假设整个工作期间都在接触危害因素）
        // 可以根据实际业务需求调整这个比例
        const riskYearsFloat = workYearsFloat; // 100% 比例，可调整为 0.8 等其他值
        const riskYears = Math.floor(riskYearsFloat);
        const riskMonths = Math.round((riskYearsFloat - riskYears) * 12);
        const riskYearsDecimal = riskYearsFloat.toFixed(1);

        formData.riskYears = riskYearsDecimal;
        formData.riskMonths = riskMonths.toString();

        console.log('✅ 自动设置接害工龄:', {
          riskYears: formData.riskYears,
          riskMonths: formData.riskMonths,
          计算逻辑: `总工龄 ${workYearsFloat} = 接害工龄 ${riskYearsFloat}`,
        });

        //createMessage.info(`根据总工龄自动设置接害工龄为：${riskYearsDecimal}年`);
      }

      // 2. 根据接害工龄计算接害开始时间
      const currentRiskYears = parseFloat(formData.riskYears);
      if (currentRiskYears > 0) {
        const currentDate = dayjs();
        const years = Math.floor(currentRiskYears);
        const months = Math.round((currentRiskYears - years) * 12);

        // 计算接害开始时间
        const calculatedStartDate = currentDate.subtract(years, 'year').subtract(months, 'month');
        formData.riskStartTime = calculatedStartDate.format('YYYY-MM-DD');

        console.log('✅ 根据接害工龄计算接害开始时间:', {
          riskYears: currentRiskYears,
          years,
          months,
          riskStartTime: formData.riskStartTime,
        });

        //createMessage.info(`根据接害工龄计算接害开始时间为：${formData.riskStartTime}`);
      }

      // 3. 更新总工龄的月份字段
      const totalYears = Math.floor(workYearsFloat);
      const totalMonths = Math.round((workYearsFloat - totalYears) * 12);
      formData.workMonths = totalMonths.toString();

      console.log('✅ 总工龄联动计算完成:', {
        workYears: workYearsFloat,
        workMonths: formData.workMonths,
        riskYears: formData.riskYears,
        riskStartTime: formData.riskStartTime,
      });
    } finally {
      // 重置标志
      setTimeout(() => {
        isCalculatingRiskTime.value = false;
        isCalculatingWorkTime.value = false;
        console.log('🔓 所有计算标志已重置');
      }, 100);
    }
  }

  defineExpose({
    add,
    edit,
    submitForm,
    reg,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 5px;
  }
  .row-title {
    position: relative;
    justify-content: space-between;
    display: flex;
    align-items: center;
    padding: 5px 20px;
  }

  .row-title:before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 3px; /* 边框宽度 */
    height: 16px; /* 边框高度 */
    background-color: v-bind('token.colorPrimary'); /* 边框颜色 */
    transform: translateY(-50%);
  }

  /* Target all a-form-item components within this component */
  :deep(.ant-form-item) {
    margin-bottom: 4px; /* Reduce bottom margin */
    margin-top: 4px; /* Reduce top margin */
    padding: 0; /* Remove padding */
  }

  /* Optional: Adjust the label padding */
  :deep(.ant-form-item-label) {
    padding: 0;
    line-height: 1;
  }
</style>
