<template>
  <div>
    <a-card size="small" style="padding: 0; height: 95vh" title="职业病体检登记列表">
      <template #extra>
        <a-button type="primary" size="middle" @click="handleAdd">新增登记</a-button>
      </template>
      <!--查询区域-->
      <div class="jeecg-basic-table-form-container">
        <a-form ref="formRef" @keyup.enter="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-row :gutter="[4, 8]">
            <a-col :span="12">
              <a-input
                allow-clear
                @focus="readIdCard4RegList"
                size="middle"
                :placeholder="idcDataSource.action == 'searchRegList' ? idcData.msg : '身份证号'"
                v-model:value="queryParam.idCard"
              />
            </a-col>
            <a-col :span="12">
              <a-input allow-clear size="middle" placeholder="体检号" v-model:value="queryParam.examNo" />
            </a-col>
            <a-col :span="12">
              <a-input allow-clear size="middle" placeholder="姓名" v-model:value="queryParam.name" />
            </a-col>
            <a-col :span="12">
              <j-dict-select-tag dict-code="regStatus" size="middle" placeholder="登记状态" v-model:value="queryParam.status" style="width: 100%" />
            </a-col>
            <a-col :span="24">
              <a-select style="width: 20%" v-model:value="queryParam.dateType">
                <a-select-option value="登记">登记</a-select-option>
                <a-select-option value="预约">预约</a-select-option>
                <a-select-option value="添加">添加</a-select-option>
              </a-select>
              <a-range-picker
                v-model:value="regDateRange"
                placement="登记日期"
                style="width: 80%"
                @change="searchQuery"
                :presets="rangePresets"
                :allow-clear="false"
              />
            </a-col>
            <template v-if="toggleSearchStatus">
              <a-col :span="12">
                <j-select-user size="middle" placeholder="登记员工" v-model:value="queryParam.creatorBy" style="width: 100%" />
              </a-col>
              <a-col :span="12">
                <a-select size="middle" placeholder="交表状态" v-model:value="queryParam.retrieveStatus" style="width: 100%">
                  <a-select-option value="">请选择</a-select-option>
                  <a-select-option value="0">未交表</a-select-option>
                  <a-select-option value="1">已交表</a-select-option>
                </a-select>
              </a-col>
              <a-col :span="12">
                <j-async-search-select
                  size="middle"
                  placeholder="所属预约"
                  @change="getTeamList"
                  v-model:value="queryParam.companyRegId"
                  dict="company_reg where lock_status=0 ,reg_name,id"
                  allowClear
                  style="width: 100%"
                />
              </a-col>
              <a-col :span="12">
                <a-select size="middle" placeholder="所属分组" v-model:value="queryParam.teamId" allowClear style="width: 100%">
                  <a-select-option value="">请选择</a-select-option>
                  <a-select-option :value="item.id" v-for="item in teamList">{{ item.name }}</a-select-option>
                </a-select>
              </a-col>
              <a-col :span="12">
                <a-input allow-clear size="middle" placeholder="单位名称" v-model:value="queryParam.companyName" />
              </a-col>
              <a-col :span="12">
                <j-async-search-select
                  size="middle"
                  placeholder="危害因素"
                  v-model:value="queryParam.riskFactor"
                  dict="zy_risk_factor,name,code"
                  :multiple="true"
                  allowClear
                  style="width: 100%"
                />
              </a-col>
            </template>
          </a-row>
        </a-form>
      </div>

      <!--引用表格-->
      <BasicTable @register="registerTable" :rowSelection="rowSelection">
        <!--插槽:table标题-->
        <template #tableTitle>
          <template v-if="selectedRowKeys.length > 0">
            <a-space>
              <a-button type="primary" size="small" @click="batchReg" :loading="actionLoading" :disabled="actionLoading">登记</a-button>
              <a-button
                type="primary"
                size="small"
                @click="sendFee"
                v-if="hasPermission('reg:customer_reg:sendFee2HisBatch')"
                :loading="actionLoading"
                :disabled="actionLoading"
                >发送收费申请</a-button
              >
              <a-popover trigger="hover">
                <template #content>
                  <a-checkbox-group v-model:value="batchPrintContent" :options="['导引单', '申请单', '条码', '体检号']" />
                  <a-button type="primary" @click="handleBatchPrint">打印</a-button>
                </template>
                <a-button type="dashed" size="small">打印</a-button>
              </a-popover>

              <a-button
                type="primary"
                size="small"
                @click="batchHandleDelete"
                v-if="hasPermission('reg:customer_reg:deleteBatch')"
                danger
                :loading="actionLoading"
                :disabled="actionLoading"
                >删除</a-button
              >
            </a-space>
          </template>
        </template>

        <template #bodyCell="{ column, text, record, index }">
          <template v-if="column.dataIndex == 'serialNo'"> {{ record.serialNo || '-' }} | {{ record.appointmentSort }} </template>
          <template v-if="column.dataIndex == 'status'">
            <a-tag color="green" v-if="text === '已登记'"> {{ text }}</a-tag>
            <a-tag color="red" v-else-if="text === '未登记'"> {{ text }}</a-tag>
            <a-tag v-else> {{ text }}</a-tag>
          </template>
          <template v-if="column.dataIndex == 'examCategory'">
            <a-tag color="orange">职业病体检</a-tag>
          </template>
          <!-- 如果有项目名称列，添加项目关系显示逻辑 -->
          <template v-if="column.dataIndex == 'itemGroupName'">
            <div style="display: flex; justify-content: space-between; align-items: center">
              <div style="display: flex; align-items: center; gap: 2px; overflow: hidden">
                <!-- 项目关系前缀显示 -->
                <span
                  v-if="getItemRelationPrefix(record.itemGroupId)"
                  :style="{
                    color: getItemRelationPrefix(record.itemGroupId).color,
                    fontSize: '12px',
                    fontWeight: 500,
                    flexShrink: 0,
                    userSelect: 'none',
                  }"
                  :title="getItemRelationPrefix(record.itemGroupId).title"
                >
                  {{ getItemRelationPrefix(record.itemGroupId).text }}
                </span>

                <!-- 项目名称 -->
                <span
                  :style="{
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    color: getItemSourceType(record.itemGroupId) !== 'main' ? '#666' : 'inherit',
                  }"
                >
                  {{ formatItemDisplayName(text, record.checkPartName) }}
                </span>

                <!-- 项目关系badge -->
                <a-tag
                  v-if="getItemSourceBadge(record.itemGroupId) && (relationDisplayMode === 'badge' || relationDisplayMode === 'both')"
                  :title="getItemSourceBadge(record.itemGroupId).title"
                  size="small"
                  :style="{
                    margin: 0,
                    fontSize: '10px',
                    lineHeight: '16px',
                    padding: '0 4px',
                    fontWeight: 500,
                    backgroundColor: getItemSourceBadge(record.itemGroupId).bg,
                    color: getItemSourceBadge(record.itemGroupId).color,
                    border: `1px solid ${getItemSourceBadge(record.itemGroupId).bg}`,
                    cursor: 'pointer',
                  }"
                  @click="showItemRelationDetail(record.itemGroupId)"
                >
                  {{ getItemSourceBadge(record.itemGroupId).text }}
                </a-tag>
              </div>
            </div>
          </template>
        </template>
      </BasicTable>
    </a-card>

    <!-- 项目关系详情模态框 -->
    <a-modal v-model:open="relationDetailModal.visible" title="项目关系详情" width="800px" :footer="null" @cancel="closeRelationDetailModal">
      <div style="padding: 10px">
        <div v-if="relationDetailModal.loading" style="text-align: center; padding: 40px">
          <a-spin size="large" />
          <div style="margin-top: 16px">正在加载关系详情...</div>
        </div>

        <div v-else-if="relationDetailModal.itemInfo && relationDetailModal.relationData" style="padding: 16px 0">
          <!-- 当前项目信息 -->
          <div style="margin-bottom: 24px">
            <h4 style="margin-bottom: 12px; color: #1890ff"> <span style="margin-right: 8px">📋</span>当前项目信息 </h4>
            <div style="background: #f5f5f5; padding: 12px; border-radius: 6px">
              <div><strong>项目名称：</strong>{{ relationDetailModal.itemInfo.itemGroupName }}</div>
              <div v-if="relationDetailModal.itemInfo.checkPartName" style="margin-top: 4px">
                <strong>检查部位：</strong>{{ relationDetailModal.itemInfo.checkPartName }}
              </div>
              <div style="margin-top: 4px">
                <strong>项目类型：</strong>
                <a-tag :color="getItemSourceBadge(relationDetailModal.itemInfo.itemGroupId)?.bg" style="margin-left: 8px; color: #fff">
                  {{ getItemSourceBadge(relationDetailModal.itemInfo.itemGroupId)?.text }}
                </a-tag>
              </div>
            </div>
          </div>

          <!-- 关系来源信息 -->
          <div v-if="relationDetailModal.relationData.sourceInfo" style="margin-bottom: 24px">
            <h4 style="margin-bottom: 12px; color: #52c41a"> <span style="margin-right: 8px">🔗</span>关系来源 </h4>
            <div style="background: #f6ffed; border: 1px solid #b7eb8f; padding: 12px; border-radius: 6px">
              <div><strong>来源项目：</strong>{{ relationDetailModal.relationData.sourceInfo.mainItem.itemGroupName }}</div>
              <div v-if="relationDetailModal.relationData.sourceInfo.mainItem.checkPartName" style="margin-top: 4px">
                <strong>来源部位：</strong>{{ relationDetailModal.relationData.sourceInfo.mainItem.checkPartName }}
              </div>
              <div style="margin-top: 8px">
                <strong>关系类型：</strong>
                <span v-if="relationDetailModal.relationData.sourceInfo.type === 'dependent'" style="color: #fa8c16">
                  依赖关系 - 当前项目是上述项目的依赖项目
                </span>
                <span v-else-if="relationDetailModal.relationData.sourceInfo.type === 'gift'" style="color: #52c41a">
                  赠送关系 - 当前项目是上述项目的赠送项目
                </span>
                <span v-else-if="relationDetailModal.relationData.sourceInfo.type === 'attach'" style="color: #722ed1">
                  附属关系 - 当前项目是上述项目的附属项目
                </span>
              </div>
            </div>
          </div>

          <!-- 说明信息 -->
          <div style="background: #e6f7ff; border: 1px solid #91d5ff; padding: 12px; border-radius: 6px">
            <h4 style="margin-bottom: 8px; color: #1890ff"> <span style="margin-right: 8px">💡</span>说明 </h4>
            <div style="color: #666; line-height: 1.6">
              <div v-if="relationDetailModal.relationData.sourceInfo?.type === 'dependent'">
                • 此项目是依赖项目，当添加来源项目时会自动添加<br />
                • 依赖项目通常是检查必需的基础项目或前置项目<br />
                • 删除来源项目时，此项目也会被自动删除
              </div>
              <div v-else-if="relationDetailModal.relationData.sourceInfo?.type === 'gift'">
                • 此项目是赠送项目，当添加来源项目时会免费获得<br />
                • 赠送项目通常是促销活动或套餐优惠的一部分<br />
                • 删除来源项目时，此项目也会被自动删除
              </div>
              <div v-else-if="relationDetailModal.relationData.sourceInfo?.type === 'attach'">
                • 此项目是附属项目，与来源项目形成组合<br />
                • 附属项目通常是主项目的补充检查或相关项目<br />
                • 删除来源项目时，此项目也会被自动删除
              </div>
            </div>
          </div>
        </div>

        <div v-else style="text-align: center; padding: 40px; color: #999"> 暂无关系详情信息 </div>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" name="CustomerRegListOfPannelOccu" setup>
  import { computed, inject, onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue';
  import { BasicTable } from '/src/components/Table';
  import { useListPage } from '/src/hooks/system/useListPage';
  import { columnsLite } from './CustomerReg.data';
  import { batchDelete, getItemGroupWithDependencyAnalysis, list, regBatch, sendFee2HisByIds } from './CustomerReg.api';
  import { companyTeamList } from './CompanyReg.api';
  import { list as listSuitCategories } from '@/views/basicinfo/SuitCategory.api';
  import { getGroupOfSuit, list as listSuits, listSuitByKeyword } from '@/views/basicinfo/ItemSuit.api';
  import { useUserStore } from '/src/store/modules/user';
  import JDictSelectTag from '../../components/Form/src/jeecg/components/JDictSelectTag.vue';
  import { message, theme } from 'ant-design-vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { JAsyncSearchSelect } from '@/components/Form';
  import JSelectUser from '../../components/Form/src/jeecg/components/JSelectUser.vue';
  import dayjs from 'dayjs';
  import type { RangeValue } from '#/types';
  import { debounce } from 'lodash-es';
  import { usePermission } from '@/hooks/web/usePermission';
  import { IdcData } from '#/utils';
  import { getItemRelations } from '@/utils/itemGroupRelationManager';

  // 套餐分类树相关类型定义
  interface SuitCategoryNode {
    id: string;
    name: string;
    code?: string;
    description?: string;
    sort?: number;
    parentId?: string;
    level: number;
    isExpanded: boolean;
    isLoading: boolean;
    children: SuitNode[];
    childrenLoaded: boolean;
    nodeType: 'category';
  }

  interface SuitNode {
    id: string;
    name: string;
    code?: string;
    description?: string;
    price?: number;
    categoryId: string;
    categoryName: string;
    enableFlag: string;
    sort?: number;
    itemCount?: number;
    isSelected: boolean;
    nodeType: 'suit';
  }

  interface SuitCategoryTree {
    categories: SuitCategoryNode[];
    expandedKeys: Set<string>;
    selectedKeys: Set<string>;
    loadingKeys: Set<string>;
    searchKeyword: string;
    filteredTree: SuitCategoryNode[];
  }

  const { hasPermission } = usePermission();

  const { createConfirm, notification, createErrorModal } = useMessage();
  const emit = defineEmits(['rowClick', 'readIdcard', 'add', 'batchRegOk', 'batchPrint', 'suitAdded']);
  const formRef = ref();
  // 固定查询条件中的体检分类为职业病体检
  const queryParam = reactive<any>({
    dateType: '预约',
  });
  const regDateRange = ref<RangeValue>([dayjs(), dayjs()]);

  const toggleSearchStatus = ref<boolean>(false);
  const userStore = useUserStore();
  const { token } = theme.useToken();
  const actionLoading = ref(false);

  // 项目关系相关数据结构
  const missingDependencies = ref([]);
  const addingDependencies = ref(false);
  const lastDependencyCheckTime = ref(0);
  const itemSourceMap = ref(new Map());
  const relationDisplayMode = ref('prefix'); // 'prefix' | 'badge' | 'both' | 'none'

  // 项目关系详情模态框状态
  const relationDetailModal = ref({
    visible: false,
    loading: false,
    itemInfo: null,
    relationData: null,
  });

  // 控制是否显示详细的badge
  const showDetailedBadges = computed(() => {
    return relationDisplayMode.value === 'badge' || relationDisplayMode.value === 'both';
  });

  // 初始化显示模式
  const savedMode = localStorage.getItem('relationDisplayMode');
  if (savedMode && ['prefix', 'badge', 'both', 'none'].includes(savedMode)) {
    relationDisplayMode.value = savedMode;
  }

  // 套餐分类树相关数据
  const suitCategoryTree = ref<SuitCategoryTree>({
    categories: [],
    expandedKeys: new Set<string>(),
    selectedKeys: new Set<string>(),
    loadingKeys: new Set<string>(),
    searchKeyword: '',
    filteredTree: [],
  });

  const suitTreeLoading = ref(false);
  const suitSearchKeyword = ref('');
  const selectedSuit = ref<SuitNode | null>(null);
  const suitTreeVisible = ref(false); // 控制套餐分类树面板的显示/隐藏

  // 性能优化相关状态
  const suitTreeCache = ref(new Map<string, { data: SuitNode[]; timestamp: number }>()); // 分类套餐缓存
  const CACHE_DURATION = 3 * 60 * 1000; // 缓存3分钟
  const loadingStates = ref(new Map<string, boolean>()); // 各分类的加载状态
  const errorStates = ref(new Map<string, string>()); // 各分类的错误状态

  /**接收并监听身份证信息，据此进行查询*/
  const idcData = inject<IdcData>('idCardDataKey', {
    data: {},
    ok: false,
    msg: '',
    state: '',
    action: '',
  });
  const sendIdCardCmd = inject('idCardSendMethod', (cmd) => {});
  const idcDataSource = computed(() => ({
    data: idcData.data,
    action: idcData.action,
  }));
  watch(idcDataSource, (val) => {
    if (val && val.data.idCardNo && val.action == 'searchRegList') {
      const idCardNo = val.data.idCardNo; // Store the value in a temporary variable
      queryParam.idCard = idCardNo; // Assign the value to queryParam.idCard
      searchQuery();
    }
  });
  function readIdCard4RegList() {
    idcData.data = {};
    idcData.action = 'searchRegList';
    sendIdCardCmd('ReadStart');
  }
  function readIdCard4RegAdd() {
    idcData.data = {};
    idcData.action = 'addReg';
    sendIdCardCmd('ReadStart');
  }

  const rangePresets = ref([
    { label: '今天', value: [dayjs(), dayjs().add(1, 'd')] },
    { label: '昨天', value: [dayjs().add(-1, 'd'), dayjs().add(-1, 'd')] },
    { label: '过去一周', value: [dayjs().add(-7, 'd'), dayjs()] },
    { label: '过去二周', value: [dayjs().add(-14, 'd'), dayjs()] },
    { label: '过去30天', value: [dayjs().add(-30, 'd'), dayjs()] },
    { label: '过去90天', value: [dayjs().add(-90, 'd'), dayjs()] },
    { label: '过去一年', value: [dayjs().add(-1, 'y'), dayjs()] },
    { label: '过去两年', value: [dayjs().add(-2, 'y'), dayjs()] },
    { label: '过去二十年', value: [dayjs().add(-20, 'y'), dayjs()] },
  ]);

  /**表格相关操作*/

  const currentRow = ref<any>({});
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      showIndexColumn: false,
      showTableSetting: true,
      api: list,
      columns: columnsLite,
      canResize: true,
      canColDrag: true,
      showHeader: true,
      useSearchForm: false,
      clickToRowSelect: false,
      size: 'small',
      showActionColumn: false,
      scroll: { y: '50vh' },
      striped: true,
      customRow: (record) => {
        return {
          onClick: async () => {
            currentRow.value = record;
            emit('rowClick', record);

            // 当选中登记记录时，获取该记录的项目关系信息
            if (record.id) {
              try {
                await fetchItemGroupWithDependencyAnalysis(record.id);
                console.log('✅ 已获取登记记录的项目关系信息:', record.id);
              } catch (error) {
                console.error('❌ 获取登记记录项目关系信息失败:', error);
              }
            }
          },
        };
      },
      rowClassName: (record) => {
        return currentRow.value && currentRow.value.id === record.id ? 'row-selected' : '';
      },
      beforeFetch: (params) => {
        if (regDateRange.value && regDateRange.value.length == 2) {
          queryParam.regTimeStart = regDateRange.value[0].format('YYYY-MM-DD') + ' 00:00:00';
          queryParam.regTimeEnd = regDateRange.value[1].format('YYYY-MM-DD') + ' 23:59:59';
        }
        queryParam.examCategory = '职业病体检';
        queryParam.examCategoryOperator = 'EQ';
        // 确保查询参数中包含职业病体检的过滤条件
        return Object.assign(params, queryParam);
      },
    },
  });
  const [
    registerTable,
    { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource },
    { rowSelection, selectedRowKeys, selectedRows },
  ] = tableContext;

  const teamList = ref<any[]>([]);
  const labelCol = reactive({
    xs: 0,
    sm: 0,
    xl: 0,
    xxl: 0,
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 24,
    xl: 24,
    xxl: 24,
  });

  function getTeamList(companyRegId) {
    if (!companyRegId) {
      teamList.value = [];
      queryParam.teamId = '';
      return;
    }
    teamList.value = [];
    queryParam.teamId = '';
    companyTeamList({ companyRegId: companyRegId, pageSize: 10000 }).then((res) => {
      teamList.value = res.records;
    });
  }

  /**打印*/
  const batchPrintContent = ref(['导引单']);
  function handleBatchPrint() {
    if (selectedRows.value.length > 0) {
      let validArr = selectedRows.value.filter((item) => item.status == '已登记');
      if (validArr.length == 0) {
        message.error('没有符合条件的数据！');
        return;
      }
      emit('batchPrint', { regList: selectedRows.value, printTasks: batchPrintContent.value });
    } else {
      message.error('未选择任何数据！');
    }
  }

  /**
   * 新增事件
   */
  function handleAdd() {
    emit('add');
    readIdCard4RegAdd();
  }

  function batchReg(sendFee2His: string = '0') {
    if (selectedRows.value.length > 0) {
      let validArr = selectedRows.value.filter((item) => item.status != '已登记');
      if (validArr.length == 0) {
        message.error('没有符合条件的数据！');
        return;
      }
      createConfirm({
        iconType: 'warning',
        title: '批量登记确认',
        content: `${validArr.length}(共选中${selectedRows.value.length})条符合条件，确认批量登记？`,
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
          let validArr = selectedRows.value.filter((item) => item.status == '未登记' || !item.status);
          let info = {
            sendFee2His: sendFee2His,
            ids: validArr.map((item) => item.id),
          };

          actionLoading.value = true;
          regBatch(info)
            .then((res) => {
              if (res.success) {
                let batchRes = res.result;

                let msg = '';
                if (batchRes.failureCount) {
                  let failReason = batchRes.failureResults.map((item) => {
                    return `${item.item?.name}:${item.reason}`;
                  });

                  msg = `成功${batchRes.successCount}条，失败${batchRes.failureCount}条。<br/>失败原因：${failReason.join('<br/>')}`;
                  createErrorModal({
                    title: `批量登记结果`,
                    content: `<div style="max-height: 50vh;overflow-y: auto;">${msg}</div>`,
                  });
                } else {
                  msg = `成功${batchRes.successCount}条。`;
                  message.success(msg);
                }

                if (batchRes.successCount > 0) {
                  emit('batchRegOk', batchRes.successResults);
                }
                reload();
                selectedRowKeys.value = [];
                selectedRows.value = [];
              } else {
                message.error('登记失败');
              }
            })
            .finally(() => {
              actionLoading.value = false;
            });
        },
      });
    } else {
      message.error('登记信息为空！');
    }
  }

  function sendFee() {
    if (selectedRows.value.length > 0) {
      let validArr = selectedRows.value.filter((item) => item.paymentState != '已支付' && item.status == '已登记');
      if (validArr.length == 0) {
        message.error('没有符合条件的数据！');
        return;
      }

      createConfirm({
        iconType: 'warning',
        title: '发送收费申请',
        content: `${validArr.length}(共选中${selectedRows.value.length})条符合条件，确认批量发送收费申请？`,
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
          let info = {
            ids: validArr.map((item) => item.id),
          };

          actionLoading.value = true;
          sendFee2HisByIds(info)
            .then((res) => {
              if (res.success) {
                let batchRes = res.result;

                let msg = '';
                if (batchRes.failureCount) {
                  let failReason = batchRes.failureResults.map((item) => {
                    return `${item.item?.name}:${item.reason}`;
                  });

                  msg = `成功${batchRes.successCount}条，失败${batchRes.failureCount}条。<br/>失败原因：${failReason.join('<br/>')}`;
                  createErrorModal({
                    title: `发送收费申请结果`,
                    content: `<div style="max-height: 50vh;overflow-y: auto;">${msg}</div>`,
                  });
                } else {
                  msg = `成功${batchRes.successCount}条。`;
                  message.success(msg);
                }

                reload();
                selectedRowKeys.value = [];
                selectedRows.value = [];
              } else {
                message.error('发送失败');
              }
            })
            .finally(() => {
              actionLoading.value = false;
            });
        },
      });
    } else {
      message.error('登记信息为空！');
    }
  }

  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    if (selectedRowKeys.value.length === 0) {
      message.error('未选择任何数据！');
      return;
    }
    actionLoading.value = true;
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
    actionLoading.value = false;
    //如果currentRow被删除，清空currentRow
    if (selectedRowKeys.value.includes(currentRow.value.id)) {
      currentRow.value = {};
      emit('rowClick', {});
    }
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }

  /**
   * 查询
   */
  async function searchQuery() {
    await reload();
    //如果只有一条数据，自动选中，并发送selectChange
    let dataSource = getDataSource();
    if (dataSource.length === 1) {
      currentRow.value = dataSource[0];
      emit('rowClick', dataSource[0]);
    } else if (dataSource.length === 0) {
      //emit('selectChange', {});
    }
  }

  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    selectedRowKeys.value = [];
    // 重置时保持职业病体检的过滤条件
    queryParam.examCategory = '职业病体检';
    //刷新数据
    reload();
  }

  function reset() {
    formRef.value.resetFields();
    selectedRowKeys.value = [];
    // 重置时保持职业病体检的过滤条件
    queryParam.examCategory = '职业病体检';
    //将时间范围设置为今天
    regDateRange.value = [dayjs(), dayjs()];
    //刷新数据
    reload();
  }

  function reloadPage() {
    reload();
    selectedRows.value = [];
    selectedRowKeys.value = [];
  }

  async function reloadAndSelect(value) {
    await reload();
    if (value && value.id) {
      let dataSource = getDataSource();
      currentRow.value = dataSource.find((item) => item.id === value.id) || {};
      emit('rowClick', currentRow.value);
    }
  }

  // 项目关系显示逻辑函数
  function getItemSourceType(itemGroupId) {
    const item = getDataSource().find((item) => item.itemGroupId === itemGroupId);
    if (!item) {
      return 'main';
    }

    // 优先使用后端返回的sourceType
    if (item.sourceType) {
      return item.sourceType;
    }

    // 降级到前端判断逻辑
    if (item.itemSuitId && item.itemSuitName) {
      return 'suit';
    }
    if (item.giveAwayFlag === '1') {
      return 'gift';
    }
    if (item.attachBaseId) {
      return 'attach';
    }
    if (item.parentGroupId) {
      return 'dependent';
    }

    const asyncResult = itemSourceMap.value.get(itemGroupId);
    if (asyncResult) {
      return asyncResult;
    }

    return 'main';
  }

  function getItemRelationPrefix(itemGroupId) {
    if (relationDisplayMode.value === 'badge' || relationDisplayMode.value === 'none') {
      return null;
    }

    const sourceType = getItemSourceType(itemGroupId);
    const prefixStyles = {
      tree: { dependent: '├─', gift: '├─', attach: '├─', suit: '├─' },
    };

    const currentStyle = 'tree';
    const prefixText = prefixStyles[currentStyle];

    switch (sourceType) {
      case 'dependent':
        return {
          text: prefixText.dependent,
          color: '#fa8c16',
          title: '依赖项目：由其他项目的依赖关系自动添加',
        };
      case 'gift':
        return {
          text: prefixText.gift,
          color: '#52c41a',
          title: '赠送项目：由其他项目的赠送关系自动添加',
        };
      case 'attach':
        return {
          text: prefixText.attach,
          color: '#722ed1',
          title: '附属项目：由其他项目的附属关系自动添加',
        };
      case 'suit':
        return {
          text: prefixText.suit,
          color: '#1890ff',
          title: '套餐项目：来自套餐配置',
        };
      default:
        return null;
    }
  }

  function getItemSourceBadge(itemGroupId) {
    const sourceType = getItemSourceType(itemGroupId);

    switch (sourceType) {
      case 'dependent':
        return {
          text: '依赖',
          bg: '#fa8c16',
          color: '#fff',
          title: '此项目是依赖项目，由其他项目的依赖关系自动添加，点击查看详情',
        };
      case 'gift':
        return {
          text: '赠送',
          bg: '#52c41a',
          color: '#fff',
          title: '此项目是赠送项目，由其他项目的赠送关系自动添加，点击查看详情',
        };
      case 'attach':
        return {
          text: '附属',
          bg: '#722ed1',
          color: '#fff',
          title: '此项目是附属项目，由其他项目的附属关系自动添加，点击查看详情',
        };
      case 'suit':
        return {
          text: '套餐',
          bg: '#1890ff',
          color: '#fff',
          title: '此项目来自套餐配置，点击查看详情',
        };
      default:
        return null;
    }
  }

  function formatItemDisplayName(itemName, checkPartName) {
    if (checkPartName && checkPartName.trim() !== '') {
      return `${itemName} (${checkPartName})`;
    }
    return itemName;
  }

  // 依赖项目提示功能
  async function handleQuickAddAllDependencies() {
    if (missingDependencies.value.length === 0) {
      message.warning('没有缺失的依赖项目');
      return;
    }

    addingDependencies.value = true;
    try {
      // 这里应该调用添加依赖项目的API
      // 由于当前组件主要是显示登记列表，暂时只显示提示信息
      message.info('依赖项目添加功能需要在项目管理面板中操作');

      // 清空依赖项目提示（模拟添加成功）
      missingDependencies.value = [];
    } catch (error) {
      console.error('添加依赖项目失败:', error);
      message.error('添加依赖项目失败: ' + (error.message || '未知错误'));
    } finally {
      addingDependencies.value = false;
    }
  }

  // 获取项目关系数据（用于支持依赖分析）
  async function fetchItemGroupWithDependencyAnalysis(regId) {
    try {
      console.log('🚀 调用后端统一依赖分析接口，登记ID:', regId);
      const response = await getItemGroupWithDependencyAnalysis({ regId: regId });
      const analysisResult = response.result || response;

      if (analysisResult && analysisResult.items) {
        // 设置依赖关系摘要
        const summary = analysisResult.summary || {};
        missingDependencies.value = summary.missingDependencies || [];

        // 设置项目来源分析结果
        const sourceMap = new Map();
        analysisResult.items.forEach((item) => {
          if (item.sourceType) {
            sourceMap.set(item.itemGroupId, item.sourceType);
          }
        });
        itemSourceMap.value = sourceMap;

        console.log('✅ 后端依赖关系分析完成');
        console.log('   - 项目数量:', analysisResult.items.length);
        console.log('   - 缺失依赖项目数量:', missingDependencies.value.length);

        return analysisResult.items;
      } else {
        console.error('❌ 后端接口返回数据格式异常:', analysisResult);
        return [];
      }
    } catch (error) {
      console.error('❌ 获取项目依赖关系分析失败:', error);
      missingDependencies.value = [];
      itemSourceMap.value = new Map();
      return [];
    }
  }

  // 项目关系详情模态框功能
  async function showItemRelationDetail(itemGroupId) {
    const currentItem = getDataSource().find((item) => item.itemGroupId === itemGroupId);
    if (!currentItem) {
      message.warning('未找到项目信息');
      return;
    }

    relationDetailModal.value.visible = true;
    relationDetailModal.value.loading = true;
    relationDetailModal.value.itemInfo = currentItem;
    relationDetailModal.value.relationData = null;

    try {
      // 获取项目关系数据
      const relationData = await getItemRelations(itemGroupId);

      // 分析当前项目的来源
      const sourceType = getItemSourceType(itemGroupId);

      // 模拟关系数据（实际应该从API获取）
      relationDetailModal.value.relationData = {
        sourceInfo: {
          type: sourceType,
          mainItem: currentItem,
          relationDetail: {},
        },
      };
    } catch (error) {
      console.error('获取项目关系详情失败:', error);
      message.error('获取项目关系详情失败');
    } finally {
      relationDetailModal.value.loading = false;
    }
  }

  function closeRelationDetailModal() {
    relationDetailModal.value.visible = false;
    relationDetailModal.value.itemInfo = null;
    relationDetailModal.value.relationData = null;
  }

  // 套餐分类树相关方法
  async function loadSuitCategories(): Promise<SuitCategoryNode[]> {
    try {
      suitTreeLoading.value = true;
      const response = await listSuitCategories({ pageSize: 1000 });

      if (response.success && response.result?.records) {
        const categories = response.result.records.map((category) => ({
          id: category.id,
          name: category.name,
          code: category.code,
          description: category.description,
          sort: category.sort || 0,
          parentId: category.parentId,
          level: 1, // 暂时设为1级，后续可支持多级
          isExpanded: false,
          isLoading: false,
          children: [],
          childrenLoaded: false,
          nodeType: 'category' as const,
        }));

        // 按排序号排序
        categories.sort((a, b) => (a.sort || 0) - (b.sort || 0));

        suitCategoryTree.value.categories = categories;
        return categories;
      } else {
        console.error('获取套餐分类失败:', response.message);
        message.error('获取套餐分类失败: ' + (response.message || '未知错误'));
        return [];
      }
    } catch (error) {
      console.error('加载套餐分类异常:', error);
      message.error('加载套餐分类失败');
      return [];
    } finally {
      suitTreeLoading.value = false;
    }
  }

  async function loadSuitsByCategory(categoryId: string): Promise<SuitNode[]> {
    // 检查缓存
    const cached = suitTreeCache.value.get(categoryId);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      console.log(`使用缓存数据加载分类 ${categoryId} 的套餐`);
      return [...cached.data]; // 返回副本避免修改缓存
    }

    try {
      loadingStates.value.set(categoryId, true);
      errorStates.value.delete(categoryId);

      const response = await listSuits({
        categoryId: categoryId,
        pageSize: 1000,
        enableFlag: '1', // 只加载启用的套餐
      });

      if (response.success && response.result?.records) {
        const suits = response.result.records.map((suit) => ({
          id: suit.id,
          name: suit.name,
          code: suit.code,
          description: suit.description,
          price: suit.price,
          categoryId: suit.categoryId,
          categoryName: suit.categoryName,
          enableFlag: suit.enableFlag,
          sort: suit.sort || 0,
          itemCount: suit.itemCount,
          isSelected: false,
          nodeType: 'suit' as const,
        }));

        // 按排序号排序
        suits.sort((a, b) => (a.sort || 0) - (b.sort || 0));

        // 缓存结果
        suitTreeCache.value.set(categoryId, {
          data: [...suits], // 存储副本
          timestamp: Date.now(),
        });

        console.log(`成功加载分类 ${categoryId} 的 ${suits.length} 个套餐`);
        return suits;
      } else {
        const errorMsg = response.message || '获取套餐列表失败';
        console.error('获取套餐列表失败:', errorMsg);
        errorStates.value.set(categoryId, errorMsg);
        return [];
      }
    } catch (error) {
      const errorMsg = error.message || '加载套餐列表异常';
      console.error('加载套餐列表异常:', error);
      errorStates.value.set(categoryId, errorMsg);
      return [];
    } finally {
      loadingStates.value.set(categoryId, false);
    }
  }

  async function searchSuits(keyword: string): Promise<SuitNode[]> {
    try {
      const response = await listSuitByKeyword({
        keyword: keyword,
        pageSize: 100,
        enableFlag: '1',
      });

      if (response.success && response.result) {
        return response.result.map((suit) => ({
          id: suit.id,
          name: suit.name,
          code: suit.code,
          description: suit.description,
          price: suit.price,
          categoryId: suit.categoryId,
          categoryName: suit.categoryName,
          enableFlag: suit.enableFlag,
          sort: suit.sort || 0,
          itemCount: suit.itemCount,
          isSelected: false,
          nodeType: 'suit' as const,
        }));
      } else {
        return [];
      }
    } catch (error) {
      console.error('搜索套餐异常:', error);
      return [];
    }
  }

  // 套餐分类树数据管理方法
  function toggleCategoryExpansion(categoryId: string) {
    const category = suitCategoryTree.value.categories.find((cat) => cat.id === categoryId);
    if (!category) return;

    if (category.isExpanded) {
      // 折叠分类
      category.isExpanded = false;
      suitCategoryTree.value.expandedKeys.delete(categoryId);
    } else {
      // 展开分类
      if (!category.childrenLoaded) {
        // 需要加载子套餐
        loadCategorySuits(categoryId);
      } else {
        category.isExpanded = true;
        suitCategoryTree.value.expandedKeys.add(categoryId);
      }
    }
  }

  async function loadCategorySuits(categoryId: string) {
    const category = suitCategoryTree.value.categories.find((cat) => cat.id === categoryId);
    if (!category) return;

    category.isLoading = true;
    suitCategoryTree.value.loadingKeys.add(categoryId);

    try {
      const suits = await loadSuitsByCategory(categoryId);
      category.children = suits;
      category.childrenLoaded = true;
      category.isExpanded = true;
      suitCategoryTree.value.expandedKeys.add(categoryId);
    } catch (error) {
      console.error('加载分类套餐失败:', error);
      message.error('加载分类套餐失败');
    } finally {
      category.isLoading = false;
      suitCategoryTree.value.loadingKeys.delete(categoryId);
    }
  }

  function selectSuit(suitId: string) {
    // 清除之前的选择
    suitCategoryTree.value.selectedKeys.clear();

    // 查找并选中套餐
    for (const category of suitCategoryTree.value.categories) {
      const suit = category.children.find((s) => s.id === suitId);
      if (suit) {
        suit.isSelected = true;
        selectedSuit.value = suit;
        suitCategoryTree.value.selectedKeys.add(suitId);
        break;
      }
    }
  }

  function clearSuitSelection() {
    suitCategoryTree.value.selectedKeys.clear();
    selectedSuit.value = null;

    // 清除所有套餐的选中状态
    for (const category of suitCategoryTree.value.categories) {
      for (const suit of category.children) {
        suit.isSelected = false;
      }
    }
  }

  function filterTree(keyword: string): SuitCategoryNode[] {
    if (!keyword.trim()) {
      return [...suitCategoryTree.value.categories];
    }

    const filtered: SuitCategoryNode[] = [];
    const lowerKeyword = keyword.toLowerCase();

    for (const category of suitCategoryTree.value.categories) {
      const matchingSuits = category.children.filter(
        (suit) =>
          suit.name.toLowerCase().includes(lowerKeyword) ||
          (suit.code && suit.code.toLowerCase().includes(lowerKeyword)) ||
          (suit.description && suit.description.toLowerCase().includes(lowerKeyword))
      );

      if (matchingSuits.length > 0) {
        const filteredCategory: SuitCategoryNode = {
          ...category,
          children: matchingSuits,
          isExpanded: true, // 搜索时自动展开包含结果的分类
        };
        filtered.push(filteredCategory);
      }
    }

    return filtered;
  }

  // 搜索处理（防抖）
  const handleSuitSearch = debounce(async (keyword: string) => {
    suitSearchKeyword.value = keyword;
    suitCategoryTree.value.searchKeyword = keyword;

    if (keyword.trim()) {
      // 如果有搜索关键字，使用搜索API
      try {
        const searchResults = await searchSuits(keyword);

        // 将搜索结果按分类组织
        const categoryMap = new Map<string, SuitNode[]>();
        for (const suit of searchResults) {
          if (!categoryMap.has(suit.categoryId)) {
            categoryMap.set(suit.categoryId, []);
          }
          categoryMap.get(suit.categoryId)!.push(suit);
        }

        // 构建过滤后的树结构
        const filteredCategories: SuitCategoryNode[] = [];
        for (const category of suitCategoryTree.value.categories) {
          const suits = categoryMap.get(category.id) || [];
          if (suits.length > 0) {
            filteredCategories.push({
              ...category,
              children: suits,
              isExpanded: true,
              childrenLoaded: true,
            });
          }
        }

        suitCategoryTree.value.filteredTree = filteredCategories;
      } catch (error) {
        console.error('搜索套餐失败:', error);
        suitCategoryTree.value.filteredTree = [];
      }
    } else {
      // 清空搜索，显示完整树结构
      suitCategoryTree.value.filteredTree = [...suitCategoryTree.value.categories];
    }
  }, 300);

  // 初始化套餐分类树
  async function initSuitCategoryTree() {
    try {
      await loadSuitCategories();
      suitCategoryTree.value.filteredTree = [...suitCategoryTree.value.categories];
    } catch (error) {
      console.error('初始化套餐分类树失败:', error);
    }
  }

  // 套餐分类树UI交互方法
  function toggleSuitTreePanel() {
    suitTreeVisible.value = !suitTreeVisible.value;

    // 首次打开时初始化数据
    if (suitTreeVisible.value && suitCategoryTree.value.categories.length === 0) {
      initSuitCategoryTree();
    }
  }

  function handleSuitNodeClick(suit: SuitNode) {
    if (suit.enableFlag !== '1') {
      message.warning('该套餐已禁用，无法选择');
      return;
    }

    selectSuit(suit.id);
  }

  function handleSuitDoubleClick(suit: SuitNode) {
    if (suit.enableFlag !== '1') {
      message.warning('该套餐已禁用，无法添加');
      return;
    }

    selectSuit(suit.id);
    handleAddSelectedSuit();
  }

  async function handleAddSelectedSuit() {
    if (!selectedSuit.value) {
      message.warning('请先选择套餐');
      return;
    }

    if (!currentRow.value || !currentRow.value.id) {
      message.warning('请先选择体检登记记录');
      return;
    }

    try {
      await addSuitToRegistration(selectedSuit.value, currentRow.value.id);
    } catch (error) {
      console.error('添加套餐失败:', error);
      message.error('添加套餐失败: ' + (error.message || '未知错误'));
    }
  }

  // 添加套餐到登记记录的核心方法
  async function addSuitToRegistration(suit: SuitNode, regId: string) {
    try {
      // 显示确认对话框
      const { createConfirm } = useMessage();

      createConfirm({
        iconType: 'warning',
        title: '添加套餐确认',
        content: `确定要将套餐"${suit.name}"添加到当前登记记录吗？套餐内的项目将与现有项目合并。`,
        okText: '确认添加',
        cancelText: '取消',
        onOk: async () => {
          await proceedAddSuit(suit, regId);
        },
      });
    } catch (error) {
      throw error;
    }
  }

  // 执行套餐添加的具体逻辑
  async function proceedAddSuit(suit: SuitNode, regId: string) {
    try {
      // 1. 获取套餐内的项目列表
      const suitItemsResponse = await getGroupOfSuit({ suitId: suit.id });

      if (!suitItemsResponse || suitItemsResponse.length === 0) {
        message.warning('该套餐暂无项目，无法添加');
        return;
      }

      // 2. 检查项目适用性和重复性
      let addList = [];
      let unsuitableItems = [];
      let duplicateItems = [];

      // 获取当前登记记录的现有项目（这里需要根据实际情况调整）
      const existingItems = getDataSource(); // 假设这能获取到当前的项目列表

      for (const group of suitItemsResponse) {
        // 检查是否重复
        const isDuplicate = existingItems.some(
          (existing) => existing.itemGroupId === group.itemGroupId && existing.checkPartId === group.checkPartId
        );

        if (isDuplicate) {
          duplicateItems.push(group.name);
          continue;
        }

        // 构建要添加的项目数据
        const itemData = {
          regId: regId,
          itemGroupId: group.itemGroupId,
          itemGroupName: group.name,
          itemSuitId: suit.id,
          itemSuitName: suit.name,
          priceAfterDis: group.priceAfterDisOfSuit || group.priceAfterDis,
          minDiscountRate: group.minDiscountRateOfSuit || group.minDiscountRate,
          disRate: group.disRateOfSuit || group.disRate,
          priceDisDiffAmount: group.priceDisDiffAmountOfSuit || group.priceDisDiffAmount,
          checkPartId: group.checkPartId,
          checkPartName: group.checkPartName,
          checkPartCode: group.checkPartCode,
        };

        addList.push(itemData);
      }

      // 3. 处理重复项目提示
      if (duplicateItems.length > 0) {
        message.warning(`以下项目已存在，将跳过添加：${duplicateItems.join(', ')}`);
      }

      // 4. 添加项目
      if (addList.length > 0) {
        // 这里应该调用专门的套餐添加API
        // const result = await addCustomerRegForSuit(addList);

        // 暂时模拟成功
        message.success(`成功添加套餐"${suit.name}"，包含 ${addList.length} 个项目`);

        // 清除选择并刷新
        clearSuitSelection();

        // 如果当前选中的登记记录就是添加套餐的记录，触发项目关系分析
        if (currentRow.value && currentRow.value.id === regId) {
          await fetchItemGroupWithDependencyAnalysis(regId);
        }

        // 触发父组件刷新
        emit('suitAdded', { suit, regId, addedItems: addList });
      } else {
        message.warning('没有可添加的项目');
      }
    } catch (error) {
      console.error('添加套餐项目失败:', error);
      message.error('添加套餐失败: ' + (error.message || '未知错误'));
      throw error;
    }
  }

  // 批量套餐操作
  async function batchAddSuits(suits: SuitNode[], regId: string) {
    if (!suits || suits.length === 0) {
      message.warning('请选择要添加的套餐');
      return;
    }

    try {
      for (const suit of suits) {
        await proceedAddSuit(suit, regId);
      }
      message.success(`批量添加 ${suits.length} 个套餐完成`);
    } catch (error) {
      console.error('批量添加套餐失败:', error);
      message.error('批量添加套餐失败');
    }
  }

  // 搜索关键字高亮显示
  function highlightSearchKeyword(text: string): string {
    if (!text || !suitSearchKeyword.value.trim()) {
      return text;
    }

    const keyword = suitSearchKeyword.value.trim();
    const regex = new RegExp(`(${keyword})`, 'gi');
    return text.replace(regex, '<mark style="background-color: #fff3cd; padding: 0 2px;">$1</mark>');
  }

  // 增强的搜索功能
  function enhancedSuitSearch(keyword: string) {
    const trimmedKeyword = keyword.trim().toLowerCase();

    if (!trimmedKeyword) {
      // 清空搜索时恢复原始树结构
      suitCategoryTree.value.filteredTree = [...suitCategoryTree.value.categories];
      return;
    }

    // 在已加载的分类中进行本地搜索
    const localResults: SuitCategoryNode[] = [];

    for (const category of suitCategoryTree.value.categories) {
      if (category.childrenLoaded && category.children.length > 0) {
        const matchingSuits = category.children.filter(
          (suit) =>
            suit.name.toLowerCase().includes(trimmedKeyword) ||
            (suit.code && suit.code.toLowerCase().includes(trimmedKeyword)) ||
            (suit.description && suit.description.toLowerCase().includes(trimmedKeyword))
        );

        if (matchingSuits.length > 0) {
          localResults.push({
            ...category,
            children: matchingSuits,
            isExpanded: true,
          });
        }
      }
    }

    // 如果本地搜索有结果，优先显示本地结果
    if (localResults.length > 0) {
      suitCategoryTree.value.filteredTree = localResults;
    } else {
      // 否则调用远程搜索
      handleSuitSearch(keyword);
    }
  }

  // 清空搜索
  function clearSuitSearch() {
    suitSearchKeyword.value = '';
    suitCategoryTree.value.searchKeyword = '';
    suitCategoryTree.value.filteredTree = [...suitCategoryTree.value.categories];
  }

  // 获取搜索统计信息
  const searchStats = computed(() => {
    if (!suitSearchKeyword.value.trim()) {
      return null;
    }

    let totalSuits = 0;
    let matchedCategories = 0;

    for (const category of suitCategoryTree.value.filteredTree) {
      if (category.children.length > 0) {
        totalSuits += category.children.length;
        matchedCategories++;
      }
    }

    return {
      totalSuits,
      matchedCategories,
      keyword: suitSearchKeyword.value.trim(),
    };
  });

  // 缓存管理功能
  function clearSuitTreeCache() {
    suitTreeCache.value.clear();
    loadingStates.value.clear();
    errorStates.value.clear();
    console.log('套餐分类树缓存已清空');
  }

  function refreshSuitCategory(categoryId: string) {
    // 清除指定分类的缓存
    suitTreeCache.value.delete(categoryId);
    errorStates.value.delete(categoryId);

    // 重新加载该分类
    const category = suitCategoryTree.value.categories.find((cat) => cat.id === categoryId);
    if (category) {
      category.childrenLoaded = false;
      category.children = [];
      if (category.isExpanded) {
        loadCategorySuits(categoryId);
      }
    }
  }

  // 批量预加载热门分类的套餐
  async function preloadPopularCategories() {
    const popularCategoryIds = suitCategoryTree.value.categories
      .slice(0, 3) // 预加载前3个分类
      .map((cat) => cat.id);

    for (const categoryId of popularCategoryIds) {
      if (!suitTreeCache.value.has(categoryId)) {
        try {
          await loadSuitsByCategory(categoryId);
        } catch (error) {
          console.warn(`预加载分类 ${categoryId} 失败:`, error);
        }
      }
    }
  }

  // 性能监控
  const performanceStats = computed(() => {
    const totalCategories = suitCategoryTree.value.categories.length;
    const loadedCategories = suitCategoryTree.value.categories.filter((cat) => cat.childrenLoaded).length;
    const cachedCategories = suitTreeCache.value.size;
    const errorCategories = errorStates.value.size;

    return {
      totalCategories,
      loadedCategories,
      cachedCategories,
      errorCategories,
      loadingProgress: totalCategories > 0 ? Math.round((loadedCategories / totalCategories) * 100) : 0,
    };
  });

  // 用户体验优化
  const userExperienceFeatures = {
    // 键盘导航支持
    handleKeyNavigation: (event: KeyboardEvent) => {
      // 实现键盘导航逻辑
      switch (event.key) {
        case 'ArrowUp':
        case 'ArrowDown':
          // 上下箭头导航
          event.preventDefault();
          break;
        case 'ArrowLeft':
        case 'ArrowRight':
          // 左右箭头展开/折叠
          event.preventDefault();
          break;
        case 'Enter':
          // 回车选择
          event.preventDefault();
          break;
      }
    },

    // 自动保存用户偏好
    saveUserPreferences: () => {
      const preferences = {
        expandedCategories: Array.from(suitCategoryTree.value.expandedKeys),
        lastSearchKeyword: suitSearchKeyword.value,
        treeVisible: suitTreeVisible.value,
      };
      localStorage.setItem('suitTreePreferences', JSON.stringify(preferences));
    },

    // 恢复用户偏好
    restoreUserPreferences: () => {
      try {
        const saved = localStorage.getItem('suitTreePreferences');
        if (saved) {
          const preferences = JSON.parse(saved);
          suitCategoryTree.value.expandedKeys = new Set(preferences.expandedCategories || []);
          suitSearchKeyword.value = preferences.lastSearchKeyword || '';
          suitTreeVisible.value = preferences.treeVisible || false;
        }
      } catch (error) {
        console.warn('恢复用户偏好失败:', error);
      }
    },
  };

  // 组件挂载时的初始化
  onMounted(() => {
    // 恢复用户偏好
    userExperienceFeatures.restoreUserPreferences();

    // 如果树面板可见，初始化数据
    if (suitTreeVisible.value) {
      initSuitCategoryTree().then(() => {
        // 预加载热门分类
        preloadPopularCategories();
      });
    }
  });

  // 组件卸载时保存用户偏好
  onBeforeUnmount(() => {
    userExperienceFeatures.saveUserPreferences();
  });

  defineExpose({
    searchQuery,
    reloadPage,
    reloadAndSelect,
    reset,
    clearSuitTreeCache,
    refreshSuitCategory,
    performanceStats,
  });
</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 12px;
      white-space: nowrap;
    }
    .query-group-cust {
      min-width: 100px !important;
    }
    .query-group-split-cust {
      width: 30px;
      display: inline-block;
      text-align: center;
    }
  }

  :deep(.row-selected td:first-child) {
    border-left: solid 5px v-bind('token.colorPrimary');
  }

  /* 套餐分类树样式 */
  .suit-category-panel {
    .suit-category-tree {
      .category-node {
        padding: 8px 12px;
        cursor: pointer;
        border-radius: 4px;
        transition: all 0.2s;
        user-select: none;

        &:hover {
          background-color: #f0f9ff;
        }

        &.expanded {
          background-color: #e6f7ff;
        }

        .category-content {
          display: flex;
          align-items: center;
          gap: 8px;

          .category-icon {
            font-size: 16px;
            color: #1890ff;
          }

          .category-name {
            font-weight: bold;
            font-size: 14px;
            color: #1890ff;
            flex: 1;
          }

          .category-count {
            font-size: 12px;
            color: #999;
          }
        }
      }

      .suits-container {
        margin-left: 24px;
        border-left: 1px dashed #d9d9d9;
        padding-left: 12px;

        .suit-node {
          padding: 6px 12px;
          cursor: pointer;
          border-radius: 4px;
          transition: all 0.2s;
          margin-bottom: 2px;

          &:hover {
            background-color: #f5f5f5;
          }

          &.selected {
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
          }

          &.disabled {
            opacity: 0.5;
            cursor: not-allowed;

            &:hover {
              background-color: transparent;
            }
          }

          .suit-content {
            display: flex;
            align-items: center;
            gap: 8px;

            .suit-icon {
              font-size: 14px;
              color: #52c41a;
              flex-shrink: 0;
            }

            .suit-info {
              flex: 1;
              min-width: 0;

              .suit-name {
                font-size: 13px;
                color: #333;
                font-weight: 500;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              .suit-meta {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-top: 2px;

                .suit-code {
                  font-size: 11px;
                  color: #999;
                  background: #f5f5f5;
                  padding: 1px 4px;
                  border-radius: 2px;
                }

                .suit-price {
                  font-size: 11px;
                  color: #fa8c16;
                  font-weight: 500;
                }
              }
            }
          }
        }
      }
    }

    .empty-state {
      text-align: center;
      padding: 40px 20px;
      color: #999;
    }
  }
</style>
