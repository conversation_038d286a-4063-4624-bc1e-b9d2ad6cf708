<template>
  <a-card title="放射接触史" size="small">
    <template #extra>
      <a-button type="primary" @click="handleAdd" :disabled="disabled">
        <template #icon>
          <plus-outlined />
        </template>
        添加放射接触史记录
      </a-button>
    </template>

    <div class="radiation-history-list">
      <div v-if="!dataSource.length && !loading" class="empty-state">
        <a-empty description="暂无放射接触史记录" />
      </div>

      <a-spin :spinning="loading">
        <div class="inline-form-list">
          <div
            v-for="(record, index) in dataSource"
            :key="record.uuid || record.id || `new_${index}`"
            class="form-item-row"
            :class="{ editing: record._editMode, 'new-record': record._isNew }"
          >
            <a-form :ref="(el) => setFormRef(el, index)" :model="record" layout="horizontal" class="card-form">
              <div class="form-content">
                <a-row :gutter="[16, 16]">
                  <a-col :span="6">
                    <a-form-item
                      label="就职单位"
                      name="company"
                      :rules="[{ required: true, message: '请输入就职单位', trigger: 'submit' }]"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                      class="form-field"
                    >
                      <AutoComplete
                        category="occu_company"
                        v-if="record._editMode"
                        v-model:value="record.company"
                        placeholder="请输入就职单位"
                        style="width: 180px"
                      />
                      <span v-else class="field-value">{{ record.company || '-' }}</span>
                    </a-form-item>
                  </a-col>

                  <a-col :span="6">
                    <a-form-item
                      label="车间"
                      name="workshop"
                      :rules="[{ required: true, message: '请输入车间' }]"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                      class="form-field"
                    >
                      <a-input v-if="record._editMode" v-model:value="record.workshop" placeholder="请输入车间" style="min-width: 120px" />
                      <span v-else class="field-value">{{ record.workshop || '-' }}</span>
                    </a-form-item>
                  </a-col>

                  <a-col :span="6">
                    <a-form-item label="工种" name="workName" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" class="form-field">
                      <a-input v-if="record._editMode" v-model:value="record.workName" placeholder="请输入工种" style="min-width: 120px" />
                      <span v-else class="field-value">{{ record.workName || '-' }}</span>
                    </a-form-item>
                  </a-col>

                  <a-col :span="6">
                    <a-form-item label="接害工龄" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" class="form-field work-duration">
                      <template v-if="record._editMode">
                        <div class="duration-inputs">
                          <a-input-number v-model:value="record.riskYears" placeholder="年" :min="0" style="width: 60px" />
                          <span>年</span>
                          <a-input-number v-model:value="record.riskMonths" placeholder="月" :min="0" :max="11" style="width: 60px" />
                          <span>月</span>
                        </div>
                      </template>
                      <span v-else class="field-value">
                        {{ formatDuration(record.riskYears, record.riskMonths) }}
                      </span>
                    </a-form-item>
                  </a-col>
                </a-row>

                <a-row :gutter="[16, 16]">
                  <a-col :span="6">
                    <a-form-item
                      label="职业照射种类"
                      name="irradiationType"
                      :rules="[{ required: true, message: '请选择职业照射种类' }]"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                      class="form-field"
                    >
                      <a-select
                        v-if="record._editMode"
                        v-model:value="record.irradiationType"
                        placeholder="请选择职业照射种类"
                        mode="multiple"
                        style="min-width: 120px"
                      >
                        <a-select-option value="X射线">X射线</a-select-option>
                        <a-select-option value="γ射线">γ射线</a-select-option>
                        <a-select-option value="β射线">β射线</a-select-option>
                        <a-select-option value="α射线">α射线</a-select-option>
                        <a-select-option value="中子">中子</a-select-option>
                        <a-select-option value="其他">其他</a-select-option>
                      </a-select>
                      <span v-else class="field-value">{{ formatMultipleValues(record.irradiationType) }}</span>
                    </a-form-item>
                  </a-col>

                  <a-col :span="6">
                    <a-form-item
                      label="放射线类型"
                      name="radiationType"
                      :rules="[{ required: true, message: '请选择放射线类型' }]"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                      class="form-field"
                    >
                      <a-select
                        v-if="record._editMode"
                        v-model:value="record.radiationType"
                        placeholder="请选择放射线类型"
                        mode="multiple"
                        style="min-width: 120px"
                      >
                        <a-select-option value="电离辐射">电离辐射</a-select-option>
                        <a-select-option value="非电离辐射">非电离辐射</a-select-option>
                        <a-select-option value="天然辐射">天然辐射</a-select-option>
                        <a-select-option value="人工辐射">人工辐射</a-select-option>
                      </a-select>
                      <span v-else class="field-value">{{ formatMultipleValues(record.radiationType) }}</span>
                    </a-form-item>
                  </a-col>

                  <a-col :span="6">
                    <a-form-item label="特殊情况" name="exceptional" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" class="form-field">
                      <a-input v-if="record._editMode" v-model:value="record.exceptional" placeholder="请输入特殊情况" style="min-width: 120px" />
                      <span v-else class="field-value">{{ record.exceptional || '-' }}</span>
                    </a-form-item>
                  </a-col>

                  <a-col :span="6">
                    <a-form-item label="个人防护" name="personalProtection" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" class="form-field">
                      <a-input
                        v-if="record._editMode"
                        v-model:value="record.personalProtection"
                        placeholder="请输入个人防护情况"
                        style="min-width: 120px"
                      />
                      <span v-else class="field-value">{{ record.personalProtection || '-' }}</span>
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>

              <div class="form-actions">
                <template v-if="record._editMode">
                  <a-button type="primary" size="small" @click="handleSave(record, index)" :loading="record._saving" :disabled="disabled">
                    保存
                  </a-button>
                  <a-button size="small" @click="handleCancel(record, index)" style="margin-left: 8px"> 取消 </a-button>
                </template>
                <template v-else>
                  <a-button type="link" size="small" @click="handleEdit(record, index)" :disabled="disabled"> 编辑 </a-button>
                  <a-popconfirm title="确定要删除这条记录吗？" @confirm="handleDelete(record, index)" :disabled="disabled">
                    <a-button type="link" danger size="small" :disabled="disabled"> 删除 </a-button>
                  </a-popconfirm>
                </template>
              </div>
            </a-form>
          </div>
        </div>
      </a-spin>
    </div>
  </a-card>
</template>

<script lang="ts" setup>
  import { ref, reactive, inject, watch, onMounted } from 'vue';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import { zyInquiryRadiationHistoryList, zyInquiryRadiationHistorySaveOrUpdate, zyInquiryRadiationHistoryDelete } from '../ZyInquiry.api';
  import { buildUUID } from '@/utils/uuid';
  import AutoComplete from '@/components/basicinfo/AutoComplete.vue';

  interface RadiationHistoryRecord {
    id?: string;
    uuid?: string; // 添加UUID字段用于卡片管理
    company: string;
    riskYears?: number;
    riskMonths?: number;
    workshop: string;
    workName: string;
    irradiationType: string[] | string;
    exceptional: string;
    radiationType: string[] | string;
    workload: string;
    cumulative: string;
    overdose: string;
    meter: boolean;
    remark: string;
    inquiryId: string;
    _editMode?: boolean;
    _isNew?: boolean;
    _saving?: boolean;
    _originalData?: any;
  }

  // Props
  interface Props {
    disabled?: boolean;
  }
  const props = withDefaults(defineProps<Props>(), {
    disabled: false,
  });

  // Injected values
  const inquiryMainId = inject('inquiryMainId', ref(''));
  const inquiryReady = inject('inquiryReady', ref(false));

  // State
  const loading = ref(false);
  const dataSource = ref<RadiationHistoryRecord[]>([]);
  const formRefs = ref<any[]>([]);

  // Set form ref for specific index
  const setFormRef = (el: any, index: number) => {
    if (el) {
      formRefs.value[index] = el;
    }
  };

  // Format duration display
  const formatDuration = (years?: number, months?: number) => {
    if (!years && !months) return '-';
    let result = '';
    if (years) result += `${years}年`;
    if (months) result += `${months}月`;
    return result || '-';
  };

  // Format multiple values display
  const formatMultipleValues = (values: string[] | string) => {
    if (!values) return '-';
    if (Array.isArray(values)) {
      return values.join(', ');
    }
    return values;
  };

  // Load data
  async function loadData() {
    if (!inquiryMainId.value) return;

    try {
      loading.value = true;
      const result = await zyInquiryRadiationHistoryList({ inquiryId: inquiryMainId.value });
      if (result && result.records) {
        dataSource.value = result.records.map((item: any) => ({
          ...item,
          uuid: item.uuid || buildUUID(), // 为回显数据生成UUID
          meter: !!item.meter,
          irradiationType: item.irradiationType
            ? typeof item.irradiationType === 'string'
              ? item.irradiationType.split(',')
              : item.irradiationType
            : [],
          radiationType: item.radiationType ? (typeof item.radiationType === 'string' ? item.radiationType.split(',') : item.radiationType) : [],
          _editMode: false,
          _isNew: false,
          _saving: false,
        }));
      } else {
        dataSource.value = [];
      }

      // 如果没有历史记录，自动添加一个空卡片
      if (dataSource.value.length === 0) {
        handleAdd();
      }
    } catch (error) {
      console.error('加载放射接触史记录失败:', error);
      message.error('加载数据失败');
      dataSource.value = [];
    } finally {
      loading.value = false;
    }
  }

  // Add new record
  function handleAdd() {
    const newRecord: RadiationHistoryRecord = {
      uuid: buildUUID(), // 为新记录生成UUID
      company: '',
      riskYears: undefined,
      riskMonths: undefined,
      workshop: '',
      workName: '',
      irradiationType: [],
      exceptional: '',
      radiationType: [],
      workload: '',
      cumulative: '',
      overdose: '',
      meter: false,
      remark: '',
      inquiryId: inquiryMainId.value,
      _editMode: true,
      _isNew: true,
      _saving: false,
    };
    dataSource.value.push(newRecord);
  }

  // Edit record
  function handleEdit(record: RadiationHistoryRecord, index: number) {
    // Store original data for cancel operation
    record._originalData = { ...record };
    record._editMode = true;
  }

  // Save record
  async function handleSave(record: RadiationHistoryRecord, index: number) {
    try {
      // Validate form
      const formRef = formRefs.value[index];
      if (formRef) {
        await formRef.validate();
      }

      record._saving = true;
      const isUpdate = !!record.id;
      const saveData = {
        id: record.id,
        company: record.company,
        riskYears: record.riskYears,
        riskMonths: record.riskMonths,
        workshop: record.workshop,
        workName: record.workName,
        irradiationType: Array.isArray(record.irradiationType) ? record.irradiationType.join(',') : record.irradiationType,
        exceptional: record.exceptional,
        radiationType: Array.isArray(record.radiationType) ? record.radiationType.join(',') : record.radiationType,
        workload: record.workload,
        cumulative: record.cumulative,
        overdose: record.overdose,
        meter: record.meter ? 1 : 0,
        remark: record.remark,
        inquiryId: inquiryMainId.value,
      };

      const result = await zyInquiryRadiationHistorySaveOrUpdate(saveData, isUpdate);
      if (result && result.success) {
        // 处理新增记录的ID更新
        if (!isUpdate && result.result) {
          // 根据后端返回的数据结构更新ID
          if (typeof result.result === 'string') {
            record.id = result.result;
          } else if (result.result.id) {
            record.id = result.result.id;
            // 如果后端返回了完整对象，更新其他字段
            Object.keys(result.result).forEach((key) => {
              if (key !== 'uuid' && record.hasOwnProperty(key)) {
                record[key] = result.result[key];
              }
            });
          }
          console.log(`✅ 新增放射史记录成功，ID: ${record.id}, UUID: ${record.uuid}`);
        } else if (isUpdate) {
          console.log(`✅ 更新放射史记录成功，ID: ${record.id}, UUID: ${record.uuid}`);
        }

        record._editMode = false;
        record._isNew = false;
        record._originalData = undefined;
        message.success(isUpdate ? '更新成功' : '保存成功');
      } else {
        throw new Error(result?.message || '保存失败');
      }
    } catch (error: any) {
      console.error('保存失败:', error);
      message.error(error.message || '保存失败');
    } finally {
      record._saving = false;
    }
  }

  // Cancel edit
  function handleCancel(record: RadiationHistoryRecord, index: number) {
    if (record._isNew) {
      // Remove new record
      dataSource.value.splice(index, 1);
    } else {
      // Restore original data
      if (record._originalData) {
        Object.assign(record, record._originalData);
        record._originalData = undefined;
      }
      record._editMode = false;
    }
  }

  // Delete record
  async function handleDelete(record: RadiationHistoryRecord, index: number) {
    try {
      // 如果是新记录（没有ID），直接从列表中移除
      if (record._isNew || !record.id) {
        console.log(`🗑️ 删除新建的放射史记录，UUID: ${record.uuid}`);
        dataSource.value.splice(index, 1);
        message.success('删除成功');
        return;
      }

      // 如果是已保存的记录，调用后端删除接口
      console.log(`🗑️ 删除放射史记录，ID: ${record.id}, UUID: ${record.uuid}`);
      await zyInquiryRadiationHistoryDelete({ id: record.id }, () => {
        dataSource.value.splice(index, 1);
        message.success('删除成功');
      });
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  }

  // 暴露方法给父组件
  defineExpose({
    loadData,
  });
</script>

<style lang="less" scoped>
  .radiation-history-list {
    .empty-state {
      text-align: center;
      padding: 60px 20px;
      border-radius: 8px;
      border: 1px dashed #d9d9d9;
    }

    .inline-form-list {
      .form-item-row {
        background: #fff;
        border: 1px solid #e8e8e8;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 16px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
        }

        &.editing {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        &.new-record {
          border-color: #52c41a;
          background: #f6ffed;
        }

        .card-form {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }

        .form-content {
          width: 100%;

          .form-field {
            margin-bottom: 12px;

            :deep(.ant-form-item-label) {
              font-weight: 500;
              color: #262626;
              padding-bottom: 4px;
              display: flex;
              align-items: center;

              label {
                font-size: 14px;
                height: auto;
              }
            }

            :deep(.ant-form-item-control) {
              line-height: 1.5;
              display: flex;
              align-items: center;
              min-height: 32px;
            }

            .field-value {
              color: #262626;
              font-size: 14px;
              min-height: 32px;
              display: inline-flex;
              align-items: center;
              line-height: 1.5;
            }

            &.work-duration {
              .duration-inputs {
                display: flex;
                align-items: center;
                gap: 6px;

                span {
                  color: #8c8c8c;
                  font-size: 13px;
                  font-weight: 500;
                }
              }
            }
          }
        }

        .form-actions {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          gap: 8px;
          padding-top: 12px;
          border-top: 1px solid #f0f0f0;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .radiation-history-list {
      .inline-form-list {
        .form-item-row {
          padding: 16px;

          .form-content {
            .form-field {
              width: 100%;

              &.work-duration {
                .duration-inputs {
                  flex-wrap: wrap;
                  gap: 4px;
                }
              }
            }
          }
        }
      }
    }
  }
</style>
