<template>
  <a-card title="疾病史管理" size="small">
    <template #extra>
      <a-button type="primary" @click="handleAdd" :disabled="disabled" style="margin-bottom: 16px">
        <template #icon>
          <plus-outlined />
        </template>
        添加疾病记录
      </a-button>
    </template>
    <div class="disease-history-list">
      <!-- 常见疾病快捷选项 -->
      <div class="quick-diseases">
        <div class="quick-diseases-header">
          <h4>常见疾病快捷选项</h4>
        </div>
        <div class="disease-buttons">
          <a-button
            v-for="disease in commonDiseases"
            :key="disease.name"
            size="small"
            type="default"
            @click="addDiseaseFromQuick(disease)"
            :disabled="disabled || isDiseaseSelected(disease.name)"
            class="disease-btn"
            :class="{ selected: isDiseaseSelected(disease.name) }"
          >
            {{ disease.name }}
          </a-button>
        </div>
      </div>

      <div v-if="!dataSource.length && !loading" class="empty-state">
        <a-empty description="暂无疾病史记录" />
      </div>

      <a-spin :spinning="loading">
        <div class="inline-form-list">
          <div
            v-for="(record, index) in dataSource"
            :key="record.uuid || record.id || `new_${index}`"
            class="form-item-row"
            :class="{ editing: record._editMode, 'new-record': record._isNew }"
          >
            <a-form :ref="(el) => setFormRef(el, index)" :model="record" layout="horizontal" class="card-form">
              <div class="form-content">
                <a-row :gutter="[16, 16]">
                  <a-col :span="6">
                    <a-form-item
                      label="疾病名称"
                      name="disease"
                      :rules="[{ required: true, message: '请输入疾病名称', trigger: 'submit' }]"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                      class="form-field"
                    >
                      <AutoComplete
                        v-if="record._editMode"
                        v-model:value="record.disease"
                        category="disease"
                        placeholder="请输入疾病名称"
                        style="min-width: 180px"
                      />
                      <span v-else class="field-value">{{ record.disease || '-' }}</span>
                    </a-form-item>
                  </a-col>

                  <a-col :span="6">
                    <a-form-item
                      label="诊断日期"
                      name="diagnoseDate"
                      :rules="[{ required: true, message: '请输入诊断日期' }]"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                      class="form-field"
                    >
                      <template v-if="record._editMode">
                        <div class="ant-form-item-control-input">
                          <div class="ant-form-item-control-input-content">
                            <input
                              v-cleave="{ date: true, delimiter: '-', datePattern: ['Y', 'm', 'd'] }"
                              v-model="record.diagnoseDate"
                              class="ant-input css-dev-only-do-not-override-udyjmm"
                              placeholder="年-月-日"
                            />
                          </div>
                        </div>
                      </template>
                      <span v-else class="field-value">{{ record.diagnoseDate || '-' }}</span>
                    </a-form-item>
                  </a-col>

                  <a-col :span="6">
                    <a-form-item
                      label="确诊单位"
                      name="diagnoseCompany"
                      :rules="[{ required: true, message: '请输入确诊单位', trigger: 'submit' }]"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                      class="form-field"
                    >
                      <AutoComplete
                        category="occu_company"
                        v-if="record._editMode"
                        v-model:value="record.diagnoseCompany"
                        placeholder="请输入确诊单位"
                        style="width: 180px"
                      />
                      <span v-else class="field-value">{{ record.diagnoseCompany || '-' }}</span>
                    </a-form-item>
                  </a-col>

                  <a-col :span="6">
                    <a-form-item label="治疗状态" name="treatmentStatus" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" class="form-field">
                      <a-select v-if="record._editMode" v-model:value="record.treatmentStatus" placeholder="请选择治疗状态" style="min-width: 120px">
                        <a-select-option value="已治愈">已治愈</a-select-option>
                        <a-select-option value="治疗中">治疗中</a-select-option>
                        <a-select-option value="未治疗">未治疗</a-select-option>
                        <a-select-option value="其他">其他</a-select-option>
                      </a-select>
                      <span v-else class="field-value">{{ record.treatmentStatus || '-' }}</span>
                    </a-form-item>
                  </a-col>
                </a-row>

                <a-row :gutter="[16, 16]">
                  <a-col :span="12">
                    <a-form-item label="症状描述" name="symptoms" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" class="form-field">
                      <a-textarea
                        v-if="record._editMode"
                        v-model:value="record.symptoms"
                        placeholder="请输入症状描述"
                        :rows="2"
                        style="min-width: 500px"
                      />
                      <span v-else class="field-value">{{ record.symptoms || '-' }}</span>
                    </a-form-item>
                  </a-col>

                  <a-col :span="12">
                    <a-form-item label="备注" name="remark" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" class="form-field">
                      <a-textarea v-if="record._editMode" v-model:value="record.remark" placeholder="请输入备注" :rows="2" style="min-width: 500px" />
                      <span v-else class="field-value">{{ record.remark || '-' }}</span>
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>

              <div class="form-actions">
                <template v-if="record._editMode">
                  <a-button type="primary" size="small" @click="handleSave(record, index)" :loading="record._saving" :disabled="disabled">
                    保存
                  </a-button>
                  <a-button size="small" @click="handleCancel(record, index)" style="margin-left: 8px"> 取消 </a-button>
                </template>
                <template v-else>
                  <a-button type="link" size="small" @click="handleEdit(record, index)" :disabled="disabled"> 编辑 </a-button>
                  <a-popconfirm title="确定要删除这条记录吗？" @confirm="handleDelete(record, index)" :disabled="disabled">
                    <a-button type="link" danger size="small" :disabled="disabled"> 删除 </a-button>
                  </a-popconfirm>
                </template>
              </div>
            </a-form>
          </div>
        </div>
      </a-spin>
    </div>
  </a-card>
</template>

<script lang="ts" setup>
  import { ref, reactive, inject, watch, onMounted } from 'vue';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import { zyInquiryDiseaseHistoryList, zyInquiryDiseaseHistorySaveOrUpdate, zyInquiryDiseaseHistoryDelete } from '../ZyInquiry.api';
  import AutoComplete from '@/components/basicinfo/AutoComplete.vue';
  import { buildUUID } from '@/utils/uuid';

  /**日期计算*/
  const dayjs = inject('$dayjs');

  // 常见疾病列表
  const commonDiseases = ref([
    { name: '高血压', treatmentMeans: '药物治疗' },
    { name: '糖尿病', treatmentMeans: '药物治疗' },
    { name: '冠心病', treatmentMeans: '药物治疗' },
    { name: '脑梗塞', treatmentMeans: '住院治疗' },
    { name: '肺结核', treatmentMeans: '药物治疗' },
    { name: '乙型肝炎', treatmentMeans: '药物治疗' },
    { name: '胃炎', treatmentMeans: '药物治疗' },
    { name: '胆结石', treatmentMeans: '手术治疗' },
    { name: '肾结石', treatmentMeans: '药物治疗' },
    { name: '关节炎', treatmentMeans: '药物治疗' },
    { name: '哮喘', treatmentMeans: '药物治疗' },
    { name: '白内障', treatmentMeans: '手术治疗' },
    { name: '痔疮', treatmentMeans: '手术治疗' },
    { name: '贫血', treatmentMeans: '药物治疗' },
    { name: '失眠', treatmentMeans: '药物治疗' },
  ]);

  interface DiseaseHistoryRecord {
    id?: string;
    uuid?: string; // 添加UUID字段用于卡片管理
    disease: string;
    diagnoseDate: string;
    treatmentMeans: string;
    diagnoseCompany: string;
    cureFlag: boolean;
    checkDoctor: string;
    inquiryId: string;
    _editMode?: boolean;
    _isNew?: boolean;
    _saving?: boolean;
    _originalData?: any;
  }

  // Props
  interface Props {
    disabled?: boolean;
  }
  const props = withDefaults(defineProps<Props>(), {
    disabled: false,
  });

  // Injected values
  const inquiryMainId = inject('inquiryMainId', ref(''));
  const inquiryReady = inject('inquiryReady', ref(false));

  // State
  const loading = ref(false);
  const dataSource = ref<DiseaseHistoryRecord[]>([]);
  const formRefs = ref<any[]>([]);

  // Set form ref for specific index
  const setFormRef = (el: any, index: number) => {
    if (el) {
      formRefs.value[index] = el;
    }
  };

  // Check if disease is already selected
  const isDiseaseSelected = (diseaseName: string) => {
    return dataSource.value.some((item) => item.disease === diseaseName);
  };

  // Load data
  async function loadData() {
    if (!inquiryMainId.value) return;

    try {
      loading.value = true;
      const result = await zyInquiryDiseaseHistoryList({ inquiryId: inquiryMainId.value });
      if (result && result.records) {
        dataSource.value = result.records.map((item: any) => ({
          ...item,
          uuid: item.uuid || buildUUID(), // 为回显数据生成UUID
          cureFlag: !!item.cureFlag,
          _editMode: false,
          _isNew: false,
          _saving: false,
        }));
      } else {
        dataSource.value = [];
      }

      // 如果没有历史记录，自动添加一个空卡片
      if (dataSource.value.length === 0) {
        handleAdd();
      }
    } catch (error) {
      console.error('加载疾病史记录失败:', error);
      message.error('加载数据失败');
      dataSource.value = [];
    } finally {
      loading.value = false;
    }
  }

  // Add disease from quick options
  function addDiseaseFromQuick(disease: any) {
    if (isDiseaseSelected(disease.name)) {
      message.warning('该疾病记录已存在');
      return;
    }

    const newRecord: DiseaseHistoryRecord = {
      uuid: buildUUID(), // 为新记录生成UUID
      disease: disease.name,
      diagnoseDate: '',
      treatmentMeans: disease.treatmentMeans,
      diagnoseCompany: '',
      cureFlag: false,
      checkDoctor: '',
      inquiryId: inquiryMainId.value,
      _editMode: true,
      _isNew: true,
      _saving: false,
    };
    dataSource.value.push(newRecord);
  }

  // Add new record manually
  function handleAdd() {
    const newRecord: DiseaseHistoryRecord = {
      uuid: buildUUID(), // 为新记录生成UUID
      disease: '',
      diagnoseDate: '',
      treatmentMeans: '',
      diagnoseCompany: '',
      cureFlag: false,
      checkDoctor: '',
      inquiryId: inquiryMainId.value,
      _editMode: true,
      _isNew: true,
      _saving: false,
    };
    dataSource.value.push(newRecord);
  }

  // Edit record
  function handleEdit(record: DiseaseHistoryRecord, index: number) {
    // Store original data for cancel operation
    record._originalData = { ...record };
    record._editMode = true;
  }

  // Save record
  async function handleSave(record: DiseaseHistoryRecord, index: number) {
    try {
      // Validate form
      const formRef = formRefs.value[index];
      if (formRef) {
        await formRef.validate();
      }

      // 校验诊断日期的合法性
      if (record.diagnoseDate) {
        if (!dayjs(record.diagnoseDate).isValid()) {
          message.error('诊断日期格式不正确');
          return;
        }

        const diagnoseDate = dayjs(record.diagnoseDate);
        const currentDate = dayjs();
        if (diagnoseDate.isAfter(currentDate)) {
          message.error('诊断日期不能晚于当前日期');
          return;
        }

        const minDate = dayjs('1900-01-01');
        if (diagnoseDate.isBefore(minDate)) {
          message.error('诊断日期不能早于1900年');
          return;
        }
      }

      // Check for duplicates (except current record)
      if (record._isNew || record._originalData?.disease !== record.disease) {
        const duplicate = dataSource.value.find((item, idx) => idx !== index && item.disease === record.disease);
        if (duplicate) {
          message.error('该疾病记录已存在');
          return;
        }
      }

      record._saving = true;
      const isUpdate = !!record.id;
      const saveData = {
        id: record.id,
        disease: record.disease,
        diagnoseDate: record.diagnoseDate,
        treatmentMeans: record.treatmentMeans,
        diagnoseCompany: record.diagnoseCompany,
        cureFlag: record.cureFlag ? 1 : 0,
        checkDoctor: record.checkDoctor,
        inquiryId: inquiryMainId.value,
      };

      const result = await zyInquiryDiseaseHistorySaveOrUpdate(saveData, isUpdate);

      if (result && result.success) {
        Object.assign(record, result.result);

        record._editMode = false;
        record._isNew = false;
        record._originalData = undefined;
        message.success(isUpdate ? '更新成功' : '保存成功');
      } else {
        throw new Error(result?.message || '保存失败');
      }
    } catch (error: any) {
      console.error('保存失败:', error);
      message.error(error.message || '保存失败');
    } finally {
      record._saving = false;
    }
  }

  // Cancel edit
  function handleCancel(record: DiseaseHistoryRecord, index: number) {
    if (record._isNew) {
      // Remove new record
      dataSource.value.splice(index, 1);
    } else {
      // Restore original data
      if (record._originalData) {
        Object.assign(record, record._originalData);
        record._originalData = undefined;
      }
      record._editMode = false;
    }
  }

  // Delete record
  async function handleDelete(record: DiseaseHistoryRecord, index: number) {
    try {
      // 如果是新记录（没有ID），直接从列表中移除
      if (record._isNew || !record.id) {
        console.log(`🗑️ 删除新建的既往病史记录，UUID: ${record.uuid}`);
        dataSource.value.splice(index, 1);
        message.success('删除成功');
        return;
      }

      // 如果是已保存的记录，调用后端删除接口
      console.log(`🗑️ 删除既往病史记录，ID: ${record.id}, UUID: ${record.uuid}`);
      await zyInquiryDiseaseHistoryDelete({ id: record.id }, () => {
        dataSource.value.splice(index, 1);
        message.success('删除成功');
      });
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  }

  // 暴露方法给父组件
  defineExpose({
    loadData,
  });
</script>

<style lang="less" scoped>
  .disease-history-list {
    .quick-diseases {
      margin-bottom: 24px;
      padding: 20px;
      border-radius: 8px;
      border: 1px solid #f0f0f0;

      .quick-diseases-header {
        h4 {
          margin: 0 0 16px 0;
          font-size: 15px;
          font-weight: 600;
          color: #262626;
        }
      }

      .disease-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        .disease-btn {
          font-size: 13px;
          height: 32px;
          padding: 0 12px;
          border-radius: 6px;
          font-weight: 500;
          transition: all 0.3s ease;

          &:hover:not(:disabled) {
            border-color: #1890ff;
            color: #1890ff;
          }

          &.selected {
            background: #1890ff;
            border-color: #1890ff;
            color: #fff;
            cursor: not-allowed;
          }

          &:disabled {
            opacity: 0.6;
          }
        }
      }
    }

    .empty-state {
      text-align: center;
      padding: 60px 20px;
      border-radius: 8px;
      border: 1px dashed #d9d9d9;
    }

    .inline-form-list {
      .form-item-row {
        background: #fff;
        border: 1px solid #e8e8e8;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 16px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
        }

        &.editing {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        &.new-record {
          border-color: #52c41a;
          background: #f6ffed;
        }

        .card-form {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }

        .form-content {
          width: 100%;

          .form-field {
            margin-bottom: 12px;

            :deep(.ant-form-item-label) {
              font-weight: 500;
              color: #262626;
              padding-bottom: 4px;
              display: flex;
              align-items: center;

              label {
                font-size: 14px;
                height: auto;
              }
            }

            :deep(.ant-form-item-control) {
              line-height: 1.5;
              display: flex;
              align-items: center;
              min-height: 32px;
            }

            .field-value {
              color: #262626;
              font-size: 14px;
              min-height: 32px;
              display: inline-flex;
              align-items: center;
              line-height: 1.5;
            }
          }
        }

        .form-actions {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          gap: 8px;
          padding-top: 12px;
          border-top: 1px solid #f0f0f0;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .disease-history-list {
      .quick-diseases {
        padding: 16px;

        .disease-buttons {
          gap: 8px;

          .disease-btn {
            font-size: 12px;
            height: 32px;
            padding: 0 12px;
          }
        }
      }

      .inline-form-list {
        .form-item-row {
          padding: 16px;

          .form-content {
            .form-field {
              width: 100%;
            }
          }
        }
      }
    }
  }
</style>
