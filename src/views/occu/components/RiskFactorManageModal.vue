<template>
  <j-modal
    title="危害因素管理"
    :width="1200"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
    okText="保存"
  >
    <div style="padding: 16px">
      <!-- 工种信息 -->
      <div style="margin-bottom: 20px; padding: 12px; background: #f8f9fa; border-radius: 6px">
        <h4 style="margin: 0 0 8px 0; color: #333">工种信息</h4>
        <div style="display: flex; gap: 24px">
          <span><strong>名称：</strong>{{ worktypeInfo.name }}</span>
          <span><strong>代码：</strong>{{ worktypeInfo.code }}</span>
        </div>
      </div>

      <div style="margin-bottom: 16px; display: flex; gap: 12px; align-items: center">
        <a-button type="primary" @click="openSelectModal" :loading="selectLoading">
          <template #icon><PlusOutlined /></template>
          添加危害因素
        </a-button>
        <a-button type="default" @click="handleBatchRemove" :disabled="selectedFactorIds.length === 0" :loading="removeLoading">
          <template #icon><MinusOutlined /></template>
          批量移除
        </a-button>
        <a-button type="default" @click="openCopyModal" :loading="copyLoading">
          <template #icon><CopyOutlined /></template>
          复制到其他工种
        </a-button>
        <div style="margin-left: auto; font-size: 12px; color: #666"> 已关联 {{ riskFactorList.length }} 个危害因素 </div>
      </div>

      <!-- 危害因素列表 -->
      <div style="border: 1px solid #d9d9d9; border-radius: 6px">
        <BasicTable @register="registerTable" :rowSelection="rowSelection" :scroll="{ y: 400 }">
          <template #action="{ record }">
            <a-button type="link" danger size="small" @click="handleRemoveSingle(record)"> 移除 </a-button>
          </template>
        </BasicTable>
      </div>
    </div>

    <!-- 危害因素选择弹窗 -->
    <RiskFactorSelectModal
      ref="riskFactorModalRef"
      v-model:modelValue="tempSelectedFactors"
      :excludeFactorIds="currentRiskFactorIds"
      @confirm="handleAddFactors"
    />

    <!-- 复制到其他工种弹窗 -->
    <WorktypeCopyModal ref="worktypeCopyModalRef" @confirm="handleCopyFactors" />
  </j-modal>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { PlusOutlined, MinusOutlined, CopyOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { BasicTable, useTable } from '/@/components/Table';
  import JModal from '/@/components/Modal/src/JModal/JModal.vue';
  import RiskFactorSelectModal from './RiskFactorSelectModal.vue';
  import WorktypeCopyModal from './WorktypeCopyModal.vue';
  import { queryRiskFactorsByWorktypeId, addRiskFactorsBatch, removeRiskFactorsBatch, copyRiskFactorsToWorktypes } from '../ZyWorktype.api';

  const emit = defineEmits(['success']);
  const { createMessage, createConfirm } = useMessage();

  const visible = ref(false);
  const confirmLoading = ref(false);
  const selectLoading = ref(false);
  const removeLoading = ref(false);
  const copyLoading = ref(false);

  const worktypeInfo = ref({ id: '', name: '', code: '' });
  const riskFactorList = ref<any[]>([]);
  const selectedFactorIds = ref<string[]>([]);

  const riskFactorModalRef = ref();
  const worktypeCopyModalRef = ref();

  // 添加用于危害因素选择的临时变量
  const tempSelectedFactors = ref<string[]>([]);

  // 计算当前已关联的危害因素ID列表
  const currentRiskFactorIds = computed(() => {
    return riskFactorList.value.map((item: any) => item.riskFactorId || item.id);
  });

  // 表格列配置
  const columns = [
    {
      title: '危害因素名称',
      dataIndex: 'factorName',
      width: 200,
    },
    {
      title: '危害因素代码',
      dataIndex: 'factorCode',
      width: 150,
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 80,
      slots: { customRender: 'action' },
    },
  ];

  const dataLoading = ref(false);
  // 注册表格
  const [registerTable, { setTableData }] = useTable({
    columns,
    loading: dataLoading,
    dataSource: riskFactorList,
    pagination: false,
    size: 'small',
    bordered: true,
    // 添加rowKey配置，使用riskFactorId作为行键
    rowKey: 'riskFactorId',
  });

  // 行选择
  const rowSelection = {
    type: 'checkbox',
    selectedRowKeys: selectedFactorIds,
    onChange: (selectedRowKeys: string[]) => {
      console.log('选中的行键:', selectedRowKeys);
      selectedFactorIds.value = selectedRowKeys;
    },
    // 移除getCheckboxProps，因为已经通过rowKey配置了正确的键值
  };

  /**
   * 打开弹窗
   */
  function open(record: any) {
    worktypeInfo.value = {
      id: record.id,
      name: record.name,
      code: record.code,
    };
    visible.value = true;
    loadRiskFactors();
  }

  /**
   * 关闭弹窗
   */
  function close() {
    visible.value = false;
    selectedFactorIds.value = [];
    riskFactorList.value = [];
  }

  /**
   * 加载危害因素列表
   */
  async function loadRiskFactors() {
    if (!worktypeInfo.value.id) {
      console.warn('工种ID为空，无法加载危害因素');
      return;
    }

    try {
      dataLoading.value = true;
      console.log('开始加载工种关联的危害因素，工种ID:', worktypeInfo.value.id);

      const res = await queryRiskFactorsByWorktypeId(worktypeInfo.value.id);
      console.log('API响应:', res);

      if (res && res.success) {
        const dataList = res.result || [];
        console.log('获取到的危害因素数据:', dataList);

        // 统一字段，兼容后端可能的多种命名（factorId / riskFactorId / id）
        const mapped = dataList.map((item) => {
          const riskFactorId = item.riskFactorId || item.risk_factor_id || item.factorId || item.id;
          const factorName = item.factorName || item.riskFactorName || item.risk_factor_name || item.riskFactorname; // 兼容大小写/下划线/拼写
          const factorCode = item.factorCode || item.riskFactorCode || item.risk_factor_code || item.riskFactorcode;
          return {
            ...item,
            id: riskFactorId, // 仅用于表格内部可能需要的id
            riskFactorId,
            factorName: factorName, // 列显示用（当前 columns 使用 factorName/factorCode）
            factorCode: factorCode,
            riskFactorName: factorName,
            riskFactorCode: factorCode,
          };
        });

        // 过滤掉缺少 riskFactorId 的记录，避免选择时出现 undefined 导致 join 为空字符串
        const invalid = mapped.filter((m) => !m.riskFactorId);
        if (invalid.length) {
          console.warn('以下记录缺少 riskFactorId，将被忽略:', invalid);
        }
        riskFactorList.value = mapped.filter((m) => !!m.riskFactorId);

        // 清空之前的选中，避免旧的无效 key 残留
        selectedFactorIds.value = [];

        setTableData(riskFactorList.value);
        console.log('设置表格数据完成，条数:', riskFactorList.value.length);
      } else {
        console.error('加载危害因素失败:', res?.message);
        createMessage.error(res?.message || '加载危害因素失败');
        riskFactorList.value = [];
        setTableData([]);
      }
    } catch (error) {
      console.error('加载危害因素异常:', error);
      createMessage.error('加载危害因素失败，请检查网络连接');
      riskFactorList.value = [];
      setTableData([]);
    } finally {
      dataLoading.value = false;
    }
  }

  /**
   * 打开选择弹窗
   */
  function openSelectModal() {
    console.log('点击添加危害因素按钮');
    console.log('riskFactorModalRef:', riskFactorModalRef.value);
    console.log('当前已关联危害因素:', currentRiskFactorIds.value);

    // 重置选择状态
    tempSelectedFactors.value = [];

    // 检查弹窗组件是否存在
    if (!riskFactorModalRef.value) {
      console.error('危害因素选择弹窗组件不存在');
      createMessage.error('弹���组件加载失败');
      return;
    }

    try {
      // 打开弹窗
      riskFactorModalRef.value.open();
      console.log('弹窗打开成功');
    } catch (error) {
      console.error('打开危害因素选择弹窗失败:', error);
      createMessage.error('打开选择弹窗失败');
    }
  }

  /**
   * 添加危害因素
   */
  async function handleAddFactors(factorIds: string[]) {
    if (!factorIds.length) return;

    try {
      confirmLoading.value = true;
      const riskFactorIds = factorIds.join(',');
      const res = await addRiskFactorsBatch({
        worktypeId: worktypeInfo.value.id,
        riskFactorIds,
      });

      if (res.success) {
        createMessage.success('添加成功');
        await loadRiskFactors();
      } else {
        createMessage.error(res.message || '���加失败');
      }
    } catch (error) {
      console.error('添加危害因素失败:', error);
      createMessage.error('添加失败');
    } finally {
      confirmLoading.value = false;
    }
  }

  /**
   * 批量移除危害因素
   */
  function handleBatchRemove() {
    if (!selectedFactorIds.value.length) {
      createMessage.warning('请选择要移除的危害因素');
      return;
    }

    createConfirm({
      iconType: 'warning',
      title: '确认移除',
      content: `确定要移除选中的 ${selectedFactorIds.value.length} 个危害因素吗？`,
      onOk: async () => {
        await removeBatchFactors(selectedFactorIds.value);
      },
    });
  }

  /**
   * 移除单个危害因素
   */
  function handleRemoveSingle(record: any) {
    createConfirm({
      iconType: 'warning',
      title: '确认移除',
      content: `确定要移除危害因素"${record.riskFactorName}"吗？`,
      onOk: async () => {
        await removeBatchFactors([record.riskFactorId]);
      },
    });
  }

  /**
   * 执行移除操作
   */
  async function removeBatchFactors(factorIds: string[]) {
    try {
      removeLoading.value = true;
      // 过滤空值 / undefined，避免 join 后得到空字符串
      const validIds = (factorIds || []).filter((id) => !!id);
      if (!validIds.length) {
        createMessage.error('选中的危害因素缺少有效ID，无法移除');
        return;
      }
      const riskFactorIds = validIds.join(',');
      console.log('批量移除提交 riskFactorIds:', riskFactorIds);
      const res = await removeRiskFactorsBatch({
        worktypeId: worktypeInfo.value.id,
        riskFactorIds,
      });

      if (res.success) {
        createMessage.success('移除成功');
        selectedFactorIds.value = [];
        await loadRiskFactors();
      } else {
        createMessage.error(res.message || '移除失败');
      }
    } catch (error) {
      console.error('移除危害因素失败:', error);
      createMessage.error('移除失败');
    } finally {
      removeLoading.value = false;
    }
  }

  /**
   * 打开复制弹窗
   */
  function openCopyModal() {
    if (!riskFactorList.value.length) {
      createMessage.warning('当前工种没有关联的危害因素');
      return;
    }
    copyLoading.value = true;
    setTimeout(() => {
      worktypeCopyModalRef.value?.open(worktypeInfo.value);
      copyLoading.value = false;
    }, 100);
  }

  /**
   * 复制危害因素到其他工种
   */
  async function handleCopyFactors(targetWorktypeIds: string[]) {
    if (!targetWorktypeIds.length) return;

    try {
      confirmLoading.value = true;
      const res = await copyRiskFactorsToWorktypes({
        sourceWorktypeId: worktypeInfo.value.id,
        targetWorktypeIds: targetWorktypeIds.join(','),
      });

      if (res.success) {
        createMessage.success('复制成功');
      } else {
        createMessage.error(res.message || '复制失败');
      }
    } catch (error) {
      console.error('复制危害因素失败:', error);
      createMessage.error('复制失败');
    } finally {
      confirmLoading.value = false;
    }
  }

  /**
   * 确认保存
   */
  function handleOk() {
    emit('success');
    close();
  }

  /**
   * 取消
   */
  function handleCancel() {
    close();
  }

  // 暴露方法
  defineExpose({
    open,
    close,
  });
</script>

<style scoped>
  :deep(.ant-table-tbody > tr > td) {
    padding: 8px 12px;
  }
</style>
