<template>
  <a-card title="家族病史" size="small">
    <template #extra>
      <a-button type="primary" @click="handleAdd" :disabled="disabled">
        <template #icon>
          <plus-outlined />
        </template>
        添加家族病史
      </a-button>
    </template>
  </a-card>
  <div class="family-history-list">
    <div class="family-container">
      <!-- 左侧关系快捷选项 -->
      <div class="quick-options">
        <div class="quick-options-header">
          <h4>家族关系快捷选项</h4>
          <a-divider />
        </div>
        <div class="relation-buttons">
          <a-button
            v-for="relation in familyRelations"
            :key="relation.value"
            type="default"
            @click="addFamilyFromQuick(relation)"
            :disabled="disabled || isRelationSelected(relation.value)"
            class="relation-btn"
            :class="{ selected: isRelationSelected(relation.value) }"
          >
            {{ relation.label }}
          </a-button>
        </div>
      </div>

      <!-- 右侧内联表单列表 -->
      <div class="form-list-container">
        <div v-if="!dataSource.length && !loading" class="empty-state">
          <a-empty description="暂无家族病史记录" />
        </div>

        <a-spin :spinning="loading">
          <div class="inline-form-list">
            <div
              v-for="(record, index) in dataSource"
              :key="record.uuid || record.id || `new_${index}`"
              class="form-item-row"
              :class="{ editing: record._editMode, 'new-record': record._isNew }"
            >
              <a-form :ref="(el) => setFormRef(el, index)" :model="record" layout="horizontal" class="card-form">
                <div class="form-content">
                  <a-row :gutter="[16, 16]" align="center">
                    <a-col :span="6">
                      <a-form-item
                        label="关系"
                        :label-col="{ span: 8 }"
                        :wrapper-col="{ span: 16 }"
                        name="relation"
                        :rules="[{ required: true, message: '请选择关系' }]"
                        class="form-field"
                      >
                        <a-select v-if="record._editMode" v-model:value="record.relation" placeholder="请选择关系" style="min-width: 150px">
                          <a-select-option v-for="relation in familyRelations" :key="relation.value" :value="relation.value">
                            {{ relation.label }}
                          </a-select-option>
                        </a-select>
                        <span v-else class="field-value">{{ getRelationText(record.relation) || '-' }}</span>
                      </a-form-item>
                    </a-col>

                    <a-col :span="6">
                      <a-form-item
                        label="疾病"
                        :label-col="{ span: 8 }"
                        :wrapper-col="{ span: 16 }"
                        name="disease"
                        :rules="[{ required: true, message: '请选择疾病' }]"
                        class="form-field"
                      >
                        <AutoComplete
                          v-if="record._editMode"
                          v-model:value="record.disease"
                          category="disease"
                          placeholder="请输入疾病名称"
                          style="min-width: 180px"
                        />
                        <span v-else class="field-value">{{ formatDiseaseText(record.disease) || '-' }}</span>
                      </a-form-item>
                    </a-col>

                    <a-col :span="6">
                      <a-form-item label="年龄" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" name="age" class="form-field">
                        <a-input-number
                          v-if="record._editMode"
                          v-model:value="record.age"
                          placeholder="年龄"
                          :min="0"
                          :max="120"
                          style="min-width: 150px"
                        />
                        <span v-else class="field-value">{{ record.age ? record.age + '岁' : '-' }}</span>
                      </a-form-item>
                    </a-col>

                    <a-col :span="6">
                      <a-form-item label="备注" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" name="remark" class="form-field">
                        <a-input v-if="record._editMode" v-model:value="record.remark" placeholder="请输入备注" style="min-width: 150px" />
                        <span v-else class="field-value">{{ record.remark || '-' }}</span>
                      </a-form-item>
                    </a-col>
                  </a-row>
                </div>

                <div class="form-actions">
                  <template v-if="record._editMode">
                    <a-button type="primary" size="small" @click="handleSave(record, index)" :loading="record._saving" :disabled="disabled">
                      保存
                    </a-button>
                    <a-button size="small" @click="handleCancel(record, index)" style="margin-left: 8px"> 取消 </a-button>
                  </template>
                  <template v-else>
                    <a-button type="link" size="small" @click="handleEdit(record, index)" :disabled="disabled"> 编辑 </a-button>
                    <a-popconfirm title="确定要删除这条记录吗？" @confirm="handleDelete(record, index)" :disabled="disabled">
                      <a-button type="link" danger size="small" :disabled="disabled"> 删除 </a-button>
                    </a-popconfirm>
                  </template>
                </div>
              </a-form>
            </div>
          </div>
        </a-spin>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, inject, watch, onMounted } from 'vue';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import { zyInquiryFamilyHistoryList, zyInquiryFamilyHistorySaveOrUpdate, zyInquiryFamilyHistoryDelete } from '../ZyInquiry.api';
  import { buildUUID } from '@/utils/uuid';
  import AutoComplete from '@/components/basicinfo/AutoComplete.vue';

  // 家族关系选项
  const familyRelations = ref([
    { value: '父亲', label: '父亲' },
    { value: '母亲', label: '母亲' },
    { value: '祖父', label: '祖父' },
    { value: '祖母', label: '祖母' },
    { value: '外祖父', label: '外祖父' },
    { value: '外祖母', label: '外祖母' },
    { value: '兄弟', label: '兄弟' },
    { value: '姐妹', label: '姐妹' },
    { value: '子女', label: '子女' },
    { value: '叔伯', label: '叔伯' },
    { value: '姑姨', label: '姑姨' },
    { value: '其他', label: '其他' },
  ]);

  interface FamilyHistoryRecord {
    id?: string;
    uuid?: string; // 添加UUID字段用于卡片管理
    relation: string;
    disease: string[] | string;
    age?: number;
    remark: string;
    inquiryId: string;
    _editMode?: boolean;
    _isNew?: boolean;
    _saving?: boolean;
    _originalData?: any;
  }

  // Props
  interface Props {
    disabled?: boolean;
  }
  const props = withDefaults(defineProps<Props>(), {
    disabled: false,
  });

  // Injected values
  const inquiryMainId = inject('inquiryMainId', ref(''));
  const inquiryReady = inject('inquiryReady', ref(false));

  // State
  const loading = ref(false);
  const dataSource = ref<FamilyHistoryRecord[]>([]);
  const formRefs = ref<any[]>([]);

  // Set form ref for specific index
  const setFormRef = (el: any, index: number) => {
    if (el) {
      formRefs.value[index] = el;
    }
  };

  // Get relation text by value
  const getRelationText = (relationValue: string) => {
    const relation = familyRelations.value.find((r) => r.value === relationValue);
    return relation ? relation.label : relationValue;
  };

  // Format disease text
  const formatDiseaseText = (diseases: string[] | string) => {
    if (!diseases) return '-';
    if (Array.isArray(diseases)) {
      return diseases.join(', ');
    }
    return diseases;
  };

  // Check if relation is already selected
  const isRelationSelected = (relationValue: string) => {
    return dataSource.value.some((item) => item.relation === relationValue);
  };

  // Load data
  async function loadData() {
    if (!inquiryMainId.value) return;

    try {
      loading.value = true;
      const result = await zyInquiryFamilyHistoryList({ inquiryId: inquiryMainId.value });
      if (result && result.records) {
        dataSource.value = result.records.map((item: any) => ({
          ...item,
          uuid: item.uuid || buildUUID(), // 为回显数据生成UUID
          disease: item.disease ? (typeof item.disease === 'string' ? item.disease.split(',') : item.disease) : [],
          _editMode: false,
          _isNew: false,
          _saving: false,
        }));
      } else {
        dataSource.value = [];
      }

      // 如果没有历史记录，自动添加一个空卡片
      if (dataSource.value.length === 0) {
        handleAdd();
      }
    } catch (error) {
      console.error('加载家族病史记录失败:', error);
      message.error('加载数据失败');
      dataSource.value = [];
    } finally {
      loading.value = false;
    }
  }

  // Add family member from quick options
  function addFamilyFromQuick(relation: any) {
    if (isRelationSelected(relation.value)) {
      message.warning('该关系的家族成员已存在');
      return;
    }

    const newRecord: FamilyHistoryRecord = {
      uuid: buildUUID(), // 为新记录生成UUID
      relation: relation.value,
      disease: [],
      age: undefined,
      remark: '',
      inquiryId: inquiryMainId.value,
      _editMode: true,
      _isNew: true,
      _saving: false,
    };
    dataSource.value.push(newRecord);
  }

  // Add new record manually
  function handleAdd() {
    const newRecord: FamilyHistoryRecord = {
      uuid: buildUUID(), // 为新记录生成UUID
      relation: '',
      disease: [],
      age: undefined,
      remark: '',
      inquiryId: inquiryMainId.value,
      _editMode: true,
      _isNew: true,
      _saving: false,
    };
    dataSource.value.push(newRecord);
  }

  // Edit record
  function handleEdit(record: FamilyHistoryRecord, index: number) {
    // Store original data for cancel operation
    record._originalData = { ...record };
    record._editMode = true;
  }

  // Save record
  async function handleSave(record: FamilyHistoryRecord, index: number) {
    try {
      // Validate form
      const formRef = formRefs.value[index];
      if (formRef) {
        await formRef.validate();
      }

      // Check for duplicates (except current record)
      if (record._isNew || record._originalData?.relation !== record.relation) {
        const duplicate = dataSource.value.find((item, idx) => idx !== index && item.relation === record.relation);
        if (duplicate) {
          message.error('该关系的家族成员已存在');
          return;
        }
      }

      record._saving = true;
      const isUpdate = !!record.id;
      const saveData = {
        id: record.id,
        relation: record.relation,
        disease: Array.isArray(record.disease) ? record.disease.join(',') : record.disease,
        age: record.age,
        remark: record.remark,
        inquiryId: inquiryMainId.value,
      };

      const result = await zyInquiryFamilyHistorySaveOrUpdate(saveData, isUpdate);
      if (result && result.success) {
        // 处理新增记录的ID更新
        if (!isUpdate && result.result) {
          // 根据后端返回的数据结构更新ID
          if (typeof result.result === 'string') {
            record.id = result.result;
          } else if (result.result.id) {
            record.id = result.result.id;
            // 如果后端返回了完整对象，更新其他字段
            Object.keys(result.result).forEach((key) => {
              if (key !== 'uuid' && record.hasOwnProperty(key)) {
                record[key] = result.result[key];
              }
            });
          }
          console.log(`✅ 新增家族病史记录成功，ID: ${record.id}, UUID: ${record.uuid}`);
        } else if (isUpdate) {
          console.log(`✅ 更新家族病史记录成功，ID: ${record.id}, UUID: ${record.uuid}`);
        }

        record._editMode = false;
        record._isNew = false;
        record._originalData = undefined;
        message.success(isUpdate ? '更新成功' : '保存成功');
      } else {
        throw new Error(result?.message || '保存失败');
      }
    } catch (error: any) {
      console.error('保存失败:', error);
      message.error(error.message || '保存失败');
    } finally {
      record._saving = false;
    }
  }

  // Cancel edit
  function handleCancel(record: FamilyHistoryRecord, index: number) {
    if (record._isNew) {
      // Remove new record
      dataSource.value.splice(index, 1);
    } else {
      // Restore original data
      if (record._originalData) {
        Object.assign(record, record._originalData);
        record._originalData = undefined;
      }
      record._editMode = false;
    }
  }

  // Delete record
  async function handleDelete(record: FamilyHistoryRecord, index: number) {
    try {
      // 如果是新记录（没有ID），直接从列表中移除
      if (record._isNew || !record.id) {
        console.log(`🗑️ 删除新建的家族病史记录，UUID: ${record.uuid}`);
        dataSource.value.splice(index, 1);
        message.success('删除成功');
        return;
      }

      // 如果是已保存的记录，调用后端删除接口
      console.log(`🗑️ 删除家族病史记录，ID: ${record.id}, UUID: ${record.uuid}`);
      await zyInquiryFamilyHistoryDelete({ id: record.id }, () => {
        dataSource.value.splice(index, 1);
        message.success('删除成功');
      });
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  }

  // 暴露方法给父组件
  defineExpose({
    loadData,
  });
</script>

<style lang="less" scoped>
  .family-history-list {
    .family-container {
      display: flex;
      gap: 24px;
      min-height: 400px;

      .quick-options {
        width: 280px;
        flex-shrink: 0;
        border-radius: 8px;
        padding: 20px;
        border: 1px solid #f0f0f0;

        .quick-options-header {
          h4 {
            margin: 0 0 16px 0;
            font-size: 15px;
            font-weight: 600;
            color: #262626;
          }
        }

        .relation-buttons {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 12px;
          margin-top: 16px;

          .relation-btn {
            width: 100%;
            height: 36px;
            font-size: 14px;
            border-radius: 6px;
            transition: all 0.3s ease;

            &:hover:not(:disabled) {
              border-color: #1890ff;
              color: #1890ff;
            }

            &.selected {
              background-color: #f6ffed;
              border-color: #52c41a;
              color: #52c41a;
            }

            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }
          }
        }
      }

      .form-list-container {
        flex: 1;
        min-width: 0;

        .empty-state {
          text-align: center;
          padding: 60px 20px;
          border-radius: 8px;
          border: 1px dashed #d9d9d9;
        }

        .inline-form-list {
          .form-item-row {
            background: #fff;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
            transition: all 0.3s ease;

            &:hover {
              box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
            }

            &.editing {
              border-color: #1890ff;
              box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            }

            &.new-record {
              border-color: #52c41a;
              background: #f6ffed;
            }

            .card-form {
              display: flex;
              flex-direction: column;
              gap: 12px;
            }

            .form-content {
              width: 100%;

              .form-field {
                .field-value {
                  color: #262626;
                  font-size: 14px;
                }
              }
            }

            .form-actions {
              display: flex;
              justify-content: flex-end;
              align-items: center;
              gap: 8px;
              padding-top: 12px;
              border-top: 1px solid #f0f0f0;
            }
          }
        }
      }
    }
  }
</style>
