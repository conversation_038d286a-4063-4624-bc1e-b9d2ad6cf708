# 批量部位选择API修复说明

## 问题描述

在实现批量部位选择功能时，出现了以下错误：
```
加载部位选项失败: getCheckPartList is not defined
```

## 问题原因

在新的批量部位选择逻辑中，错误地使用了不存在的 `getCheckPartList` 函数，而实际应该使用已导入的 `listByItemGroup` 函数。

## 修复内容

### 1. API函数修正

**修复前：**
```javascript
const res = await getCheckPartList(params);
```

**修复后：**
```javascript
const res = await listByItemGroup(params);
```

### 2. 确认API导入

文件中已正确导入了所需的API函数：
```javascript
import { listByItemGroup } from '@/views/basicinfo/CheckPartDict.api';
```

### 3. API函数定义

`listByItemGroup` 函数在 `CheckPartDict.api.ts` 中的定义：
```javascript
/**
 * 根据项目ID获取部位选项（带使用频次排序）
 * @param params
 */
export const listByItemGroup = (params) => {
  return defHttp.get({ url: Api.listByItemGroup, params });
}
```

## 参数格式

### 基本参数
```javascript
const params = { itemGroupId };
```

### 带搜索关键字的参数
```javascript
const params = { 
  itemGroupId,
  keyword: keyword.trim()
};
```

## 响应格式处理

新的批量部位选择逻辑支持两种响应格式：

### 1. 直接数组格式
```javascript
if (Array.isArray(res)) {
  selection.options = res.map((item: any) => ({
    label: `${item.name || ''}${item.frequency ? ` (${item.frequency}次)` : ''}`,
    value: item.id || '',
    frequency: item.frequency || 0
  }));
}
```

### 2. 包装对象格式
```javascript
else if (res && res.success && Array.isArray(res.result)) {
  selection.options = res.result.map((item: any) => ({
    label: `${item.name || ''}${item.frequency ? ` (${item.frequency}次)` : ''}`,
    value: item.id || '',
    frequency: item.frequency || 0
  }));
}
```

## 兼容性

### 旧的单项目部位选择
旧的部位选择逻辑继续使用相同的 `listByItemGroup` API：
```javascript
const res = await listByItemGroup(params);
```

### 新的批量部位选择
新的批量部位选择逻辑现在也使用相同的 `listByItemGroup` API：
```javascript
const res = await listByItemGroup(params);
```

## 测试建议

### 1. 基本功能测试
- 测试项目选择框的批量部位选择
- 测试套餐的批量部位选择
- 验证部位选项正确加载

### 2. 搜索功能测试
- 测试部位搜索功能
- 验证搜索结果正确显示
- 测试搜索防抖功能

### 3. 错误处理测试
- 测试网络异常情况
- 测试空结果处理
- 验证错误提示友好性

## 相关文件

### 前端文件
- `src/views/reg/components/TeamGroupCard.vue` - 主要修改文件
- `src/views/basicinfo/CheckPartDict.api.ts` - API定义文件

### 修改内容
- 第1158行：将 `getCheckPartList` 修正为 `listByItemGroup`

### 文档文件
- `docs/批量部位选择API修复说明.md` - 本文档

## 预期效果

修复后，批量部位选择功能应该能够：

1. **正常加载部位选项**：为每个项目正确加载可用的部位选项
2. **支持搜索功能**：用户可以通过关键字搜索部位
3. **显示使用频次**：部位选项显示历史使用频次
4. **错误处理完善**：网络异常或空结果时显示友好提示

## 后续验证

请测试以下场景：

1. **套餐添加**：选择包含需要部位选择项目的套餐
2. **项目选择**：手动选择需要部位选择的项目
3. **部位搜索**：在部位选择器中输入关键字搜索
4. **批量操作**：同时为多个项目选择部位

如果以上场景都能正常工作，说明API修复成功。
