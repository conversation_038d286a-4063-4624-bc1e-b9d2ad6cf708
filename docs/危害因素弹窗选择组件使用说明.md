# 危害因素弹窗选择组件使用说明

## 概述

将原来的 `j-async-search-select` 异步搜索选择组件替换为弹窗选择的方式，使用表格列表形式直接显示一批数据，支持多选，提供更好的用户体验和更直观的选择界面。

## 组件结构

### 1. RiskFactorSelectModal.vue
危害因素选择弹窗组件，提供以下功能：
- **表格列表**：以表格形式展示所有危害因素数据
- **搜索功能**：支持按名称或代码实时搜索过滤
- **多选支持**：表格行选择，支持单选和批量选择
- **批量操作**：全选、清空功能
- **实时预览**：显示已选择数量
- **响应式布局**：适配不同屏幕尺寸

### 2. 修改后的 ZyWorktypeForm.vue
工种表单组件，集成了弹窗选择功能：
- 按钮触发：点击按钮打开选择弹窗
- 标签展示：以标签形式展示已选择的危害因素
- 单个删除：支持点击标签的关闭按钮删除单个选项
- 状态同步：与弹窗选择状态保持同步

## 主要功能特性

### 1. 表格列表展示
- **表格形式**：清晰展示危害因素名称、代码等信息
- **固定表头**：支持滚动浏览大量数据
- **行选择**：支持单行选择和批量选择
- **选中高亮**：选中行高亮显示，视觉反馈清晰

### 2. 搜索功能
- **实时搜索**：支持按危害因素名称搜索
- **代码搜索**：支持按危害因素代码搜索
- **即时过滤**：输入即时过滤显示结果

### 3. 批量操作
- **全选功能**：一键选择当前过滤结果中的所有项目
- **清空功能**：一键清除所有已选择的项目
- **选择统计**：实时显示已选择数量

### 4. 用户体验优化
- **弹窗形式**：不占用表单空间，界面更整洁
- **响应式设计**：适配不同屏幕尺寸
- **加载状态**：数据加载时显示loading状态
- **操作便捷**：支持键盘操作和鼠标操作

## 使用方法

### 1. 基本使用
```vue
<template>
  <!-- 触发按钮 -->
  <a-button @click="openRiskFactorModal">
    选择危害因素
  </a-button>
  
  <!-- 弹窗组件 -->
  <RiskFactorSelectModal
    ref="riskFactorModalRef"
    v-model:modelValue="selectedRiskFactors"
    @confirm="handleRiskFactorConfirm"
  />
</template>

<script setup>
import RiskFactorSelectModal from './RiskFactorSelectModal.vue';

const selectedRiskFactors = ref([]);
const riskFactorModalRef = ref();

const openRiskFactorModal = () => {
  riskFactorModalRef.value?.show();
};

const handleRiskFactorConfirm = (selectedFactors) => {
  console.log('选择的危害因素:', selectedFactors);
};
</script>
```

### 2. 组件属性

#### RiskFactorSelectModal Props
| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| modelValue | string[] | [] | 已选择的危害因素ID数组 |

#### RiskFactorSelectModal Events
| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | value: string[] | 选择值变化时触发 |
| confirm | selectedFactors: RiskFactor[] | 确认选择时触发 |

#### RiskFactorSelectModal Methods
| 方法名 | 参数 | 说明 |
|--------|------|------|
| show | - | 显示弹窗 |
| hide | - | 隐藏弹窗 |

## 数据结构

### RiskFactor 接口
```typescript
interface RiskFactor {
  id: string;      // 危害因素ID
  name: string;    // 危害因素名称
  code: string;    // 危害因素代码
  remark?: string; // 备注信息（可选）
}
```

### 表格列配置
```typescript
const columns = [
  {
    title: '危害因素名称',
    dataIndex: 'name',
    key: 'name',
    width: 200,
    ellipsis: true,
  },
  {
    title: '危害因素代码',
    dataIndex: 'code',
    key: 'code',
    width: 150,
    align: 'center',
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    ellipsis: true,
  },
];
```

## 样式定制

### 1. 选中项目样式
```less
.selected-item {
  background-color: #f6ffed;
  border-color: #52c41a !important;
}
```

### 2. 标签展示样式
```less
.selected-factors {
  max-height: 120px;
  overflow-y: auto;
  padding: 8px;
  background-color: #fafafa;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}
```

## 注意事项

1. **数据加载**：组件会自动加载危害因素列表，确保API接口正常
2. **状态同步**：弹窗内的选择状态与外部状态保持同步
3. **性能优化**：大量数据时建议添加虚拟滚动
4. **错误处理**：网络请求失败时会在控制台输出错误信息

## 优势对比

### 原 j-async-search-select 组件
- 占用表单空间较大
- 多选时显示不够直观
- 搜索功能相对简单
- 数据展示信息有限

### 新弹窗表格选择组件
- ✅ **节省表单空间**：弹窗形式，不占用表单布局
- ✅ **直观的表格展示**：清晰显示所有字段信息
- ✅ **强大的多选功能**：表格行选择，支持批量操作
- ✅ **丰富的搜索功能**：实时搜索过滤
- ✅ **更好的用户体验**：加载状态、选择反馈等
- ✅ **响应式设计**：适配不同设备
- ✅ **数据展示完整**：可显示更多字段信息

## 演示组件

为了方便测试和演示，提供了 `RiskFactorSelectDemo.vue` 演示组件：

### 功能特性
- **完整演示**：展示组件的所有功能
- **交互测试**：可以测试选择、删除、清空等操作
- **结果展示**：实时显示选择结果和ID列表
- **模拟数据**：提供模拟选择功能用于测试

### 使用方法
```vue
<template>
  <RiskFactorSelectDemo />
</template>

<script setup>
import RiskFactorSelectDemo from '@/views/occu/components/RiskFactorSelectDemo.vue';
</script>
```

## 扩展建议

1. **分类筛选**：可以添加按危害因素分类筛选的功能
2. **最近使用**：记录最近使用的危害因素，快速选择
3. **自定义排序**：支持按名称、代码等字段排序
4. **导入导出**：支持批量导入导出危害因素配置
5. **虚拟滚动**：当数据量很大时，可以考虑使用虚拟滚动优化性能
6. **分页加载**：支持后端分页，减少一次性加载的数据量
