# 互斥检查功能重新添加说明

## 概述

根据业务需求，互斥检查是重要的业务逻辑，不能移除。本次重新添加了完整的互斥检查功能，包括前端预检查和后端最终验证。

## 重新添加的功能

### 1. 前端互斥检查工具

#### 文件位置：`src/utils/itemGroupMutexCheck.js`

**核心功能：**
- 缓存互斥关系数据（5分钟缓存）
- 检查新项目与现有项目的冲突
- 检查新项目之间的冲突
- 格式化冲突信息为用户友好的消息

**主要方法：**
```javascript
// 检查项目互斥
export async function checkItemMutex(newItems, existingItems)

// 格式化冲突消息
export function formatConflictMessage(conflicts)

// 清除缓存
export function clearMutexCache()

// 预加载数据
export async function preloadMutexData(itemGroupIds)
```

### 2. 前端调用位置

#### 2.1 CustomerRegGroupPannel.vue
```javascript
// 导入互斥检查工具
import { checkItemMutex, formatConflictMessage } from '@/utils/itemGroupMutexCheck';

// 在添加项目前进行互斥检查
const mutexCheck = await checkItemMutex(itemGroups, regGroupDataSource.value);
if (!mutexCheck.isValid) {
  const conflictMsg = formatConflictMessage(mutexCheck.conflicts);
  message.error('项目冲突：\n' + conflictMsg);
  return;
}

if (mutexCheck.warning) {
  message.warning(mutexCheck.warning);
}
```

#### 2.2 GroupListOfPannel.vue
同样的互斥检查逻辑，确保两个添加项目的入口都有互斥验证。

### 3. 后端互斥检查

#### 文件位置：`CustomerRegServiceImpl.addItemGroupWithCheckParts()`

**检查时机：**
1. **主项目互斥检查**：保存主项目前
2. **附属项目互斥检查**：保存附属项目前
3. **赠送项目互斥检查**：保存赠送项目前

**实现代码：**
```java
// 5. 验证是否存在互斥项目
try {
    itemGroupRelationService.checkIsHaveMutexes(customerRegItemGroupList);
} catch (Exception e) {
    throw new JeecgBootException("项目互斥验证失败：" + e.getMessage());
}

// 7. 获取附属项目
List<CustomerRegItemGroup> attachItemGroups = itemGroupRelationService.getAttachGroups(customerRegItemGroupList);
if (CollectionUtils.isNotEmpty(attachItemGroups)) {
    customerRegItemGroupList.addAll(attachItemGroups);
    //验证是否存在互斥项目
    try {
        itemGroupRelationService.checkIsHaveMutexes(attachItemGroups);
    } catch (Exception e) {
        throw new JeecgBootException("附属项目互斥验证失败：" + e.getMessage());
    }
    customerRegItemGroupService.saveBatch(attachItemGroups);
}

// 8. 获取赠送项目
List<CustomerRegItemGroup> giftItemGroups = itemGroupRelationService.getGiftGroups(allItemGroups);
if (CollectionUtils.isNotEmpty(giftItemGroups)) {
    customerRegItemGroupList.addAll(giftItemGroups);
    //验证是否存在互斥项目
    try {
        itemGroupRelationService.checkIsHaveMutexes(giftItemGroups);
    } catch (Exception e) {
        throw new JeecgBootException("赠送项目互斥验证失败：" + e.getMessage());
    }
    customerRegItemGroupService.saveBatch(giftItemGroups);
}
```

## 双重保护机制

### 1. 前端预检查
- **优势**：快速响应，用户体验好
- **机制**：基于缓存的互斥关系数据
- **目的**：提前发现冲突，避免无效请求

### 2. 后端最终验证
- **优势**：数据准确，安全可靠
- **机制**：基于最新的数据库数据
- **目的**：确保数据一致性，防止并发问题

## 性能优化策略

### 1. 前端缓存机制
```javascript
// 5分钟缓存，减少API调用
let mutexCache = new Map();
let cacheExpireTime = 0;
const CACHE_DURATION = 5 * 60 * 1000;

async function getMutexItems(itemGroupId) {
  // 检查缓存
  if (mutexCache.has(itemGroupId) && Date.now() < cacheExpireTime) {
    return mutexCache.get(itemGroupId);
  }
  
  // 从API获取并缓存
  const res = await getExclusiveGroups({ itemGroupId });
  mutexCache.set(itemGroupId, res);
  return res;
}
```

### 2. 错误降级处理
```javascript
try {
  // 互斥检查逻辑
} catch (error) {
  console.error('互斥检查失败:', error);
  // 如果检查失败，返回警告但不阻止操作
  return {
    isValid: true,
    conflicts: [],
    warning: '互斥检查失败，请注意项目冲突'
  };
}
```

## 用户体验设计

### 1. 友好的错误提示
```javascript
// 格式化冲突信息
export function formatConflictMessage(conflicts) {
  const messages = conflicts.map(conflict => {
    const newItemName = conflict.newItem.partName ? 
      `${conflict.newItem.name}-${conflict.newItem.partName}` : 
      conflict.newItem.name;
    const existingItemName = conflict.existingItem.partName ? 
      `${conflict.existingItem.name}-${conflict.existingItem.partName}` : 
      conflict.existingItem.name;
    
    return `"${newItemName}" 与 "${existingItemName}" 互斥`;
  });
  
  return messages.join('\n');
}
```

### 2. 多种提示类型
- **错误提示**：发现互斥冲突时阻止操作
- **警告提示**：检查失败时给出警告但不阻止
- **信息提示**：预加载完成等状态信息

## 业务场景处理

### 1. 典型互斥场景
```
场景：用户尝试添加"增强CT"，但已有"普通CT"
检查结果：发现互斥关系
用户提示：项目冲突："增强CT" 与 "普通CT" 互斥
用户操作：需要先移除"普通CT"再添加"增强CT"
```

### 2. 部位相关互斥
```
场景：用户尝试添加"胸部增强CT"，但已有"胸部普通CT"
检查结果：发现部位级别的互斥关系
用户提示：项目冲突："胸部增强CT" 与 "胸部普通CT" 互斥
用户操作：需要先移除胸部普通CT再添加胸部增强CT
```

### 3. 附属项目互斥
```
场景：主项目自动添加了附属项目，但附属项目与已有项目互斥
检查结果：后端检测到附属项目互斥
用户提示：附属项目互斥验证失败：附属项目"XXX"与已有项目"YYY"互斥
用户操作：需要先处理互斥项目
```

## 监控和维护

### 1. 性能监控
- 监控前端缓存命中率
- 监控互斥检查的响应时间
- 监控互斥冲突的发生频率

### 2. 数据质量监控
```sql
-- 监控互斥关系配置的使用情况
SELECT 
    main_group_id,
    exclusive_group_id,
    COUNT(*) as conflict_count
FROM item_group_relation 
WHERE relation_type = 'EXCLUSIVE'
    AND create_time >= DATE_SUB(NOW(), INTERVAL 1 DAY)
GROUP BY main_group_id, exclusive_group_id
ORDER BY conflict_count DESC;
```

### 3. 错误监控
- 监控前端互斥检查失败的情况
- 监控后端互斥验证异常
- 监控缓存失效和重建情况

## 配置管理

### 1. 互斥关系配置
**页面位置**：`src/views/basicinfo/components/ItemGroupRelationForm.vue`
**数据表**：`item_group_relation`
**关系类型**：`relationType = 'EXCLUSIVE'`

### 2. 配置验证
- 确保互斥关系的双向性
- 验证互斥关系的合理性
- 定期检查配置的有效性

## 相关文件

### 新增文件
- `src/utils/itemGroupMutexCheck.js` - 前端互斥检查工具

### 修改文件
- `src/views/reg/components/CustomerRegGroupPannel.vue` - 添加前端互斥检查
- `src/views/reg/GroupListOfPannel.vue` - 添加前端互斥检查
- `CustomerRegServiceImpl.java` - 恢复后端互斥验证

### 文档文件
- `docs/互斥检查功能重新添加说明.md` - 本文档

## 测试验证

### 1. 功能测试
1. **基本互斥测试**：验证基本的项目互斥检查
2. **部位互斥测试**：验证带部位的项目互斥
3. **附属项目互斥测试**：验证附属项目的互斥检查
4. **赠送项目互斥测试**：验证赠送项目的互斥检查

### 2. 性能测试
1. **缓存效果测试**：验证缓存机制的性能提升
2. **并发测试**：验证多用户同时操作的性能
3. **大数据量测试**：验证大量互斥关系的处理性能

### 3. 用户体验测试
1. **错误提示测试**：验证错误提示的友好性
2. **操作流程测试**：验证用户处理互斥冲突的流程
3. **边界情况测试**：验证各种边界情况的处理

## 总结

通过重新添加互斥检查功能，我们实现了：

✅ **完整的业务逻辑**：恢复了重要的互斥检查功能
✅ **双重保护机制**：前端预检查 + 后端最终验证
✅ **性能优化**：通过缓存机制减少API调用
✅ **用户体验**：友好的错误提示和处理流程
✅ **数据安全**：确保互斥规则的正确执行

这个设计既保证了业务逻辑的完整性，又通过优化策略保持了良好的性能和用户体验。
