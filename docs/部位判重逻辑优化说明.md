# 部位判重逻辑优化说明

## 优化背景

添加部位选择功能后，原来的项目判重逻辑需要调整。现在同一个项目可以有多个不同部位的记录，所以判重应该基于"项目+部位"的组合，而不是仅仅基于项目ID。

## 问题分析

### 原有判重逻辑
```javascript
// 只基于项目ID判重
if (groupList.find((item) => item.itemGroupId == group.itemGroupId)) {
  return; // 跳过添加
}
```

### 问题场景
1. **CT检查项目**：用户已添加"CT检查-左膝关节"
2. **套餐添加**：套餐中包含"CT检查"项目
3. **错误结果**：系统认为CT检查已存在，跳过添加
4. **期望结果**：应该提示用户通过部位选择器添加其他部位

## 优化方案

### 1. 统一判重函数

创建了 `checkItemGroupDuplicate` 函数来统一处理判重逻辑：

```javascript
function checkItemGroupDuplicate(existingGroups: CustomerRegItemGroup[], newGroup: ItemGroup): boolean {
  // 过滤出有效的现有项目（排除已减项和已退款的）
  const validExistingGroups = existingGroups.filter(item => 
    item.addMinusFlag !== -1 && item.payStatus !== '退款成功'
  );

  // 如果项目不需要部位选择，按原逻辑判重
  if (newGroup.hasCheckPart !== '1') {
    return validExistingGroups.some(item => item.itemGroupId === newGroup.id);
  }

  // 如果项目需要部位选择，检查是否已有该项目的记录
  const existingParts = validExistingGroups.filter(item => item.itemGroupId === newGroup.id);
  
  if (existingParts.length > 0) {
    const partNames = existingParts.map(item => item.checkPartName || '未知部位').join('、');
    console.log(`项目 ${newGroup.name} 已存在以下部位：${partNames}，建议使用部位选择器添加其他部位`);
    return true;
  }

  return false;
}
```

### 2. 特定组合判重函数

添加了 `checkItemPartCombinationDuplicate` 函数用于检查特定的项目-部位组合：

```javascript
function checkItemPartCombinationDuplicate(existingGroups: CustomerRegItemGroup[], itemGroupId: string, checkPartId: string): boolean {
  return existingGroups.some(item => 
    item.itemGroupId === itemGroupId && 
    item.checkPartId === checkPartId &&
    item.addMinusFlag !== -1 && 
    item.payStatus !== '退款成功'
  );
}
```

## 修复的代码位置

### 1. 套餐添加逻辑
**文件位置**: `src/views/reg/GroupListOfPannel.vue` 第1804行

**修复前**:
```javascript
if (groupList.find((item) => item.itemGroupId == group.itemGroupId)) {
  return;
}
```

**修复后**:
```javascript
const isDuplicate = checkItemGroupDuplicate(groupList, group);
if (isDuplicate) {
  console.log(`项目 ${group.name} 已存在，跳过添加`);
  return;
}
```

### 2. 批量添加逻辑
**文件位置**: `src/views/reg/GroupListOfPannel.vue` 第2100行

**修复前**:
```javascript
if (groupList.find((item) => item.itemGroupId == row.id)) {
  return;
}
```

**修复后**:
```javascript
const isDuplicate = checkItemGroupDuplicate(groupList, row);
if (isDuplicate) {
  console.log(`项目 ${row.name} 已存在，跳过添加`);
  return;
}
```

### 3. 必检项目过滤逻辑
**文件位置**: `src/views/reg/GroupListOfPannel.vue` 第1042行

**修复前**:
```javascript
return mustCheckItemGroup.value.filter((item) => 
  !regGroupDataSource.value.some((row) => row.itemGroupId === item.id)
);
```

**修复后**:
```javascript
return mustCheckItemGroup.value.filter((item) => {
  return !checkItemGroupDuplicate(regGroupDataSource.value, item);
});
```

### 4. 必检项目状态更新
**文件位置**: `src/views/reg/GroupListOfPannel.vue` 第1057行

**修复前**:
```javascript
item.mustCheckAdded = regGroupDataSource.value.some((row) => row.itemGroupId === item.id);
```

**修复后**:
```javascript
item.mustCheckAdded = checkItemGroupDuplicate(regGroupDataSource.value, item);
```

## 判重逻辑说明

### 1. 无需部位选择的项目
- **判重规则**: 基于 `itemGroupId` 判重
- **行为**: 如果项目已存在，直接跳过
- **适用**: `hasCheckPart !== '1'` 的项目

### 2. 需要部位选择的项目
- **判重规则**: 检查是否已有该项目的任何部位记录
- **行为**: 如果已有部位记录，提示使用部位选择器添加其他部位
- **适用**: `hasCheckPart === '1'` 的项目

### 3. 有效性过滤
所有判重逻辑都会过滤掉：
- `addMinusFlag === -1` (已减项的记录)
- `payStatus === '退款成功'` (已退款的记录)

## 用户体验改进

### 1. 智能提示
- 显示已存在的部位信息
- 引导用户使用正确的添加方式

### 2. 控制台日志
```javascript
console.log(`项目 ${newGroup.name} 已存在以下部位：${partNames}，建议使用部位选择器添加其他部位`);
```

### 3. 避免重复操作
- 防止用户重复添加相同的项目-部位组合
- 提供清晰的操作指引

## 后端支持

后端的 `addItemGroupWithCheckParts` 方法已经实现了基于项目-部位组合的判重：

```java
// 检查是否已存在相同的项目-部位组合
LambdaQueryWrapper<CustomerRegItemGroup> existQuery = new LambdaQueryWrapper<>();
existQuery.eq(CustomerRegItemGroup::getCustomerRegId, request.getCustomerRegId())
        .eq(CustomerRegItemGroup::getItemGroupId, request.getItemGroupId())
        .eq(CustomerRegItemGroup::getCheckPartId, checkPartId)
        .ne(CustomerRegItemGroup::getAddMinusFlag, -1); // 排除已减项的
```

## 测试场景

### 1. 普通项目添加
- 添加不需要部位的项目（如血常规）
- 验证基于项目ID的判重逻辑

### 2. 部位项目添加
- 添加需要部位的项目（如CT检查）
- 验证部位相关的判重逻辑

### 3. 套餐添加
- 添加包含部位项目的套餐
- 验证套餐中项目的判重处理

### 4. 必检项目
- 验证必检项目的过滤和状态更新
- 确保部位信息正确处理

## 注意事项

1. **向后兼容**: 保持对无部位项目的兼容性
2. **性能考虑**: 判重逻辑在前端执行，避免不必要的后端请求
3. **用户引导**: 通过日志和提示引导用户正确操作
4. **数据一致性**: 与后端判重逻辑保持一致
