# 依赖检查功能增强说明

## 概述

根据业务需求，依赖检查不仅应该在结果录入时触发，在添加项目后也应该触发，以确保新添加的项目满足依赖关系。本次增强了依赖检查功能，实现了全流程的依赖验证。

## 增强内容

### 1. 前端依赖检查增强

#### 1.1 添加依赖检查器初始化

**CustomerRegGroupPannel.vue 和 GroupListOfPannel.vue：**
```javascript
import { DependencyChecker } from '@/utils/DependencyChecker';

// 依赖检查器
const dependencyChecker = ref(null);

// 初始化依赖检查器
onMounted(() => {
  dependencyChecker.value = new DependencyChecker(regGroupDataSource);
});
```

#### 1.2 添加项目后的依赖检查方法

```javascript
// 添加项目后的依赖检查
async function checkDependenciesAfterAdd(addedItems) {
  if (!dependencyChecker.value) {
    console.warn('依赖检查器未初始化');
    return;
  }

  const missingDependencies = [];
  
  for (const item of addedItems) {
    const validation = dependencyChecker.value.checkDependencies(item.itemGroupId);
    if (!validation.valid) {
      missingDependencies.push({
        itemName: item.itemGroupName,
        partName: item.checkPartName,
        missing: validation.missing
      });
    }
  }

  if (missingDependencies.length > 0) {
    // 格式化依赖提示信息
    const messages = missingDependencies.map(dep => {
      const itemDisplay = dep.partName ? `${dep.itemName}-${dep.partName}` : dep.itemName;
      const missingItems = dep.missing.map(m => m.name).join('、');
      return `项目"${itemDisplay}"缺少依赖项目：${missingItems}`;
    });
    
    message.warning({
      content: `检测到依赖项目缺失：\n${messages.join('\n')}\n建议添加相关依赖项目以确保检查流程完整`,
      duration: 8, // 延长显示时间
    });
  }
}
```

#### 1.3 在添加项目成功后调用依赖检查

```javascript
const res = await addItemGroupWithCheckParts(params);
if (res.success) {
  const partNames = selectedPartNames.join('、');
  message.success(`成功添加 ${checkPartState.currentItemGroup.name} - ${partNames}`);

  // 刷新列表
  await fetchCustomerRegGroupList(selectedCustomerReg.value.id);
  
  // 🆕 添加项目后进行依赖检查
  await checkDependenciesAfterAdd(itemGroups);
  
  closeCheckPartSelector();
}
```

### 2. 后端依赖检查增强

#### 2.1 主项目依赖验证

```java
// 5.1 验证依赖项目
try {
    itemGroupRelationService.checkDependentGroups(customerRegItemGroupList);
} catch (Exception e) {
    throw new JeecgBootException("项目依赖验证失败：" + e.getMessage());
}
```

#### 2.2 附属项目依赖验证

```java
// 7. 获取附属项目
List<CustomerRegItemGroup> attachItemGroups = itemGroupRelationService.getAttachGroups(customerRegItemGroupList);
if (CollectionUtils.isNotEmpty(attachItemGroups)) {
    customerRegItemGroupList.addAll(attachItemGroups);
    
    // 验证附属项目的依赖关系
    try {
        itemGroupRelationService.checkDependentGroups(attachItemGroups);
    } catch (Exception e) {
        throw new JeecgBootException("附属项目依赖验证失败：" + e.getMessage());
    }
    
    customerRegItemGroupService.saveBatch(attachItemGroups);
}
```

#### 2.3 赠送项目依赖验证

```java
// 8. 获取赠送项目
List<CustomerRegItemGroup> giftItemGroups = itemGroupRelationService.getGiftGroups(allItemGroups);
if (CollectionUtils.isNotEmpty(giftItemGroups)) {
    customerRegItemGroupList.addAll(giftItemGroups);
    
    // 验证赠送项目的依赖关系
    try {
        itemGroupRelationService.checkDependentGroups(giftItemGroups);
    } catch (Exception e) {
        throw new JeecgBootException("赠送项目依赖验证失败：" + e.getMessage());
    }
    
    customerRegItemGroupService.saveBatch(giftItemGroups);
}
```

## 完整的依赖检查流程

### 1. 添加项目时的依赖检查流程

```
1. 前端数据拼装
   ↓
2. 前端互斥预检查
   ↓
3. 后端主项目互斥验证
   ↓
4. 🆕 后端主项目依赖验证
   ↓
5. 后端保存主项目
   ↓
6. 后端获取附属项目
   ↓
7. 后端附属项目互斥验证
   ↓
8. 🆕 后端附属项目依赖验证
   ↓
9. 后端保存附属项目
   ↓
10. 后端获取赠送项目
    ↓
11. 后端赠送项目互斥验证
    ↓
12. 🆕 后端赠送项目依赖验证
    ↓
13. 后端保存赠送项目
    ↓
14. 🆕 前端依赖检查提醒
    ↓
15. 完成
```

### 2. 结果录入时的依赖检查流程（保持不变）

```
1. 用户录入结果
   ↓
2. 前端依赖检查 (DependencyChecker)
   ↓
3. 如果有缺失依赖 → 显示验证弹窗
   ↓
4. 用户选择：强制保存 或 取消
   ↓
5. 保存结果
```

## 依赖检查的层次

### 1. 严格验证（后端）
- **时机**：添加项目时
- **作用**：阻止不满足依赖关系的项目添加
- **异常处理**：抛出异常，阻止保存

### 2. 友好提醒（前端）
- **时机**：添加项目成功后
- **作用**：提醒用户注意依赖关系，但不阻止操作
- **用户体验**：警告提示，建议添加依赖项目

### 3. 结果验证（前端）
- **时机**：保存结果前
- **作用**：确保结果录入的完整性
- **用户选择**：允许强制保存或取消

## 业务场景处理

### 1. 典型依赖场景

#### 场景1：添加需要依赖的项目
```
用户操作：尝试添加"心脏彩超"
系统检测：心脏彩超依赖"心电图"
后端验证：检查是否已有心电图项目
结果：
- 如果有心电图 → 允许添加
- 如果无心电图 → 抛出异常，阻止添加
```

#### 场景2：添加项目后的友好提醒
```
用户操作：成功添加"心脏彩超"
前端检查：检测到心脏彩超依赖心电图
系统提示：检测到依赖项目缺失：项目"心脏彩超"缺少依赖项目：心电图
用户操作：可以选择添加心电图项目
```

#### 场景3：附属项目的依赖检查
```
主项目：胸部CT
附属项目：心电图（依赖血压测量）
系统检查：验证心电图的依赖项目血压测量是否存在
结果：如果缺少血压测量，阻止添加整个项目组合
```

### 2. 部位相关的依赖

#### 场景：部位级别的依赖关系
```
项目：胸部CT-胸部
依赖：胸部X光-胸部
检查逻辑：不仅检查项目依赖，还要检查部位匹配
结果：只有当胸部X光的胸部部位存在时，才允许添加胸部CT的胸部
```

## 用户体验设计

### 1. 错误提示层次

#### 严格错误（后端验证失败）
```
错误类型：项目依赖验证失败
提示信息：项目依赖验证失败：项目"心脏彩超"依赖项目"心电图"，但该依赖项目不存在
用户操作：必须先添加依赖项目
```

#### 友好提醒（前端检查）
```
提示类型：警告提示
提示信息：检测到依赖项目缺失：
项目"心脏彩超"缺少依赖项目：心电图
建议添加相关依赖项目以确保检查流程完整
用户操作：可以选择添加或忽略
```

### 2. 提示信息优化

#### 详细的依赖信息
- 显示具体的项目名称
- 显示部位信息（如果有）
- 列出所有缺失的依赖项目
- 提供操作建议

#### 延长显示时间
```javascript
message.warning({
  content: `检测到依赖项目缺失：\n${messages.join('\n')}\n建议添加相关依赖项目以确保检查流程完整`,
  duration: 8, // 延长显示时间，让用户有足够时间阅读
});
```

## 性能考虑

### 1. 前端性能优化
- 依赖检查器初始化一次，重复使用
- 异步执行依赖检查，不阻塞界面
- 只在添加项目成功后进行检查

### 2. 后端性能优化
- 批量验证依赖关系
- 缓存依赖关系配置
- 分别验证主项目、附属项目、赠送项目

### 3. 用户体验优化
- 前端提醒不阻塞操作流程
- 后端验证确保数据完整性
- 提供清晰的错误信息和操作建议

## 配置管理

### 依赖关系配置
**页面位置**：`src/views/basicinfo/components/ItemGroupRelationForm.vue`
**数据表**：`item_group_relation`
**关系类型**：`relationType = 'DEPENDENT'`

### 依赖类型
- **大项依赖**：项目级别的依赖关系
- **小项依赖**：具体检查项的依赖关系
- **部位依赖**：特定部位的依赖关系

## 监控和维护

### 1. 依赖验证统计
```sql
-- 统计依赖验证失败的情况
SELECT 
    DATE(create_time) as date,
    COUNT(*) as failure_count
FROM system_log 
WHERE log_content LIKE '%依赖验证失败%'
    AND create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY DATE(create_time)
ORDER BY date DESC;
```

### 2. 依赖关系使用统计
```sql
-- 统计依赖关系的使用情况
SELECT 
    main_group_id,
    dependent_group_id,
    COUNT(*) as usage_count
FROM item_group_relation 
WHERE relation_type = 'DEPENDENT'
    AND create_time >= DATE_SUB(NOW(), INTERVAL 1 DAY)
GROUP BY main_group_id, dependent_group_id
ORDER BY usage_count DESC;
```

## 相关文件

### 修改文件
- `src/views/reg/components/CustomerRegGroupPannel.vue` - 添加前端依赖检查
- `src/views/reg/GroupListOfPannel.vue` - 添加前端依赖检查
- `CustomerRegServiceImpl.java` - 添加后端依赖验证

### 依赖文件
- `src/utils/DependencyChecker.js` - 依赖检查工具
- `src/components/DependencyValidationModal.vue` - 依赖验证弹窗

### 文档文件
- `docs/依赖检查功能增强说明.md` - 本文档

## 测试验证

### 1. 功能测试
1. **基本依赖测试**：验证基本的项目依赖检查
2. **部位依赖测试**：验证带部位的项目依赖
3. **附属项目依赖测试**：验证附属项目的依赖检查
4. **赠送项目依赖测试**：验证赠送项目的依赖检查
5. **多层依赖测试**：验证复杂的依赖关系链

### 2. 用户体验测试
1. **错误提示测试**：验证各种错误提示的友好性
2. **操作流程测试**：验证用户处理依赖问题的流程
3. **性能测试**：验证依赖检查对性能的影响

## 总结

通过这次依赖检查功能增强，我们实现了：

✅ **全流程依赖验证**：添加项目时和结果录入时都进行依赖检查
✅ **多层次验证**：后端严格验证 + 前端友好提醒
✅ **完整的业务覆盖**：主项目、附属项目、赠送项目都进行依赖验证
✅ **用户体验优化**：清晰的错误提示和操作建议
✅ **性能保证**：异步检查，不影响操作流程

这个设计确保了依赖关系在整个业务流程中得到正确的验证和处理，提升了数据质量和用户体验。
