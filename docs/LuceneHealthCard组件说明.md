# LuceneHealthCard 组件说明文档

## 概述

LuceneHealthCard 是一个用于显示和管理 Lucene 搜索引擎健康状态的 Vue 组件。该组件提供了完整的索引健康监控、操作管理和任务跟踪功能。

## 功能特性

### 1. 健康状态监控
- **整体状态显示**：根据索引存在性和健康状态显示整体状况
- **实时指标**：搜索成功率、索引成功率、文档数量等关键指标
- **状态分级**：优秀、良好、正常、需关注等多级状态显示
- **颜色编码**：使用不同颜色直观表示各项指标的健康程度

### 2. 索引操作管理
- **创建索引**：初始化 Lucene 索引
- **更新索引**：增量更新索引内容
- **重建索引**：完全重建所有索引
- **修复索引**：修复损坏的索引文件
- **智能按钮状态**：根据索引状态自动启用/禁用相关操作

### 3. 任务监控
- **实时任务跟踪**：监控正在执行的索引任务
- **任务历史记录**：保存最近的任务执行历史
- **进度显示**：实时显示任务执行进度
- **自适应轮询**：根据任务状态调整监控频率

## 数据结构

### 健康数据结构
```typescript
interface HealthData {
  healthy: boolean;                // 整体健康状态
  lastCheckTime: number;          // 最后检查时间戳
  totalSearchCount: number;       // 总搜索次数
  failedSearchCount: number;      // 失败搜索次数
  totalIndexCount: number;        // 总索引次数
  failedIndexCount: number;       // 失败索引次数
  searchSuccessRate: number;      // 搜索成功率 (0-1)
  indexSuccessRate: number;       // 索引成功率 (0-1)
  indexExists: boolean;           // 索引是否存在
  indexDocumentCount: number;     // 索引文档数量
  indexSizeInfo: string;          // 索引大小信息
}
```

### 任务数据结构
```typescript
interface TaskStatus {
  taskId: string;                 // 任务ID
  taskType: string;               // 任务类型
  status: string;                 // 任务状态
  startTime: number;              // 开始时间
  endTime?: number;               // 结束时间
  duration?: number;              // 执行时长
  progress?: number;              // 进度百分比
  message?: string;               // 状态消息
}
```

## 组件结构

### 1. 操作按钮区域
- 刷新状态按钮
- 健康检查按钮
- 响应式布局适配

### 2. 核心指标卡片
- 整体状态指示器
- 索引状态和文档数量
- 搜索成功率和次数统计
- 索引成功率和次数统计

### 3. 详细信息区域
- **时间信息**：最后检查时间、检查间隔
- **索引详情**：索引存在状态、文档数量、索引大小
- **操作统计**：搜索和索引的成功/失败次数

### 4. 索引操作区域
- 操作按钮组
- 智能状态提示
- 操作确认对话框

### 5. 任务监控区域
- 当前任务状态显示
- 任务进度条
- 轮询状态指示器
- 任务历史记录

## 主要方法

### 数据获取方法
- `refreshHealth()`: 刷新健康报告
- `performHealthCheck()`: 执行强制健康检查
- `refreshTaskStatus()`: 刷新任务状态

### 索引操作方法
- `handleCreateIndex()`: 创建索引
- `handleUpdateIndex()`: 更新索引
- `handleRebuildIndex()`: 重建索引
- `handleRepairIndex()`: 修复索引

### 任务监控方法
- `startTaskMonitoring()`: 启动任务监控
- `startActivePolling()`: 开始活跃轮询 (3秒间隔)
- `startSlowPolling()`: 开始慢速轮询 (30秒间隔)
- `stopPolling()`: 停止轮询

### 工具方法
- `formatNumber()`: 格式化数字显示 (K/M 单位)
- `formatIndexSize()`: 格式化索引大小
- `getTimeSinceLastCheck()`: 计算距离上次检查的时间
- `getSuccessRateColor()`: 根据成功率获取颜色
- `getOverallStatusText()`: 获取整体状态文本

## 样式特性

### 1. 响应式设计
- 移动端适配
- 弹性布局
- 自适应列宽

### 2. 视觉效果
- 渐变背景
- 悬停动画
- 阴影效果
- 颜色编码

### 3. 交互反馈
- 加载状态
- 按钮禁用
- 状态提示
- 进度显示

## 使用示例

```vue
<template>
  <LuceneHealthCard ref="healthCardRef" />
</template>

<script setup>
import { ref } from 'vue';
import LuceneHealthCard from './components/LuceneHealthCard.vue';

const healthCardRef = ref();

// 手动刷新健康状态
const refreshHealth = () => {
  healthCardRef.value?.refreshHealth();
};
</script>
```

## API 接口

### 健康检查相关
- `GET /summary/lucene/healthReport` - 获取健康报告
- `POST /summary/lucene/forceHealthCheck` - 强制健康检查

### 索引操作相关
- `POST /summary/lucene/createIndex` - 创建索引
- `POST /summary/lucene/updateNecessaryIndex` - 更新必要索引
- `POST /summary/lucene/rebuildAllIndex` - 重建所有索引
- `POST /summary/lucene/repairIndex` - 修复索引

### 任务监控相关
- `GET /summary/lucene/taskStatus` - 获取任务状态

## 注意事项

1. **权限控制**：确保用户有执行索引操作的权限
2. **数据备份**：重建索引前建议备份重要数据
3. **性能影响**：大量数据的索引操作可能影响系统性能
4. **监控频率**：任务监控会根据任务状态自动调整轮询频率
5. **错误处理**：所有操作都包含完整的错误处理和用户提示

## 更新日志

### v2.0.0 (当前版本)
- 重构UI布局，采用卡片式设计
- 增强视觉效果和交互体验
- 优化数据格式化和显示
- 改进响应式设计
- 增加智能操作提示
- 完善任务监控功能
