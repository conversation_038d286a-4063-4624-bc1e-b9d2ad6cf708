# 字典组件性能优化方案

## 当前机制分析

### 1. 字典数据预加载机制 ✅

**优势：**
- 用户登录时一次性加载所有字典数据
- 数据存储在 `userStore` 和本地缓存中
- 避免了重复的网络请求

**实现位置：**
- `src/store/modules/user.ts` - `getUserInfoAction()`
- `src/utils/dict/index.ts` - `getDictItemsByCode()`

### 2. 字典组件智能加载策略 ✅

**加载顺序：**
1. **缓存优先**：检查 `userStore.getAllDictItems`
2. **本地缓存**：检查 `localStorage` 中的字典数据
3. **异步请求**：缓存未命中时发起网络请求

**组件渲染策略：**
- 组件立即渲染基础结构
- 显示加载状态（如 `JDictSelectTag` 的 `loadingEcho`）
- 字典数据加载完成后更新显示

## 性能优化建议

### 1. 骨架屏优化 🚀

**当前状态：**
```vue
<!-- JDictSelectTag.vue 已实现 -->
<a-input v-if="loadingEcho" readOnly placeholder="加载中…">
  <template #prefix>
    <LoadingOutlined />
  </template>
</a-input>
```

**建议：**
- 为更多字典组件添加骨架屏
- 统一加载状态的视觉效果

### 2. 字典数据分批加载 🚀

**问题：**
- 一次性加载所有字典可能导致首次登录较慢
- 某些字典可能永远不会被使用

**解决方案：**
```typescript
// 核心字典预加载，非核心字典按需加载
const CORE_DICT_CODES = [
  'examination_type',
  'yes_no',
  'sex',
  // ... 其他核心字典
];

// 分批加载策略
async function loadDictByPriority() {
  // 1. 立即加载核心字典
  await loadCoreDictItems(CORE_DICT_CODES);
  
  // 2. 延迟加载其他字典
  setTimeout(() => {
    loadRemainingDictItems();
  }, 1000);
}
```

### 3. 字典数据缓存优化 🚀

**当前缓存策略：**
- 内存缓存：`userStore.dictItems`
- 本地缓存：`localStorage[DB_DICT_DATA_KEY]`

**优化建议：**
```typescript
// 添加缓存过期机制
interface DictCacheItem {
  data: any[];
  timestamp: number;
  ttl: number; // 生存时间
}

// 缓存版本控制
const DICT_CACHE_VERSION = '1.0.0';
```

### 4. 组件级别优化 🚀

#### A. 懒加载字典组件
```typescript
// 对于复杂的字典组件，使用异步加载
const JDictSelectTag = defineAsyncComponent({
  loader: () => import('./JDictSelectTag.vue'),
  loadingComponent: DictLoadingSkeleton,
  delay: 200,
});
```

#### B. 虚拟滚动优化
```vue
<!-- 对于大量选项的字典，使用虚拟滚动 -->
<a-select
  :virtual="true"
  :listHeight="256"
  :options="dictOptions"
/>
```

### 5. 网络请求优化 🚀

#### A. 请求合并
```typescript
// 将多个字典请求合并为一个
async function batchLoadDictItems(dictCodes: string[]) {
  const response = await defHttp.post({
    url: '/sys/dict/batchGetDictItems',
    data: { dictCodes }
  });
  return response;
}
```

#### B. 请求缓存
```typescript
// 使用 Map 缓存正在进行的请求，避免重复请求
const pendingRequests = new Map<string, Promise<any>>();

export function loadDictWithCache(dictCode: string) {
  if (pendingRequests.has(dictCode)) {
    return pendingRequests.get(dictCode);
  }
  
  const promise = ajaxGetDictItems(dictCode);
  pendingRequests.set(dictCode, promise);
  
  promise.finally(() => {
    pendingRequests.delete(dictCode);
  });
  
  return promise;
}
```

## 实施建议

### 阶段一：立即可实施 ⭐
1. **保持现有预加载机制** - 已经很好地解决了大部分性能问题
2. **为缺少加载状态的字典组件添加骨架屏**
3. **统一字典组件的加载状态样式**

### 阶段二：中期优化 ⭐⭐
1. **实施字典数据分批加载**
2. **添加字典缓存过期机制**
3. **优化网络请求策略**

### 阶段三：长期优化 ⭐⭐⭐
1. **实施虚拟滚动**
2. **字典组件懒加载**
3. **智能预测和预加载**

## 结论

**当前系统已经有很好的字典加载机制，不需要等待所有字典组件加载完成。**

**核心优势：**
- ✅ 字典数据预加载避免了运行时的网络延迟
- ✅ 缓存机制减少了重复请求
- ✅ 组件可以立即渲染，数据异步填充
- ✅ 用户体验良好，无明显的加载阻塞

**建议：**
- 保持现有机制
- 重点优化加载状态的用户体验
- 考虑分批加载以进一步提升首次登录速度

## 监控指标

建议监控以下指标来评估优化效果：
- 首次登录时间
- 字典数据加载时间
- 页面渲染完成时间
- 字典缓存命中率
