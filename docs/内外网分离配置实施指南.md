# 内外网分离配置实施指南

## 🎯 实施目标

通过内外网分离配置解决混合内容问题，确保系统在不同网络环境下都能正确加载文件。

## 📋 配置清单

### 1. 开发环境配置 (.env.development)
```bash
# 文件上传类型
VITE_GLOB_UPLOAD_TYPE=minio

# 内网地址（HTTP，开发环境直连）
VITE_GLOB_MINIO_INTERNAL_URL=http://************:9000

# 外网地址（开发环境可以使用相同地址）
VITE_GLOB_MINIO_EXTERNAL_URL=http://************:9000
```

### 2. 生产环境配置 (.env.production)
```bash
# 文件上传类型
VITE_GLOB_UPLOAD_TYPE=minio

# 内网地址（HTTP，避免证书问题）
VITE_GLOB_MINIO_INTERNAL_URL=http://************:9000

# 外网地址（HTTPS，通过代理访问）
VITE_GLOB_MINIO_EXTERNAL_URL=https://************:8888/minio
```

## 🔧 验证步骤

### 第一步：配置验证
1. 启动应用后，打开浏览器控制台
2. 运行配置验证命令：
```javascript
// 快速验证
await window.configValidator.validate();
```

### 第二步：网络测试
```javascript
// 网络环境测试
await window.networkTest.quick();
```

### 第三步：文件URL测试
```javascript
// 测试文件URL生成
import { getFileAccessHttpUrlAsync } from '@/utils/fileUrlHelper';
const testUrl = await getFileAccessHttpUrlAsync('test/image.jpg');
console.log('生成的文件URL:', testUrl);
```

## 🌐 工作原理

### 自动环境检测
系统会自动检测当前网络环境：

1. **内网环境检测**
   - 测试内网URL连通性
   - 优先使用HTTP内网地址
   - 避免SSL证书问题

2. **外网环境检测**
   - 内网不通时测试外网URL
   - 使用HTTPS外网地址
   - 确保安全传输

3. **协议适配**
   - HTTPS页面优先使用HTTPS资源
   - HTTP页面可以使用任意协议
   - 自动处理混合内容问题

### URL选择策略
```
当前页面协议: HTTPS
├── 优先: 外网HTTPS URL
├── 备选: 内网HTTP URL (会有警告)
└── 降级: 配置的默认URL

当前页面协议: HTTP
├── 优先: 内网HTTP URL
├── 备选: 外网HTTP/HTTPS URL
└── 降级: 配置的默认URL
```

## 📊 验证结果解读

### 配置验证报告示例
```
🔧 MinIO配置验证报告
📋 配置信息
  上传类型: minio
  内网URL: http://************:9000
  外网URL: https://************:8888/minio
验证状态: ✅ 通过

🌐 网络测试结果
  当前环境: internal
  最佳URL: http://************:9000
```

### 常见问题及解决方案

#### 1. 配置未生效
**问题**: 验证显示仍在使用旧配置
**解决**: 
- 重启开发服务器
- 清除浏览器缓存
- 检查环境变量文件路径

#### 2. 内网URL无法访问
**问题**: 内网URL连通性测试失败
**解决**:
- 检查MinIO服务是否启动
- 确认IP地址和端口正确
- 检查防火墙设置

#### 3. 外网URL证书问题
**问题**: HTTPS外网URL访问失败
**解决**:
- 检查SSL证书是否有效
- 考虑使用Nginx代理
- 临时使用HTTP外网地址

## 🚀 部署建议

### 开发环境
```bash
# 简单配置，内外网使用相同地址
VITE_GLOB_MINIO_INTERNAL_URL=http://************:9000
VITE_GLOB_MINIO_EXTERNAL_URL=http://************:9000
```

### 测试环境
```bash
# 模拟生产环境，测试内外网切换
VITE_GLOB_MINIO_INTERNAL_URL=http://************:9000
VITE_GLOB_MINIO_EXTERNAL_URL=https://test-files.yourdomain.com/minio
```

### 生产环境
```bash
# 完整的内外网分离配置
VITE_GLOB_MINIO_INTERNAL_URL=http://************:9000
VITE_GLOB_MINIO_EXTERNAL_URL=https://files.yourdomain.com/minio
```

## 🔍 监控和维护

### 1. 定期检查
- 每周运行配置验证
- 监控网络切换频率
- 检查文件访问成功率

### 2. 日志监控
关注控制台中的以下信息：
- 网络环境切换日志
- 混合内容警告
- URL连通性测试结果

### 3. 性能优化
- 缓存网络测试结果
- 减少不必要的连通性检查
- 优化URL选择算法

## 📝 故障排除

### 快速诊断命令
```javascript
// 1. 检查当前配置
console.log('当前配置:', window.configValidator.validator.generateConfigSuggestions());

// 2. 检查网络环境
console.log('网络环境:', window.networkTest.manager.getEnvironmentInfo());

// 3. 测试文件URL
const testResult = await window.networkTest.helper.testUrlConnectivity('http://************:9000/test.jpg');
console.log('连通性测试:', testResult);

// 4. 检查混合内容
const mixedInfo = window.mixedContentHelper.detectMixedContent('http://************:9000/test.jpg');
console.log('混合内容检查:', mixedInfo);
```

### 常见错误代码
- `ERR_SSL_PROTOCOL_ERROR`: SSL协议错误，检查证书配置
- `ERR_CONNECTION_REFUSED`: 连接被拒绝，检查服务状态
- `Mixed Content`: 混合内容被阻止，检查协议配置

## ✅ 验收标准

配置成功的标志：
1. ✅ 配置验证通过
2. ✅ 网络测试显示正确的环境检测
3. ✅ 文件URL能够正确生成和访问
4. ✅ HTTPS页面能够正常加载图片
5. ✅ 内外网环境切换正常

完成以上验证后，内外网分离配置即可投入使用！
