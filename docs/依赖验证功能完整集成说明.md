# 依赖验证功能完整集成说明

## 功能概述

已完成依赖项目验证功能在所有保存操作中的完整集成，现在用户在执行任何保存操作时都会自动检查依赖关系并提供友好的交互提示。

## 已集成的保存操作

### 1. 暂存按钮 ✅
- **触发方式**：点击"暂存"按钮
- **对应方法**：`saveResult()` → `doSave()`
- **验证范围**：所有有结果的项目组
- **验证逻辑**：全局依赖验证

### 2. 保存并生成小结按钮 ✅
- **触发方式**：点击"保存并生成小结"按钮
- **对应方法**：`saveSummary()`
- **验证范围**：所有有结果的项目组
- **验证逻辑**：全局依赖验证

### 3. 保存组合数据图标 ✅
- **触发方式**：点击项目组右上角的保存图标
- **对应方法**：`saveGroupItemResult()` → `doSaveGroupResult()`
- **验证范围**：当前项目组
- **验证逻辑**：单项目组依赖验证

## 验证逻辑说明

### 全局依赖验证 (`validateDependencies`)

**适用场景：**
- 暂存操作
- 保存并生成小结操作

**验证逻辑：**
```javascript
async function validateDependencies() {
  // 1. 检查依赖检查器是否初始化
  if (!dependencyChecker.value) return true;
  
  // 2. 获取所有有结果的项目组
  const groupsWithResults = groupList.value.filter(group => {
    return group.itemList && group.itemList.some(item => {
      return item.itemResult && item.itemResult.value !== '';
    });
  });
  
  // 3. 检查每个项目组的依赖关系
  const allMissing = [];
  for (const group of groupsWithResults) {
    const validation = dependencyChecker.value.checkDependencies(group.itemGroupId);
    if (!validation.valid) {
      allMissing.push({ group, missing: validation.missing });
    }
  }
  
  // 4. 如果有缺失依赖，显示验证弹窗
  if (allMissing.length > 0) {
    return showDependencyModal(allMissing);
  }
  
  return true;
}
```

### 单项目组依赖验证 (`validateGroupDependencies`)

**适用场景：**
- 保存单个项目组数据

**验证逻辑：**
```javascript
async function validateGroupDependencies(group) {
  // 1. 检查依赖检查器是否初始化
  if (!dependencyChecker.value) return true;
  
  // 2. 检查该项目组是否有结果
  const hasResults = group.itemList && group.itemList.some(item => {
    return item.itemResult && item.itemResult.value !== '';
  });
  
  if (!hasResults) return true;
  
  // 3. 检查该项目组的依赖关系
  const validation = dependencyChecker.value.checkDependencies(group.itemGroupId);
  
  // 4. 如果有缺失依赖，显示验证弹窗
  if (!validation.valid) {
    return showDependencyModal([{ group, missing: validation.missing }]);
  }
  
  return true;
}
```

## 用户交互流程

### 场景1：全局保存操作（暂存/保存并生成小结）

```
用户点击按钮
    ↓
检查所有有结果的项目组
    ↓
发现依赖缺失？
    ↓ 是
显示依赖验证弹窗
    ↓
用户选择操作：
├─ 点击"去录入" → 导航到缺失项目 → 弹窗关闭
├─ 点击"强制继续" → 继续保存操作 → 弹窗关闭
└─ 点击"取消录入" → 中断保存操作 → 弹窗关闭
    ↓ 否
直接执行保存操作
```

### 场景2：单项目组保存操作

```
用户点击项目组保存图标
    ↓
检查当前项目组是否有结果
    ↓
检查当前项目组的依赖关系
    ↓
发现依赖缺失？
    ↓ 是
显示依赖验证弹窗（针对当前项目组）
    ↓
用户选择操作：
├─ 点击"去录入" → 导航到缺失项目 → 弹窗关闭
├─ 点击"强制继续" → 继续保存操作 → 弹窗关闭
└─ 点击"取消录入" → 中断保存操作 → 弹窗关闭
    ↓ 否
直接执行保存操作
```

## 测试场景

### 测试场景1：暂存操作依赖验证

**步骤：**
1. 配置项目依赖关系（如：肺功能检查依赖身高、体重）
2. 录入肺功能检查结果，但不录入身高、体重
3. 点击"暂存"按钮

**预期结果：**
- ✅ 弹出依赖验证弹窗
- ✅ 显示缺失的依赖项目（身高、体重）
- ✅ 提供导航和强制保存选项

### 测试场景2：保存并生成小结依赖验证

**步骤：**
1. 录入多个项目的结果，但缺少某些依赖项目
2. 点击"保存并生成小结"按钮

**预期结果：**
- ✅ 弹出依赖验证弹窗
- ✅ 显示所有缺失的依赖项目
- ✅ 可以选择强制继续生成小结

### 测试场景3：单项目组保存依赖验证

**步骤：**
1. 在某个有依赖关系的项目组中录入结果
2. 点击该项目组右上角的保存图标

**预期结果：**
- ✅ 弹出依赖验证弹窗（仅针对当前项目组）
- ✅ 显示当前项目组缺失的依赖项目
- ✅ 可以导航到依赖项目或强制保存

### 测试场景4：依赖满足时的正常保存

**步骤：**
1. 先录入所有依赖项目的结果
2. 再录入主项目的结果
3. 执行任意保存操作

**预期结果：**
- ✅ 不弹出依赖验证弹窗
- ✅ 直接执行保存操作
- ✅ 显示保存成功提示

## 功能特点

### 1. 全面覆盖
- **所有保存操作**：覆盖了系统中所有的结果保存操作
- **智能识别**：自动识别有结果的项目进行验证
- **精确验证**：针对不同操作采用相应的验证策略

### 2. 用户友好
- **清晰提示**：明确显示缺失的依赖项目
- **便捷导航**：一键跳转到需要录入的项目
- **灵活选择**：支持强制继续或取消操作

### 3. 性能优化
- **按需验证**：只验证有结果的项目
- **缓存机制**：依赖关系数据缓存，避免重复请求
- **异步处理**：验证过程不阻塞界面响应

### 4. 扩展性强
- **模块化设计**：验证逻辑独立，易于维护
- **配置驱动**：依赖关系通过配置管理
- **类型支持**：支持大项和小项两种依赖类型

## 调试信息

### 控制台日志

在执行保存操作时，可以在浏览器控制台看到以下日志：

```
// 依赖检查器状态
依赖检查器未初始化，跳过依赖验证
依赖关系加载完成: Map(3) {...}

// 验证过程
没有项目有结果，跳过依赖验证
所有依赖验证通过
项目组 肺功能检查 依赖验证通过

// 用户操作
导航到依赖项目: {type: "ITEM", groupId: "...", itemId: "..."}
依赖验证确认: {group: {...}, missing: [...], forceConfirm: true}
依赖验证取消: {group: {...}, missing: [...]}
```

### 网络请求

- `getDependenciesByGroupIds` - 加载依赖关系（页面初始化时）
- `saveItemResult` - 保存项目结果（验证通过后）

## 注意事项

### 1. 依赖关系配置
- 确保在"项目组管理"中正确配置了依赖关系
- 依赖关系会在页面加载时自动获取

### 2. 数据同步
- 依赖验证基于当前页面的数据状态
- 如果其他地方修改了结果，需要刷新页面

### 3. 性能考虑
- 大量项目时验证可能需要一定时间
- 可以通过强制继续跳过验证

## 总结

依赖项目验证功能现已完全集成到结果录入界面的所有保存操作中，提供了：

1. **完整的依赖关系检查**：确保数据完整性
2. **友好的用户交互**：清晰的提示和便捷的操作
3. **灵活的处理方式**：支持导航、强制保存和取消操作
4. **全面的功能覆盖**：所有保存操作都包含依赖验证

用户现在可以在任何保存操作中得到智能的依赖项目提醒，大大提升了数据录入的准确性和用户体验。
