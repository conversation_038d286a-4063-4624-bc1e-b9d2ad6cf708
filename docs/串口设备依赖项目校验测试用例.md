# 串口设备依赖项目校验测试用例

## 测试环境准备

### 1. 数据准备
- 创建测试体检人员
- 配置项目组和依赖关系
- 设置串口设备配置
- 准备测试数据

### 2. 设备配置
```json
{
  "name": "测试设备",
  "deviceModel": "TEST_DEVICE_001",
  "groupId": "test-group-001",
  "websocketUrl": "ws://localhost:8080/websocket",
  "cmdFunction": "return {deviceCode: 'TEST_DEVICE_001', command: 'START_TEST', patientId: customerReg.id};",
  "extraConfig": "{\"validateDependentItems\": true, \"allowIgnoreDependentItems\": true}"
}
```

## 功能测试用例

### 测试用例1：无依赖项目的测试启动
**测试目标**：验证没有依赖项目时的正常测试流程

**前置条件**：
- 项目组没有配置依赖项目
- 设备连接正常

**测试步骤**：
1. 点击"开始测试"按钮
2. 观察系统提示信息

**预期结果**：
- 显示"无依赖项目要求，正在发送测试指令..."
- 成功发送测试指令
- 设备状态更新为"测试指令已发送，等待设备响应..."

### 测试用例2：依赖项目全部完成的测试启动
**测试目标**：验证依赖项目全部完成时的正常测试流程

**前置条件**：
- 项目组配置了3个依赖项目
- 所有依赖项目都已填写结果

**测试步骤**：
1. 点击"开始测试"按钮
2. 观察系统提示信息

**预期结果**：
- 显示"依赖项目校验通过(3/3)，正在发送测试指令..."
- 成功发送测试指令
- 状态标签显示"3/3已完成"

### 测试用例3：部分依赖项目缺失的校验
**测试目标**：验证依赖项目缺失时的校验和用户交互

**前置条件**：
- 项目组配置了5个依赖项目
- 其中2个已完成，3个未填写

**测试步骤**：
1. 点击"开始测试"按钮
2. 观察校验弹窗内容
3. 点击"取消测试"

**预期结果**：
- 显示依赖项目校验弹窗
- 弹窗显示"共有5个依赖项目，其中2个已完成，3个尚未完成"
- 列出3个缺失的依赖项目详情
- 点击取消后返回项目列表

### 测试用例4：保存并继续测试流程
**测试目标**：验证保存依赖项目后继续测试的流程

**前置条件**：
- 项目组配置了依赖项目
- 部分依赖项目已填写但未保存

**测试步骤**：
1. 填写部分依赖项目结果
2. 点击"开始测试"按钮
3. 在校验弹窗中选择"更多选项"
4. 选择"保存并继续"

**预期结果**：
- 显示"正在保存依赖项目..."
- 显示"已保存X项依赖项目结果"
- 显示"依赖项目已保存，正在启动测试..."
- 成功发送测试指令

### 测试用例5：忽略依赖项目继续测试
**测试目标**：验证忽略依赖项目校验的流程

**前置条件**：
- 项目组配置了依赖项目
- 部分依赖项目未完成

**测试步骤**：
1. 点击"开始测试"按钮
2. 在校验弹窗中选择"更多选项"
3. 选择"忽略并继续"
4. 在确认弹窗中选择"确定忽略"

**预期结果**：
- 显示确认忽略弹窗
- 显示"忽略X个依赖项目可能会影响测量结果的准确性"
- 确认后显示"跳过依赖项目校验，正在发送测试指令..."
- 成功发送测试指令

### 测试用例6：设备配置校验开关
**测试目标**：验证设备配置中的校验开关功能

**前置条件**：
- 设备配置中设置 `"validateDependentItems": false`

**测试步骤**：
1. 点击"开始测试"按钮

**预期结果**：
- 跳过依赖项目校验
- 直接发送测试指令

### 测试用例7：状态标签显示
**测试目标**：验证依赖项目状态标签的正确显示

**测试场景**：
- 场景A：无依赖项目 → 不显示标签
- 场景B：3/5已完成 → 显示绿色"3/5已完成"
- 场景C：2项缺失 → 显示红色"2项缺失"

**预期结果**：
- 状态标签颜色和文字正确
- 实时更新状态变化

## 异常测试用例

### 测试用例8：网络异常处理
**测试目标**：验证网络异常时的错误处理

**测试步骤**：
1. 断开网络连接
2. 点击"开始测试"按钮

**预期结果**：
- 显示"获取依赖项目信息失败"错误提示
- 测试状态重置为未开始

### 测试用例9：设备连接异常
**测试目标**：验证设备连接异常时的处理

**前置条件**：
- WebSocket连接断开

**测试步骤**：
1. 点击"开始测试"按钮

**预期结果**：
- 显示"设备连接已断开，请重新连接"
- 不进行依赖项目校验

### 测试用例10：数据异常处理
**测试目标**：验证数据异常时的容错处理

**测试场景**：
- 依赖项目数据格式异常
- 设备配置数据缺失
- API返回数据异常

**预期结果**：
- 显示友好的错误提示
- 系统状态正确恢复

## 性能测试用例

### 测试用例11：大量依赖项目性能
**测试目标**：验证大量依赖项目时的性能表现

**前置条件**：
- 配置50个依赖项目

**测试步骤**：
1. 点击"开始测试"按钮
2. 记录响应时间

**预期结果**：
- 校验完成时间 < 3秒
- 界面无明显卡顿

### 测试用例12：并发测试
**测试目标**：验证多个项目组同时测试的处理

**测试步骤**：
1. 同时点击多个项目组的"开始测试"按钮

**预期结果**：
- 每个项目组独立处理
- 状态显示正确
- 无数据混乱

## 兼容性测试用例

### 测试用例13：向后兼容性
**测试目标**：验证与现有功能的兼容性

**测试步骤**：
1. 使用原有的保存依赖项目功能
2. 使用原有的设备测试功能

**预期结果**：
- 原有功能正常工作
- 新功能不影响现有流程

### 测试用例14：浏览器兼容性
**测试目标**：验证不同浏览器的兼容性

**测试环境**：
- Chrome、Firefox、Edge、Safari

**预期结果**：
- 所有浏览器功能正常
- 界面显示一致

## 用户体验测试用例

### 测试用例15：操作流畅性
**测试目标**：验证用户操作的流畅性

**测试要点**：
- 按钮响应速度
- 状态更新及时性
- 错误提示清晰度
- 操作引导有效性

**预期结果**：
- 操作响应 < 1秒
- 状态更新实时
- 错误提示准确
- 用户能快速理解操作流程

## 测试数据清理

### 清理步骤
1. 删除测试体检人员数据
2. 清理测试设备配置
3. 恢复原始依赖关系配置
4. 清理测试日志

## 测试报告模板

### 测试结果记录
- 测试用例编号
- 测试结果（通过/失败）
- 实际结果描述
- 问题描述（如有）
- 修复建议（如有）

### 总体评估
- 功能完整性
- 性能表现
- 用户体验
- 稳定性
- 兼容性
