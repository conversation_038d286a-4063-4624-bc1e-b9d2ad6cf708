# 套餐分类树设计方案

## 1. 数据结构设计

### 1.1 套餐分类节点结构
```typescript
interface SuitCategoryNode {
  id: string;                    // 分类ID
  name: string;                  // 分类名称
  code?: string;                 // 分类编码
  description?: string;          // 分类描述
  sort?: number;                 // 排序号
  parentId?: string;             // 父分类ID（支持多级分类）
  level: number;                 // 层级深度
  isExpanded: boolean;           // 是否展开
  isLoading: boolean;            // 是否正在加载子节点
  children: SuitNode[];          // 子套餐列表
  childrenLoaded: boolean;       // 子节点是否已加载
  nodeType: 'category';          // 节点类型标识
}
```

### 1.2 套餐节点结构
```typescript
interface SuitNode {
  id: string;                    // 套餐ID
  name: string;                  // 套餐名称
  code?: string;                 // 套餐编码
  description?: string;          // 套餐描述
  price?: number;                // 套餐价格
  categoryId: string;            // 所属分类ID
  categoryName: string;          // 所属分类名称
  enableFlag: string;            // 启用状态 ('1'启用, '0'禁用)
  sort?: number;                 // 排序号
  itemCount?: number;            // 包含项目数量
  isSelected: boolean;           // 是否选中
  nodeType: 'suit';              // 节点类型标识
}
```

### 1.3 树形数据结构
```typescript
interface SuitCategoryTree {
  categories: SuitCategoryNode[];     // 分类节点列表
  expandedKeys: Set<string>;          // 展开的节点key集合
  selectedKeys: Set<string>;          // 选中的节点key集合
  loadingKeys: Set<string>;           // 正在加载的节点key集合
  searchKeyword: string;              // 搜索关键字
  filteredTree: SuitCategoryNode[];   // 过滤后的树结构
}
```

## 2. 展示逻辑设计

### 2.1 树形结构展示
- **分类节点**：
  - 图标：📁 (折叠) / 📂 (展开)
  - 样式：粗体字体，较大字号
  - 颜色：深色主题色
  - 展开/折叠动画效果

- **套餐节点**：
  - 图标：📦 套餐图标
  - 样式：普通字体，标准字号
  - 颜色：次要文字颜色
  - 缩进：相对分类节点缩进

### 2.2 状态指示
- **加载状态**：显示加载动画
- **空状态**：显示"暂无套餐"提示
- **禁用状态**：灰色显示，不可选择
- **选中状态**：高亮背景色

### 2.3 搜索结果展示
- 保持树形结构
- 高亮匹配关键字
- 自动展开包含匹配结果的分类
- 显示匹配数量统计

## 3. 交互方式设计

### 3.1 基础交互
- **点击分类节点**：展开/折叠子套餐列表
- **点击套餐节点**：选中套餐，触发选择事件
- **双击套餐节点**：直接添加套餐到登记记录
- **右键菜单**：显示套餐详情、添加等操作

### 3.2 键盘交互
- **上下箭头**：在节点间导航
- **左右箭头**：展开/折叠分类节点
- **Enter键**：选择当前节点
- **Space键**：展开/折叠当前分类节点

### 3.3 搜索交互
- **实时搜索**：输入时即时过滤
- **搜索范围**：套餐名称、编码、描述
- **搜索结果**：保持树形结构，高亮匹配项
- **清空搜索**：恢复完整树结构

## 4. 性能优化策略

### 4.1 懒加载
- 分类节点默认不加载子套餐
- 点击展开时才加载对应分类的套餐列表
- 已加载的数据进行缓存

### 4.2 虚拟滚动
- 当套餐数量超过100个时启用虚拟滚动
- 只渲染可视区域内的节点
- 动态计算节点高度

### 4.3 数据缓存
- 分类列表缓存5分钟
- 套餐列表按分类缓存3分钟
- 搜索结果缓存1分钟

## 5. 集成方案

### 5.1 与现有组件集成
- 在CustomerRegListOfPannelOccu.vue中添加套餐分类树面板
- 与现有的项目关系逻辑无缝集成
- 保持原有的登记列表功能不变

### 5.2 API集成
- 使用SuitCategory.api.ts获取分类列表
- 使用ItemSuit.api.ts获取套餐列表和搜索
- 复用现有的套餐添加逻辑

### 5.3 状态管理
- 使用Vue 3 Composition API管理状态
- 响应式数据结构
- 统一的错误处理和加载状态管理

## 6. 具体实现方案

### 6.1 组件结构
```
CustomerRegListOfPannelOccu.vue
├── 套餐分类树面板 (SuitCategoryTreePanel)
│   ├── 搜索框 (SuitSearchInput)
│   ├── 分类树 (SuitCategoryTree)
│   │   ├── 分类节点 (CategoryNode)
│   │   └── 套餐节点 (SuitNode)
│   └── 操作按钮区 (ActionButtons)
└── 现有的登记列表表格
```

### 6.2 核心方法设计
```typescript
// 加载分类列表
async function loadSuitCategories(): Promise<SuitCategoryNode[]>

// 加载指定分类下的套餐
async function loadSuitsByCategory(categoryId: string): Promise<SuitNode[]>

// 搜索套餐
async function searchSuits(keyword: string): Promise<SuitNode[]>

// 展开/折叠分类节点
function toggleCategoryExpansion(categoryId: string): void

// 选择套餐
function selectSuit(suitId: string): void

// 添加套餐到登记记录
async function addSuitToRegistration(suitId: string): Promise<void>

// 过滤树结构
function filterTree(keyword: string): SuitCategoryNode[]
```

### 6.3 样式设计
```scss
.suit-category-tree {
  .category-node {
    font-weight: bold;
    font-size: 14px;
    color: #1890ff;
    padding: 8px 12px;
    cursor: pointer;

    &:hover {
      background-color: #f0f9ff;
    }

    &.expanded {
      background-color: #e6f7ff;
    }
  }

  .suit-node {
    font-size: 13px;
    color: #666;
    padding: 6px 12px 6px 32px;
    cursor: pointer;

    &:hover {
      background-color: #f5f5f5;
    }

    &.selected {
      background-color: #e6f7ff;
      color: #1890ff;
    }

    &.disabled {
      color: #ccc;
      cursor: not-allowed;
    }
  }
}
```

### 6.4 事件处理
```typescript
// 分类节点点击事件
const handleCategoryClick = async (category: SuitCategoryNode) => {
  if (category.isLoading) return;

  if (!category.childrenLoaded) {
    category.isLoading = true;
    try {
      category.children = await loadSuitsByCategory(category.id);
      category.childrenLoaded = true;
    } finally {
      category.isLoading = false;
    }
  }

  category.isExpanded = !category.isExpanded;
};

// 套餐节点点击事件
const handleSuitClick = (suit: SuitNode) => {
  if (suit.enableFlag !== '1') return;

  selectedSuit.value = suit;
  emit('suitSelected', suit);
};

// 搜索事件
const handleSearch = debounce((keyword: string) => {
  searchKeyword.value = keyword;
  if (keyword.trim()) {
    filteredTree.value = filterTree(keyword);
  } else {
    filteredTree.value = [...categoryTree.value];
  }
}, 300);
```
