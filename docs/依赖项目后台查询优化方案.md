# 依赖项目后台查询优化方案

## 概述

本次优化主要解决了依赖项目检查逻辑中的问题，简化了代码结构，并实现了向串口设备中间服务发送依赖项目结果的功能。

## 主要改进

### 1. 修复API接口调用错误

**问题**：前端调用的依赖关系查询接口URL错误

**解决方案**：
- 修正 `getDependenciesByGroupIds` 接口调用URL
- 新增 `getDependentItemResultsBatch` 接口用于批量查询依赖项目结果

```typescript
// 修正前
export const getDependenciesByGroupIds = (data) => defHttp.post({ url: '/reg/customerReg/getDependentItemResultsBatch', data });

// 修正后
export const getDependenciesByGroupIds = (data) => defHttp.post({ url: '/basicinfo/itemGroupRelation/getDependenciesByGroupIds', data });
export const getDependentItemResultsBatch = (data) => defHttp.post({ url: '/reg/customerReg/getDependentItemResultsBatch', data });
```

### 2. 简化依赖项目检查逻辑

**改进点**：
- 直接从后台接口获取依赖项目结果，无需前端复杂计算
- 实现依赖项目信息缓存机制
- 添加缺失的 `validateDependencies` 全局验证函数

**核心流程**：
```javascript
async function loadDependencies() {
  // 1. 加载依赖关系
  const dependencyResponse = await getDependenciesByGroupIds(groupIds);
  
  // 2. 预构建依赖项目信息缓存
  await prebuildDependentItemsCache();
  
  // 3. 初始化依赖检查器
  dependencyChecker.value = new DependencyChecker(groupList.value, dependencyMap.value);
}
```

### 3. 实现依赖项目信息缓存

**功能**：
- 批量查询所有项目的依赖项目结果
- 构建本地缓存，提高性能
- 支持大项和小项依赖的统一管理

```javascript
async function prebuildDependentItemsCache() {
  // 批量查询所有项目的依赖项目结果
  const dependentResults = await getDependentItemResultsBatch(currentReg.value.id, allGroupIds);
  
  // 构建缓存
  dependentItemsCache.value.clear();
  Object.entries(dependentResults).forEach(([groupId, results]) => {
    results.forEach(result => {
      if (result.type === 'GROUP') {
        dependentItemsCache.value.set(result.groupId, result);
      } else if (result.type === 'ITEM') {
        dependentItemsCache.value.set(result.itemId, result);
      }
    });
  });
}
```

### 4. 优化串口设备依赖项目发送

**改进**：
- 从缓存中快速获取依赖项目信息
- 添加备用方案，从当前数据构建依赖项目
- 确保串口设备能够获取完整的依赖项目结果

```javascript
function buildDependentItems(groupId) {
  const dependencies = dependencyMap.value.get(groupId);
  const dependentItems = [];
  
  dependencies.forEach(dep => {
    let dependentItem = null;
    
    if (dep.relationItemType === 'GROUP') {
      // 优先从缓存获取
      dependentItem = dependentItemsCache.value.get(dep.relationGroupId);
      if (!dependentItem) {
        // 备用方案：从当前数据构建
        dependentItem = buildDependentItemFromCurrentData(dep, 'GROUP');
      }
    } else if (dep.relationItemType === 'ITEM') {
      dependentItem = dependentItemsCache.value.get(dep.relationItemId);
      if (!dependentItem) {
        dependentItem = buildDependentItemFromCurrentData(dep, 'ITEM');
      }
    }
    
    if (dependentItem) {
      dependentItems.push(dependentItem);
    }
  });
  
  return dependentItems;
}
```

## 技术优势

### 1. 性能提升
- **批量查询**：一次性获取所有依赖项目结果，减少网络请求
- **本地缓存**：避免重复查询，提高响应速度
- **异步处理**：不阻塞主流程，提升用户体验

### 2. 代码简化
- **统一接口**：使用后台统一的依赖项目查询接口
- **减少计算**：将复杂的依赖项目计算逻辑移至后台
- **清晰结构**：分离关注点，提高代码可维护性

### 3. 可靠性增强
- **错误处理**：完善的异常处理机制
- **备用方案**：缓存失效时的降级处理
- **日志记录**：详细的调试信息，便于问题排查

## 使用说明

### 1. 依赖验证
系统会在以下操作时自动进行依赖验证：
- 点击"暂存"按钮
- 点击"保存并生成小结"按钮
- 点击项目组的"保存组合数据"图标

### 2. 串口设备集成
串口设备在连接时会自动获取依赖项目信息：
- 设备连接成功后，系统会构建完整的 `ExamItemInfo` 结构
- 包含 `dependentItems` 字段，提供所有依赖项目的结果数据
- 支持大项和小项依赖的混合场景

### 3. 错误处理
- 如果依赖项目缺失，系统会显示友好的提示弹窗
- 用户可以选择继续保存或取消操作
- 支持导航到缺失的依赖项目进行补录

## 后续优化建议

1. **缓存策略优化**：考虑实现更智能的缓存更新机制
2. **性能监控**：添加性能指标监控，优化查询效率
3. **用户体验**：进一步优化依赖验证的交互流程
4. **扩展性**：为未来更复杂的依赖关系预留扩展空间

## 问题排查

### API调用错误排查

如果遇到 "JSON parse error: Cannot deserialize value of type" 错误，请检查：

1. **前端API调用格式**：
```javascript
// 正确的调用方式
const groupIds = ['group1', 'group2', 'group3'];
const response = await getDependenciesByGroupIds(groupIds);

// 错误的调用方式（传递对象而不是数组）
const response = await getDependenciesByGroupIds({groupIds: groupIds});
```

2. **后台接口参数类型**：
```java
// 后台期望接收 List<String> 类型
@PostMapping(value = "/getDependenciesByGroupIds")
public Result<?> getDependenciesByGroupIds(@RequestBody List<String> groupIds)
```

3. **检查网络请求**：
- 打开浏览器开发者工具
- 查看Network标签页中的请求内容
- 确认请求体是数组格式：`["group1", "group2"]`

### 常见问题解决

1. **缓存构建失败**：检查DependentItemResultDTO字段映射是否正确
2. **依赖验证不生效**：确认dependencyChecker是否正确初始化
3. **串口设备依赖项目为空**：检查dependentItemsCache是否有数据

## 总结

本次优化显著提升了依赖项目功能的性能和可靠性，简化了代码结构，为串口设备集成提供了完整的依赖项目支持。通过后台统一查询和前端缓存机制，实现了高效、可靠的依赖项目管理。
