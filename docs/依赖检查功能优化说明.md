# 依赖检查功能优化说明

## 概述

本次优化主要针对添加项目时的依赖检查逻辑进行了全面改进，实现了只提示大项依赖、优化项目名称显示、提供快捷添加功能等核心需求。

## 主要改进内容

### 1. 项目名称获取优化

#### 问题描述
- 原有逻辑中，依赖项目名称显示不完整，经常显示为`项目${dependentId}`
- 缺少统一的项目字典管理机制

#### 解决方案
- **新增项目字典缓存**：通过`getAllGroup` API获取所有项目信息，建立ID到名称的映射
- **智能名称获取**：优先使用关系数据中的名称，否则从项目字典获取
- **缓存机制**：项目字典缓存10分钟，避免频繁API调用

```javascript
// 新增项目字典缓存
let projectDictCache = new Map();
let projectDictCacheTime = 0;
const PROJECT_DICT_CACHE_DURATION = 10 * 60 * 1000; // 10分钟缓存

// 获取项目字典
async function getProjectDict() {
  const now = Date.now();
  
  if (projectDictCache.size > 0 && now < projectDictCacheTime) {
    return projectDictCache;
  }
  
  const projects = await getAllGroup({});
  const dict = new Map();
  
  if (Array.isArray(projects)) {
    projects.forEach(project => {
      dict.set(project.id, project.name);
    });
  }
  
  projectDictCache = dict;
  projectDictCacheTime = now + PROJECT_DICT_CACHE_DURATION;
  
  return dict;
}
```

### 2. 项目列表上方依赖提示功能

#### 设计理念
- **直观显示**：在项目列表上方直接显示缺失的依赖项目
- **一键操作**：提供简单的一键添加功能
- **非侵入式**：不使用弹窗，不打断用户操作流程
- **持续提醒**：每次打开项目列表都会检查并提示依赖关系

#### 核心特性
- **大项合并显示**：相同大项的依赖会合并为一条记录
- **小项详情展示**：在大项名称后显示具体依赖的小项名称
- **智能检查时机**：
  - 每次打开项目列表时自动检查（强制检查）
  - 添加新项目后立即检查
  - 删除项目后强制重新检查（确保依赖提示及时消失）
  - 减项操作后强制重新检查
  - 反减操作后强制重新检查
  - 其他数据刷新时使用缓存检查

- **主要功能**：
  - `依赖项目标签展示`：以标签形式显示所有缺失的依赖大项
  - `小项信息展示`：在标签中显示具体依赖的小项名称
  - `一键添加所有依赖项目`：自动查找并添加所有缺失的依赖项目
  - `工具提示`：鼠标悬停显示依赖此项目的检查项目列表

#### 界面特性
```vue
<template>
  <!-- 依赖项目提示区域 -->
  <div v-if="missingDependencies.length > 0" class="missing-dependencies-alert">
    <a-alert type="warning" show-icon :closable="false">
      <template #message>
        <div class="missing-dependencies-content">
          <span class="alert-title">检测到缺失的依赖项目</span>
          <div class="missing-projects-list">
            <a-tag
              v-for="dependency in missingDependencies"
              :key="dependency.dependentId"
              color="orange"
            >
              {{ dependency.dependentName }}
              <template v-if="dependency.dependentItemDetails">
                ({{ dependency.dependentItemDetails }})
              </template>
            </a-tag>
          </div>
        </div>
      </template>
      <template #action>
        <a-space>
          <a-button type="primary" size="small" @click="handleQuickAddAllDependencies">
            一键添加所有依赖项目
          </a-button>
          <a-button size="small" @click="dismissDependencyAlert">
            忽略
          </a-button>
        </a-space>
      </template>
    </a-alert>
  </div>
</template>
```

### 3. 依赖检查逻辑优化

#### 改进的检查流程
1. **执行依赖检查**：使用统一的关系管理器检查依赖
2. **获取详细信息**：通过`getMissingDependencyDetails`获取缺失依赖的完整信息
3. **智能提示方式**：
   - 有详细信息时：显示快捷添加模态框
   - 无详细信息时：降级到原有的警告提示

```javascript
// 优化后的依赖检查函数
async function checkDependenciesAfterAdd(addedItems) {
  try {
    const dependencyCheck = await checkItemDependencies(addedItems, regGroupDataSource.value);

    if (!dependencyCheck.isValid) {
      // 获取缺失依赖项目的详细信息
      const missingDependencyDetails = await getMissingDependencyDetails(dependencyCheck.missing);
      
      if (missingDependencyDetails.length > 0) {
        // 显示快捷添加模态框
        dependencyQuickAddModalRef.value?.open(missingDependencyDetails, addedItems);
      } else {
        // 降级到原有的警告提示
        const dependencyMsg = formatDependencyMessage(dependencyCheck.missing);
        message.warning({
          content: `检测到依赖项目缺失：\n${dependencyMsg}\n建议添加相关依赖项目以确保检查流程完整`,
          duration: 8,
        });
      }
    }
  } catch (error) {
    console.error('依赖检查失败:', error);
    message.warning('依赖检查失败，请注意项目依赖关系');
  }
}
```

### 4. 快捷添加处理逻辑

#### 核心功能实现
```javascript
// 处理依赖项目快捷添加
async function handleDependencyQuickAdd({ dependencies, originalItems }) {
  try {
    dependencyQuickAddModalRef.value?.setLoading(true);
    
    // 根据依赖项目ID查找对应的项目信息
    const projectsToAdd = [];
    
    for (const dependency of dependencies) {
      const foundProject = groupDataSource.value.find(item => item.id === dependency.id);
      if (foundProject) {
        projectsToAdd.push(foundProject);
      }
    }
    
    if (projectsToAdd.length === 0) {
      message.warn('未找到可添加的依赖项目，请手动搜索添加');
      return;
    }
    
    // 批量添加依赖项目
    await handleAddBatch(projectsToAdd);
    
    // 关闭模态框
    dependencyQuickAddModalRef.value?.close();
    
    message.success(`成功添加 ${projectsToAdd.length} 个依赖项目`);
    
  } catch (error) {
    console.error('快捷添加依赖项目失败:', error);
    message.error('快捷添加失败: ' + (error.message || '未知错误'));
  }
}
```

## 涉及的文件修改

### 1. 核心工具类
- `src/utils/itemGroupRelationManager.js`
  - 新增项目字典缓存机制
  - 优化依赖检查中的项目名称获取
  - 新增`getMissingDependencyDetails`函数
  - 新增缓存清理函数

### 2. 新增组件
- `src/components/DependencyQuickAddModal.vue`
  - 依赖项目快捷添加模态框组件
  - 提供友好的用户交互界面
  - 支持多种添加和搜索方式

### 3. 页面组件更新
- `src/views/reg/GroupListOfPannel.vue`
  - 集成DependencyQuickAddModal组件
  - 实现快捷添加处理逻辑
  - 优化依赖检查流程

- `src/views/reg/components/TeamGroupCard.vue`
  - 更新依赖检查逻辑
  - 改进提示信息显示

- `src/views/reg/components/CustomerRegGroupPannel.vue`
  - 更新依赖检查逻辑
  - 改进提示信息显示

## 用户体验改进

### 1. 更清晰的提示信息
- **之前**：`项目"心电图检查"缺少依赖项目：项目abc123`
- **现在**：`项目"心电图检查"缺少依赖项目：胸部CT检查`

### 2. 便捷的操作方式
- **快捷添加**：一键添加所有缺失的依赖项目
- **智能搜索**：自动设置搜索关键词，帮助用户快速定位
- **灵活选择**：支持忽略依赖检查继续操作

### 3. 友好的界面设计
- 清晰的项目列表展示
- 直观的操作按钮布局
- 详细的操作说明和帮助信息

## 技术特点

### 1. 缓存优化
- 项目关系数据缓存：5分钟
- 项目字典缓存：10分钟
- 避免重复API调用，提升性能

### 2. 错误处理
- 完善的异常捕获和处理
- 优雅的降级机制
- 用户友好的错误提示

### 3. 代码复用
- 统一的关系管理器
- 可复用的组件设计
- 一致的API调用方式

## 实施完成状态

### ✅ 已完成的功能

1. **项目名称获取优化** - 完成
   - 新增项目字典缓存机制
   - 智能名称获取逻辑
   - 10分钟缓存优化

2. **快捷添加依赖项目功能** - 完成
   - 创建DependencyQuickAddModal组件
   - 实现一键添加所有依赖项目
   - 支持单个和批量搜索功能

3. **依赖检查逻辑优化** - 完成
   - 改进的检查流程
   - 智能提示方式
   - 优雅的降级机制

4. **用户交互体验改进** - 完成
   - 友好的界面设计
   - 清晰的操作指引
   - 完善的错误处理

### 🔧 技术实现细节

- **文件修改**：6个文件
- **新增组件**：1个（DependencyQuickAddModal.vue）
- **新增函数**：3个（getProjectDict, getMissingDependencyDetails, 缓存管理函数）
- **优化函数**：4个（checkItemDependencies及相关处理函数）

### 📋 使用说明

1. **添加项目时**：系统会自动检查依赖关系
2. **发现缺失依赖**：显示快捷添加模态框
3. **用户选择**：
   - 一键添加所有依赖项目
   - 搜索特定依赖项目
   - 忽略并继续操作

### 🎯 核心优势

- **只提示大项依赖**：符合业务需求，简化操作流程
- **真实项目名称**：通过项目字典获取准确的项目名称
- **快捷添加功能**：一键解决依赖项目缺失问题
- **智能搜索**：自动设置搜索关键词，提升用户体验

## 后续优化建议

1. **性能优化**：考虑使用虚拟滚动处理大量依赖项目
2. **功能扩展**：支持依赖项目的批量配置和管理
3. **用户体验**：添加依赖关系的可视化展示
4. **数据同步**：实现依赖关系的实时更新和同步

## 测试建议

1. **基础功能测试**：
   - 添加有依赖关系的项目
   - 验证依赖检查是否正常触发
   - 测试快捷添加功能

2. **边界情况测试**：
   - 依赖项目不存在的情况
   - 网络异常时的处理
   - 大量依赖项目的性能表现

3. **用户体验测试**：
   - 界面交互是否流畅
   - 提示信息是否清晰
   - 操作流程是否符合预期

## 📋 显示效果

当检测到依赖项目缺失时，项目列表上方会显示：

```
⚠️ 检测到缺失的依赖项目
[身高体重(体重、身高)] [眼科(矫正视力)] [其他依赖项目]     [一键添加所有依赖项目]
```

### 合并逻辑示例

**原始依赖数据**：
- 肺通气功能检查 → 身高体重.体重
- 肺通气功能检查 → 身高体重.身高
- 心电图检查 → 身高体重.体重
- 视力检查 → 眼科.矫正视力

**合并后显示**：
- `身高体重(体重、身高)` - 工具提示：依赖此项目的检查项目: 肺通气功能检查、心电图检查
- `眼科(矫正视力)` - 工具提示：依赖此项目的检查项目: 视力检查

### 用户体验特点

这种设计更加直观和用户友好，用户可以：
- 清楚看到所有缺失的依赖大项和具体小项
- 了解哪些检查项目依赖这些大项（通过工具提示）
- 一键解决所有依赖问题
- 系统会持续提醒直到依赖问题解决
- 每次打开项目列表都会自动检查依赖关系
