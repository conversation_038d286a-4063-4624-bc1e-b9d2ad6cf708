# 依赖项目校验优化

## 优化目标

根据业务需求，优化依赖项目的校验逻辑，**只校验大项依赖，不校验小项依赖**，以简化用户操作流程，提高系统易用性。

## 问题分析

### 原有逻辑的问题

在原有的依赖检查逻辑中，系统会同时检查大项依赖和小项依赖：

1. **大项依赖**：项目A依赖项目B（整个大项）
2. **小项依赖**：项目A依赖项目B中的某个具体小项

这种严格的依赖检查虽然保证了数据的完整性，但在实际使用中存在以下问题：

- **操作复杂**：用户需要确保所有依赖的小项都有结果才能进行后续操作
- **流程繁琐**：在项目录入阶段就要求小项结果完整，不符合实际工作流程
- **用户体验差**：频繁的依赖检查警告影响用户操作效率

### 业务需求

根据实际业务场景分析：

- **大项依赖**：确实需要严格校验，确保相关项目已经添加
- **小项依赖**：在项目录入阶段不需要严格校验，可以在后续检查阶段处理

## 解决方案

### 核心思路

修改 `checkItemDependencies` 函数，实现**选择性依赖检查**：

- ✅ **保留大项依赖检查**：确保依赖的大项已经添加到项目列表中
- ❌ **跳过小项依赖检查**：不再检查具体小项的结果是否完整

### 技术实现

#### 1. 数据结构识别

依赖关系数据包含 `relationItemType` 字段来区分依赖类型：

```javascript
// 大项依赖
{
  relationItemType: 'GROUP',
  relationGroupId: 'group123',
  relationGroupName: '心电图检查'
}

// 小项依赖
{
  relationItemType: 'ITEM', 
  relationGroupId: 'group123',
  relationItemId: 'item456',
  relationGroupName: '心电图检查',
  relationItemName: '心率'
}
```

#### 2. 优化后的检查逻辑

```javascript
export async function checkItemDependencies(newItems, existingItems) {
  const missingDependencies = [];
  
  for (const newItem of newItems) {
    const relations = await getItemRelations(newItem.itemGroupId);
    const dependentItems = relations.dependentGroups;
    
    for (const dependent of dependentItems) {
      // 兼容旧版本字符串格式（作为大项处理）
      if (typeof dependent === 'string') {
        // 检查大项是否存在
        const dependentExists = existingItems.some(item => 
          item.itemGroupId === dependent && 
          item.addMinusFlag !== -1 && 
          item.payStatus !== '退款成功'
        );
        
        if (!dependentExists) {
          missingDependencies.push({
            itemId: newItem.itemGroupId,
            itemName: newItem.itemGroupName,
            dependentId: dependent,
            dependentName: `项目${dependent}`,
            dependentType: 'GROUP'
          });
        }
      }
      // 新版本对象格式：只处理大项依赖
      else if (dependent.relationItemType === 'GROUP') {
        // 检查大项依赖
        const dependentExists = existingItems.some(item => 
          item.itemGroupId === dependent.relationGroupId && 
          item.addMinusFlag !== -1 && 
          item.payStatus !== '退款成功'
        );
        
        if (!dependentExists) {
          missingDependencies.push({
            itemId: newItem.itemGroupId,
            itemName: newItem.itemGroupName,
            dependentId: dependent.relationGroupId,
            dependentName: dependent.relationGroupName,
            dependentType: 'GROUP'
          });
        }
      }
      // 跳过小项依赖检查
      else if (dependent.relationItemType === 'ITEM') {
        console.log(`跳过小项依赖检查: ${newItem.itemGroupName} -> ${dependent.relationGroupName}.${dependent.relationItemName}`);
        continue;
      }
    }
  }
  
  return {
    isValid: missingDependencies.length === 0,
    missing: missingDependencies
  };
}
```

## 优化效果

### 1. 用户体验提升

- **简化操作流程**：用户只需关注大项依赖，无需在录入阶段处理复杂的小项依赖
- **减少警告干扰**：大幅减少依赖检查警告的频次
- **提高操作效率**：用户可以更快速地完成项目录入

### 2. 业务流程优化

- **符合实际工作流程**：项目录入阶段专注于项目选择，小项结果在检查阶段处理
- **保持核心约束**：大项依赖检查确保了关键业务逻辑的完整性
- **灵活性增强**：为后续的工作流程优化提供了更大的灵活性

### 3. 系统性能改善

- **减少检查开销**：跳过小项依赖检查减少了系统计算负担
- **降低数据库查询**：减少了对小项结果数据的频繁查询
- **提升响应速度**：依赖检查过程更加快速

## 向后兼容性

### 数据格式兼容

- **支持旧版本**：兼容字符串格式的依赖数据（作为大项处理）
- **支持新版本**：正确处理对象格式的依赖数据，区分大项和小项

### 功能兼容

- **保持API接口不变**：不影响现有的调用方式
- **保持返回格式不变**：依赖检查结果的数据结构保持一致
- **保持错误处理不变**：异常情况的处理逻辑保持不变

## 用户体验优化

### 1. 快捷添加依赖项目

当检测到依赖项目缺失时，系统会：

1. **智能解析错误消息**：从后端返回的错误信息中提取依赖项目名称
2. **提供快捷添加选项**：显示确认对话框，询问用户是否快捷添加依赖项目
3. **引导用户操作**：提示用户需要手动搜索和添加的具体项目名称

```javascript
// 解析依赖错误消息，提取依赖项目信息
function parseDependencyErrorMessage(errorMessage: string): Array<{name: string, id?: string}> {
  const dependencyProjects = [];

  // 匹配大项依赖：项目【xxx】依赖项目【yyy】
  const groupDependencyRegex = /项目【([^】]+)】依赖项目【([^】]+)】/g;
  let match;
  while ((match = groupDependencyRegex.exec(errorMessage)) !== null) {
    const dependentProjectName = match[2];
    if (!dependencyProjects.some(p => p.name === dependentProjectName)) {
      dependencyProjects.push({ name: dependentProjectName });
    }
  }

  return dependencyProjects;
}
```

### 2. 改进的Loading机制

优化了加载状态的管理：

1. **错误时正确关闭模态框**：确保在依赖检查失败时，部位选择模态框能够正确关闭
2. **Loading状态重置**：在finally块中确保loading状态被正确重置
3. **数据回滚机制**：在保存失败时，自动移除已添加到前端列表的项目

```javascript
} catch (error) {
  // 检查是否是依赖项目错误，提供快捷添加选项
  const errorMessage = error.message || '未知错误';
  if (errorMessage.includes('依赖项目')) {
    const dependencyProjects = parseDependencyErrorMessage(errorMessage);
    if (dependencyProjects.length > 0) {
      showDependencyQuickAddModal(dependencyProjects, batchPartState.itemGroups);
    } else {
      message.error('添加失败: ' + errorMessage);
      closeBatchPartSelector();
    }
  } else {
    message.error('添加失败: ' + errorMessage);
    closeBatchPartSelector();
  }

  // 数据回滚
  // ...
} finally {
  batchPartState.loading = false;
}
```

## 注意事项

1. **小项依赖的处理**：虽然在录入阶段跳过了小项依赖检查，但在检查结果录入和总检阶段仍需要考虑小项依赖关系

2. **日志记录**：对于跳过的小项依赖检查，系统会记录相应的日志信息，便于调试和追踪

3. **业务规则**：这个优化主要针对项目录入阶段，其他业务场景的依赖检查逻辑可能需要单独考虑

4. **配置灵活性**：未来可以考虑将依赖检查的严格程度作为系统配置项，支持不同的业务需求

5. **快捷添加功能**：目前的快捷添加功能主要是引导用户手动搜索，未来可以考虑实现自动搜索和添加功能
