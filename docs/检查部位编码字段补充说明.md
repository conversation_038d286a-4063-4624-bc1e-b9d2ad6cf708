# 检查部位编码字段补充说明

## 补充概述

为了提升部位信息的精确性和系统集成能力，在相关表和实体中补充了 `check_part_code` 字段，实现了基于编码的部位标识和匹配机制。

## 主要补充内容

### 1. 数据库表结构扩展

#### 1.1 customer_reg_item_group 表
```sql
-- 添加检查部位编码字段
ALTER TABLE `customer_reg_item_group` 
ADD COLUMN IF NOT EXISTS `check_part_code` varchar(50) DEFAULT NULL COMMENT '检查部位编码' AFTER `check_part_name`;

-- 添加索引
ALTER TABLE `customer_reg_item_group` 
ADD INDEX IF NOT EXISTS `idx_check_part_code` (`check_part_code`);

-- 同步现有数据
UPDATE `customer_reg_item_group` crig 
JOIN `check_part_dict` cpd ON crig.check_part_id = cpd.id 
SET crig.check_part_code = cpd.code 
WHERE crig.check_part_id IS NOT NULL AND crig.check_part_code IS NULL;
```

#### 1.2 suit_group 表
```sql
-- 添加检查部位编码字段
ALTER TABLE `suit_group` 
ADD COLUMN IF NOT EXISTS `check_part_code` varchar(50) DEFAULT NULL COMMENT '检查部位编码' AFTER `check_part_name`;
```

### 2. 实体类扩展

#### 2.1 CustomerRegItemGroup 实体
```java
/**
 * 检查部位编码
 */
@Excel(name = "检查部位编码", width = 15)
@ApiModelProperty(value = "检查部位编码")
private String checkPartCode;
```

#### 2.2 SuitGroup 实体
```java
/**检查部位编码*/
@Excel(name = "检查部位编码", width = 15)
@ApiModelProperty(value = "检查部位编码")
private java.lang.String checkPartCode;
```

### 3. 后端服务层完善

#### 3.1 addItemGroupWithCheckParts 方法
```java
// 设置部位信息
regGroup.setCheckPartId(checkPartId);
regGroup.setCheckPartName(checkPart.getName());
regGroup.setCheckPartCode(checkPart.getCode());  // 新增
regGroup.setParentGroupId(parentGroupId);
regGroup.setItemGroupName(itemGroup.getName() + "-" + checkPart.getName());
```

#### 3.2 ItemSuitMapper.xml 查询优化
```xml
select ig.*,
       sg.price_after_dis as price_after_dis_of_suit,
       sg.min_discount_rate as min_discount_rate_of_suit,
       sg.price_dis_diff_amount as price_dis_diff_amount_of_suit,
       sg.dis_rate as dis_rate_of_suit,
       sg.check_part_id,
       sg.check_part_name,
       sg.check_part_code  -- 新增
from suit_group sg 
join item_group ig on sg.group_id = ig.id
```

### 4. 前端功能完善

#### 4.1 套餐维护页面
**部位选择器数据映射**：
```javascript
checkPartOptions.value = res.map(item => ({
  label: item.frequency > 0 ? `${item.name} (${item.frequency}次)` : item.name,
  value: item.id,
  name: item.name,
  code: item.code  // 新增
}));
```

**部位更新方法**：
```javascript
function updateCheckPart(record: any, checkPartId: string) {
  const selectedPart = checkPartOptions.value.find(option => option.value === checkPartId);
  record.checkPartId = checkPartId;
  record.checkPartName = selectedPart ? selectedPart.name : '';
  record.checkPartCode = selectedPart ? selectedPart.code : '';  // 新增
  
  updateSuitGroup();
}
```

**数据保存**：
```javascript
return {
  // ... 其他字段
  checkPartId: item.checkPartId || '',
  checkPartName: item.checkPartName || '',
  checkPartCode: item.checkPartCode || '',  // 新增
};
```

#### 4.2 套餐使用逻辑
**套餐添加时的部位信息传递**：
```javascript
// 设置部位信息（如果套餐中有预设）
if (group.checkPartId && group.checkPartName) {
  data.checkPartId = group.checkPartId;
  data.checkPartName = group.checkPartName;
  data.checkPartCode = group.checkPartCode || '';  // 新增
  console.log(`设置部位信息：${group.name} - ${group.checkPartName} (${group.checkPartCode})`);
}
```

#### 4.3 部位补充弹窗
**SuitPartSelectionModal 组件**：
```javascript
// 构建结果数据
const result = itemsNeedParts.value.map(item => {
  const selectedPart = item.partOptions?.find(option => option.value === item.selectedPartId);
  return {
    ...item,
    checkPartId: item.selectedPartId,
    checkPartName: selectedPart?.name || '',
    checkPartCode: selectedPart?.code || '',  // 新增
  };
});
```

## 数据流转过程

### 1. 套餐维护阶段
```
用户选择部位 → 获取部位完整信息(id, name, code) → 保存到 suit_group 表
```

### 2. 套餐使用阶段
```
读取套餐配置 → 获取预设部位信息(包含code) → 传递到项目记录
```

### 3. 部位补充阶段
```
用户选择部位 → 获取部位完整信息 → 设置到项目记录(包含code)
```

### 4. 最终存储
```
customer_reg_item_group 表包含完整部位信息：
- check_part_id: 部位ID
- check_part_name: 部位名称  
- check_part_code: 部位编码
```

## 应用场景和优势

### 1. 精确匹配
- **编码唯一性**：部位编码提供唯一标识，避免名称重复问题
- **系统集成**：便于与外部系统进行数据交换和匹配
- **数据一致性**：确保部位信息在不同模块间的一致性

### 2. 业务应用
- **设备集成**：医疗设备可以基于编码进行精确的部位识别
- **报告生成**：基于编码生成标准化的检查报告
- **数据分析**：支持基于部位编码的统计分析

### 3. 系统维护
- **数据迁移**：编码便于数据的导入导出和系统迁移
- **版本兼容**：编码作为稳定标识，支持系统版本升级
- **多语言支持**：编码不受语言影响，支持国际化

## 数据迁移和验证

### 1. 自动数据同步
迁移脚本会自动从 `check_part_dict` 表同步现有数据的部位编码：
```sql
UPDATE `customer_reg_item_group` crig 
JOIN `check_part_dict` cpd ON crig.check_part_id = cpd.id 
SET crig.check_part_code = cpd.code 
WHERE crig.check_part_id IS NOT NULL AND crig.check_part_code IS NULL;
```

### 2. 数据完整性验证
```sql
-- 检查是否有缺失编码的记录
SELECT COUNT(*) as missing_code_count
FROM `customer_reg_item_group` 
WHERE check_part_id IS NOT NULL 
  AND check_part_id != '' 
  AND (check_part_code IS NULL OR check_part_code = '');
```

### 3. 索引优化
```sql
-- 添加性能优化索引
ALTER TABLE `customer_reg_item_group` 
ADD INDEX `idx_check_part_code` (`check_part_code`);

ALTER TABLE `customer_reg_item_group` 
ADD INDEX `idx_customer_reg_item_part` (`customer_reg_id`, `item_group_id`, `check_part_id`);
```

## 向后兼容性

### 1. 字段可选
- `check_part_code` 字段允许为空，保持向后兼容
- 现有功能不受影响，可以逐步完善编码信息

### 2. 渐进增强
- 新添加的项目会自动包含编码信息
- 现有数据通过迁移脚本自动补充编码
- 支持手动维护和批量更新

### 3. 功能扩展
- 为未来的功能扩展提供了基础
- 支持更精确的部位匹配和查询
- 便于与第三方系统集成

## 测试建议

### 1. 数据库测试
- 执行迁移脚本，验证字段添加成功
- 验证数据同步的准确性和完整性
- 测试索引的创建和性能提升

### 2. 套餐维护测试
- 测试部位选择时编码的正确设置
- 验证保存和加载时编码信息的完整性
- 测试清除部位时编码的正确清理

### 3. 套餐使用测试
- 验证预设部位编码的正确传递
- 测试部位补充弹窗中编码的处理
- 确认最终保存的记录包含完整的部位信息

### 4. 数据一致性测试
- 验证部位ID、名称、编码的一致性
- 测试不同入口添加项目时编码的正确性
- 确认编码与部位字典的同步

## 总结

通过补充 `check_part_code` 字段，系统实现了：

- ✅ **完整的部位标识**：ID、名称、编码三重标识确保精确性
- ✅ **系统集成能力**：基于编码的标准化接口
- ✅ **数据一致性**：统一的部位信息管理
- ✅ **向后兼容性**：不影响现有功能的正常使用
- ✅ **性能优化**：添加了必要的索引提升查询效率
- ✅ **自动化处理**：完整的数据迁移和同步机制

这个补充为系统的部位管理功能提供了更加完善和标准化的基础，支持未来更多高级功能的开发和第三方系统的集成。
