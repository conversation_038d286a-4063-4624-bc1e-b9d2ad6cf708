# 团检自动添加附属赠送项目功能实现说明

## 概述

本次实现为团检分组功能添加了自动添加附属项目和赠送项目的逻辑，使团检功能与个人登记功能保持一致，确保在添加主项目时能够自动添加相关的附属项目和赠送项目。

## 问题背景

### 原有问题
- **团检分组**：缺少自动添加附属项目和赠送项目的逻辑
- **个人登记**：已有完整的附属项目和赠送项目自动添加功能
- **功能不一致**：两个模块的行为不统一，用户体验不佳

### 影响范围
- 团检分组项目配置
- 套餐添加到团检分组
- 手动添加项目到团检分组
- 必检项目添加到团检分组

## 实现方案

### 1. 后端实现

#### 1.1 修改 CompanyRegServiceImpl

**文件位置**：`jeecg-module-physicalex/src/main/java/org/jeecg/modules/reg/service/impl/CompanyRegServiceImpl.java`

##### 新增重载方法
```java
@Override
@Transactional(rollbackFor = Exception.class)
public void saveItemGroupOfTeam(String teamId, List<CompanyTeamItemGroup> itemGroupList, boolean skipGiftAndAttach) {
    // 原有保存逻辑...
    
    // 如果不跳过附属和赠送项目处理
    if (!skipGiftAndAttach && itemGroupList != null && !itemGroupList.isEmpty()) {
        // 转换为CustomerRegItemGroup格式以复用现有逻辑
        List<CustomerRegItemGroup> tempGroupList = convertToCustomerRegItemGroups(itemGroupList);
        
        // 获取附属项目
        List<CustomerRegItemGroup> attachGroups = itemGroupRelationService.getAttachGroups(tempGroupList);
        if (CollectionUtils.isNotEmpty(attachGroups)) {
            // 验证附属项目的互斥关系
            itemGroupRelationService.checkIsHaveMutexes(attachGroups);
            
            // 转换为CompanyTeamItemGroup并保存
            List<CompanyTeamItemGroup> attachTeamGroups = convertToCompanyTeamItemGroups(attachGroups, teamId);
            companyTeamItemGroupService.saveBatch(attachTeamGroups);
            
            // 将附属项目添加到主列表中
            tempGroupList.addAll(attachGroups);
            itemGroupList.addAll(attachTeamGroups);
        }

        // 获取赠送项目
        List<CustomerRegItemGroup> giftGroups = itemGroupRelationService.getGiftGroups(tempGroupList);
        if (CollectionUtils.isNotEmpty(giftGroups)) {
            // 验证赠送项目的互斥关系
            itemGroupRelationService.checkIsHaveMutexes(giftGroups);
            
            // 转换为CompanyTeamItemGroup并保存
            List<CompanyTeamItemGroup> giftTeamGroups = convertToCompanyTeamItemGroups(giftGroups, teamId);
            companyTeamItemGroupService.saveBatch(giftTeamGroups);
            
            // 将赠送项目添加到主列表中
            itemGroupList.addAll(giftTeamGroups);
        }
    }
    
    // 更新团检分组价格...
}
```

##### 新增转换方法
- `convertToCustomerRegItemGroups()`: 将CompanyTeamItemGroup转换为CustomerRegItemGroup
- `convertToCompanyTeamItemGroups()`: 将CustomerRegItemGroup转换为CompanyTeamItemGroup

#### 1.2 新增API端点

**文件位置**：`jeecg-module-physicalex/src/main/java/org/jeecg/modules/reg/controller/CompanyRegController.java`

```java
@PostMapping(value = "/saveItemGroupOfTeamWithRelations")
public Result<?> saveItemGroupOfTeamWithRelations(@RequestBody JSONObject info) {
    String teamId = info.getString("teamId");
    List<CompanyTeamItemGroup> itemGroupList = info.getJSONArray("groupList").toJavaList(CompanyTeamItemGroup.class);
    Boolean skipGiftAndAttach = info.getBoolean("skipGiftAndAttach");
    if (skipGiftAndAttach == null) {
        skipGiftAndAttach = false; // 默认处理附属和赠送项目
    }
    companyRegService.saveItemGroupOfTeam(teamId, itemGroupList, skipGiftAndAttach);
    return Result.OK("操作成功!");
}
```

### 2. 前端实现

#### 2.1 修改 CompanyReg.api.ts

**文件位置**：`src/views/reg/CompanyReg.api.ts`

##### 新增API方法
```typescript
export const saveItemGroupOfTeamWithRelations = (params) => {
  return defHttp.post({ url: Api.saveItemGroupOfTeamWithRelations, params }, { isTransformResponse: false });
};
```

#### 2.2 修改 TeamGroupCard.vue

**文件位置**：`src/views/reg/components/TeamGroupCard.vue`

##### 核心修改点

1. **新增内部保存方法**
```javascript
function updateTeamGroupInternal(skipGiftAndAttach = false) {
    const apiMethod = skipGiftAndAttach ? saveItemGroupOfTeam : saveItemGroupOfTeamWithRelations;
    const params = { 
        teamId: mainTeamId.value, 
        groupList: dataSource.value,
        skipGiftAndAttach: skipGiftAndAttach
    };
    // 调用相应的API...
}
```

2. **智能选择处理方式**
   - **需要处理附属和赠送项目**：添加新项目时（套餐、手动添加、必检项目）
   - **跳过附属和赠送项目**：更新现有项目属性时（价格、折扣、类型等）

## 功能特性

### 1. 🎯 自动添加附属项目
- 根据项目关系配置自动获取附属项目
- 支持部位匹配逻辑
- 验证附属项目的互斥关系
- 自动继承主项目的部位信息

### 2. 🎁 自动添加赠送项目
- 根据项目关系配置自动获取赠送项目
- 考虑主项目和附属项目的赠送项目
- 验证赠送项目的互斥关系
- 支持级联赠送逻辑

### 3. 🔄 智能处理策略
- **添加场景**：自动处理附属和赠送项目
- **更新场景**：跳过附属和赠送项目处理，避免重复添加

### 4. 🛡️ 安全保障
- 事务保护确保数据一致性
- 互斥关系验证防止冲突
- 错误回滚机制保护数据完整性

## 使用场景

### 1. 套餐添加
```javascript
// 添加套餐时自动处理附属和赠送项目
handleSuitModalSuccess(suitList) {
    // ... 添加套餐项目逻辑
    updateTeamGroupInternal(false); // 处理附属和赠送项目
}
```

### 2. 手动添加项目
```javascript
// 手动添加项目时自动处理附属和赠送项目
handleGroupModalSuccess(groupList) {
    // ... 添加项目逻辑
    updateTeamGroupInternal(false); // 处理附属和赠送项目
}
```

### 3. 必检项目添加
```javascript
// 添加必检项目时自动处理附属和赠送项目
addMustCheckItem() {
    // ... 添加必检项目逻辑
    updateTeamGroupInternal(false); // 处理附属和赠送项目
}
```

### 4. 属性更新
```javascript
// 更新项目属性时跳过附属和赠送项目处理
handlePriceChange(record, event) {
    // ... 更新价格逻辑
    updateTeamGroupInternal(true); // 跳过附属和赠送项目处理
}
```

## 技术亮点

### 1. 代码复用
- 复用个人登记的附属和赠送项目处理逻辑
- 通过类型转换实现代码共享
- 减少重复开发和维护成本

### 2. 向下兼容
- 保留原有API接口，确保现有功能不受影响
- 新增重载方法提供扩展功能
- 渐进式升级策略

### 3. 智能优化
- 根据操作类型智能选择处理策略
- 避免不必要的附属和赠送项目处理
- 提升系统性能和用户体验

## 测试建议

### 1. 功能测试
- 测试套餐添加时的附属和赠送项目自动添加
- 测试手动添加项目时的附属和赠送项目自动添加
- 测试必检项目添加时的附属和赠送项目自动添加
- 测试项目属性更新时不会重复添加附属和赠送项目

### 2. 边界测试
- 测试没有配置附属和赠送项目的情况
- 测试附属和赠送项目存在互斥关系的情况
- 测试部位匹配逻辑的正确性

### 3. 性能测试
- 测试大量项目添加时的性能表现
- 测试并发操作时的数据一致性

## 问题修复

### 1. companyRegId字段缺失问题

**问题描述**：
在保存附属项目和赠送项目时出现错误：`Field 'company_reg_id' doesn't have a default value`

**问题原因**：
转换 `CustomerRegItemGroup` 为 `CompanyTeamItemGroup` 时，没有设置 `companyRegId` 字段。

**解决方案**：
```java
private List<CompanyTeamItemGroup> convertToCompanyTeamItemGroups(List<CustomerRegItemGroup> customerGroups, String teamId) {
    // 获取团检分组信息以获取companyRegId
    CompanyTeam companyTeam = companyTeamMapper.selectById(teamId);
    String companyRegId = companyTeam != null ? companyTeam.getCompanyRegId() : null;

    return customerGroups.stream().map(customerGroup -> {
        CompanyTeamItemGroup teamGroup = new CompanyTeamItemGroup();
        teamGroup.setCompanyRegId(companyRegId); // 🔥 关键修复：设置单位预约ID
        teamGroup.setTeamId(teamId);
        // ... 其他字段设置
        return teamGroup;
    }).collect(Collectors.toList());
}
```

## 总结

本次实现成功为团检分组功能添加了自动添加附属项目和赠送项目的能力，使其与个人登记功能保持一致。通过智能的处理策略和完善的错误处理机制，确保了功能的稳定性和用户体验的一致性。

### 2. 唯一约束冲突问题

**问题描述**：
保存附属项目和赠送项目时出现唯一约束冲突：`Duplicate entry for key 'unique_reg_team_item_part'`

**根本原因**：
数据库表的唯一索引设计不合理，不应该阻止赠送项目的多次添加，因为赠送逻辑中同一个项目可能需要赠送多次。

**解决方案**：
1. **数据库层面**：修正了数据表中的唯一索引
2. **代码层面**：移除了不必要的去重逻辑，恢复正常的附属项目和赠送项目添加流程

```java
// 恢复正常的保存流程
// 1. 先保存主项目
jdbcTemplate.update("delete from company_team_item_group where team_id = ?", teamId);
if (itemGroupList != null && !itemGroupList.isEmpty()) {
    companyTeamItemGroupService.saveOrUpdateBatch(itemGroupList);
}

// 2. 处理附属项目
List<CustomerRegItemGroup> attachGroups = itemGroupRelationService.getAttachGroups(tempGroupList);
if (CollectionUtils.isNotEmpty(attachGroups)) {
    itemGroupRelationService.checkIsHaveMutexes(attachGroups);
    List<CompanyTeamItemGroup> attachTeamGroups = convertToCompanyTeamItemGroups(attachGroups, teamId);
    if (!attachTeamGroups.isEmpty()) {
        companyTeamItemGroupService.saveBatch(attachTeamGroups);
        itemGroupList.addAll(attachTeamGroups);
    }
}

// 3. 处理赠送项目
List<CustomerRegItemGroup> giftGroups = itemGroupRelationService.getGiftGroups(tempGroupList);
if (CollectionUtils.isNotEmpty(giftGroups)) {
    itemGroupRelationService.checkIsHaveMutexes(giftGroups);
    List<CompanyTeamItemGroup> giftTeamGroups = convertToCompanyTeamItemGroups(giftGroups, teamId);
    if (!giftTeamGroups.isEmpty()) {
        companyTeamItemGroupService.saveBatch(giftTeamGroups);
        itemGroupList.addAll(giftTeamGroups);
    }
}
```

### 3. 必填字段完善

**优化内容**：
完善了转换方法中的字段设置，确保所有必填字段都有合适的默认值：

```java
// 价格相关字段（确保不为null）
teamGroup.setPrice(customerGroup.getPrice() != null ? customerGroup.getPrice() : BigDecimal.ZERO);
teamGroup.setPriceAfterDis(customerGroup.getPriceAfterDis() != null ? customerGroup.getPriceAfterDis() : BigDecimal.ZERO);
teamGroup.setDisRate(customerGroup.getDisRate() != null ? customerGroup.getDisRate() : BigDecimal.ONE);
teamGroup.setMinDiscountRate(customerGroup.getMinDiscountRate() != null ? customerGroup.getMinDiscountRate() : BigDecimal.ONE);
teamGroup.setPriceDisDiffAmount(BigDecimal.ZERO); // 默认差额为0

// 项目类别字段
teamGroup.setItemGroupCategory(customerGroup.getType() != null ? customerGroup.getType() : "健康项目");
```

### 4. 调试和监控优化

**优化内容**：
添加了详细的调试日志，便于排查附属项目和赠送项目未添加的问题：

```java
log.info("开始处理附属和赠送项目，主项目数量: {}, teamId: {}", itemGroupList.size(), teamId);
log.info("获取到附属项目数量: {}", attachGroups != null ? attachGroups.size() : 0);
log.info("获取到赠送项目数量: {}", giftGroups != null ? giftGroups.size() : 0);
log.info("成功添加 {} 个附属项目", attachTeamGroups.size());
log.info("成功添加 {} 个赠送项目", giftTeamGroups.size());
log.info("附属和赠送项目处理完成，最终项目总数: {}", itemGroupList.size());
```

### 5. 前端刷新时机问题

**问题描述**：
附属项目和赠送项目在后端成功保存，但前端没有及时刷新显示，需要完全关闭编辑弹窗并刷新整个团检列表才能看到。

**问题原因**：
前端在打开部位选择弹窗时就调用了保存API，此时用户还没有完成部位选择，导致保存时机不正确。

**解决方案**：
调整保存时机，只在适当的时候触发附属项目和赠送项目的处理：

```javascript
// 修改前：无论是否需要部位选择，都立即保存
if (needPartSelectionItems.length > 0) {
    showBatchPartSelector(needPartSelectionItems, 'manual');
}
updateTeamGroupInternal(false); // ❌ 过早调用

// 修改后：根据是否需要部位选择来决定保存时机
if (needPartSelectionItems.length > 0) {
    // 需要部位选择时，在部位选择完成后再保存
    showBatchPartSelector(needPartSelectionItems, 'manual');
} else {
    // 不需要部位选择时，立即保存
    updateTeamGroupInternal(false); // ✅ 适当时机调用
}
```

**关键改进**：
1. **条件保存**：只有在不需要部位选择时才立即保存
2. **延迟保存**：需要部位选择时，在用户完成选择后再保存
3. **确保刷新**：保存成功后立即刷新前端数据

### 6. 依赖项目处理逻辑平移

**功能描述**：
将 `GroupListOfPannel.vue` 中的依赖项目处理逻辑和项目类型逻辑完整平移到团检组件中。

**主要功能**：

1. **依赖项目检查**：
   - 自动检查项目依赖关系
   - 在项目列表上方显示缺失的依赖项目提示
   - 支持一键添加所有依赖项目

2. **项目类型管理**：
   - 启用项目类型列显示
   - 支持在线修改项目类型（健康项目/职业项目）
   - 必检项目自动设置为职业项目

3. **项目来源标识**：
   - 显示项目来源标识（主项/依赖/赠送/附属）
   - 不同来源使用不同颜色的标签区分
   - 鼠标悬停显示详细说明

**实现细节**：

```javascript
// 依赖检查核心逻辑
async function checkDependenciesAfterAdd(addedItems) {
  const dependencyCheck = await checkItemDependencies(addedItems, dataSource.value);
  if (!dependencyCheck.isValid) {
    const mergedDependencies = mergeDependenciesByGroup(dependencyCheck.missing);
    missingDependencies.value = mergedDependencies;
    message.info('检测到依赖项目缺失，请查看项目列表上方的提示');
  }
}

// 项目来源标识
function getItemSourceBadge(itemGroupId) {
  const sourceType = getItemSourceType(itemGroupId);
  switch (sourceType) {
    case 'dependent': return { text: '依赖', bg: '#fa8c16' };
    case 'gift': return { text: '赠送', bg: '#52c41a' };
    case 'attach': return { text: '附属', bg: '#722ed1' };
    default: return { text: '主项', bg: '#1890ff' };
  }
}

// 一键添加依赖项目
async function handleQuickAddAllDependencies() {
  const projectsToAdd = [];
  for (const dependency of missingDependencies.value) {
    const searchResult = await listItemGroup({ name: dependency.dependentName });
    const foundProject = searchResult.records?.find(item => item.id === dependency.dependentId);
    if (foundProject) projectsToAdd.push(foundProject);
  }
  await handleAddBatch(projectsToAdd);
  message.success(`成功添加 ${projectsToAdd.length} 个依赖项目`);
}
```

**UI 改进**：

1. **依赖项目提示区域**：
```html
<div v-if="missingDependencies.length > 0" class="missing-dependencies-alert">
  <a-alert type="warning" show-icon>
    <template #message>
      <span class="alert-title">检测到缺失的依赖项目</span>
      <div class="missing-projects-list">
        <a-tag v-for="dependency in missingDependencies"
               :key="dependency.dependentId"
               color="orange">
          {{ dependency.dependentName }}
        </a-tag>
      </div>
    </template>
    <template #action>
      <a-button type="primary" size="small"
                @click="handleQuickAddAllDependencies"
                :loading="addingDependencies">
        一键添加
      </a-button>
    </template>
  </a-alert>
</div>
```

2. **项目来源标识**：
```html
<template v-if="'itemGroupName' == column.dataIndex">
  <div style="display: flex; align-items: center; gap: 4px;">
    <a-tag color="red" v-if="record.mustCheckFlag == '1'">必检</a-tag>
    <a-tag v-if="getItemSourceType(record.itemGroupId) !== 'main'"
           :color="getItemSourceBadge(record.itemGroupId).bg"
           :title="getItemSourceBadge(record.itemGroupId).title">
      {{ getItemSourceBadge(record.itemGroupId).text }}
    </a-tag>
    <span>{{ getDisplayItemName(record) }}</span>
  </div>
</template>
```

3. **项目类型选择**：
```html
<template v-else-if="'itemGroupCategory' == column.dataIndex">
  <a-select @change="updateType(record)"
            size="small"
            v-model:value="record.itemGroupCategory"
            :options="[
              { label: '健康项目', value: '健康项目' },
              { label: '职业项目', value: '职业项目' }
            ]" />
</template>
```

### 7. 一键添加依赖项目重复问题

**问题描述**：
使用依赖项目的一键导入功能后，所有项目都被重新添加了一遍，导致项目重复。

**问题原因**：
1. `handleAddBatch` 方法调用 `updateTeamGroupInternal(false)` 保存项目
2. 后端 `saveItemGroupOfTeamWithRelations` API 会先删除所有项目，再重新保存
3. 重新保存时会触发附属项目和赠送项目的处理逻辑
4. 导致所有项目（包括原有项目）都被重新添加

**根本原因**：
前端API调用逻辑有问题：
1. 当 `skipGiftAndAttach = true` 时，前端调用 `saveItemGroupOfTeam` API
2. 但后端的 `saveItemGroupOfTeam` 方法内部调用重载版本，默认 `skipGiftAndAttach = false`
3. 导致即使前端想跳过附属和赠送项目处理，后端仍然执行了这些逻辑

**解决方案**：
修改前端API调用逻辑，统一使用 `saveItemGroupOfTeamWithRelations` API：

```javascript
// 修改前：API调用逻辑有问题
function updateTeamGroupInternal(skipGiftAndAttach = false) {
  const apiMethod = skipGiftAndAttach ? saveItemGroupOfTeam : saveItemGroupOfTeamWithRelations;
  // ❌ 当 skipGiftAndAttach = true 时调用 saveItemGroupOfTeam
  // 但后端 saveItemGroupOfTeam 内部仍然会处理附属和赠送项目
}

// 修改后：统一使用 saveItemGroupOfTeamWithRelations API
function updateTeamGroupInternal(skipGiftAndAttach = false) {
  // 始终使用 saveItemGroupOfTeamWithRelations API，通过 skipGiftAndAttach 参数控制行为
  const apiMethod = saveItemGroupOfTeamWithRelations;
  const params = {
    teamId: mainTeamId.value,
    groupList: dataSource.value,
    skipGiftAndAttach: skipGiftAndAttach // ✅ 正确传递参数给后端
  };
  // ...
}

async function updateTeamGroupAsync(skipGiftAndAttach = false) {
  // 始终使用 saveItemGroupOfTeamWithRelations API
  const apiMethod = saveItemGroupOfTeamWithRelations;
  const params = {
    teamId: mainTeamId.value,
    groupList: dataSource.value,
    skipGiftAndAttach: skipGiftAndAttach // ✅ 正确传递参数给后端
  };
  // ...
}
```

**关键改进**：
1. **智能分类**：区分需要部位选择和不需要部位选择的项目
2. **避免重复处理**：保存时明确跳过附属和赠送项目处理
3. **保持数据一致性**：确保依赖项目正确添加到数据库

### 8. 添加项目行为重构

**需求描述**：
重新整理添加项目的正确行为，包括重复校验规则、添加行为和类型标注行为。

**重复校验规则**：
- **基本规则**：`teamId` + `itemGroupId` + `checkPartId` 都相同 = 重复
- **例外情况**：
  - 耗材（`chargeItemOnlyFlag === '1'`）可以重复
  - 赠送和依赖的多个项目可以重复

**添加行为**：
1. **"添加项目"按钮**：需要查找附属项目和赠送项目并添加
2. **"使用套餐"按钮**：不查找附属和赠送项目，只添加套餐中的项目

**类型标注行为**：
- 在项目列表中用 `a-tag` 标注：赠送、依赖、附属
- 点击标签弹出关系详情弹窗

**实现方案**：

1. **前端重复校验逻辑**：
```javascript
// 重复校验：teamId + itemGroupId + checkPartId 都相同才算重复
const isDuplicate = dataSource.value.some((row) =>
  row.itemGroupId === group.id &&
  row.checkPartId === checkPartId &&
  !(group.chargeItemOnlyFlag === '1') // 耗材可以重复
);
```

2. **添加项目行为区分**：
```javascript
// "添加项目"按钮 - 处理附属和赠送项目
async function handleGroupModalSuccess(groupList) {
  // ... 添加主项目
  updateTeamGroupInternal(false); // 处理附属和赠送项目
}

// "使用套餐"按钮 - 不处理附属和赠送项目
async function handleSuitModalSuccess(suitList) {
  // ... 添加套餐项目
  updateTeamGroupInternal(true); // 跳过附属和赠送项目处理
}
```

3. **项目来源标记**：
```javascript
// 前端添加 sourceType 字段标记
dataSource.value.push({
  // ... 其他字段
  sourceType: 'main', // main-主项目, attach-附属, gift-赠送, dependent-依赖
});

// 后端转换时设置来源类型
List<CompanyTeamItemGroup> attachTeamGroups = convertToCompanyTeamItemGroups(attachGroups, teamId, "attach");
List<CompanyTeamItemGroup> giftTeamGroups = convertToCompanyTeamItemGroups(giftGroups, teamId, "gift");
```

4. **类型标签显示**：
```html
<a-tag
  v-if="record.sourceType && record.sourceType !== 'main'"
  :color="getSourceTypeColor(record.sourceType)"
  @click="showRelationDetail(record)"
>
  {{ getSourceTypeText(record.sourceType) }}
</a-tag>
```

5. **后端实体类扩展**：
```java
// CompanyTeamItemGroup 实体类添加字段
@ApiModelProperty(value = "项目来源类型")
@TableField(exist = false)
private String sourceType;
```

### 修复记录
- ✅ 修复了字段映射问题（`itemGroupCategory` ↔ `type`）
- ✅ 修复了 `companyRegId` 字段缺失问题
- ✅ 修复了唯一约束冲突问题（数据库索引 + 代码逻辑）
- ✅ 移除了不必要的去重逻辑，支持赠送项目多次添加
- ✅ 完善了必填字段的默认值设置
- ✅ 添加了详细的调试日志
- ✅ 修复了前端刷新时机问题
- ✅ 平移了依赖项目处理逻辑和项目类型管理功能
- ✅ 修复了一键添加依赖项目重复问题
- ✅ 重构了添加项目行为，实现正确的重复校验和类型标注
- ✅ 确保了数据完整性和一致性
