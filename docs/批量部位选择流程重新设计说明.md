# 批量部位选择流程重新设计说明

## 设计背景

### 原有设计的问题
1. **数据流不匹配**：输入是项目数组，但处理是单个项目
2. **用户体验差**：多个项目需要逐一选择部位，效率低下
3. **状态管理混乱**：`checkPartState.currentItemGroup` 只能存储一个项目
4. **重复检查不完善**：导致数据库约束冲突

### 新设计目标
1. **批量处理**：一次性为多个项目选择部位
2. **统一流程**：项目选择框和套餐使用相同的部位选择流程
3. **更好的用户体验**：清晰的界面，批量操作
4. **完善的重复检查**：避免数据库约束冲突

## 新设计架构

### 1. 状态结构重新设计

#### 原有状态（单项目）
```javascript
const checkPartState = reactive({
  visible: false,
  loading: false,
  options: [],
  selectedParts: [],
  currentItemGroup: null, // 只能存储一个项目
});
```

#### 新状态（批量项目）
```javascript
const batchPartState = reactive({
  visible: false,
  loading: false,
  itemGroups: [], // 需要选择部位的项目列表
  itemPartSelections: new Map(), // 每个项目的部位选择状态
  source: '', // 来源：manual | suit
});
```

### 2. 数据结构

#### itemPartSelections Map结构
```javascript
Map<itemGroupId, {
  options: Array<{label: string, value: string, frequency: number}>,
  selectedParts: string[],
  loading: boolean
}>
```

### 3. 核心方法

#### 批量部位选择器
- `showBatchPartSelector(itemGroups, source)` - 显示批量选择器
- `closeBatchPartSelector()` - 关闭选择器
- `confirmBatchAddItemsWithParts()` - 批量确认添加

#### 辅助方法
- `getItemPartSelection(itemGroupId)` - 获取项目的部位选择状态
- `hasAnyPartSelected()` - 检查是否有任何部位被选择
- `getBatchPartNameById(itemGroupId, partId)` - 获取批量选择中的部位名称
- `getPartNameById(partId)` - 获取单项目选择中的部位名称（保留兼容性）

#### 数据加载
- `loadItemParts(itemGroupId, keyword?)` - 加载项目的部位选项
- `searchItemParts(itemGroupId, keyword)` - 搜索项目的部位

## 用户界面设计

### 1. 模态框结构
- **标题**：批量选择检查部位
- **尺寸**：800px 宽度，60vh 高度
- **滚动**：支持垂直滚动

### 2. 项目列表展示
```
┌─────────────────────────────────────────┐
│ 共 3 个项目需要选择部位                    │
├─────────────────────────────────────────┤
│ 1. 胸部CT  [放射科]                      │
│ 选择部位: [多选下拉框]                    │
│ 已选择部位: [左侧] [右侧]                 │
├─────────────────────────────────────────┤
│ 2. 腹部超声  [超声科]                    │
│ 选择部位: [多选下拉框]                    │
│ 已选择部位: [上腹部]                     │
├─────────────────────────────────────────┤
│ 3. 心电图  [心电图室]                    │
│ 选择部位: [多选下拉框]                    │
│ 已选择部位: 暂无                        │
└─────────────────────────────────────────┘
```

### 3. 交互特性
- **独立加载**：每个项目的部位选项独立加载
- **搜索支持**：每个项目支持独立的部位搜索
- **实时预览**：显示已选择的部位
- **验证提示**：未选择部位的项目会有提示

## 流程设计

### 1. 项目选择框流程
```
用户选择项目 → 过滤需要部位的项目 → showBatchPartSelector(items, 'manual')
```

### 2. 套餐选择流程
```
用户选择套餐 → 获取套餐项目 → 过滤需要部位的项目 → showBatchPartSelector(items, 'suit')
```

### 3. 必检项目流程
```
系统添加必检项目 → 过滤需要部位的项目 → showBatchPartSelector(items, 'manual')
```

### 4. 批量部位选择流程
```
显示批量选择器 → 用户为每个项目选择部位 → 验证完整性 → 批量添加到dataSource → 保存到后端
```

## 重复检查逻辑

### 1. 统一检查函数
```javascript
function isItemExists(itemGroupId: string, checkPartId?: string): boolean {
  const targetKey = checkPartId ? `${itemGroupId}-${checkPartId}` : itemGroupId;
  return dataSource.value.some(row => getRowKey(row) === targetKey);
}
```

### 2. 检查时机
- **添加前检查**：在 `confirmBatchAddItemsWithParts` 中检查每个项目-部位组合
- **跳过重复项**：已存在的组合会被跳过，不会添加
- **用户反馈**：显示添加成功和跳过的数量

## 错误处理和回滚

### 1. 前端验证
- 验证每个项目至少选择一个部位
- 验证项目信息完整性

### 2. 后端保存失败处理
- 自动回滚：移除刚添加到 `dataSource` 的项目
- 错误提示：显示具体的错误信息
- 状态保持：保持模态框打开，用户可以重试

### 3. 事务保护
- 后端使用 `@Transactional` 确保数据一致性
- 前端使用 try-catch 确保状态一致性

## 性能优化

### 1. 懒加载
- 部位选项在用户聚焦时才加载
- 避免一次性加载所有项目的部位选项

### 2. 防抖搜索
- 搜索使用 300ms 防抖
- 避免频繁的API调用

### 3. 状态管理
- 使用 Map 结构高效管理多个项目的状态
- 及时清理不需要的状态

## 兼容性考虑

### 1. 保留旧方法
- 保留 `checkPartState` 相关方法，以防其他地方还在使用
- 逐步迁移到新的批量选择流程

### 2. 渐进式升级
- 新功能优先使用批量选择
- 旧功能保持兼容性

## 测试建议

### 1. 功能测试
- 测试项目选择框的批量部位选择
- 测试套餐的批量部位选择
- 测试必检项目的批量部位选择

### 2. 边界测试
- 测试单个项目的部位选择
- 测试大量项目的部位选择
- 测试网络异常情况

### 3. 用户体验测试
- 测试界面响应性
- 测试操作流畅性
- 测试错误提示的友好性

## 相关文件

### 前端文件
- `src/views/reg/components/TeamGroupCard.vue` - 主要修改文件

### 修改内容
1. **状态结构**：从单项目状态改为批量项目状态
2. **UI组件**：从单项目选择改为批量项目选择
3. **核心方法**：新增批量处理相关方法
4. **流程整合**：统一项目选择框、套餐、必检项目的处理流程

### 文档文件
- `docs/批量部位选择流程重新设计说明.md` - 本文档

## 预期效果

### 1. 用户体验提升
- ✅ 一次性处理多个项目的部位选择
- ✅ 清晰的界面展示和操作反馈
- ✅ 减少重复操作，提高效率

### 2. 系统稳定性提升
- ✅ 统一的重复检查逻辑
- ✅ 完善的错误处理和回滚机制
- ✅ 避免数据库约束冲突

### 3. 代码质量提升
- ✅ 统一的处理流程
- ✅ 更好的状态管理
- ✅ 更清晰的代码结构

## 后续优化方向

### 1. 智能推荐
- 根据历史使用频次推荐常用部位
- 支持部位组合的快速选择

### 2. 批量操作增强
- 支持批量复制部位选择
- 支持模板化的部位配置

### 3. 性能进一步优化
- 虚拟滚动支持大量项目
- 更智能的缓存策略
