# 项目层级展示逻辑迁移说明

## 概述

本次迁移将 `GroupListOfPannel.vue` 中的项目层级展示逻辑完整迁移到 `CustomerRegGroupPannel.vue` 中，实现项目的层级化显示，包括主项目和子项目的关系展示。

## 迁移的核心功能

### 1. 项目层级结构构建

#### 新增函数：
- `buildHierarchicalStructure(data)` - 构建项目层级结构的核心函数
- `findChildItemsFast(mainItem, dataList)` - 快速查找主项目的子项目

#### 功能说明：
- 将项目按照主项目和子项目的关系进行层级化排列
- 主项目优先显示，其子项目紧随其后
- 支持依赖、赠送、附属、套餐等多种关系类型
- 按照加项(1) -> 正常(0) -> 减项(-1)的顺序排列主项目

### 2. 项目关系显示模式

#### 新增配置：
- `relationDisplayMode` - 关系显示模式：'prefix' | 'badge' | 'both' | 'none'
- `showDetailedBadges` - 控制是否显示详细的badge标签

#### 新增函数：
- `getItemRelationPrefix(itemGroupId)` - 获取项目关系前缀显示配置
- `toggleRelationDisplayMode()` - 切换显示模式
- `getDisplayModeText()` - 获取显示模式文本
- `getDisplayModeIcon()` - 获取显示模式图标
- `getDisplayModeTooltip()` - 获取显示模式提示信息

#### 显示模式说明：
- **前缀模式**：使用 `├─` 符号显示项目关系
- **标签模式**：使用彩色标签显示项目关系
- **完整模式**：同时显示前缀和标签
- **隐藏模式**：不显示项目关系标识

### 3. 项目名称格式化

#### 新增函数：
- `formatItemDisplayName(itemName, checkPartName)` - 格式化项目名称显示
- `getItemTooltipText(itemName, checkPartName)` - 获取项目tooltip文本

#### 功能说明：
- 统一项目名称和部位信息的显示格式
- 支持 `项目名称[部位名称]` 的格式化显示

### 4. UI界面更新

#### 新增UI组件：
- 关系显示模式切换按钮，位于搜索框前方
- 支持图标和文字的切换按钮
- 提供tooltip提示当前模式和操作说明

#### 表格显示更新：
- 项目名称列支持层级前缀显示
- 根据项目来源类型显示不同颜色的前缀
- 支持可选的badge标签显示
- 子项目使用较浅的颜色显示，区分主项目

## 技术实现细节

### 1. 数据流程

```
原始项目数据 -> 关键字过滤 -> buildHierarchicalStructure -> 层级化显示
```

### 2. 项目关系识别

项目关系通过以下字段识别：
- `attachBaseId` - 附属项目的基础项目ID
- `attachGroupIds` - 主项目的附属项目ID列表
- `parentGroupId` - 父项目ID
- `itemSuitId` - 套餐ID（同套餐项目建立关系）
- `getItemSourceType()` - 项目来源类型分析

### 3. 缓存机制

- `itemRelationshipMap` - 项目关系映射缓存
- `mainToChildrenMap` - 主项目到子项目的映射缓存
- `localStorage` - 用户显示模式偏好保存

### 4. 性能优化

- 使用computed属性自动响应数据变化
- 缓存项目关系分析结果
- 延迟加载和预加载机制

## 兼容性说明

### 1. 向后兼容
- 保留原有的项目显示逻辑作为fallback
- 不影响现有的项目操作功能
- 保持原有的API调用方式

### 2. 配置持久化
- 用户的显示模式偏好保存在localStorage中
- 页面刷新后保持用户选择的显示模式

## 使用说明

### 1. 切换显示模式
- 点击搜索框前的显示模式按钮
- 按钮会循环切换四种显示模式
- 当前模式会显示在按钮文字中

### 2. 查看项目关系
- 点击项目名称前的关系前缀查看详细信息
- 点击彩色标签查看项目关系详情
- 使用tooltip查看完整的项目信息

### 3. 层级结构理解
- 主项目显示在前，子项目紧随其后
- 子项目使用前缀符号标识层级关系
- 不同关系类型使用不同颜色区分

## 注意事项

1. **数据依赖**：层级显示依赖项目的关系数据，确保相关字段正确填充
2. **性能考虑**：大量项目时可能影响渲染性能，已实施缓存优化
3. **用户体验**：提供多种显示模式满足不同用户需求
4. **调试支持**：控制台输出详细的层级构建日志，便于问题排查

## 后续优化建议

1. 支持更多的前缀样式选择
2. 添加项目关系的可视化图表
3. 支持项目关系的批量编辑
4. 优化大数据量下的渲染性能
