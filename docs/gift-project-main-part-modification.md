# 赠送项目主项目部位默认值移除

## 修改概述

根据用户需求，移除了赠送项目配置中主项目部位的默认值设置，确保用户在添加赠送项目时不会自动填充主项目部位。

## 修改文件

- `src/views/basicinfo/components/ItemGroupRelationForm.vue`

## 具体修改内容

### 1. 添加赠送项目函数 (addGiftItem)

**修改位置**: 第704-718行

**修改前**:
```javascript
// 添加赠送项
const addGiftItem = () => {
  const commonPart = getMainProjectCommonPart();
  const newItem = {
    relationGroupId: null,
    quantity: 1,
    mainCheckPartId: null,
    mainCheckPartName: null,
    mainCheckPartCode: null,
    relationCheckPartId: null,
    relationCheckPartName: null,
    relationCheckPartCode: null
  };

  // 如果有常用部位，默认设置主项目部位和赠送项目部位
  if (commonPart) {
    newItem.mainCheckPartId = commonPart.id;
    newItem.mainCheckPartName = commonPart.name;
    newItem.mainCheckPartCode = commonPart.code;
    newItem.relationCheckPartId = commonPart.id;
    newItem.relationCheckPartName = commonPart.name;
    newItem.relationCheckPartCode = commonPart.code;
  }

  relationData.giftGroups.push(newItem);
};
```

**修改后**:
```javascript
// 添加赠送项
const addGiftItem = () => {
  const newItem = {
    relationGroupId: null,
    quantity: 1,
    mainCheckPartId: null,
    mainCheckPartName: null,
    mainCheckPartCode: null,
    relationCheckPartId: null,
    relationCheckPartName: null,
    relationCheckPartCode: null
  };

  relationData.giftGroups.push(newItem);
};
```

### 2. 重置表单函数 (handleReset)

**修改位置**: 第1261-1273行

**修改前**:
```javascript
// 重置赠送项目，使用常用部位作为默认值
const defaultGiftItem = {
  relationGroupId: null,
  quantity: 1,
  mainCheckPartId: null,
  mainCheckPartName: null,
  mainCheckPartCode: null,
  relationCheckPartId: null,
  relationCheckPartName: null,
  relationCheckPartCode: null
};

if (commonPart) {
  defaultGiftItem.mainCheckPartId = commonPart.id;
  defaultGiftItem.mainCheckPartName = commonPart.name;
  defaultGiftItem.mainCheckPartCode = commonPart.code;
  defaultGiftItem.relationCheckPartId = commonPart.id;
  defaultGiftItem.relationCheckPartName = commonPart.name;
  defaultGiftItem.relationCheckPartCode = commonPart.code;
}

relationData.giftGroups = [defaultGiftItem];
```

**修改后**:
```javascript
// 重置赠送项目
const defaultGiftItem = {
  relationGroupId: null,
  quantity: 1,
  mainCheckPartId: null,
  mainCheckPartName: null,
  mainCheckPartCode: null,
  relationCheckPartId: null,
  relationCheckPartName: null,
  relationCheckPartCode: null
};

relationData.giftGroups = [defaultGiftItem];
```

### 3. 初始化数据函数 (onMounted)

**修改位置**: 第1396-1410行

**修改前**:
```javascript
// 如果没有赠送项目配置，添加一个默认项，使用常用部位
if (relationData.giftGroups.length === 0) {
  const commonPart = getMainProjectCommonPart();
  const defaultItem = {
    relationGroupId: null,
    quantity: 1,
    mainCheckPartId: null,
    mainCheckPartName: null,
    mainCheckPartCode: null,
    relationCheckPartId: null,
    relationCheckPartName: null,
    relationCheckPartCode: null
  };

  if (commonPart) {
    defaultItem.mainCheckPartId = commonPart.id;
    defaultItem.mainCheckPartName = commonPart.name;
    defaultItem.mainCheckPartCode = commonPart.code;
    defaultItem.relationCheckPartId = commonPart.id;
    defaultItem.relationCheckPartName = commonPart.name;
    defaultItem.relationCheckPartCode = commonPart.code;
  }

  relationData.giftGroups = [defaultItem];
}
```

**修改后**:
```javascript
// 如果没有赠送项目配置，添加一个默认项
if (relationData.giftGroups.length === 0) {
  const defaultItem = {
    relationGroupId: null,
    quantity: 1,
    mainCheckPartId: null,
    mainCheckPartName: null,
    mainCheckPartCode: null,
    relationCheckPartId: null,
    relationCheckPartName: null,
    relationCheckPartCode: null
  };

  relationData.giftGroups = [defaultItem];
}
```

## 修改影响

1. **用户体验改进**: 用户在添加赠送项目时，主项目部位字段将保持空白，需要用户手动选择，避免了不必要的默认值干扰。

2. **数据一致性**: 确保赠送项目的主项目部位只有在用户明确选择时才会被设置，提高了数据的准确性。

3. **向后兼容**: 修改不影响现有的赠送项目配置数据，只影响新添加的赠送项目。

## 注意事项

- 附属项目的主项目部位默认值设置保持不变，只修改了赠送项目的行为
- 赠送项目的部位联动逻辑（当选择主项目部位后自动同步到赠送项目部位）仍然保持正常工作
- 批量设置功能不受影响，用户仍可以通过批量设置来快速配置所有赠送项目的部位

## 测试建议

1. 测试添加新的赠送项目时，主项目部位字段应为空
2. 测试重置表单后，赠送项目的主项目部位字段应为空
3. 测试页面初始化时，如果没有现有配置，默认赠送项目的主项目部位字段应为空
4. 测试现有的赠送项目配置加载是否正常
5. 测试部位联动功能是否正常工作
