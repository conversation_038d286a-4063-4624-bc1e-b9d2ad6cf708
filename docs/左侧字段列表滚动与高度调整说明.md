# 左侧字段列表滚动与高度调整说明

## 目标
- 让左侧字段列表高度自适应页面，不再使用固定高度
- 列表内容超出时，左侧列表成为唯一滚动容器，避免内容被遮挡或不滚动

## 本次修改点

1) 左侧面板高度交由父容器 flex 撑开
```css
.left-panel {
  width: 420px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  /* 自适应高度 */
  min-height: 0;
}
```

2) 字段列表卡片参与弹性布局
```css
.field-list-card {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.field-list-card .ant-card-body {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;   /* 统一让 .field-list 滚动 */
  padding: 12px;
}
```

3) 列表成为唯一滚动容器
```css
.field-list {
  flex: 1;
  min-height: 0;
  overflow-y: auto;   /* 唯一滚动层 */
  padding-right: 4px; /* 滚动条间距 */
}
```

## 说明
- 删除了原 `height: calc(100vh - 120px); min-height: 600px;`，避免在不同屏幕上溢出不滚动
- 通过给各层设置 `min-height: 0`，允许 flex 子元素成为滚动容器
- 保证 .field-list 是唯一滚动层，避免卡片 body 与内部列表双滚动

## 验证建议
1. 浏览器缩放或调整窗口高度，验证左侧高度自适应
2. 字段较多时（超出可视区域），确认左侧列表可滚动
3. 右侧内容滚动不受影响，整体布局稳定

## 影响范围
- 仅样式层面调整，不影响数据与交互逻辑
- 不需要重启服务，前端热更新已自动生效

