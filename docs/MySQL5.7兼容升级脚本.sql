-- =====================================================
-- MySQL 5.7 兼容的依赖项目功能升级脚本
-- 说明：专门为MySQL 5.7优化，避免动态SQL问题
-- 版本：V1.0
-- 日期：2025-01-03
-- =====================================================

-- 开始事务
START TRANSACTION;

-- =====================================================
-- 1. 检查并添加字段
-- =====================================================

-- 检查 relation_item_type 字段是否存在
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'relation_item_type字段已存在'
        ELSE 'relation_item_type字段不存在，需要添加'
    END AS relation_item_type_status
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'item_group_relation' 
  AND COLUMN_NAME = 'relation_item_type';

-- 添加 relation_item_type 字段（如果执行时报错"字段已存在"，可以忽略）
ALTER TABLE `item_group_relation` 
ADD COLUMN `relation_item_type` VARCHAR(10) NULL COMMENT '关联项类型：GROUP-大项，ITEM-小项' 
AFTER `relation_group_name`;

-- 检查 relation_item_id 字段是否存在
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'relation_item_id字段已存在'
        ELSE 'relation_item_id字段不存在，需要添加'
    END AS relation_item_id_status
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'item_group_relation' 
  AND COLUMN_NAME = 'relation_item_id';

-- 添加 relation_item_id 字段（如果执行时报错"字段已存在"，可以忽略）
ALTER TABLE `item_group_relation` 
ADD COLUMN `relation_item_id` VARCHAR(32) NULL COMMENT '关联小项ID' 
AFTER `relation_item_type`;

-- 检查 relation_item_name 字段是否存在
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'relation_item_name字段已存在'
        ELSE 'relation_item_name字段不存在，需要添加'
    END AS relation_item_name_status
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'item_group_relation' 
  AND COLUMN_NAME = 'relation_item_name';

-- 添加 relation_item_name 字段（如果执行时报错"字段已存在"，可以忽略）
ALTER TABLE `item_group_relation` 
ADD COLUMN `relation_item_name` VARCHAR(100) NULL COMMENT '关联小项名称' 
AFTER `relation_item_id`;

-- =====================================================
-- 2. 更新现有数据
-- =====================================================

-- 为现有数据设置默认的关联项类型
UPDATE `item_group_relation` 
SET `relation_item_type` = 'GROUP' 
WHERE `relation_item_type` IS NULL 
  AND `relation_group_id` IS NOT NULL;

-- =====================================================
-- 3. 创建索引
-- =====================================================

-- 检查并创建 relation_item_type 索引
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'idx_relation_item_type索引已存在'
        ELSE 'idx_relation_item_type索引不存在，需要创建'
    END AS idx_relation_item_type_status
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'item_group_relation' 
  AND INDEX_NAME = 'idx_relation_item_type';

-- 创建 relation_item_type 索引（如果执行时报错"索引已存在"，可以忽略）
CREATE INDEX `idx_relation_item_type` ON `item_group_relation` (`relation_item_type`);

-- 检查并创建 relation_item_id 索引
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'idx_relation_item_id索引已存在'
        ELSE 'idx_relation_item_id索引不存在，需要创建'
    END AS idx_relation_item_id_status
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'item_group_relation' 
  AND INDEX_NAME = 'idx_relation_item_id';

-- 创建 relation_item_id 索引（如果执行时报错"索引已存在"，可以忽略）
CREATE INDEX `idx_relation_item_id` ON `item_group_relation` (`relation_item_id`);

-- 检查并创建复合索引
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'idx_group_relation_type索引已存在'
        ELSE 'idx_group_relation_type索引不存在，需要创建'
    END AS idx_group_relation_type_status
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'item_group_relation' 
  AND INDEX_NAME = 'idx_group_relation_type';

-- 创建复合索引（如果执行时报错"索引已存在"，可以忽略）
CREATE INDEX `idx_group_relation_type` ON `item_group_relation` (`group_id`, `relation`, `relation_item_type`);

-- =====================================================
-- 4. 验证升级结果
-- =====================================================

-- 检查字段是否添加成功
SELECT 
    '=== 字段检查结果 ===' as message
UNION ALL
SELECT 
    CONCAT('✓ ', COLUMN_NAME, ' (', DATA_TYPE, 
           CASE WHEN CHARACTER_MAXIMUM_LENGTH IS NOT NULL 
                THEN CONCAT('(', CHARACTER_MAXIMUM_LENGTH, ')') 
                ELSE '' END, 
           ') - ', IFNULL(COLUMN_COMMENT, '无注释'))
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'item_group_relation'
  AND COLUMN_NAME IN ('relation_item_type', 'relation_item_id', 'relation_item_name')
ORDER BY ORDINAL_POSITION;

-- 检查索引是否创建成功
SELECT 
    '=== 索引检查结果 ===' as message
UNION ALL
SELECT 
    CONCAT('✓ ', INDEX_NAME, ' (', GROUP_CONCAT(COLUMN_NAME ORDER BY SEQ_IN_INDEX), ')')
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'item_group_relation'
  AND INDEX_NAME IN ('idx_relation_item_type', 'idx_relation_item_id', 'idx_group_relation_type')
GROUP BY INDEX_NAME
ORDER BY INDEX_NAME;

-- 检查数据更新情况
SELECT 
    '=== 数据检查结果 ===' as message
UNION ALL
SELECT 
    CONCAT('关系类型: ', IFNULL(relation, 'NULL'), 
           ', 关联项类型: ', IFNULL(relation_item_type, 'NULL'), 
           ', 数量: ', COUNT(*))
FROM item_group_relation 
GROUP BY relation, relation_item_type
ORDER BY relation, relation_item_type;

-- 提交事务
COMMIT;

-- =====================================================
-- 升级完成提示
-- =====================================================
SELECT 
    '=== 升级完成 ===' as message
UNION ALL
SELECT '✓ 字段添加完成'
UNION ALL
SELECT '✓ 索引创建完成'
UNION ALL
SELECT '✓ 数据更新完成'
UNION ALL
SELECT ''
UNION ALL
SELECT '注意事项：'
UNION ALL
SELECT '1. 如果看到"字段已存在"或"索引已存在"的错误，这是正常的'
UNION ALL
SELECT '2. 请检查上面的验证结果确认升级成功'
UNION ALL
SELECT '3. 现在可以测试前端依赖项目功能'
UNION ALL
SELECT '4. 如有问题请查看错误日志';

-- =====================================================
-- 使用说明
-- =====================================================
/*
使用方法：

1. 备份数据库：
   mysqldump -u username -p database_name > backup.sql

2. 执行升级脚本：
   mysql -u username -p database_name < MySQL5.7兼容升级脚本.sql

3. 如果执行过程中出现以下错误，可以忽略：
   - ERROR 1060: Duplicate column name 'relation_item_type'
   - ERROR 1061: Duplicate key name 'idx_relation_item_type'
   
   这些错误表示字段或索引已经存在，是正常现象。

4. 检查升级结果：
   - 查看脚本输出的验证信息
   - 确认所有字段和索引都已创建
   - 测试前端功能是否正常

5. 如果需要回滚：
   DROP INDEX idx_relation_item_type ON item_group_relation;
   DROP INDEX idx_relation_item_id ON item_group_relation;
   DROP INDEX idx_group_relation_type ON item_group_relation;
   ALTER TABLE item_group_relation DROP COLUMN relation_item_name;
   ALTER TABLE item_group_relation DROP COLUMN relation_item_id;
   ALTER TABLE item_group_relation DROP COLUMN relation_item_type;
*/
