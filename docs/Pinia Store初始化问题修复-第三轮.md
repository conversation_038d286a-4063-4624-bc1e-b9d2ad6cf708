# Pinia Store 初始化问题修复 - 第三轮

## 问题描述

在前两轮修复后，仍然出现 Pinia store 初始化错误。经过深入分析，发现问题出现在**路由守卫**中，多个路由守卫函数在模块顶层就调用了 store，导致在 Pinia 完全初始化之前就尝试使用 store。

## 问题根源分析

### 1. 路由守卫中的早期调用
在以下路由守卫文件中，store 在函数顶层被调用：

#### `src/router/guard/index.ts`
```typescript
// ❌ 问题代码
function createPageLoadingGuard(router: Router) {
  const userStore = useUserStoreWithOut();    // 函数顶层调用
  const appStore = useAppStoreWithOut();      // 函数顶层调用
  // ...
}
```

#### `src/router/guard/permissionGuard.ts`
```typescript
// ❌ 问题代码
export function createPermissionGuard(router: Router) {
  const userStore = useUserStoreWithOut();         // 函数顶层调用
  const permissionStore = usePermissionStoreWithOut(); // 函数顶层调用
  // ...
}
```

#### `src/router/guard/paramMenuGuard.ts`
```typescript
// ❌ 问题代码
export function createParamMenuGuard(router: Router) {
  const permissionStore = usePermissionStoreWithOut(); // 函数顶层调用
  // ...
}
```

### 2. 调用时机问题
这些路由守卫函数在 `main.ts` 的 `setupRouterGuard(router)` 中被调用，时机是：
```typescript
setupStore(app);           // 第50行：创建 Pinia store
// ...
setupRouterGuard(router);  // 第61行：设置路由守卫，此时调用了 store
```

虽然 `setupStore(app)` 已经被调用，但路由守卫的创建可能在 store 完全准备好之前就执行了。

## 解决方案

### 核心思路
将 store 的获取从**函数顶层**移动到**路由守卫回调函数内部**，确保只有在实际路由导航时才获取 store。

### 1. 修复 `src/router/guard/index.ts`

**修复前**：
```typescript
function createPageLoadingGuard(router: Router) {
  const userStore = useUserStoreWithOut();    // ❌ 函数顶层调用
  const appStore = useAppStoreWithOut();      // ❌ 函数顶层调用
  
  router.beforeEach(async (to) => {
    if (!userStore.getToken) {
      return true;
    }
    // ...
  });
}
```

**修复后**：
```typescript
function createPageLoadingGuard(router: Router) {
  const { getOpenPageLoading } = useTransitionSetting();
  
  router.beforeEach(async (to) => {
    // ✅ 在路由守卫执行时才获取 store
    const userStore = useUserStoreWithOut();
    if (!userStore.getToken) {
      return true;
    }
    
    if (unref(getOpenPageLoading)) {
      const appStore = useAppStoreWithOut();  // ✅ 需要时才获取
      appStore.setPageLoadingAction(true);
    }
    // ...
  });
}
```

### 2. 修复 `src/router/guard/permissionGuard.ts`

**修复前**：
```typescript
export function createPermissionGuard(router: Router) {
  const userStore = useUserStoreWithOut();         // ❌ 函数顶层调用
  const permissionStore = usePermissionStoreWithOut(); // ❌ 函数顶层调用
  
  router.beforeEach(async (to, from, next) => {
    // 使用 userStore 和 permissionStore
  });
}
```

**修复后**：
```typescript
export function createPermissionGuard(router: Router) {
  let homePathJumpCount = 0;
  
  router.beforeEach(async (to, from, next) => {
    // ✅ 在路由守卫执行时才获取 store
    const userStore = useUserStoreWithOut();
    const permissionStore = usePermissionStoreWithOut();
    
    // 使用 userStore 和 permissionStore
  });
}
```

### 3. 修复 `src/router/guard/paramMenuGuard.ts`

**修复前**：
```typescript
export function createParamMenuGuard(router: Router) {
  const permissionStore = usePermissionStoreWithOut(); // ❌ 函数顶层调用
  
  router.beforeEach(async (to, _, next) => {
    // 使用 permissionStore
  });
}
```

**修复后**：
```typescript
export function createParamMenuGuard(router: Router) {
  router.beforeEach(async (to, _, next) => {
    // ✅ 在路由守卫执行时才获取 store
    const permissionStore = usePermissionStoreWithOut();
    
    // 使用 permissionStore
  });
}
```

## 修复效果

### 1. 解决初始化时机问题
- ✅ store 只在实际需要时（路由导航时）才被获取
- ✅ 确保 Pinia 完全初始化后才使用 store
- ✅ 避免了函数创建时的早期调用

### 2. 保持功能完整性
- ✅ 所有路由守卫功能保持不变
- ✅ 权限检查、页面加载状态等功能正常工作
- ✅ 性能影响微乎其微（store 获取是轻量操作）

### 3. 提高代码健壮性
- ✅ 避免了模块加载顺序依赖问题
- ✅ 减少了初始化阶段的复杂性
- ✅ 提高了代码的可维护性

## 修复的文件列表

1. **`src/router/guard/index.ts`**
   - 修复了 `createPageLoadingGuard` 函数中的 store 调用时机

2. **`src/router/guard/permissionGuard.ts`**
   - 修复了 `createPermissionGuard` 函数中的 store 调用时机

3. **`src/router/guard/paramMenuGuard.ts`**
   - 修复了 `createParamMenuGuard` 函数中的 store 调用时机

## 最佳实践总结

### 1. Store 使用原则
- **避免在模块顶层调用 store**：特别是在应用初始化阶段
- **延迟获取 store**：在实际需要时才获取，而不是在函数创建时
- **在回调函数内获取**：在事件处理器、路由守卫回调等内部获取

### 2. 路由守卫最佳实践
```typescript
// ✅ 推荐做法
export function createSomeGuard(router: Router) {
  router.beforeEach(async (to, from, next) => {
    // 在这里获取 store
    const someStore = useSomeStoreWithOut();
    // 使用 store
  });
}

// ❌ 避免的做法
export function createSomeGuard(router: Router) {
  const someStore = useSomeStoreWithOut(); // 过早调用
  router.beforeEach(async (to, from, next) => {
    // 使用 store
  });
}
```

### 3. 初始化顺序建议
```typescript
// 推荐的初始化顺序
setupStore(app);           // 1. 创建 Pinia store
await setupI18n(app);      // 2. 初始化国际化
initAppConfigStore();      // 3. 初始化应用配置
setupProps(props);         // 4. 设置应用属性
setupRouter(app);          // 5. 设置路由
setupRouterGuard(router);  // 6. 设置路由守卫（现在安全了）
```

## 结论

通过将路由守卫中的 store 获取从函数顶层移动到回调函数内部，彻底解决了 Pinia store 的初始化时机问题。这个修复确保了：

1. **时机正确**：store 只在 Pinia 完全初始化后才被使用
2. **功能完整**：所有路由守卫功能保持正常
3. **代码健壮**：避免了模块加载顺序依赖问题

现在应用应该可以完全正常启动，不会再出现任何 Pinia store 相关的初始化错误。
