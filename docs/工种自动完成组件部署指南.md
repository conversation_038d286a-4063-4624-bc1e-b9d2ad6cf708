# 工种自动完成组件部署指南

## 概述

本文档描述了工种自动完成组件的完整部署流程，包括数据库更新、后端代码部署和前端组件使用。

## 🗄️ 数据库部署

### 1. 执行数据库更新脚本

运行以下SQL脚本来更新数据库结构：

```bash
# 执行数据库更新脚本
mysql -u username -p database_name < docs/database/zy_worktype_use_count_update.sql
```

### 2. 验证数据库更新

```sql
-- 验证useCount字段是否添加成功
DESCRIBE zy_worktype;

-- 验证索引是否创建成功
SHOW INDEX FROM zy_worktype;

-- 验证缓存表是否创建成功
DESCRIBE zy_worktype_cache;

-- 查看测试数据
SELECT name, code, help_char, use_count FROM zy_worktype WHERE enable_flag = 1 ORDER BY use_count DESC LIMIT 10;
```

## 🔧 后端部署

### 1. 代码文件清单

以下文件已被修改或新增：

**修改的文件：**
- `jeecg-module-physicalex/src/main/java/org/jeecg/modules/occu/entity/ZyWorktype.java`
- `jeecg-module-physicalex/src/main/java/org/jeecg/modules/occu/controller/ZyWorktypeController.java`
- `jeecg-module-physicalex/src/main/java/org/jeecg/modules/occu/service/IZyWorktypeService.java`
- `jeecg-module-physicalex/src/main/java/org/jeecg/modules/occu/service/impl/ZyWorktypeServiceImpl.java`

**新增的文件：**
- `docs/database/zy_worktype_use_count_update.sql`
- `docs/工种自动完成组件部署指南.md`

### 2. 依赖检查

确保项目中包含以下依赖：

```xml
<!-- Redis依赖 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
</dependency>

<!-- MyBatis Plus -->
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
</dependency>
```

### 3. 配置检查

确保Redis配置正确：

```yaml
# application.yml
spring:
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0
```

### 4. 新增的API接口

部署后将提供以下新接口：

- `GET /occu/zyWorktype/autoComplete` - 自动完成搜索
- `POST /occu/zyWorktype/updateUseCount` - 更新使用频次
- `POST /occu/zyWorktype/autoCreate` - 自动创建工种

## 🎨 前端部署

### 1. 文件清单

**新增的文件：**
- `src/components/occu/WorktypeAutoComplete.vue` - 主组件
- `src/components/occu/WorktypeAutoComplete.md` - 组件文档
- `src/components/occu/WorktypeAutoCompleteTest.vue` - 测试页面

**修改的文件：**
- `src/views/occu/ZyWorktype.api.ts` - API接口
- `src/views/occu/components/OccupationalHistoryList.vue` - 集成示例

### 2. 组件使用

在需要工种输入的地方使用组件：

```vue
<template>
  <WorktypeAutoComplete
    v-model:value="worktypeName"
    placeholder="请输入工种名称或助记码"
    :allowAutoCreate="true"
    searchType="both"
    @select="handleSelect"
    @create="handleCreate"
  />
</template>

<script setup>
import WorktypeAutoComplete from '@/components/occu/WorktypeAutoComplete.vue';

const worktypeName = ref('');

const handleSelect = (value, option) => {
  console.log('选择的工种:', value, option);
};

const handleCreate = (newWorktype) => {
  console.log('新创建的工种:', newWorktype);
};
</script>
```

## 🧪 功能测试

### 1. 后端接口测试

使用Postman或其他API测试工具测试接口：

**自动完成搜索：**
```http
GET /occu/zyWorktype/autoComplete?keyword=电&limit=10&searchType=both
```

**更新使用频次：**
```http
POST /occu/zyWorktype/updateUseCount
Content-Type: application/json

{
  "id": "工种ID"
}
```

**自动创建工种：**
```http
POST /occu/zyWorktype/autoCreate
Content-Type: application/json

{
  "name": "新工种名称",
  "helpChar": "XGZ",
  "enableFlag": 1,
  "sort": 999
}
```

### 2. 前端功能测试

1. **基本搜索测试**
   - 输入工种名称，验证自动完成功能
   - 输入助记码，验证搜索匹配
   - 验证搜索结果按使用频次排序

2. **自动创建测试**
   - 输入不存在的工种名称
   - 验证"新建"选项出现
   - 选择新建选项，验证工种自动创建

3. **使用频次测试**
   - 选择已存在的工种
   - 验证使用频次自动增加
   - 验证热门工种排序更新

4. **缓存测试**
   - 多次搜索相同关键词
   - 验证响应速度提升
   - 验证缓存过期机制

## 🔍 故障排查

### 1. 常见问题

**问题1：自动完成接口返回500错误**
- 检查数据库连接是否正常
- 检查useCount字段是否添加成功
- 检查索引是否创建成功

**问题2：Redis缓存异常**
- 检查Redis服务是否启动
- 检查Redis连接配置
- 查看应用日志中的Redis相关错误

**问题3：前端组件无法加载**
- 检查组件文件路径是否正确
- 检查API接口路径是否正确
- 检查浏览器控制台错误信息

### 2. 日志查看

**后端日志：**
```bash
# 查看应用日志
tail -f logs/jeecg-boot.log | grep -i worktype

# 查看Redis相关日志
tail -f logs/jeecg-boot.log | grep -i redis
```

**前端日志：**
- 打开浏览器开发者工具
- 查看Console面板的错误信息
- 查看Network面板的API请求状态

### 3. 性能监控

**数据库性能：**
```sql
-- 查看慢查询
SHOW PROCESSLIST;

-- 查看索引使用情况
EXPLAIN SELECT * FROM zy_worktype WHERE name LIKE '%电%' AND enable_flag = 1;
```

**Redis性能：**
```bash
# 连接Redis查看缓存情况
redis-cli
> KEYS worktype:autocomplete:*
> TTL worktype:autocomplete:both:电:10
```

## 📊 监控指标

建议监控以下指标：

1. **API响应时间**
   - 自动完成搜索接口平均响应时间
   - 缓存命中率

2. **数据库性能**
   - 工种表查询响应时间
   - 索引使用效率

3. **用户使用情况**
   - 自动完成使用频次
   - 新工种创建数量
   - 热门工种排行

## 🚀 上线检查清单

- [ ] 数据库脚本执行完成
- [ ] 后端代码部署完成
- [ ] 前端组件部署完成
- [ ] API接口测试通过
- [ ] 前端功能测试通过
- [ ] Redis缓存正常工作
- [ ] 性能测试通过
- [ ] 日志监控配置完成
- [ ] 用户培训完成

## 📞 技术支持

如遇到问题，请联系开发团队或查看相关文档：

- 组件使用文档：`src/components/occu/WorktypeAutoComplete.md`
- 测试页面：访问WorktypeAutoCompleteTest.vue页面
- 技术支持：联系开发团队
