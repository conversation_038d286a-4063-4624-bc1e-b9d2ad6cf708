# ZyRiskFactorAutoComplete 危害因素自动完成组件

## 概述

`ZyRiskFactorAutoComplete` 是一个功能强大的危害因素自动完成组件，基于 `CompanyAutoComplete` 组件的全套逻辑开发，支持汉字搜索、助记码搜索、五笔码搜索等多种搜索方式。

## 特性

- ✅ **多种搜索方式**：支持汉字、助记码、五笔码搜索
- ✅ **单选/多选模式**：灵活的选择模式
- ✅ **手动录入验证**：支持空格分隔多个危害因素名称的手动录入
- ✅ **批量验证**：自动调用后端接口验证手动录入的名称
- ✅ **智能匹配**：返回完整的危害因素对象信息
- ✅ **缓存支持**：带缓存的自动完成接口，提升性能
- ✅ **降级机制**：接口失败时自动降级到普通搜索
- ✅ **响应式设计**：适配移动端和桌面端
- ✅ **主题支持**：支持深色主题和高对比度主题

## 安装和引入

```typescript
import ZyRiskFactorAutoComplete from '@/components/basicinfo/ZyRiskFactorAutoComplete.vue';
```

## 基础用法

### 单选模式

```vue
<template>
  <ZyRiskFactorAutoComplete
    v-model:value="riskFactorValue"
    placeholder="请输入危害因素名称"
    @change="handleChange"
    @select="handleSelect"
  />
</template>

<script setup>
import { ref } from 'vue';

const riskFactorValue = ref('');

const handleChange = (event) => {
  console.log('选择变化:', event);
  if (event.selectedFactor) {
    console.log('选中的危害因素:', event.selectedFactor);
  }
};

const handleSelect = (value, factor) => {
  console.log('选择了:', factor.name);
};
</script>
```

### 多选模式

```vue
<template>
  <ZyRiskFactorAutoComplete
    v-model:value="multiValue"
    :config="multiConfig"
    @change="handleMultiChange"
  />
</template>

<script setup>
import { ref } from 'vue';

const multiValue = ref('');
const multiConfig = {
  allowMultiple: true,
  separator: ' ',
  enableManualValidation: true,
};

const handleMultiChange = (event) => {
  console.log('多选变化:', event);
  if (event.selectedFactors) {
    console.log('选中的危害因素列表:', event.selectedFactors);
  }
};
</script>
```

## API 参考

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `value` | `string` | `''` | 输入值，支持 v-model |
| `placeholder` | `string` | `'请输入危害因素名称、编码或助记码'` | 占位符文本 |
| `allowClear` | `boolean` | `true` | 是否显示清除按钮 |
| `disabled` | `boolean` | `false` | 是否禁用 |
| `config` | `ZyRiskFactorAutoCompleteConfig` | `{}` | 组件配置选项 |

### Config 配置选项

```typescript
interface ZyRiskFactorAutoCompleteConfig {
  /** 是否支持多值输入 */
  allowMultiple?: boolean;
  /** 多值分隔符 */
  separator?: string;
  /** 是否启用手动输入验证 */
  enableManualValidation?: boolean;
  /** 搜索防抖延迟（毫秒） */
  searchDebounce?: number;
  /** 最大显示选项数 */
  maxOptions?: number;
  /** 是否显示危害因素编码 */
  showCode?: boolean;
  /** 是否显示助记码 */
  showHelpChar?: boolean;
  /** 是否显示五笔码 */
  showWubiChar?: boolean;
  /** 是否显示危害等级 */
  showHarmLevel?: boolean;
}
```

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:value` | `(value: string)` | 输入值变化时触发 |
| `change` | `(event: ZyRiskFactorChangeEvent)` | 选择变化时触发 |
| `select` | `(value: string, option: ZyRiskFactorAutoCompleteDTO)` | 选择选项时触发 |
| `validation-complete` | `(results: ZyRiskFactorValidationResult[])` | 验证完成时触发 |

### Methods

通过 `ref` 可以调用以下方法：

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `validateManualInput` | `()` | `Promise<void>` | 手动触发验证 |
| `clearSelection` | `()` | `void` | 清空选择 |
| `getSelectedFactors` | `()` | `ZyRiskFactorAutoCompleteDTO[]` | 获取选中的危害因素 |
| `getValidationResults` | `()` | `ZyRiskFactorValidationResult[]` | 获取验证结果 |

## 高级用法

### 自定义配置

```vue
<template>
  <ZyRiskFactorAutoComplete
    v-model:value="value"
    :config="customConfig"
    @change="handleChange"
    @validation-complete="handleValidation"
  />
</template>

<script setup>
const customConfig = {
  allowMultiple: true,
  separator: ',',
  enableManualValidation: true,
  searchDebounce: 500,
  maxOptions: 100,
  showCode: true,
  showHelpChar: true,
  showWubiChar: false,
  showHarmLevel: true,
};

const handleValidation = (results) => {
  const summary = results.reduce((acc, result) => {
    if (result.found) acc.success++;
    else acc.failure++;
    return acc;
  }, { success: 0, failure: 0 });
  
  console.log(`验证完成：${summary.success}个成功，${summary.failure}个失败`);
};
</script>
```

### 手动批量验证

```vue
<template>
  <div>
    <a-textarea
      v-model:value="manualInput"
      placeholder="输入多个危害因素，用空格分隔"
      :rows="3"
    />
    <a-button @click="validateInput" :loading="validating">
      批量验证
    </a-button>
    
    <div v-if="validationResults.length > 0">
      <h4>验证结果：</h4>
      <ul>
        <li v-for="result in validationResults" :key="result.inputName">
          {{ result.inputName }}: 
          <span :style="{ color: result.found ? 'green' : 'red' }">
            {{ result.found ? '✓ 找到' : '✗ 未找到' }}
          </span>
          <span v-if="result.matchedFactor">
            - {{ result.matchedFactor.name }} ({{ result.matchedFactor.code }})
          </span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { zyRiskFactorValidationService } from '@/services/zyRiskFactorValidationService';

const manualInput = ref('');
const validating = ref(false);
const validationResults = ref([]);

const validateInput = async () => {
  if (!manualInput.value.trim()) return;
  
  try {
    validating.value = true;
    const { validationResponse } = await zyRiskFactorValidationService.validateMultiValueInput(
      manualInput.value,
      ' ',
      true
    );
    validationResults.value = validationResponse.results;
  } finally {
    validating.value = false;
  }
};
</script>
```

## 数据结构

### ZyRiskFactorAutoCompleteDTO

```typescript
interface ZyRiskFactorAutoCompleteDTO {
  /** 危害因素ID */
  id: string;
  /** 危害因素名称 */
  name: string;
  /** 危害因素编码 */
  code: string;
  /** 助记码 */
  helpChar?: string;
  /** 五笔码 */
  wubiChar?: string;
  /** 危害因素类型 */
  type?: string;
  /** 危害因素分类 */
  category?: string;
  /** 危害等级 */
  harmLevel?: string;
  /** 排序号 */
  sortNo?: number;
}
```

### ZyRiskFactorChangeEvent

```typescript
interface ZyRiskFactorChangeEvent {
  /** 输入值 */
  value: string;
  /** 选中的危害因素（单选时） */
  selectedFactor?: ZyRiskFactorAutoCompleteDTO;
  /** 选中的危害因素列表（多选时） */
  selectedFactors?: ZyRiskFactorAutoCompleteDTO[];
  /** 是否为手动输入 */
  isManualInput: boolean;
  /** 验证结果（手动输入时） */
  validationResults?: ZyRiskFactorValidationResult[];
}
```

## 样式定制

组件支持通过 CSS 变量进行样式定制：

```css
.zy-risk-factor-autocomplete {
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #fa8c16;
  --error-color: #ff4d4f;
  --border-radius: 6px;
}
```

## 注意事项

1. **后端接口依赖**：组件依赖后端提供的危害因素相关接口，确保接口正常可用
2. **缓存配置**：建议配置 Redis 缓存以提升自动完成接口性能
3. **数据量限制**：建议单次验证的危害因素数量不超过 50 个
4. **网络异常处理**：组件内置了降级机制，但建议在使用时添加额外的错误处理
5. **性能优化**：对于大量数据的场景，建议调整 `searchDebounce` 和 `maxOptions` 参数

## 完整示例

查看完整的使用示例，请参考：`src/components/basicinfo/ZyRiskFactorAutoCompleteExample.vue`

## 相关文件

- **组件文件**：`src/components/basicinfo/ZyRiskFactorAutoComplete.vue`
- **类型定义**：`src/types/basicinfo/zyRiskFactor.ts`
- **API 接口**：`src/api/basicinfo/zyRiskFactor.ts`
- **验证服务**：`src/services/zyRiskFactorValidationService.ts`
- **工具函数**：`src/utils/zyRiskFactorUtils.ts`
- **样式文件**：`src/styles/components/zyRiskFactorAutoComplete.less`
- **使用示例**：`src/components/basicinfo/ZyRiskFactorAutoCompleteExample.vue`

## 后端接口要求

组件需要后端提供以下接口：

### 1. 自动完成接口
```
GET /basicinfo/zyRiskFactor/autoComplete
参数：
- keyword: string (可选) - 搜索关键词
- pageSize: number (可选) - 返回数量限制
- searchType: string (可选) - 搜索类型
```

### 2. 批量验证接口
```
POST /basicinfo/zyRiskFactor/batchValidate
请求体：
{
  "names": ["危害因素1", "危害因素2", ...]
}
```

### 3. 精确匹配接口
```
GET /basicinfo/zyRiskFactor/exactMatch
参数：
- name: string - 危害因素名称
```

### 4. 根据名称获取接口
```
POST /basicinfo/zyRiskFactor/getByNames
请求体：
{
  "names": ["危害因素1", "危害因素2", ...]
}
```

## 故障排除

### 常见问题

1. **自动完成不工作**
   - 检查后端接口是否正常
   - 检查网络连接
   - 查看浏览器控制台错误信息

2. **验证失败**
   - 确认后端验证接口可用
   - 检查输入格式是否正确
   - 查看错误提示信息

3. **性能问题**
   - 调整 `searchDebounce` 参数
   - 减少 `maxOptions` 数量
   - 检查后端缓存配置

4. **样式问题**
   - 确认样式文件已正确引入
   - 检查 CSS 变量是否被覆盖
   - 查看浏览器兼容性

## 更新日志

### v1.0.0
- 初始版本发布
- 支持单选/多选模式
- 支持手动录入验证
- 支持批量验证
- 支持多种搜索方式
- 支持缓存和降级机制
- 支持响应式设计和主题定制
