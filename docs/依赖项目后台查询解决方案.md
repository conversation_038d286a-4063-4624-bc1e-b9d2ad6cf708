# 依赖项目后台查询解决方案

## 问题分析

您指出的问题非常正确：**在当前大项列表中肯定找不到依赖项，应该去后台找**。

### 原有问题

1. **局限性查找**：只在当前体检人员的项目列表中查找依赖项目
2. **跨科室依赖**：依赖项目可能属于其他科室，不在当前项目列表中
3. **时序问题**：设备连接时需要同步获取依赖信息，但后台查询是异步的

### 典型场景

```
肺功能检查（肺功能科）依赖：
├─ 收缩压（内科）
├─ 舒张压（内科）  
├─ 既往病史（内科）
└─ 身高、体重（内科）

问题：体检人员只做肺功能检查，没有做内科检查
结果：在当前项目列表中找不到内科项目
```

## 解决方案

### 1. 预构建缓存策略

**核心思想：**在页面加载时预先查询所有依赖项目信息，构建缓存，设备连接时直接从缓存获取。

#### 1.1 时序设计

```
页面加载 → 加载项目组 → 加载依赖关系 → 预构建依赖缓存 → 设备连接 → 从缓存获取
```

#### 1.2 缓存结构

```javascript
// 依赖项目信息缓存
const dependentItemsCache = ref(new Map());

// 缓存结构示例
dependentItemsCache.value.set("itemId", {
  itemType: 'ITEM',
  customerRegId: '体检人员ID',
  itemId: '项目ID',
  itemName: '项目名称',
  value: '项目值',  // 从后台查询或为空
  unit: '单位',
  abnormalFlag: '异常标志',
  // ... 其他字段
});
```

### 2. 实现步骤

#### 2.1 依赖关系加载增强

```javascript
async function loadDependencies() {
  try {
    // 1. 加载依赖关系
    const response = await getDependenciesByGroupIds(groupIds);
    dependencyMap.value.clear();
    Object.entries(response).forEach(([groupId, deps]) => {
      dependencyMap.value.set(groupId, deps);
    });
    
    // 2. 预构建所有依赖项目信息
    await prebuildDependentItems();
    
    // 3. 初始化依赖检查器
    dependencyChecker.value = new DependencyChecker(groupList.value, dependencyMap.value);
  } catch (error) {
    console.error('加载依赖关系失败:', error);
  }
}
```

#### 2.2 预构建依赖项目信息

```javascript
async function prebuildDependentItems() {
  console.log('开始预构建依赖项目信息...');
  
  // 收集所有需要查询的依赖项目ID
  const allDependentItemIds = new Set();
  const allDependentGroupIds = new Set();
  
  dependencyMap.value.forEach((dependencies, groupId) => {
    dependencies.forEach(dep => {
      if (dep.relationItemType === 'ITEM') {
        allDependentItemIds.add(dep.relationItemId);
      } else if (dep.relationItemType === 'GROUP') {
        allDependentGroupIds.add(dep.relationGroupId);
      }
    });
  });

  // 批量查询依赖项目的基本信息和结果
  if (allDependentItemIds.size > 0) {
    await loadDependentItemsInfo(Array.from(allDependentItemIds));
  }
  
  if (allDependentGroupIds.size > 0) {
    await loadDependentGroupsInfo(Array.from(allDependentGroupIds));
  }
}
```

#### 2.3 批量查询依赖项目信息

```javascript
async function loadDependentItemsInfo(itemIds) {
  try {
    // 批量查询小项基本信息
    const itemInfoPromises = itemIds.map(itemId => queryItemInfoById({ id: itemId }));
    const itemInfoResults = await Promise.all(itemInfoPromises);
    
    // 查询当前体检人员的小项结果
    const regItemResults = await queryRegItemResults(itemIds);
    
    // 构建依赖小项信息缓存
    itemInfoResults.forEach((itemInfo, index) => {
      if (itemInfo) {
        const itemId = itemIds[index];
        const itemResult = regItemResults.get(itemId);
        
        const dependentItem = {
          itemType: 'ITEM',
          customerRegId: currentReg.value.id || '',
          itemId: itemId,
          itemName: itemInfo.name,
          hisCode: itemInfo.hisCode || '',
          value: itemResult?.value || '', // 可能为空
          unit: itemInfo.unit || '',
          abnormalFlag: itemResult?.abnormalFlag || 'N',
          valueRefRange: itemInfo.normalRef || '',
          dependentItems: []
        };
        
        dependentItemsCache.value.set(itemId, dependentItem);
      }
    });
  } catch (error) {
    console.error('加载依赖小项信息失败:', error);
  }
}
```

#### 2.4 同步获取依赖项目

```javascript
function buildDependentItems(groupId) {
  const dependentItems = [];
  const dependencies = dependencyMap.value.get(groupId);
  
  if (!dependencies || dependencies.length === 0) {
    return dependentItems;
  }

  dependencies.forEach(dep => {
    let dependentItem = null;
    
    if (dep.relationItemType === 'GROUP') {
      // 从缓存获取大项依赖
      dependentItem = dependentItemsCache.value.get(dep.relationGroupId);
    } else if (dep.relationItemType === 'ITEM') {
      // 从缓存获取小项依赖
      dependentItem = dependentItemsCache.value.get(dep.relationItemId);
    }
    
    if (dependentItem) {
      dependentItems.push(dependentItem);
    }
  });

  return dependentItems;
}
```

### 3. 技术优势

#### 3.1 解决时序问题

- **预加载**：在设备连接前完成所有异步查询
- **同步获取**：设备连接时同步从缓存获取
- **无阻塞**：不影响设备连接的响应速度

#### 3.2 完整数据覆盖

- **跨科室查询**：通过后台API查询所有依赖项目
- **基本信息**：获取项目的完整基本信息
- **结果状态**：查询体检人员的实际结果

#### 3.3 性能优化

- **批量查询**：减少API调用次数
- **并行处理**：使用Promise.all并行查询
- **缓存复用**：多个设备可复用同一份缓存

### 4. 数据流示例

#### 4.1 预构建阶段

```
页面加载
    ↓
加载项目组列表: [肺功能检查]
    ↓
加载依赖关系: [肺功能检查 → 内科(收缩压、舒张压、身高、体重)]
    ↓
收集依赖项目ID: [收缩压ID, 舒张压ID, 身高ID, 体重ID]
    ↓
批量查询项目基本信息: [收缩压{name, unit, hisCode}, 舒张压{...}, ...]
    ↓
查询体检人员结果: [收缩压{value: ""}, 舒张压{value: ""}, ...] (可能为空)
    ↓
构建缓存: dependentItemsCache.set(itemId, completeItemInfo)
```

#### 4.2 设备连接阶段

```
设备连接请求
    ↓
buildExamItemInfo(肺功能检查)
    ↓
buildDependentItems(肺功能检查ID)
    ↓
从缓存获取: [收缩压{完整信息}, 舒张压{完整信息}, ...]
    ↓
构建完整ExamItemInfo: {
  itemId: "肺功能检查ID",
  dependentItems: [收缩压{完整信息}, 舒张压{完整信息}, ...]
}
    ↓
发送给设备
```

### 5. 预期效果

#### 5.1 解决原有问题

- ✅ **跨科室依赖**：能查询到其他科室的依赖项目
- ✅ **完整信息**：获取项目的完整基本信息和结果
- ✅ **时序正确**：设备连接时同步获取，无延迟

#### 5.2 数据完整性

```json
{
  "examItemInfo": {
    "itemId": "肺功能检查ID",
    "itemName": "肺通气功能检查",
    "dependentItems": [
      {
        "itemId": "收缩压ID",
        "itemName": "收缩压",
        "value": "",  // 可能为空，表示未检查
        "unit": "mmHg",
        "hisCode": "SBP",
        "abnormalFlag": "N"
      },
      {
        "itemId": "舒张压ID", 
        "itemName": "舒张压",
        "value": "",  // 可能为空，表示未检查
        "unit": "mmHg",
        "hisCode": "DBP",
        "abnormalFlag": "N"
      }
      // ... 其他依赖项目
    ]
  }
}
```

### 6. 实施建议

#### 6.1 分阶段实施

1. **阶段1**：实现基本的后台查询和缓存机制
2. **阶段2**：优化查询性能和错误处理
3. **阶段3**：添加缓存更新和数据同步机制

#### 6.2 监控和调试

- **加载时间**：监控依赖项目预构建的耗时
- **缓存命中**：统计缓存的使用情况
- **错误处理**：记录查询失败的情况

#### 6.3 后续优化

- **增量更新**：当结果发生变化时更新缓存
- **智能预测**：根据历史数据预测可能的依赖项目
- **缓存策略**：实现更智能的缓存失效和更新机制

## 总结

这个解决方案通过预构建缓存的方式，彻底解决了依赖项目查询的时序问题和跨科室依赖问题。现在系统能够：

1. **完整查询**：通过后台API查询所有依赖项目信息
2. **时序正确**：预先构建缓存，设备连接时同步获取
3. **性能优化**：批量查询和缓存复用
4. **数据完整**：包含项目基本信息和体检人员结果

这样设备就能获得完整、准确的依赖项目信息，为设备的正确计算和分析提供可靠的数据基础。
