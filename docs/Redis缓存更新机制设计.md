# Redis缓存更新机制设计

## 概述

为了解决依赖检查功能的性能问题，设计了基于Redis的缓存机制。本文档详细说明了缓存的更新策略、失效机制和实现方案。

## 缓存更新策略

### 1. 写入时失效策略（推荐）

#### 优点
- 实现简单，逻辑清晰
- 数据一致性好
- 避免缓存与数据库不一致

#### 实现方案
```java
// 后端Service层示例
@Service
public class ItemGroupRelationService {
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    @Autowired
    private WebSocketService webSocketService;
    
    /**
     * 更新项目关系
     */
    @Transactional
    public void updateItemGroupRelation(ItemGroupRelation relation) {
        // 1. 更新数据库
        itemGroupRelationMapper.updateById(relation);
        
        // 2. 删除相关缓存
        invalidateRelationCache(relation.getMainId());
        
        // 3. 通知前端缓存失效
        notifyFrontendCacheInvalidation("itemRelation", relation.getMainId());
    }
    
    /**
     * 删除项目关系缓存
     */
    private void invalidateRelationCache(String mainId) {
        String cacheKey = "item_relation:" + mainId;
        redisTemplate.delete(cacheKey);
        
        // 删除相关的批量查询缓存
        String batchCachePattern = "item_relation_batch:*:" + mainId + ":*";
        Set<String> keys = redisTemplate.keys(batchCachePattern);
        if (!keys.isEmpty()) {
            redisTemplate.delete(keys);
        }
        
        log.info("已删除项目关系缓存: {}", mainId);
    }
    
    /**
     * 通知前端缓存失效
     */
    private void notifyFrontendCacheInvalidation(String type, String itemId) {
        CacheInvalidationMessage message = new CacheInvalidationMessage();
        message.setType(type);
        message.setItemId(itemId);
        message.setTimestamp(System.currentTimeMillis());
        
        webSocketService.broadcast("cacheInvalidation", message);
    }
}
```

### 2. 缓存键设计

#### 项目关系缓存
```
item_relation:{itemGroupId}
例: item_relation:1814465929023721473
```

#### 项目字典缓存
```
item_dict:all
item_dict:version:{version}
```

#### 批量查询缓存
```
item_relation_batch:{hash}:{itemId1}:{itemId2}:...
例: item_relation_batch:abc123:1814465929023721473:1942139225193451521
```

### 3. 缓存失效触发时机

#### 数据变更时失效
- 添加项目关系
- 修改项目关系
- 删除项目关系
- 修改项目基本信息

#### 定时失效
- 项目关系缓存：30分钟TTL
- 项目字典缓存：1小时TTL
- 批量查询缓存：15分钟TTL

## WebSocket实时通知

### 1. 服务端实现

```java
@Component
public class CacheInvalidationWebSocketHandler extends TextWebSocketHandler {
    
    private final Set<WebSocketSession> sessions = ConcurrentHashMap.newKeySet();
    
    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        sessions.add(session);
        log.info("缓存失效监听器已连接: {}", session.getId());
    }
    
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        sessions.remove(session);
        log.info("缓存失效监听器已断开: {}", session.getId());
    }
    
    /**
     * 广播缓存失效消息
     */
    public void broadcastCacheInvalidation(CacheInvalidationMessage message) {
        String jsonMessage = JSON.toJSONString(Map.of(
            "type", "cacheInvalidation",
            "data", message
        ));
        
        sessions.parallelStream().forEach(session -> {
            try {
                if (session.isOpen()) {
                    session.sendMessage(new TextMessage(jsonMessage));
                }
            } catch (Exception e) {
                log.error("发送缓存失效消息失败", e);
                sessions.remove(session);
            }
        });
    }
}
```

### 2. 前端监听实现

```javascript
// 在main.js中初始化
import { initCacheInvalidationListener } from '@/utils/cacheInvalidationListener';

// 初始化缓存失效监听器
const wsUrl = process.env.VUE_APP_WS_URL || 'ws://localhost:8080/ws/cache-invalidation';
initCacheInvalidationListener(wsUrl);

// 监听缓存失效事件
window.addEventListener('cacheInvalidated', (event) => {
  const { type, itemId } = event.detail;
  console.log('收到缓存失效通知:', type, itemId);
  
  // 可以在这里触发UI更新
  if (type === 'itemRelation') {
    // 重新检查依赖关系
    checkAllDependencies(true);
  }
});
```

## 缓存一致性保证

### 1. 事务性更新

```java
@Transactional
public void updateItemGroupRelation(ItemGroupRelation relation) {
    try {
        // 1. 更新数据库
        itemGroupRelationMapper.updateById(relation);
        
        // 2. 删除缓存（在事务提交后执行）
        TransactionSynchronizationManager.registerSynchronization(
            new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    invalidateRelationCache(relation.getMainId());
                    notifyFrontendCacheInvalidation("itemRelation", relation.getMainId());
                }
            }
        );
    } catch (Exception e) {
        log.error("更新项目关系失败", e);
        throw new BusinessException("更新失败");
    }
}
```

### 2. 版本控制

```java
// 使用版本号控制缓存
public class CacheVersionManager {
    
    private static final String VERSION_KEY = "cache_version:item_relation";
    
    /**
     * 获取当前缓存版本
     */
    public Long getCurrentVersion() {
        String version = redisTemplate.opsForValue().get(VERSION_KEY);
        return version != null ? Long.parseLong(version) : 0L;
    }
    
    /**
     * 增加缓存版本
     */
    public Long incrementVersion() {
        return redisTemplate.opsForValue().increment(VERSION_KEY);
    }
    
    /**
     * 构建带版本的缓存键
     */
    public String buildVersionedKey(String baseKey) {
        Long version = getCurrentVersion();
        return baseKey + ":v" + version;
    }
}
```

### 3. 降级策略

```java
/**
 * 缓存降级策略
 */
@Component
public class CacheFallbackStrategy {
    
    /**
     * 获取项目关系（带降级）
     */
    public List<ItemGroupRelation> getItemRelations(String itemGroupId) {
        try {
            // 1. 尝试从Redis获取
            String cacheKey = "item_relation:" + itemGroupId;
            String cached = redisTemplate.opsForValue().get(cacheKey);
            
            if (cached != null) {
                return JSON.parseArray(cached, ItemGroupRelation.class);
            }
            
            // 2. Redis未命中，查询数据库
            List<ItemGroupRelation> relations = queryFromDatabase(itemGroupId);
            
            // 3. 写入Redis缓存
            redisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(relations), 
                Duration.ofMinutes(30));
            
            return relations;
            
        } catch (Exception e) {
            log.error("获取项目关系缓存失败，降级到数据库查询", e);
            // 4. Redis异常，直接查询数据库
            return queryFromDatabase(itemGroupId);
        }
    }
}
```

## 监控和告警

### 1. 缓存指标监控

```java
@Component
public class CacheMetrics {
    
    private final MeterRegistry meterRegistry;
    
    /**
     * 记录缓存命中率
     */
    public void recordCacheHit(String cacheType, boolean hit) {
        Counter.builder("cache.requests")
            .tag("type", cacheType)
            .tag("result", hit ? "hit" : "miss")
            .register(meterRegistry)
            .increment();
    }
    
    /**
     * 记录缓存失效次数
     */
    public void recordCacheInvalidation(String cacheType) {
        Counter.builder("cache.invalidations")
            .tag("type", cacheType)
            .register(meterRegistry)
            .increment();
    }
}
```

### 2. 告警规则

- 缓存命中率 < 70%
- 缓存失效频率 > 100次/分钟
- WebSocket连接断开超过5分钟
- Redis连接异常

## 部署配置

### 1. Redis配置

```yaml
# application.yml
spring:
  redis:
    host: localhost
    port: 6379
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
```

### 2. WebSocket配置

```java
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {
    
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(new CacheInvalidationWebSocketHandler(), "/ws/cache-invalidation")
                .setAllowedOrigins("*");
    }
}
```

## 总结

Redis缓存更新机制通过以下方式保证数据一致性：

1. **写入时失效**：数据变更时立即删除相关缓存
2. **实时通知**：通过WebSocket通知前端缓存失效
3. **版本控制**：使用版本号避免缓存污染
4. **降级策略**：Redis异常时自动降级到数据库
5. **监控告警**：实时监控缓存状态和性能指标

这套机制既保证了性能提升，又确保了数据的一致性和系统的可靠性。
