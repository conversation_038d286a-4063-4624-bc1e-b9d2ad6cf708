# 职业健康问卷集成接口规范

## 接口概述

**接口地址**: `/occu/zyInquiry/getCompleteDataByRegId`  
**请求方式**: GET  
**接口描述**: 根据体检人ID获取完整的职业健康问卷数据，包括主问卷和所有子问卷数据

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regId | String | 是 | 体检人ID |

## 响应数据结构

```json
{
  "success": true,
  "message": "操作成功",
  "code": 200,
  "result": {
    "basicInfo": {
      "id": "主问卷ID",
      "customerRegId": "体检人ID",
      "menarche": 14,
      "menstruation": 5,
      "period": "28",
      "menopauseAge": null,
      "childCount": 1,
      "abortionCount": 0,
      "prematureCount": 0,
      "stillbirth": 0,
      "abnormalfetal": 0,
      "pregnancy": 1,
      "congenitalMalformations": "无",
      "smokStatus": "从不吸",
      "smokAmount": "0",
      "smokYears": "0",
      "drinkStatus": "不喝酒",
      "drinkAmount": "0",
      "drinkYears": "0",
      "createTime": "2024-01-15 10:30:00",
      "updateTime": "2024-01-15 10:30:00"
    },
    "occupationHistory": [
      {
        "id": "职业史记录ID",
        "inquiryId": "主问卷ID",
        "startDate": "2020-01-01",
        "endDate": "2023-12-31",
        "company": "某某化工厂",
        "workshop": "生产车间",
        "workName": "操作工",
        "workYears": 4,
        "workMonths": 0,
        "riskYears": 4,
        "riskMonths": 0,
        "riskName": "苯、甲苯",
        "protectFlag": true,
        "protectMeasures": "佩戴防毒面具、定期体检",
        "createTime": "2024-01-15 10:30:00"
      }
    ],
    "symptoms": [
      {
        "id": "症状记录ID",
        "inquiryId": "主问卷ID",
        "symptomName": "头痛",
        "description": "偶尔头痛，工作时加重",
        "createTime": "2024-01-15 10:30:00"
      },
      {
        "id": "症状记录ID2",
        "inquiryId": "主问卷ID",
        "symptomName": "咳嗽",
        "description": "干咳，晨起明显",
        "createTime": "2024-01-15 10:30:00"
      }
    ],
    "diseaseHistory": [
      {
        "id": "疾病史记录ID",
        "inquiryId": "主问卷ID",
        "diseaseName": "高血压",
        "diagnosisDate": "2022-06-15",
        "description": "轻度高血压，药物控制",
        "createTime": "2024-01-15 10:30:00"
      }
    ],
    "radiationHistory": [
      {
        "id": "放射史记录ID",
        "inquiryId": "主问卷ID",
        "radiationType": "X射线",
        "startDate": "2021-01-01",
        "endDate": "2021-12-31",
        "dose": "低剂量",
        "description": "医院放射科工作",
        "createTime": "2024-01-15 10:30:00"
      }
    ],
    "familyHistory": [
      {
        "id": "家族史记录ID",
        "inquiryId": "主问卷ID",
        "relationship": "父亲",
        "diseaseName": "糖尿病",
        "age": 65,
        "description": "2型糖尿病，胰岛素治疗",
        "createTime": "2024-01-15 10:30:00"
      }
    ],
    "maritalStatus": [
      {
        "id": "婚姻状况记录ID",
        "inquiryId": "主问卷ID",
        "maritalStatus": "已婚",
        "marriageDate": "2018-05-20",
        "spouseInfo": "配偶健康状况良好",
        "createTime": "2024-01-15 10:30:00"
      }
    ]
  },
  "timestamp": 1705123456789
}
```

## 字段说明

### basicInfo (基本信息)
- `menarche`: 月经初潮年龄（仅女性）
- `menstruation`: 经期天数（仅女性）
- `period`: 月经周期（仅女性）
- `menopauseAge`: 停经年龄（仅女性）
- `childCount`: 现有子女个数
- `abortionCount`: 流产次数（仅女性）
- `prematureCount`: 早产次数（仅女性）
- `stillbirth`: 死胎次数（仅女性）
- `abnormalfetal`: 异常胎次数（仅女性）
- `pregnancy`: 怀孕次数（仅女性）
- `congenitalMalformations`: 先天畸形情况
- `smokStatus`: 吸烟状态
- `smokAmount`: 一天吸烟量（支）
- `smokYears`: 吸烟年限
- `drinkStatus`: 饮酒状态
- `drinkAmount`: 一天饮酒量（ml）
- `drinkYears`: 饮酒年限

### occupationHistory (职业史)
- `startDate`: 开始日期
- `endDate`: 结束日期
- `company`: 就职单位
- `workshop`: 车间
- `workName`: 工种
- `workYears`: 总工龄（年）
- `workMonths`: 总工龄（月）
- `riskYears`: 接害工龄（年）
- `riskMonths`: 接害工龄（月）
- `riskName`: 危害因素
- `protectFlag`: 是否防护（boolean）
- `protectMeasures`: 防护措施

### symptoms (症状)
- `symptomName`: 症状名称
- `description`: 症状描述

### diseaseHistory (既往病史)
- `diseaseName`: 疾病名称
- `diagnosisDate`: 诊断日期
- `description`: 详细描述

### radiationHistory (放射史)
- `radiationType`: 放射类型
- `startDate`: 开始日期
- `endDate`: 结束日期
- `dose`: 接触剂量
- `description`: 详细描述

### familyHistory (家族史)
- `relationship`: 关系
- `diseaseName`: 疾病名称
- `age`: 年龄
- `description`: 详细描述

### maritalStatus (婚姻状况)
- `maritalStatus`: 婚姻状态
- `marriageDate`: 结婚日期
- `spouseInfo`: 配偶信息

## 错误响应

```json
{
  "success": false,
  "message": "错误信息",
  "code": 500,
  "result": null,
  "timestamp": 1705123456789
}
```

## 前端展示格式

所有子问卷数据在前端都以**表格形式**展示，具体格式如下：

### 职业接触史表格
| 序号 | 起止日期 | 就职单位 | 车间 | 工种 | 总工龄 | 接害工龄 | 危害因素 | 是否防护 | 防护措施 |

### 症状表格
| 序号 | 症状名称 | 详细描述 | 记录时间 |

### 既往病史表格
| 序号 | 疾病名称 | 诊断时间 | 详细描述 | 记录时间 |

### 放射史表格
| 序号 | 放射类型 | 接触时间 | 接触剂量 | 详细描述 | 记录时间 |

### 家族史表格
| 序号 | 关系 | 疾病名称 | 年龄 | 详细描述 | 记录时间 |

### 婚姻状况表格
| 序号 | 婚姻状态 | 结婚时间 | 配偶信息 | 记录时间 |

## 注意事项

1. 如果某个子问卷没有数据，对应的数组字段应返回空数组 `[]`
2. 如果主问卷不存在，`basicInfo` 字段应返回 `null` 或空对象 `{}`
3. 日期字段统一使用 `YYYY-MM-DD` 格式
4. 时间字段统一使用 `YYYY-MM-DD HH:mm:ss` 格式
5. 布尔字段使用 `true/false`，数据库中可能存储为 `1/0`，需要转换
6. 所有数值字段如果为空，应返回 `null` 而不是字符串
7. **重要**：所有子问卷记录都需要包含 `createTime` 字段，用于在表格中显示记录时间

## 实现建议

1. 可以使用 SQL 的 LEFT JOIN 一次性查询所有相关数据
2. 建议在 Service 层进行数据组装，确保数据结构的一致性
3. 考虑添加缓存机制，提高查询性能
4. 添加适当的日志记录，便于问题排查
