-- 工种危害因素关联表
CREATE TABLE `zy_risk_factor_worktype` (
  `id` varchar(50) NOT NULL COMMENT '主键',
  `worktype_id` varchar(50) NOT NULL COMMENT '工种ID',
  `risk_factor_id` varchar(50) NOT NULL COMMENT '危害因素ID',
  `risk_factor_name` varchar(100) DEFAULT NULL COMMENT '危害因素名称',
  `risk_factor_code` varchar(100) DEFAULT NULL COMMENT '危害因素代码',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  KEY `idx_worktype_id` (`worktype_id`),
  KEY `idx_risk_factor_id` (`risk_factor_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工种危害因素关联表';
