<template>
  <a-radio-group v-if="compType === CompTypeEnum.Radio" v-bind="attrs" v-model:value="state" @change="handleChangeRadio">
    <template v-for="item in dictOptions" :key="`${item.value}`">
      <a-radio :value="item.value">
        <span :class="[useDicColor && item.color ? 'colorText' : '']" :style="{ backgroundColor: `${useDicColor && item.color}` }">
          {{ item.label }}
          <span v-if="showHelpChar && item.helpChar" class="help-char-text">[{{ item.helpChar }}]</span>
        </span>
      </a-radio>
    </template>
  </a-radio-group>

  <a-radio-group
    v-else-if="compType === CompTypeEnum.RadioButton"
    v-bind="attrs"
    v-model:value="state"
    buttonStyle="solid"
    @change="handleChangeRadio"
  >
    <template v-for="item in dictOptions" :key="`${item.value}`">
      <a-radio-button :value="item.value">
        {{ item.label }}
        <span v-if="showHelpChar && item.helpChar" class="help-char-text">[{{ item.helpChar }}]</span>
      </a-radio-button>
    </template>
  </a-radio-group>

  <template v-else-if="compType === CompTypeEnum.Select">
    <!-- 显示加载效果 -->
    <a-input v-if="loadingEcho" readOnly placeholder="加载中…">
      <template #prefix>
        <LoadingOutlined />
      </template>
    </a-input>
    <a-select
      v-else
      :placeholder="computedPlaceholder"
      v-bind="attrs"
      show-search
      v-model:value="state"
      :filterOption="handleFilterOption"
      :getPopupContainer="getPopupContainer"
      :style="style"
      @change="handleChange"
    >
      <a-select-option v-if="showChooseOption" :value="null">请选择…</a-select-option>
      <template v-for="item in dictOptions" :key="`${item.value}`">
        <a-select-option :value="item.value" :helpChar="item.helpChar">
          <span
            :class="[useDicColor && item.color ? 'colorText' : '']"
            :style="{ backgroundColor: `${useDicColor && item.color}` }"
            :title="getOptionTitle(item)"
          >
            {{ item.label }}
            <span v-if="showHelpChar && item.helpChar" class="help-char-text">[{{ item.helpChar }}]</span>
          </span>
        </a-select-option>
      </template>
    </a-select>
  </template>
</template>

<script lang="ts">
  import { defineComponent, PropType, ref, reactive, watchEffect, computed, unref, watch, onMounted, nextTick } from 'vue';
  import { propTypes } from '/@/utils/propTypes';
  import { useAttrs } from '/@/hooks/core/useAttrs';
  import { initDictOptions } from '/@/utils/dict';
  import { get, omit } from 'lodash-es';
  import { useRuleFormItem } from '/@/hooks/component/useFormItem';
  import { CompTypeEnum } from '/@/enums/CompTypeEnum';
  import { LoadingOutlined } from '@ant-design/icons-vue';

  export default defineComponent({
    name: 'JDictSelectTag',
    components: { LoadingOutlined },
    inheritAttrs: false,
    props: {
      value: propTypes.oneOfType([propTypes.string, propTypes.number, propTypes.array]),
      dictCode: propTypes.string,
      type: propTypes.string,
      placeholder: propTypes.string,
      stringToNumber: propTypes.bool,
      useDicColor: propTypes.bool.def(false),
      getPopupContainer: {
        type: Function,
        default: (node) => node?.parentNode,
      },
      // 是否显示【请选择】选项
      showChooseOption: propTypes.bool.def(true),
      // 下拉项-online使用
      options: {
        type: Array,
        default: [],
        required: false,
      },
      style: propTypes.any,
      
      // 新增：助记码相关属性
      helpCharField: propTypes.string, // 助记码字段名
      enableHelpCharSearch: propTypes.bool.def(false), // 是否启用助记码搜索
      searchType: propTypes.oneOf(['text', 'helpChar', 'both']).def('both'), // 搜索类型
      showHelpChar: propTypes.bool.def(true), // 是否显示助记码
      helpCharPlaceholder: propTypes.string, // 助记码搜索提示文本
    },
    emits: ['options-change', 'change', 'update:value'],
    setup(props, { emit, refs }) {
      const dictOptions = ref<any[]>([]);
      const attrs = useAttrs();
      const [state, , , formItemContext] = useRuleFormItem(props, 'value', 'change');
      const getBindValue = Object.assign({}, unref(props), unref(attrs));
      // 是否正在加载回显数据
      const loadingEcho = ref<boolean>(false);
      // 是否是首次加载回显，只有首次加载，才会显示 loading
      let isFirstLoadEcho = true;

      //组件类型
      const compType = computed(() => {
        return !props.type || props.type === 'list' ? 'select' : props.type;
      });

      // 计算占位符文本
      const computedPlaceholder = computed(() => {
        if (props.placeholder) {
          return props.placeholder;
        }
        if (props.enableHelpCharSearch && props.helpCharPlaceholder) {
          return props.helpCharPlaceholder;
        }
        if (props.enableHelpCharSearch) {
          const searchTypeText = {
            'text': '请输入名称搜索',
            'helpChar': '请输入助记码搜索',
            'both': '请输入名称或助记码搜索'
          };
          return searchTypeText[props.searchType] || '请选择';
        }
        return '请选择';
      });

      /**
       * 监听字典code
       */
      watchEffect(() => {
        if (props.dictCode) {
          loadingEcho.value = isFirstLoadEcho;
          isFirstLoadEcho = false;
          initDictData().finally(() => {
            loadingEcho.value = isFirstLoadEcho;
          });
        }
        //update-begin-author:taoyan date: 如果没有提供dictCode 可以走options的配置--
        if (!props.dictCode) {
          dictOptions.value = props.options;
        }
        //update-end-author:taoyan date: 如果没有提供dictCode 可以走options的配置--
      });

      //update-begin-author:taoyan date:20220404 for: 使用useRuleFormItem定义的value，会有一个问题，如果不是操作设置的值而是代码设置的控件值而不能触发change事件
      // 此处添加空值的change事件,即当组件调用地代码设置value为''也能触发change事件
      watch(
        () => props.value,
        () => {
          if (props.value === '') {
            emit('change', '');
            nextTick(() => formItemContext.onFieldChange());
          }
        }
      );
      //update-end-author:taoyan date:20220404 for: 使用useRuleFormItem定义的value，会有一个问题，如果不是操作设置的值而是代码设置的控件值而不能触发change事件

      async function initDictData() {
        let { dictCode, stringToNumber, helpCharField, enableHelpCharSearch } = props;
        
        // 构建字典查询参数
        let dictCodeWithHelpChar = dictCode;
        if (enableHelpCharSearch && helpCharField) {
          // 如果启用助记码搜索，需要在dictCode中包含助记码字段信息
          // 格式：table,textField,valueField,helpCharField
          const parts = dictCode.split(',');
          if (parts.length >= 3) {
            dictCodeWithHelpChar = `${parts[0]},${parts[1]},${parts[2]},${helpCharField}`;
            if (parts.length > 3) {
              // 如果有where条件，保持原有格式
              dictCodeWithHelpChar += ',' + parts.slice(3).join(',');
            }
          }
        }
        
        //根据字典Code, 初始化字典数组
        const dictData = await initDictOptions(dictCodeWithHelpChar);
        dictOptions.value = dictData.reduce((prev, next) => {
          if (next) {
            const value = next['value'];
            const item = {
              label: next['text'] || next['label'],
              value: stringToNumber ? +value : value,
              color: next['color'],
              helpChar: next[helpCharField] || next['helpChar'], // 支持助记码字段
              ...omit(next, ['text', 'value', 'color', 'helpChar', helpCharField]),
            };
            prev.push(item);
          }
          return prev;
        }, []);
      }

      function handleChange(e) {
        const { mode } = unref<Recordable>(getBindValue);
        let changeValue: any;
        // 兼容多选模式

        //update-begin---author:wangshuai ---date:20230216  for：[QQYUN-4290]公文发文：选择机关代字报错,是因为值改变触发了change事件三次，导致数据发生改变------------
        //采用一个值，不然的话state值变换触发多个change
        if (mode === 'multiple') {
          changeValue = e?.target?.value ?? e;
          // 过滤掉空值
          if (changeValue == null || changeValue === '') {
            changeValue = [];
          }
          if (Array.isArray(changeValue)) {
            changeValue = changeValue.filter((item) => item != null && item !== '');
          }
        } else {
          changeValue = e?.target?.value ?? e;
        }
        state.value = changeValue;

        //update-begin---author:wangshuai ---date:20230403  for：【issues/4507】JDictSelectTag组件使用时，浏览器给出警告提示：Expected Function, got Array------------
        emit('update:value', changeValue);
        //update-end---author:wangshuai ---date:20230403  for：【issues/4507】JDictSelectTag组件使用时，浏览器给出警告提示：Expected Function, got Array述------------
        //update-end---author:wangshuai ---date:20230216  for：[QQYUN-4290]公文发文：选择机关代字报错,是因为值改变触发了change事件三次，导致数据发生改变------------

        // nextTick(() => formItemContext.onFieldChange());
      }

      /** 单选radio的值变化事件 */
      function handleChangeRadio(e) {
        state.value = e?.target?.value ?? e;
        //update-begin---author:wangshuai ---date:20230504  for：【issues/506】JDictSelectTag 组件 type="radio" 没有返回值------------
        emit('update:value', e?.target?.value ?? e);
        //update-end---author:wangshuai ---date:20230504  for：【issues/506】JDictSelectTag 组件 type="radio" 没有返回值------------
      }

      /** 用于搜索下拉框中的内容 */
      function handleFilterOption(input, option) {
        const inputLower = input.toLowerCase();
        const optionLabel = typeof option.children === 'function' ? option.children()[0]?.children : option.children;
        const optionValue = option.value || '';

        // 基础匹配
        const labelMatch = optionLabel.toLowerCase().includes(inputLower);
        const valueMatch = optionValue.toString().toLowerCase().includes(inputLower);

        // 助记码匹配
        let helpCharMatch = false;
        if (props.enableHelpCharSearch && option.helpChar) {
          helpCharMatch = option.helpChar.toLowerCase().includes(inputLower);
        }

        // 根据搜索类型决定匹配策略
        if (props.enableHelpCharSearch) {
          switch (props.searchType) {
            case 'text':
              return labelMatch || valueMatch;
            case 'helpChar':
              return helpCharMatch;
            case 'both':
            default:
              return labelMatch || valueMatch || helpCharMatch;
          }
        }

        return labelMatch || valueMatch;
      }

      /** 获取选项的title提示文本 */
      function getOptionTitle(item) {
        let title = item.label;
        if (props.showHelpChar && item.helpChar) {
          title += ` [${item.helpChar}]`;
        }
        return title;
      }

      return {
        state,
        compType,
        attrs,
        loadingEcho,
        getBindValue,
        dictOptions,
        CompTypeEnum,
        computedPlaceholder,
        handleChange,
        handleChangeRadio,
        handleFilterOption,
        getOptionTitle,
      };
    },
  });
</script>

<style scoped lang="less">
  // update-begin--author:liaozhiyang---date:20230110---for：【QQYUN-7799】字典组件（原生组件除外）加上颜色配置
  .colorText {
    display: inline-block;
    height: 20px;
    line-height: 20px;
    padding: 0 6px;
    border-radius: 8px;
    background-color: red;
    color: #fff;
    font-size: 12px;
  }
  
  // 助记码样式
  .help-char-text {
    color: #999;
    font-size: 12px;
    margin-left: 4px;
  }
  // update-begin--author:liaozhiyang---date:20230110---for：【QQYUN-7799】字典组件（原生组件除外）加上颜色配置
</style>
