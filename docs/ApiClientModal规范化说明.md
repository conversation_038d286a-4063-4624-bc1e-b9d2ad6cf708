# ApiClientModal 组件规范化说明

## 概述

本文档说明了将 `src/views/comInterface/modules/ApiClientModal.vue` 从 Vue 2 Options API 风格规范化为 Vue 3 Composition API 风格的改造过程，参照 `src/views/basicinfo/components/ZyConclusionDictForm.vue` 的结构标准。

## 规范化内容

### 1. API 风格转换

#### 改造前 (Vue 2 Options API)
```javascript
export default {
  name: 'ApiClientModal',
  data() {
    return {
      model: {},
      confirmLoading: false,
      // ...
    }
  },
  methods: {
    add() { /* ... */ },
    edit() { /* ... */ }
  }
}
```

#### 改造后 (Vue 3 Composition API)
```typescript
<script lang="ts" setup>
import { ref, reactive, defineExpose, nextTick, defineProps, computed } from 'vue';

const formData = reactive<Record<string, any>>({
  id: '',
  clientName: '',
  // ...
});

const confirmLoading = ref<boolean>(false);

function add(record) { /* ... */ }
function edit(record) { /* ... */ }

defineExpose({
  add,
  edit,
  submitForm,
});
</script>
```

### 2. 表单组件升级

#### 改造前
- 使用 `a-form-model` 组件
- 使用 `this.$form.createForm(this)` 创建表单
- 使用 `v-model` 双向绑定

#### 改造后
- 使用 `a-form` 组件
- 使用 `Form.useForm` 创建表单验证
- 使用 `v-model:value` 双向绑定
- 添加 `v-bind="validateInfos"` 进行验证信息绑定

### 3. 响应式数据管理

#### 改造前
```javascript
data() {
  return {
    model: {},
    confirmLoading: false,
    validatorRules: {
      clientName: [{ required: true, message: '请输入客户端名称!' }],
    }
  }
}
```

#### 改造后
```typescript
const formData = reactive<Record<string, any>>({
  id: '',
  clientName: '',
  apiKey: '',
  apiSecret: '',
  status: 1,
  expireTime: undefined,
  ipWhitelist: '',
  remark: '',
});

const confirmLoading = ref<boolean>(false);

const validatorRules = reactive({
  clientName: [{ required: true, message: '请输入客户端名称!' }],
  status: [{ required: true, message: '请选择状态!' }],
});
```

### 4. 表单验证方式

#### 改造前
```javascript
this.form.validateFields((err, values) => {
  if (!err) {
    // 处理提交逻辑
  }
})
```

#### 改造后
```typescript
const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

async function submitForm() {
  await validate();
  // 处理提交逻辑
}
```

### 5. API 调用方式

#### 改造前
```javascript
import { httpAction, getAction } from '@/api/manage'

httpAction(httpurl, formData, method).then((res) => {
  if(res.success) {
    that.$message.success(res.message);
  }
})
```

#### 改造后
```typescript
import { saveOrUpdate } from '../ApiClient.api';
import { useMessage } from '/@/hooks/web/useMessage';

const { createMessage } = useMessage();

await saveOrUpdate(model, isUpdate.value)
  .then((res) => {
    if (res.success) {
      createMessage.success(res.message);
      emit('ok');
    }
  })
```

### 6. 组件导入和图标使用

#### 改造前
```javascript
<a-icon slot="suffix" type="copy" @click="copyToClipboard(model.apiKey)"/>
```

#### 改造后
```typescript
import { CopyOutlined } from '@ant-design/icons-vue';

<template #suffix>
  <CopyOutlined @click="copyToClipboard(formData.apiKey)" style="cursor: pointer"/>
</template>
```

### 7. Props 和 Emit 定义

#### 改造前
```javascript
// 无明确的 props 定义
this.$emit('ok');
```

#### 改造后
```typescript
const props = defineProps({
  formDisabled: { type: Boolean, default: false },
  formData: { type: Object, default: () => {} },
  formBpm: { type: Boolean, default: true },
});

const emit = defineEmits(['register', 'ok']);
```

### 8. 计算属性

#### 改造前
```javascript
// 无禁用状态计算逻辑
```

#### 改造后
```typescript
const disabled = computed(() => {
  if (props.formBpm === true) {
    if (props.formData.disabled === false) {
      return false;
    } else {
      return true;
    }
  }
  return props.formDisabled;
});
```

## 新增功能

### 1. API 文件创建
创建了 `src/views/comInterface/ApiClient.api.ts` 文件，统一管理 API 接口：
- 使用 `defHttp` 进行 HTTP 请求
- 统一的错误处理和消息提示
- 符合项目 API 规范

### 2. TypeScript 支持
- 添加了 `<script lang="ts" setup>` 语法
- 为响应式数据添加类型定义
- 提供更好的类型检查和 IDE 支持

### 3. 表单禁用逻辑
- 添加了表单禁用状态的计算属性
- 支持基于 props 的动态禁用控制

## 保持的功能

### 1. 复制功能
- 保持了 API 密钥和秘钥的复制功能
- 支持现代浏览器的 Clipboard API 和降级方案

### 2. 表单验证
- 保持了客户端名称和状态的必填验证
- 保持了原有的验证提示信息

### 3. 数据处理逻辑
- 保持了数组数据的字符串转换逻辑
- 保持了时间格式化处理

## 样式保持

保持了原有的样式结构：
```less
.antd-modal-form {
  height: 500px !important;
  overflow-y: auto;
  padding: 14px;
}
```

## 兼容性说明

1. **向后兼容**: 组件的对外接口保持不变，可以无缝替换原组件
2. **功能完整**: 所有原有功能都得到保留和优化
3. **性能提升**: 使用 Composition API 提供更好的性能和可维护性

## 使用方式

组件的使用方式保持不变：
```vue
<template>
  <ApiClientModal ref="modalRef" @ok="handleOk" />
</template>

<script>
// 调用方式
this.$refs.modalRef.add({}); // 新增
this.$refs.modalRef.edit(record); // 编辑
</script>
```

## 总结

本次规范化改造成功将组件从 Vue 2 Options API 升级到 Vue 3 Composition API，提升了代码的可维护性、类型安全性和性能，同时保持了完整的功能兼容性。
