# 依赖项目结果查询接口文档

## 概述

本文档描述了在CustomerRegController中新增的依赖项目结果查询接口，用于查询体检人员的依赖项目结果数据。

## 接口列表

### 1. 查询单个项目的依赖项目结果

**接口地址：** `GET /reg/customerReg/getDependentItemResults`

**接口描述：** 根据体检登记ID和项目组ID查询其依赖项目的结果

**请求参数：**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| customerRegId | String | 是 | 体检登记ID |
| groupId | String | 是 | 项目组ID |

**响应数据：**

```json
{
  "success": true,
  "message": "操作成功",
  "code": 200,
  "result": [
    {
      "itemType": "ITEM",
      "customerRegId": "1234567890",
      "customerRegItemGroupId": "group123",
      "itemId": "item001",
      "itemName": "收缩压",
      "hisCode": "SBP",
      "hisName": "收缩压",
      "value": "120",
      "unit": "mmHg",
      "abnormalFlag": "N",
      "valueRefRange": "90-140",
      "departmentId": "dept001",
      "departmentName": "内科",
      "checkStatus": "已检",
      "isCompleted": true,
      "resultTime": "2025-01-03 10:30:00",
      "inputOperator": "doctor01"
    }
  ],
  "timestamp": 17***********
}
```

### 2. 批量查询多个项目的依赖项目结果

**接口地址：** `POST /reg/customerReg/getDependentItemResultsBatch`

**接口描述：** 根据体检登记ID和多个项目组ID批量查询依赖项目结果

**请求参数：**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| customerRegId | String | 是 | 体检登记ID（Query参数） |

**请求体：**

```json
["groupId1", "groupId2", "groupId3"]
```

**响应数据：**

```json
{
  "success": true,
  "message": "操作成功",
  "code": 200,
  "result": {
    "groupId1": [
      {
        "itemType": "ITEM",
        "customerRegId": "1234567890",
        "itemId": "item001",
        "itemName": "收缩压",
        "value": "120",
        "unit": "mmHg",
        "abnormalFlag": "N",
        "isCompleted": true
      }
    ],
    "groupId2": [
      {
        "itemType": "GROUP",
        "customerRegId": "1234567890",
        "itemId": "group002",
        "itemName": "心电图检查",
        "value": "已检查",
        "abnormalFlag": "N",
        "isCompleted": true
      }
    ]
  },
  "timestamp": 17***********
}
```

### 3. 查询体检人员的所有依赖项目结果

**接口地址：** `GET /reg/customerReg/getAllDependentItemResults`

**接口描述：** 根据体检登记ID查询该人员所有项目的依赖关系和结果

**请求参数：**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| customerRegId | String | 是 | 体检登记ID |

**响应数据：**

```json
{
  "success": true,
  "message": "操作成功",
  "code": 200,
  "result": {
    "肺功能检查": [
      {
        "itemType": "ITEM",
        "itemId": "height",
        "itemName": "身高",
        "value": "175",
        "unit": "cm",
        "isCompleted": true
      },
      {
        "itemType": "ITEM",
        "itemId": "weight",
        "itemName": "体重",
        "value": "70",
        "unit": "kg",
        "isCompleted": true
      }
    ],
    "心脏彩超": [
      {
        "itemType": "GROUP",
        "itemId": "ecg_group",
        "itemName": "心电图检查",
        "value": "已检查",
        "isCompleted": true
      }
    ]
  },
  "timestamp": 17***********
}
```

## 数据结构说明

### DependentItemResultDTO

| 字段名 | 类型 | 描述 |
|--------|------|------|
| itemType | String | 项目类型：GROUP-大项，ITEM-小项 |
| customerRegId | String | 体检登记ID |
| customerRegItemGroupId | String | 体检项目组ID |
| itemId | String | 项目ID（大项ID或小项ID） |
| itemName | String | 项目名称 |
| hisCode | String | HIS代码 |
| hisName | String | HIS名称 |
| value | String | 结果值 |
| unit | String | 单位 |
| abnormalFlag | String | 异常标志：Y-异常，N-正常 |
| valueRefRange | String | 参考值范围 |
| departmentId | String | 科室ID |
| departmentName | String | 科室名称 |
| checkStatus | String | 检查状态 |
| isCompleted | Boolean | 是否已完成检查 |
| resultTime | String | 结果录入时间 |
| inputOperator | String | 录入人员 |

## 业务逻辑说明

### 1. 依赖关系查询

- 通过ItemGroupRelation表查询项目的依赖关系
- 支持大项依赖（GROUP）和小项依赖（ITEM）两种类型
- 只查询关系类型为"依赖"的记录

### 2. 结果数据构建

**大项依赖：**
- 查询ItemGroup获取基本信息
- 查询CustomerRegItemGroup获取登记状态
- 设置检查状态和完成标志

**小项依赖：**
- 查询ItemInfo获取基本信息
- 查询CustomerRegItemGroup获取项目组状态
- 查询CustomerRegItemResult获取具体结果值

### 3. 状态处理

- **未登记**：该依赖项目未在体检套餐中
- **未开始**：已登记但未开始检查
- **未检查**：已开始但未录入结果
- **已检查**：已录入结果
- **已放弃**：标记为放弃检查

## 错误处理

### 常见错误码

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 400 | 参数错误 | 检查必填参数是否传递 |
| 500 | 服务器内部错误 | 查看日志排查具体原因 |

### 错误响应示例

```json
{
  "success": false,
  "message": "Query dependent item results failed: 数据库连接异常",
  "code": 500,
  "result": null,
  "timestamp": 17***********
}
```

## 使用场景

### 1. 设备连接时获取依赖数据

在设备连接时，可以通过这些接口获取当前项目所依赖的其他项目的检查结果，用于设备的参数设置或数据验证。

### 2. 结果录入验证

在录入检查结果时，可以验证依赖项目是否已完成检查，确保数据的完整性和准确性。

### 3. 报告生成

在生成体检报告时，可以获取相关依赖项目的结果，用于综合分析和诊断。

## 性能考虑

### 1. 批量查询优化

- 使用批量查询接口减少网络请求次数
- 内部实现了查询结果缓存，避免重复查询

### 2. 数据库查询优化

- 使用索引优化查询性能
- 避免N+1查询问题
- 合理使用分页和限制条件

## 注意事项

1. **权限控制**：确保调用者有权限访问对应的体检数据
2. **数据一致性**：依赖项目的状态可能会实时变化，注意数据的时效性
3. **异常处理**：妥善处理依赖项目不存在或数据异常的情况
4. **日志记录**：重要操作会记录详细日志，便于问题排查

## 更新日志

- **2025-01-03**：初始版本，新增依赖项目结果查询功能
