# 内网外网环境兼容的文件URL生成方案

## 概述

本方案解决了体检管理系统在内网和外网环境中文件URL生成的兼容性问题，通过智能网络环境检测和多URL配置策略，确保文件在不同网络环境下都能正确访问。

## 核心特性

### 1. 智能网络环境检测
- 自动检测客户端网络环境（内网/外网）
- 基于URL连通性测试选择最佳可用地址
- 支持网络环境变化时的自动切换

### 2. 多URL配置策略
- 支持单一URL配置（向后兼容）
- 支持多URL配置（逗号分隔）
- 支持明确指定内外网地址

### 3. 异步URL处理
- 提供同步和异步两种URL获取方式
- 支持DOM元素的动态URL更新
- 优化用户体验，避免阻塞

## 配置方案

### 方案一：单一URL配置（传统方式）
```bash
# 系统会自动检测网络环境并尝试连接
VITE_GLOB_MINIO_URL=http://************:9000
```

### 方案二：多URL配置（推荐）
```bash
# 支持多个URL，用逗号分隔，系统会自动选择可用的URL
VITE_GLOB_MINIO_URL=http://************:9000,https://minio.example.com
```

### 方案三：明确指定内外网地址（最佳实践）
```bash
# 内网MinIO地址（局域网访问）
VITE_GLOB_MINIO_INTERNAL_URL=http://************:9000

# 外网MinIO地址（公网访问，通过Nginx代理）
VITE_GLOB_MINIO_EXTERNAL_URL=https://minio.example.com
```

## 使用方法

### 1. 基础使用

#### 同步方式（兼容现有代码）
```typescript
import { getFileAccessHttpUrl } from '@/utils/common/compUtils';

// 立即返回URL，后续可能异步更新
const fileUrl = getFileAccessHttpUrl('path/to/file.jpg');
```

#### 异步方式（推荐）
```typescript
import { getFileAccessHttpUrlAsync } from '@/utils/common/compUtils';

// 等待最佳URL获取完成
const fileUrl = await getFileAccessHttpUrlAsync('path/to/file.jpg');
```

### 2. 使用工具类

```typescript
import { FileUrlHelper, useFileUrl } from '@/utils/fileUrlHelper';

// 批量处理文件URL
const processedUrls = await FileUrlHelper.processBatchUrls([
  'file1.jpg',
  'file2.pdf',
  'file3.mp4'
]);

// 处理对象中的文件字段
const processedData = await FileUrlHelper.processObjectFileUrls(
  { avatar: 'avatar.jpg', attachments: ['doc1.pdf', 'doc2.docx'] },
  ['avatar', 'attachments']
);

// 处理表格数据
const processedTableData = await FileUrlHelper.processTableFileUrls(
  tableData,
  ['imageUrl', 'fileUrl']
);
```

### 3. Vue组合式API

```typescript
import { useFileUrl } from '@/utils/fileUrlHelper';

export default {
  setup() {
    const { processFileUrl, createImagePreviewUrl } = useFileUrl();
    
    const handleFileUrl = async (fileUrl: string) => {
      const processedUrl = await processFileUrl(fileUrl);
      return processedUrl;
    };
    
    return {
      handleFileUrl
    };
  }
};
```

### 4. Vue指令

```vue
<template>
  <!-- 自动处理图片URL -->
  <img v-file-url="imageUrl" alt="图片" />
  
  <!-- 自动处理链接URL -->
  <a v-file-url="fileUrl">下载文件</a>
</template>

<script>
import { vFileUrl } from '@/utils/fileUrlHelper';

export default {
  directives: {
    fileUrl: vFileUrl
  }
};
</script>
```

## 网络环境检测机制

### 1. 检测策略
- 优先测试内网URL的连通性
- 内网不通时测试外网URL
- 使用HEAD请求避免下载大量数据
- 3秒超时机制防止长时间等待

### 2. 缓存机制
- URL连通性测试结果缓存
- 避免重复网络请求
- 支持强制重新检测

### 3. 错误处理
- 网络异常时的降级策略
- 日志记录便于问题排查
- 用户友好的错误提示

## 部署配置示例

### 开发环境
```bash
# .env.development
VITE_GLOB_UPLOAD_TYPE=minio
VITE_GLOB_MINIO_INTERNAL_URL=http://************:9000
VITE_GLOB_MINIO_EXTERNAL_URL=https://dev-minio.example.com
```

### 生产环境
```bash
# .env.production
VITE_GLOB_UPLOAD_TYPE=minio
VITE_GLOB_MINIO_INTERNAL_URL=http://*************:9000
VITE_GLOB_MINIO_EXTERNAL_URL=https://files.example.com/minio
```

## 最佳实践

### 1. 配置建议
- 使用方案三（明确指定内外网地址）获得最佳性能
- 内网地址使用HTTP协议，外网地址使用HTTPS协议
- 外网地址建议通过Nginx代理提供额外安全性

### 2. 性能优化
- 使用异步API避免阻塞用户界面
- 批量处理文件URL减少网络请求
- 合理使用缓存机制

### 3. 错误处理
- 监控网络连通性测试结果
- 记录URL切换日志便于问题排查
- 提供用户友好的错误提示

## 兼容性说明

### 1. 向后兼容
- 现有的`getFileAccessHttpUrl`函数保持兼容
- 支持原有的单一URL配置方式
- 不影响现有业务逻辑

### 2. 渐进式升级
- 可以逐步将同步调用改为异步调用
- 可以逐步使用新的工具类和API
- 支持新旧配置方式并存

## 故障排除

### 1. 常见问题
- **文件无法访问**：检查网络连通性和URL配置
- **切换不及时**：清除缓存或强制重新检测
- **性能问题**：使用异步API和批量处理

### 2. 调试方法
- 查看浏览器控制台的网络请求日志
- 使用`NetworkEnvironmentManager.getInstance().getEnvironmentInfo()`查看环境信息
- 使用`NetworkEnvironmentManager.getInstance().forceRedetect()`强制重新检测

### 3. 监控指标
- URL连通性测试成功率
- 网络环境切换频率
- 文件访问错误率

## 实施步骤

### 1. 环境变量配置
根据实际部署情况，选择合适的配置方案：

```bash
# 推荐配置（方案三）
VITE_GLOB_UPLOAD_TYPE=minio
VITE_GLOB_MINIO_INTERNAL_URL=http://************:9000
VITE_GLOB_MINIO_EXTERNAL_URL=https://files.example.com/minio
```

### 2. 代码迁移
逐步将现有代码迁移到新的API：

```typescript
// 旧代码
const fileUrl = getFileAccessHttpUrl('path/to/file.jpg');

// 新代码（推荐）
const fileUrl = await getFileAccessHttpUrlAsync('path/to/file.jpg');
```

### 3. 测试验证
使用提供的测试工具验证配置：

```typescript
// 在浏览器控制台中运行
await window.networkTest.quick();
```

### 4. 监控部署
部署后监控网络切换情况和文件访问成功率。

## 文件清单

本方案涉及的主要文件：

### 核心文件
- `src/utils/common/compUtils.ts` - 核心网络环境管理器和URL处理逻辑
- `src/utils/fileUrlHelper.ts` - 文件URL处理工具类和组合式API
- `src/utils/networkTestHelper.ts` - 网络测试和验证工具

### 配置文件
- `types/config.d.ts` - TypeScript类型定义
- `src/utils/env.ts` - 环境变量读取逻辑
- `src/hooks/setting/index.ts` - 全局设置处理
- `.env.example` - 环境变量配置示例

### 示例和文档
- `src/components/examples/FileUrlExample.vue` - 使用示例组件
- `docs/内网外网环境兼容方案.md` - 详细文档
- `docs/VITE_GLOB_MINIO_URL_分析.md` - 原有分析文档

## 总结

本方案通过智能网络环境检测和多URL配置策略，有效解决了内网外网环境的兼容性问题，提供了：

1. **自动化**：无需手动配置，系统自动选择最佳URL
2. **高可用**：多URL备份，确保服务可用性
3. **高性能**：缓存机制和异步处理优化用户体验
4. **易维护**：清晰的配置方式和完善的错误处理
5. **向后兼容**：不影响现有功能，支持渐进式升级
6. **完整工具链**：提供测试、验证、监控等完整工具支持
