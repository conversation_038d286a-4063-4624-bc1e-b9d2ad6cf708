# 项目关系美观显示方案

## 设计理念

基于用户反馈，设计了一套既能体现项目关系，又保持整齐美观的显示方案。提供多种显示模式，让用户根据个人喜好和使用场景选择最适合的显示方式。

## 核心特性

### 1. 多种显示模式

#### 前缀模式（推荐）
- **符号**：使用 `├─` 树形连接符显示关系
- **颜色**：不同关系类型使用不同颜色
- **优势**：简洁美观，类似文件树结构，直观易懂

#### 标签模式（经典）
- **显示**：使用彩色标签显示关系类型
- **内容**：显示"依赖"、"赠送"、"附属"等文字
- **优势**：信息明确，颜色区分度高

#### 完整模式（详细）
- **组合**：同时显示前缀符号和标签
- **信息**：提供最完整的关系信息
- **适用**：需要详细信息的场景

#### 隐藏模式（简洁）
- **显示**：不显示任何关系标识
- **用途**：追求极简界面的用户
- **特点**：最大化项目名称显示空间

### 2. 智能切换机制

- **一键切换**：点击按钮即可在四种模式间循环切换
- **记忆功能**：自动保存用户偏好设置
- **实时预览**：切换后立即生效，无需刷新

### 3. 视觉设计优化

#### 颜色方案
```css
依赖项目：#fa8c16 (橙色) - 表示需要注意的关联关系
赠送项目：#52c41a (绿色) - 表示免费获得的项目  
附属项目：#722ed1 (紫色) - 表示从属关系
```

#### 字体设计
- **前缀符号**：12px，加粗显示，突出关系
- **项目名称**：关系项目使用灰色 (#666)，区分主项目
- **对齐方式**：左对齐，保持整齐排列

## 技术实现

### 1. 核心数据结构

```javascript
// 显示模式配置
const relationDisplayMode = ref('prefix'); // 'prefix' | 'badge' | 'both' | 'none'

// 前缀样式配置
const prefixStyles = {
  'tree': { dependent: '├─', gift: '├─', attach: '├─' },
  'arrow': { dependent: '→', gift: '→', attach: '→' },
  'dot': { dependent: '●', gift: '●', attach: '●' },
  'icon': { dependent: '🔗', gift: '🎁', attach: '📎' }
};
```

### 2. 关键函数

#### getItemRelationPrefix()
```javascript
function getItemRelationPrefix(itemGroupId) {
  if (relationDisplayMode.value === 'badge' || relationDisplayMode.value === 'none') {
    return null;
  }
  
  const sourceType = getItemSourceType(itemGroupId);
  const currentStyle = 'tree'; // 可配置
  const prefixText = prefixStyles[currentStyle];
  
  switch (sourceType) {
    case 'dependent':
      return {
        text: prefixText.dependent,
        color: '#fa8c16',
        title: '依赖项目：由其他项目的依赖关系自动添加'
      };
    // ... 其他类型
  }
}
```

#### toggleRelationDisplayMode()
```javascript
function toggleRelationDisplayMode() {
  const modes = ['prefix', 'badge', 'both', 'none'];
  const currentIndex = modes.indexOf(relationDisplayMode.value);
  const nextIndex = (currentIndex + 1) % modes.length;
  relationDisplayMode.value = modes[nextIndex];
  
  // 保存用户偏好
  localStorage.setItem('relationDisplayMode', relationDisplayMode.value);
}
```

### 3. 层级排序算法

```javascript
// 构建层级结构：主项目 + 其子项目
function buildHierarchicalStructure(data) {
  const result = [];
  const processedItems = new Set();

  // 按照加项和减项进行大分类
  const categorizedData = {
    added: data.filter(item => item.addMinusFlag === 1),
    normal: data.filter(item => item.addMinusFlag !== 1 && item.addMinusFlag !== -1),
    reduced: data.filter(item => item.addMinusFlag === -1)
  };

  // 处理每个分类
  ['added', 'normal', 'reduced'].forEach(category => {
    const categoryData = categorizedData[category];

    // 先处理主项目
    categoryData.forEach(item => {
      if (processedItems.has(item.id)) return;

      const sourceType = getItemSourceType(item.itemGroupId);
      if (sourceType === 'main') {
        // 添加主项目
        result.push(item);
        processedItems.add(item.id);

        // 查找并添加该主项目的所有子项目
        const childItems = findChildItems(item.itemGroupId, categoryData);
        childItems.forEach(childItem => {
          if (!processedItems.has(childItem.id)) {
            result.push(childItem);
            processedItems.add(childItem.id);
          }
        });
      }
    });

    // 处理没有找到主项目的孤立子项目
    categoryData.forEach(item => {
      if (!processedItems.has(item.id)) {
        const sourceType = getItemSourceType(item.itemGroupId);
        if (sourceType !== 'main') {
          result.push(item);
          processedItems.add(item.id);
        }
      }
    });
  });

  return result;
}
```

### 4. 关系映射管理

```javascript
// 建立详细的关系映射
async function buildRelationshipMaps() {
  const mainItems = regGroupDataSource.value.filter(item =>
    getItemSourceType(item.itemGroupId) === 'main'
  );

  // 清空现有映射
  itemRelationshipMap.value.clear();
  mainToChildrenMap.value.clear();

  // 为每个主项目建立关系映射
  for (const mainItem of mainItems) {
    const relations = await getItemRelations(mainItem.itemGroupId);
    const childrenIds = new Set();

    // 处理依赖项目
    relations.dependentGroups?.forEach(dep => {
      const depId = typeof dep === 'string' ? dep : dep.relationGroupId;
      if (depId) {
        itemRelationshipMap.value.set(`${depId}_dependent`, mainItem.itemGroupId);
        childrenIds.add(depId);
      }
    });

    // 处理赠送项目
    relations.giftGroups?.forEach(gift => {
      const giftId = typeof gift === 'string' ? gift : gift.relationGroupId;
      if (giftId) {
        itemRelationshipMap.value.set(`${giftId}_gift`, mainItem.itemGroupId);
        childrenIds.add(giftId);
      }
    });

    // 处理附属项目
    relations.attachGroups?.forEach(attach => {
      const attachId = typeof attach === 'string' ? attach : attach.relationGroupId;
      if (attachId) {
        itemRelationshipMap.value.set(`${attachId}_attach`, mainItem.itemGroupId);
        childrenIds.add(attachId);
      }
    });

    // 建立主项目到子项目的映射
    if (childrenIds.size > 0) {
      mainToChildrenMap.value.set(mainItem.itemGroupId, Array.from(childrenIds));
    }
  }
}
```

### 5. 模板渲染

```vue
<template>
  <div style="display: flex; align-items: center; gap: 2px; overflow: hidden;">
    <!-- 项目关系前缀显示 -->
    <span
      v-if="getItemRelationPrefix(record.itemGroupId)"
      :style="{
        color: getItemRelationPrefix(record.itemGroupId).color,
        fontSize: '12px',
        fontWeight: 500,
        flexShrink: 0,
        userSelect: 'none'
      }"
      :title="getItemRelationPrefix(record.itemGroupId).title"
    >
      {{ getItemRelationPrefix(record.itemGroupId).text }}
    </span>

    <!-- 项目名称 -->
    <span
      :style="{
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap',
        color: getItemSourceType(record.itemGroupId) !== 'main' ? '#666' : 'inherit'
      }"
    >
      {{ formatItemDisplayName(text, record.checkPartName) }}
    </span>

    <!-- 可选的badge显示 -->
    <a-tag
      v-if="getItemSourceBadge(record.itemGroupId) && showDetailedBadges"
      :title="getItemSourceBadge(record.itemGroupId).title"
      size="small"
      :style="badgeStyle"
      @click="showItemRelationDetail(record.itemGroupId)"
    >
      {{ getItemSourceBadge(record.itemGroupId).text }}
    </a-tag>
  </div>
</template>
```

### 4. 切换按钮设计

```vue
<a-tooltip :title="getDisplayModeTooltip()">
  <a-button 
    size="small" 
    @click="toggleRelationDisplayMode"
    :icon="getDisplayModeIcon()"
    style="flex-shrink: 0;"
  >
    {{ getDisplayModeText() }}
  </a-button>
</a-tooltip>
```

## 界面展示效果

### 前缀模式（层级结构）
```
1. 血常规                    [主项目1]
2. ├─ 尿常规                [依赖项目，属于主项目1]
3. ├─ 血糖                  [赠送项目，属于主项目1]
4. 心电图                    [主项目2]
5. ├─ 胸片                  [依赖项目，属于主项目2]
6. B超                       [主项目3]
7. ├─ 彩超                  [附属项目，属于主项目3]
8. ├─ 心脏彩超              [依赖项目，属于主项目3]
```

### 标签模式（层级结构）
```
1. 血常规                    [主项目1]
2. 尿常规 [依赖]            [依赖项目，属于主项目1]
3. 血糖 [赠送]              [赠送项目，属于主项目1]
4. 心电图                    [主项目2]
5. 胸片 [依赖]              [依赖项目，属于主项目2]
6. B超                       [主项目3]
7. 彩超 [附属]              [附属项目，属于主项目3]
8. 心脏彩超 [依赖]          [依赖项目，属于主项目3]
```

### 完整模式
```
1. 血常规
2. ├─ 尿常规 [依赖]
3. 心电图
4. ├─ 胸片 [依赖]
5. ├─ 肝功能 [赠送]
6. B超
7. ├─ 彩超 [附属]
```

### 隐藏模式
```
1. 血常规
2. 尿常规
3. 心电图
4. 胸片
5. 肝功能
6. B超
7. 彩超
```

## 用户体验优势

### 1. 灵活性
- **个性化选择**：用户可根据喜好选择显示方式
- **场景适应**：不同使用场景可选择不同模式
- **即时切换**：无需重新加载，实时生效

### 2. 美观性
- **整齐对齐**：所有模式都保持良好的对齐效果
- **颜色协调**：使用统一的颜色方案，视觉和谐
- **空间优化**：合理利用显示空间，避免拥挤

### 3. 易用性
- **直观理解**：前缀符号类似文件树，用户容易理解
- **信息层次**：通过颜色和符号区分信息重要性
- **操作简单**：一键切换，无需复杂设置

### 4. 功能完整性
- **关系清晰**：清楚显示项目间的依赖关系
- **详情可查**：点击可查看完整的关系详情
- **状态保持**：记住用户的选择偏好

## 扩展性设计

### 1. 前缀样式扩展
```javascript
const prefixStyles = {
  'tree': { dependent: '├─', gift: '├─', attach: '├─' },
  'arrow': { dependent: '→', gift: '→', attach: '→' },
  'dot': { dependent: '●', gift: '●', attach: '●' },
  'icon': { dependent: '🔗', gift: '🎁', attach: '📎' },
  // 可以轻松添加新样式
  'line': { dependent: '│', gift: '│', attach: '│' }
};
```

### 2. 显示模式扩展
- 可以添加新的显示模式
- 支持自定义颜色方案
- 支持用户自定义前缀符号

### 3. 配置化支持
- 前缀样式可配置
- 颜色方案可配置
- 默认显示模式可配置

## 性能考虑

### 1. 渲染优化
- 使用computed属性缓存计算结果
- 避免不必要的DOM操作
- 优化大列表的渲染性能

### 2. 内存管理
- 合理使用缓存机制
- 及时清理无用数据
- 避免内存泄漏

### 3. 用户体验
- 切换动画流畅
- 响应速度快
- 不影响其他功能

## 兼容性

### 1. 向后兼容
- 保持原有功能不变
- 默认使用前缀模式
- 支持旧版本数据

### 2. 浏览器兼容
- 支持主流浏览器
- 优雅降级处理
- 响应式设计

## 总结

这个设计方案通过提供多种显示模式，既满足了用户对美观性的要求，又保持了功能的完整性。前缀模式作为推荐方案，在简洁性和信息表达之间找到了很好的平衡点。用户可以根据自己的需求和喜好选择最适合的显示方式，真正实现了个性化的用户体验。
