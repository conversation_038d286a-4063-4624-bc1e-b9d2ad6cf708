# 附属项目和赠送项目部位继承逻辑说明

## 概述

本次修改实现了附属项目和赠送项目的部位继承逻辑：当跟随主项目添加的附属项目或赠送项目没有配置部位信息时，会自动使用主项目的部位信息。

## 实现内容

### 1. 部位继承规则

#### 1.1 附属项目部位继承
- **优先级1**: 使用配置中指定的附属项目部位
- **优先级2**: 如果附属项目没有配置部位，但主项目有部位，则使用主项目的部位
- **优先级3**: 如果都没有部位信息，则不设置部位

#### 1.2 赠送项目部位继承
- **优先级1**: 使用配置中指定的赠送项目部位
- **优先级2**: 如果赠送项目没有配置部位，但主项目有部位，则使用主项目的部位
- **优先级3**: 如果都没有部位信息，则不设置部位

### 2. 代码实现

#### 2.1 setAttachGroupPartInfo方法增强
```java
private void setAttachGroupPartInfo(CustomerRegItemGroup attachGroup, ItemGroupRelation relation, CustomerRegItemGroup mainGroup) {
    // 优先使用配置中指定的附属项目部位
    if (StringUtils.isNotBlank(relation.getRelationCheckPartId())) {
        // 设置配置的部位信息
        attachGroup.setCheckPartId(relation.getRelationCheckPartId());
        attachGroup.setCheckPartName(relation.getRelationCheckPartName());
        attachGroup.setCheckPartCode(relation.getRelationCheckPartCode());
        // 更新项目名称
    } 
    // 如果附属项目没有配置部位，但主项目有部位，则使用主项目的部位
    else if (StringUtils.isNotBlank(mainGroup.getCheckPartId())) {
        // 继承主项目的部位信息
        attachGroup.setCheckPartId(mainGroup.getCheckPartId());
        attachGroup.setCheckPartName(mainGroup.getCheckPartName());
        attachGroup.setCheckPartCode(mainGroup.getCheckPartCode());
        // 更新项目名称
    }
}
```

#### 2.2 setGiftGroupPartInfo方法增强
```java
private void setGiftGroupPartInfo(CustomerRegItemGroup giftGroup, ItemGroupRelation relation, CustomerRegItemGroup mainGroup) {
    // 优先使用配置中指定的赠送项目部位
    if (StringUtils.isNotBlank(relation.getRelationCheckPartId())) {
        // 设置配置的部位信息并标记为赠送
    } 
    // 如果赠送项目没有配置部位，但主项目有部位，则使用主项目的部位
    else if (StringUtils.isNotBlank(mainGroup.getCheckPartId())) {
        // 继承主项目的部位信息并标记为赠送
        giftGroup.setCheckPartId(mainGroup.getCheckPartId());
        giftGroup.setCheckPartName(mainGroup.getCheckPartName());
        giftGroup.setCheckPartCode(mainGroup.getCheckPartCode());
        // 更新项目名称为 "项目名-部位名(赠送)"
    } else {
        // 没有部位信息时也要标记为赠送
    }
}
```

### 3. 业务场景示例

#### 3.1 附属项目场景
**配置**：
- 主项目：胸部CT（胸部）
- 附属项目配置：心电图（未配置部位）

**结果**：
- 主项目：胸部CT-胸部
- 附属项目：心电图-胸部（继承主项目部位）

#### 3.2 赠送项目场景
**配置**：
- 主项目：腹部B超（腹部）
- 赠送项目配置：血常规（未配置部位）

**结果**：
- 主项目：腹部B超-腹部
- 赠送项目：血常规-腹部(赠送)（继承主项目部位）

#### 3.3 混合场景
**配置**：
- 主项目：头部MRI（头部）
- 附属项目1：脑电图（配置为头部）
- 附属项目2：心电图（未配置部位）
- 赠送项目：血压测量（未配置部位）

**结果**：
- 主项目：头部MRI-头部
- 附属项目1：脑电图-头部（使用配置部位）
- 附属项目2：心电图-头部（继承主项目部位）
- 赠送项目：血压测量-头部(赠送)（继承主项目部位）

### 4. 项目名称处理

#### 4.1 附属项目名称格式
- 有配置部位：`项目名-配置部位名`
- 继承主项目部位：`项目名-主项目部位名`
- 无部位信息：`项目名`

#### 4.2 赠送项目名称格式
- 有配置部位：`项目名-配置部位名(赠送)`
- 继承主项目部位：`项目名-主项目部位名(赠送)`
- 无部位信息：`项目名(赠送)`

### 5. 日志记录

系统会记录部位继承的情况：
```java
log.info("附属项目{}使用主项目{}的部位信息: {}", 
    attachGroup.getItemGroupName(), mainGroup.getItemGroupName(), mainGroup.getCheckPartName());

log.info("赠送项目{}使用主项目{}的部位信息: {}", 
    giftGroup.getItemGroupName(), mainGroup.getItemGroupName(), mainGroup.getCheckPartName());
```

### 6. 技术要点

#### 6.1 方法签名修改
- `setAttachGroupPartInfo(attachGroup, relation, mainGroup)` - 新增mainGroup参数
- `setGiftGroupPartInfo(giftGroup, relation, mainGroup)` - 新增mainGroup参数

#### 6.2 调用点修改
- `getAttachGroups()` 方法中的调用点已更新
- `getGiftGroups()` 方法中的调用点已更新

#### 6.3 向后兼容
- 保持原有的配置优先级不变
- 只在没有配置部位时才使用主项目部位
- 不影响现有的部位配置功能

### 7. 使用效果

#### 7.1 用户体验改进
- 减少了重复的部位配置工作
- 保持了项目间部位信息的一致性
- 提供了更直观的项目名称显示

#### 7.2 数据一致性
- 确保附属项目和赠送项目的部位信息与主项目保持一致
- 避免了部位信息缺失的情况
- 提供了清晰的部位继承逻辑

### 8. 测试建议

#### 8.1 功能测试
1. 测试有配置部位的附属项目和赠送项目
2. 测试无配置部位的附属项目和赠送项目（应继承主项目部位）
3. 测试主项目无部位时的情况
4. 测试混合场景（部分有配置部位，部分无配置部位）

#### 8.2 界面测试
1. 验证项目名称显示是否正确
2. 验证部位信息是否正确传递
3. 验证赠送项目的标识是否正确

### 9. 注意事项

1. **配置优先级**: 始终优先使用配置中明确指定的部位信息
2. **部位继承**: 只有在没有配置部位时才继承主项目部位
3. **名称更新**: 继承部位后会自动更新项目名称
4. **日志监控**: 可通过日志监控部位继承的执行情况

## 总结

本次修改实现了智能的部位继承逻辑，让附属项目和赠送项目能够自动继承主项目的部位信息，提升了用户体验和数据一致性。同时保持了原有配置的优先级，确保了向后兼容性。
