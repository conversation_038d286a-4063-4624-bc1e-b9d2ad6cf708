# 套餐专用接口实现说明

## 实现背景

原有的 `addItemGroupWithCheckParts` 接口是专门为客户登记设计的，不能直接用于套餐管理。因此创建了专门的套餐项目管理接口，用于处理套餐中的项目添加，包括附属项目和赠送项目的自动处理。

## 后端实现

### 1. Controller层

在 `ItemSuitController.java` 中添加了新的接口：

```java
@PostMapping(value = "/addItemGroupsToSuit")
public Result<?> addItemGroupsToSuit(@RequestBody JSONObject params) {
    try {
        String suitId = params.getString("suitId");
        List<SuitGroup> itemGroups = params.getJSONArray("itemGroups").toJavaList(SuitGroup.class);
        
        // 调用服务层方法处理项目添加（包含附属项目和赠送项目）
        itemSuitService.addItemGroupsToSuit(suitId, itemGroups);
        
        // 清除缓存
        itemSuitService.evcitCache();
        
        return Result.OK("项目添加成功，已自动处理附属项目和赠送项目");
        
    } catch (Exception e) {
        log.error("添加项目到套餐失败", e);
        return Result.error("添加失败：" + e.getMessage());
    }
}
```

### 2. Service层

在 `IItemSuitService.java` 接口中添加方法：

```java
/**
 * 添加项目到套餐（含附属项目和赠送项目处理）
 * @param suitId 套餐ID
 * @param itemGroups 要添加的项目列表
 */
void addItemGroupsToSuit(String suitId, List<SuitGroup> itemGroups);
```

### 3. Service实现

在 `ItemSuitServiceImpl.java` 中实现具体逻辑：

#### 3.1 主要处理流程

```java
@Override
@Transactional(rollbackFor = Exception.class)
public void addItemGroupsToSuit(String suitId, List<SuitGroup> itemGroups) {
    // 1. 验证套餐是否存在
    // 2. 获取现有套餐项目，用于重复检查
    // 3. 处理主项目列表
    // 4. 获取附属项目和赠送项目
    // 5. 处理附属项目
    // 6. 处理赠送项目
    // 7. 批量保存所有项目
    // 8. 重新计算套餐价格
}
```

#### 3.2 附属项目处理

```java
// 获取附属项目
List<ItemGroupRelation> subsidiaryRelations = itemGroupRelationService.getSubsidiaryItemsByMainIds(mainGroupIds);

// 为每个主项目创建对应的附属项目
for (ItemGroupRelation relation : subsidiaryRelations) {
    for (SuitGroup mainItem : mainItemsToAdd) {
        if (mainItem.getGroupId().equals(relation.getMainId())) {
            // 创建附属项目
            SuitGroup subsidiaryItem = createSuitGroupFromItemGroup(subsidiaryGroup, suitId);
            subsidiaryItem.setGroupName(subsidiaryGroup.getName() + "(附属)");
            
            // 附属项目继承主项目的部位信息
            if (subsidiaryGroup.getHasCheckPart() != null && subsidiaryGroup.getHasCheckPart().equals("1")) {
                subsidiaryItem.setCheckPartId(mainItem.getCheckPartId());
                subsidiaryItem.setCheckPartName(mainItem.getCheckPartName());
                subsidiaryItem.setCheckPartCode(mainItem.getCheckPartCode());
            }
        }
    }
}
```

#### 3.3 赠送项目处理

```java
// 获取赠送项目
List<ItemGroupRelation> giftRelations = itemGroupRelationService.getGiftItemsByMainIds(mainGroupIds);

// 为每个主项目创建对应的赠送项目
for (ItemGroupRelation relation : giftRelations) {
    for (SuitGroup mainItem : mainItemsToAdd) {
        if (mainItem.getGroupId().equals(relation.getMainId())) {
            // 创建赠送项目
            SuitGroup giftItem = createSuitGroupFromItemGroup(giftGroup, suitId);
            giftItem.setGroupName(giftGroup.getName() + "(赠送)");
            giftItem.setPrice(BigDecimal.ZERO); // 赠送项目价格为0
            giftItem.setPriceAfterDis(BigDecimal.ZERO); // 赠送项目折后价为0
            giftItem.setDisRate("0"); // 赠送项目折扣率为0
            
            // 赠送项目继承主项目的部位信息
            if (giftGroup.getHasCheckPart() != null && giftGroup.getHasCheckPart().equals("1")) {
                giftItem.setCheckPartId(mainItem.getCheckPartId());
                giftItem.setCheckPartName(mainItem.getCheckPartName());
                giftItem.setCheckPartCode(mainItem.getCheckPartCode());
            }
        }
    }
}
```

#### 3.4 辅助方法

```java
/**
 * 从ItemGroup创建SuitGroup
 */
private SuitGroup createSuitGroupFromItemGroup(ItemGroup itemGroup, String suitId) {
    SuitGroup suitGroup = new SuitGroup();
    suitGroup.setSuitId(suitId);
    suitGroup.setGroupId(itemGroup.getId());
    suitGroup.setGroupName(itemGroup.getName());
    suitGroup.setDepartmentId(itemGroup.getDepartmentId());
    suitGroup.setDepartmentName(itemGroup.getDepartmentName());
    suitGroup.setType("健康项目");
    suitGroup.setDisRate("1"); // 默认折扣率为1（无折扣）
    suitGroup.setPrice(itemGroup.getPrice() != null ? itemGroup.getPrice() : BigDecimal.ZERO);
    suitGroup.setPriceAfterDis(itemGroup.getPrice() != null ? itemGroup.getPrice() : BigDecimal.ZERO);
    suitGroup.setMinDiscountRate(itemGroup.getMinDiscountRate() != null ? itemGroup.getMinDiscountRate() : BigDecimal.ONE);
    suitGroup.setPriceDisDiffAmount(BigDecimal.ZERO);
    return suitGroup;
}

/**
 * 重新计算套餐价格
 */
private void recalculateSuitPrice(String suitId) {
    List<SuitGroup> allSuitGroups = this.getGroupOfSuit(suitId, false);
    
    BigDecimal totalPrice = BigDecimal.ZERO;
    BigDecimal totalPriceAfterDis = BigDecimal.ZERO;
    
    for (SuitGroup suitGroup : allSuitGroups) {
        if (suitGroup.getPrice() != null) {
            totalPrice = totalPrice.add(suitGroup.getPrice());
        }
        if (suitGroup.getPriceAfterDis() != null) {
            totalPriceAfterDis = totalPriceAfterDis.add(suitGroup.getPriceAfterDis());
        }
    }
    
    BigDecimal diffPrice = totalPrice.subtract(totalPriceAfterDis);
    
    // 更新套餐价格
    jdbcTemplate.update(
        "UPDATE item_suit SET cost_price = ?, price = ?, diff_price = ? WHERE id = ?",
        totalPrice, totalPriceAfterDis, diffPrice, suitId
    );
}
```

## 前端实现

### 1. API定义

在 `ItemSuit.api.ts` 中添加新的API方法：

```typescript
enum Api {
  // ... 其他API
  addItemGroupsToSuit = '/basicinfo/itemSuit/addItemGroupsToSuit',
}

/**
 * 添加项目到套餐（含附属项目和赠送项目处理）
 */
export const addItemGroupsToSuit = (params) => defHttp.post({ url: Api.addItemGroupsToSuit, params });
```

### 2. 前端调用

在 `GroupTransfer4Suit.vue` 中使用新的API：

```typescript
// 导入新的API
import { addItemGroupsToSuit } from '@/views/basicinfo/ItemSuit.api';

// 批量确认添加项目和部位
async function confirmBatchAddItemsWithParts() {
  // 准备要添加的项目列表（按照套餐API格式）
  const itemGroups: any[] = [];

  batchPartState.itemGroups.forEach(itemGroup => {
    const selection = getItemPartSelection(itemGroup.id);
    selection.selectedParts.forEach(partId => {
      const partOption = getBatchPartOptionById(itemGroup.id, partId);
      const partName = partOption ? partOption.name : '';
      const partCode = partOption ? partOption.code : '';

      const newItem = {
        suitId: props.suitId,
        groupName: itemGroup.name,
        groupId: itemGroup.id, // 套餐API使用groupId
        departmentName: itemGroup.departmentName,
        departmentId: itemGroup.departmentId,
        type: '健康项目',
        disRate: '1', // 字符串格式
        price: itemGroup.price,
        priceAfterDis: itemGroup.price,
        minDiscountRate: itemGroup.minDiscountRate,
        priceDisDiffAmount: 0,
        checkPartId: partId,
        checkPartName: partName,
        checkPartCode: partCode,
      };
      itemGroups.push(newItem);
    });
  });

  // 调用套餐专用API
  const params = {
    suitId: props.suitId,
    itemGroups: itemGroups,
  };

  const res = await addItemGroupsToSuit(params);
  
  if (res.success) {
    message.success(`成功添加 ${itemGroups.length} 个项目-部位组合，后端已自动处理附属项目和赠送项目`);
    await loadSuitData();
    await checkAllDependencies(true);
    closeBatchPartSelector();
  }
}
```

## 主要特点

### 1. 专门针对套餐设计
- 使用 `SuitGroup` 实体而不是 `CustomerRegItemGroup`
- 数据格式完全适配套餐管理需求
- 自动计算套餐总价

### 2. 完整的项目关系处理
- **附属项目**：自动添加，继承主项目部位信息，标记"(附属)"
- **赠送项目**：自动添加，价格设为0，继承主项目部位信息，标记"(赠送)"
- **重复检查**：基于项目ID+部位ID的精确重复检查

### 3. 事务安全
- 使用 `@Transactional` 确保数据一致性
- 异常时自动回滚
- 完整的错误处理和日志记录

### 4. 性能优化
- 批量保存项目，减少数据库操作
- 智能的重复检查，避免不必要的数据库查询
- 缓存清理，确保数据实时性

## 与原有接口的区别

| 特性 | 客户登记接口 | 套餐专用接口 |
|------|-------------|-------------|
| 数据实体 | CustomerRegItemGroup | SuitGroup |
| 主要用途 | 客户体检项目管理 | 套餐项目管理 |
| 价格计算 | 客户级别价格计算 | 套餐级别价格计算 |
| 数据格式 | 客户登记格式 | 套餐管理格式 |
| 业务逻辑 | 客户相关验证 | 套餐相关验证 |

## 总结

通过创建专门的套餐项目管理接口，实现了：

1. **业务分离**：套餐管理和客户登记使用不同的接口和逻辑
2. **功能完整**：自动处理附属项目和赠送项目
3. **数据安全**：完整的事务保护和错误处理
4. **性能优化**：批量操作和智能缓存管理
5. **易于维护**：清晰的代码结构和完整的文档

现在 GroupTransfer4Suit.vue 可以正确地使用专门的套餐接口，确保附属项目和赠送项目能够正常生效。
