# 串口设备依赖项目集成方案

## 需求背景

在向串口设备发送数据时，需要在构建检查项目时包含依赖项目信息。串口设备中间件系统要求的检查项目结构需要包含`dependentItems`字段，用于传递依赖项目的完整信息。

## 技术方案

### 1. ExamItemInfo数据结构

```java
@Data
public class ExamItemInfo {
    /** 体检项目ID */
    private String itemId;
    
    /** 项目名称 */
    private String itemName;
    
    /** HIS代码 */
    private String hisCode;
    
    /** HIS名称 */
    private String hisName;
    
    /** 项目值 */
    private String value;
    
    /** 单位 */
    private String unit;
    
    /** 异常标志 */
    private String abnormalFlag;
    
    /** 参考值范围 */
    private String valueRefRange;
    
    /** 所属科室ID */
    private String departmentId;
    
    /** 所属科室名称 */
    private String departmentName;
    
    /** 依赖项目列表 */
    private List<ExamItemInfo> dependentItems;
}
```

### 2. 依赖项目构建逻辑

#### 2.1 前端构建逻辑

```javascript
/**
 * 构建体检项目信息对象
 * @param comEquipment 设备配置
 * @returns ExamItemInfo 对象
 */
function buildExamItemInfo(comEquipment) {
  const depart = currentDepart.value;
  const relatedGroup = groupList.value.find((group) => group.itemGroupId === comEquipment.groupId);

  // 构建基本项目信息
  const examItemInfo = {
    itemId: comEquipment.groupId || '',
    itemName: relatedGroup?.itemGroupName || '',
    hisName: relatedGroup?.hisName || '',
    hisCode: relatedGroup?.hisCode || '',
    departmentId: depart?.id || '',
    departmentName: depart?.departName || '',
    value: '',
    unit: '',
    abnormalFlag: '',
    valueRefRange: '',
    dependentItems: []
  };

  // 构建依赖项目信息
  if (relatedGroup && relatedGroup.itemGroupId) {
    examItemInfo.dependentItems = buildDependentItems(relatedGroup.itemGroupId);
  }

  return examItemInfo;
}
```

#### 2.2 依赖项目构建

```javascript
/**
 * 构建依赖项目信息
 * @param groupId 主项目组ID
 * @returns 依赖项目列表
 */
function buildDependentItems(groupId) {
  const dependentItems = [];
  
  // 获取项目的依赖关系
  const dependencies = dependencyMap.value.get(groupId);
  if (!dependencies || dependencies.length === 0) {
    return dependentItems;
  }

  dependencies.forEach(dep => {
    if (dep.relationItemType === 'GROUP') {
      // 大项依赖处理
      const dependentGroup = groupList.value.find(g => g.itemGroupId === dep.relationGroupId);
      if (dependentGroup) {
        const dependentItem = {
          itemId: dep.relationGroupId,
          itemName: dep.relationGroupName || dependentGroup.itemGroupName,
          hisCode: dependentGroup.hisCode || '',
          hisName: dependentGroup.hisName || '',
          value: getDependentGroupValue(dependentGroup),
          unit: '',
          abnormalFlag: getDependentGroupAbnormalFlag(dependentGroup),
          valueRefRange: '',
          departmentId: dependentGroup.departmentId || '',
          departmentName: dependentGroup.departmentName || '',
          dependentItems: []
        };
        dependentItems.push(dependentItem);
      }
    } else if (dep.relationItemType === 'ITEM') {
      // 小项依赖处理
      const dependentGroup = groupList.value.find(g => g.itemGroupId === dep.relationGroupId);
      if (dependentGroup && dependentGroup.itemList) {
        const dependentItem = dependentGroup.itemList.find(item => item.id === dep.relationItemId);
        if (dependentItem) {
          const examItem = {
            itemId: dep.relationItemId,
            itemName: dep.relationItemName || dependentItem.name,
            hisCode: dependentItem.hisCode || '',
            hisName: dependentItem.hisName || '',
            value: getDependentItemValue(dependentItem),
            unit: dependentItem.unit || '',
            abnormalFlag: getDependentItemAbnormalFlag(dependentItem),
            valueRefRange: dependentItem.normalRef || '',
            departmentId: dependentGroup.departmentId || '',
            departmentName: dependentGroup.departmentName || '',
            dependentItems: []
          };
          dependentItems.push(examItem);
        }
      }
    }
  });

  return dependentItems;
}
```

### 3. 依赖项目值获取

#### 3.1 大项值获取

```javascript
/**
 * 获取依赖大项的值（通常是完成状态）
 * @param group 大项信息
 * @returns 大项状态值
 */
function getDependentGroupValue(group) {
  if (group.checkStatus === 1) {
    return '已完成';
  } else if (group.checkStatus === 0) {
    return '进行中';
  } else {
    return '未开始';
  }
}

/**
 * 获取依赖大项的异常标志
 * @param group 大项信息
 * @returns 异常标志
 */
function getDependentGroupAbnormalFlag(group) {
  return group.checkStatus === 1 ? 'N' : 'Y';
}
```

#### 3.2 小项值获取

```javascript
/**
 * 获取依赖小项的值
 * @param item 小项信息
 * @returns 小项结果值
 */
function getDependentItemValue(item) {
  if (item.itemResult && item.itemResult.value !== null && item.itemResult.value !== undefined) {
    return String(item.itemResult.value);
  }
  return '';
}

/**
 * 获取依赖小项的异常标志
 * @param item 小项信息
 * @returns 异常标志
 */
function getDependentItemAbnormalFlag(item) {
  if (item.itemResult && item.itemResult.abnormalFlag) {
    return item.itemResult.abnormalFlag;
  }
  return 'N'; // 默认正常
}
```

### 4. 依赖关系数据加载

#### 4.1 依赖关系映射

```javascript
/**依赖关系部分*/
const dependencyMap = ref(new Map());
const dependencyLoading = ref(false);

/**
 * 加载项目依赖关系
 */
async function loadDependencies() {
  if (groupList.value.length === 0) {
    return;
  }

  try {
    dependencyLoading.value = true;
    const groupIds = groupList.value.map(g => g.itemGroupId);
    const response = await getDependenciesByGroupIds(groupIds);
    
    // 构建依赖关系映射
    dependencyMap.value.clear();
    if (response && typeof response === 'object') {
      Object.entries(response).forEach(([groupId, deps]) => {
        dependencyMap.value.set(groupId, deps);
      });
    }
    
    console.log('依赖关系加载完成:', dependencyMap.value);
  } catch (error) {
    console.error('加载依赖关系失败:', error);
  } finally {
    dependencyLoading.value = false;
  }
}
```

#### 4.2 集成到项目组加载

```javascript
function getGroupByDepartId(departmentId) {
  // ... 原有逻辑
  
  listGroupByRegId(reqParam)
    .then((res) => {
      // ... 原有处理逻辑
      
      groupList.value = res;
      
      // 加载依赖关系
      loadDependencies();
      
      // ... 其他逻辑
    });
}
```

## 数据流示例

### 输入数据
```javascript
// 主项目：肺功能检查
const mainProject = {
  itemId: "1814465929023721473",
  itemName: "肺通气功能检查",
  hisCode: "FGNG",
  hisName: "肺功能",
  departmentId: "1813483628940890114",
  departmentName: "功能科"
};

// 依赖关系配置
const dependencies = [
  {
    relationItemType: "ITEM",
    relationGroupId: "1813483628940890114", 
    relationGroupName: "内科检查",
    relationItemId: "1838176728762159106",
    relationItemName: "身高"
  },
  {
    relationItemType: "ITEM", 
    relationGroupId: "1813483628940890114",
    relationGroupName: "内科检查", 
    relationItemId: "1838176959482433538",
    relationItemName: "体重"
  }
];
```

### 输出数据
```javascript
const examItemInfo = {
  itemId: "1814465929023721473",
  itemName: "肺通气功能检查",
  hisCode: "FGNG", 
  hisName: "肺功能",
  value: "",
  unit: "",
  abnormalFlag: "",
  valueRefRange: "",
  departmentId: "1813483628940890114",
  departmentName: "功能科",
  dependentItems: [
    {
      itemId: "1838176728762159106",
      itemName: "身高",
      hisCode: "SG",
      hisName: "身高",
      value: "175",
      unit: "cm",
      abnormalFlag: "N",
      valueRefRange: "150-200",
      departmentId: "1813483628940890114", 
      departmentName: "内科检查",
      dependentItems: []
    },
    {
      itemId: "1838176959482433538", 
      itemName: "体重",
      hisCode: "TZ",
      hisName: "体重", 
      value: "70",
      unit: "kg",
      abnormalFlag: "N", 
      valueRefRange: "50-100",
      departmentId: "1813483628940890114",
      departmentName: "内科检查", 
      dependentItems: []
    }
  ]
};
```

## 业务价值

### 1. 设备集成完整性
- **完整数据传输**：确保串口设备获得完整的项目和依赖信息
- **计算准确性**：设备可以基于依赖数据进行准确计算
- **数据一致性**：保证设备端和系统端数据的一致性

### 2. 依赖关系支持
- **自动依赖解析**：自动识别和构建项目依赖关系
- **实时数据获取**：获取依赖项目的最新结果值
- **状态同步**：同步依赖项目的完成状态和异常标志

### 3. 扩展性设计
- **递归支持**：支持多层依赖关系（当前实现为一层）
- **类型兼容**：支持大项和小项两种依赖类型
- **格式标准**：符合串口设备中间件的数据格式要求

## 实施步骤

### 阶段1：基础结构（已完成）
- ✅ ExamItemInfo类定义
- ✅ 依赖项目构建逻辑
- ✅ 依赖关系数据加载

### 阶段2：集成测试
- 🔄 串口设备数据传输测试
- 🔄 依赖项目数据验证
- 🔄 异常情况处理测试

### 阶段3：优化完善
- ⏳ 性能优化
- ⏳ 错误处理增强
- ⏳ 日志记录完善

## 注意事项

1. **数据同步**：确保依赖项目的数据是最新的
2. **循环依赖**：避免项目间的循环依赖关系
3. **性能考虑**：大量依赖项目时的性能优化
4. **错误处理**：依赖项目缺失时的降级处理
5. **设备兼容**：确保数据格式符合设备要求

## 总结

该方案通过在ExamItemInfo中集成dependentItems字段，实现了向串口设备传输完整的项目依赖信息。方案具有良好的扩展性和兼容性，能够满足各种设备的数据需求，为设备端的准确计算和数据处理提供了可靠的数据基础。
