# 前端导入错误修复报告

## 问题描述

在构建前端项目时出现以下错误：
```
ERROR  error during build:
src/views/reg/FormFieldConfigManage.vue?vue&type=script&setup=true&lang.ts (13:11): "getFormFieldConfigList" is not exported by "src/views/reg/FormFieldConfig.api.ts", imported by "src/views/reg/FormFieldConfigManage.vue?vue&type=script&setup=true&lang.ts".
```

## 问题原因

在`FormFieldConfigManage.vue`文件中导入的函数名与`FormFieldConfig.api.ts`中实际导出的函数名不匹配：

- **导入的函数名**：`getFormFieldConfigList`
- **实际导出的函数名**：`getFormDisplayConfigList`

## 修复内容

### 1. 修复导入语句

**文件**：`src/views/reg/FormFieldConfigManage.vue`

**修改前**：
```typescript
import { getFormFieldConfigList, deleteFormFieldConfig, copyFormFieldConfig, saveFormFieldConfig } from './FormFieldConfig.api';
```

**修改后**：
```typescript
import { getFormDisplayConfigList, deleteFormDisplayConfig, saveFormDisplayConfig, copyFormDisplayConfig } from './FormFieldConfig.api';
```

### 2. 修复函数调用

#### 2.1 修复列表查询调用
**修改前**：
```typescript
const result = await getFormFieldConfigList(params);
```

**修改后**：
```typescript
const result = await getFormDisplayConfigList(params);
```

#### 2.2 修复删除调用
**修改前**：
```typescript
await deleteFormFieldConfig(record.id!);
```

**修改后**：
```typescript
await deleteFormDisplayConfig(record.id!);
```

#### 2.3 修复保存调用
**修改前**：
```typescript
await saveFormFieldConfig(record);
```

**修改后**：
```typescript
await saveFormDisplayConfig(record);
```

#### 2.4 修复复制调用
**修改前**：
```typescript
await copyFormFieldConfig(copyForm);
```

**修改后**：
```typescript
await copyFormDisplayConfig(copyForm);
```

### 3. 添加缺失的复制函数

在`FormFieldConfig.api.ts`中添加了`copyFormDisplayConfig`函数：

```typescript
/**
 * 复制表单显示配置
 */
export const copyFormDisplayConfig = (params: {
  sourceId: string;
  configName: string;
  targetCenterId?: string;
}) => {
  return defHttp.post({
    url: Api.saveFormFieldConfig.replace('/save', '/copy'),
    params: {
      sourceId: params.sourceId,
      newConfigName: params.configName,
      targetCenterId: params.targetCenterId,
    },
  });
};
```

### 4. 修复复制表单结构

#### 4.1 移除不存在的字段
从复制表单中移除了`configCode`字段，因为后端接口不需要这个参数。

**修改前**：
```typescript
const copyForm = reactive({
  sourceId: '',
  configName: '',
  configCode: '',
  targetCenterId: '',
});
```

**修改后**：
```typescript
const copyForm = reactive({
  sourceId: '',
  configName: '',
  targetCenterId: '',
});
```

#### 4.2 移除表单项
从复制弹窗中移除了配置编码的表单项：

**移除的代码**：
```vue
<a-form-item label="配置编码" name="configCode" :rules="[{ required: true, message: '请输入配置编码' }]">
  <a-input v-model:value="copyForm.configCode" placeholder="请输入新配置编码" />
</a-form-item>
```

#### 4.3 修复复制逻辑
**修改前**：
```typescript
const handleCopy = (record: FormDisplayConfig) => {
  copyForm.sourceId = record.id!;
  copyForm.configName = `${record.configName}_副本`;
  copyForm.configCode = `${record.configCode}_copy`;
  copyForm.targetCenterId = record.centerId;
  copyModalVisible.value = true;
};
```

**修改后**：
```typescript
const handleCopy = (record: FormDisplayConfig) => {
  copyForm.sourceId = record.id!;
  copyForm.configName = `${record.configName}_副本`;
  copyForm.targetCenterId = record.centerId;
  copyModalVisible.value = true;
};
```

## 修复结果

修复后，前端项目应该能够正常构建，不再出现导入错误。所有的API调用都使用了正确的函数名，并且复制功能也能正常工作。

## 验证步骤

1. **构建验证**：运行`npm run build`确认没有导入错误
2. **功能验证**：
   - 测试配置列表加载
   - 测试配置删除功能
   - 测试配置保存功能
   - 测试配置复制功能

## 注意事项

1. 确保后端的复制接口`POST /reg/formFieldConfig/copy`已经正确实现
2. 复制功能的参数格式需要与后端接口保持一致
3. 其他使用相同API的文件（如`FieldDisplayConfigManage.vue`）已经使用了正确的函数名，无需修改

## 相关文件

- `src/views/reg/FormFieldConfigManage.vue` - 主要修复文件
- `src/views/reg/FormFieldConfig.api.ts` - 添加复制函数
- `src/views/reg/FieldDisplayConfigManage.vue` - 已使用正确函数名，无需修改
