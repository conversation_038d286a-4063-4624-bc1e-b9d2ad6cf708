# 智能填写功能设计文档

## 功能概述

智能填写功能基于客户的历史职业问卷数据，通过后端多维度评分算法自动推荐最适合的历史数据模板，帮助用户快速填写当前问卷，提高填写效率和数据一致性。

**架构变更**：核心算法逻辑已从前端迁移到后端实现，提高性能和安全性。

## 核心功能

### 数据提取与去重

智能填写算法的核心功能是从多次历史问卷记录中提取、去重和合并子问卷数据：

#### 1. 数据提取
- **职业史**：提取所有历史记录中的职业接触史
- **既往病史**：提取所有疾病记录
- **放射史**：提取所有放射接触记录
- **家族史**：提取所有家族疾病史
- **症状记录**：提取所有症状信息
- **婚姻状况**：提取婚姻状态变化

#### 2. 智能去重
基于关键字段组合进行去重，确保数据完整且不重复：

- **职业史去重**：`company + workName + startDate + endDate`
- **既往病史去重**：`diseaseName + diagnosisDate`
- **放射史去重**：`radiationType + startDate + endDate`
- **家族史去重**：`relationship + diseaseName`
- **症状去重**：`symptomName`
- **婚姻状况去重**：`maritalStatus + marriageDate`

#### 3. 数据合并策略
- **保留最完整数据**：当发现重复记录时，选择字段更完整的版本
- **时间排序**：按时间倒序排列，最新记录在前
- **数据标准化**：统一数据格式，忽略大小写和空格差异

## 算法设计

### 评分维度

智能推荐算法基于以下5个维度进行评分，总分100分：

#### 1. 时间相关性 (25分) - 权重降低
- **近期数据** (≤1年): 25分
- **较新数据** (1-2年): 18分
- **历史数据** (>2年): 10分

#### 2. 合并数据完整性 (35分) - 权重提高，核心优势
- **职业史记录**: 10分 (≥3条额外+5分)
- **症状记录**: 5分
- **既往病史**: 5分
- **放射史**: 5分
- **家族史**: 5分
- **婚姻状况**: 5分

**评价标准**：
- 数据丰富 (≥30分): "数据完整丰富"
- 数据完整 (20-29分): "数据较完整"
- 数据一般 (<20分): 基础数据

#### 3. 数据整合优势 (20分) - 新增维度
- **多记录整合** (>1次记录): 20分
- **单记录**: 0分

**推荐理由**：显示"整合N次历史记录"

#### 4. 基本信息完整性 (10分)
- 吸烟状态: 3分
- 饮酒状态: 3分
- 子女个数: 2分
- 先天畸形情况: 2分

#### 5. 健康状况稳定性 (10分)
- **健康生活方式** (从不吸烟 + 不喝酒): 10分
- **生活方式良好** (不经常吸烟 + 不经常喝酒): 6分
- **其他情况**: 0分

### 推荐排序

1. 按总分从高到低排序
2. 自动选择最高分的记录作为默认推荐
3. 显示推荐理由标签

### 推荐标题

- 第1名: "最佳推荐" (皇冠图标)
- 第2名: "次佳推荐"
- 第3名: "备选推荐"
- 其他: "推荐 N"

## 界面设计

### 推荐卡片

每个推荐项包含：

1. **头部信息**
   - 推荐标题 + 匹配度分数
   - 记录时间
   - 预览按钮

2. **推荐理由**
   - 智能生成的推荐标签
   - 颜色编码的理由说明

3. **数据摘要**
   - 职业史数量
   - 症状数量
   - 吸烟状态
   - 饮酒状态

4. **选中状态**
   - 点击选择
   - 视觉反馈
   - 选中指示器

### 分数颜色编码

- **90-100分**: 绿色 (优秀)
- **80-89分**: 蓝色 (良好)
- **70-79分**: 橙色 (一般)
- **<70分**: 红色 (较差)

## 去重算法详解

### 去重流程

1. **数据收集**：从所有历史记录中收集同类型的子问卷数据
2. **键值生成**：基于关键字段组合生成唯一标识键
3. **重复检测**：使用Map结构检测重复数据
4. **数据选择**：当发现重复时，选择更完整的数据版本
5. **排序输出**：按时间倒序排列最终结果

### 去重示例

**职业史去重**：
```javascript
// 原始数据
[
  { company: '某某化工厂', workName: '操作工', startDate: '2020-01-01', endDate: '2023-12-31' },
  { company: '某某化工厂', workName: '操作工', startDate: '2020-01-01', endDate: '2023-12-31' }, // 重复
  { company: '某某电子厂', workName: '装配工', startDate: '2019-01-01', endDate: '2019-12-31' }
]

// 去重后
[
  { company: '某某化工厂', workName: '操作工', startDate: '2020-01-01', endDate: '2023-12-31' },
  { company: '某某电子厂', workName: '装配工', startDate: '2019-01-01', endDate: '2019-12-31' }
]
```

**症状去重**：
```javascript
// 原始数据
[
  { symptomName: '头痛', description: '偶尔头痛' },
  { symptomName: '头痛', description: '工作时头痛加重' }, // 重复但更详细
  { symptomName: '咳嗽', description: '干咳' }
]

// 去重后（选择更完整的描述）
[
  { symptomName: '头痛', description: '工作时头痛加重' },
  { symptomName: '咳嗽', description: '干咳' }
]
```

## 使用流程

### 1. 触发智能填写
```javascript
// 点击"智能填写"按钮
handleSmartFill() {
  // 验证客户信息和身份证号
  smartFillModalRef.value?.open(currentCustomerReg.value);
}
```

### 2. 加载推荐数据
```javascript
// 直接调用后端智能填写接口，后端处理所有算法逻辑
const result = await getSmartFillRecommendations({ idCard: customerReg.idCard });
```

### 3. 选择和预览
- 用户可以点击不同的推荐项进行选择
- 点击"预览"按钮查看完整的问卷内容
- 系统自动选择最佳推荐作为默认选项

### 4. 应用推荐数据
```javascript
// 应用选中的推荐数据（包含合并后的子问卷数据）
handleApplySmartData(smartData) {
  // 1. 保留当前客户信息
  // 2. 应用主问卷数据 (smartData.basicInfo)
  // 3. 清空现有子问卷
  // 4. 应用合并后的子问卷数据 (smartData.mergedSubQuestionnaires)
  // 5. 显示整合统计信息
  // 6. 刷新界面显示
}
```

## 数据应用策略

### 主问卷数据
应用 `smartData.basicInfo` 中的数据，但保留：
- 当前记录ID
- 客户关联信息
- 身份证号、体检号等客户基本信息

### 合并后的子问卷数据
应用 `smartData.mergedSubQuestionnaires` 中已去重合并的数据：

1. **清空策略**: 先清空所有现有子问卷数据
2. **批量应用**: 调用各子组件的批量添加方法
3. **数据统计**: 显示整合的记录数量和数据统计
4. **数据刷新**: 刷新所有子组件显示

### 数据整合优势
- **去重保证**: 确保没有重复的职业史、病史等记录
- **完整性保证**: 选择最完整的数据版本
- **时间排序**: 按时间顺序展示，便于查看发展历程
- **统计信息**: 清楚显示整合了多少次历史记录

### 支持的子问卷类型
- **职业接触史**: 去重合并所有工作经历
- **症状记录**: 去重合并所有症状信息
- **既往病史**: 去重合并所有疾病记录
- **放射史**: 去重合并所有放射接触史
- **家族史**: 去重合并所有家族疾病史
- **婚姻状况**: 去重合并婚姻状态变化

## 错误处理

### 前置条件检查
- 客户信息完整性
- 身份证号存在性
- 历史数据可用性

### 异常情况处理
- 网络请求失败
- 数据格式错误
- 子组件应用失败

### 用户提示
- 操作成功提示
- 错误信息提示
- 确认对话框

## 性能优化

### 算法优化
- 客户端计算，减少服务器压力
- 缓存历史数据，避免重复请求
- 异步处理，提升用户体验

### 界面优化
- 虚拟滚动（如果数据量大）
- 懒加载预览数据
- 防抖处理用户操作

## 扩展性设计

### 算法参数化
```javascript
const SCORE_WEIGHTS = {
  timeRelevance: 30,
  dataCompleteness: 25,
  occupationContinuity: 20,
  healthStability: 15,
  symptomRelevance: 10
};
```

### 自定义评分规则
- 支持配置不同维度的权重
- 支持添加新的评分维度
- 支持行业特定的评分规则

### 机器学习集成
- 收集用户选择偏好
- 优化推荐算法
- 个性化推荐策略

## 测试策略

### 单元测试
- 评分算法测试
- 边界条件测试
- 数据格式验证

### 集成测试
- 组件交互测试
- 数据流测试
- 错误处理测试

### 用户测试
- 可用性测试
- 效率提升验证
- 用户满意度调研

## 未来优化方向

1. **智能学习**: 基于用户选择行为优化推荐
2. **行业适配**: 针对不同行业定制评分规则
3. **模板管理**: 支持用户自定义模板
4. **批量操作**: 支持多人员批量智能填写
5. **数据分析**: 提供填写效率和准确性分析
