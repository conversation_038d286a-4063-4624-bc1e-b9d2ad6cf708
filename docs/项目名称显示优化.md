# 项目名称显示优化

## 问题描述

在 `GroupListOfPannel.vue` 组件中，项目名称的tooltip显示存在语法错误，导致显示异常。原代码中的表达式运算符优先级不正确：

```javascript
:title="text+'['+record.checkPartName||'' +']'"
```

这个表达式会因为运算符优先级问题导致逻辑错误。

## 解决方案

### 1. 修复语法错误

首先修复了原有的语法错误，正确处理运算符优先级：

```javascript
:title="text + (record.checkPartName ? '[' + record.checkPartName + ']' : '')"
```

### 2. 代码重构优化

为了提高代码的可维护性和可读性，添加了专门的辅助方法来处理项目名称的格式化显示：

#### 新增辅助方法

```typescript
// 格式化项目名称显示（包含部位信息）
function formatItemDisplayName(itemName: string, checkPartName?: string): string {
  if (!checkPartName) {
    return itemName;
  }
  return `${itemName}[${checkPartName}]`;
}

// 获取项目名称的完整显示文本（用于tooltip）
function getItemTooltipText(itemName: string, checkPartName?: string): string {
  return formatItemDisplayName(itemName, checkPartName);
}
```

#### 优化后的模板代码

```html
<a-tooltip v-else :title="getItemTooltipText(text, record.checkPartName)" trigger="click">
  <span style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap">
    {{ formatItemDisplayName(text, record.checkPartName) }}
  </span>
</a-tooltip>
```

## 优化效果

### 1. 代码可读性提升
- 使用语义化的方法名，代码意图更加清晰
- 消除了复杂的内联表达式，提高了模板的可读性

### 2. 维护性改善
- 项目名称格式化逻辑集中管理，便于统一修改
- 如果需要调整显示格式，只需修改辅助方法即可

### 3. 复用性增强
- 辅助方法可以在组件的其他地方复用
- 为将来可能的功能扩展提供了良好的基础

### 4. 错误修复
- 修复了原有的语法错误，确保tooltip正确显示
- 保证了在有部位信息和无部位信息时都能正确显示

## 显示效果

- **无部位信息**：显示为 "项目名称"
- **有部位信息**：显示为 "项目名称[部位名称]"
- **tooltip**：与显示文本保持一致，提供完整的项目信息

## 注意事项

1. 辅助方法使用了可选参数，确保向后兼容
2. 保持了原有的显示格式，不影响用户体验
3. 方法命名遵循了项目的命名规范
4. 代码结构清晰，便于后续维护和扩展
