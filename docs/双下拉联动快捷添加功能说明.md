# 快捷搜索联动部位选择功能说明

## 功能概述

在现有快捷搜索下拉框的右侧添加了部位选择下拉框，实现项目搜索与部位选择的紧密联动。当选择需要部位的项目时，自动聚焦到部位选择框，支持键盘快捷键操作，提供更流畅的项目添加体验。

## 功能特点

### 1. 紧密联动设计
- **保持原有快捷搜索**：完全保留现有的项目和套餐搜索功能
- **右侧部位选择**：在快捷搜索框右侧添加部位选择下拉框
- **智能聚焦**：选择需要部位的项目后，自动聚焦到部位选择框

### 2. 双重操作方式
- **键盘快捷操作**：在任一下拉框中按 Ctrl+Enter 完成添加
- **按钮点击操作**：点击"添加"按钮完成添加
- **自动聚焦流转**：项目选择后自动聚焦到部位选择框
- **操作完成后重新聚焦**：添加完成后自动聚焦回快捷搜索框

### 3. 用户体验优化
- **无缝集成**：与现有界面完美融合，不破坏原有布局
- **智能提示**：部位选项按使用频次排序，常用部位优先显示
- **状态同步**：项目和部位选择状态实时同步

## 界面布局

```
┌─────────────────────────────────────────────────────────────┐
│ [快捷搜索项目/套餐 ▼]  [选择部位 ▼]  [添加]                  │
├─────────────────────────────────────────────────────────────┤
│ 其他原有功能（保持不变）                                      │
└─────────────────────────────────────────────────────────────┘
```

### 布局比例
- **快捷搜索框**：占 14/24 (约58%) 的宽度
- **部位选择框**：占 7/24 (约29%) 的宽度
- **添加按钮**：占 3/24 (约13%) 的宽度

## 技术实现

### 1. 状态管理
```typescript
// 保持原有快捷搜索状态
const quikSearchState = reactive({
  data: [],           // 下拉选项的数据
  value: [],          // 选中的值
  fetching: false,    // 加载状态
});

// 新增部位搜索状态
const partSearchState = reactive({
  selectedParts: [],  // 选中的部位ID列表
  options: [],        // 部位选项
  loading: false,     // 部位加载状态
  currentProject: null, // 当前项目数据
});
```

### 2. 核心方法
- `handleSearchChange()`: 增强的项目选择处理，支持部位联动
- `loadPartsForCurrentProject()`: 加载当前项目的部位选项
- `handleQuickSearchKeydown()`: 快捷搜索框键盘事件处理
- `handlePartSelectKeydown()`: 部位选择框键盘事件处理
- `handleQuickAdd()`: 统一的快捷添加方法

### 3. API调用
- 项目搜索：复用现有的 `listItemGroupByKeyword`
- 部位加载：复用现有的 `listByItemGroup`
- 项目添加：复用现有的 `addItemGroupWithCheckParts` 或 `handleAddOne`

### 4. 键盘事件处理优化
```javascript
// 使用 nextTick 确保状态更新完成
function handlePartSelectKeydown(event: KeyboardEvent) {
  if (event.ctrlKey && event.key === 'Enter') {
    event.preventDefault();
    nextTick(() => {
      handleQuickAdd();
    });
  }
}

// 在添加操作开始时保存状态，避免被清空
async function handleQuickAdd() {
  const selectedParts = [...partSearchState.selectedParts];
  // 使用保存的状态进行后续操作
}
```

## 使用流程

### 场景1：添加无部位项目
1. 在快捷搜索框中输入关键字搜索项目
2. 选择目标项目（不需要部位的项目）
3. 系统自动添加项目，重置状态并重新聚焦到搜索框

### 场景2：添加有部位项目
1. 在快捷搜索框中输入关键字搜索项目
2. 选择需要部位的项目
3. 系统自动加载部位选项并聚焦到部位选择框
4. 选择一个或多个部位
5. 按 Ctrl+Enter 快捷添加，或点击"添加"按钮
6. 系统为每个部位创建独立的项目记录

### 场景3：套餐添加（保持原有逻辑）
1. 在快捷搜索框中输入 "--" + 套餐关键字
2. 选择套餐
3. 系统按原有逻辑处理套餐添加

## 与现有功能的关系

### 完全兼容现有功能
- **快捷搜索**：完全保留原有的项目和套餐搜索逻辑
- **项目管理弹窗**：完全保留，用于复杂的批量操作
- **部位选择弹窗**：完全保留，用于套餐等复杂场景

### 功能增强
- **联动部位选择**：为单个项目添加提供更便捷的部位选择方式
- **键盘快捷操作**：提升操作效率
- **智能聚焦**：优化操作流程

## 键盘快捷键

- **Ctrl+Enter**：在快捷搜索框或部位选择框中快速添加项目
- **Tab**：在两个下拉框之间切换焦点
- **Esc**：清空当前选择

## 样式设计

- **网格布局**：快捷搜索框占 2/3 宽度，部位选择框占 1/3 宽度
- **响应式设计**：适配不同屏幕尺寸
- **一致性**：与现有界面风格保持完全一致
- **加载状态**：清晰的加载动画和状态提示

## 错误处理

- **API调用失败**：显示友好的错误提示
- **网络异常**：提供重试机制
- **数据格式异常**：容错处理，避免界面崩溃
- **用户操作验证**：实时验证和提示
- **状态保护**：在添加操作开始时保存选中状态，避免键盘事件导致状态清空

## 性能优化

- **复用现有API**：不增加额外的网络请求
- **防抖搜索**：减少不必要的API调用
- **智能排序**：部位按使用频次排序
- **状态管理**：及时清理状态，避免内存泄漏
- **聚焦管理**：优化用户操作流程
