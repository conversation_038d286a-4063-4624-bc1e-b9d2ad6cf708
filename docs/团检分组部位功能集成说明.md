# 团检分组部位功能集成说明

## 概述

为团检分组的关联项目添加了完整的部位逻辑支持，包括数据库表结构修改、后端实体类更新、前端组件功能增强，以及后端登记逻辑的完善。

## 已完成的功能模块

### 1. 数据库表结构修改

#### company_team_item_group表增强
- ✅ 添加了 `check_part_id` 字段（检查部位ID）
- ✅ 添加了 `check_part_name` 字段（检查部位名称）
- ✅ 添加了 `check_part_code` 字段（检查部位代码）
- ✅ 添加了复合索引 `idx_team_item_part`

**SQL脚本位置：** `docs/database/company_team_item_group_alter.sql`

### 2. 后端实体类和类型定义修改

#### CompanyTeamItemGroup实体类
- ✅ 添加了 `checkPartId` 字段
- ✅ 添加了 `checkPartName` 字段
- ✅ 添加了 `checkPartCode` 字段
- ✅ 添加了相应的注解和文档

#### 前端类型定义
- ✅ 更新了 `CompanyTeamItemGroup` 接口定义
- ✅ 添加了部位相关的可选字段

### 3. 后端登记逻辑增强

#### CustomerRegServiceImpl.getItemGroupByTeamId方法
- ✅ 在从团检分组创建个人登记项目时传递部位信息
- ✅ 设置 `checkPartId`、`checkPartName`、`checkPartCode` 字段
- ✅ 当有部位信息时，自动更新项目名称格式为 "项目名称-部位名称"

### 4. 前端组件功能增强

#### TeamGroupCard.vue组件
- ✅ 添加了部位选择状态管理
- ✅ 集成了部位选择模态框
- ✅ 实现了部位选择相关的所有方法
- ✅ 增强了项目添加逻辑以支持部位选择

#### 核心功能实现

**部位选择模态框：**
- 支持多选部位
- 支持关键字搜索
- 显示使用频次信息
- 完整的操作提示

**智能项目添加逻辑：**
- 自动识别需要部位选择的项目
- 对需要部位的项目打开部位选择器
- 支持套餐中预设部位信息的项目直接添加
- 完善的重复检查逻辑（考虑部位信息）

**项目显示增强：**
- 项目名称自动包含部位信息
- 智能的名称显示逻辑

### 5. 各种场景的部位处理

#### 手动添加项目
- ✅ 检测项目是否需要部位选择
- ✅ 自动打开部位选择器
- ✅ 支持多部位选择创建多条记录

#### 套餐添加项目
- ✅ 支持套餐中预设部位信息的项目直接添加
- ✅ 对需要部位但无预设信息的项目提示用户选择
- ✅ 智能处理混合情况

#### 必检项目添加
- ✅ 区分需要和不需要部位选择的必检项目
- ✅ 自动添加不需要部位的项目
- ✅ 提示用户为需要部位的项目选择部位

### 6. 重复检查逻辑优化

#### 智能重复检查
- ✅ 对不需要部位的项目：按项目ID检查重复
- ✅ 对需要部位的项目：按项目ID+部位ID组合检查重复
- ✅ 确保同一项目的不同部位可以共存

### 7. 表格操作优化

#### 行键管理
- ✅ 实现了复合行键：`itemGroupId` 或 `itemGroupId-checkPartId`
- ✅ 确保表格中每行都有唯一标识
- ✅ 支持同一项目的多个部位记录

#### 选择和删除操作
- ✅ 修改了行选择逻辑以支持复合行键
- ✅ 优化了批量删除功能
- ✅ 确保操作的准确性和一致性

## 技术实现细节

### 1. 部位选择流程
```javascript
// 1. 检测项目是否需要部位选择
if (group.hasCheckPart === '1') {
  showCheckPartSelector(group);
  return;
}

// 2. 加载部位选项
await loadCheckParts(itemGroup.id);

// 3. 用户选择部位后创建记录
checkPartState.selectedParts.forEach((partId) => {
  // 创建带部位信息的团检分组项目记录
});
```

### 2. 数据流转
```
团检分组项目 (company_team_item_group)
    ↓ (包含部位信息)
个人登记项目 (customer_reg_item_group)
    ↓ (部位信息传递)
体检流程各环节
```

### 3. 命名规则
- **不含部位的项目：** "胸部CT"
- **含部位的项目：** "胸部CT-胸部"
- **多部位项目：** 创建多条记录，每条记录对应一个部位

## API集成

### 1. 部位相关API
- `listByItemGroup` - 根据项目获取部位选项
- 支持关键字搜索和频次排序

### 2. 团检分组API
- `saveItemGroupOfTeam` - 保存团检分组项目（支持部位信息）
- `getItemGroupByTeam` - 获取团检分组项目（包含部位信息）

## 用户体验优化

### 1. 智能提示
- 自动识别需要部位选择的项目
- 提供清晰的操作指导
- 区分不同场景的处理方式

### 2. 操作流畅性
- 无需额外配置，自动检测部位需求
- 支持批量操作中的混合处理
- 保持原有操作习惯的同时增强功能

### 3. 错误处理
- 完善的异常处理机制
- 用户友好的错误提示
- 操作失败时的状态恢复

## 兼容性保证

### 1. 向后兼容
- ✅ 不影响现有不需要部位的项目
- ✅ 保持原有的操作流程
- ✅ 数据库字段为可选字段

### 2. 渐进式增强
- ✅ 现有项目继续正常工作
- ✅ 新项目可以选择是否启用部位功能
- ✅ 平滑的功能迁移

## 数据一致性

### 1. 数据传递链路
```
前端部位选择 → 团检分组表 → 个人登记表 → 体检流程
```

### 2. 字段映射
- `checkPartId` - 部位ID（关联check_part_dict表）
- `checkPartName` - 部位名称（用于显示）
- `checkPartCode` - 部位代码（用于接口对接）

## 总结

团检分组的部位功能集成已经完成，实现了：

1. **完整的数据支持** - 从数据库到前端的全链路部位信息支持
2. **智能的用户体验** - 自动识别和处理不同场景的部位需求
3. **灵活的操作方式** - 支持手动添加、套餐添加、必检项目等多种场景
4. **强大的兼容性** - 不影响现有功能，平滑支持新功能
5. **完善的错误处理** - 用户友好的提示和异常处理

所有功能都已经过测试和验证，可以正常使用。团检分组现在具备了与个人登记相同的部位选择能力。
