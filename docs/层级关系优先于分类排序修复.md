# 层级关系优先于分类排序修复

## 问题根源

您提出的问题非常准确！**"加项统一放在上面，减项统一放在下面"的逻辑确实会影响加项的层级展示。**

### 原始问题逻辑

```javascript
// 原始的分类优先逻辑
['added', 'normal', 'reduced'].forEach(category => {
  // 先处理加项分类，再处理正常项目分类，最后处理减项分类
  // 这导致分类排序覆盖了层级关系
});
```

### 问题场景分析

**胸部CT组（都是加项）：**
- 主项目：胸部CT (`addMinusFlag: 1`)
- 附属项目：颈部CT增强 × 2 (`addMinusFlag: 1`, `attachBaseId: 主项目ID`)

**问题表现：**
```
❌ 原始逻辑结果（分类优先）：
1. 胸部CT                    [加项，但失去主项目身份]
2. 颈部CT增强                [加项，但失去附属关系]
3. 颈部CT增强                [加项，但失去附属关系]
4. 头部CT                    [正常项目，主项目]
5. 材料费一                  [正常项目，附属项目]
```

**期望结果：**
```
✅ 修复后结果（层级优先）：
1. 胸部CT                    [加项，主项目]
2. ├─ 颈部CT增强             [加项，附属项目]
3. ├─ 颈部CT增强             [加项，附属项目]
4. 头部CT                    [正常项目，主项目]
5. ├─ 材料费一               [正常项目，附属项目]
```

## 修复方案

### 核心思路：层级关系优先于分类排序

1. **首先建立完整的层级结构**
2. **然后在层级结构内部进行分类排序**

### 新的处理逻辑

```javascript
function buildHierarchicalStructure(data) {
  // 1. 首先找出所有主项目（不分类别）
  const allMainItems = data.filter(item => {
    const sourceType = getItemSourceType(item.itemGroupId);
    return sourceType === 'main';
  });

  // 2. 按照加减项标志对主项目进行排序
  const sortedMainItems = allMainItems.sort((a, b) => {
    // 加项(1) -> 正常(0) -> 减项(-1)
    const getOrder = (flag) => {
      if (flag === 1) return 0;  // 加项最前
      if (flag === -1) return 2; // 减项最后
      return 1; // 正常项目在中间
    };
    return getOrder(a.addMinusFlag) - getOrder(b.addMinusFlag);
  });

  // 3. 为每个主项目添加其子项目（保持层级关系）
  sortedMainItems.forEach(mainItem => {
    // 添加主项目
    result.push(mainItem);
    
    // 查找并添加该主项目的所有子项目
    const childItems = findChildItemsFast(mainItem, data);
    childItems.forEach(childItem => {
      result.push(childItem); // 子项目紧跟主项目
    });
  });

  // 4. 处理孤立子项目（按分类排序）
  // ...
}
```

## 关键改进点

### 1. 主项目优先识别
```javascript
// 不再按分类查找主项目，而是在全部数据中查找
const allMainItems = data.filter(item => {
  const sourceType = getItemSourceType(item.itemGroupId);
  return sourceType === 'main';
});
```

### 2. 主项目分类排序
```javascript
// 对主项目按加减项标志排序，但保持其完整性
const sortedMainItems = allMainItems.sort((a, b) => {
  const getOrder = (flag) => {
    if (flag === 1) return 0;  // 加项主项目最前
    if (flag === -1) return 2; // 减项主项目最后
    return 1; // 正常主项目在中间
  };
  return getOrder(a.addMinusFlag) - getOrder(b.addMinusFlag);
});
```

### 3. 层级关系保持
```javascript
// 为每个主项目立即添加其子项目，保持层级关系
sortedMainItems.forEach(mainItem => {
  result.push(mainItem);           // 添加主项目
  
  const childItems = findChildItemsFast(mainItem, data);
  childItems.forEach(childItem => {
    result.push(childItem);        // 子项目紧跟主项目
  });
});
```

## 修复效果

### 加项层级关系
```
✅ 现在正确显示：
1. 胸部CT (加项主项目)
2. ├─ 颈部CT增强 (加项附属项目)
3. ├─ 颈部CT增强 (加项附属项目)
```

### 正常项目层级关系
```
✅ 继续正确显示：
4. 头部CT (正常主项目)
5. ├─ 材料费一 (正常附属项目)
6. ├─ 材料费一 (正常附属项目)
```

### 混合场景支持
```
✅ 支持复杂场景：
- 加项主项目 + 正常附属项目
- 正常主项目 + 加项附属项目
- 减项主项目 + 各种附属项目
```

## 技术优势

### 1. 逻辑清晰
- **第一优先级**：项目关系（主项目-子项目）
- **第二优先级**：项目状态（加项-正常-减项）

### 2. 用户体验
- 层级关系一目了然
- 分类排序依然有效
- 符合用户认知习惯

### 3. 扩展性强
- 支持任意复杂的层级关系
- 支持新的项目状态类型
- 易于维护和调试

## 调试信息

新增的详细调试信息帮助验证修复效果：

```javascript
console.log('找到的所有主项目:', allMainItems.map(item => 
  `${item.itemGroupName} (${item.id}) - 加减项标志: ${item.addMinusFlag}`
));

console.log('排序后的主项目:', sortedMainItems.map(item => 
  `${item.itemGroupName} (${item.id}) - 加减项标志: ${item.addMinusFlag}`
));

console.log('最终结果:', result.map((item, index) => 
  `${index + 1}. ${item.itemGroupName} (${item.id}) - 加减项: ${item.addMinusFlag}`
));
```

## 验证方法

### 1. 控制台调试
查看浏览器控制台的详细输出：
- 主项目识别是否正确
- 子项目查找是否成功
- 最终排序是否符合预期

### 2. 界面验证
检查项目列表显示：
- 加项主项目是否显示在顶部
- 加项附属项目是否显示前缀 `├─`
- 层级关系是否清晰

### 3. 功能测试
测试不同场景：
- 纯加项的层级关系
- 纯正常项目的层级关系
- 混合状态的层级关系

## 总结

这个修复解决了一个关键的架构问题：**分类排序与层级关系的优先级冲突**。

通过将层级关系提升为第一优先级，我们确保了：
1. **加项的层级关系能够正确显示**
2. **分类排序依然有效**（在层级关系内部）
3. **用户体验得到显著提升**

这是一个典型的"业务逻辑优先级"问题的解决方案，体现了良好的软件架构设计原则。
