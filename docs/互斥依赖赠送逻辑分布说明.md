# 互斥、依赖和赠送项目逻辑分布说明

## 概述

本文档说明了系统中互斥项目、依赖项目和赠送项目的逻辑分布和处理机制。

## 逻辑分布总览

| 功能类型 | 前端逻辑 | 后端逻辑 | 处理时机 | 状态 |
|----------|----------|----------|----------|------|
| **互斥检查** | ✅ 预检查（缓存） | ✅ 最终验证 | 添加项目前/后 | 启用 |
| **依赖检查** | ✅ 添加后提醒 + 结果录入验证 | ✅ 严格验证 | 添加项目时/后 + 保存结果前 | 启用 |
| **附属项目** | ❌ 无 | ✅ 自动获取添加 | 添加主项目后 | 启用 |
| **赠送项目** | ❌ 无 | ✅ 自动获取添加 | 添加主+附属项目后 | 启用 |

## 详细实现

### 1. ✅ 互斥项目检查（已启用）

#### 状态：双重保护机制
互斥检查是重要的业务逻辑，采用前端预检查 + 后端最终验证的双重保护机制。

#### 前端实现位置：
**文件**：`src/utils/itemGroupMutexCheck.js`
```javascript
export async function checkItemMutex(newItems, existingItems) {
    // 获取项目的互斥关系（带缓存）
    // 检查新项目与现有项目的冲突
    // 检查新项目之间的冲突
    // 返回冲突详情
}
```

#### 后端实现位置：
**文件**：`ItemGroupRelationServiceImpl.checkIsHaveMutexes()`
- 验证主项目互斥关系
- 验证附属项目互斥关系
- 验证赠送项目互斥关系

#### 调用时机：
- **前端**：添加项目前预检查
- **后端**：保存主项目、附属项目、赠送项目时分别验证

### 2. ✅ 依赖项目检查（全面启用）

#### 状态：全流程依赖验证

#### 前端实现位置：
**文件**：`src/utils/DependencyChecker.js`
```javascript
export class DependencyChecker {
  checkDependencies(groupId) {
    // 检查大项依赖和小项依赖
    // 验证依赖项目的完成状态
    // 返回缺失的依赖项目
  }
}
```

**调用位置1**：`src/views/reg/components/CustomerRegGroupPannel.vue` 和 `src/views/reg/GroupListOfPannel.vue`
```javascript
// 添加项目后的依赖检查（友好提醒）
async function checkDependenciesAfterAdd(addedItems) {
  const missingDependencies = [];
  for (const item of addedItems) {
    const validation = dependencyChecker.value.checkDependencies(item.itemGroupId);
    if (!validation.valid) {
      missingDependencies.push({
        itemName: item.itemGroupName,
        missing: validation.missing
      });
    }
  }
  // 显示警告提示，建议添加依赖项目
}
```

**调用位置2**：`src/views/station/ItemResultPannel.vue`
```javascript
// 保存结果前的依赖验证
async function validateDependencies() {
  // 显示依赖验证弹窗
}
```

#### 后端实现：
**状态**：已恢复（严格验证）
**文件**：`ItemGroupRelationServiceImpl.checkDependentGroups()`
- 验证主项目依赖关系
- 验证附属项目依赖关系
- 验证赠送项目依赖关系

#### 调用时机：
- **后端严格验证**：添加主项目、附属项目、赠送项目时分别验证
- **前端友好提醒**：添加项目成功后提醒用户注意依赖
- **前端结果验证**：保存结果前验证依赖完整性

### 3. ✅ 附属项目（完全启用）

#### 状态：后端自动处理

#### 后端实现位置：
**文件**：`ItemGroupRelationServiceImpl.java`
```java
@Override
public List<CustomerRegItemGroup> getAttachGroups(List<CustomerRegItemGroup> addingItemGroups) {
    List<CustomerRegItemGroup> attachItemGroups = new ArrayList<>();
    
    for (CustomerRegItemGroup addingGroup : addingItemGroups) {
        // 查询附属关系配置
        List<ItemGroupRelation> attachRelations = getAttachRelations(addingGroup.getItemGroupId());
        
        for (ItemGroupRelation relation : attachRelations) {
            // 检查部位匹配逻辑
            if (isPartMatched(addingGroup, relation)) {
                // 创建附属项目记录
                CustomerRegItemGroup attachGroup = createAttachGroup(addingGroup, relation);
                attachItemGroups.add(attachGroup);
            }
        }
    }
    
    return attachItemGroups;
}
```

#### 调用位置：
**文件**：`CustomerRegServiceImpl.addItemGroupWithCheckParts()`
```java
// 8. 获取附属项目
List<CustomerRegItemGroup> attachItemGroups = itemGroupRelationService.getAttachGroups(customerRegItemGroupList);
if (CollectionUtils.isNotEmpty(attachItemGroups)) {
    customerRegItemGroupList.addAll(attachItemGroups);
    customerRegItemGroupService.saveBatch(attachItemGroups);
}
```

#### 处理逻辑：
1. **查询配置**：根据主项目ID查询附属关系配置
2. **部位匹配**：检查主项目部位与配置的匹配规则
3. **自动创建**：为匹配的附属项目创建记录
4. **继承属性**：附属项目继承主项目的客户、价格等信息
5. **部位处理**：根据hasCheckPart属性决定是否继承部位

### 4. ✅ 赠送项目（完全启用）

#### 状态：后端自动处理

#### 后端实现位置：
**文件**：`ItemGroupRelationServiceImpl.java`
```java
@Override
public List<CustomerRegItemGroup> getGiftGroups(List<CustomerRegItemGroup> addingItemGroups) {
    List<CustomerRegItemGroup> giftItemGroups = new ArrayList<>();
    
    for (CustomerRegItemGroup addingGroup : addingItemGroups) {
        // 查询赠送关系配置
        List<ItemGroupRelation> giftRelations = getGiftRelations(addingGroup.getItemGroupId());
        
        for (ItemGroupRelation relation : giftRelations) {
            // 检查部位匹配逻辑
            if (isPartMatched(addingGroup, relation)) {
                // 创建赠送项目记录
                CustomerRegItemGroup giftGroup = createGiftGroup(addingGroup, relation);
                giftItemGroups.add(giftGroup);
            }
        }
    }
    
    return giftItemGroups;
}
```

#### 调用位置：
**文件**：`CustomerRegServiceImpl.addItemGroupWithCheckParts()`
```java
// 9. 获取赠送项目（需要考虑主项目和附属项目的赠送项目）
List<CustomerRegItemGroup> allItemGroups = new ArrayList<>(customerRegItemGroupList);
List<CustomerRegItemGroup> giftItemGroups = itemGroupRelationService.getGiftGroups(allItemGroups);
if (CollectionUtils.isNotEmpty(giftItemGroups)) {
    customerRegItemGroupList.addAll(giftItemGroups);
    customerRegItemGroupService.saveBatch(giftItemGroups);
}
```

#### 处理逻辑：
1. **基于全部项目**：考虑主项目和附属项目的赠送配置
2. **查询配置**：根据项目ID查询赠送关系配置
3. **部位匹配**：检查项目部位与配置的匹配规则
4. **自动创建**：为匹配的赠送项目创建记录
5. **标记赠送**：设置特殊标识表明这是赠送项目

## 处理流程

### 添加项目的完整流程：

```
1. 前端数据拼装
   ↓
2. 前端互斥预检查 (itemGroupMutexCheck.js)
   ↓
3. 后端互斥验证 (主项目)
   ↓
4. 🆕 后端依赖验证 (主项目)
   ↓
5. 后端保存主项目
   ↓
6. 后端获取附属项目 (getAttachGroups)
   ↓
7. 后端互斥验证 (附属项目)
   ↓
8. 🆕 后端依赖验证 (附属项目)
   ↓
9. 后端保存附属项目
   ↓
10. 后端获取赠送项目 (getGiftGroups) - 基于主项目+附属项目
    ↓
11. 后端互斥验证 (赠送项目)
    ↓
12. 🆕 后端依赖验证 (赠送项目)
    ↓
13. 后端保存赠送项目
    ↓
14. 🆕 前端依赖检查提醒 (checkDependenciesAfterAdd)
    ↓
15. 更新使用频次
    ↓
16. 返回成功结果
```

### 结果录入的依赖检查流程：

```
1. 用户录入结果
   ↓
2. 前端依赖检查 (DependencyChecker)
   ↓
3. 如果有缺失依赖 → 显示验证弹窗
   ↓
4. 用户选择：强制保存 或 取消
   ↓
5. 保存结果
```

## 配置管理

### 项目关系配置位置：
**前端页面**：`src/views/basicinfo/components/ItemGroupRelationForm.vue`
**后端表**：`item_group_relation`

### 配置类型：
- **附属项目**：`relationType = 'ATTACH'`
- **赠送项目**：`relationType = 'GIFT'`
- **互斥项目**：`relationType = 'EXCLUSIVE'` (已禁用)
- **依赖项目**：`relationType = 'DEPENDENT'` (仅结果录入时检查)

## 性能考虑

### 已优化的方面：
1. **移除互斥检查**：避免复杂的数据库查询
2. **移除依赖检查**：在添加项目时不检查依赖
3. **批量处理**：附属和赠送项目批量保存
4. **前端验证**：重复检查在前端完成

### 保留的功能：
1. **自动化处理**：附属和赠送项目自动添加
2. **部位逻辑**：正确处理部位继承和匹配
3. **结果依赖**：在关键的结果录入环节保留依赖检查

## 监控和维护

### 日志记录：
- 附属项目添加数量
- 赠送项目添加数量
- 依赖验证触发情况

### 数据统计：
```sql
-- 统计附属项目使用情况
SELECT 
    main_item_group_id,
    relation_group_id,
    COUNT(*) as usage_count
FROM customer_reg_item_group 
WHERE relation_type = 'ATTACH'
    AND create_time >= DATE_SUB(NOW(), INTERVAL 1 DAY)
GROUP BY main_item_group_id, relation_group_id;

-- 统计赠送项目使用情况
SELECT 
    main_item_group_id,
    relation_group_id,
    COUNT(*) as usage_count
FROM customer_reg_item_group 
WHERE relation_type = 'GIFT'
    AND create_time >= DATE_SUB(NOW(), INTERVAL 1 DAY)
GROUP BY main_item_group_id, relation_group_id;
```

## 总结

当前的设计实现了：

✅ **性能优化**：移除了耗时的互斥和依赖检查
✅ **自动化**：附属和赠送项目自动添加
✅ **关键验证**：在结果录入时保留依赖检查
✅ **用户体验**：快速的项目添加流程
✅ **数据完整性**：通过前端验证和数据库约束保证

这种设计在保证核心业务功能的同时，大幅提升了系统性能和用户体验。
