# 耗材判断逻辑和MySQL 5.7兼容性修正说明

## 修正内容

### 1. 耗材判断逻辑修正

#### 问题
之前错误地使用了 `type === '耗材'` 或 `feeType === '耗材'` 来判断耗材。

#### 正确的判断方式
耗材的正确判断应该是：`chargeItemOnlyFlag === '1'`

#### 修正前的错误代码
```javascript
// ❌ 错误的耗材判断
const isConsumable = checkPartState.currentItemGroup.type === '耗材' || 
                   checkPartState.currentItemGroup.feeType === '耗材';

if (!isConsumable) {
  // 重复检查逻辑
}
```

#### 修正后的正确代码
```javascript
// ✅ 使用现有的checkItemGroupDuplicate方法
// 该方法已经正确处理了耗材逻辑：chargeItemOnlyFlag === '1'
const tempItem = {
  ...checkPartState.currentItemGroup,
  checkPartId: partId,
  checkPartName: partName
};

const isDuplicate = checkItemGroupDuplicate(regGroupDataSource.value, tempItem);
if (isDuplicate) {
  message.warn(`${checkPartState.currentItemGroup.name} - ${partName} 已存在`);
  continue;
}
```

### 2. 现有checkItemGroupDuplicate方法分析

#### GroupListOfPannel.vue中的实现
```javascript
function checkItemGroupDuplicate(existingGroups: CustomerRegItemGroup[], newGroup: ItemGroup): boolean {
  // ✅ 正确的耗材判断
  if (newGroup.chargeItemOnlyFlag === '1') {
    console.log(`项目 ${newGroup.name} 是仅收费项目，允许重复添加`);
    return false; // 耗材允许重复
  }

  // 过滤出有效的现有项目（排除已减项和已退款的）
  const validExistingGroups = existingGroups.filter(item =>
    item.addMinusFlag !== -1 && item.payStatus !== '退款成功'
  );

  // 根据是否需要部位选择进行不同的判重逻辑
  if (newGroup.hasCheckPart !== '1') {
    // 不需要部位选择的项目
    return validExistingGroups.some(item => item.itemGroupId === newGroup.id);
  } else {
    // 需要部位选择的项目，检查项目-部位组合
    return validExistingGroups.some(item => 
      item.itemGroupId === newGroup.id && 
      item.checkPartId === newGroup.checkPartId
    );
  }
}
```

#### 优势
1. **正确的耗材判断**：使用 `chargeItemOnlyFlag === '1'`
2. **完整的业务逻辑**：考虑了部位选择、减项、退款等状态
3. **已经过验证**：在现有代码中广泛使用且稳定

### 3. MySQL 5.7兼容性问题

#### 问题
MySQL 5.7 不支持带 WHERE 条件的唯一约束：
```sql
-- ❌ MySQL 5.7 不支持
ALTER TABLE customer_reg_item_group 
ADD CONSTRAINT uk_customer_item_part_non_consumable 
UNIQUE (customer_reg_id, item_group_id, check_part_id)
WHERE (type IS NULL OR type != '耗材') 
    AND add_minus_flag != -1;
```

#### 解决方案

##### 方案A：使用触发器（推荐）
```sql
DELIMITER $$

CREATE TRIGGER tr_customer_reg_item_group_before_insert
BEFORE INSERT ON customer_reg_item_group
FOR EACH ROW
BEGIN
    DECLARE duplicate_count INT DEFAULT 0;
    
    -- 只对非耗材项目进行重复检查
    IF (NEW.charge_item_only_flag IS NULL OR NEW.charge_item_only_flag != '1') 
       AND NEW.add_minus_flag != -1 THEN
        
        SELECT COUNT(*) INTO duplicate_count
        FROM customer_reg_item_group
        WHERE customer_reg_id = NEW.customer_reg_id
          AND item_group_id = NEW.item_group_id
          AND (
              (check_part_id = NEW.check_part_id) OR 
              (check_part_id IS NULL AND NEW.check_part_id IS NULL)
          )
          AND add_minus_flag != -1
          AND (charge_item_only_flag IS NULL OR charge_item_only_flag != '1');
        
        IF duplicate_count > 0 THEN
            SIGNAL SQLSTATE '45000' 
            SET MESSAGE_TEXT = 'Duplicate non-consumable item already exists for this customer';
        END IF;
    END IF;
END$$

DELIMITER ;
```

##### 方案B：使用生成列（MySQL 5.7支持）
```sql
-- 添加生成列
ALTER TABLE customer_reg_item_group 
ADD COLUMN unique_key_non_consumable VARCHAR(255) GENERATED ALWAYS AS (
    CASE 
        WHEN charge_item_only_flag = '1' OR add_minus_flag = -1 THEN 
            CONCAT(id, '_', UNIX_TIMESTAMP(), '_', RAND()) -- 耗材和减项使用唯一值
        ELSE 
            CONCAT(customer_reg_id, '_', item_group_id, '_', COALESCE(check_part_id, 'NULL'))
    END
) STORED;

-- 为生成列添加唯一索引
CREATE UNIQUE INDEX uk_customer_item_non_consumable 
ON customer_reg_item_group (unique_key_non_consumable);
```

##### 方案C：应用层处理 + 索引优化（当前采用）
```sql
-- 创建索引提高查询性能
CREATE INDEX idx_customer_item_duplicate_check 
ON customer_reg_item_group (customer_reg_id, item_group_id, check_part_id, add_minus_flag, charge_item_only_flag);
```

### 4. 当前采用的方案

基于性能考虑和现有代码的稳定性，当前采用：

1. **前端验证**：使用现有的 `checkItemGroupDuplicate` 方法
2. **数据库索引**：添加复合索引提高查询性能
3. **错误处理**：当数据库插入失败时自动刷新数据

#### 优势
- **性能最优**：避免了后端验证的数据库查询开销
- **逻辑正确**：使用正确的耗材判断逻辑
- **兼容性好**：不依赖MySQL版本特性
- **稳定可靠**：基于现有的成熟代码

### 5. 修改的文件

#### 前端文件
- `src/views/reg/components/CustomerRegGroupPannel.vue`
  - 修正了耗材判断逻辑
  - 使用现有的 `checkItemGroupDuplicate` 方法

- `src/views/reg/GroupListOfPannel.vue`
  - 修正了耗材判断逻辑
  - 使用现有的 `checkItemGroupDuplicate` 方法

#### 数据库脚本
- `docs/database/mysql57_compatible_unique_constraint.sql`
  - 提供了MySQL 5.7兼容的约束方案
  - 包含触发器、生成列、索引等多种方案

### 6. 测试验证

#### 6.1 耗材重复添加测试
1. 选择一个 `chargeItemOnlyFlag === '1'` 的项目
2. 多次添加该项目
3. 验证可以成功重复添加

#### 6.2 非耗材重复检查测试
1. 选择一个 `chargeItemOnlyFlag !== '1'` 的项目
2. 尝试重复添加
3. 验证前端阻止重复添加

#### 6.3 部位组合测试
1. 选择需要部位选择的项目
2. 添加不同部位的组合
3. 验证相同部位不能重复，不同部位可以添加

### 7. 性能影响

#### 前端验证性能
- **优势**：基于内存中的数据，响应极快
- **数据量**：通常单个客户的项目数量有限，性能影响可忽略

#### 数据库索引性能
```sql
-- 建议的索引
CREATE INDEX idx_customer_item_duplicate_check 
ON customer_reg_item_group (customer_reg_id, item_group_id, check_part_id, add_minus_flag, charge_item_only_flag);
```

### 8. 监控建议

#### 8.1 数据质量监控
```sql
-- 监控可能的重复数据（非耗材）
SELECT 
    customer_reg_id,
    item_group_id,
    check_part_id,
    COUNT(*) as count
FROM customer_reg_item_group 
WHERE add_minus_flag != -1
    AND (charge_item_only_flag IS NULL OR charge_item_only_flag != '1')
GROUP BY customer_reg_id, item_group_id, check_part_id
HAVING COUNT(*) > 1;
```

#### 8.2 耗材使用统计
```sql
-- 统计耗材的使用情况
SELECT 
    item_group_id,
    item_group_name,
    COUNT(*) as usage_count,
    COUNT(DISTINCT customer_reg_id) as customer_count
FROM customer_reg_item_group 
WHERE charge_item_only_flag = '1'
    AND add_minus_flag != -1
    AND create_time >= DATE_SUB(NOW(), INTERVAL 1 DAY)
GROUP BY item_group_id, item_group_name
ORDER BY usage_count DESC;
```

## 总结

通过这次修正，我们实现了：

✅ **正确的耗材判断**：使用 `chargeItemOnlyFlag === '1'`
✅ **复用现有逻辑**：使用经过验证的 `checkItemGroupDuplicate` 方法
✅ **MySQL 5.7兼容**：提供了多种兼容性方案
✅ **性能优化**：保持了高频操作的性能优势
✅ **数据安全**：通过前端验证和错误处理保证数据质量

这个方案既修正了之前的错误，又保持了系统的稳定性和性能。
