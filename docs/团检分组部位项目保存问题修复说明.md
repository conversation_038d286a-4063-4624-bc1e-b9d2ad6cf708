# 团检分组部位项目保存问题修复说明

## 问题描述

在团检分组中添加需要配置部位的项目时，如果保存过程中出现错误，会导致：
1. 之前配置好的项目被删除
2. 新添加的项目保存失败
3. 数据库中该团检分组的所有项目记录丢失

## 问题原因分析

### 根本原因
后端的 `saveItemGroupOfTeam` 方法采用了"先删除再插入"的策略，但缺乏事务保护：

```java
// 原有代码问题
public void saveItemGroupOfTeam(String teamId, List<CompanyTeamItemGroup> itemGroupList) {
    // 1. 先删除所有记录
    jdbcTemplate.update("delete from company_team_item_group where team_id = ?", teamId);
    
    // 2. 再插入新记录（如果这里失败，数据就丢失了）
    companyTeamItemGroupService.saveOrUpdateBatch(itemGroupList);
}
```

### 触发条件
1. 添加需要部位配置的项目
2. 保存过程中发生任何异常（数据验证失败、网络问题、数据库约束冲突等）
3. 前端没有适当的错误处理机制

### 影响范围
- 团检分组项目配置功能
- 所有涉及部位选择的项目添加操作

## 解决方案

### 1. 后端修复

#### 添加事务保护
在 `CompanyRegServiceImpl.saveItemGroupOfTeam` 方法上添加 `@Transactional` 注解，确保操作的原子性：

```java
@Override
@Transactional(rollbackFor = Exception.class)
public void saveItemGroupOfTeam(String teamId, List<CompanyTeamItemGroup> itemGroupList) {
    try {
        // 数据验证
        if (itemGroupList != null) {
            for (CompanyTeamItemGroup item : itemGroupList) {
                if (item.getItemGroupId() == null || item.getItemGroupId().trim().isEmpty()) {
                    throw new RuntimeException("项目组ID不能为空");
                }
                if (item.getTeamId() == null || !item.getTeamId().equals(teamId)) {
                    item.setTeamId(teamId);
                }
            }
        }
        
        // 在事务中先删除再插入
        jdbcTemplate.update("delete from company_team_item_group where team_id = ?", teamId);
        
        if (itemGroupList != null && !itemGroupList.isEmpty()) {
            companyTeamItemGroupService.saveOrUpdateBatch(itemGroupList);
        }
        
        // 其他业务逻辑...
        
    } catch (Exception e) {
        log.error("保存团检分组项目失败，teamId: {}, 错误信息: {}", teamId, e.getMessage(), e);
        throw new RuntimeException("保存团检分组项目失败: " + e.getMessage(), e);
    }
}
```

#### 添加数据验证
- 验证项目组ID不能为空
- 确保teamId一致性
- 添加详细的错误日志

### 2. 前端修复

#### 改进错误处理
在 `TeamGroupCard.vue` 中添加完善的错误处理机制：

```javascript
// 同步版本 - 用于一般操作
function updateTeamGroup() {
  saveItemGroupOfTeam({ teamId: mainTeamId.value, groupList: dataSource.value })
    .then((res) => {
      if (res.success) {
        message.success('操作成功');
      } else {
        message.error('保存失败：' + (res.message || '未知错误'));
        fetchData(); // 重新加载数据恢复状态
      }
    })
    .catch((error) => {
      console.error('保存团检分组项目失败:', error);
      message.error('保存失败：' + (error.message || '网络错误'));
      fetchData(); // 重新加载数据恢复状态
    });
}

// 异步版本 - 用于需要等待结果的场景
async function updateTeamGroupAsync() {
  try {
    const res = await saveItemGroupOfTeam({ teamId: mainTeamId.value, groupList: dataSource.value });
    if (res.success) {
      message.success('操作成功');
      return true;
    } else {
      throw new Error(res.message || '保存失败');
    }
  } catch (error) {
    console.error('保存团检分组项目失败:', error);
    throw error;
  }
}
```

#### 部位选择错误回滚
在部位选择确认时，如果保存失败，自动移除刚添加的项目：

```javascript
async function confirmAddItemWithParts() {
  checkPartState.loading = true;
  try {
    // 添加项目到dataSource
    // ...
    
    // 尝试保存
    await updateTeamGroupAsync();
    closeCheckPartSelector();
  } catch (error) {
    console.error('Add item with parts error:', error);
    message.error('添加失败: ' + (error.message || '未知错误'));
    
    // 保存失败，移除刚添加的项目
    checkPartState.selectedParts.forEach((partId) => {
      const index = dataSource.value.findIndex((row) =>
        row.itemGroupId === checkPartState.currentItemGroup.id &&
        row.checkPartId === partId
      );
      if (index !== -1) {
        dataSource.value.splice(index, 1);
      }
    });
  } finally {
    checkPartState.loading = false;
  }
}
```

## 修复效果

### 1. 数据安全性
- ✅ 事务保护确保要么全部成功，要么全部回滚
- ✅ 不会再出现部分数据丢失的情况
- ✅ 添加了详细的错误日志便于问题排查

### 2. 用户体验
- ✅ 保存失败时显示明确的错误信息
- ✅ 自动恢复到操作前的状态
- ✅ 避免用户困惑和数据丢失

### 3. 系统稳定性
- ✅ 增强了异常处理能力
- ✅ 提高了数据一致性
- ✅ 减少了因操作失败导致的数据不一致问题

## 测试建议

### 1. 正常流程测试
- 添加普通项目（无部位要求）
- 添加需要部位选择的项目
- 批量添加混合项目

### 2. 异常情况测试
- 网络中断时的保存操作
- 数据库连接异常时的保存操作
- 并发操作时的数据一致性

### 3. 回滚测试
- 验证保存失败时数据是否正确回滚
- 验证前端状态是否正确恢复

## 相关文件

### 后端文件
- `jeecg-module-physicalex/src/main/java/org/jeecg/modules/reg/service/impl/CompanyRegServiceImpl.java`

### 前端文件
- `src/views/reg/components/TeamGroupCard.vue`

### 文档文件
- `docs/团检分组部位项目保存问题修复说明.md`

## 注意事项

1. **事务边界**：确保所有相关操作都在同一个事务中
2. **错误信息**：提供用户友好的错误提示
3. **数据恢复**：保存失败时自动恢复到操作前状态
4. **日志记录**：记录详细的操作日志便于问题排查

## 后续优化建议

1. **乐观锁**：考虑添加版本控制避免并发冲突
2. **批量验证**：在保存前进行完整的数据验证
3. **操作确认**：对于重要操作添加二次确认机制
4. **数据备份**：考虑在重要操作前自动备份数据
