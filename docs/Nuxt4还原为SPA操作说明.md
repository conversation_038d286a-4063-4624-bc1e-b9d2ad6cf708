# Nuxt4 还原为 SPA 操作说明

## 概述

本文档记录了将项目从 Nuxt4 配置完全还原为纯 SPA（Single Page Application）项目的详细操作过程。

## 还原前项目状态

项目原本已经集成了 Nuxt4 配置，包含以下 Nuxt 相关文件和配置：

- `nuxt.config.ts` - Nuxt 配置文件
- `app/` 目录 - Nuxt 应用目录
  - `app/app.vue` - Nuxt 应用根组件
  - `app/app.html` - Nuxt HTML 模板
  - `app/router.options.ts` - Nuxt 路由配置
  - `app/plugins/` - Nuxt 插件目录
    - `register-app.client.ts` - 应用注册插件
    - `svg-icons.client.ts` - SVG 图标插件
- `package.json` 中的 Nuxt 相关依赖和脚本

## 还原操作步骤

### 1. 删除 Nuxt4 相关文件

删除了以下文件和目录：

```bash
# 删除 Nuxt 配置文件
nuxt.config.ts

# 删除整个 app 目录及其所有内容
app/
├── app.vue
├── app.html  
├── router.options.ts
└── plugins/
    ├── register-app.client.ts
    └── svg-icons.client.ts
```

### 2. 更新 package.json

#### 修改 npm scripts

将 Nuxt 相关的脚本命令还原为 Vite 命令：

**修改前：**
```json
{
  "scripts": {
    "dev": "nuxt dev --port 5173",
    "build": "nuxt build", 
    "preview": "nuxt preview --port 5173",
    "vite:dev": "vite",
    "vite:build": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=8192 vite build && esno ./build/script/postBuild.ts"
  }
}
```

**修改后：**
```json
{
  "scripts": {
    "dev": "vite",
    "build": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=8192 vite build && esno ./build/script/postBuild.ts",
    "preview": "vite preview --port 5173"
  }
}
```

#### 移除 Nuxt 相关依赖

从 `devDependencies` 中移除：
- `nuxt: ^4.0.3`
- `@unocss/nuxt: ^0.58.9`

### 3. 还原 vite.config.ts

使用项目中的备份配置文件 `vite.config-backup.ts` 的内容替换当前的 `vite.config.ts`，主要差异包括：

- 恢复了更详细的 `manualChunks` 配置，优化代码分割
- 恢复了 `optimizeDeps` 的完整配置，包括预构建依赖列表
- 恢复了 `esbuild` 的稳定性配置
- 将 `treeshake` 设置为 `false` 以防止删除重要代码

### 4. 验证入口文件配置

确认以下文件配置正确：

#### index.html
- 包含正确的 `<div id="app">` 挂载点
- 包含 `<script type="module" src="/src/main.ts"></script>` 入口脚本
- 保留了项目所需的第三方脚本（ActiveReports、身份证读卡、Lodop等）

#### src/main.ts  
- 作为 SPA 应用的入口文件
- 包含完整的应用初始化逻辑
- 不需要修改，已经是纯 SPA 配置

### 5. 更新项目文档

更新 `README.md` 文件：
- 移除了 Nuxt4 相关的说明章节
- 恢复了 SPA 项目的运行说明
- 更新了项目特性描述

## 还原后项目状态

还原完成后，项目回到纯 SPA 状态：

### 项目结构
```
├── src/                 # 源代码目录
│   ├── main.ts         # SPA 应用入口
│   ├── App.vue         # 根组件
│   └── ...
├── index.html          # HTML 模板
├── vite.config.ts      # Vite 配置
├── package.json        # 项目配置
└── ...
```

### 运行命令
```bash
# 安装依赖
pnpm install

# 开发启动
npm run dev

# 生产构建  
npm run build

# 预览构建结果
npm run preview
```

## 验证还原结果

1. **依赖安装**：运行 `pnpm install` 确保所有依赖正确安装
2. **开发启动**：运行 `npm run dev` 确保项目能正常启动
3. **功能测试**：验证主要功能模块是否正常工作
4. **构建测试**：运行 `npm run build` 确保能正常构建

## 注意事项

1. **缓存清理**：建议删除 `node_modules` 和 `pnpm-lock.yaml` 后重新安装依赖
2. **浏览器缓存**：清理浏览器缓存以避免旧配置影响
3. **环境变量**：确保 `.env` 文件中的配置适用于 Vite 构建
4. **热更新**：项目支持 Vite 的热更新功能，修改代码后会自动刷新

## 总结

通过以上步骤，成功将项目从 Nuxt4 配置还原为纯 SPA 项目。项目现在使用 Vite 作为构建工具，保持了原有的功能特性，同时去除了 Nuxt 的复杂性，更适合纯前端 SPA 应用的开发和部署。
