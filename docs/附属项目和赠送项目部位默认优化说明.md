# 附属项目和赠送项目部位自动同步优化说明

## 概述

本次优化主要针对附属项目配置和赠送项目配置进行用户体验改进，实现了**选中主项目部位后自动给附属项目和赠送项目的部位赋值**的功能，大大减少用户的重复操作。

## 优化内容

### 1. 🎯 核心功能：部位自动同步

#### 1.1 选择主项目部位时自动同步
- **附属项目**：当用户选择主项目部位时，自动将该部位同步到附属项目部位
- **赠送项目**：当用户选择主项目部位时，自动将该部位同步到赠送项目部位
- **即时反馈**：同步完成后显示成功提示消息
- **清空同步**：清空主项目部位时，也自动清空对应的关联项目部位

#### 1.2 智能默认部位设置
- 新增 `loadMainProjectCommonParts()` 方法，通过 `listByItemGroup` API 获取主项目的常用部位
- 部位按使用频次排序，优先使用最常用的部位作为默认值
- 新增 `getMainProjectCommonPart()` 方法，返回主项目最常用的部位

#### 1.3 添加项目时的智能默认
- **添加附属项目**：`addAttachedItem()` 方法自动设置主项目部位和附属项目部位为常用部位
- **添加赠送项目**：`addGiftItem()` 方法自动设置主项目部位和赠送项目部位为常用部位
- **初始化时**：如果没有已保存的配置，默认项也会使用常用部位
- **重置时**：`handleReset()` 方法重置后的默认项使用常用部位

### 2. 批量设置功能

#### 2.1 附属项目批量设置
- 在附属项目配置卡片标题栏添加"批量设置"下拉菜单
- **🆕 同步主项目部位到附属部位**：一键将所有主项目部位同步到对应的附属项目部位
- 支持一键清空所有附属项目的部位设置
- 支持一键将所有附属项目设置为指定部位（显示前10个常用部位）
- 同时设置主项目部位和附属项目部位，保持一致性

#### 2.2 赠送项目批量设置
- 在赠送项目配置卡片标题栏添加"批量设置"下拉菜单
- **🆕 同步主项目部位到赠送部位**：一键将所有主项目部位同步到对应的赠送项目部位
- 支持一键清空所有赠送项目的部位设置
- 支持一键将所有赠送项目设置为指定部位（显示前10个常用部位）
- 同时设置主项目部位和赠送项目部位，保持一致性

### 3. 界面优化

#### 3.1 卡片标题栏增强
```vue
<template #title>
  <div style="display: flex; justify-content: space-between; align-items: center;">
    <span>附属项目配置</span>
    <a-dropdown v-if="checkPartList.length > 0">
      <a-button size="small" type="link">
        <template #icon><SettingOutlined /></template>
        批量设置
      </a-button>
      <template #overlay>
        <a-menu @click="handleBatchSetAttachPart">
          <a-menu-item key="clear">清空所有部位</a-menu-item>
          <a-menu-divider />
          <a-menu-item v-for="part in checkPartList.slice(0, 10)" :key="part.id">
            设为：{{ part.name }}
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </div>
</template>
```

#### 3.2 用户反馈
- 批量设置操作后显示成功提示消息
- 明确告知用户设置的结果（如"已将所有附属项目部位设为：胸部"）

## 技术实现

### 1. 🎯 核心功能：部位自动同步

#### 1.1 附属项目主部位变更处理
```javascript
const onMainPartChange = (index, partId) => {
  const item = relationData.attachGroups[index];
  if (partId) {
    const part = checkPartList.value.find(p => p.id === partId);
    if (part) {
      item.mainCheckPartName = part.name;
      item.mainCheckPartCode = part.code;

      // 🎯 核心功能：自动同步到附属项目部位
      item.relationCheckPartId = part.id;
      item.relationCheckPartName = part.name;
      item.relationCheckPartCode = part.code;

      message.success(`已自动将附属项目部位设为：${part.name}`);
    }
  } else {
    // 清空主项目部位时，也清空附属项目部位
    item.mainCheckPartName = null;
    item.mainCheckPartCode = null;
    item.relationCheckPartId = null;
    item.relationCheckPartName = null;
    item.relationCheckPartCode = null;
  }
};
```

#### 1.2 赠送项目主部位变更处理
```javascript
const onGiftMainPartChange = (index, partId) => {
  const item = relationData.giftGroups[index];
  if (partId) {
    const part = checkPartList.value.find(p => p.id === partId);
    if (part) {
      item.mainCheckPartName = part.name;
      item.mainCheckPartCode = part.code;

      // 🎯 核心功能：自动同步到赠送项目部位
      item.relationCheckPartId = part.id;
      item.relationCheckPartName = part.name;
      item.relationCheckPartCode = part.code;

      message.success(`已自动将赠送项目部位设为：${part.name}`);
    }
  } else {
    // 清空主项目部位时，也清空赠送项目部位
    item.mainCheckPartName = null;
    item.mainCheckPartCode = null;
    item.relationCheckPartId = null;
    item.relationCheckPartName = null;
    item.relationCheckPartCode = null;
  }
};
```

### 2. 数据结构

#### 1.1 主项目常用部位
```javascript
// 主项目常用部位（根据使用频次排序）
const mainProjectCommonParts = ref([]);

// 获取主项目最常用的部位
const getMainProjectCommonPart = () => {
  return mainProjectCommonParts.value.length > 0 ? mainProjectCommonParts.value[0] : null;
};
```

#### 1.2 默认项目结构
```javascript
const createDefaultItem = (commonPart) => {
  const item = {
    relationGroupId: null,
    quantity: 1,
    mainCheckPartId: null,
    mainCheckPartName: null,
    mainCheckPartCode: null,
    relationCheckPartId: null,
    relationCheckPartName: null,
    relationCheckPartCode: null
  };
  
  if (commonPart) {
    item.mainCheckPartId = commonPart.id;
    item.mainCheckPartName = commonPart.name;
    item.mainCheckPartCode = commonPart.code;
    item.relationCheckPartId = commonPart.id;
    item.relationCheckPartName = commonPart.name;
    item.relationCheckPartCode = commonPart.code;
  }
  
  return item;
};
```

### 2. API调用

#### 2.1 获取主项目常用部位
```javascript
const loadMainProjectCommonParts = async () => {
  if (!props.itemGroupId) return;
  
  try {
    const res = await listByItemGroup({ itemGroupId: props.itemGroupId });
    if (Array.isArray(res)) {
      mainProjectCommonParts.value = res;
    }
  } catch (error) {
    console.error('加载主项目常用部位失败:', error);
    mainProjectCommonParts.value = [];
  }
};
```

### 3. 批量操作

#### 3.1 批量设置附属项目部位（含同步功能）
```javascript
const handleBatchSetAttachPart = ({ key }) => {
  if (key === 'sync') {
    // 🆕 同步主项目部位到附属部位
    let syncCount = 0;
    relationData.attachGroups.forEach(item => {
      if (item.mainCheckPartId && item.mainCheckPartName) {
        item.relationCheckPartId = item.mainCheckPartId;
        item.relationCheckPartName = item.mainCheckPartName;
        item.relationCheckPartCode = item.mainCheckPartCode;
        syncCount++;
      }
    });
    if (syncCount > 0) {
      message.success(`已同步${syncCount}个附属项目的部位`);
    } else {
      message.warning('没有可同步的主项目部位');
    }
  } else if (key === 'clear') {
    // 清空所有部位
    relationData.attachGroups.forEach(item => {
      item.mainCheckPartId = null;
      item.mainCheckPartName = null;
      item.mainCheckPartCode = null;
      item.relationCheckPartId = null;
      item.relationCheckPartName = null;
      item.relationCheckPartCode = null;
    });
    message.success('已清空所有附属项目部位');
  } else {
    // 设置为指定部位
    const part = checkPartList.value.find(p => p.id === key);
    if (part) {
      relationData.attachGroups.forEach(item => {
        item.mainCheckPartId = part.id;
        item.mainCheckPartName = part.name;
        item.mainCheckPartCode = part.code;
        item.relationCheckPartId = part.id;
        item.relationCheckPartName = part.name;
        item.relationCheckPartCode = part.code;
      });
      message.success(`已将所有附属项目部位设为：${part.name}`);
    }
  }
};
```

## 用户体验改进

### 1. 🎯 核心改进：自动同步部位
- **之前**：用户需要为每个附属/赠送项目分别选择主项目部位和关联项目部位，操作繁琐且容易出错
- **现在**：**选择主项目部位后，系统自动将关联项目部位设为相同部位**，一步操作完成两个设置

### 2. 减少重复操作
- **智能默认**：系统自动使用主项目的常用部位作为默认值
- **即时同步**：选择主项目部位时立即同步到关联项目部位
- **批量同步**：支持一键同步所有已设置的主项目部位

### 2. 智能化配置
- **常用部位优先**：系统根据主项目的历史使用数据，自动选择最常用的部位
- **一致性保证**：默认情况下主项目部位和关联项目部位保持一致，符合大多数使用场景

### 3. 批量操作支持
- **快速设置**：支持一键将所有项目设置为相同部位
- **快速清空**：支持一键清空所有部位设置
- **常用部位快选**：下拉菜单显示前10个常用部位，方便快速选择

### 4. 向后兼容
- **保持原有功能**：所有原有的手动选择功能都保持不变
- **渐进增强**：新功能作为辅助功能，不影响现有工作流程
- **数据完整性**：不影响已保存的配置数据

## 使用场景

### 1. 🎯 核心场景：选择主项目部位自动同步
1. 打开项目关系配置页面
2. 添加附属项目或赠送项目
3. **选择主项目部位**（如：胸部）
4. **系统自动将附属/赠送项目部位设为相同部位**（胸部）
5. 显示成功提示："已自动将附属项目部位设为：胸部"
6. 用户无需手动设置关联项目部位，大大减少操作步骤

### 2. 批量同步已有配置
1. 已有多个附属项目，但部位不一致
2. 分别设置各项目的主项目部位
3. 点击"批量设置" → "同步主项目部位到附属部位"
4. 系统自动将所有主项目部位同步到对应的附属项目部位

### 3. 批量配置相同部位
1. 添加多个附属项目或赠送项目
2. 点击卡片标题栏的"批量设置"按钮
3. 选择目标部位，系统自动应用到所有项目
4. 确认配置并保存

### 4. 清空重新配置
1. 点击"批量设置" → "清空所有部位"
2. 系统清空所有部位设置
3. 用户可重新进行个性化配置

## 注意事项

1. **API依赖**：功能依赖 `listByItemGroup` API 返回按使用频次排序的部位数据
2. **性能考虑**：常用部位数据在组件初始化时加载，避免重复请求
3. **错误处理**：如果获取常用部位失败，不影响基本功能，只是不提供默认值
4. **数据一致性**：批量设置时同时更新主项目部位和关联项目部位，保持数据一致性

## 相关文件

- **主要文件**: `src/views/basicinfo/components/ItemGroupRelationForm.vue`
- **API文件**: `src/views/basicinfo/CheckPartDict.api.ts`
- **图标依赖**: `@ant-design/icons-vue` (新增 `SettingOutlined`)

## 后续优化建议

1. **记忆用户偏好**：记录用户的部位选择偏好，提供个性化默认值
2. **智能推荐**：根据项目类型智能推荐合适的部位组合
3. **模板功能**：提供常用的部位配置模板，支持一键应用
4. **统计分析**：提供部位使用统计，帮助优化默认设置
