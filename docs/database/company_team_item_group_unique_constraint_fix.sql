-- 修复company_team_item_group表的唯一约束问题
-- 问题：原有的unique_reg_team_item约束只包含(company_reg_id, team_id, item_group_id)
-- 但支持部位后，同一项目可能有多个部位，需要将check_part_id也包含在约束中

-- 1. 首先查看当前的约束情况
SELECT 
    CONSTRAINT_NAME,
    COLUMN_NAME,
    ORDINAL_POSITION
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'company_team_item_group'
  AND CONSTRAINT_NAME = 'unique_reg_team_item'
ORDER BY ORDINAL_POSITION;

-- 2. 检查是否存在重复数据（在修改约束前需要清理）
SELECT 
    company_reg_id,
    team_id, 
    item_group_id,
    check_part_id,
    COUNT(*) as count
FROM company_team_item_group 
GROUP BY company_reg_id, team_id, item_group_id, check_part_id
HAVING COUNT(*) > 1;

-- 3. 如果存在重复数据，需要先清理（保留最新的记录）
-- 注意：执行前请备份数据！
/*
DELETE t1 FROM company_team_item_group t1
INNER JOIN company_team_item_group t2 
WHERE t1.company_reg_id = t2.company_reg_id
  AND t1.team_id = t2.team_id
  AND t1.item_group_id = t2.item_group_id
  AND (t1.check_part_id = t2.check_part_id OR (t1.check_part_id IS NULL AND t2.check_part_id IS NULL))
  AND t1.id < t2.id;  -- 保留ID较大的（通常是较新的）记录
*/

-- 4. 删除原有的唯一约束（重要：必须删除，否则会与新约束冲突）
ALTER TABLE `company_team_item_group`
DROP INDEX IF EXISTS `unique_reg_team_item`;

-- 验证旧约束已删除
SELECT
    CONSTRAINT_NAME,
    COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'company_team_item_group'
  AND CONSTRAINT_NAME = 'unique_reg_team_item';
-- 上述查询应该返回空结果

-- 5. 创建新的唯一约束，包含部位信息
-- 方案A：包含check_part_id的约束（推荐）
ALTER TABLE `company_team_item_group` 
ADD UNIQUE KEY `unique_reg_team_item_part` (`company_reg_id`, `team_id`, `item_group_id`, `check_part_id`);

-- 6. 验证新约束
SELECT 
    CONSTRAINT_NAME,
    COLUMN_NAME,
    ORDINAL_POSITION
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'company_team_item_group'
  AND CONSTRAINT_NAME = 'unique_reg_team_item_part'
ORDER BY ORDINAL_POSITION;

-- 7. 测试约束是否生效
-- 以下语句应该成功（不同部位）
/*
INSERT INTO company_team_item_group 
(company_reg_id, team_id, item_group_id, item_group_name, check_part_id, check_part_name, price, dis_rate, price_after_dis, payer_type, add_minus_flag)
VALUES 
('test_company_1', 'test_team_1', 'test_item_1', '测试项目-左侧', 'part_1', '左侧', 100.00, 1.0, 100.00, '个人', 0),
('test_company_1', 'test_team_1', 'test_item_1', '测试项目-右侧', 'part_2', '右侧', 100.00, 1.0, 100.00, '个人', 0);
*/

-- 以下语句应该失败（相同的项目-部位组合）
/*
INSERT INTO company_team_item_group 
(company_reg_id, team_id, item_group_id, item_group_name, check_part_id, check_part_name, price, dis_rate, price_after_dis, payer_type, add_minus_flag)
VALUES 
('test_company_1', 'test_team_1', 'test_item_1', '测试项目-左侧重复', 'part_1', '左侧', 100.00, 1.0, 100.00, '个人', 0);
*/

-- 8. 清理测试数据
/*
DELETE FROM company_team_item_group 
WHERE company_reg_id = 'test_company_1' 
  AND team_id = 'test_team_1' 
  AND item_group_id = 'test_item_1';
*/

-- 9. 创建相关索引以提升查询性能
CREATE INDEX IF NOT EXISTS `idx_company_team_item_lookup` 
ON `company_team_item_group` (`company_reg_id`, `team_id`, `item_group_id`);

CREATE INDEX IF NOT EXISTS `idx_team_item_part_lookup` 
ON `company_team_item_group` (`team_id`, `item_group_id`, `check_part_id`);

-- 10. 显示最终的表结构
SHOW CREATE TABLE `company_team_item_group`;

-- 11. 验证数据完整性
SELECT 
    '=== 数据完整性检查 ===' as step,
    COUNT(*) as total_records,
    COUNT(DISTINCT CONCAT(company_reg_id, '-', team_id, '-', item_group_id, '-', COALESCE(check_part_id, 'NULL'))) as unique_combinations
FROM `company_team_item_group`;

-- 12. 显示部位相关的统计信息
SELECT 
    '=== 部位统计信息 ===' as step,
    COUNT(*) as total_records,
    SUM(CASE WHEN check_part_id IS NOT NULL THEN 1 ELSE 0 END) as with_part,
    SUM(CASE WHEN check_part_id IS NULL THEN 1 ELSE 0 END) as without_part
FROM `company_team_item_group`;

-- 使用说明：
-- 1. 执行前请备份company_team_item_group表数据
-- 2. 如果第2步查询发现重复数据，需要先执行第3步的清理语句
-- 3. 新约束允许同一项目的不同部位，但不允许相同的项目-部位组合重复
-- 4. NULL部位被视为一个特殊值，同一项目只能有一个NULL部位记录

-- 注意事项：
-- 1. 此修改会影响所有依赖该约束的应用逻辑
-- 2. 前端代码已经更新了重复检查逻辑以配合新约束
-- 3. 建议在测试环境先验证，确认无问题后再在生产环境执行
-- 4. 如果有大量数据，索引创建可能需要较长时间

-- 回滚方案（如果需要）：
-- ALTER TABLE `company_team_item_group` DROP INDEX `unique_reg_team_item_part`;
-- ALTER TABLE `company_team_item_group` ADD UNIQUE KEY `unique_reg_team_item` (`company_reg_id`, `team_id`, `item_group_id`);
