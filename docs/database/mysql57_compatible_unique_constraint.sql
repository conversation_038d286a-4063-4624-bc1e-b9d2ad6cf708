-- MySQL 5.7 兼容的customer_reg_item_group表唯一约束方案
-- MySQL 5.7 不支持带WHERE条件的唯一约束，需要使用其他方案

-- 1. 首先查看当前数据中耗材的标识
SELECT 
    charge_item_only_flag,
    COUNT(*) as count,
    COUNT(DISTINCT item_group_id) as distinct_items
FROM customer_reg_item_group 
WHERE add_minus_flag != -1
GROUP BY charge_item_only_flag
ORDER BY count DESC;

-- 2. 查看是否存在重复的非耗材项目
SELECT 
    customer_reg_id, 
    item_group_id, 
    COALESCE(check_part_id, 'NULL') as check_part_id,
    charge_item_only_flag,
    COUNT(*) as count
FROM customer_reg_item_group 
WHERE add_minus_flag != -1 
    AND (charge_item_only_flag IS NULL OR charge_item_only_flag != '1')
GROUP BY customer_reg_id, item_group_id, check_part_id, charge_item_only_flag
HAVING COUNT(*) > 1;

-- 3. 方案A：使用触发器实现约束（推荐）
DELIMITER $$

CREATE TRIGGER tr_customer_reg_item_group_duplicate_check
BEFORE INSERT ON customer_reg_item_group
FOR EACH ROW
BEGIN
    DECLARE duplicate_count INT DEFAULT 0;
    
    -- 只对非耗材项目进行重复检查
    IF (NEW.charge_item_only_flag IS NULL OR NEW.charge_item_only_flag != '1') 
       AND NEW.add_minus_flag != -1 THEN
        
        SELECT COUNT(*) INTO duplicate_count
        FROM customer_reg_item_group
        WHERE customer_reg_id = NEW.customer_reg_id
          AND item_group_id = NEW.item_group_id
          AND (check_part_id = NEW.check_part_id OR (check_part_id IS NULL AND NEW.check_part_id IS NULL))
          AND add_minus_flag != -1
          AND (charge_item_only_flag IS NULL OR charge_item_only_flag != '1');
        
        IF duplicate_count > 0 THEN
            SIGNAL SQLSTATE '45000' 
            SET MESSAGE_TEXT = 'Duplicate item group for customer (non-consumable items only)';
        END IF;
    END IF;
END$$

DELIMITER ;

-- 4. 方案B：使用函数索引（如果支持）
-- MySQL 5.7 支持生成列，可以创建一个计算列用于唯一约束
ALTER TABLE customer_reg_item_group 
ADD COLUMN unique_key_non_consumable VARCHAR(255) GENERATED ALWAYS AS (
    CASE 
        WHEN charge_item_only_flag = '1' OR add_minus_flag = -1 THEN 
            CONCAT(id, '_', UNIX_TIMESTAMP(), '_', RAND()) -- 耗材和减项使用唯一值
        ELSE 
            CONCAT(customer_reg_id, '_', item_group_id, '_', COALESCE(check_part_id, 'NULL'))
    END
) STORED;

-- 为生成列添加唯一索引
CREATE UNIQUE INDEX uk_customer_item_non_consumable 
ON customer_reg_item_group (unique_key_non_consumable);

-- 5. 方案C：分表策略（复杂但有效）
-- 创建一个专门的唯一性检查表
CREATE TABLE customer_reg_item_unique_check (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    customer_reg_id VARCHAR(32) NOT NULL,
    item_group_id VARCHAR(32) NOT NULL,
    check_part_id VARCHAR(32),
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_customer_item_part (customer_reg_id, item_group_id, check_part_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 6. 方案D：应用层处理 + 普通索引
-- 不使用唯一约束，在应用层处理重复检查
-- 创建普通索引提高查询性能
CREATE INDEX idx_customer_item_duplicate_check 
ON customer_reg_item_group (customer_reg_id, item_group_id, check_part_id, add_minus_flag, charge_item_only_flag);

-- 7. 推荐的实施方案（方案A + 方案D）
-- 使用触发器 + 应用层双重保护

-- 首先创建索引提高性能
CREATE INDEX idx_customer_item_duplicate_check 
ON customer_reg_item_group (customer_reg_id, item_group_id, check_part_id, add_minus_flag, charge_item_only_flag);

-- 然后创建触发器作为最后防线
DELIMITER $$

CREATE TRIGGER tr_customer_reg_item_group_before_insert
BEFORE INSERT ON customer_reg_item_group
FOR EACH ROW
BEGIN
    DECLARE duplicate_count INT DEFAULT 0;
    
    -- 只对非耗材项目进行重复检查
    IF (NEW.charge_item_only_flag IS NULL OR NEW.charge_item_only_flag != '1') 
       AND NEW.add_minus_flag != -1 THEN
        
        SELECT COUNT(*) INTO duplicate_count
        FROM customer_reg_item_group
        WHERE customer_reg_id = NEW.customer_reg_id
          AND item_group_id = NEW.item_group_id
          AND (
              (check_part_id = NEW.check_part_id) OR 
              (check_part_id IS NULL AND NEW.check_part_id IS NULL)
          )
          AND add_minus_flag != -1
          AND (charge_item_only_flag IS NULL OR charge_item_only_flag != '1');
        
        IF duplicate_count > 0 THEN
            SIGNAL SQLSTATE '45000' 
            SET MESSAGE_TEXT = 'Duplicate non-consumable item already exists for this customer';
        END IF;
    END IF;
END$$

DELIMITER ;

-- 8. 验证触发器
-- 查看创建的触发器
SHOW TRIGGERS LIKE 'customer_reg_item_group';

-- 9. 测试触发器（可选）
-- 尝试插入重复的非耗材项目，应该失败
/*
INSERT INTO customer_reg_item_group 
(customer_reg_id, item_group_id, check_part_id, charge_item_only_flag, add_minus_flag, item_group_name, exam_no)
SELECT 
    customer_reg_id, item_group_id, check_part_id, charge_item_only_flag, add_minus_flag, item_group_name, exam_no
FROM customer_reg_item_group 
WHERE add_minus_flag != -1 
    AND (charge_item_only_flag IS NULL OR charge_item_only_flag != '1')
LIMIT 1;
*/

-- 10. 如果需要删除触发器
-- DROP TRIGGER IF EXISTS tr_customer_reg_item_group_before_insert;

-- 11. 性能优化建议
-- 为常用查询创建复合索引
CREATE INDEX idx_customer_reg_active_items 
ON customer_reg_item_group (customer_reg_id, add_minus_flag, charge_item_only_flag);

CREATE INDEX idx_item_group_customer_lookup 
ON customer_reg_item_group (item_group_id, customer_reg_id, add_minus_flag);

-- 使用说明：
-- 1. 推荐使用方案A（触发器）+ 应用层检查的双重保护
-- 2. 触发器只对非耗材项目（charge_item_only_flag != '1'）进行检查
-- 3. 应用层使用现有的checkItemGroupDuplicate方法
-- 4. 创建适当的索引提高查询性能

-- 注意事项：
-- 1. 触发器会增加插入操作的开销，但保证数据一致性
-- 2. 在高并发场景下，触发器可能成为性能瓶颈
-- 3. 建议在测试环境充分验证性能影响
-- 4. 可以考虑在业务低峰期执行数据库变更
