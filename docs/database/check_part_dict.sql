-- 检查部位字典表扩展（基于现有表结构）
-- 现有表结构：
-- CREATE TABLE `check_part_dict` (
--   `id` varchar(32) NOT NULL,
--   `code` varchar(50) DEFAULT NULL COMMENT '检查部位编码',
--   `name` varchar(50) DEFAULT NULL COMMENT '检查部位名称',
--   PRIMARY KEY (`id`),
--   KEY `check_part_code_idx` (`code`) USING BTREE
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 为现有表添加必要字段
ALTER TABLE `check_part_dict`
ADD COLUMN `help_char` varchar(200) DEFAULT NULL COMMENT '拼音缩写' AFTER `name`,
ADD COLUMN `category` varchar(50) DEFAULT NULL COMMENT '部位分类' AFTER `help_char`,
ADD COLUMN `sort_order` int DEFAULT 0 COMMENT '排序号' AFTER `category`,
ADD COLUMN `enable_flag` char(1) DEFAULT '1' COMMENT '启用状态(1-启用,0-禁用)' AFTER `sort_order`,
ADD COLUMN `remark` varchar(500) DEFAULT NULL COMMENT '备注' AFTER `enable_flag`,
ADD COLUMN `create_by` varchar(50) DEFAULT NULL COMMENT '创建人' AFTER `remark`,
ADD COLUMN `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `create_by`,
ADD COLUMN `update_by` varchar(50) DEFAULT NULL COMMENT '更新人' AFTER `create_time`,
ADD COLUMN `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `update_by`,
ADD COLUMN `del_flag` tinyint DEFAULT 0 COMMENT '删除标志(0-正常,1-删除)' AFTER `update_time`;

-- 添加索引
ALTER TABLE `check_part_dict`
ADD INDEX `idx_name` (`name`),
ADD INDEX `idx_help_char` (`help_char`),
ADD INDEX `idx_category` (`category`),
ADD INDEX `idx_enable_flag` (`enable_flag`),
ADD INDEX `idx_del_flag` (`del_flag`);

-- 检查部位使用统计表
CREATE TABLE `check_part_usage_stat` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `item_group_id` varchar(32) NOT NULL COMMENT '项目组合ID',
  `check_part_id` varchar(32) NOT NULL COMMENT '部位ID', 
  `usage_count` bigint DEFAULT 0 COMMENT '使用次数',
  `last_used_time` datetime DEFAULT NULL COMMENT '最后使用时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_group_part` (`item_group_id`, `check_part_id`),
  KEY `idx_item_group` (`item_group_id`),
  KEY `idx_check_part` (`check_part_id`),
  KEY `idx_usage_count` (`usage_count`),
  KEY `idx_last_used` (`last_used_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='检查部位使用统计表';

-- 初始化一些常用部位数据（适配现有表结构）
INSERT INTO `check_part_dict` (`id`, `code`, `name`, `help_char`, `category`, `sort_order`, `enable_flag`) VALUES
('1', 'LEFT_KNEE', '左膝关节', 'ZXGJ', '关节', 1, '1'),
('2', 'RIGHT_KNEE', '右膝关节', 'YXGJ', '关节', 2, '1'),
('3', 'LEFT_SHOULDER', '左肩关节', 'ZJGJ', '关节', 3, '1'),
('4', 'RIGHT_SHOULDER', '右肩关节', 'YJGJ', '关节', 4, '1'),
('5', 'LEFT_ANKLE', '左踝关节', 'ZHGJ', '关节', 5, '1'),
('6', 'RIGHT_ANKLE', '右踝关节', 'YHGJ', '关节', 6, '1'),
('7', 'CERVICAL', '颈椎', 'JZ', '脊柱', 7, '1'),
('8', 'THORACIC', '胸椎', 'XZ', '脊柱', 8, '1'),
('9', 'LUMBAR', '腰椎', 'YZ', '脊柱', 9, '1'),
('10', 'LEFT_HAND', '左手', 'ZS', '四肢', 10, '1'),
('11', 'RIGHT_HAND', '右手', 'YS', '四肢', 11, '1'),
('12', 'LEFT_FOOT', '左足', 'ZZ', '四肢', 12, '1'),
('13', 'RIGHT_FOOT', '右足', 'YZ', '四肢', 13, '1'),
('14', 'HEAD', '头部', 'TB', '头颈部', 14, '1'),
('15', 'NECK', '颈部', 'JB', '头颈部', 15, '1'),
('16', 'CHEST', '胸部', 'XB', '躯干', 16, '1'),
('17', 'ABDOMEN', '腹部', 'FB', '躯干', 17, '1'),
('18', 'PELVIS', '盆腔', 'PQ', '躯干', 18, '1'),
('19', 'UPPER_ABDOMEN', '上腹部', 'SFB', '躯干', 19, '1'),
('20', 'LOWER_ABDOMEN', '下腹部', 'XFB', '躯干', 20, '1');
