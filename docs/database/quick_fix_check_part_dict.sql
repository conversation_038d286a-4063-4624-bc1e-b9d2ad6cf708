-- 检查部位字典安全修复脚本
-- 此脚本用于快速解决部位弹窗"加载部位选项失败"的问题
-- 注意：此脚本只会添加缺失的字段和数据，不会删除现有数据

-- 1. 检查表是否存在
SELECT
    CASE
        WHEN COUNT(*) > 0 THEN '✓ check_part_dict表已存在'
        ELSE '✗ check_part_dict表不存在，需要手动创建'
    END as table_status
FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'check_part_dict';

-- 如果表不存在，只创建基础表结构（保留现有数据）
CREATE TABLE IF NOT EXISTS `check_part_dict` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `code` varchar(50) DEFAULT NULL COMMENT '检查部位编码',
  `name` varchar(50) DEFAULT NULL COMMENT '检查部位名称',
  PRIMARY KEY (`id`),
  KEY `check_part_code_idx` (`code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='检查部位字典';

-- 2. 安全地添加缺失的字段（不会影响现有数据）

-- 检查并添加 enable_flag 字段
SELECT
    CASE
        WHEN COUNT(*) > 0 THEN '✓ enable_flag字段已存在'
        ELSE '需要添加enable_flag字段'
    END as enable_flag_status
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'check_part_dict'
  AND COLUMN_NAME = 'enable_flag';

-- 添加 enable_flag 字段（如果不存在）
ALTER TABLE `check_part_dict`
ADD COLUMN IF NOT EXISTS `enable_flag` char(1) DEFAULT '1' COMMENT '启用状态(1-启用,0-禁用)';

-- 检查并添加 help_char 字段
SELECT
    CASE
        WHEN COUNT(*) > 0 THEN '✓ help_char字段已存在'
        ELSE '需要添加help_char字段'
    END as help_char_status
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'check_part_dict'
  AND COLUMN_NAME = 'help_char';

-- 添加 help_char 字段（如果不存在）
ALTER TABLE `check_part_dict`
ADD COLUMN IF NOT EXISTS `help_char` varchar(200) DEFAULT NULL COMMENT '拼音缩写' AFTER `name`;

-- 添加其他可能缺失的字段
ALTER TABLE `check_part_dict`
ADD COLUMN IF NOT EXISTS `category` varchar(50) DEFAULT NULL COMMENT '部位分类' AFTER `help_char`;

ALTER TABLE `check_part_dict`
ADD COLUMN IF NOT EXISTS `sort_order` int DEFAULT 0 COMMENT '排序号' AFTER `category`;

ALTER TABLE `check_part_dict`
ADD COLUMN IF NOT EXISTS `remark` varchar(500) DEFAULT NULL COMMENT '备注' AFTER `enable_flag`;

ALTER TABLE `check_part_dict`
ADD COLUMN IF NOT EXISTS `create_by` varchar(50) DEFAULT NULL COMMENT '创建人' AFTER `remark`;

ALTER TABLE `check_part_dict`
ADD COLUMN IF NOT EXISTS `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `create_by`;

ALTER TABLE `check_part_dict`
ADD COLUMN IF NOT EXISTS `update_by` varchar(50) DEFAULT NULL COMMENT '更新人' AFTER `create_time`;

ALTER TABLE `check_part_dict`
ADD COLUMN IF NOT EXISTS `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `update_by`;

ALTER TABLE `check_part_dict`
ADD COLUMN IF NOT EXISTS `del_flag` tinyint DEFAULT 0 COMMENT '删除标志(0-正常,1-删除)' AFTER `update_time`;

-- 3. 安全地更新现有数据
-- 为现有数据设置默认的 enable_flag 值（不会覆盖已有值）
UPDATE `check_part_dict`
SET `enable_flag` = '1'
WHERE `enable_flag` IS NULL OR `enable_flag` = '';

-- 显示当前数据状态
SELECT
    '当前数据状态' as info,
    COUNT(*) as total_records,
    SUM(CASE WHEN enable_flag = '1' THEN 1 ELSE 0 END) as enabled_records
FROM `check_part_dict`;

-- 4. 安全地插入基础数据（只插入不存在的记录）
INSERT IGNORE INTO `check_part_dict` (`id`, `code`, `name`, `help_char`, `category`, `sort_order`, `enable_flag`) VALUES
('1', 'HEAD', '头部', 'TB', '头颈部', 1, '1'),
('2', 'NECK', '颈部', 'JB', '头颈部', 2, '1'),
('3', 'CHEST', '胸部', 'XB', '躯干', 3, '1'),
('4', 'ABDOMEN', '腹部', 'FB', '躯干', 4, '1'),
('5', 'UPPER_ABDOMEN', '上腹部', 'SFB', '躯干', 5, '1'),
('6', 'LOWER_ABDOMEN', '下腹部', 'XFB', '躯干', 6, '1'),
('7', 'PELVIS', '盆腔', 'PQ', '躯干', 7, '1'),
('8', 'LEFT_KNEE', '左膝关节', 'ZXGJ', '关节', 8, '1'),
('9', 'RIGHT_KNEE', '右膝关节', 'YXGJ', '关节', 9, '1'),
('10', 'LEFT_SHOULDER', '左肩关节', 'ZJGJ', '关节', 10, '1'),
('11', 'RIGHT_SHOULDER', '右肩关节', 'YJGJ', '关节', 11, '1'),
('12', 'LEFT_ANKLE', '左踝关节', 'ZHGJ', '关节', 12, '1'),
('13', 'RIGHT_ANKLE', '右踝关节', 'YHGJ', '关节', 13, '1'),
('14', 'CERVICAL', '颈椎', 'JZ', '脊柱', 14, '1'),
('15', 'THORACIC', '胸椎', 'XZ', '脊柱', 15, '1'),
('16', 'LUMBAR', '腰椎', 'YZ', '脊柱', 16, '1'),
('17', 'LEFT_HAND', '左手', 'ZS', '四肢', 17, '1'),
('18', 'RIGHT_HAND', '右手', 'YS', '四肢', 18, '1'),
('19', 'LEFT_FOOT', '左足', 'ZZ', '四肢', 19, '1'),
('20', 'RIGHT_FOOT', '右足', 'YZ', '四肢', 20, '1');

-- 5. 安全地创建使用统计表（如果不存在，不会影响现有数据）
CREATE TABLE IF NOT EXISTS `check_part_usage_stat` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `item_group_id` varchar(32) NOT NULL COMMENT '项目组合ID',
  `check_part_id` varchar(32) NOT NULL COMMENT '部位ID',
  `usage_count` bigint DEFAULT 0 COMMENT '使用次数',
  `last_used_time` datetime DEFAULT NULL COMMENT '最后使用时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_group_part` (`item_group_id`, `check_part_id`),
  KEY `idx_item_group` (`item_group_id`),
  KEY `idx_check_part` (`check_part_id`),
  KEY `idx_usage_count` (`usage_count`),
  KEY `idx_last_used` (`last_used_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='检查部位使用统计';

-- 添加索引（如果不存在）
ALTER TABLE `check_part_dict` ADD INDEX IF NOT EXISTS `idx_name` (`name`);
ALTER TABLE `check_part_dict` ADD INDEX IF NOT EXISTS `idx_help_char` (`help_char`);
ALTER TABLE `check_part_dict` ADD INDEX IF NOT EXISTS `idx_category` (`category`);
ALTER TABLE `check_part_dict` ADD INDEX IF NOT EXISTS `idx_enable_flag` (`enable_flag`);
ALTER TABLE `check_part_dict` ADD INDEX IF NOT EXISTS `idx_del_flag` (`del_flag`);

-- 6. 验证修复结果
SELECT 
    '修复完成验证' as check_type,
    COUNT(*) as total_count,
    SUM(CASE WHEN enable_flag = '1' THEN 1 ELSE 0 END) as enabled_count
FROM check_part_dict;

-- 7. 显示可用的部位数据
SELECT 
    '可用部位数据' as info,
    id, code, name, help_char, category, sort_order
FROM check_part_dict 
WHERE enable_flag = '1' 
ORDER BY sort_order, name 
LIMIT 10;

-- 8. 提示信息
SELECT 
    '修复完成提示' as info,
    CASE 
        WHEN (SELECT COUNT(*) FROM check_part_dict WHERE enable_flag = '1') > 0 
        THEN '✅ 修复成功！现在可以测试部位选择功能了。'
        ELSE '❌ 修复失败！请检查数据库权限或手动执行SQL语句。'
    END as message;
