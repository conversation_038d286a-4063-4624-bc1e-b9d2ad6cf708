-- customer_reg_item_group表增加部位相关字段
ALTER TABLE `customer_reg_item_group` 
ADD COLUMN `check_part_id` varchar(32) DEFAULT NULL COMMENT '检查部位ID' AFTER `class_code`,
ADD COLUMN `check_part_name` varchar(100) DEFAULT NULL COMMENT '检查部位名称' AFTER `check_part_id`,
ADD COLUMN `parent_group_id` varchar(32) DEFAULT NULL COMMENT '父项目组ID（用于关联同一项目的不同部位）' AFTER `check_part_name`;

-- 添加索引
ALTER TABLE `customer_reg_item_group` 
ADD INDEX `idx_check_part_id` (`check_part_id`),
ADD INDEX `idx_parent_group_id` (`parent_group_id`);

-- item_group表增加hasCheckPart字段（如果还没有的话）
-- 先检查字段是否存在，如果不存在则添加
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'item_group' 
     AND COLUMN_NAME = 'has_check_part') > 0,
    'SELECT "Column has_check_part already exists"',
    'ALTER TABLE `item_group` ADD COLUMN `has_check_part` char(1) DEFAULT "0" COMMENT "是否有检查部位(1-是,0-否)" AFTER `class_code`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
