-- 附属项目配置添加部位逻辑字段
-- 为 item_group_relation 表添加部位相关字段

-- 1. 添加主项目部位相关字段
ALTER TABLE `item_group_relation`
  ADD COLUMN  `main_check_part_id` varchar(32) DEFAULT NULL COMMENT '主项目部位ID' AFTER `relation_item_name`;

ALTER TABLE `item_group_relation`
  ADD COLUMN  `main_check_part_name` varchar(50) DEFAULT NULL COMMENT '主项目部位名称' AFTER `main_check_part_id`;

ALTER TABLE `item_group_relation`
  ADD COLUMN  `main_check_part_code` varchar(50) DEFAULT NULL COMMENT '主项目部位编码' AFTER `main_check_part_name`;

-- 2. 添加关联项目部位相关字段
ALTER TABLE `item_group_relation`
  ADD COLUMN  `relation_check_part_id` varchar(32) DEFAULT NULL COMMENT '关联项目部位ID' AFTER `main_check_part_code`;

ALTER TABLE `item_group_relation`
  ADD COLUMN  `relation_check_part_name` varchar(50) DEFAULT NULL COMMENT '关联项目部位名称' AFTER `relation_check_part_id`;

ALTER TABLE `item_group_relation`
  ADD COLUMN  `relation_check_part_code` varchar(50) DEFAULT NULL COMMENT '关联项目部位编码' AFTER `relation_check_part_name`;

-- 3. 添加索引以提升查询性能
ALTER TABLE `item_group_relation`
  ADD INDEX  `idx_main_check_part` (`main_check_part_id`);

ALTER TABLE `item_group_relation`
  ADD INDEX  `idx_relation_check_part` (`relation_check_part_id`);

ALTER TABLE `item_group_relation`
  ADD INDEX  `idx_group_main_part` (`group_id`, `main_check_part_id`);

ALTER TABLE `item_group_relation`
  ADD INDEX  `idx_relation_group_part` (`relation_group_id`, `relation_check_part_id`);

-- 4. 添加复合索引支持部位匹配查询
ALTER TABLE `item_group_relation`
  ADD INDEX  `idx_attach_part_match` (`group_id`, `relation`, `main_check_part_id`, `relation_check_part_id`);

-- 验证字段添加结果
SELECT 
    'item_group_relation表部位字段检查' as check_type,
    COLUMN_NAME as field_name,
    DATA_TYPE as data_type,
    IS_NULLABLE as nullable,
    COLUMN_COMMENT as comment
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'item_group_relation'
  AND COLUMN_NAME LIKE '%check_part%'
ORDER BY ORDINAL_POSITION;
