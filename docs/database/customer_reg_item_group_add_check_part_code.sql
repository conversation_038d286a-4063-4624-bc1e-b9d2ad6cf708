-- 为 customer_reg_item_group 表添加检查部位编码字段
-- 执行时间：请在系统维护时间执行
-- 影响范围：体检登记项目管理功能

-- 1. 检查表是否存在
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✓ customer_reg_item_group表存在'
        ELSE '✗ customer_reg_item_group表不存在'
    END as table_status
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'customer_reg_item_group';

-- 2. 检查字段是否已存在
SELECT 
    'check_part_code字段检查' as check_type,
    CASE 
        WHEN COUNT(*) > 0 THEN '✓ 字段已存在'
        ELSE '需要添加字段'
    END as status
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'customer_reg_item_group' 
  AND COLUMN_NAME = 'check_part_code';

-- 3. 添加检查部位编码字段
ALTER TABLE `customer_reg_item_group` 
ADD COLUMN IF NOT EXISTS `check_part_code` varchar(50) DEFAULT NULL COMMENT '检查部位编码' AFTER `check_part_name`;

-- 4. 添加索引以提升查询性能
ALTER TABLE `customer_reg_item_group` 
ADD INDEX IF NOT EXISTS `idx_check_part_code` (`check_part_code`);

ALTER TABLE `customer_reg_item_group` 
ADD INDEX IF NOT EXISTS `idx_customer_reg_item_part` (`customer_reg_id`, `item_group_id`, `check_part_id`);

-- 5. 从 check_part_dict 表同步部位编码数据
UPDATE `customer_reg_item_group` crig 
JOIN `check_part_dict` cpd ON crig.check_part_id = cpd.id 
SET crig.check_part_code = cpd.code 
WHERE crig.check_part_id IS NOT NULL 
  AND crig.check_part_id != '' 
  AND crig.check_part_code IS NULL;

-- 6. 验证字段添加结果
SELECT 
    '=== 字段添加验证 ===' as step,
    COLUMN_NAME as field_name,
    DATA_TYPE as data_type,
    IS_NULLABLE as nullable,
    COLUMN_DEFAULT as default_value,
    COLUMN_COMMENT as comment
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'customer_reg_item_group' 
  AND COLUMN_NAME = 'check_part_code'
ORDER BY ORDINAL_POSITION;

-- 7. 显示部位相关字段的表结构
SELECT 
    '=== 部位相关字段结构 ===' as step,
    COLUMN_NAME as field_name,
    DATA_TYPE as data_type,
    CHARACTER_MAXIMUM_LENGTH as max_length,
    IS_NULLABLE as nullable,
    COLUMN_DEFAULT as default_value,
    COLUMN_COMMENT as comment
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'customer_reg_item_group'
  AND COLUMN_NAME IN ('check_part_id', 'check_part_name', 'check_part_code')
ORDER BY ORDINAL_POSITION;

-- 8. 检查索引创建情况
SELECT 
    '=== 索引检查 ===' as step,
    INDEX_NAME as index_name,
    COLUMN_NAME as column_name,
    NON_UNIQUE as non_unique,
    INDEX_COMMENT as comment
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'customer_reg_item_group'
  AND INDEX_NAME IN ('idx_check_part_code', 'idx_customer_reg_item_part')
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- 9. 数据同步统计
SELECT 
    '=== 数据同步统计 ===' as step,
    COUNT(*) as total_records,
    SUM(CASE WHEN check_part_id IS NOT NULL AND check_part_id != '' THEN 1 ELSE 0 END) as has_part_id,
    SUM(CASE WHEN check_part_name IS NOT NULL AND check_part_name != '' THEN 1 ELSE 0 END) as has_part_name,
    SUM(CASE WHEN check_part_code IS NOT NULL AND check_part_code != '' THEN 1 ELSE 0 END) as has_part_code,
    SUM(CASE WHEN check_part_id IS NOT NULL AND check_part_id != '' AND check_part_code IS NULL THEN 1 ELSE 0 END) as missing_code
FROM `customer_reg_item_group`;

-- 10. 显示部位数据示例
SELECT 
    '=== 部位数据示例 ===' as step,
    id,
    item_group_name,
    check_part_id,
    check_part_name,
    check_part_code,
    create_time
FROM `customer_reg_item_group` 
WHERE check_part_id IS NOT NULL 
  AND check_part_id != ''
ORDER BY create_time DESC 
LIMIT 10;

-- 11. 验证数据完整性
SELECT 
    '=== 数据完整性检查 ===' as step,
    CASE 
        WHEN COUNT(*) = 0 THEN '✓ 所有有部位ID的记录都有部位编码'
        ELSE CONCAT('✗ 发现 ', COUNT(*), ' 条记录缺少部位编码')
    END as integrity_status
FROM `customer_reg_item_group` 
WHERE check_part_id IS NOT NULL 
  AND check_part_id != '' 
  AND (check_part_code IS NULL OR check_part_code = '');

-- 12. 完成提示
SELECT 
    '=== 迁移完成 ===' as step,
    '✅ customer_reg_item_group表已成功添加检查部位编码字段' as message,
    '✅ 已从check_part_dict表同步现有数据的部位编码' as sync_status,
    '现在可以在体检登记和结果录入中使用部位编码进行精确匹配' as next_step;
