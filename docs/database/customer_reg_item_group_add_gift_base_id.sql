-- 为customer_reg_item_group表添加赠送基础项目ID字段
-- 用于标识赠送项目与主项目的关联关系

-- 添加赠送基础项目ID字段
ALTER TABLE `customer_reg_item_group` 
ADD COLUMN IF NOT EXISTS `gift_base_id` varchar(32) DEFAULT NULL COMMENT '赠送基础项目ID' AFTER `attach_base_id`;

-- 添加索引以提升查询性能
ALTER TABLE `customer_reg_item_group` 
ADD INDEX IF NOT EXISTS `idx_gift_base_id` (`gift_base_id`);

-- 验证字段添加结果
SELECT 
    'customer_reg_item_group表赠送字段检查' as check_type,
    COLUMN_NAME as field_name,
    DATA_TYPE as data_type,
    IS_NULLABLE as nullable,
    COLUMN_COMMENT as comment
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'customer_reg_item_group'
  AND COLUMN_NAME IN ('attach_base_id', 'gift_base_id')
ORDER BY ORDINAL_POSITION;
