-- 工种自动完成组件数据库更新脚本
-- 为zy_worktype表添加使用频次字段和相关索引
--
-- 注意：本组件使用Redis作为缓存层，无需创建数据库缓存表
-- Redis缓存的优势：
-- 1. 更快的读写速度
-- 2. 自动过期机制
-- 3. 减少数据库压力
-- 4. 更好的并发性能

-- 1. 为zy_worktype表添加use_count字段
ALTER TABLE zy_worktype ADD COLUMN use_count INT DEFAULT 0 COMMENT '使用频次';

-- 2. 为现有数据初始化use_count字段
UPDATE zy_worktype SET use_count = 0 WHERE use_count IS NULL;

-- 3. 创建索引优化查询性能
-- 为name字段创建索引（如果不存在）
CREATE INDEX  idx_zy_worktype_name ON zy_worktype(name);

-- 为helpChar字段创建索引（如果不存在）
CREATE INDEX  idx_zy_worktype_help_char ON zy_worktype(help_char);

-- 为use_count字段创建索引
CREATE INDEX  idx_zy_worktype_use_count ON zy_worktype(use_count DESC);

-- 为enableFlag字段创建索引（如果不存在）
CREATE INDEX  idx_zy_worktype_enable_flag ON zy_worktype(enable_flag);

-- 创建复合索引用于自动完成搜索
CREATE INDEX  idx_zy_worktype_search ON zy_worktype(enable_flag, use_count DESC, name, help_char);
