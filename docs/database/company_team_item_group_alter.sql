-- company_team_item_group表增加部位相关字段
ALTER TABLE `company_team_item_group` 
ADD COLUMN `check_part_id` varchar(32) DEFAULT NULL COMMENT '检查部位ID' AFTER `class_code`,
ADD COLUMN `check_part_name` varchar(100) DEFAULT NULL COMMENT '检查部位名称' AFTER `check_part_id`,
ADD COLUMN `check_part_code` varchar(50) DEFAULT NULL COMMENT '检查部位代码' AFTER `check_part_name`;

-- 添加索引
ALTER TABLE `company_team_item_group` 
ADD INDEX `idx_team_item_part` (`team_id`, `item_group_id`, `check_part_id`);

-- 添加注释
ALTER TABLE `company_team_item_group` 
COMMENT = '单位分组项目组合表（支持部位选择）';
