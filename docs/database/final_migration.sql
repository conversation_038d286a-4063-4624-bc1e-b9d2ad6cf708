-- 检查部位选择功能数据库迁移脚本
-- 适配现有 check_part_dict 表结构

-- 1. 扩展现有 check_part_dict 表
ALTER TABLE `check_part_dict` 
ADD COLUMN `help_char` varchar(200) DEFAULT NULL COMMENT '拼音缩写' AFTER `name`,
ADD COLUMN `category` varchar(50) DEFAULT NULL COMMENT '部位分类' AFTER `help_char`,
ADD COLUMN `sort_order` int DEFAULT 0 COMMENT '排序号' AFTER `category`,
ADD COLUMN `enable_flag` char(1) DEFAULT '1' COMMENT '启用状态(1-启用,0-禁用)' AFTER `sort_order`,
ADD COLUMN `remark` varchar(500) DEFAULT NULL COMMENT '备注' AFTER `enable_flag`,
ADD COLUMN `create_by` varchar(50) DEFAULT NULL COMMENT '创建人' AFTER `remark`,
ADD COLUMN `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `create_by`,
ADD COLUMN `update_by` varchar(50) DEFAULT NULL COMMENT '更新人' AFTER `create_time`,
ADD COLUMN `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `update_by`,
ADD COLUMN `del_flag` tinyint DEFAULT 0 COMMENT '删除标志(0-正常,1-删除)' AFTER `update_time`;

-- 2. 为 check_part_dict 表添加索引
ALTER TABLE `check_part_dict` 
ADD INDEX `idx_name` (`name`),
ADD INDEX `idx_help_char` (`help_char`),
ADD INDEX `idx_category` (`category`),
ADD INDEX `idx_enable_flag` (`enable_flag`),
ADD INDEX `idx_del_flag` (`del_flag`);

-- 3. 创建检查部位使用统计表
CREATE TABLE `check_part_usage_stat` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `item_group_id` varchar(32) NOT NULL COMMENT '项目组合ID',
  `check_part_id` varchar(32) NOT NULL COMMENT '部位ID', 
  `usage_count` bigint DEFAULT 0 COMMENT '使用次数',
  `last_used_time` datetime DEFAULT NULL COMMENT '最后使用时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_group_part` (`item_group_id`, `check_part_id`),
  KEY `idx_item_group` (`item_group_id`),
  KEY `idx_check_part` (`check_part_id`),
  KEY `idx_usage_count` (`usage_count`),
  KEY `idx_last_used` (`last_used_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='检查部位使用统计表';

-- 4. 扩展 customer_reg_item_group 表
ALTER TABLE `customer_reg_item_group` 
ADD COLUMN `check_part_id` varchar(32) DEFAULT NULL COMMENT '检查部位ID' AFTER `class_code`,
ADD COLUMN `check_part_name` varchar(100) DEFAULT NULL COMMENT '检查部位名称' AFTER `check_part_id`,
ADD COLUMN `parent_group_id` varchar(32) DEFAULT NULL COMMENT '父项目组ID（用于关联同一项目的不同部位）' AFTER `check_part_name`;

-- 5. 为 customer_reg_item_group 表添加索引
ALTER TABLE `customer_reg_item_group` 
ADD INDEX `idx_check_part_id` (`check_part_id`),
ADD INDEX `idx_parent_group_id` (`parent_group_id`);

-- 6. 为 item_group 表添加 has_check_part 字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'item_group' 
     AND COLUMN_NAME = 'has_check_part') > 0,
    'SELECT "Column has_check_part already exists"',
    'ALTER TABLE `item_group` ADD COLUMN `has_check_part` char(1) DEFAULT "0" COMMENT "是否有检查部位(1-是,0-否)" AFTER `class_code`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 7. 初始化常用部位数据
INSERT INTO `check_part_dict` (`id`, `code`, `name`, `help_char`, `category`, `sort_order`, `enable_flag`) VALUES
('1', 'LEFT_KNEE', '左膝关节', 'ZXGJ', '关节', 1, '1'),
('2', 'RIGHT_KNEE', '右膝关节', 'YXGJ', '关节', 2, '1'),
('3', 'LEFT_SHOULDER', '左肩关节', 'ZJGJ', '关节', 3, '1'),
('4', 'RIGHT_SHOULDER', '右肩关节', 'YJGJ', '关节', 4, '1'),
('5', 'LEFT_ANKLE', '左踝关节', 'ZHGJ', '关节', 5, '1'),
('6', 'RIGHT_ANKLE', '右踝关节', 'YHGJ', '关节', 6, '1'),
('7', 'CERVICAL', '颈椎', 'JZ', '脊柱', 7, '1'),
('8', 'THORACIC', '胸椎', 'XZ', '脊柱', 8, '1'),
('9', 'LUMBAR', '腰椎', 'YZ', '脊柱', 9, '1'),
('10', 'LEFT_HAND', '左手', 'ZS', '四肢', 10, '1'),
('11', 'RIGHT_HAND', '右手', 'YS', '四肢', 11, '1'),
('12', 'LEFT_FOOT', '左足', 'ZZ', '四肢', 12, '1'),
('13', 'RIGHT_FOOT', '右足', 'YZ', '四肢', 13, '1'),
('14', 'HEAD', '头部', 'TB', '头颈部', 14, '1'),
('15', 'NECK', '颈部', 'JB', '头颈部', 15, '1'),
('16', 'CHEST', '胸部', 'XB', '躯干', 16, '1'),
('17', 'ABDOMEN', '腹部', 'FB', '躯干', 17, '1'),
('18', 'PELVIS', '盆腔', 'PQ', '躯干', 18, '1'),
('19', 'UPPER_ABDOMEN', '上腹部', 'SFB', '躯干', 19, '1'),
('20', 'LOWER_ABDOMEN', '下腹部', 'XFB', '躯干', 20, '1')
ON DUPLICATE KEY UPDATE 
  `help_char` = VALUES(`help_char`),
  `category` = VALUES(`category`),
  `sort_order` = VALUES(`sort_order`),
  `enable_flag` = VALUES(`enable_flag`);

-- 8. 验证脚本执行结果
SELECT 'check_part_dict表字段检查' as check_type, 
       COUNT(*) as field_count 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'check_part_dict';

SELECT 'check_part_usage_stat表检查' as check_type,
       COUNT(*) as table_count
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'check_part_usage_stat';

SELECT 'customer_reg_item_group表字段检查' as check_type,
       COUNT(*) as field_count
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'customer_reg_item_group'
  AND COLUMN_NAME IN ('check_part_id', 'check_part_name', 'parent_group_id');

SELECT 'check_part_dict数据检查' as check_type,
       COUNT(*) as data_count
FROM check_part_dict 
WHERE enable_flag = '1';
