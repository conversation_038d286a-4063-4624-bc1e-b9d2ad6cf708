-- 检查部位字典安全修复脚本（兼容所有MySQL版本）
-- 此脚本绝对不会删除现有数据，只会添加缺失的字段和数据

-- 1. 检查表是否存在
SELECT 
    '=== 表存在性检查 ===' as step,
    CASE 
        WHEN COUNT(*) > 0 THEN '✓ check_part_dict表已存在'
        ELSE '✗ check_part_dict表不存在'
    END as status
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'check_part_dict';

-- 2. 检查字段是否存在
SELECT 
    '=== 字段存在性检查 ===' as step,
    'enable_flag' as field_name,
    CASE 
        WHEN COUNT(*) > 0 THEN '✓ 字段已存在'
        ELSE '✗ 字段不存在，需要添加'
    END as status
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'check_part_dict' 
  AND COLUMN_NAME = 'enable_flag'

UNION ALL

SELECT 
    '=== 字段存在性检查 ===' as step,
    'help_char' as field_name,
    CASE 
        WHEN COUNT(*) > 0 THEN '✓ 字段已存在'
        ELSE '✗ 字段不存在，需要添加'
    END as status
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'check_part_dict' 
  AND COLUMN_NAME = 'help_char';

-- 3. 如果表不存在，创建基础表（只有基础字段）
-- 注意：这里使用 CREATE TABLE IF NOT EXISTS，不会影响现有表
CREATE TABLE IF NOT EXISTS `check_part_dict` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `code` varchar(50) DEFAULT NULL COMMENT '检查部位编码',
  `name` varchar(50) DEFAULT NULL COMMENT '检查部位名称',
  PRIMARY KEY (`id`),
  KEY `check_part_code_idx` (`code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='检查部位字典';

-- 4. 安全地添加字段（使用存储过程方式，兼容所有MySQL版本）

-- 添加 enable_flag 字段
DELIMITER $$
CREATE PROCEDURE AddEnableFlagColumn()
BEGIN
    DECLARE column_exists INT DEFAULT 0;
    
    SELECT COUNT(*) INTO column_exists 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'check_part_dict' 
      AND COLUMN_NAME = 'enable_flag';
    
    IF column_exists = 0 THEN
        ALTER TABLE `check_part_dict` 
        ADD COLUMN `enable_flag` char(1) DEFAULT '1' COMMENT '启用状态(1-启用,0-禁用)';
        SELECT '✓ enable_flag字段已添加' as result;
    ELSE
        SELECT '✓ enable_flag字段已存在' as result;
    END IF;
END$$
DELIMITER ;

CALL AddEnableFlagColumn();
DROP PROCEDURE AddEnableFlagColumn;

-- 添加 help_char 字段
DELIMITER $$
CREATE PROCEDURE AddHelpCharColumn()
BEGIN
    DECLARE column_exists INT DEFAULT 0;
    
    SELECT COUNT(*) INTO column_exists 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'check_part_dict' 
      AND COLUMN_NAME = 'help_char';
    
    IF column_exists = 0 THEN
        ALTER TABLE `check_part_dict` 
        ADD COLUMN `help_char` varchar(200) DEFAULT NULL COMMENT '拼音缩写' AFTER `name`;
        SELECT '✓ help_char字段已添加' as result;
    ELSE
        SELECT '✓ help_char字段已存在' as result;
    END IF;
END$$
DELIMITER ;

CALL AddHelpCharColumn();
DROP PROCEDURE AddHelpCharColumn;

-- 添加其他字段
DELIMITER $$
CREATE PROCEDURE AddOtherColumns()
BEGIN
    DECLARE column_exists INT DEFAULT 0;
    
    -- 添加 category 字段
    SELECT COUNT(*) INTO column_exists 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'check_part_dict' 
      AND COLUMN_NAME = 'category';
    
    IF column_exists = 0 THEN
        ALTER TABLE `check_part_dict` 
        ADD COLUMN `category` varchar(50) DEFAULT NULL COMMENT '部位分类' AFTER `help_char`;
    END IF;
    
    -- 添加 sort_order 字段
    SELECT COUNT(*) INTO column_exists 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'check_part_dict' 
      AND COLUMN_NAME = 'sort_order';
    
    IF column_exists = 0 THEN
        ALTER TABLE `check_part_dict` 
        ADD COLUMN `sort_order` int DEFAULT 0 COMMENT '排序号' AFTER `category`;
    END IF;
    
    SELECT '✓ 其他字段检查完成' as result;
END$$
DELIMITER ;

CALL AddOtherColumns();
DROP PROCEDURE AddOtherColumns;

-- 5. 安全地更新现有数据
UPDATE `check_part_dict` 
SET `enable_flag` = '1' 
WHERE `enable_flag` IS NULL OR `enable_flag` = '';

-- 6. 安全地插入基础数据（使用 INSERT IGNORE 避免重复）
INSERT IGNORE INTO `check_part_dict` (`id`, `code`, `name`, `help_char`, `category`, `sort_order`, `enable_flag`) VALUES
('1', 'HEAD', '头部', 'TB', '头颈部', 1, '1'),
('2', 'NECK', '颈部', 'JB', '头颈部', 2, '1'),
('3', 'CHEST', '胸部', 'XB', '躯干', 3, '1'),
('4', 'ABDOMEN', '腹部', 'FB', '躯干', 4, '1'),
('5', 'LEFT_KNEE', '左膝关节', 'ZXGJ', '关节', 5, '1'),
('6', 'RIGHT_KNEE', '右膝关节', 'YXGJ', '关节', 6, '1'),
('7', 'CERVICAL', '颈椎', 'JZ', '脊柱', 7, '1'),
('8', 'LUMBAR', '腰椎', 'YZ', '脊柱', 8, '1');

-- 7. 创建统计表（如果不存在）
CREATE TABLE IF NOT EXISTS `check_part_usage_stat` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `item_group_id` varchar(32) NOT NULL COMMENT '项目组合ID',
  `check_part_id` varchar(32) NOT NULL COMMENT '部位ID', 
  `usage_count` bigint DEFAULT 0 COMMENT '使用次数',
  `last_used_time` datetime DEFAULT NULL COMMENT '最后使用时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_group_part` (`item_group_id`, `check_part_id`),
  KEY `idx_item_group` (`item_group_id`),
  KEY `idx_check_part` (`check_part_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='检查部位使用统计';

-- 8. 验证修复结果
SELECT 
    '=== 修复结果验证 ===' as step,
    COUNT(*) as total_records,
    SUM(CASE WHEN enable_flag = '1' THEN 1 ELSE 0 END) as enabled_records,
    CASE 
        WHEN SUM(CASE WHEN enable_flag = '1' THEN 1 ELSE 0 END) > 0 
        THEN '✅ 修复成功！'
        ELSE '❌ 仍有问题，请检查数据'
    END as status
FROM `check_part_dict`;

-- 9. 显示可用数据
SELECT 
    '=== 可用部位数据 ===' as step,
    id, code, name, help_char, category, enable_flag
FROM `check_part_dict` 
WHERE `enable_flag` = '1' 
ORDER BY `sort_order`, `name` 
LIMIT 10;
