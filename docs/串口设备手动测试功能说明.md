# 串口设备手动测试功能说明

## 功能概述

为配置了串口设备且`autoStartMeasurement`为false的大项添加了"开始测试"按钮，允许用户手动触发设备测试指令，提供更灵活的设备控制方式。

## 功能特性

### 1. 智能显示条件

"开始测试"按钮只在满足以下所有条件时显示：

- ✅ **配置了串口设备**：项目组关联了串口设备配置
- ✅ **非自动启动**：`autoStartMeasurement`为false或'N'
- ✅ **项目状态为"未检"**：确保项目可以接收测试数据
- ✅ **设备连接成功**：`comEquipmentStatus`为'success'

### 2. 按钮位置和样式

- **位置**：项目组右上角操作区域，保存按钮旁边
- **样式**：蓝色主色调按钮，带播放图标
- **状态**：支持loading状态，防止重复点击

### 3. 测试流程

```
用户点击"开始测试"
    ↓
检查设备配置和连接状态
    ↓
构建测试指令
    ↓
通过WebSocket发送指令
    ↓
等待设备响应
    ↓
接收并解析设备数据
    ↓
更新项目结果
```

## 技术实现

### 1. 显示条件判断

```javascript
function shouldShowStartTestButton(group) {
  // 检查该项目组是否配置了串口设备
  const comEquipment = comEquipmentList.value.find(item => item.groupId === group.itemGroupId);
  if (!comEquipment) return false;

  // 检查autoStartMeasurement是否为false
  if (comEquipment.autoStartMeasurement === 'Y' || comEquipment.autoStartMeasurement === true) {
    return false;
  }

  // 检查项目组状态是否为"未检"
  if (group.checkStatus !== '未检') return false;

  // 检查设备连接状态
  if (group.comEquipmentStatus !== 'success') return false;

  return true;
}
```

### 2. 测试指令发送

```javascript
function startDeviceTest(group) {
  try {
    // 查找对应的串口设备配置
    const comEquipment = comEquipmentList.value.find(item => item.groupId === group.itemGroupId);
    
    // 检查WebSocket连接
    const ws = websocketMap.get(comEquipment.websocketUrl);
    if (!ws || !ws.isConnected()) {
      message.error('设备连接已断开，请重新连接');
      return;
    }

    // 设置测试状态
    group.testingInProgress = true;
    group.comEquipmentTip = '正在发送测试指令...';

    // 构建并发送测试指令
    const cmdFunction = comEquipment.cmdFunction ? createCmdFunction(comEquipment.cmdFunction) : null;
    if (cmdFunction) {
      const cmdMessage = cmdFunction(currentReg.value);
      ws.sendMessage('sendPortCmd', cmdMessage);
      message.success('测试指令已发送');
    }
  } catch (error) {
    group.testingInProgress = false;
    message.error('发送测试指令异常：' + error.message);
  }
}
```

### 3. 状态管理

```javascript
// 测试状态字段
group.testingInProgress = true;  // 测试进行中
group.comEquipmentTip = '状态提示'; // 设备状态提示

// 超时处理
setTimeout(() => {
  if (group.testingInProgress) {
    group.testingInProgress = false;
    group.comEquipmentTip = '等待设备响应超时';
  }
}, 30000); // 30秒超时

// 数据接收时清除状态
if (group.testingInProgress) {
  group.testingInProgress = false;
}
```

## 用户界面

### 1. 按钮显示

```vue
<a-tooltip title="开始测试" v-if="shouldShowStartTestButton(group)">
  <a-button
    type="primary"
    size="small"
    @click="startDeviceTest(group)"
    :loading="group.testingInProgress"
    :style="{ marginLeft: '8px' }"
  >
    <template #icon>
      <PlayCircleOutlined />
    </template>
    开始测试
  </a-button>
</a-tooltip>
```

### 2. 状态提示

- **正常状态**：显示"设备连接成功"
- **发送指令**：显示"正在发送测试指令..."
- **等待响应**：显示"测试指令已发送，等待设备响应..."
- **解析数据**：显示"设备数据解析中..."
- **完成测试**：显示"设备数据解析完成"
- **超时状态**：显示"等待设备响应超时"

## 使用场景

### 场景1：血压计手动测试

**设备配置：**
- 设备类型：血压计
- autoStartMeasurement：false
- 连接状态：成功

**操作流程：**
1. 用户进入血压检查项目
2. 看到"开始测试"按钮
3. 点击按钮发送测试指令
4. 设备开始测量血压
5. 测量完成后自动上传数据
6. 系统解析并填入结果

### 场景2：心电图手动测试

**设备配置：**
- 设备类型：心电图机
- autoStartMeasurement：false
- 连接状态：成功

**操作流程：**
1. 用户进入心电图检查项目
2. 准备好电极片等设备
3. 点击"开始测试"按钮
4. 设备开始记录心电图
5. 记录完成后上传数据
6. 系统生成心电图报告

### 场景3：肺功能检查

**设备配置：**
- 设备类型：肺功能仪
- autoStartMeasurement：false
- 连接状态：成功

**操作流程：**
1. 用户进入肺功能检查项目
2. 指导体检者准备测试
3. 点击"开始测试"按钮
4. 设备开始肺功能测试
5. 测试完成后上传数据
6. 系统计算肺功能指标

## 错误处理

### 1. 设备未配置

**错误信息**：未找到对应的设备配置
**处理方式**：检查设备配置是否正确关联项目组

### 2. 设备连接断开

**错误信息**：设备连接已断开，请重新连接
**处理方式**：重新连接设备或检查网络状态

### 3. 指令构建失败

**错误信息**：测试指令构建失败
**处理方式**：检查设备的cmdFunction配置

### 4. 响应超时

**错误信息**：等待设备响应超时
**处理方式**：检查设备状态，必要时重新测试

## 配置要求

### 1. 设备配置

在设备管理中需要正确配置：
- **autoStartMeasurement**：设置为false或'N'
- **cmdFunction**：配置测试指令生成函数
- **dataFunction**：配置数据解析函数
- **websocketUrl**：WebSocket服务地址

### 2. 项目关联

确保设备配置正确关联到项目组：
- **groupId**：设备配置的groupId与项目组ID匹配

### 3. 网络连接

确保WebSocket连接正常：
- **服务状态**：中间件服务正常运行
- **网络通信**：前端与中间件网络通畅

## 优势特点

### 1. 灵活控制

- **按需测试**：用户可以根据实际情况决定何时开始测试
- **状态可控**：清晰的测试状态提示和进度显示
- **错误恢复**：完善的错误处理和超时机制

### 2. 用户友好

- **直观操作**：简单的按钮点击即可开始测试
- **状态反馈**：实时的状态提示和进度显示
- **错误提示**：清晰的错误信息和处理建议

### 3. 系统集成

- **无缝集成**：与现有设备管理系统完全集成
- **数据同步**：测试结果自动同步到项目结果
- **状态管理**：统一的设备状态管理机制

## 注意事项

### 1. 设备准备

- 确保设备已正确连接并初始化
- 检查设备的物理连接状态
- 验证设备配置参数正确

### 2. 测试时机

- 只在项目状态为"未检"时进行测试
- 确保体检者已准备好接受测试
- 避免在设备故障时强制测试

### 3. 数据处理

- 测试完成后及时保存结果
- 检查数据解析是否正确
- 必要时进行数据校验

## 总结

手动测试功能为串口设备提供了更灵活的控制方式，特别适用于需要人工干预或特定时机启动的医疗设备。通过智能的显示条件判断、完善的错误处理和友好的用户界面，大大提升了设备操作的便利性和可靠性。
