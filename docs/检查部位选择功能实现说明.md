# 检查部位选择功能实现说明

## 功能概述

本功能为体检项目管理系统增加了检查部位选择功能。当项目具有`hasCheckPart=1`属性时，用户在选择项目后需要进一步选择检查部位，系统会根据选择的部位数量创建对应数量的项目记录。

## 技术架构

### 前端技术栈
- Vue 3 + TypeScript
- Ant Design Vue
- 响应式状态管理
- 防抖搜索优化

### 后端技术栈
- Spring Boot
- MyBatis Plus
- Redis缓存
- 异步处理

## 数据库设计

### 1. 检查部位字典表 (check_part_dict) - 基于现有表扩展
```sql
-- 现有表结构
CREATE TABLE `check_part_dict` (
  `id` varchar(32) NOT NULL,
  `code` varchar(50) DEFAULT NULL COMMENT '检查部位编码',
  `name` varchar(50) DEFAULT NULL COMMENT '检查部位名称',
  PRIMARY KEY (`id`),
  KEY `check_part_code_idx` (`code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 扩展字段
ALTER TABLE `check_part_dict`
ADD COLUMN `help_char` varchar(200) DEFAULT NULL COMMENT '拼音缩写',
ADD COLUMN `category` varchar(50) DEFAULT NULL COMMENT '部位分类',
ADD COLUMN `sort_order` int DEFAULT 0 COMMENT '排序号',
ADD COLUMN `enable_flag` char(1) DEFAULT '1' COMMENT '启用状态',
ADD COLUMN `remark` varchar(500) DEFAULT NULL COMMENT '备注',
ADD COLUMN `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
ADD COLUMN `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
ADD COLUMN `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
ADD COLUMN `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
ADD COLUMN `del_flag` tinyint DEFAULT 0 COMMENT '删除标志';
```

### 2. 检查部位使用统计表 (check_part_usage_stat)
```sql
CREATE TABLE `check_part_usage_stat` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `item_group_id` varchar(32) NOT NULL COMMENT '项目组合ID',
  `check_part_id` varchar(32) NOT NULL COMMENT '部位ID', 
  `usage_count` bigint DEFAULT 0 COMMENT '使用次数',
  `last_used_time` datetime DEFAULT NULL COMMENT '最后使用时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_group_part` (`item_group_id`, `check_part_id`)
);
```

### 3. 项目登记表扩展 (customer_reg_item_group)
```sql
ALTER TABLE `customer_reg_item_group` 
ADD COLUMN `check_part_id` varchar(32) DEFAULT NULL COMMENT '检查部位ID',
ADD COLUMN `check_part_name` varchar(100) DEFAULT NULL COMMENT '检查部位名称',
ADD COLUMN `parent_group_id` varchar(32) DEFAULT NULL COMMENT '父项目组ID';
```

## 核心功能实现

### 1. 前端部位选择流程

#### 项目选择检测
```typescript
async function handleSearchChange(options) {
  const selectedOption = options.option;
  if (selectedOption?.itemData) {
    const itemGroup = selectedOption.itemData;
    if (itemGroup.hasCheckPart === '1') {
      await showCheckPartSelector(itemGroup);
    } else {
      handleAddOne(itemGroup);
    }
  }
}
```

#### 部位选择器
```vue
<a-modal 
  title="选择检查部位" 
  v-model:open="checkPartState.visible" 
  @ok="confirmAddItemWithParts"
>
  <a-select
    v-model:value="checkPartState.selectedParts"
    mode="multiple"
    :filter-option="false"
    @search="searchCheckParts"
  >
    <a-select-option v-for="option in checkPartState.options">
      {{ option.label }}
      <span v-if="option.frequency > 0">
        ({{ option.frequency }}次)
      </span>
    </a-select-option>
  </a-select>
</a-modal>
```

### 2. 后端服务实现

#### 部位数据服务
```java
@Service
public class CheckPartDictServiceImpl implements ICheckPartDictService {
    
    @Cacheable(value = "checkPartByGroup")
    public List<CheckPartDict> listByItemGroupWithFrequency(String itemGroupId, String keyword) {
        // 1. 获取基础部位数据
        List<CheckPartDict> parts = getCheckPartsFromCache(keyword);
        
        // 2. 获取使用频次数据
        Map<String, Long> frequencyMap = getFrequencyMap(itemGroupId);
        
        // 3. 设置频次并排序
        parts.forEach(part -> {
            part.setFrequency(frequencyMap.getOrDefault(part.getId(), 0L));
        });
        
        // 4. 按频次降序排序
        return parts.stream()
            .sorted(Comparator.comparing(CheckPartDict::getFrequency).reversed())
            .collect(Collectors.toList());
    }
}
```

#### 项目添加服务
```java
@Transactional
public void addItemGroupWithCheckParts(AddItemGroupWithCheckPartsRequest request) {
    // 1. 验证参数和权限
    validateRequest(request);
    
    // 2. 按部位创建多条记录
    String parentGroupId = IdUtil.simpleUUID();
    List<CustomerRegItemGroup> groupList = new ArrayList<>();
    
    for (String checkPartId : request.getCheckPartIds()) {
        CheckPartDict checkPart = checkPartDictService.getById(checkPartId);
        CustomerRegItemGroup regGroup = createRegItemGroup(request, itemGroup);
        
        // 设置部位信息
        regGroup.setCheckPartId(checkPartId);
        regGroup.setCheckPartName(checkPart.getPartName());
        regGroup.setParentGroupId(parentGroupId);
        regGroup.setItemGroupName(itemGroup.getName() + "-" + checkPart.getPartName());
        
        groupList.add(regGroup);
    }
    
    // 3. 批量保存
    customerRegItemGroupService.saveBatch(groupList);
    
    // 4. 异步更新使用频次
    checkPartDictService.updateUsageFrequency(request.getItemGroupId(), request.getCheckPartIds());
}
```

## 缓存策略

### 1. Redis缓存设计
- **部位基础数据缓存**: `check_part:{keyword}` (24小时)
- **使用频次缓存**: `check_part_freq:{itemGroupId}` (24小时)
- **缓存预热**: 应用启动时预热热门项目数据

### 2. 缓存更新策略
- 新增使用记录时异步清除相关缓存
- 定时任务定期刷新热门数据
- 缓存穿透保护

## 性能优化

### 1. 数据库优化
- 为关键字段添加索引
- 使用批量操作减少数据库交互
- 异步处理统计数据更新

### 2. 前端优化
- 搜索防抖处理 (300ms)
- 选项数据懒加载
- 状态管理优化

### 3. 接口优化
- 分页查询大数据集
- 缓存热点数据
- 异步处理非关键操作

## 业务规则

### 1. 部位选择规则
- 只有`hasCheckPart=1`的项目才显示部位选择
- 支持多选部位，每个部位创建独立记录
- 相同项目-部位组合不能重复添加

### 2. 排序规则
- 按使用频次降序排列
- 频次相同时按部位名称升序
- 新部位默认频次为0

### 3. 数据关联规则
- 同一项目的不同部位通过`parent_group_id`关联
- 项目名称格式: `{项目名称}-{部位名称}`
- 保持原有项目的所有属性

## 扩展性设计

### 1. 部位分类支持
- 预留`category`字段支持部位分类
- 可按分类筛选部位选项

### 2. 权限控制
- 支持按角色控制部位选择权限
- 可配置特定项目的部位限制

### 3. 统计分析
- 使用频次统计支持数据分析
- 可生成部位使用报表

## 测试要点

### 1. 功能测试
- 部位选择流程完整性
- 多部位记录创建正确性
- 缓存数据一致性

### 2. 性能测试
- 大量部位数据加载性能
- 并发添加项目性能
- 缓存命中率测试

### 3. 异常测试
- 网络异常处理
- 数据库异常回滚
- 缓存失效处理

## 部署说明

### 1. 数据库脚本执行
```bash
# 1. 执行表创建脚本
mysql -u username -p database < check_part_dict.sql

# 2. 执行表结构修改脚本  
mysql -u username -p database < customer_reg_item_group_alter.sql
```

### 2. Redis配置
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 2000ms
```

### 3. 缓存配置
```java
@EnableCaching
@Configuration
public class CacheConfig {
    @Bean
    public CacheManager cacheManager() {
        RedisCacheManager.Builder builder = RedisCacheManager
            .RedisCacheManagerBuilder
            .fromConnectionFactory(redisConnectionFactory())
            .cacheDefaults(cacheConfiguration());
        return builder.build();
    }
}
```

## 维护说明

### 1. 日常维护
- 定期清理过期统计数据
- 监控缓存命中率
- 检查数据库索引效果

### 2. 故障排查
- 检查Redis连接状态
- 查看应用日志错误信息
- 验证数据库表结构完整性

### 3. 性能监控
- 监控API响应时间
- 统计缓存使用情况
- 分析数据库查询性能
