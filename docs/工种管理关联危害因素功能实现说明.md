# 工种管理关联危害因素功能实现说明

## 功能概述

为工种管理模块添加关联危害因素的功能，允许用户在工种维护时选择和管理该工种相关的危害因素，实现工种与危害因素的多对多关联关系。

## 实现架构

### 数据库设计

#### 关联表结构
```sql
CREATE TABLE `zy_risk_factor_worktype` (
  `id` varchar(50) NOT NULL COMMENT '主键',
  `worktype_id` varchar(50) NOT NULL COMMENT '工种ID',
  `risk_factor_id` varchar(50) NOT NULL COMMENT '危害因素ID',
  `risk_factor_name` varchar(100) DEFAULT NULL COMMENT '危害因素名称',
  `risk_factor_code` varchar(100) DEFAULT NULL COMMENT '危害因素代码',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  KEY `idx_worktype_id` (`worktype_id`),
  KEY `idx_risk_factor_id` (`risk_factor_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工种危害因素关联表';
```

### 后端实现

#### 1. 实体类
- **ZyRiskFactorWorktype.java**: 工种危害因素关联表实体类
- **ZyWorktypePage.java**: 工种页面对象，包含关联的危害因素列表

#### 2. 数据访问层
- **ZyWorktypeRiskFactorMapper.java**: 关联表Mapper接口
- **ZyWorktypeRiskFactorMapper.xml**: MyBatis映射文件

#### 3. 服务层
- **IZyWorktypeRiskFactorService.java**: 关联表服务接口
- **ZyWorktypeRiskFactorServiceImpl.java**: 关联表服务实现
- **IZyWorktypeService.java**: 扩展工种服务接口，添加主子表操作方法
- **ZyWorktypeServiceImpl.java**: 实现主子表操作逻辑

#### 4. 控制层
- **ZyWorktypeController.java**: 修改工种控制器，支持主子表操作

### 前端实现

#### 1. API接口
**文件**: `src/views/occu/ZyWorktype.api.ts`

新增API方法：
- `queryZyWorktypeRiskFactor`: 查询工种关联的危害因素
- `getRiskFactorList`: 查询危害因素列表

#### 2. 表单组件
**文件**: `src/views/occu/components/ZyWorktypeForm.vue`

主要功能：
- 添加危害因素选择区域
- 支持多选危害因素
- 支持搜索过滤
- 数据提交时构建关联关系

#### 3. 模态框组件
**文件**: `src/views/occu/components/ZyWorktypeModal.vue`

调整：
- 模态框宽度从800px调整为1000px，适应新增的危害因素配置区域

## 核心功能实现

### 1. 主子表操作

#### 保存操作
```java
@Override
@Transactional(rollbackFor = Exception.class)
public void saveMain(ZyWorktype zyWorktype, List<ZyRiskFactorWorktype> zyWorktypeRiskFactorList) {
    // 1. 保存主表数据
    zyWorktypeMapper.insert(zyWorktype);
    
    // 2. 保存子表数据
    if(zyWorktypeRiskFactorList != null && zyWorktypeRiskFactorList.size() > 0) {
        for(ZyRiskFactorWorktype entity : zyWorktypeRiskFactorList) {
            entity.setWorktypeId(zyWorktype.getId());
            zyWorktypeRiskFactorMapper.insert(entity);
        }
    }
}
```

#### 更新操作
```java
@Override
@Transactional(rollbackFor = Exception.class)
public void updateMain(ZyWorktype zyWorktype, List<ZyRiskFactorWorktype> zyWorktypeRiskFactorList) {
    // 1. 更新主表数据
    zyWorktypeMapper.updateById(zyWorktype);
    
    // 2. 删除原有关联关系
    zyWorktypeRiskFactorMapper.deleteByWorktypeId(zyWorktype.getId());
    
    // 3. 重新插入关联关系
    if(zyWorktypeRiskFactorList != null && zyWorktypeRiskFactorList.size() > 0) {
        for(ZyRiskFactorWorktype entity : zyWorktypeRiskFactorList) {
            entity.setWorktypeId(zyWorktype.getId());
            zyWorktypeRiskFactorMapper.insert(entity);
        }
    }
}
```

### 2. 前端数据处理

#### 数据提交
```javascript
// 构建关联的危害因素数据
const zyWorktypeRiskFactorList = selectedRiskFactors.value.map(factorId => {
  const factor = riskFactorList.value.find(f => f.id === factorId);
  return {
    riskFactorId: factorId,
    riskFactorName: factor?.name || '',
    riskFactorCode: factor?.code || ''
  };
});

// 构建提交数据
const submitData = {
  ...model,
  zyWorktypeRiskFactorList
};
```

#### 数据加载
```javascript
// 加载工种关联的危害因素
const loadWorktypeRiskFactors = async (worktypeId) => {
  try {
    const res = await queryZyWorktypeRiskFactor({ worktypeId });
    if (res.success) {
      selectedRiskFactors.value = res.result.map(item => item.riskFactorId);
    }
  } catch (error) {
    console.error('加载工种关联危害因素失败:', error);
  }
};
```

## 使用说明

### 1. 新增工种
1. 点击"新增"按钮打开工种维护弹窗
2. 填写工种基本信息（名称、代码、助记码等）
3. 在"关联危害因素配置"区域选择相关的危害因素
4. 点击"确定"保存工种及其关联的危害因素

### 2. 编辑工种
1. 点击工种列表中的"编辑"按钮
2. 修改工种基本信息
3. 在危害因素配置区域添加或移除关联的危害因素
4. 点击"确定"保存修改

### 3. 删除工种
- 删除工种时会自动删除其关联的危害因素关系

## 技术特点

1. **事务管理**: 使用`@Transactional`注解确保主子表操作的数据一致性
2. **级联删除**: 删除工种时自动清理关联关系
3. **搜索过滤**: 支持按危害因素名称和代码进行搜索
4. **多选支持**: 一个工种可以关联多个危害因素
5. **数据冗余**: 在关联表中保存危害因素名称和代码，提高查询效率

## 扩展性

该实现方案具有良好的扩展性：
- 可以轻松添加更多的关联字段
- 支持复杂的查询和统计需求
- 便于后续功能扩展（如权重、优先级等）

## 注意事项

1. 确保数据库中已创建`zy_risk_factor_worktype`关联表
2. 危害因素数据需要预先维护在`zy_risk_factor`表中
3. 删除危害因素时需要考虑对工种关联关系的影响
4. 建议定期清理无效的关联关系数据
