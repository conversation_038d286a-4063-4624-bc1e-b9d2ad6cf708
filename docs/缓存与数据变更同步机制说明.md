# 缓存与数据变更同步机制说明

## 概述

为了保证项目关系缓存与数据变更的一致性，我们设计了一套完整的缓存同步机制，包括自动同步、手动同步、事件驱动等多种方式。

## 核心问题

### 缓存不一致的场景
1. **多用户并发**：用户A修改了项目关系，用户B的缓存未更新
2. **跨页面操作**：在配置页面修改关系，在业务页面缓存未刷新
3. **数据库直接修改**：通过SQL直接修改数据，前端缓存未感知
4. **网络异常**：API调用失败但缓存未清理
5. **长时间使用**：缓存过期但未及时刷新

## 解决方案架构

### 1. 多层次缓存同步机制

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API拦截器     │    │   事件管理器    │    │   页面监听器    │
│  自动触发同步   │───▶│  统一事件处理   │───▶│  页面级同步     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   缓存管理器    │    │   手动同步      │    │   定时清理      │
│  智能失效处理   │    │  用户主动刷新   │    │  过期缓存清理   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2. 事件驱动的缓存同步

#### 事件类型定义
```javascript
export const CacheEvents = {
  ITEM_GROUP_UPDATED: 'itemGroupUpdated',           // 项目信息更新
  ITEM_GROUP_DELETED: 'itemGroupDeleted',           // 项目删除
  RELATION_UPDATED: 'relationUpdated',              // 项目关系更新
  RELATION_DELETED: 'relationDeleted',              // 项目关系删除
  CUSTOMER_REG_UPDATED: 'customerRegUpdated',       // 客户登记更新
  BATCH_OPERATION: 'batchOperation'                 // 批量操作
};
```

#### 事件处理流程
```javascript
// 1. API调用成功后自动触发事件
API调用 → 拦截器检测 → 提取变更数据 → 触发缓存事件

// 2. 事件管理器处理
缓存事件 → 事件监听器 → 缓存失效处理 → 相关缓存清理

// 3. 智能缓存刷新
缓存失效 → 按需重新加载 → 更新缓存 → 通知相关组件
```

## 核心组件

### 1. 项目关系管理器增强

#### 缓存失效处理
```javascript
/**
 * 使指定项目的缓存失效
 */
export function invalidateCache(itemGroupIds) {
  const ids = Array.isArray(itemGroupIds) ? itemGroupIds : [itemGroupIds];
  ids.forEach(id => {
    if (relationCache.has(id)) {
      relationCache.delete(id);
      console.log(`缓存失效: ${id}`);
    }
  });
}

/**
 * 使相关项目的缓存失效
 * 当项目关系发生变更时，需要使所有相关项目的缓存失效
 */
export async function invalidateRelatedCache(changedItemId) {
  // 1. 使当前项目的缓存失效
  invalidateCache(changedItemId);
  
  // 2. 查找所有可能受影响的项目
  const affectedItems = [];
  for (const [itemId, relations] of relationCache.entries()) {
    const isAffected = 
      relations.exclusiveGroups.includes(changedItemId) ||
      relations.dependentGroups.includes(changedItemId) ||
      relations.attachGroups.includes(changedItemId) ||
      relations.giftGroups.includes(changedItemId);
    
    if (isAffected) {
      affectedItems.push(itemId);
    }
  }
  
  // 3. 使受影响的项目缓存失效
  if (affectedItems.length > 0) {
    invalidateCache(affectedItems);
  }
}
```

#### 智能缓存刷新
```javascript
/**
 * 智能缓存刷新
 * 在数据可能发生变更的场景下调用
 */
export async function refreshCache(itemIds = []) {
  try {
    // 1. 清除指定项目的缓存
    if (itemIds.length > 0) {
      invalidateCache(itemIds);
    }
    
    // 2. 重新加载数据
    const promises = itemIds.map(id => getItemRelations(id));
    await Promise.all(promises);
    
    console.log('缓存刷新完成:', itemIds);
  } catch (error) {
    console.error('缓存刷新失败:', error);
  }
}
```

### 2. 缓存事件管理器

#### 事件监听和处理
```javascript
/**
 * 初始化缓存事件管理器
 */
export function initCacheEventManager() {
  // 项目信息更新时
  addEventListener(CacheEvents.ITEM_GROUP_UPDATED, async (data) => {
    const { itemGroupId } = data;
    if (itemGroupId) {
      await invalidateRelatedCache(itemGroupId);
    }
  });
  
  // 项目关系更新时
  addEventListener(CacheEvents.RELATION_UPDATED, (data) => {
    const { mainItemId, relationChanges } = data;
    if (mainItemId) {
      onRelationChanged(mainItemId, relationChanges);
    }
  });
  
  // 批量操作时
  addEventListener(CacheEvents.BATCH_OPERATION, async (data) => {
    const { operationType, affectedItemIds } = data;
    if (affectedItemIds && affectedItemIds.length > 0) {
      await refreshCache(affectedItemIds);
    }
  });
}
```

#### 页面级缓存同步
```javascript
/**
 * 页面级缓存同步
 * 在页面获得焦点时检查是否需要刷新缓存
 */
export function setupPageCacheSync() {
  // 监听页面可见性变化
  document.addEventListener('visibilitychange', () => {
    if (!document.hidden) {
      checkAndRefreshExpiredCache();
    }
  });
  
  // 监听窗口焦点变化
  window.addEventListener('focus', () => {
    checkAndRefreshExpiredCache();
  });
}
```

### 3. API缓存拦截器

#### 自动缓存同步
```javascript
/**
 * 需要触发缓存同步的API路径配置
 */
const CACHE_SYNC_APIS = {
  '/basicinfo/itemGroup/edit': {
    event: CacheEvents.ITEM_GROUP_UPDATED,
    extractData: (response, requestData) => ({
      itemGroupId: requestData.id
    })
  },
  
  '/basicinfo/itemGroupRelation/addRelationGroupBatch': {
    event: CacheEvents.RELATION_UPDATED,
    extractData: (response, requestData) => ({
      mainItemId: requestData.mainId,
      relationChanges: {
        exclusiveGroups: requestData.exclusiveGroups || [],
        dependentGroups: requestData.dependentGroups || [],
        attachGroups: requestData.attachGroups || [],
        giftGroups: requestData.giftGroups || []
      }
    })
  }
};

/**
 * 安装API缓存拦截器
 */
export function installApiCacheInterceptor(httpInstance) {
  httpInstance.interceptors.response.use(
    (response) => {
      handleCacheSync(response);
      return response;
    },
    (error) => {
      console.error('API请求失败:', error);
      return Promise.reject(error);
    }
  );
}
```

## 使用场景

### 1. 项目关系配置页面

```javascript
// 保存配置时触发缓存同步
async function saveRelationConfig() {
  const res = await addRelationGroupBatch(payload);
  if (res.success) {
    // 触发缓存同步事件
    emitCacheEvent(CacheEvents.RELATION_UPDATED, {
      mainItemId: props.itemGroupId,
      relationChanges: {
        exclusiveGroups: payload.exclusiveGroups,
        dependentGroups: payload.dependentGroups,
        attachGroups: payload.attachGroups,
        giftGroups: payload.giftGroups
      }
    });
  }
}
```

### 2. 项目添加页面

```javascript
// 页面加载时预加载缓存
onMounted(() => {
  preloadCommonItemRelations();
});

// 添加项目成功后自动刷新相关缓存
async function addItemSuccess() {
  // API拦截器会自动处理缓存同步
  // 无需手动调用缓存刷新
}
```

### 3. 手动缓存管理

```javascript
// 提供给用户的手动刷新接口
async function manualRefreshCache() {
  const success = await manualCacheSync();
  if (success) {
    message.success('缓存刷新成功');
  } else {
    message.error('缓存刷新失败');
  }
}
```

## 性能优化

### 1. 智能缓存失效

```javascript
// 只失效真正受影响的缓存
export async function invalidateRelatedCache(changedItemId) {
  // 精确计算受影响的项目
  const affectedItems = [];
  for (const [itemId, relations] of relationCache.entries()) {
    if (isRelatedTo(relations, changedItemId)) {
      affectedItems.push(itemId);
    }
  }
  
  // 批量失效，避免逐个处理
  invalidateCache(affectedItems);
}
```

### 2. 按需加载

```javascript
// 只在需要时重新加载缓存
export async function refreshCache(itemIds = []) {
  // 过滤出真正需要刷新的项目
  const needRefresh = itemIds.filter(id => !relationCache.has(id));
  
  if (needRefresh.length > 0) {
    const promises = needRefresh.map(id => getItemRelations(id));
    await Promise.all(promises);
  }
}
```

### 3. 定时清理

```javascript
// 定期清理过期缓存
export function startCacheCleanup() {
  setInterval(() => {
    const now = Date.now();
    if (now >= cacheExpireTime && relationCache.size > 0) {
      console.log('清理过期缓存');
      clearRelationCache();
    }
  }, 5 * 60 * 1000);
}
```

## 监控和调试

### 1. 缓存统计

```javascript
// 获取缓存统计信息
const stats = getCacheStats();
console.log('缓存大小:', stats.cacheSize);
console.log('过期时间:', stats.cacheExpireTime);
console.log('是否过期:', stats.isExpired);
```

### 2. 调试模式

```javascript
// 启用缓存同步调试
enableCacheSyncDebug();

// 所有缓存同步事件都会在控制台打印详细信息
// 🔄 缓存同步事件
// 事件类型: relationUpdated
// 事件数据: { mainItemId: "123", relationChanges: {...} }
// 时间: 14:30:25
```

### 3. 性能监控

```javascript
// 监控缓存命中率
const hitRate = cacheHits / (cacheHits + cacheMisses);
console.log('缓存命中率:', hitRate);

// 监控API调用减少情况
const apiReduction = (originalCalls - currentCalls) / originalCalls;
console.log('API调用减少:', apiReduction);
```

## 相关文件

### 核心文件
- `src/utils/itemGroupRelationManager.js` - 增强的项目关系管理器
- `src/utils/cacheEventManager.js` - 缓存事件管理器
- `src/utils/apiCacheInterceptor.js` - API缓存拦截器

### 配置文件
- `src/main.ts` - 应用启动时初始化缓存管理器
- `src/utils/http/axios/index.ts` - HTTP拦截器配置

### 使用文件
- `src/views/basicinfo/components/ItemGroupRelationForm.vue` - 项目关系配置页面
- `src/views/reg/components/CustomerRegGroupPannel.vue` - 项目添加页面
- `src/views/reg/GroupListOfPannel.vue` - 项目列表页面

## 总结

通过这套完整的缓存同步机制，我们实现了：

✅ **自动同步**：API调用后自动触发缓存同步
✅ **智能失效**：精确计算受影响的缓存项
✅ **事件驱动**：统一的事件管理和处理
✅ **页面感知**：页面焦点变化时自动检查缓存
✅ **手动控制**：提供用户手动刷新接口
✅ **性能优化**：按需加载，定时清理
✅ **监控调试**：完整的监控和调试工具

这套机制确保了缓存与数据变更的强一致性，同时保持了良好的性能和用户体验。
