# 统一项目关系管理器说明

## 概述

基于 `getRelationGroupsByMainId` API 一次性获取所有项目关系数据的特点，我们设计了统一的项目关系管理器，实现了互斥、依赖、附属、赠送项目关系的统一缓存和管理。

## 设计理念

### 核心思想：一次获取，多次使用

```javascript
// 一次API调用获取所有关系数据
const relations = await getItemRelations(itemGroupId);

// 多种用途
const mutexItems = relations.exclusiveGroups;    // 互斥检查
const dependentItems = relations.dependentGroups; // 依赖检查
const attachItems = relations.attachGroups;       // 附属项目
const giftItems = relations.giftGroups;           // 赠送项目
```

### 优势对比

#### 原始方案（分散管理）
```javascript
// ❌ 多个独立的工具和缓存
import { checkItemMutex } from '@/utils/itemGroupMutexCheck';
import { DependencyChecker } from '@/utils/DependencyChecker';
// 每个功能独立调用API，重复获取数据
```

#### 新方案（统一管理）
```javascript
// ✅ 统一的关系管理器
import { 
  checkItemMutex, 
  checkItemDependencies,
  getAttachItems,
  getGiftItems 
} from '@/utils/itemGroupRelationManager';
// 一次API调用，缓存所有关系数据
```

## 核心功能

### 1. 统一数据获取和缓存

#### 数据结构
```javascript
const relations = {
  exclusiveGroups: ["itemId1", "itemId2"],    // 互斥项目ID列表
  dependentGroups: ["itemId3", "itemId4"],    // 依赖项目ID列表
  attachGroups: ["itemId5", "itemId6"],       // 附属项目ID列表
  giftGroups: ["itemId7", "itemId8"]          // 赠送项目ID列表
};
```

#### 缓存机制
```javascript
// 5分钟缓存，避免重复API调用
let relationCache = new Map();
const CACHE_DURATION = 5 * 60 * 1000;

async function getItemRelations(itemGroupId) {
  // 检查缓存
  if (relationCache.has(itemGroupId) && now < cacheExpireTime) {
    return relationCache.get(itemGroupId);
  }
  
  // 获取并缓存数据
  const res = await getRelationGroupsByMainId({ mainId: itemGroupId });
  const relations = extractRelationData(res);
  relationCache.set(itemGroupId, relations);
  
  return relations;
}
```

### 2. 互斥检查功能

```javascript
export async function checkItemMutex(newItems, existingItems) {
  const conflicts = [];
  
  for (const newItem of newItems) {
    // 获取项目关系数据（使用缓存）
    const relations = await getItemRelations(newItem.itemGroupId);
    const mutexItems = relations.exclusiveGroups;
    
    // 检查与现有项目的冲突
    for (const existingItem of existingItems) {
      if (mutexItems.includes(existingItem.itemGroupId)) {
        conflicts.push({
          newItem: { id: newItem.itemGroupId, name: newItem.itemGroupName },
          existingItem: { id: existingItem.itemGroupId, name: existingItem.itemGroupName },
          reason: '项目互斥'
        });
      }
    }
  }
  
  return { isValid: conflicts.length === 0, conflicts };
}
```

### 3. 依赖检查功能

```javascript
export async function checkItemDependencies(newItems, existingItems) {
  const missingDependencies = [];
  
  for (const newItem of newItems) {
    // 获取项目关系数据（使用缓存）
    const relations = await getItemRelations(newItem.itemGroupId);
    const dependentItems = relations.dependentGroups;
    
    // 检查依赖项目是否存在
    for (const dependentId of dependentItems) {
      const dependentExists = existingItems.some(item => 
        item.itemGroupId === dependentId && 
        item.addMinusFlag !== -1
      );
      
      if (!dependentExists) {
        missingDependencies.push({
          itemId: newItem.itemGroupId,
          itemName: newItem.itemGroupName,
          dependentId: dependentId
        });
      }
    }
  }
  
  return { isValid: missingDependencies.length === 0, missing: missingDependencies };
}
```

### 4. 附属和赠送项目获取

```javascript
// 获取附属项目
export async function getAttachItems(mainItems) {
  const attachItems = [];
  
  for (const mainItem of mainItems) {
    const relations = await getItemRelations(mainItem.itemGroupId);
    attachItems.push(...relations.attachGroups);
  }
  
  return [...new Set(attachItems)]; // 去重
}

// 获取赠送项目
export async function getGiftItems(mainItems) {
  const giftItems = [];
  
  for (const mainItem of mainItems) {
    const relations = await getItemRelations(mainItem.itemGroupId);
    giftItems.push(...relations.giftGroups);
  }
  
  return [...new Set(giftItems)]; // 去重
}
```

## 前端集成

### 1. 导入统一管理器

```javascript
import { 
  checkItemMutex, 
  formatConflictMessage, 
  checkItemDependencies,
  formatDependencyMessage,
  preloadRelationData 
} from '@/utils/itemGroupRelationManager';
```

### 2. 添加项目时的使用

```javascript
async function confirmAddItemWithParts() {
  // 1. 前端互斥检查
  const mutexCheck = await checkItemMutex(itemGroups, regGroupDataSource.value);
  if (!mutexCheck.isValid) {
    const conflictMsg = formatConflictMessage(mutexCheck.conflicts);
    message.error('项目冲突：\n' + conflictMsg);
    return;
  }
  
  // 2. 发送到后端
  const res = await addItemGroupWithCheckParts(params);
  
  if (res.success) {
    // 3. 刷新列表
    await fetchCustomerRegGroupList(selectedCustomerReg.value.id);
    
    // 4. 依赖检查提醒
    await checkDependenciesAfterAdd(itemGroups);
  }
}

// 依赖检查提醒
async function checkDependenciesAfterAdd(addedItems) {
  const dependencyCheck = await checkItemDependencies(addedItems, regGroupDataSource.value);
  
  if (!dependencyCheck.isValid) {
    const dependencyMsg = formatDependencyMessage(dependencyCheck.missing);
    message.warning({
      content: `检测到依赖项目缺失：\n${dependencyMsg}\n建议添加相关依赖项目以确保检查流程完整`,
      duration: 8,
    });
  }
}
```

### 3. 预加载机制

```javascript
// 页面加载时预加载常用项目的关系数据
onMounted(() => {
  preloadCommonItemRelations();
});

async function preloadCommonItemRelations() {
  // 获取当前客户已有的项目ID列表
  const existingItemIds = regGroupDataSource.value
    .filter(item => item.addMinusFlag !== -1)
    .map(item => item.itemGroupId);
  
  // 预加载这些项目的关系数据
  if (existingItemIds.length > 0) {
    await preloadRelationData([...new Set(existingItemIds)]);
    console.log('预加载项目关系数据完成');
  }
}
```

## 性能优化

### 1. 缓存命中率提升

#### 原始方案
```javascript
// 每个功能独立缓存，缓存利用率低
mutexCache.set(itemId, mutexData);
dependencyCache.set(itemId, dependencyData);
// 同一个项目的数据被多次获取和缓存
```

#### 新方案
```javascript
// 统一缓存，一次获取所有关系数据
relationCache.set(itemId, {
  exclusiveGroups: [...],
  dependentGroups: [...],
  attachGroups: [...],
  giftGroups: [...]
});
// 一次缓存，多次使用
```

### 2. API调用次数减少

#### 性能对比
```
原始方案：
- 互斥检查：1次API调用
- 依赖检查：1次API调用  
- 附属项目：1次API调用
- 赠送项目：1次API调用
总计：4次API调用

新方案：
- 所有功能：1次API调用
总计：1次API调用

性能提升：75%的API调用减少
```

### 3. 内存使用优化

```javascript
// 统一的数据结构，避免重复存储
const relations = {
  exclusiveGroups: ["id1", "id2"],  // 只存储ID，节省内存
  dependentGroups: ["id3", "id4"],  // 避免存储完整对象
  attachGroups: ["id5", "id6"],
  giftGroups: ["id7", "id8"]
};
```

## 工具方法

### 1. 缓存管理

```javascript
// 清除缓存
clearRelationCache();

// 获取缓存统计
const stats = getCacheStats();
console.log('缓存大小:', stats.cacheSize);
console.log('过期时间:', stats.cacheExpireTime);
console.log('是否过期:', stats.isExpired);
```

### 2. 预加载

```javascript
// 预加载指定项目的关系数据
await preloadRelationData(['itemId1', 'itemId2', 'itemId3']);

// 预加载当前客户的所有项目关系
const itemIds = regGroupDataSource.value.map(item => item.itemGroupId);
await preloadRelationData([...new Set(itemIds)]);
```

### 3. 消息格式化

```javascript
// 格式化互斥冲突消息
const conflictMsg = formatConflictMessage(conflicts);
// 输出："胸部CT-胸部" 与 "胸部增强CT-胸部" 互斥

// 格式化依赖缺失消息
const dependencyMsg = formatDependencyMessage(missing);
// 输出：项目"心脏彩超-心脏"缺少依赖项目：心电图
```

## 相关文件

### 核心文件
- `src/utils/itemGroupRelationManager.js` - 统一项目关系管理器

### 使用文件
- `src/views/reg/components/CustomerRegGroupPannel.vue` - 项目添加页面
- `src/views/reg/GroupListOfPannel.vue` - 项目列表页面

### 依赖文件
- `src/views/basicinfo/ItemGroup.api.ts` - 项目关系API

### 文档文件
- `docs/统一项目关系管理器说明.md` - 本文档

## 总结

通过统一项目关系管理器，我们实现了：

✅ **API调用优化**：减少75%的API调用次数
✅ **缓存效率提升**：统一缓存，一次获取多次使用
✅ **代码简化**：统一的接口，简化前端逻辑
✅ **性能提升**：预加载机制，提升用户体验
✅ **维护性增强**：集中管理，便于维护和扩展

这个设计充分利用了 `getRelationGroupsByMainId` API 的特点，实现了高效的项目关系管理。
