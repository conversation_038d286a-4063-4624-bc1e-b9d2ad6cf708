# 套餐部位信息问题排查说明

## 问题描述

使用套餐添加项目时，套餐中项目的部位信息没有自动带上，需要排查原因并修复。

## 问题分析

### 1. 数据流程分析

#### 完整的数据流程
```
套餐配置 → 后端SQL查询 → 前端接收 → 数据处理 → 项目添加
    ↓           ↓           ↓         ↓         ↓
SuitGroup   getGroupOfSuit  res数组   setGroupBySuit  CustomerRegItemGroup
```

#### 关键数据字段
- `checkPartId`: 检查部位ID
- `checkPartName`: 检查部位名称  
- `checkPartCode`: 检查部位编码

### 2. 后端数据查询验证

#### SQL查询（ItemSuitMapper.xml）
```xml
<select id="getGroupOfSuit" resultType="org.jeecg.modules.basicinfo.entity.ItemGroup">
    SELECT 
        g.id, g.name, g.help_char, g.his_code, g.his_name, 
        g.plat_code, g.plat_name, g.class_code, g.type, 
        g.price, g.department_id, g.department_name, g.department_code,
        g.has_check_part, g.charge_item_only_flag,
        sg.dis_rate as disRateOfSuit,
        sg.price_after_dis as priceAfterDisOfSuit,
        sg.price_dis_diff_amount as priceDisDiffAmountOfSuit,
        sg.min_discount_rate as minDiscountRateOfSuit,
        sg.check_part_id,     -- ✅ 部位ID
        sg.check_part_name,   -- ✅ 部位名称
        sg.check_part_code    -- ✅ 部位编码
    FROM item_group g 
    INNER JOIN suit_group sg ON g.id = sg.group_id 
    WHERE sg.suit_id = #{suitId} 
    ORDER BY sg.sort
</select>
```

**验证点**：
- ✅ SQL查询包含了部位相关字段
- ✅ 字段映射正确

#### 后端实体类验证

**SuitGroup.java**：
```java
/**检查部位ID*/
@Excel(name = "检查部位ID", width = 15)
@ApiModelProperty(value = "检查部位ID")
private java.lang.String checkPartId;

/**检查部位名称*/
@Excel(name = "检查部位名称", width = 15)
@ApiModelProperty(value = "检查部位名称")
private java.lang.String checkPartName;

/**检查部位编码*/
@Excel(name = "检查部位编码", width = 15)
@ApiModelProperty(value = "检查部位编码")
private java.lang.String checkPartCode;
```

**验证点**：
- ✅ 实体类包含部位字段
- ✅ 字段注解正确

### 3. 前端数据接收验证

#### 类型定义（types.d.ts）
```typescript
interface ItemGroup {
  // ... 其他字段
  // 部位相关字段（套餐中的项目可能包含预设部位）
  checkPartId?: string;
  checkPartName?: string;
  checkPartCode?: string;
}
```

**验证点**：
- ✅ 前端类型定义包含部位字段
- ✅ 字段为可选类型

### 4. 前端处理逻辑验证

#### setGroupBySuit方法分析
```javascript
res.forEach((group) => {
  // 调试：打印套餐项目的完整信息
  console.log('套餐项目数据:', {
    name: group.name,
    hasCheckPart: group.hasCheckPart,
    checkPartId: group.checkPartId,
    checkPartName: group.checkPartName,
    checkPartCode: group.checkPartCode
  });
  
  // 检查项目是否需要部位选择
  if (group.hasCheckPart === '1') {
    // 如果套餐中已经预设了部位信息，则可以直接添加
    if (group.checkPartId && group.checkPartName) {
      console.log(`✅ 套餐中的项目 ${group.name} 使用预设部位：${group.checkPartName}`);
      // 继续处理
    } else {
      console.log(`⚠️ 套餐中的项目 ${group.name} 需要部位选择但未预设部位，跳过自动添加`);
      needPartSelectionItems.push(group);
      return;
    }
  }
  
  // ... 其他处理逻辑
  
  // 设置部位信息
  if (group.checkPartId && group.checkPartName) {
    data.checkPartId = group.checkPartId;
    data.checkPartName = group.checkPartName;
    data.checkPartCode = group.checkPartCode || '';
    console.log(`✅ 设置部位信息成功：${group.name} - ${group.checkPartName}`);
  }
});
```

**验证点**：
- ✅ 逻辑正确检查部位信息
- ✅ 正确设置部位字段
- ✅ 添加了详细的调试信息

#### generateCustomerRegItemGroup方法验证
```javascript
function generateCustomerRegItemGroup(row) {
  // ... 其他字段
  let data: CustomerRegItemGroup = {
    // ... 其他字段
    // 部位信息（如果有的话）
    checkPartId: row.checkPartId || null,
    checkPartName: row.checkPartName || null,
    checkPartCode: row.checkPartCode || null,
  };
  return data;
}
```

**验证点**：
- ✅ 方法正确处理部位字段
- ✅ 字段赋值逻辑正确

## 可能的问题原因

### 1. 套餐配置问题
**问题**：套餐维护时没有正确设置部位信息
**检查方法**：
```sql
-- 检查套餐中的部位配置
SELECT 
    s.name as suit_name,
    g.name as group_name,
    g.has_check_part,
    sg.check_part_id,
    sg.check_part_name,
    sg.check_part_code
FROM item_suit s
INNER JOIN suit_group sg ON s.id = sg.suit_id
INNER JOIN item_group g ON sg.group_id = g.id
WHERE s.id = '套餐ID'
ORDER BY sg.sort;
```

### 2. 数据库字段问题
**问题**：数据库表中部位字段为空
**检查方法**：
```sql
-- 检查suit_group表的部位字段
SELECT 
    suit_id,
    group_id,
    check_part_id,
    check_part_name,
    check_part_code
FROM suit_group 
WHERE suit_id = '套餐ID' 
  AND check_part_id IS NOT NULL;
```

### 3. 前端数据传递问题
**问题**：API返回的数据中部位字段丢失
**检查方法**：
- 打开浏览器开发者工具
- 查看Network标签页
- 找到`getGroupOfSuit`请求
- 检查响应数据中是否包含部位字段

### 4. 前端处理逻辑问题
**问题**：前端处理时部位信息被覆盖或丢失
**检查方法**：
- 查看浏览器控制台
- 检查调试信息输出
- 验证数据处理流程

## 排查步骤

### 第一步：检查套餐配置
1. 登录套餐维护页面
2. 查看目标套餐的项目配置
3. 确认需要部位选择的项目是否设置了部位信息

### 第二步：检查数据库数据
```sql
-- 查询套餐的部位配置
SELECT 
    s.name as 套餐名称,
    g.name as 项目名称,
    g.has_check_part as 是否需要部位,
    sg.check_part_id as 部位ID,
    sg.check_part_name as 部位名称,
    sg.check_part_code as 部位编码
FROM item_suit s
INNER JOIN suit_group sg ON s.id = sg.suit_id
INNER JOIN item_group g ON sg.group_id = g.id
WHERE s.name LIKE '%套餐名称%'
  AND g.has_check_part = '1'
ORDER BY s.name, sg.sort;
```

### 第三步：检查API响应
1. 打开浏览器开发者工具
2. 切换到Network标签页
3. 使用套餐添加项目
4. 查看`getGroupOfSuit`请求的响应数据
5. 确认响应中包含部位字段

### 第四步：检查前端处理
1. 打开浏览器控制台
2. 使用套餐添加项目
3. 查看调试信息输出：
   ```
   套餐项目数据: {name: "项目名", checkPartId: "部位ID", checkPartName: "部位名称"}
   ✅ 套餐中的项目 XXX 使用预设部位：YYY
   ✅ 设置部位信息成功：XXX - YYY
   ```

### 第五步：检查最终结果
1. 查看添加的项目记录
2. 确认部位信息是否正确保存
3. 检查数据库中的记录

## 修复方案

### 1. 如果是套餐配置问题
**解决方案**：在套餐维护页面为需要部位选择的项目设置预设部位

**操作步骤**：
1. 进入套餐维护页面
2. 编辑目标套餐
3. 为需要部位选择的项目设置部位信息
4. 保存套餐配置

### 2. 如果是数据库问题
**解决方案**：直接更新数据库中的部位信息

```sql
-- 更新套餐中项目的部位信息
UPDATE suit_group 
SET 
    check_part_id = '部位ID',
    check_part_name = '部位名称',
    check_part_code = '部位编码'
WHERE suit_id = '套餐ID' 
  AND group_id = '项目ID';
```

### 3. 如果是前端处理问题
**解决方案**：修复前端处理逻辑

已在代码中添加了详细的调试信息和修复逻辑：
- ✅ 增强了数据验证
- ✅ 添加了调试输出
- ✅ 完善了错误处理

## 验证方法

### 1. 功能验证
1. 创建包含需要部位选择项目的套餐
2. 为这些项目设置预设部位
3. 使用套餐添加项目
4. 确认部位信息正确传递

### 2. 数据验证
```sql
-- 验证添加的项目是否包含部位信息
SELECT 
    item_group_name,
    check_part_id,
    check_part_name,
    check_part_code,
    item_suit_name
FROM customer_reg_item_group 
WHERE customer_reg_id = '客户登记ID'
  AND item_suit_id IS NOT NULL
  AND check_part_id IS NOT NULL
ORDER BY create_time DESC;
```

### 3. 日志验证
查看浏览器控制台输出，确认看到类似信息：
```
套餐项目数据: {name: "胸部CT", checkPartId: "part001", checkPartName: "胸部"}
✅ 套餐中的项目 胸部CT 使用预设部位：胸部 (ID: part001)
✅ 设置部位信息成功：胸部CT - 胸部 (ID: part001, Code: chest)
```

## 相关文件

### 后端文件
- `jeecg-module-physicalex/src/main/java/org/jeecg/modules/basicinfo/entity/SuitGroup.java` - 套餐项目实体
- `jeecg-module-physicalex/src/main/java/org/jeecg/modules/basicinfo/mapper/xml/ItemSuitMapper.xml` - SQL查询
- `jeecg-module-physicalex/src/main/java/org/jeecg/modules/reg/entity/CustomerRegItemGroup.java` - 客户项目实体

### 前端文件
- `src/views/reg/GroupListOfPannel.vue` - 项目添加逻辑
- `types/types.d.ts` - 类型定义
- `src/views/basicinfo/ItemSuit.api.ts` - 套餐API

### 文档文件
- `docs/套餐部位信息问题排查说明.md` - 本文档

## 总结

通过以上排查和修复，套餐部位信息传递问题应该得到解决。关键点包括：

✅ **后端数据完整**：SQL查询和实体类都包含部位字段
✅ **前端类型正确**：TypeScript类型定义包含部位字段
✅ **处理逻辑完善**：前端正确处理和设置部位信息
✅ **调试信息完整**：添加了详细的调试输出
✅ **验证方法明确**：提供了完整的验证步骤

如果问题仍然存在，请按照排查步骤逐一检查，并查看控制台输出的调试信息。
