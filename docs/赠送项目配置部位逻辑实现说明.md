# 赠送项目配置部位逻辑实现说明

## 概述

本次实现为项目关系维护面板添加了赠送项目的逻辑，包含部位信息支持，并增强了前端页面的自动添加功能。赠送项目在添加主项目时会自动添加，并且充分考虑了主项目和赠送项目的附属项目关系。

## 实现内容

### 1. 数据库层面修改

#### 1.1 CustomerRegItemGroup实体类扩展
在 `CustomerRegItemGroup.java` 中添加了赠送基础项目ID字段：

```java
@ApiModelProperty(value = "赠送基础项目ID")
private String giftBaseId;
```

#### 1.2 数据库表结构修改
创建了数据库迁移脚本 `customer_reg_item_group_add_gift_base_id.sql`，为 `customer_reg_item_group` 表添加 `gift_base_id` 字段和索引。

### 2. 后端服务层修改

#### 2.1 GroupRelationVO 扩展
添加了赠送项目配置字段：
```java
// 新增：赠送项目配置
private List<ItemGroupRelation> giftGroups;
```

#### 2.2 IItemGroupRelationService 接口扩展
添加了获取赠送项目的方法：
```java
List<CustomerRegItemGroup> getGiftGroups(List<CustomerRegItemGroup> addingItemGroups);
```

#### 2.3 ItemGroupRelationServiceImpl 增强
- **getGiftGroups()**: 获取赠送项目列表，支持部位匹配
- **filterGiftGroupsByPart()**: 根据主项目部位筛选匹配的赠送项目
- **setGiftGroupPartInfo()**: 为赠送项目设置部位信息和标记
- **getRelationGroupsByMainId()**: 修改以支持赠送关系查询
- **saveRelationGroupBatch()**: 修改以支持赠送关系保存

#### 2.4 CustomerRegServiceImpl 增强
在添加项目时同时处理赠送项目：
- 在处理附属项目后，获取所有项目（包括主项目和附属项目）的赠送项目
- 自动添加赠送项目到项目列表
- 验证赠送项目的互斥关系

### 3. 前端界面修改

#### 3.1 ItemGroupRelationForm.vue 增强
- 添加了赠送项目配置区域
- 支持主项目部位和赠送项目部位选择
- 重新调整了布局，将原来的3列改为4列布局
- 添加了赠送项目的数据管理和事件处理

#### 3.2 新增功能
- **赠送项目配置**: 支持配置主项目部位对应的赠送项目及其部位
- **部位匹配**: 根据主项目部位自动筛选匹配的赠送项目
- **免费标记**: 赠送项目自动设置为免费（价格为0）
- **名称标识**: 赠送项目名称自动添加"(赠送)"标识

### 4. 业务逻辑说明

#### 4.1 赠送项目维护逻辑
用户可以配置：
- 当前项目的A部位 → 赠送项目B的A部位（数量1个）
- 当前项目的A部位 → 赠送项目B的B部位（数量1个）
- 当前项目的A部位 → 赠送项目C的D部位（数量2个）

#### 4.2 自动添加逻辑
当在CustomerRegGroupPannel.vue和GroupListOfPannel.vue中添加主项目时：
1. 系统首先添加主项目
2. 根据主项目获取并添加附属项目
3. 根据所有项目（主项目+附属项目）获取并添加赠送项目
4. 赠送项目会自动设置为免费，并标记赠送来源

#### 4.3 部位匹配规则
- **精确匹配**: 主项目部位ID必须与配置中的主项目部位ID完全一致
- **通配匹配**: 如果配置中没有指定主项目部位，则匹配所有部位
- **部位传递**: 赠送项目会继承配置中指定的部位信息

### 5. 赠送项目特性

#### 5.1 价格处理
- 赠送项目的价格自动设置为0.0
- 折后价格也设置为0.0
- 不影响原项目的价格计算

#### 5.2 标识处理
- 项目名称自动添加"(赠送)"后缀
- 如果有部位信息，格式为："项目名-部位名(赠送)"
- 通过giftBaseId字段关联到赠送来源项目

#### 5.3 关系处理
- 赠送项目也可以有自己的附属项目
- 赠送项目参与互斥关系验证
- 支持级联赠送（赠送项目的附属项目也会被添加）

## 使用说明

### 1. 配置赠送项目
1. 在项目管理页面选择主项目
2. 在赠送项目配置区域：
   - 选择主项目部位（可选）
   - 选择赠送项目
   - 选择赠送项目部位（可选）
   - 设置数量
3. 保存配置

### 2. 添加项目时的自动处理
当用户添加带有部位信息的主项目时，系统会：
1. 添加主项目
2. 查找并添加主项目的附属项目
3. 查找并添加所有项目的赠送项目
4. 赠送项目自动设置为免费并标记

## 技术要点

### 1. 级联处理
- 主项目 → 附属项目 → 赠送项目的级联添加
- 确保所有相关项目都被正确添加

### 2. 部位匹配
- 支持精确部位匹配和通配匹配
- 部位信息的完整传递和显示

### 3. 价格处理
- 赠送项目价格自动归零
- 不影响其他项目的价格计算

### 4. 标识管理
- 清晰的赠送项目标识
- 通过数据库字段追踪赠送关系

## 部署说明

### 1. 数据库迁移
执行以下SQL脚本：
```sql
-- 执行 docs/database/customer_reg_item_group_add_gift_base_id.sql
```

### 2. 代码部署
- 后端代码已完成修改，重新编译部署即可
- 前端代码已完成修改，重新构建部署即可

### 3. 功能验证
1. 登录系统，进入项目管理页面
2. 配置项目的赠送关系
3. 在项目添加页面测试自动添加赠送项目功能
4. 验证赠送项目的价格和标识是否正确

## 实现状态

✅ **已完成**:
- GroupRelationVO扩展（添加giftGroups字段）
- CustomerRegItemGroup实体扩展（添加giftBaseId字段）
- 数据库迁移脚本创建
- ItemGroupRelationServiceImpl赠送项目逻辑
- CustomerRegServiceImpl级联添加逻辑
- ItemGroupRelationForm.vue界面改造
- 完整的实现文档

⚠️ **需要手动执行**:
- 数据库迁移脚本（需要在数据库管理工具中手动执行）

## 后续优化建议

1. **批量配置**: 支持批量设置多个项目的赠送关系
2. **赠送规则**: 支持更复杂的赠送规则（如满额赠送）
3. **统计分析**: 添加赠送项目的使用统计
4. **模板功能**: 提供常用赠送配置模板
5. **审批流程**: 为赠送项目配置添加审批机制
