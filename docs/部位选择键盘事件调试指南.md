# 部位选择键盘事件调试指南

## 问题描述

在快捷搜索联动部位选择功能中，用户反馈：
1. 按 `Ctrl+Enter` 时已选部位被清空
2. 点击添加按钮虽然有成功提示，但项目列表没有变化

## 问题分析

### 问题1: Ctrl+Enter 清空已选部位

**根本原因：**
1. `event.preventDefault()` 阻止了部位选择器的状态更新
2. 键盘事件处理时机过早，状态还未完成更新
3. Vue 的响应式更新和 DOM 事件处理的时序问题

**解决方案：**
1. 使用 `setTimeout(100ms)` 延迟执行，给足够时间让状态更新
2. 在方法开始时保存状态副本，避免执行过程中被清空
3. 从选择器实例获取值作为备用方案

### 问题2: API调用失败 "参数不能为空"

**根本原因：**
1. API参数结构不正确：后端期望 `customerRegId` 和 `itemGroups` 数组
2. 我们之前传递的是 `regId`、`itemGroupId` 和 `checkPartIds`
3. 缺少完整的 `CustomerRegItemGroup` 数据结构

**解决方案：**
1. 使用 `generateCustomerRegItemGroup()` 方法构造完整的数据结构
2. 为每个部位创建独立的 `CustomerRegItemGroup` 对象
3. 使用正确的API参数格式：`{ customerRegId, itemGroups }`

### 问题3: 项目列表不刷新

**原因：**
1. 之前API调用失败，所以没有实际添加项目
2. 现在API调用成功后，列表应该能正常刷新

## 当前实现

### 键盘事件处理
```javascript
function handlePartSelectKeydown(event: KeyboardEvent) {
  if (event.ctrlKey && event.key === 'Enter') {
    console.log('Ctrl+Enter pressed, current parts before:', partSearchState.selectedParts);
    
    // 立即获取当前选择器的值
    const selectInstance = partSelectRef.value;
    
    setTimeout(() => {
      console.log('Delayed execution, current parts:', partSearchState.selectedParts);
      
      // 如果状态为空，尝试从选择器实例获取值
      if ((!partSearchState.selectedParts || partSearchState.selectedParts.length === 0) && selectInstance?.value) {
        partSearchState.selectedParts = Array.isArray(selectInstance.value) ? selectInstance.value : [selectInstance.value];
      }
      
      handleQuickAdd();
    }, 100);
    
    event.preventDefault();
    event.stopPropagation();
  }
}
```

### 状态保护机制
```javascript
async function handleQuickAdd() {
  // 在方法开始时保存选中的部位，避免在执行过程中被清空
  const selectedParts = [...partSearchState.selectedParts];
  
  // 使用保存的状态进行后续操作
  if (projectData.hasCheckPart === '1') {
    if (!selectedParts || selectedParts.length === 0) {
      message.warning('该项目需要选择检查部位');
      return;
    }
    // 使用 selectedParts 而不是 partSearchState.selectedParts
  }
}
```

## 调试步骤

### 1. 检查控制台输出
打开浏览器开发者工具，查看以下调试信息：
- `Ctrl+Enter pressed, current parts before:` - 按键时的状态
- `Delayed execution, current parts:` - 延迟执行时的状态
- `handleQuickAdd called:` - 添加方法调用时的详细状态

### 2. 使用测试页面
访问 `/test/part-select-test` 页面进行独立测试：
- 选择部位
- 按 `Ctrl+Enter`
- 观察状态变化和日志输出

### 3. 检查网络请求
在 Network 面板中检查：
- `addItemGroupWithCheckParts` 请求是否发送
- 请求参数是否正确
- 响应状态是否成功

### 4. 检查数据刷新
确认以下调用：
- `fetchCustomerRegGroupList(selectedCustomerReg.value.id, true)` 是否执行
- 项目列表数据是否实际更新

## 可能的进一步解决方案

### 方案1: 使用 watch 监听状态变化
```javascript
import { watch } from 'vue';

watch(() => partSearchState.selectedParts, (newParts, oldParts) => {
  console.log('Parts changed:', { oldParts, newParts });
}, { deep: true });
```

### 方案2: 直接从 DOM 获取值
```javascript
function getSelectedPartsFromDOM() {
  const selectElement = partSelectRef.value?.$el?.querySelector('input');
  // 或者使用 Ant Design Vue 的内部 API
  return partSelectRef.value?.value || [];
}
```

### 方案3: 使用事件委托
```javascript
// 在父元素上监听键盘事件，避免直接在 select 上处理
function handleContainerKeydown(event: KeyboardEvent) {
  if (event.ctrlKey && event.key === 'Enter' && event.target.closest('.ant-select')) {
    // 处理逻辑
  }
}
```

## 测试用例

### 测试用例1: 基本功能
1. 选择项目 → 自动聚焦到部位选择框
2. 选择部位 → 点击添加按钮 → 检查是否成功添加

### 测试用例2: 键盘快捷键
1. 选择项目 → 自动聚焦到部位选择框
2. 选择部位 → 按 `Ctrl+Enter` → 检查是否成功添加

### 测试用例3: 状态保持
1. 选择部位后不要立即操作
2. 等待几秒钟再按 `Ctrl+Enter`
3. 检查部位是否仍然选中

### 测试用例4: 多次操作
1. 连续添加多个项目
2. 检查每次操作后状态是否正确重置
3. 检查项目列表是否正确更新

## 预期结果

修复后应该实现：
1. ✅ 按 `Ctrl+Enter` 不会清空已选部位
2. ✅ 添加操作成功后项目列表立即刷新
3. ✅ 状态正确重置，可以进行下一次操作
4. ✅ 控制台输出清晰的调试信息
