# 依赖项目功能用户指南

## 功能概述

依赖项目功能允许您为体检项目设置前置条件，确保体检流程的正确性和安全性。

## 快速开始

### 1. 进入配置页面
1. 打开 **项目组合管理** 页面
2. 找到要配置的项目，点击 **编辑** 按钮
3. 切换到 **关联管理** 标签页
4. 找到 **依赖项目配置** 区域

### 2. 添加依赖项目
1. 点击 **添加依赖项** 按钮
2. 选择依赖类型：
   - **大项**：依赖其他项目组合（如：心脏彩超增强 → 心脏彩超基础）
   - **小项**：依赖具体检查项目（如：肺功能 → 身高、体重）
3. 根据依赖类型选择项目：
   - **大项依赖**：直接从下拉列表选择依赖的大项
   - **小项依赖**：先选择大项，再选择该大项下的具体小项
4. 重复步骤1-3添加更多依赖项目

### 3. 管理依赖项目
- **修改**：直接在下拉框中重新选择
- **删除**：点击右侧的删除按钮（🗑️）
- **类型切换**：重新选择依赖类型，系统会自动清空已选项目

### 4. 保存配置
1. 确认所有依赖项目配置正确
2. 点击 **保存配置** 按钮
3. 等待保存成功提示

## 使用效果

### 体检登记时
- 选择有依赖的项目时，系统会自动检查
- 如果依赖项目未选择，会显示明确的错误提示
- 必须先选择依赖项目才能继续

### 错误提示示例
```
❌ 项目【心脏彩超增强】依赖项目【心脏彩超基础】，请先添加依赖项目
❌ 项目【肺功能检查】依赖小项【身高】（属于【内科检查】），请先添加【内科检查】项目
❌ 项目【肺功能检查】依赖小项【体重】（属于【内科检查】），请先添加【内科检查】项目
```

## 常见应用场景

### 1. 检查流程依赖
**场景**：增强检查需要基础检查作为前提
- 心脏彩超增强 → 依赖 → 心脏彩超基础
- 肝脏MRI增强 → 依赖 → 肝脏MRI平扫

### 2. 数据计算依赖
**场景**：检查结果需要基础数据进行计算
- 肺功能检查 → 依赖 → 身高、体重（内科检查）
- BMI计算 → 依赖 → 身高、体重（内科检查）
- 体表面积计算 → 依赖 → 身高、体重（内科检查）

### 3. 安全性检查依赖
**场景**：有风险的检查需要安全评估
- 造影剂检查 → 依赖 → 过敏史询问（内科检查）
- 运动负荷试验 → 依赖 → 心电图检查

### 4. 设备共享依赖
**场景**：复杂检查需要基础数据
- 冠脉造影分析 → 依赖 → 心电图检查
- 心功能评估 → 依赖 → 心脏彩超基础

## 注意事项

### ✅ 推荐做法
- 合理设置依赖关系，避免过度复杂
- 依赖关系应该符合实际医疗流程
- 定期检查和更新依赖配置

### ❌ 避免问题
- 不要设置循环依赖（A依赖B，B依赖A）
- 不要设置过多层级的依赖关系
- 不要忘记保存配置

## 故障排除

### 问题：依赖项目下拉框没有数据
**解决方案**：
1. 检查网络连接
2. 确认有相应的项目数据
3. 刷新页面重试

### 问题：保存时提示验证失败
**解决方案**：
1. 检查是否所有依赖项都已选择
2. 确认依赖类型和项目匹配
3. 查看具体错误提示信息

### 问题：体检登记时依赖验证不生效
**解决方案**：
1. 确认依赖配置已正确保存
2. 检查项目ID是否匹配
3. 联系系统管理员检查后端配置

## 技术支持

如果遇到其他问题，请联系系统管理员并提供：
1. 具体的错误信息
2. 操作步骤截图
3. 浏览器控制台错误日志
