# 部位选择模态框优化

## 问题描述

在 `TeamGroupCard.vue` 组件中，存在两个问题：
1. 未定义的变量 `checkPartState`，该变量在部位选择相关的功能中被使用，但没有进行定义，导致代码运行时会出现错误。
2. 部位选择时没有正确保存 `checkPartCode`，导致添加的项目缺少部位代码信息。

## 解决方案

### 问题1：未定义的 checkPartState

通过分析代码，发现系统中已经存在一个批量部位选择模态框（`batchPartState`），而单个项目的部位选择功能（`checkPartState`）与批量部位选择功能非常相似。因此，我们采用了复用批量部位选择模态框的方案，而不是为单个项目部位选择创建一个新的模态框。

#### 主要修改

1. 删除了未定义的 `checkPartState` 相关代码
2. 修改了 `showCheckPartSelector` 方法，使其调用 `showBatchPartSelector` 方法，并传入单个项目作为参数
3. 动态调整模态框标题，根据项目数量显示不同的标题：
   - 当只有一个项目时，显示"选择检查部位"
   - 当有多个项目时，显示"批量选择检查部位"

#### 代码修改详情

1. 删除了 `checkPartState` 的定义：
```typescript
// 单个项目部位选择相关状态
const checkPartState = reactive({
  visible: false,
  loading: false,
  currentItemGroup: null as ItemGroup | null,
  selectedParts: [] as string[],
  options: [] as Array<{label: string, value: string, frequency: number}>,
});
```

2. 修改了 `showCheckPartSelector` 方法，使其复用批量部位选择模态框：
```typescript
// 部位选择相关方法 - 复用批量部位选择模态框
async function showCheckPartSelector(itemGroup: ItemGroup) {
  console.log('Showing check part selector for item group:', itemGroup);

  // 使用批量部位选择器处理单个项目
  showBatchPartSelector([itemGroup], 'manual');
}
```

3. 删除了单个项目部位选择模态框的模板代码，并修改了批量部位选择模态框的标题：
```html
<!-- 批量部位选择模态框 -->
<a-modal
  :title="batchPartState.itemGroups.length === 1 ? '选择检查部位' : '批量选择检查部位'"
  v-model:open="batchPartState.visible"
  width="800px"
  @ok="confirmBatchAddItemsWithParts"
  @cancel="closeBatchPartSelector"
  :confirmLoading="batchPartState.loading"
  :okButtonProps="{ disabled: !hasAnyPartSelected() }"
  okText="确认添加"
  cancelText="取消"
>
```

### 问题2：部位代码未保存

在部位选择功能中，我们发现部位的代码（`checkPartCode`）没有被正确保存。这是因为在处理API返回的部位数据时，只保存了部位的ID、名称和使用频次，但没有保存部位代码。

#### 主要修改

1. 修改了 `loadItemParts` 方法，在处理API返回的部位数据时，保存部位的代码信息
2. 修改了 `confirmBatchAddItemsWithParts` 方法，在添加项目时，从选项中获取部位代码并保存

#### 代码修改详情

1. 修改 `loadItemParts` 方法，保存部位代码：
```typescript
selection.options = res.map((item: any) => ({
  label: `${item.name || ''}${item.frequency ? ` (${item.frequency}次)` : ''}`,
  value: item.id || '',
  frequency: item.frequency || 0,
  code: item.code || '', // 保存部位代码
  name: item.name || '' // 保存部位名称
}));
```

2. 修改 `confirmBatchAddItemsWithParts` 方法，使用保存的部位代码：
```typescript
// 获取部位代码
const partOption = selection.options.find(opt => opt.value === partId);
const partCode = partOption ? partOption.code : '';

// 添加项目时使用部位代码
dataSource.value.push({
  // ...其他字段
  checkPartId: partId,
  checkPartName: cleanPartName,
  checkPartCode: partCode, // 使用从API获取的部位代码
});
```

## 优势

1. **代码复用**：避免了重复的模态框代码和相关逻辑
2. **维护性提高**：减少了代码量，使维护更加简单
3. **一致的用户体验**：无论是单个项目还是多个项目的部位选择，用户界面和操作方式都保持一致
4. **数据完整性**：正确保存部位代码，确保数据的完整性
5. **减少潜在bug**：通过复用已经稳定的代码，减少了引入新bug的可能性

## 注意事项

- 批量部位选择模态框现在需要处理单个项目和多个项目两种情况
- 模态框的标题会根据项目数量动态变化
- 所有原来依赖 `checkPartState` 的方法都已被移除，相关功能通过 `batchPartState` 实现
- 部位选择功能现在会正确保存部位代码信息
