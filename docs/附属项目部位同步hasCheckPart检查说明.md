# 附属项目部位同步hasCheckPart检查说明

## 概述

本次修改完善了附属项目和赠送项目的部位自动同步功能，增加了对项目`hasCheckPart`属性的检查。只有当附属项目或赠送项目的`hasCheckPart`等于1时，才会自动为其添加部位信息。

## 修改背景

在之前的实现中，系统会无条件地为所有附属项目和赠送项目同步主项目的部位信息。但实际上，并不是所有项目都支持部位选择。只有当项目的`hasCheckPart`属性等于1时，该项目才支持部位功能。

## 修改内容

### 1. 主项目部位变更时的智能检查

#### 1.1 附属项目部位同步
```javascript
const onMainPartChange = (index, partId) => {
  const item = relationData.attachGroups[index];
  if (partId) {
    const part = checkPartList.value.find(p => p.id === partId);
    if (part) {
      item.mainCheckPartName = part.name;
      item.mainCheckPartCode = part.code;
      
      // 🎯 关键检查：只有支持部位的附属项目才自动同步
      if (item.relationGroupId) {
        const attachGroup = groupList.value.find(g => g.id === item.relationGroupId);
        if (attachGroup && attachGroup.hasCheckPart === 1) {
          // 附属项目支持部位，自动同步
          item.relationCheckPartId = part.id;
          item.relationCheckPartName = part.name;
          item.relationCheckPartCode = part.code;
          message.success(`已自动将附属项目部位设为：${part.name}`);
        } else {
          // 附属项目不支持部位，清空部位信息
          item.relationCheckPartId = null;
          item.relationCheckPartName = null;
          item.relationCheckPartCode = null;
          if (attachGroup) {
            message.info(`附属项目"${attachGroup.name}"不支持部位选择`);
          }
        }
      }
    }
  }
};
```

#### 1.2 赠送项目部位同步
```javascript
const onGiftMainPartChange = (index, partId) => {
  // 类似的逻辑，检查赠送项目的hasCheckPart属性
  if (giftGroup && giftGroup.hasCheckPart === 1) {
    // 赠送项目支持部位，自动同步
  } else {
    // 赠送项目不支持部位，清空部位信息并提示
  }
};
```

### 2. 选择关联项目时的智能检查

#### 2.1 附属项目选择时检查
```javascript
const onAttachGroupChange = (index, groupId) => {
  const item = relationData.attachGroups[index];
  // 清空部位选择
  item.relationCheckPartId = null;
  item.relationCheckPartName = null;
  item.relationCheckPartCode = null;
  
  // 🎯 检查新选择的附属项目是否支持部位
  if (groupId && item.mainCheckPartId) {
    const selectedGroup = groupList.value.find(g => g.id === groupId);
    if (selectedGroup && selectedGroup.hasCheckPart === 1) {
      // 附属项目支持部位，自动同步主项目部位
      item.relationCheckPartId = item.mainCheckPartId;
      item.relationCheckPartName = item.mainCheckPartName;
      item.relationCheckPartCode = item.mainCheckPartCode;
      message.success(`附属项目支持部位，已自动同步主项目部位：${item.mainCheckPartName}`);
    }
  }
};
```

### 3. 批量同步功能的智能过滤

#### 3.1 附属项目批量同步
```javascript
const handleBatchSetAttachPart = ({ key }) => {
  if (key === 'sync') {
    let syncCount = 0;
    let skipCount = 0;
    relationData.attachGroups.forEach(item => {
      if (item.mainCheckPartId && item.mainCheckPartName && item.relationGroupId) {
        // 🎯 检查附属项目是否支持部位
        const attachGroup = groupList.value.find(g => g.id === item.relationGroupId);
        if (attachGroup && attachGroup.hasCheckPart === 1) {
          // 支持部位，执行同步
          item.relationCheckPartId = item.mainCheckPartId;
          item.relationCheckPartName = item.mainCheckPartName;
          item.relationCheckPartCode = item.mainCheckPartCode;
          syncCount++;
        } else {
          // 不支持部位的项目跳过
          skipCount++;
        }
      }
    });
    
    if (syncCount > 0) {
      let msg = `已同步${syncCount}个附属项目的部位`;
      if (skipCount > 0) {
        msg += `，跳过${skipCount}个不支持部位的项目`;
      }
      message.success(msg);
    } else {
      message.warning('没有可同步的主项目部位或附属项目不支持部位');
    }
  }
};
```

### 4. 界面显示的智能控制

#### 4.1 条件显示部位选择器
```vue
<!-- 附属项目部位选择 -->
<a-space v-if="getGroupById(item.relationGroupId)?.hasCheckPart === 1">
  <span style="width: 80px; display: inline-block;">附属部位:</span>
  <a-select
    v-model:value="item.relationCheckPartId"
    placeholder="选择附属项目部位（可选）"
    style="width: 200px"
    :options="checkPartOptions"
    :disabled="!props.itemGroupId || !item.relationGroupId"
    show-search
    :filter-option="filterCheckPart"
    allow-clear
    @change="(value) => onAttachPartChange(index, value)"
  />
</a-space>

<!-- 不支持部位的提示 -->
<a-space v-else-if="item.relationGroupId && getGroupById(item.relationGroupId)?.hasCheckPart !== 1">
  <span style="width: 80px; display: inline-block;">附属部位:</span>
  <span style="color: #999; font-style: italic;">该项目不支持部位选择</span>
</a-space>
```

#### 4.2 辅助方法
```javascript
// 根据ID获取项目组信息
const getGroupById = (groupId) => {
  if (!groupId) return null;
  return groupList.value.find(g => g.id === groupId);
};
```

## 用户体验改进

### 1. 智能提示
- **支持部位的项目**：自动同步部位并显示成功提示
- **不支持部位的项目**：显示信息提示，告知用户该项目不支持部位选择
- **批量操作**：显示同步成功的项目数量和跳过的项目数量

### 2. 界面优化
- **条件显示**：只有支持部位的项目才显示部位选择器
- **友好提示**：不支持部位的项目显示灰色斜体提示文字
- **避免混淆**：用户不会看到无效的部位选择器

### 3. 操作逻辑
- **自动判断**：系统自动判断项目是否支持部位，无需用户手动判断
- **智能跳过**：批量操作时自动跳过不支持部位的项目
- **清晰反馈**：明确告知用户哪些项目被同步，哪些被跳过

## 业务场景示例

### 场景1：混合项目配置
- **主项目**：胸部CT（支持部位）
- **附属项目1**：心电图（不支持部位）
- **附属项目2**：胸部X光（支持部位）

**操作流程**：
1. 用户选择主项目部位为"胸部"
2. 系统自动将胸部X光的部位设为"胸部"（成功提示）
3. 系统跳过心电图（信息提示：该项目不支持部位选择）

### 场景2：批量同步操作
- **配置**：5个附属项目，其中3个支持部位，2个不支持部位
- **操作**：点击"同步主项目部位到附属部位"
- **结果**：显示"已同步3个附属项目的部位，跳过2个不支持部位的项目"

## 技术要点

### 1. hasCheckPart属性检查
- **值为1**：项目支持部位选择
- **值不为1或未设置**：项目不支持部位选择

### 2. 数据一致性
- 不支持部位的项目，其部位相关字段会被清空
- 避免在数据库中保存无效的部位信息

### 3. 性能考虑
- 使用`find()`方法查找项目信息，在项目数量不大的情况下性能良好
- 可考虑使用Map缓存项目信息以提升性能

## 注意事项

1. **数据完整性**：确保项目数据中的`hasCheckPart`字段正确设置
2. **向后兼容**：对于没有`hasCheckPart`字段的旧数据，系统会将其视为不支持部位
3. **用户教育**：需要告知用户哪些项目支持部位选择，哪些不支持
4. **测试覆盖**：需要测试各种hasCheckPart值的组合情况

## 相关文件

- **主要文件**: `src/views/basicinfo/components/ItemGroupRelationForm.vue`
- **数据模型**: ItemGroup实体的hasCheckPart字段
- **API接口**: 获取项目列表的相关接口

## 后续优化建议

1. **性能优化**：使用Map缓存项目信息，避免重复查找
2. **批量提示**：在批量操作前预先提示用户有多少项目支持部位
3. **配置管理**：提供管理界面让管理员配置项目的部位支持情况
4. **数据验证**：在后端添加数据验证，确保部位信息只保存到支持部位的项目中
