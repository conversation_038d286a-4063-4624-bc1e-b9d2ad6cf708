# 表单规则管理系统 - 后端代码部署说明

## 概述

已成功将表单规则管理系统的后端代码部署到项目的合适包结构中。所有代码文件已按照标准的Spring Boot项目结构进行组织。

## 代码结构

### 包结构
```
org.jeecg.modules.formrule/
├── entity/                     # 实体类
│   ├── FormRuleConfig.java     # 表单规则配置实体
│   ├── FormRuleChangeLog.java  # 变更历史实体
│   └── FormRuleCacheVersion.java # 缓存版本实体
├── dto/                        # 数据传输对象
│   ├── FormRuleConfigDTO.java  # 表单规则配置DTO
│   ├── FieldRuleDTO.java       # 字段规则DTO
│   ├── ValidationRuleDTO.java  # 验证规则DTO
│   ├── DependencyRuleDTO.java  # 联动规则DTO
│   ├── FormRuleVersionDTO.java # 版本信息DTO
│   ├── FormRuleQueryDTO.java   # 查询条件DTO
│   └── FormRuleStatisticsVO.java # 统计信息VO
├── mapper/                     # 数据访问层
│   └── FormRuleConfigMapper.java # 主要Mapper接口
├── service/                    # 服务接口
│   └── IFormRuleService.java   # 服务接口定义
├── service/impl/               # 服务实现
│   ├── FormRuleServiceImpl.java # 主要服务实现
│   ├── FormRuleCacheService.java # 缓存服务
│   └── FormRuleNotificationService.java # 通知服务
├── controller/                 # 控制器
│   └── FormRuleController.java # REST API控制器
├── websocket/                  # WebSocket相关
│   ├── FormRuleWebSocketHandler.java # WebSocket处理器
│   └── FormRuleRedisMessageListener.java # Redis消息监听器
└── config/                     # 配置类
    ├── FormRuleConfiguration.java # 主配置类
    └── FormRuleScheduledTasks.java # 定时任务配置
```

### 资源文件
```
src/main/resources/
├── db/
│   └── form_rule_tables.sql    # 数据库初始化脚本
└── application-formrule.yml    # 模块配置文件
```

## 部署步骤

### 1. 数据库初始化

执行数据库初始化脚本：
```sql
-- 在MySQL中执行
source /path/to/physicalex-lkd/jeecg-module-physicalex/src/main/resources/db/form_rule_tables.sql
```

或者直接运行SQL文件内容来创建表结构和初始数据。

### 2. 配置文件集成

在主配置文件 `application.yml` 中添加：
```yaml
spring:
  profiles:
    include: formrule
```

### 3. 依赖检查

确保项目中包含以下依赖（通常JeecgBoot项目已包含）：
- Spring Boot Web
- Spring Boot WebSocket
- Spring Data Redis
- MyBatis Plus
- FastJSON
- Lombok

### 4. 启动验证

启动应用后，可以通过以下方式验证：

#### API测试
```bash
# 获取表单规则
curl http://localhost:8080/api/form-rules/customer_reg_form

# 获取版本信息
curl http://localhost:8080/api/form-rules/customer_reg_form/version

# 获取统计信息
curl http://localhost:8080/api/form-rules/statistics
```

#### WebSocket测试
```javascript
const ws = new WebSocket('ws://localhost:8080/ws/form-rule');
ws.onopen = function() {
    ws.send(JSON.stringify({
        action: 'SUBSCRIBE',
        data: 'customer_reg_form'
    }));
};
```

## 功能特性

### 1. 核心功能
- ✅ 表单规则配置管理
- ✅ 字段验证规则定义
- ✅ 字段联动关系配置
- ✅ 版本控制和历史记录
- ✅ 多级缓存机制
- ✅ 实时通知推送

### 2. API接口
- ✅ RESTful API设计
- ✅ 完整的CRUD操作
- ✅ 分页查询支持
- ✅ 批量操作接口
- ✅ 统计信息接口

### 3. 缓存机制
- ✅ Redis分布式缓存
- ✅ 版本控制缓存
- ✅ 分布式锁机制
- ✅ 缓存预热功能

### 4. 实时通信
- ✅ WebSocket长连接
- ✅ Redis Pub/Sub消息
- ✅ 多客户端同步
- ✅ 连接状态管理

### 5. 监控运维
- ✅ 定时任务清理
- ✅ 统计信息收集
- ✅ 日志记录完整
- ✅ 异常处理机制

## 配置说明

### 缓存配置
```yaml
form-rule:
  cache:
    expire-hours: 24              # 缓存过期时间
    version-expire-hours: 1       # 版本缓存过期时间
    enable-warmup: true           # 启用缓存预热
    warmup-form-codes:            # 预热表单列表
      - customer_reg_form
```

### WebSocket配置
```yaml
form-rule:
  websocket:
    enabled: true                 # 启用WebSocket
    path: /ws/form-rule          # WebSocket路径
    allowed-origins: "*"         # 允许的源
```

### 定时任务配置
```yaml
form-rule:
  scheduled:
    enabled: true                 # 启用定时任务
    cache-cleanup-cron: "0 0 * * * ?"    # 缓存清理
    log-cleanup-cron: "0 2 0 * * ?"      # 日志清理
    statistics-cron: "0 30 0 * * ?"      # 统计生成
```

## 注意事项

### 1. 权限配置
确保用户具有相应的权限：
- `form:rule:view` - 查看权限
- `form:rule:create` - 创建权限
- `form:rule:update` - 更新权限
- `form:rule:delete` - 删除权限
- `form:rule:config` - 配置权限

### 2. Redis配置
确保Redis服务正常运行，并配置正确的连接参数。

### 3. 数据库配置
确保MySQL版本支持JSON字段类型（5.7+）。

### 4. 内存配置
建议JVM堆内存至少2GB，以支持缓存和WebSocket连接。

## 故障排除

### 1. 启动失败
- 检查数据库连接配置
- 确认Redis服务状态
- 查看启动日志错误信息

### 2. API调用失败
- 检查权限配置
- 确认请求参数格式
- 查看后端日志

### 3. WebSocket连接失败
- 检查防火墙设置
- 确认WebSocket配置
- 验证网络连接

### 4. 缓存问题
- 检查Redis连接
- 清理过期缓存
- 重启应用服务

## 扩展建议

### 1. 性能优化
- 配置数据库连接池
- 启用Redis集群
- 使用CDN加速

### 2. 安全加固
- 配置HTTPS
- 启用访问控制
- 添加API限流

### 3. 监控告警
- 集成Prometheus
- 配置Grafana仪表板
- 设置告警规则

## 总结

后端代码已成功部署到项目中，提供了完整的表单规则管理功能。系统具有良好的扩展性和维护性，可以满足企业级应用的需求。

通过合理的配置和部署，可以实现高性能、高可用的表单规则管理服务。
