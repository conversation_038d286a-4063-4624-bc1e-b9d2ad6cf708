# 职业健康问卷预览功能使用说明

## 功能概述

在职业健康问卷编辑组件中新增了预览功能，无论是编辑模式还是查看模式都会显示"预览问卷"按钮，点击后可以打开正式文书样式的问卷展示弹窗。

## 主要特性

### 1. 智能填写功能
- **后端算法**：核心算法逻辑在后端实现，性能更好、更安全
- **智能推荐**：基于历史问卷数据，使用多维度评分算法推荐最适合的填写模板
- **一键应用**：选择推荐数据后，自动填写主问卷和所有子问卷内容
- **编辑模式专用**：仅在编辑模式下显示，避免误操作

### 2. 历史问卷管理
- **历史查看**：基于身份证号查询客户的所有历史问卷记录
- **数据复制**：支持从历史记录复制数据到当前问卷
- **记录管理**：支持删除不需要的历史记录

### 3. 全模式预览支持
- **编辑模式**：显示预览按钮，预览当前正在编辑的内容（包括未保存的数据）
- **查看模式**：显示预览按钮，预览已保存的完整数据

### 2. 正式文书样式
- 标准医疗文档格式
- 清晰的章节结构
- 专业的表格布局
- 支持打印功能
- 响应式设计

### 3. 数据兼容性
- 优先使用集成接口 `/occu/zyInquiry/getCompleteDataByRegId`
- 集成接口不可用时自动降级到分离接口
- 确保在后台接口未完成时也能正常工作

## 使用方法

### 在现有代码中使用

```vue
<template>
  <div>
    <!-- 查看模式，显示预览按钮（预览已保存数据） -->
    <a-button @click="openViewMode">查看问卷</a-button>

    <!-- 编辑模式，显示预览按钮（预览当前编辑内容） -->
    <a-button @click="openEditMode">编辑问卷</a-button>

    <ComprehensiveInquiryQuestionnaire
      ref="questionnaireRef"
      @success="handleSuccess"
    />
  </div>
</template>

<script setup>
import ComprehensiveInquiryQuestionnaire from '@/views/occu/components/ComprehensiveInquiryQuestionnaire.vue'

const questionnaireRef = ref()

// 查看模式（预览已保存的完整数据）
const openViewMode = () => {
  questionnaireRef.value?.open(customerData, true) // readonly = true
}

// 编辑模式（预览当前编辑的内容，包括未保存数据）
const openEditMode = () => {
  questionnaireRef.value?.open(customerData, false) // readonly = false
}

const handleSuccess = () => {
  console.log('操作完成')
}
</script>
```

### 客户数据格式

```javascript
const customerData = {
  id: '客户ID',
  name: '姓名',
  gender: '性别',
  age: 年龄,
  ageUnit: '年龄单位',
  phone: '电话',
  customerNo: '体检号',
  avatar: '头像URL（可选）'
}
```

## 界面展示

### 查看模式界面
```
┌─────────────────────────────────────────────────┐
│ [头像] 张三                    [●] 查看模式 [预览问卷] │
│        性别：女 年龄：28岁 电话：138****8001        │
└─────────────────────────────────────────────────┘
│                                               │
│ [基本情况] [症状] [职业史] [既往病史] [放射史]      │
│                                               │
```

### 编辑模式界面
```
┌─────────────────────────────────────────────────────────────────┐
│ [头像] 张三                [●] 编辑模式 [智能填写] [历史问卷] [预览问卷] │
│        性别：女 年龄：28岁 电话：138****8001 体检号：ZY001 身份证：110*** │
└─────────────────────────────────────────────────────────────────┘
│                                                               │
│ [基本情况] [症状] [职业史] [既往病史] [放射史]                      │
│                                                               │
```

### 预览弹窗差异

**编辑模式预览**：
- 弹窗标题：`张三 - 职业健康检查问卷（预览）`
- 顶部显示蓝色提示框："预览模式 - 当前显示的是编辑中的问卷内容预览，部分数据可能尚未保存。"
- 基本信息显示当前表单中填写的内容（包括未保存的修改）

**查看模式预览**：
- 弹窗标题：`张三 - 职业健康检查问卷`
- 无提示框
- 显示数据库中已保存的完整数据

## 预览弹窗内容

预览弹窗包含以下章节：

1. **文档头部**
   - 标题：职业健康检查问卷
   - 体检号、填表日期

2. **一、基本信息**
   - 姓名、性别、年龄、联系电话

3. **二、烟酒情况**
   - 吸烟状态、吸烟量、吸烟年限
   - 饮酒状态、饮酒量、饮酒年限

4. **三、月经史**（仅女性显示）
   - 月经初潮、经期、周期、停经年龄

5. **四/五、生育史**
   - 子女个数、流产次数、早产次数等
   - 先天畸形情况

6. **职业接触史**
   - 表格形式展示：序号、起止日期、就职单位、车间、工种、总工龄、接害工龄、危害因素、是否防护、防护措施

7. **症状**
   - 表格形式展示：序号、症状名称、详细描述、记录时间

8. **既往病史**
   - 表格形式展示：序号、疾病名称、诊断时间、详细描述、记录时间

9. **放射史**
   - 表格形式展示：序号、放射类型、接触时间、接触剂量、详细描述、记录时间

10. **家族史**
    - 表格形式展示：序号、关系、疾病名称、年龄、详细描述、记录时间

11. **婚姻状况**
    - 表格形式展示：序号、婚姻状态、结婚时间、配偶信息、记录时间

12. **文档尾部**
    - 签名栏：填表人、医生、日期、医疗机构

## 技术实现

### 组件结构
```
ComprehensiveInquiryQuestionnaire.vue (主组件)
├── SmartFillModal.vue (智能填写弹窗)
├── QuestionnaireHistoryModal.vue (历史问卷弹窗)
├── QuestionnaireDisplayModal.vue (预览弹窗)
└── 其他子组件...
```

### API调用策略
1. **智能填写**：`getSmartFillRecommendations` - 后端处理所有算法逻辑
2. **历史问卷**：`getCompleteHistoryByIdCard` - 获取完整历史记录
3. **问卷预览**：
   - 优先使用集成接口 `getCompleteDataByRegId`
   - 如果集成接口失败，自动降级到分离接口

### 架构优势
- **后端算法**：智能填写算法在后端实现，性能更好
- **前端简化**：前端只负责界面展示和用户交互
- **安全性高**：核心业务逻辑不暴露给前端
- **可维护性强**：算法优化只需要修改后端

### 样式特点
- 正式医疗文档样式
- **统一表格展示**：所有子问卷都采用表格形式，便于阅读和打印
- 支持打印（专门的打印CSS）
- 响应式设计（移动端适配，自动隐藏记录时间列）
- 清晰的视觉层次

## 注意事项

1. **预览模式**：编辑模式和查看模式都显示预览按钮，但预览内容有所不同
2. **数据实时性**：编辑模式下预览显示当前表单数据，查看模式下预览显示已保存数据
3. **数据完整性**：预览会显示所有已填写的数据，空数据显示为"-"或"无"
4. **打印功能**：预览弹窗支持直接打印，会自动应用打印样式
5. **性能优化**：使用 Promise.allSettled 并行加载数据，提高加载速度
6. **错误处理**：API调用失败时有友好的错误提示
7. **视觉区分**：编辑模式预览有明显的提示信息，避免用户混淆

## 测试方法

可以使用 `QuestionnaireTestPage.vue` 组件进行功能测试：

```vue
<QuestionnaireTestPage />
```

该组件提供了编辑模式和查看模式的测试按钮，方便验证功能是否正常工作。
