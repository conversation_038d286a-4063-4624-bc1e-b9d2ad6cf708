# 后端智能填写算法实现方案

## 概述

将智能填写的核心算法逻辑从前端迁移到后端实现，提高性能、安全性和可维护性。

## 后端实现结构

### 1. Service接口添加方法

在 `IZyInquiryService.java` 中添加：

```java
/**
 * 根据身份证号获取智能填写推荐数据
 * @param idCard 身份证号
 * @return 智能推荐数据列表
 */
List<Map<String, Object>> getSmartFillRecommendations(String idCard);
```

### 2. 创建智能填写算法工具类

创建 `SmartFillAlgorithm.java`：

```java
package org.jeecg.modules.occu.utils;

import org.springframework.stereotype.Component;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class SmartFillAlgorithm {
    
    /**
     * 数据去重配置
     */
    private static final Map<String, String[]> DEDUP_CONFIG = new HashMap<>();
    static {
        DEDUP_CONFIG.put("occupation", new String[]{"company", "workName", "startDate", "endDate"});
        DEDUP_CONFIG.put("disease", new String[]{"diseaseName", "diagnosisDate"});
        DEDUP_CONFIG.put("radiation", new String[]{"radiationType", "startDate", "endDate"});
        DEDUP_CONFIG.put("family", new String[]{"relationship", "diseaseName"});
        DEDUP_CONFIG.put("symptom", new String[]{"symptomName"});
        DEDUP_CONFIG.put("marital", new String[]{"maritalStatus", "marriageDate"});
    }

    /**
     * 处理历史问卷数据，生成智能推荐
     */
    public List<Map<String, Object>> processHistoryData(List<Map<String, Object>> historyData) {
        if (historyData == null || historyData.isEmpty()) {
            return new ArrayList<>();
        }

        // 1. 提取和合并所有子问卷数据
        Map<String, Object> mergedData = extractAndMergeSubQuestionnaires(historyData);

        // 2. 为每个历史记录生成推荐数据
        List<Map<String, Object>> recommendations = new ArrayList<>();
        for (Map<String, Object> item : historyData) {
            Map<String, Object> recommendation = createRecommendationData(item, mergedData, historyData);
            recommendations.add(recommendation);
        }

        // 3. 计算推荐分数并排序
        Date currentDate = new Date();
        for (Map<String, Object> item : recommendations) {
            Map<String, Object> scoreResult = calculateRecommendationScore(item, currentDate);
            item.put("score", scoreResult.get("score"));
            item.put("reasons", scoreResult.get("reasons"));
        }

        // 4. 按分数排序
        recommendations.sort((a, b) -> {
            Integer scoreA = (Integer) a.get("score");
            Integer scoreB = (Integer) b.get("score");
            return scoreB.compareTo(scoreA);
        });

        return recommendations;
    }

    /**
     * 提取和合并所有历史记录中的子问卷数据
     */
    private Map<String, Object> extractAndMergeSubQuestionnaires(List<Map<String, Object>> historyData) {
        Map<String, Object> merged = new HashMap<>();
        
        // 收集所有子问卷数据
        List<Map<String, Object>> allOccupation = new ArrayList<>();
        List<Map<String, Object>> allSymptoms = new ArrayList<>();
        List<Map<String, Object>> allDisease = new ArrayList<>();
        List<Map<String, Object>> allRadiation = new ArrayList<>();
        List<Map<String, Object>> allFamily = new ArrayList<>();
        List<Map<String, Object>> allMarital = new ArrayList<>();

        for (Map<String, Object> record : historyData) {
            if (record.get("occupationHistory") != null) {
                allOccupation.addAll((List<Map<String, Object>>) record.get("occupationHistory"));
            }
            if (record.get("symptoms") != null) {
                allSymptoms.addAll((List<Map<String, Object>>) record.get("symptoms"));
            }
            if (record.get("diseaseHistory") != null) {
                allDisease.addAll((List<Map<String, Object>>) record.get("diseaseHistory"));
            }
            if (record.get("radiationHistory") != null) {
                allRadiation.addAll((List<Map<String, Object>>) record.get("radiationHistory"));
            }
            if (record.get("familyHistory") != null) {
                allFamily.addAll((List<Map<String, Object>>) record.get("familyHistory"));
            }
            if (record.get("maritalStatus") != null) {
                allMarital.addAll((List<Map<String, Object>>) record.get("maritalStatus"));
            }
        }

        // 对每类数据进行去重和排序
        merged.put("occupationHistory", deduplicateAndSort(allOccupation, "occupation", "startDate"));
        merged.put("symptoms", deduplicateAndSort(allSymptoms, "symptom", "createTime"));
        merged.put("diseaseHistory", deduplicateAndSort(allDisease, "disease", "diagnosisDate"));
        merged.put("radiationHistory", deduplicateAndSort(allRadiation, "radiation", "startDate"));
        merged.put("familyHistory", deduplicateAndSort(allFamily, "family", "createTime"));
        merged.put("maritalStatus", deduplicateAndSort(allMarital, "marital", "marriageDate"));

        return merged;
    }

    /**
     * 数据去重和排序
     */
    private List<Map<String, Object>> deduplicateAndSort(List<Map<String, Object>> data, String type, String sortField) {
        if (data == null || data.isEmpty()) {
            return new ArrayList<>();
        }

        String[] dedupFields = DEDUP_CONFIG.get(type);
        Map<String, Map<String, Object>> uniqueMap = new LinkedHashMap<>();

        for (Map<String, Object> item : data) {
            String key = Arrays.stream(dedupFields)
                    .map(field -> normalizeValue(item.get(field)))
                    .collect(Collectors.joining("|"));

            if (!uniqueMap.containsKey(key)) {
                uniqueMap.put(key, item);
            } else {
                // 如果有重复，选择更完整的数据
                Map<String, Object> existing = uniqueMap.get(key);
                if (isMoreComplete(item, existing)) {
                    uniqueMap.put(key, item);
                }
            }
        }

        // 转换为列表并排序
        List<Map<String, Object>> result = new ArrayList<>(uniqueMap.values());
        result.sort((a, b) -> {
            Object dateA = a.get(sortField);
            Object dateB = b.get(sortField);
            if (dateA == null && dateB == null) return 0;
            if (dateA == null) return 1;
            if (dateB == null) return -1;
            return dateB.toString().compareTo(dateA.toString());
        });

        return result;
    }

    /**
     * 标准化值用于比较
     */
    private String normalizeValue(Object value) {
        if (value == null) return "";
        return value.toString().trim().toLowerCase();
    }

    /**
     * 判断数据是否更完整
     */
    private boolean isMoreComplete(Map<String, Object> current, Map<String, Object> existing) {
        long currentFields = current.values().stream()
                .filter(v -> v != null && !v.toString().trim().isEmpty())
                .count();
        long existingFields = existing.values().stream()
                .filter(v -> v != null && !v.toString().trim().isEmpty())
                .count();
        return currentFields > existingFields;
    }

    /**
     * 创建推荐数据
     */
    private Map<String, Object> createRecommendationData(Map<String, Object> historyItem, 
                                                        Map<String, Object> mergedData, 
                                                        List<Map<String, Object>> allHistory) {
        Map<String, Object> recommendation = new HashMap<>();
        
        // 基本信息
        Map<String, Object> basicInfo = (Map<String, Object>) historyItem.get("basicInfo");
        if (basicInfo == null) {
            basicInfo = historyItem; // 如果没有嵌套结构，直接使用
        }
        
        recommendation.put("id", basicInfo.get("id"));
        recommendation.put("createTime", basicInfo.get("createTime"));
        recommendation.put("basicInfo", basicInfo);
        recommendation.put("mergedSubQuestionnaires", mergedData);
        
        // 统计信息
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalRecords", allHistory.size());
        statistics.put("occupationCount", ((List<?>) mergedData.get("occupationHistory")).size());
        statistics.put("symptomCount", ((List<?>) mergedData.get("symptoms")).size());
        statistics.put("diseaseCount", ((List<?>) mergedData.get("diseaseHistory")).size());
        statistics.put("radiationCount", ((List<?>) mergedData.get("radiationHistory")).size());
        statistics.put("familyCount", ((List<?>) mergedData.get("familyHistory")).size());
        statistics.put("maritalCount", ((List<?>) mergedData.get("maritalStatus")).size());
        recommendation.put("statistics", statistics);
        
        return recommendation;
    }

    /**
     * 计算推荐分数
     */
    private Map<String, Object> calculateRecommendationScore(Map<String, Object> item, Date currentDate) {
        int score = 0;
        List<String> reasons = new ArrayList<>();
        
        // 获取创建时间
        Object createTimeObj = item.get("createTime");
        if (createTimeObj != null) {
            // 这里需要根据实际的日期格式进行解析
            // 简化处理，假设是最近的数据
            score += 25;
            reasons.add("近期数据");
        }
        
        // 数据完整性评分
        Map<String, Object> statistics = (Map<String, Object>) item.get("statistics");
        if (statistics != null) {
            int occupationCount = (Integer) statistics.getOrDefault("occupationCount", 0);
            int symptomCount = (Integer) statistics.getOrDefault("symptomCount", 0);
            int diseaseCount = (Integer) statistics.getOrDefault("diseaseCount", 0);
            int totalRecords = (Integer) statistics.getOrDefault("totalRecords", 1);
            
            if (occupationCount > 0) {
                score += 10;
                if (occupationCount >= 3) {
                    score += 5;
                    reasons.add("职业史丰富");
                } else {
                    reasons.add("有职业史");
                }
            }
            
            if (symptomCount > 0) {
                score += 5;
                reasons.add("有症状记录");
            }
            
            if (diseaseCount > 0) {
                score += 5;
                reasons.add("有病史记录");
            }
            
            if (totalRecords > 1) {
                score += 20;
                reasons.add("整合" + totalRecords + "次记录");
            }
        }
        
        // 基本信息完整性
        Map<String, Object> basicInfo = (Map<String, Object>) item.get("basicInfo");
        if (basicInfo != null) {
            if (basicInfo.get("smokStatus") != null) score += 3;
            if (basicInfo.get("drinkStatus") != null) score += 3;
            if (basicInfo.get("childCount") != null) score += 2;
            if (basicInfo.get("congenitalMalformations") != null) score += 2;
            
            // 健康生活方式
            String smokStatus = (String) basicInfo.get("smokStatus");
            String drinkStatus = (String) basicInfo.get("drinkStatus");
            if ("从不吸".equals(smokStatus) && "不喝酒".equals(drinkStatus)) {
                score += 10;
                reasons.add("健康生活方式");
            }
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("score", Math.min(100, score));
        result.put("reasons", reasons);
        return result;
    }
}
```

### 3. Service实现类添加方法

在 `ZyInquiryServiceImpl.java` 中添加：

```java
@Autowired
private SmartFillAlgorithm smartFillAlgorithm;

@Override
public List<Map<String, Object>> getSmartFillRecommendations(String idCard) {
    // 1. 获取完整的历史问卷数据
    List<Map<String, Object>> historyData = getCompleteHistoryByIdCard(idCard);
    
    if (historyData == null || historyData.isEmpty()) {
        return new ArrayList<>();
    }
    
    // 2. 使用智能算法处理数据
    return smartFillAlgorithm.processHistoryData(historyData);
}
```

### 4. Controller添加接口

在 `ZyInquiryController.java` 中添加：

```java
/**
 * 根据身份证号获取智能填写推荐数据
 * @param idCard 身份证号
 * @return 智能推荐数据列表
 */
@AutoLog(value = "职业病问诊-智能填写推荐")
@ApiOperation(value = "职业病问诊-智能填写推荐", notes = "职业病问诊-智能填写推荐")
@GetMapping(value = "/getSmartFillRecommendations")
public Result<List<Map<String, Object>>> getSmartFillRecommendations(
    @RequestParam(name="idCard",required=true) String idCard) {
    List<Map<String, Object>> recommendations = zyInquiryService.getSmartFillRecommendations(idCard);
    return Result.OK(recommendations);
}
```

## 优势

1. **性能提升**：后端处理大量数据，减少网络传输
2. **安全性**：核心算法逻辑不暴露给前端
3. **可维护性**：算法集中管理，便于优化和调试
4. **扩展性**：可以轻松添加新的评分维度和去重规则
5. **缓存支持**：后端可以添加缓存机制提高性能

## 前端简化

前端只需要：
1. 调用 `getSmartFillRecommendations` 接口
2. 展示后端返回的推荐数据
3. 处理用户选择和数据应用

算法的复杂逻辑全部由后端处理，前端代码大大简化。
