# 职业病体检登记组件优化说明

## 概述
对 `CustomerRegFormOfPannel4Occu.vue` 组件进行了优化，使其专门用于职业病体检登记，简化了界面和逻辑，提高了用户体验。

## 主要修改内容

### 1. 固定体检类别
- **修改前**: 可选择多种体检分类（健康体检、干部体检、学生体检等）
- **修改后**: 固定为"职业病体检"，不可修改
- **实现方式**: 将体检分类字段改为只读的 `a-input` 组件，默认值为"职业病体检"

### 2. 移除字段隐藏配置逻辑
- **移除内容**: 
  - 展开/收起按钮及相关逻辑
  - `toggleSearchStatus` 状态管理
  - 扩展字段区域的显示/隐藏控制
- **效果**: 界面更加简洁，用户无需额外操作即可看到所有必要字段

### 3. 简化表单字段
移除了与职业病体检无关的字段：
- 民族、血型、国籍
- 邮政编码、文化程度
- 健康证号、体检卡号、介绍人
- 补检、预缴标志
- 复查相关字段
- 发票抬头、保密等级
- 客户类别、所属科室
- 原检人相关字段

### 4. 保留核心字段
**基本信息字段**:
- 姓名、证件类型、证件号
- 性别、年龄、出生日期
- 电话、婚姻状况、是否备孕
- 职业、单位名称
- 省市区县、详细地址
- 紧急联系人、紧急电话
- 备注

**职业病体检专用字段**:
- 行业
- 危害因素
- 开始接触危害因素时间
- 岗位类别
- 工种、车间
- 总工龄（年/月）
- 接害工龄（年/月）
- 照射种类
- 工号

### 5. 移除不必要的验证逻辑
- 移除了基于体检类别的动态验证规则
- 移除了原检人相关的验证逻辑
- 简化了表单验证规则

### 6. 清理代码结构
- 移除了不再使用的导入
- 删除了相关的函数和变量
- 简化了数据结构

## 技术实现细节

### 组件导入优化
```javascript
// 移除的导入
- TeamLimitAmount4ShareDetailModal
- getTeamInfoByIdCard, getTeamAndLimitInfoByIdCard
- getCompanyDeptListByPid

// 保留的核心导入
- 基础Vue组件
- 表单相关组件
- 职业病相关API
```

### 数据结构简化
```javascript
// 移除的字段
- nation, bloodType, country, countryCode
- examCardNo, healthNo, postCode, eduLevel
- customerCategory, companyDeptId
- originCustomerIdcard, originCustomerRelation
- 等其他非核心字段

// 保留的核心字段
- 基本个人信息
- 职业病体检专用信息
```

### 验证规则优化
```javascript
// 移除的验证
- 基于体检类别的动态验证
- 原检人相关验证

// 保留的验证
- 基本信息必填验证
- 危害因素相关的岗位类别验证
```

## 用户体验改进

1. **界面简洁**: 移除了不必要的展开/收起操作，所有相关字段直接展示
2. **专业性强**: 专门针对职业病体检场景，字段更加专业和相关
3. **操作简化**: 减少了用户的选择和配置操作
4. **逻辑清晰**: 去除了复杂的条件逻辑，流程更加直观

## 兼容性说明

- 保持了原有的API接口调用
- 保留了核心的保存和登记功能
- 维持了与其他组件的数据交互接口
- 确保了现有业务流程的正常运行

## 新增职业病体检专用列表组件

### 组件创建
创建了 `CustomerRegListOfPannelOccu.vue` 组件，专门用于职业病体检的登记记录列表管理。

### 主要特性
1. **固定查询条件**: 自动过滤只显示职业病体检类型的记录
2. **专业化查询字段**:
   - 增加了单位名称查询
   - 增加了危害因素查询（支持多选）
3. **界面优化**:
   - 标题更改为"职业病体检登记列表"
   - 新增按钮文案为"新增职业病体检登记"
   - 体检分类列显示橙色"职业病体检"标签

### 技术实现
```javascript
// 固定查询参数
const queryParam = reactive<any>({
  dateType: '预约',
  examCategory: '职业病体检'  // 固定为职业病体检
});

// 重置时保持过滤条件
function searchReset() {
  formRef.value.resetFields();
  selectedRowKeys.value = [];
  queryParam.examCategory = '职业病体检'; // 保持过滤条件
  reload();
}
```

## 职业病体检登记页面调整

### 页面更新
更新了 `CustomerRegPannel4Occu.vue` 页面，使其使用新的职业病体检专用组件。

### 主要修改
1. **组件引用**: 将 `CustomerRegListOfPannel` 替换为 `CustomerRegListOfPannelOccu`
2. **标题更新**: 登记详情卡片标题改为"职业病体检登记详情"
3. **导入更新**: 更新了组件导入路径

### 修改内容
```vue
<!-- 原来 -->
<customer-reg-list-of-pannel
  ref="customerRegLiteList"
  @row-click="handleRegTableRowClick"
  @add="handelAddCustomerReg"
  @batch-reg-ok="handleBatchRegOk"
/>

<!-- 修改后 -->
<customer-reg-list-of-pannel-occu
  ref="customerRegLiteList"
  @row-click="handleRegTableRowClick"
  @add="handelAddCustomerReg"
  @batch-reg-ok="handleBatchRegOk"
  @batch-print="handleBatchPrint"
/>
```

## 整体效果

### 用户体验提升
1. **专业化界面**: 整个界面专门针对职业病体检场景设计
2. **简化操作**: 用户无需手动选择体检类型，系统自动过滤
3. **专业查询**: 提供职业病相关的专业查询字段
4. **一致性**: 列表和表单组件保持一致的职业病体检主题

### 功能完整性
1. **保持兼容**: 所有原有功能都得到保留
2. **数据完整**: 职业病体检相关的所有字段都可以正常使用
3. **业务流程**: 登记、收费、打印等业务流程正常运行

## 后续建议

1. 可以考虑为职业病体检添加更多专业化的验证规则
2. 可以根据行业类型动态显示相关的危害因素选项
3. 可以添加职业病体检特有的提示和帮助信息
4. 可以考虑添加职业病体检相关的统计和报表功能
5. 可以优化危害因素的选择界面，提供更好的用户体验
