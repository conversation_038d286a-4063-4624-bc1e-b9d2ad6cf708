# 字典组件助记码搜索功能测试

## 测试页面访问

### 方法一：直接访问路由
在浏览器地址栏输入：
```
http://localhost:3100/#/test/dict-help-char
```

### 方法二：添加菜单配置
在系统管理 -> 菜单管理中添加测试菜单：

```json
{
  "menuName": "字典助记码测试",
  "menuType": 1,
  "path": "/test/dict-help-char",
  "component": "test/DictHelpCharTest",
  "perms": "",
  "icon": "ant-design:experiment-outlined",
  "orderNum": 999,
  "parentId": "系统管理菜单ID"
}
```

### 方法三：临时路由配置
在 `src/router/routes/modules/` 目录下创建 `test.ts` 文件：

```typescript
import type { AppRouteModule } from '/@/router/types';
import { LAYOUT } from '/@/router/constant';

const test: AppRouteModule = {
  path: '/test',
  name: 'Test',
  component: LAYOUT,
  redirect: '/test/dict-help-char',
  meta: {
    orderNo: 999,
    icon: 'ant-design:experiment-outlined',
    title: '功能测试',
  },
  children: [
    {
      path: 'dict-help-char',
      name: 'DictHelpCharTest',
      component: () => import('/@/views/test/DictHelpCharTest.vue'),
      meta: {
        title: '字典助记码测试',
        icon: 'ant-design:search-outlined',
      },
    },
  ],
};

export default test;
```

## 测试用例说明

### 1. 基础兼容性测试
- **目的**：确保现有功能完全不受影响
- **测试项**：
  - JDictSelectTag 基础用法
  - JSearchSelect 基础用法
- **预期结果**：功能正常，与之前完全一致

### 2. 助记码搜索功能测试
- **目的**：验证新增的助记码搜索功能
- **测试项**：
  - both模式：支持文本和助记码搜索
  - helpChar模式：仅支持助记码搜索
  - text模式：仅支持文本搜索
- **预期结果**：
  - 下拉选项显示助记码 [助记码]
  - 搜索时支持对应的匹配模式
  - 占位符文本正确显示

### 3. 自定义表测试
- **目的**：验证自定义数据表的助记码功能
- **测试项**：
  - 带where条件的自定义表查询
  - 助记码字段正确映射
- **预期结果**：
  - 正确查询自定义表数据
  - 助记码字段正确显示和搜索

### 4. 混合使用测试
- **目的**：验证不同模式可以在同一页面共存
- **测试项**：
  - 同时使用基础模式和助记码模式
  - 不同搜索类型的组合使用
- **预期结果**：
  - 各组件独立工作，互不影响
  - 功能正常，无冲突

## 测试步骤

### 步骤1：基础功能验证
1. 打开测试页面
2. 测试基础字典选择功能
3. 确认现有功能正常工作

### 步骤2：助记码功能验证
1. 在启用助记码的组件中输入搜索
2. 验证助记码显示是否正确
3. 测试不同搜索模式的匹配效果

### 步骤3：兼容性验证
1. 同时操作多个组件
2. 验证组件间无相互影响
3. 检查控制台是否有错误

### 步骤4：数据验证
1. 选择不同的选项
2. 查看表单数据是否正确
3. 验证数据格式和值的准确性

## 预期测试结果

### ✅ 成功指标
- [ ] 所有现有功能正常工作
- [ ] 助记码正确显示在选项中
- [ ] 搜索功能按预期工作
- [ ] 不同搜索模式正确匹配
- [ ] 表单数据正确收集
- [ ] 无JavaScript错误
- [ ] 组件样式正常显示

### ❌ 失败指标
- 现有功能出现异常
- 助记码不显示或显示错误
- 搜索功能不工作
- 控制台出现错误
- 组件样式异常
- 数据收集错误

## 问题排查

### 常见问题及解决方案

1. **助记码不显示**
   - 检查 `helpCharField` 是否正确设置
   - 确认数据库表中是否存在对应字段
   - 验证 `showHelpChar` 是否为 true

2. **搜索不生效**
   - 检查 `enableHelpCharSearch` 是否为 true
   - 确认 `searchType` 设置是否正确
   - 验证后端接口是否支持新参数

3. **样式异常**
   - 检查CSS样式是否正确加载
   - 确认组件版本兼容性

4. **数据错误**
   - 检查字段映射是否正确
   - 验证数据格式和类型

## 性能测试

### 测试项目
- [ ] 大数据量下的搜索响应时间
- [ ] 内存使用情况
- [ ] 网络请求频率
- [ ] 组件渲染性能

### 性能指标
- 搜索响应时间 < 500ms
- 内存使用无明显增长
- 网络请求合理防抖
- 组件渲染流畅

通过以上测试，可以全面验证字典组件助记码搜索功能的正确性和兼容性。
