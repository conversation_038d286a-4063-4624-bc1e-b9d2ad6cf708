# 设备中间件事件处理说明

## 概述

本文档说明了如何处理设备中间件服务发送的各种事件，包括`testResult`、`portStatus`和`serialData`事件，特别是针对肺功能测试设备（FGY-200）的处理逻辑。同时介绍了设备用户映射机制，确保多台设备连接时数据不会错乱。

## 设备用户映射机制

### 概述

为了确保在连接多台设备时数据不会错乱，系统实现了设备用户映射机制。每个设备通过`deviceId`与当前用户建立映射关系，确保设备数据只能被对应的用户接收和处理。

### 映射管理

- **注册映射**: 当设备连接成功时，自动将设备ID注册到当前用户
- **验证映射**: 在接收设备数据前，验证设备是否属于当前用户
- **释放映射**: 当设备断开连接或用户退出时，释放映射关系

## 事件数据结构

### testResult事件

设备中间件服务发送的`testResult`事件包含以下数据结构：

```javascript
{
  "deviceId": "设备ID",
  "deviceModel": "设备型号",
  "patientId": "患者检查号",
  "patientName": "患者姓名",
  "resultType": "LUNG_FUNCTION_TEST",
  "data": "肺功能测试结果数据",
  "testDuration": 测试持续时间(毫秒),
  "timestamp": 时间戳
}
```

### 字段说明

- **deviceId**: 设备唯一标识符（保留字段）
- **deviceModel**: 设备型号，用于匹配设备配置
- **patientId**: 患者检查号，必须与当前体检记录的examNo匹配
- **patientName**: 患者姓名，用于显示和确认
- **resultType**: 结果类型，目前支持`LUNG_FUNCTION_TEST`（肺功能测试）
- **data**: 测试结果的原始数据，将传递给设备的数据解析函数
- **testDuration**: 测试持续时间（毫秒），用于显示测试耗时
- **timestamp**: 测试完成的时间戳

### portStatus事件

设备状态变化事件包含以下数据结构：

```javascript
{
  "deviceId": "设备ID",
  "port": "串口描述符",
  "open": true/false,
  // 其他状态信息...
}
```

### serialData事件

串口数据接收事件包含以下数据结构：

```javascript
{
  "deviceId": "设备ID",
  "port": "串口描述符",
  "data": "设备数据",
  // 其他数据信息...
}
```

## 处理流程

### 1. 事件监听

在`ItemResultPannel.vue`的WebSocket事件处理器中添加了`testResult`事件监听：

```javascript
testResult: (resultData: any) => {
  console.log('接收到设备测试结果:', resultData);
  handleTestResult(resultData, comEquipments);
}
```

### 2. portStatus事件处理

`portStatus`事件处理流程：

1. **设备匹配**: 优先使用`deviceId`匹配设备配置，如果没有则使用`port`
2. **用户验证**: 验证设备是否属于当前用户或未被占用
3. **状态处理**:
   - 连接成功：注册设备到当前用户，更新状态，发送指令
   - 连接失败：释放设备映射，更新错误状态

### 3. serialData事件处理

`serialData`事件处理流程：

1. **设备匹配**: 使用`port`匹配设备配置
2. **数据处理**: 调用设备的数据解析函数处理接收到的数据
3. **人员匹配**: 通过设备的数据解析函数内部逻辑确保数据与当前体检人员匹配

**注意**: serialData事件保持原有的简单逻辑，因为原来的逻辑中已经包含了保证数据匹配人员的机制。

### 4. testResult事件处理

`handleTestResult`函数执行以下验证步骤：

1. **必要字段验证**: 检查`deviceModel`、`patientId`、`resultType`、`data`字段是否存在
2. **结果类型验证**: 确认`resultType`为`LUNG_FUNCTION_TEST`
3. **患者信息验证**: 验证`patientId`是否与当前体检记录的`examNo`匹配
4. **用户验证**: 如果有`deviceId`，验证设备是否属于当前用户
5. **设备配置验证**: 根据`deviceModel`查找对应的设备配置
6. **项目组验证**: 查找设备对应的项目组配置
7. **体检状态验证**: 确认项目组状态为"未检"

### 3. 数据处理

验证通过后，执行以下处理步骤：

1. **获取解析函数**: 从设备配置中获取`dataFunction`
2. **更新状态**: 设置设备状态为"正在处理设备测试结果..."
3. **调用解析函数**: 使用`dataFunction(group, resultData.data)`处理测试数据
4. **更新完成状态**: 显示处理完成信息和测试耗时
5. **清除测试状态**: 重置`testingInProgress`标志

## 设备用户映射详细说明

### 映射函数

#### registerDeviceToCurrentUser(deviceId)
- **功能**: 将设备注册到当前用户
- **调用时机**: 设备连接成功时
- **参数**: deviceId - 设备唯一标识符

#### validateDeviceOwnership(deviceId)
- **功能**: 验证设备是否属于当前用户
- **返回值**: boolean - true表示可以使用，false表示被其他用户占用
- **逻辑**:
  - 如果没有映射关系，返回true（设备可用）
  - 如果有映射关系，检查是否属于当前用户

#### releaseDeviceMapping(deviceId)
- **功能**: 释放设备映射关系
- **调用时机**: 设备断开连接时

#### clearCurrentUserDeviceMappings()
- **功能**: 清理当前用户的所有设备映射
- **调用时机**: 组件卸载时

### 映射策略

1. **独占模式**: 一个设备同时只能被一个用户使用
2. **自动注册**: 设备连接成功时自动注册到当前用户
3. **自动释放**: 设备断开或用户退出时自动释放映射
4. **冲突处理**: 当接收到其他用户设备的数据时，显示警告并忽略

## 错误处理

### 常见错误情况

1. **数据不完整**: 缺少必要字段时显示错误提示
2. **患者信息不匹配**: 当接收到的患者信息与当前体检记录不符时发出警告
3. **设备配置缺失**: 找不到对应设备配置时显示错误
4. **体检状态异常**: 当项目组状态不是"未检"时拒绝处理
5. **解析函数缺失**: 设备未配置数据解析代码时显示错误
6. **处理异常**: 数据解析过程中的任何异常都会被捕获并显示
7. **设备被占用**: 当设备已被其他用户占用时显示警告并忽略数据

### 错误恢复

- 发生错误时，自动重置设备状态和测试进度
- 显示具体的错误信息帮助用户排查问题
- 记录详细的错误日志便于调试

## 与串口设备处理的区别

### 相似点

- 都使用WebSocket进行通信
- 都需要设备配置和数据解析函数
- 都有状态管理和错误处理机制

### 不同点

| 特性 | 串口设备(serialData) | 设备中间件(testResult) | portStatus事件 |
|------|---------------------|----------------------|----------------|
| 触发方式 | 实时数据流 | 完整测试结果 | 设备状态变化 |
| 数据标识 | 端口描述符(port) | 设备型号(deviceModel) | 设备ID/端口描述符 |
| 用户验证 | 无（保持原有逻辑） | 支持设备ID验证 | 支持设备ID验证 |
| 患者验证 | 通过数据解析函数内部逻辑 | 严格验证患者信息 | 无 |
| 测试状态 | 无特殊处理 | 自动清除测试进度 | 管理设备连接状态 |
| 耗时显示 | 无 | 显示测试持续时间 | 无 |
| 映射管理 | 无 | 验证映射关系 | 注册/释放映射关系 |

## 配置要求

### 设备配置

设备配置中需要包含以下字段：

- **deviceModel**: 设备型号，必须与testResult事件中的deviceModel字段匹配
- **groupId**: 关联的项目组ID
- **dataFunction**: 数据解析函数代码

#### 设备型号匹配

系统通过比较设备配置表中的`deviceModel`字段与testResult事件中的`deviceModel`字段来找到对应的设备配置。

**注意**: `deviceModel`必须与设备中间件服务中配置的设备型号完全一致。

### 数据解析函数

数据解析函数的签名保持不变：

```javascript
function(group, comData) {
  // 处理测试结果数据
  // group: 项目组对象
  // comData: 从testResult.data传入的测试数据
}
```

## 日志和调试

### 关键日志

- 接收到测试结果时的完整数据日志
- 患者信息匹配验证日志
- 数据处理完成的统计信息
- 错误处理的详细信息

### 调试建议

1. 检查浏览器控制台中的WebSocket连接状态
2. 确认设备ID配置是否正确
3. 验证患者检查号是否匹配
4. 检查数据解析函数是否正确配置
5. 确认项目组状态是否为"未检"

## 扩展性

该处理机制设计为可扩展的：

- 可以通过修改`resultType`支持其他类型的设备测试结果
- 可以添加更多的验证规则和处理逻辑
- 可以扩展状态管理和用户反馈机制

## 配置示例

### 设备配置表示例

在设备管理界面中，需要确保设备型号字段正确配置：

```
设备名称: 肺功能测试仪FGY-200
设备型号: FGY-200  // 这个值必须与testResult.deviceModel匹配
关联项目: 肺功能检查
数据解析函数: function(group, comData) { ... }
```

## 注意事项

1. **设备型号匹配**: 确保设备配置中的deviceModel与中间件服务发送的完全一致
2. **设备ID配置**: 在设备的extraConfig中正确配置deviceId，确保与中间件服务一致
3. **用户隔离**: 设备用户映射机制确保多用户环境下的数据隔离
4. **安全性**: 严格验证患者信息和设备归属，防止数据混乱
5. **稳定性**: 完善的错误处理确保系统稳定运行
6. **用户体验**: 清晰的状态提示和错误信息
7. **性能**: 异步处理避免阻塞用户界面
8. **兼容性**: 与现有串口设备处理逻辑保持兼容，支持新旧两种匹配方式
9. **资源管理**: 组件卸载时自动清理设备映射关系，避免内存泄漏
