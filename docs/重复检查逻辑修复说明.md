# 重复检查逻辑修复说明

## 问题描述

套餐添加项目时，重复校验不起作用，没有正确地与已有项目列表进行项目ID和部位ID的比对，导致重复项目被添加。

## 问题原因

原有的重复检查逻辑依赖于 `getRowKey` 函数进行字符串比较，这种方式不够直接和可靠：

```javascript
// 原有逻辑（有问题）
function isItemExists(itemGroupId: string, checkPartId?: string): boolean {
  const targetKey = checkPartId ? `${itemGroupId}-${checkPartId}` : itemGroupId;
  return dataSource.value.some(row => getRowKey(row) === targetKey);
}
```

## 修复方案

### 1. 直接比对字段值

重写 `isItemExists` 函数，直接比对项目ID和部位ID字段：

```javascript
function isItemExists(itemGroupId: string, checkPartId?: string): boolean {
  return dataSource.value.some(row => {
    const itemIdMatch = String(row.itemGroupId) === String(itemGroupId);
    
    if (checkPartId) {
      // 有部位的情况：项目ID和部位ID都必须匹配
      const partIdMatch = String(row.checkPartId || '') === String(checkPartId);
      return itemIdMatch && partIdMatch;
    } else {
      // 无部位的情况：只比对项目ID，且现有项目也不能有部位
      const rowHasNoPart = !row.checkPartId || row.checkPartId === '' || row.checkPartId === null;
      return itemIdMatch && rowHasNoPart;
    }
  });
}
```

### 2. 检查逻辑说明

#### 有部位项目的检查
- **条件**：`itemGroupId` 和 `checkPartId` 都提供
- **逻辑**：项目ID匹配 **且** 部位ID匹配
- **示例**：项目A-左侧 vs 项目A-右侧 = 不重复

#### 无部位项目的检查
- **条件**：只提供 `itemGroupId`，`checkPartId` 为空
- **逻辑**：项目ID匹配 **且** 现有项目也没有部位
- **示例**：项目B vs 项目B-左侧 = 不重复

### 3. 数据类型处理

使用 `String()` 转换确保数据类型一致：
- 避免字符串与数字比较的问题
- 处理 `null`、`undefined` 等特殊值

### 4. 调试功能

#### 控制台输出
```javascript
console.log(`检查项目: ${itemGroupId}${checkPartId ? `-${checkPartId}` : '(无部位)'}`);
console.log(`结果: ${exists ? '已存在，跳过' : '不存在，添加'}`);
```

#### 测试函数
添加了 `testDuplicateCheck()` 函数用于调试：
```javascript
// 在浏览器控制台中调用
window.testDuplicateCheck();
```

## 使用方法

### 1. 套餐添加时的调用

#### 有预设部位的项目
```javascript
const exists = isItemExists(group.id, group.checkPartId);
if (!exists) {
  // 添加项目
}
```

#### 无部位的项目
```javascript
const exists = isItemExists(group.id);
if (!exists) {
  // 添加项目
}
```

### 2. 调试步骤

1. **打开开发者工具**：按F12，切换到Console标签
2. **执行套餐添加**：选择套餐进行添加
3. **观察输出**：查看每个项目的检查结果
4. **手动测试**：在控制台执行 `window.testDuplicateCheck()`

### 3. 预期输出示例

```
=== 检查有预设部位的项目 ===
项目名称: 胸部CT-左侧
检查项目: 1833316037517512706-part001
结果: 不存在，添加

=== 检查无部位项目 ===
项目名称: 血常规
检查项目: 1833316037517512707(无部位)
结果: 已存在，跳过
```

## 测试场景

### 1. 基本重复检查
- **场景**：添加已存在的项目
- **预期**：返回 `true`，跳过添加

### 2. 部位区分检查
- **场景**：同一项目的不同部位
- **预期**：返回 `false`，允许添加

### 3. 部位与无部位区分
- **场景**：同一项目，一个有部位，一个无部位
- **预期**：返回 `false`，允许添加

### 4. 数据类型兼容性
- **场景**：字符串ID vs 数字ID
- **预期**：正确比较，不受数据类型影响

## 常见问题排查

### 问题1：所有项目都被认为是重复
**可能原因**：
- `itemGroupId` 字段名错误
- 数据结构不匹配

**排查方法**：
```javascript
console.log('检查数据结构:', dataSource.value[0]);
```

### 问题2：部位项目检查不正确
**可能原因**：
- `checkPartId` 字段处理错误
- 部位数据格式异常

**排查方法**：
```javascript
console.log('部位数据:', {
  checkPartId: group.checkPartId,
  checkPartName: group.checkPartName
});
```

### 问题3：无部位项目检查失效
**可能原因**：
- 空值判断逻辑错误
- 数据中存在意外的部位值

**排查方法**：
```javascript
dataSource.value.forEach(item => {
  console.log(`项目: ${item.itemGroupName}, 部位: "${item.checkPartId}"`);
});
```

## 性能考虑

### 1. 时间复杂度
- **当前实现**：O(n)，其中n是已有项目数量
- **优化建议**：如果项目数量很大，可以考虑使用Map缓存

### 2. 内存使用
- **String转换**：临时创建字符串，影响较小
- **some方法**：找到匹配项时立即返回，效率较高

## 后续优化

### 1. 缓存优化
```javascript
// 可以考虑的优化方案
const existingItemsMap = new Map();
dataSource.value.forEach(item => {
  const key = item.checkPartId ? 
    `${item.itemGroupId}-${item.checkPartId}` : 
    item.itemGroupId;
  existingItemsMap.set(key, true);
});
```

### 2. 批量检查
```javascript
// 批量检查多个项目
function batchCheckExists(items: Array<{id: string, partId?: string}>) {
  return items.map(item => isItemExists(item.id, item.partId));
}
```

## 相关文件

- `src/views/reg/components/TeamGroupCard.vue` - 主要修改文件
- `docs/重复检查逻辑修复说明.md` - 本文档

## 修复验证

修复完成后，请验证以下场景：

1. ✅ 添加已存在的项目被正确跳过
2. ✅ 添加新项目被正确添加
3. ✅ 同一项目的不同部位被正确区分
4. ✅ 有部位和无部位的项目被正确区分
5. ✅ 数据类型不一致时仍能正确比较

如果以上场景都通过，说明重复检查逻辑修复成功。
