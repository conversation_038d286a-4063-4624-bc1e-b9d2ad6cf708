package org.jeecg.modules.basicinfo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 扩展现有CheckPartDict实体类以支持新功能
 * 现有字段：id, code, name
 * 新增字段：helpChar, category, sortOrder, enableFlag, remark, createBy, createTime, updateBy, updateTime, delFlag
 * 
 * @Description: 检查部位字典
 * @Author: jeecg-boot
 * @Date: 2024-12-19
 * @Version: V1.0
 */
@Data
@TableName("check_part_dict")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "check_part_dict对象", description = "检查部位字典")
public class CheckPartDict implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键ID*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**检查部位编码*/
    @Excel(name = "检查部位编码", width = 15)
    @ApiModelProperty(value = "检查部位编码")
    private String code;

    /**检查部位名称*/
    @Excel(name = "检查部位名称", width = 15)
    @ApiModelProperty(value = "检查部位名称")
    private String name;

    /**拼音缩写*/
    @Excel(name = "拼音缩写", width = 15)
    @ApiModelProperty(value = "拼音缩写")
    private String helpChar;

    /**部位分类*/
    @Excel(name = "部位分类", width = 15)
    @ApiModelProperty(value = "部位分类")
    private String category;

    /**排序号*/
    @Excel(name = "排序号", width = 15)
    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;

    /**启用状态*/
    @Excel(name = "启用状态", width = 15)
    @ApiModelProperty(value = "启用状态(1-启用,0-禁用)")
    private String enableFlag;

    /**备注*/
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**删除标志*/
    @ApiModelProperty(value = "删除标志(0-正常,1-删除)")
    private Integer delFlag;

    /**使用频次（非持久化字段）*/
    @TableField(exist = false)
    @ApiModelProperty(value = "使用频次")
    private Long frequency;
}
