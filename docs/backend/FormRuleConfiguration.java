package org.jeecg.modules.formrule.config;

import org.jeecg.modules.formrule.websocket.FormRuleRedisMessageListener;
import org.jeecg.modules.formrule.websocket.FormRuleWebSocketHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * 表单规则模块配置类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Configuration
@EnableWebSocket
public class FormRuleConfiguration implements WebSocketConfigurer {

    @Autowired
    private FormRuleWebSocketHandler formRuleWebSocketHandler;

    @Autowired
    private FormRuleRedisMessageListener redisMessageListener;

    /**
     * 注册WebSocket处理器
     */
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(formRuleWebSocketHandler, "/ws/form-rule")
                .setAllowedOrigins("*") // 生产环境应该配置具体的域名
                .withSockJS(); // 启用SockJS支持
    }

    /**
     * Redis消息监听容器
     */
    @Bean
    public RedisMessageListenerContainer redisMessageListenerContainer(
            RedisConnectionFactory connectionFactory) {
        
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        
        // 监听表单规则更新频道
        container.addMessageListener(
                new MessageListenerAdapter(redisMessageListener),
                new ChannelTopic("form_rule_update")
        );
        
        return container;
    }

    /**
     * 表单规则缓存配置
     */
    @Bean
    public FormRuleCacheConfig formRuleCacheConfig() {
        return new FormRuleCacheConfig();
    }

    /**
     * 表单规则缓存配置类
     */
    public static class FormRuleCacheConfig {
        
        // 缓存过期时间（小时）
        private int cacheExpireHours = 24;
        
        // 版本缓存过期时间（小时）
        private int versionCacheExpireHours = 1;
        
        // 分布式锁过期时间（分钟）
        private int lockExpireMinutes = 5;
        
        // 是否启用缓存预热
        private boolean enableCacheWarmup = true;
        
        // 预热的表单代码列表
        private String[] warmupFormCodes = {
            "customer_reg_form",
            "employee_reg_form",
            "order_form"
        };

        // getter and setter methods
        public int getCacheExpireHours() { return cacheExpireHours; }
        public void setCacheExpireHours(int cacheExpireHours) { this.cacheExpireHours = cacheExpireHours; }
        
        public int getVersionCacheExpireHours() { return versionCacheExpireHours; }
        public void setVersionCacheExpireHours(int versionCacheExpireHours) { this.versionCacheExpireHours = versionCacheExpireHours; }
        
        public int getLockExpireMinutes() { return lockExpireMinutes; }
        public void setLockExpireMinutes(int lockExpireMinutes) { this.lockExpireMinutes = lockExpireMinutes; }
        
        public boolean isEnableCacheWarmup() { return enableCacheWarmup; }
        public void setEnableCacheWarmup(boolean enableCacheWarmup) { this.enableCacheWarmup = enableCacheWarmup; }
        
        public String[] getWarmupFormCodes() { return warmupFormCodes; }
        public void setWarmupFormCodes(String[] warmupFormCodes) { this.warmupFormCodes = warmupFormCodes; }
    }
}

/**
 * 表单规则启动配置
 */
@Component
@Slf4j
class FormRuleStartupConfiguration implements ApplicationRunner {

    @Autowired
    private IFormRuleService formRuleService;

    @Autowired
    private FormRuleConfiguration.FormRuleCacheConfig cacheConfig;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("表单规则模块启动配置开始执行...");
        
        try {
            // 缓存预热
            if (cacheConfig.isEnableCacheWarmup()) {
                warmupCache();
            }
            
            // 清理过期数据
            cleanupExpiredData();
            
            log.info("表单规则模块启动配置执行完成");
        } catch (Exception e) {
            log.error("表单规则模块启动配置执行失败", e);
        }
    }

    /**
     * 缓存预热
     */
    private void warmupCache() {
        try {
            String[] formCodes = cacheConfig.getWarmupFormCodes();
            if (formCodes != null && formCodes.length > 0) {
                List<String> formCodeList = Arrays.asList(formCodes);
                formRuleService.warmupCache(formCodeList);
                log.info("表单规则缓存预热完成: formCodes={}", formCodeList);
            }
        } catch (Exception e) {
            log.error("表单规则缓存预热失败", e);
        }
    }

    /**
     * 清理过期数据
     */
    private void cleanupExpiredData() {
        try {
            // 这里可以添加清理过期数据的逻辑
            // 比如清理过期的变更日志、缓存版本信息等
            log.info("表单规则过期数据清理完成");
        } catch (Exception e) {
            log.error("表单规则过期数据清理失败", e);
        }
    }
}

/**
 * 表单规则定时任务配置
 */
@Component
@Slf4j
class FormRuleScheduledTasks {

    @Autowired
    private FormRuleCacheService formRuleCacheService;

    @Autowired
    private FormRuleConfigMapper formRuleConfigMapper;

    /**
     * 每小时清理过期缓存
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void cleanupExpiredCache() {
        try {
            log.info("开始清理过期的表单规则缓存...");
            
            // 获取缓存统计信息
            FormRuleCacheService.CacheStatistics before = formRuleCacheService.getCacheStatistics();
            
            // 清理过期缓存（这里可以根据实际需求实现具体的清理逻辑）
            // formRuleCacheService.cleanupExpiredCache();
            
            FormRuleCacheService.CacheStatistics after = formRuleCacheService.getCacheStatistics();
            
            log.info("表单规则缓存清理完成: before={}, after={}", 
                    JSON.toJSONString(before), JSON.toJSONString(after));
        } catch (Exception e) {
            log.error("清理过期表单规则缓存失败", e);
        }
    }

    /**
     * 每天清理过期的变更日志
     */
    @Scheduled(cron = "0 2 0 * * ?")
    public void cleanupExpiredChangeLogs() {
        try {
            log.info("开始清理过期的表单规则变更日志...");
            
            // 清理30天前的变更日志
            String beforeDate = LocalDateTime.now().minusDays(30)
                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            
            int cleanedCount = formRuleConfigMapper.cleanupExpiredLogs(beforeDate);
            
            log.info("表单规则变更日志清理完成: cleanedCount={}", cleanedCount);
        } catch (Exception e) {
            log.error("清理过期表单规则变更日志失败", e);
        }
    }

    /**
     * 每天统计表单规则使用情况
     */
    @Scheduled(cron = "0 30 0 * * ?")
    public void generateDailyStatistics() {
        try {
            log.info("开始生成表单规则每日统计...");
            
            FormRuleStatisticsVO statistics = formRuleConfigMapper.getStatistics();
            
            // 这里可以将统计信息保存到数据库或发送报告
            log.info("表单规则每日统计: {}", JSON.toJSONString(statistics));
        } catch (Exception e) {
            log.error("生成表单规则每日统计失败", e);
        }
    }
}

/**
 * 表单规则健康检查
 */
@Component
class FormRuleHealthIndicator implements HealthIndicator {

    @Autowired
    private FormRuleCacheService formRuleCacheService;

    @Autowired
    private IFormRuleService formRuleService;

    @Override
    public Health health() {
        try {
            // 检查缓存服务
            FormRuleCacheService.CacheStatistics cacheStats = formRuleCacheService.getCacheStatistics();
            
            // 检查数据库连接
            FormRuleStatisticsVO dbStats = formRuleService.getStatistics();
            
            return Health.up()
                    .withDetail("cache", cacheStats)
                    .withDetail("database", dbStats)
                    .withDetail("timestamp", System.currentTimeMillis())
                    .build();
        } catch (Exception e) {
            return Health.down()
                    .withDetail("error", e.getMessage())
                    .withDetail("timestamp", System.currentTimeMillis())
                    .build();
        }
    }
}

/**
 * 表单规则监控指标
 */
@Component
class FormRuleMetrics {

    private final MeterRegistry meterRegistry;
    private final Counter ruleUpdateCounter;
    private final Timer ruleQueryTimer;
    private final Gauge cacheHitRatio;

    @Autowired
    private FormRuleCacheService formRuleCacheService;

    public FormRuleMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        
        // 规则更新计数器
        this.ruleUpdateCounter = Counter.builder("form_rule_updates_total")
                .description("Total number of form rule updates")
                .register(meterRegistry);
        
        // 规则查询计时器
        this.ruleQueryTimer = Timer.builder("form_rule_query_duration")
                .description("Form rule query duration")
                .register(meterRegistry);
        
        // 缓存命中率
        this.cacheHitRatio = Gauge.builder("form_rule_cache_hit_ratio")
                .description("Form rule cache hit ratio")
                .register(meterRegistry, this, FormRuleMetrics::calculateCacheHitRatio);
    }

    public void incrementRuleUpdate() {
        ruleUpdateCounter.increment();
    }

    public Timer.Sample startQueryTimer() {
        return Timer.start(meterRegistry);
    }

    public void recordQueryTime(Timer.Sample sample) {
        sample.stop(ruleQueryTimer);
    }

    private double calculateCacheHitRatio(FormRuleMetrics metrics) {
        try {
            // 这里需要实现缓存命中率的计算逻辑
            // 可以通过统计缓存命中次数和总查询次数来计算
            return 0.85; // 示例值
        } catch (Exception e) {
            return 0.0;
        }
    }
}
