package org.jeecg.modules.formrule.websocket;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * 表单规则WebSocket处理器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Component
@Slf4j
public class FormRuleWebSocketHandler implements WebSocketHandler {

    // 存储WebSocket会话
    private static final ConcurrentHashMap<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    
    // 存储用户订阅的表单代码
    private static final ConcurrentHashMap<String, CopyOnWriteArraySet<String>> userSubscriptions = new ConcurrentHashMap<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String sessionId = session.getId();
        String userId = getUserId(session);
        
        sessions.put(sessionId, session);
        log.info("WebSocket连接建立: sessionId={}, userId={}", sessionId, userId);
        
        // 发送连接成功消息
        sendMessage(session, new WebSocketMessage("CONNECTION_ESTABLISHED", "连接成功", null));
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        String sessionId = session.getId();
        String userId = getUserId(session);
        
        try {
            if (message instanceof TextMessage) {
                String payload = ((TextMessage) message).getPayload();
                WebSocketRequest request = JSON.parseObject(payload, WebSocketRequest.class);
                
                handleWebSocketRequest(session, request);
            }
        } catch (Exception e) {
            log.error("处理WebSocket消息失败: sessionId={}, userId={}", sessionId, userId, e);
            sendErrorMessage(session, "消息处理失败: " + e.getMessage());
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        String sessionId = session.getId();
        String userId = getUserId(session);
        
        log.error("WebSocket传输错误: sessionId={}, userId={}", sessionId, userId, exception);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        String sessionId = session.getId();
        String userId = getUserId(session);
        
        // 清理会话和订阅信息
        sessions.remove(sessionId);
        userSubscriptions.remove(sessionId);
        
        log.info("WebSocket连接关闭: sessionId={}, userId={}, closeStatus={}", 
                sessionId, userId, closeStatus);
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * 处理WebSocket请求
     */
    private void handleWebSocketRequest(WebSocketSession session, WebSocketRequest request) {
        String action = request.getAction();
        String sessionId = session.getId();
        
        switch (action) {
            case "SUBSCRIBE":
                handleSubscribe(session, request);
                break;
            case "UNSUBSCRIBE":
                handleUnsubscribe(session, request);
                break;
            case "PING":
                handlePing(session);
                break;
            default:
                log.warn("未知的WebSocket请求类型: sessionId={}, action={}", sessionId, action);
                sendErrorMessage(session, "未知的请求类型: " + action);
        }
    }

    /**
     * 处理订阅请求
     */
    private void handleSubscribe(WebSocketSession session, WebSocketRequest request) {
        String sessionId = session.getId();
        String formCode = (String) request.getData();
        
        if (formCode == null || formCode.trim().isEmpty()) {
            sendErrorMessage(session, "表单代码不能为空");
            return;
        }
        
        userSubscriptions.computeIfAbsent(sessionId, k -> new CopyOnWriteArraySet<>()).add(formCode);
        
        log.info("用户订阅表单规则更新: sessionId={}, formCode={}", sessionId, formCode);
        sendMessage(session, new WebSocketMessage("SUBSCRIBE_SUCCESS", "订阅成功", formCode));
    }

    /**
     * 处理取消订阅请求
     */
    private void handleUnsubscribe(WebSocketSession session, WebSocketRequest request) {
        String sessionId = session.getId();
        String formCode = (String) request.getData();
        
        CopyOnWriteArraySet<String> subscriptions = userSubscriptions.get(sessionId);
        if (subscriptions != null) {
            subscriptions.remove(formCode);
        }
        
        log.info("用户取消订阅表单规则更新: sessionId={}, formCode={}", sessionId, formCode);
        sendMessage(session, new WebSocketMessage("UNSUBSCRIBE_SUCCESS", "取消订阅成功", formCode));
    }

    /**
     * 处理心跳请求
     */
    private void handlePing(WebSocketSession session) {
        sendMessage(session, new WebSocketMessage("PONG", "心跳响应", System.currentTimeMillis()));
    }

    /**
     * 广播表单规则更新消息
     */
    public void broadcastRuleUpdate(String formCode, Object ruleData) {
        WebSocketMessage message = new WebSocketMessage("RULE_UPDATE", "表单规则已更新", 
                new RuleUpdateData(formCode, ruleData));
        
        int sentCount = 0;
        for (String sessionId : userSubscriptions.keySet()) {
            CopyOnWriteArraySet<String> subscriptions = userSubscriptions.get(sessionId);
            if (subscriptions != null && subscriptions.contains(formCode)) {
                WebSocketSession session = sessions.get(sessionId);
                if (session != null && session.isOpen()) {
                    if (sendMessage(session, message)) {
                        sentCount++;
                    }
                }
            }
        }
        
        log.info("广播表单规则更新消息: formCode={}, sentCount={}", formCode, sentCount);
    }

    /**
     * 广播状态变更消息
     */
    public void broadcastStatusChange(String formCode, Integer status) {
        WebSocketMessage message = new WebSocketMessage("STATUS_CHANGE", "表单规则状态已变更", 
                new StatusChangeData(formCode, status));
        
        int sentCount = 0;
        for (String sessionId : userSubscriptions.keySet()) {
            CopyOnWriteArraySet<String> subscriptions = userSubscriptions.get(sessionId);
            if (subscriptions != null && subscriptions.contains(formCode)) {
                WebSocketSession session = sessions.get(sessionId);
                if (session != null && session.isOpen()) {
                    if (sendMessage(session, message)) {
                        sentCount++;
                    }
                }
            }
        }
        
        log.info("广播表单规则状态变更消息: formCode={}, status={}, sentCount={}", 
                formCode, status, sentCount);
    }

    /**
     * 发送消息到指定会话
     */
    private boolean sendMessage(WebSocketSession session, WebSocketMessage message) {
        try {
            if (session.isOpen()) {
                String messageJson = JSON.toJSONString(message);
                session.sendMessage(new TextMessage(messageJson));
                return true;
            }
        } catch (IOException e) {
            log.error("发送WebSocket消息失败: sessionId={}", session.getId(), e);
            // 清理无效会话
            cleanupSession(session.getId());
        }
        return false;
    }

    /**
     * 发送错误消息
     */
    private void sendErrorMessage(WebSocketSession session, String errorMessage) {
        WebSocketMessage message = new WebSocketMessage("ERROR", errorMessage, null);
        sendMessage(session, message);
    }

    /**
     * 清理无效会话
     */
    private void cleanupSession(String sessionId) {
        sessions.remove(sessionId);
        userSubscriptions.remove(sessionId);
        log.info("清理无效WebSocket会话: sessionId={}", sessionId);
    }

    /**
     * 获取用户ID
     */
    private String getUserId(WebSocketSession session) {
        // 从session中获取用户ID，这里需要根据实际的认证机制实现
        Object userId = session.getAttributes().get("userId");
        return userId != null ? userId.toString() : "anonymous";
    }

    /**
     * 获取连接统计信息
     */
    public ConnectionStatistics getConnectionStatistics() {
        int totalConnections = sessions.size();
        int totalSubscriptions = userSubscriptions.values().stream()
                .mapToInt(CopyOnWriteArraySet::size)
                .sum();
        
        return new ConnectionStatistics(totalConnections, totalSubscriptions);
    }

    /**
     * WebSocket消息类
     */
    public static class WebSocketMessage {
        private String type;
        private String message;
        private Object data;
        private long timestamp;

        public WebSocketMessage() {
            this.timestamp = System.currentTimeMillis();
        }

        public WebSocketMessage(String type, String message, Object data) {
            this.type = type;
            this.message = message;
            this.data = data;
            this.timestamp = System.currentTimeMillis();
        }

        // getter and setter methods
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public Object getData() { return data; }
        public void setData(Object data) { this.data = data; }
        
        public long getTimestamp() { return timestamp; }
        public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
    }

    /**
     * WebSocket请求类
     */
    public static class WebSocketRequest {
        private String action;
        private Object data;

        // getter and setter methods
        public String getAction() { return action; }
        public void setAction(String action) { this.action = action; }
        
        public Object getData() { return data; }
        public void setData(Object data) { this.data = data; }
    }

    /**
     * 规则更新数据类
     */
    public static class RuleUpdateData {
        private String formCode;
        private Object ruleData;

        public RuleUpdateData(String formCode, Object ruleData) {
            this.formCode = formCode;
            this.ruleData = ruleData;
        }

        // getter and setter methods
        public String getFormCode() { return formCode; }
        public void setFormCode(String formCode) { this.formCode = formCode; }
        
        public Object getRuleData() { return ruleData; }
        public void setRuleData(Object ruleData) { this.ruleData = ruleData; }
    }

    /**
     * 状态变更数据类
     */
    public static class StatusChangeData {
        private String formCode;
        private Integer status;

        public StatusChangeData(String formCode, Integer status) {
            this.formCode = formCode;
            this.status = status;
        }

        // getter and setter methods
        public String getFormCode() { return formCode; }
        public void setFormCode(String formCode) { this.formCode = formCode; }
        
        public Integer getStatus() { return status; }
        public void setStatus(Integer status) { this.status = status; }
    }

    /**
     * 连接统计信息类
     */
    public static class ConnectionStatistics {
        private int totalConnections;
        private int totalSubscriptions;

        public ConnectionStatistics(int totalConnections, int totalSubscriptions) {
            this.totalConnections = totalConnections;
            this.totalSubscriptions = totalSubscriptions;
        }

        // getter and setter methods
        public int getTotalConnections() { return totalConnections; }
        public void setTotalConnections(int totalConnections) { this.totalConnections = totalConnections; }
        
        public int getTotalSubscriptions() { return totalSubscriptions; }
        public void setTotalSubscriptions(int totalSubscriptions) { this.totalSubscriptions = totalSubscriptions; }
    }
}

/**
 * Redis消息监听器
 */
@Component
@Slf4j
class FormRuleRedisMessageListener implements MessageListener {

    @Autowired
    private FormRuleWebSocketHandler webSocketHandler;

    @Override
    public void onMessage(Message message, byte[] pattern) {
        try {
            String channel = new String(message.getChannel());
            String messageBody = new String(message.getBody());
            
            if ("form_rule_update".equals(channel)) {
                handleRuleUpdateMessage(messageBody);
            }
        } catch (Exception e) {
            log.error("处理Redis消息失败", e);
        }
    }

    /**
     * 处理规则更新消息
     */
    private void handleRuleUpdateMessage(String messageBody) {
        try {
            FormRuleNotificationService.NotificationMessage notification = 
                    JSON.parseObject(messageBody, FormRuleNotificationService.NotificationMessage.class);
            
            String type = notification.getType();
            String formCode = notification.getFormCode();
            
            if ("RULE_UPDATE".equals(type)) {
                webSocketHandler.broadcastRuleUpdate(formCode, notification.getData());
            } else if ("STATUS_CHANGE".equals(type)) {
                Object data = notification.getData();
                if (data instanceof Map) {
                    Integer status = (Integer) ((Map<?, ?>) data).get("status");
                    webSocketHandler.broadcastStatusChange(formCode, status);
                }
            }
        } catch (Exception e) {
            log.error("处理规则更新消息失败: messageBody={}", messageBody, e);
        }
    }
}
