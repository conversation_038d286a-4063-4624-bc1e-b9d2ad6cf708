package org.jeecg.modules.formrule.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.jeecg.modules.formrule.dto.FormRuleStatisticsVO;
import org.jeecg.modules.formrule.entity.FormRuleConfig;
import org.jeecg.modules.formrule.entity.FormRuleCacheVersion;

import java.util.List;

/**
 * 表单规则配置Mapper接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface FormRuleConfigMapper extends BaseMapper<FormRuleConfig> {

    /**
     * 递增表单规则版本号
     * 
     * @param formCode 表单代码
     * @return 新版本号
     */
    @Select("CALL sp_increment_form_rule_version(#{formCode}, @new_version); SELECT @new_version as version")
    Integer incrementVersion(@Param("formCode") String formCode);

    /**
     * 根据表单代码获取版本信息
     * 
     * @param formCode 表单代码
     * @return 版本信息
     */
    @Select("SELECT * FROM form_rule_cache_version WHERE form_code = #{formCode}")
    FormRuleCacheVersion getVersionByFormCode(@Param("formCode") String formCode);

    /**
     * 批量获取版本信息
     * 
     * @param formCodes 表单代码列表
     * @return 版本信息列表
     */
    @Select("<script>" +
            "SELECT * FROM form_rule_cache_version WHERE form_code IN " +
            "<foreach collection='formCodes' item='formCode' open='(' separator=',' close=')'>" +
            "#{formCode}" +
            "</foreach>" +
            "</script>")
    List<FormRuleCacheVersion> getVersionsByFormCodes(@Param("formCodes") List<String> formCodes);

    /**
     * 获取表单规则统计信息
     * 
     * @return 统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as totalForms, " +
            "SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as enabledForms, " +
            "SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as disabledForms, " +
            "SUM(CASE WHEN DATE(update_time) = CURDATE() THEN 1 ELSE 0 END) as todayUpdated, " +
            "SUM(CASE WHEN update_time >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as weekUpdated, " +
            "AVG(JSON_LENGTH(field_rules)) as avgFieldCount, " +
            "AVG(JSON_LENGTH(dependency_rules)) as avgDependencyCount " +
            "FROM form_rule_config WHERE del_flag = 0")
    FormRuleStatisticsVO getStatistics();

    /**
     * 根据表单代码获取最新的规则配置
     * 
     * @param formCode 表单代码
     * @return 规则配置
     */
    @Select("SELECT * FROM form_rule_config " +
            "WHERE form_code = #{formCode} AND del_flag = 0 AND status = 1 " +
            "ORDER BY version DESC LIMIT 1")
    FormRuleConfig getLatestByFormCode(@Param("formCode") String formCode);

    /**
     * 获取所有启用的表单规则配置
     * 
     * @return 规则配置列表
     */
    @Select("SELECT * FROM form_rule_config " +
            "WHERE del_flag = 0 AND status = 1 " +
            "ORDER BY update_time DESC")
    List<FormRuleConfig> getAllEnabled();

    /**
     * 根据表单代码列表获取规则配置
     * 
     * @param formCodes 表单代码列表
     * @return 规则配置列表
     */
    @Select("<script>" +
            "SELECT * FROM form_rule_config " +
            "WHERE del_flag = 0 AND status = 1 AND form_code IN " +
            "<foreach collection='formCodes' item='formCode' open='(' separator=',' close=')'>" +
            "#{formCode}" +
            "</foreach>" +
            "ORDER BY update_time DESC" +
            "</script>")
    List<FormRuleConfig> getByFormCodes(@Param("formCodes") List<String> formCodes);

    /**
     * 更新表单规则状态
     * 
     * @param formCode 表单代码
     * @param status 状态
     * @param updateBy 更新人
     * @return 影响行数
     */
    @Update("UPDATE form_rule_config SET status = #{status}, update_by = #{updateBy}, " +
            "update_time = NOW() WHERE form_code = #{formCode} AND del_flag = 0")
    int updateStatusByFormCode(@Param("formCode") String formCode, 
                              @Param("status") Integer status, 
                              @Param("updateBy") String updateBy);

    /**
     * 逻辑删除表单规则配置
     * 
     * @param formCode 表单代码
     * @param updateBy 更新人
     * @return 影响行数
     */
    @Update("UPDATE form_rule_config SET del_flag = 1, update_by = #{updateBy}, " +
            "update_time = NOW() WHERE form_code = #{formCode}")
    int deleteByFormCode(@Param("formCode") String formCode, @Param("updateBy") String updateBy);

    /**
     * 检查表单代码是否存在
     * 
     * @param formCode 表单代码
     * @return 是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM form_rule_config " +
            "WHERE form_code = #{formCode} AND del_flag = 0")
    boolean existsByFormCode(@Param("formCode") String formCode);

    /**
     * 获取表单规则配置的字段数量
     * 
     * @param formCode 表单代码
     * @return 字段数量
     */
    @Select("SELECT JSON_LENGTH(field_rules) FROM form_rule_config " +
            "WHERE form_code = #{formCode} AND del_flag = 0 AND status = 1 " +
            "ORDER BY version DESC LIMIT 1")
    Integer getFieldCountByFormCode(@Param("formCode") String formCode);

    /**
     * 获取表单规则配置的联动规则数量
     * 
     * @param formCode 表单代码
     * @return 联动规则数量
     */
    @Select("SELECT JSON_LENGTH(dependency_rules) FROM form_rule_config " +
            "WHERE form_code = #{formCode} AND del_flag = 0 AND status = 1 " +
            "ORDER BY version DESC LIMIT 1")
    Integer getDependencyCountByFormCode(@Param("formCode") String formCode);

    /**
     * 搜索表单规则配置
     * 
     * @param keyword 关键词
     * @return 规则配置列表
     */
    @Select("SELECT * FROM form_rule_config " +
            "WHERE del_flag = 0 AND (form_code LIKE CONCAT('%', #{keyword}, '%') " +
            "OR form_name LIKE CONCAT('%', #{keyword}, '%') " +
            "OR description LIKE CONCAT('%', #{keyword}, '%')) " +
            "ORDER BY update_time DESC")
    List<FormRuleConfig> searchByKeyword(@Param("keyword") String keyword);

    /**
     * 获取最近更新的表单规则配置
     * 
     * @param limit 限制数量
     * @return 规则配置列表
     */
    @Select("SELECT * FROM form_rule_config " +
            "WHERE del_flag = 0 " +
            "ORDER BY update_time DESC " +
            "LIMIT #{limit}")
    List<FormRuleConfig> getRecentUpdated(@Param("limit") Integer limit);

    /**
     * 获取指定时间范围内更新的表单规则配置
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 规则配置列表
     */
    @Select("SELECT * FROM form_rule_config " +
            "WHERE del_flag = 0 AND update_time BETWEEN #{startTime} AND #{endTime} " +
            "ORDER BY update_time DESC")
    List<FormRuleConfig> getUpdatedInTimeRange(@Param("startTime") String startTime, 
                                              @Param("endTime") String endTime);
}

/**
 * 表单规则变更历史Mapper接口
 */
@Mapper
interface FormRuleChangeLogMapper extends BaseMapper<FormRuleChangeLog> {

    /**
     * 根据表单代码获取变更历史
     * 
     * @param formCode 表单代码
     * @param limit 限制数量
     * @return 变更历史列表
     */
    @Select("SELECT * FROM form_rule_change_log " +
            "WHERE form_code = #{formCode} " +
            "ORDER BY create_time DESC " +
            "LIMIT #{limit}")
    List<FormRuleChangeLog> getChangeLogByFormCode(@Param("formCode") String formCode, 
                                                  @Param("limit") Integer limit);

    /**
     * 获取最近的变更历史
     * 
     * @param limit 限制数量
     * @return 变更历史列表
     */
    @Select("SELECT * FROM form_rule_change_log " +
            "ORDER BY create_time DESC " +
            "LIMIT #{limit}")
    List<FormRuleChangeLog> getRecentChangeLogs(@Param("limit") Integer limit);

    /**
     * 根据操作人获取变更历史
     * 
     * @param operator 操作人
     * @param limit 限制数量
     * @return 变更历史列表
     */
    @Select("SELECT * FROM form_rule_change_log " +
            "WHERE operator = #{operator} " +
            "ORDER BY create_time DESC " +
            "LIMIT #{limit}")
    List<FormRuleChangeLog> getChangeLogByOperator(@Param("operator") String operator, 
                                                  @Param("limit") Integer limit);

    /**
     * 统计变更次数
     * 
     * @param formCode 表单代码
     * @return 变更次数
     */
    @Select("SELECT COUNT(*) FROM form_rule_change_log WHERE form_code = #{formCode}")
    Integer countChangesByFormCode(@Param("formCode") String formCode);

    /**
     * 清理过期的变更历史
     * 
     * @param beforeDate 截止日期
     * @return 清理数量
     */
    @Update("DELETE FROM form_rule_change_log WHERE create_time < #{beforeDate}")
    int cleanupExpiredLogs(@Param("beforeDate") String beforeDate);
}

/**
 * 表单规则缓存版本Mapper接口
 */
@Mapper
interface FormRuleCacheVersionMapper extends BaseMapper<FormRuleCacheVersion> {

    /**
     * 更新缓存版本信息
     * 
     * @param formCode 表单代码
     * @param version 版本号
     * @param lastUpdated 最后更新时间戳
     * @return 影响行数
     */
    @Update("INSERT INTO form_rule_cache_version (id, form_code, version, cache_key, last_updated) " +
            "VALUES (CONCAT('cache_', #{formCode}, '_', LPAD(#{version}, 3, '0')), " +
            "#{formCode}, #{version}, CONCAT('form_rule:', #{formCode}), #{lastUpdated}) " +
            "ON DUPLICATE KEY UPDATE " +
            "version = #{version}, last_updated = #{lastUpdated}, update_time = NOW()")
    int updateCacheVersion(@Param("formCode") String formCode, 
                          @Param("version") Integer version, 
                          @Param("lastUpdated") Long lastUpdated);

    /**
     * 删除缓存版本信息
     * 
     * @param formCode 表单代码
     * @return 影响行数
     */
    @Update("DELETE FROM form_rule_cache_version WHERE form_code = #{formCode}")
    int deleteCacheVersion(@Param("formCode") String formCode);

    /**
     * 获取所有缓存版本信息
     * 
     * @return 缓存版本信息列表
     */
    @Select("SELECT * FROM form_rule_cache_version ORDER BY update_time DESC")
    List<FormRuleCacheVersion> getAllCacheVersions();

    /**
     * 清理过期的缓存版本信息
     * 
     * @param beforeTimestamp 截止时间戳
     * @return 清理数量
     */
    @Update("DELETE FROM form_rule_cache_version WHERE last_updated < #{beforeTimestamp}")
    int cleanupExpiredCacheVersions(@Param("beforeTimestamp") Long beforeTimestamp);
}
