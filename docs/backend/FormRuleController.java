package org.jeecg.modules.formrule.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.formrule.dto.*;
import org.jeecg.modules.formrule.entity.FormRuleConfig;
import org.jeecg.modules.formrule.service.IFormRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * 表单规则配置控制器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Api(tags = "表单规则管理")
@RestController
@RequestMapping("/api/form-rules")
@Slf4j
@Validated
public class FormRuleController extends JeecgController<FormRuleConfig, IFormRuleService> {

    @Autowired
    private IFormRuleService formRuleService;

    /**
     * 根据表单代码获取规则配置
     */
    @ApiOperation(value = "获取表单规则配置", notes = "根据表单代码获取规则配置")
    @GetMapping("/{formCode}")
    @AutoLog(value = "获取表单规则配置")
    public Result<FormRuleConfigDTO> getFormRules(
            @ApiParam(value = "表单代码", required = true) 
            @PathVariable @NotBlank(message = "表单代码不能为空") String formCode) {
        
        try {
            FormRuleConfigDTO config = formRuleService.getByFormCode(formCode);
            if (config == null) {
                return Result.error("表单规则配置不存在");
            }
            return Result.OK(config);
        } catch (Exception e) {
            log.error("获取表单规则配置失败: formCode={}", formCode, e);
            return Result.error("获取表单规则配置失败: " + e.getMessage());
        }
    }

    /**
     * 保存表单规则配置
     */
    @ApiOperation(value = "保存表单规则配置", notes = "保存或更新表单规则配置")
    @PostMapping("/{formCode}")
    @AutoLog(value = "保存表单规则配置")
    public Result<Void> saveFormRules(
            @ApiParam(value = "表单代码", required = true) 
            @PathVariable @NotBlank(message = "表单代码不能为空") String formCode,
            @ApiParam(value = "表单规则配置", required = true) 
            @RequestBody @Valid FormRuleConfigDTO config,
            HttpServletRequest request) {
        
        try {
            // 确保表单代码一致
            config.setFormCode(formCode);
            
            boolean result = formRuleService.saveOrUpdateFormRule(config);
            if (result) {
                return Result.OK("保存成功");
            } else {
                return Result.error("保存失败");
            }
        } catch (Exception e) {
            log.error("保存表单规则配置失败: formCode={}", formCode, e);
            return Result.error("保存失败: " + e.getMessage());
        }
    }

    /**
     * 获取表单规则版本信息
     */
    @ApiOperation(value = "获取表单规则版本", notes = "获取表单规则的版本信息")
    @GetMapping("/{formCode}/version")
    @AutoLog(value = "获取表单规则版本")
    public Result<FormRuleVersionDTO> getFormRuleVersion(
            @ApiParam(value = "表单代码", required = true) 
            @PathVariable @NotBlank(message = "表单代码不能为空") String formCode) {
        
        try {
            FormRuleVersionDTO version = formRuleService.getVersionInfo(formCode);
            if (version == null) {
                return Result.error("表单规则版本信息不存在");
            }
            return Result.OK(version);
        } catch (Exception e) {
            log.error("获取表单规则版本失败: formCode={}", formCode, e);
            return Result.error("获取版本信息失败: " + e.getMessage());
        }
    }

    /**
     * 批量获取表单规则版本信息
     */
    @ApiOperation(value = "批量获取表单规则版本", notes = "批量获取多个表单的规则版本信息")
    @PostMapping("/versions")
    @AutoLog(value = "批量获取表单规则版本")
    public Result<Map<String, Integer>> getFormRuleVersions(
            @ApiParam(value = "表单代码列表", required = true) 
            @RequestBody @Valid List<String> formCodes) {
        
        try {
            Map<String, Integer> versions = formRuleService.getVersionInfoBatch(formCodes);
            return Result.OK(versions);
        } catch (Exception e) {
            log.error("批量获取表单规则版本失败: formCodes={}", formCodes, e);
            return Result.error("获取版本信息失败: " + e.getMessage());
        }
    }

    /**
     * 推送规则更新通知
     */
    @ApiOperation(value = "推送规则更新通知", notes = "推送表单规则更新通知给前端")
    @PostMapping("/{formCode}/notify")
    @AutoLog(value = "推送规则更新通知")
    public Result<Void> notifyRuleUpdate(
            @ApiParam(value = "表单代码", required = true) 
            @PathVariable @NotBlank(message = "表单代码不能为空") String formCode) {
        
        try {
            formRuleService.notifyRuleUpdate(formCode);
            return Result.OK("通知发送成功");
        } catch (Exception e) {
            log.error("推送规则更新通知失败: formCode={}", formCode, e);
            return Result.error("通知发送失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询表单规则配置
     */
    @ApiOperation(value = "分页查询表单规则", notes = "分页查询表单规则配置列表")
    @GetMapping("/list")
    @AutoLog(value = "分页查询表单规则")
    public Result<IPage<FormRuleConfigDTO>> queryPageList(
            FormRuleQueryDTO queryDTO,
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        
        try {
            Page<FormRuleConfig> page = new Page<>(pageNo, pageSize);
            IPage<FormRuleConfigDTO> pageList = formRuleService.queryPage(page, queryDTO);
            return Result.OK(pageList);
        } catch (Exception e) {
            log.error("分页查询表单规则失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 创建表单规则配置
     */
    @ApiOperation(value = "创建表单规则", notes = "创建新的表单规则配置")
    @PostMapping("/create")
    @AutoLog(value = "创建表单规则")
    public Result<Void> createFormRule(
            @ApiParam(value = "表单规则配置", required = true) 
            @RequestBody @Valid FormRuleConfigDTO config) {
        
        try {
            boolean result = formRuleService.saveOrUpdateFormRule(config);
            if (result) {
                return Result.OK("创建成功");
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建表单规则失败: formCode={}", config.getFormCode(), e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 更新表单规则配置
     */
    @ApiOperation(value = "更新表单规则", notes = "更新表单规则配置")
    @PutMapping("/{id}")
    @AutoLog(value = "更新表单规则")
    public Result<Void> updateFormRule(
            @ApiParam(value = "规则配置ID", required = true) 
            @PathVariable @NotBlank(message = "ID不能为空") String id,
            @ApiParam(value = "表单规则配置", required = true) 
            @RequestBody @Valid FormRuleConfigDTO config) {
        
        try {
            config.setId(id);
            boolean result = formRuleService.saveOrUpdateFormRule(config);
            if (result) {
                return Result.OK("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新表单规则失败: id={}", id, e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除表单规则配置
     */
    @ApiOperation(value = "删除表单规则", notes = "删除表单规则配置")
    @DeleteMapping("/{formCode}")
    @AutoLog(value = "删除表单规则")
    public Result<Void> deleteFormRule(
            @ApiParam(value = "表单代码", required = true) 
            @PathVariable @NotBlank(message = "表单代码不能为空") String formCode) {
        
        try {
            boolean result = formRuleService.deleteByFormCode(formCode);
            if (result) {
                return Result.OK("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除表单规则失败: formCode={}", formCode, e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 启用/禁用表单规则
     */
    @ApiOperation(value = "更新表单规则状态", notes = "启用或禁用表单规则")
    @PutMapping("/{formCode}/status")
    @AutoLog(value = "更新表单规则状态")
    public Result<Void> updateStatus(
            @ApiParam(value = "表单代码", required = true) 
            @PathVariable @NotBlank(message = "表单代码不能为空") String formCode,
            @ApiParam(value = "状态", required = true) 
            @RequestParam Integer status) {
        
        try {
            boolean result = formRuleService.updateStatus(formCode, status);
            if (result) {
                return Result.OK("状态更新成功");
            } else {
                return Result.error("状态更新失败");
            }
        } catch (Exception e) {
            log.error("更新表单规则状态失败: formCode={}, status={}", formCode, status, e);
            return Result.error("状态更新失败: " + e.getMessage());
        }
    }

    /**
     * 复制表单规则配置
     */
    @ApiOperation(value = "复制表单规则", notes = "复制表单规则配置到新的表单")
    @PostMapping("/{sourceFormCode}/copy")
    @AutoLog(value = "复制表单规则")
    public Result<Void> copyFormRule(
            @ApiParam(value = "源表单代码", required = true) 
            @PathVariable @NotBlank(message = "源表单代码不能为空") String sourceFormCode,
            @ApiParam(value = "目标表单代码", required = true) 
            @RequestParam @NotBlank(message = "目标表单代码不能为空") String targetFormCode,
            @ApiParam(value = "目标表单名称", required = true) 
            @RequestParam @NotBlank(message = "目标表单名称不能为空") String targetFormName) {
        
        try {
            boolean result = formRuleService.copyFormRule(sourceFormCode, targetFormCode, targetFormName);
            if (result) {
                return Result.OK("复制成功");
            } else {
                return Result.error("复制失败");
            }
        } catch (Exception e) {
            log.error("复制表单规则失败: sourceFormCode={}, targetFormCode={}", 
                    sourceFormCode, targetFormCode, e);
            return Result.error("复制失败: " + e.getMessage());
        }
    }

    /**
     * 获取表单规则统计信息
     */
    @ApiOperation(value = "获取统计信息", notes = "获取表单规则的统计信息")
    @GetMapping("/statistics")
    @AutoLog(value = "获取表单规则统计信息")
    public Result<FormRuleStatisticsVO> getStatistics() {
        try {
            FormRuleStatisticsVO statistics = formRuleService.getStatistics();
            return Result.OK(statistics);
        } catch (Exception e) {
            log.error("获取表单规则统计信息失败", e);
            return Result.error("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 清除表单规则缓存
     */
    @ApiOperation(value = "清除缓存", notes = "清除指定表单的规则缓存")
    @DeleteMapping("/{formCode}/cache")
    @AutoLog(value = "清除表单规则缓存")
    public Result<Void> clearCache(
            @ApiParam(value = "表单代码", required = true) 
            @PathVariable @NotBlank(message = "表单代码不能为空") String formCode) {
        
        try {
            formRuleService.clearCache(formCode);
            return Result.OK("缓存清除成功");
        } catch (Exception e) {
            log.error("清除表单规则缓存失败: formCode={}", formCode, e);
            return Result.error("缓存清除失败: " + e.getMessage());
        }
    }

    /**
     * 预热表单规则缓存
     */
    @ApiOperation(value = "预热缓存", notes = "预热指定表单的规则缓存")
    @PostMapping("/cache/warmup")
    @AutoLog(value = "预热表单规则缓存")
    public Result<Void> warmupCache(
            @ApiParam(value = "表单代码列表", required = true) 
            @RequestBody @Valid List<String> formCodes) {
        
        try {
            formRuleService.warmupCache(formCodes);
            return Result.OK("缓存预热成功");
        } catch (Exception e) {
            log.error("预热表单规则缓存失败: formCodes={}", formCodes, e);
            return Result.error("缓存预热失败: " + e.getMessage());
        }
    }
}
