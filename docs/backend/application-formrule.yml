# 表单规则管理模块配置
# 在主配置文件中引入: spring.profiles.include: formrule

# 表单规则模块配置
form-rule:
  # 缓存配置
  cache:
    # 缓存过期时间（小时）
    expire-hours: 24
    # 版本缓存过期时间（小时）
    version-expire-hours: 1
    # 分布式锁过期时间（分钟）
    lock-expire-minutes: 5
    # 是否启用缓存预热
    enable-warmup: true
    # 预热的表单代码列表
    warmup-form-codes:
      - customer_reg_form
      - employee_reg_form
      - order_form
  
  # WebSocket配置
  websocket:
    # 是否启用WebSocket
    enabled: true
    # WebSocket路径
    path: /ws/form-rule
    # 允许的源（生产环境应配置具体域名）
    allowed-origins: "*"
    # 心跳间隔（秒）
    heartbeat-interval: 30
    # 连接超时时间（秒）
    connection-timeout: 60
  
  # Redis配置
  redis:
    # 通知频道
    notification-channel: form_rule_update
    # 缓存键前缀
    cache-key-prefix: "form_rule:"
    # 版本键前缀
    version-key-prefix: "form_rule_version:"
    # 锁键前缀
    lock-key-prefix: "form_rule_lock:"
  
  # 数据库配置
  database:
    # 是否启用变更日志
    enable-change-log: true
    # 变更日志保留天数
    change-log-retention-days: 30
    # 是否启用缓存版本表
    enable-cache-version: true
    # 缓存版本保留天数
    cache-version-retention-days: 7
  
  # 定时任务配置
  scheduled:
    # 是否启用定时任务
    enabled: true
    # 缓存清理cron表达式（每小时执行）
    cache-cleanup-cron: "0 0 * * * ?"
    # 日志清理cron表达式（每天凌晨2点执行）
    log-cleanup-cron: "0 2 0 * * ?"
    # 统计生成cron表达式（每天凌晨0:30执行）
    statistics-cron: "0 30 0 * * ?"
  
  # 监控配置
  monitoring:
    # 是否启用健康检查
    health-check-enabled: true
    # 是否启用指标收集
    metrics-enabled: true
    # 指标前缀
    metrics-prefix: "form_rule"
  
  # 安全配置
  security:
    # 是否启用权限检查
    permission-check-enabled: true
    # 管理员角色
    admin-roles:
      - admin
      - form_rule_admin
    # 操作权限
    permissions:
      view: "form:rule:view"
      create: "form:rule:create"
      update: "form:rule:update"
      delete: "form:rule:delete"
      config: "form:rule:config"

# Spring Boot Actuator配置（用于健康检查和监控）
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  health:
    redis:
      enabled: true
    db:
      enabled: true

# 日志配置
logging:
  level:
    org.jeecg.modules.formrule: DEBUG
    org.springframework.data.redis: INFO
    org.springframework.web.socket: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{50}] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{50}] - %msg%n"

# MyBatis Plus配置
mybatis-plus:
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 开启二级缓存
    cache-enabled: true
    # 日志实现
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      # 逻辑删除字段
      logic-delete-field: delFlag
      # 逻辑删除值
      logic-delete-value: 1
      # 逻辑未删除值
      logic-not-delete-value: 0
      # 主键类型
      id-type: ASSIGN_ID

# Redis配置（如果使用独立的Redis实例）
spring:
  redis:
    # Redis服务器地址
    host: localhost
    # Redis服务器端口
    port: 6379
    # Redis数据库索引（默认为0）
    database: 0
    # 连接超时时间
    timeout: 5000ms
    # 连接池配置
    lettuce:
      pool:
        # 连接池最大连接数
        max-active: 20
        # 连接池最大阻塞等待时间
        max-wait: -1ms
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 5

# 数据源配置（如果使用独立的数据源）
# spring:
#   datasource:
#     form-rule:
#       driver-class-name: com.mysql.cj.jdbc.Driver
#       url: *************************************************************************************************************
#       username: form_rule_user
#       password: form_rule_password
#       type: com.alibaba.druid.pool.DruidDataSource
#       druid:
#         initial-size: 5
#         min-idle: 5
#         max-active: 20
#         max-wait: 60000
#         time-between-eviction-runs-millis: 60000
#         min-evictable-idle-time-millis: 300000
#         validation-query: SELECT 1
#         test-while-idle: true
#         test-on-borrow: false
#         test-on-return: false

# 线程池配置
spring:
  task:
    execution:
      pool:
        # 核心线程数
        core-size: 5
        # 最大线程数
        max-size: 20
        # 队列容量
        queue-capacity: 100
        # 线程名前缀
        thread-name-prefix: "form-rule-"
        # 线程空闲时间
        keep-alive: 60s
    scheduling:
      pool:
        # 定时任务线程池大小
        size: 5
        # 线程名前缀
        thread-name-prefix: "form-rule-scheduled-"

# 缓存配置
spring:
  cache:
    type: redis
    redis:
      # 缓存过期时间
      time-to-live: 24h
      # 是否缓存空值
      cache-null-values: false
      # 键前缀
      key-prefix: "form_rule_cache:"
      # 是否使用键前缀
      use-key-prefix: true

# 序列化配置
spring:
  jackson:
    # 日期格式
    date-format: yyyy-MM-dd HH:mm:ss
    # 时区
    time-zone: GMT+8
    # 序列化配置
    serialization:
      # 格式化输出
      indent-output: false
      # 忽略空值
      write-null-map-values: false
    # 反序列化配置
    deserialization:
      # 忽略未知属性
      fail-on-unknown-properties: false

# 国际化配置
spring:
  messages:
    # 消息文件基础名
    basename: i18n/form-rule-messages
    # 编码
    encoding: UTF-8
    # 缓存时间
    cache-duration: 3600s

# 文档配置（Swagger）
springdoc:
  api-docs:
    groups:
      enabled: true
  group-configs:
    - group: 'form-rule'
      display-name: '表单规则管理'
      paths-to-match: '/api/form-rules/**'
