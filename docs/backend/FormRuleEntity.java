package org.jeecg.modules.formrule.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 表单规则配置实体类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@TableName("form_rule_config")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "form_rule_config对象", description = "表单规则配置")
public class FormRuleConfig implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**表单代码*/
    @Excel(name = "表单代码", width = 15)
    @ApiModelProperty(value = "表单代码")
    private String formCode;

    /**表单名称*/
    @Excel(name = "表单名称", width = 15)
    @ApiModelProperty(value = "表单名称")
    private String formName;

    /**表单描述*/
    @Excel(name = "表单描述", width = 15)
    @ApiModelProperty(value = "表单描述")
    private String description;

    /**版本号*/
    @Excel(name = "版本号", width = 15)
    @ApiModelProperty(value = "版本号")
    private Integer version;

    /**状态：1启用，0禁用*/
    @Excel(name = "状态", width = 15, dicCode = "valid_status")
    @Dict(dicCode = "valid_status")
    @ApiModelProperty(value = "状态：1启用，0禁用")
    private Integer status;

    /**字段规则JSON配置*/
    @ApiModelProperty(value = "字段规则JSON配置")
    private String fieldRules;

    /**联动规则JSON配置*/
    @ApiModelProperty(value = "联动规则JSON配置")
    private String dependencyRules;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**创建日期*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;

    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**更新日期*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;

    /**删除标志*/
    @Excel(name = "删除标志", width = 15)
    @LogicDelete
    @ApiModelProperty(value = "删除标志")
    private Integer delFlag;
}

/**
 * 表单规则变更历史实体类
 */
@Data
@TableName("form_rule_change_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "form_rule_change_log对象", description = "表单规则变更历史")
class FormRuleChangeLog implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**表单代码*/
    @ApiModelProperty(value = "表单代码")
    private String formCode;

    /**变更类型*/
    @ApiModelProperty(value = "变更类型")
    private String changeType;

    /**变更前版本*/
    @ApiModelProperty(value = "变更前版本")
    private Integer versionBefore;

    /**变更后版本*/
    @ApiModelProperty(value = "变更后版本")
    private Integer versionAfter;

    /**变更内容详情*/
    @ApiModelProperty(value = "变更内容详情")
    private String changeContent;

    /**变更摘要*/
    @ApiModelProperty(value = "变更摘要")
    private String changeSummary;

    /**操作人*/
    @ApiModelProperty(value = "操作人")
    private String operator;

    /**操作IP*/
    @ApiModelProperty(value = "操作IP")
    private String operatorIp;

    /**操作时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "操作时间")
    private Date createTime;
}

/**
 * 表单规则缓存版本实体类
 */
@Data
@TableName("form_rule_cache_version")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "form_rule_cache_version对象", description = "表单规则缓存版本")
class FormRuleCacheVersion implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**表单代码*/
    @ApiModelProperty(value = "表单代码")
    private String formCode;

    /**当前版本号*/
    @ApiModelProperty(value = "当前版本号")
    private Integer version;

    /**Redis缓存键*/
    @ApiModelProperty(value = "Redis缓存键")
    private String cacheKey;

    /**最后更新时间戳*/
    @ApiModelProperty(value = "最后更新时间戳")
    private Long lastUpdated;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
