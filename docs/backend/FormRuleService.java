package org.jeecg.modules.formrule.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.formrule.dto.*;
import org.jeecg.modules.formrule.entity.FormRuleConfig;

import java.util.List;
import java.util.Map;

/**
 * 表单规则配置服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface IFormRuleService extends IService<FormRuleConfig> {

    /**
     * 根据表单代码获取规则配置
     * 
     * @param formCode 表单代码
     * @return 表单规则配置
     */
    FormRuleConfigDTO getByFormCode(String formCode);

    /**
     * 保存或更新表单规则配置
     * 
     * @param dto 表单规则配置DTO
     * @return 是否成功
     */
    boolean saveOrUpdateFormRule(FormRuleConfigDTO dto);

    /**
     * 获取表单规则版本信息
     * 
     * @param formCode 表单代码
     * @return 版本信息
     */
    FormRuleVersionDTO getVersionInfo(String formCode);

    /**
     * 批量获取表单规则版本信息
     * 
     * @param formCodes 表单代码列表
     * @return 版本信息映射
     */
    Map<String, Integer> getVersionInfoBatch(List<String> formCodes);

    /**
     * 分页查询表单规则配置
     * 
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    IPage<FormRuleConfigDTO> queryPage(IPage<FormRuleConfig> page, FormRuleQueryDTO queryDTO);

    /**
     * 启用/禁用表单规则
     * 
     * @param formCode 表单代码
     * @param status 状态
     * @return 是否成功
     */
    boolean updateStatus(String formCode, Integer status);

    /**
     * 删除表单规则配置
     * 
     * @param formCode 表单代码
     * @return 是否成功
     */
    boolean deleteByFormCode(String formCode);

    /**
     * 复制表单规则配置
     * 
     * @param sourceFormCode 源表单代码
     * @param targetFormCode 目标表单代码
     * @param targetFormName 目标表单名称
     * @return 是否成功
     */
    boolean copyFormRule(String sourceFormCode, String targetFormCode, String targetFormName);

    /**
     * 获取表单规则统计信息
     * 
     * @return 统计信息
     */
    FormRuleStatisticsVO getStatistics();

    /**
     * 推送规则更新通知
     * 
     * @param formCode 表单代码
     */
    void notifyRuleUpdate(String formCode);

    /**
     * 清除表单规则缓存
     * 
     * @param formCode 表单代码
     */
    void clearCache(String formCode);

    /**
     * 预热表单规则缓存
     * 
     * @param formCodes 表单代码列表
     */
    void warmupCache(List<String> formCodes);
}

/**
 * 表单规则配置服务实现类
 */
@Service
@Slf4j
public class FormRuleServiceImpl extends ServiceImpl<FormRuleConfigMapper, FormRuleConfig> 
        implements IFormRuleService {

    @Autowired
    private FormRuleConfigMapper formRuleConfigMapper;
    
    @Autowired
    private FormRuleCacheService formRuleCacheService;
    
    @Autowired
    private FormRuleNotificationService notificationService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String CACHE_KEY_PREFIX = "form_rule:";
    private static final String VERSION_KEY_PREFIX = "form_rule_version:";

    @Override
    public FormRuleConfigDTO getByFormCode(String formCode) {
        // 1. 先从缓存获取
        FormRuleConfigDTO cached = formRuleCacheService.getFromCache(formCode);
        if (cached != null) {
            return cached;
        }

        // 2. 从数据库获取
        LambdaQueryWrapper<FormRuleConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FormRuleConfig::getFormCode, formCode)
                   .eq(FormRuleConfig::getStatus, 1)
                   .orderByDesc(FormRuleConfig::getVersion)
                   .last("LIMIT 1");
        
        FormRuleConfig entity = this.getOne(queryWrapper);
        if (entity == null) {
            return null;
        }

        // 3. 转换为DTO
        FormRuleConfigDTO dto = convertToDTO(entity);
        
        // 4. 放入缓存
        formRuleCacheService.putToCache(formCode, dto);
        
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateFormRule(FormRuleConfigDTO dto) {
        try {
            FormRuleConfig entity;
            boolean isUpdate = false;
            
            if (StringUtils.isNotBlank(dto.getId())) {
                // 更新操作
                entity = this.getById(dto.getId());
                if (entity == null) {
                    throw new JeecgBootException("表单规则配置不存在");
                }
                isUpdate = true;
            } else {
                // 新增操作
                entity = new FormRuleConfig();
                entity.setCreateTime(new Date());
                entity.setCreateBy(SecurityUtils.getUsername());
            }

            // 设置基本信息
            entity.setFormCode(dto.getFormCode())
                  .setFormName(dto.getFormName())
                  .setDescription(dto.getDescription())
                  .setStatus(dto.getStatus() != null ? dto.getStatus() : 1)
                  .setUpdateTime(new Date())
                  .setUpdateBy(SecurityUtils.getUsername());

            // 转换规则为JSON
            entity.setFieldRules(JSON.toJSONString(dto.getFieldRules()));
            entity.setDependencyRules(JSON.toJSONString(dto.getDependencyRules()));

            // 处理版本号
            if (isUpdate) {
                // 更新时递增版本号
                Integer newVersion = formRuleConfigMapper.incrementVersion(dto.getFormCode());
                entity.setVersion(newVersion);
                dto.setVersion(newVersion);
            } else {
                entity.setVersion(1);
                dto.setVersion(1);
            }

            // 保存到数据库
            boolean result = this.saveOrUpdate(entity);
            
            if (result) {
                // 更新缓存
                dto.setId(entity.getId());
                dto.setLastUpdated(System.currentTimeMillis());
                formRuleCacheService.putToCache(dto.getFormCode(), dto);
                
                // 推送更新通知
                notificationService.notifyRuleUpdate(dto.getFormCode(), dto);
                
                log.info("表单规则配置保存成功: formCode={}, version={}", 
                        dto.getFormCode(), dto.getVersion());
            }
            
            return result;
        } catch (Exception e) {
            log.error("保存表单规则配置失败: formCode={}", dto.getFormCode(), e);
            throw new JeecgBootException("保存表单规则配置失败: " + e.getMessage());
        }
    }

    @Override
    public FormRuleVersionDTO getVersionInfo(String formCode) {
        // 先从缓存获取版本信息
        String versionKey = VERSION_KEY_PREFIX + formCode;
        Object cached = redisTemplate.opsForValue().get(versionKey);
        
        if (cached != null) {
            return JSON.parseObject(cached.toString(), FormRuleVersionDTO.class);
        }

        // 从数据库获取
        FormRuleCacheVersion versionEntity = formRuleConfigMapper.getVersionByFormCode(formCode);
        if (versionEntity == null) {
            return null;
        }

        FormRuleVersionDTO dto = new FormRuleVersionDTO()
                .setFormCode(versionEntity.getFormCode())
                .setVersion(versionEntity.getVersion())
                .setLastUpdated(versionEntity.getLastUpdated())
                .setUpdateTime(versionEntity.getUpdateTime());

        // 缓存版本信息
        redisTemplate.opsForValue().set(versionKey, JSON.toJSONString(dto), 
                Duration.ofHours(1));

        return dto;
    }

    @Override
    public Map<String, Integer> getVersionInfoBatch(List<String> formCodes) {
        Map<String, Integer> result = new HashMap<>();
        
        if (CollectionUtils.isEmpty(formCodes)) {
            return result;
        }

        // 批量从数据库获取版本信息
        List<FormRuleCacheVersion> versions = formRuleConfigMapper.getVersionsByFormCodes(formCodes);
        
        for (FormRuleCacheVersion version : versions) {
            result.put(version.getFormCode(), version.getVersion());
        }

        return result;
    }

    @Override
    public IPage<FormRuleConfigDTO> queryPage(IPage<FormRuleConfig> page, FormRuleQueryDTO queryDTO) {
        LambdaQueryWrapper<FormRuleConfig> queryWrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.isNotBlank(queryDTO.getFormCode())) {
            queryWrapper.like(FormRuleConfig::getFormCode, queryDTO.getFormCode());
        }
        if (StringUtils.isNotBlank(queryDTO.getFormName())) {
            queryWrapper.like(FormRuleConfig::getFormName, queryDTO.getFormName());
        }
        if (queryDTO.getStatus() != null) {
            queryWrapper.eq(FormRuleConfig::getStatus, queryDTO.getStatus());
        }
        if (StringUtils.isNotBlank(queryDTO.getCreateBy())) {
            queryWrapper.like(FormRuleConfig::getCreateBy, queryDTO.getCreateBy());
        }
        if (queryDTO.getCreateTimeStart() != null) {
            queryWrapper.ge(FormRuleConfig::getCreateTime, queryDTO.getCreateTimeStart());
        }
        if (queryDTO.getCreateTimeEnd() != null) {
            queryWrapper.le(FormRuleConfig::getCreateTime, queryDTO.getCreateTimeEnd());
        }

        queryWrapper.orderByDesc(FormRuleConfig::getUpdateTime);

        IPage<FormRuleConfig> entityPage = this.page(page, queryWrapper);
        
        // 转换为DTO分页结果
        IPage<FormRuleConfigDTO> dtoPage = new Page<>();
        BeanUtils.copyProperties(entityPage, dtoPage);
        
        List<FormRuleConfigDTO> dtoList = entityPage.getRecords().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        dtoPage.setRecords(dtoList);

        return dtoPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(String formCode, Integer status) {
        LambdaUpdateWrapper<FormRuleConfig> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(FormRuleConfig::getFormCode, formCode)
                   .set(FormRuleConfig::getStatus, status)
                   .set(FormRuleConfig::getUpdateTime, new Date())
                   .set(FormRuleConfig::getUpdateBy, SecurityUtils.getUsername());

        boolean result = this.update(updateWrapper);
        
        if (result) {
            // 清除缓存
            formRuleCacheService.removeFromCache(formCode);
            
            // 推送状态变更通知
            notificationService.notifyStatusChange(formCode, status);
            
            log.info("表单规则状态更新成功: formCode={}, status={}", formCode, status);
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByFormCode(String formCode) {
        LambdaUpdateWrapper<FormRuleConfig> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(FormRuleConfig::getFormCode, formCode)
                   .set(FormRuleConfig::getDelFlag, 1)
                   .set(FormRuleConfig::getUpdateTime, new Date())
                   .set(FormRuleConfig::getUpdateBy, SecurityUtils.getUsername());

        boolean result = this.update(updateWrapper);
        
        if (result) {
            // 清除缓存
            formRuleCacheService.removeFromCache(formCode);
            
            log.info("表单规则删除成功: formCode={}", formCode);
        }
        
        return result;
    }

    @Override
    public boolean copyFormRule(String sourceFormCode, String targetFormCode, String targetFormName) {
        FormRuleConfigDTO sourceDto = this.getByFormCode(sourceFormCode);
        if (sourceDto == null) {
            throw new JeecgBootException("源表单规则不存在");
        }

        // 创建新的规则配置
        FormRuleConfigDTO targetDto = new FormRuleConfigDTO()
                .setFormCode(targetFormCode)
                .setFormName(targetFormName)
                .setDescription(sourceDto.getDescription() + "(复制)")
                .setFieldRules(sourceDto.getFieldRules())
                .setDependencyRules(sourceDto.getDependencyRules())
                .setStatus(1);

        return this.saveOrUpdateFormRule(targetDto);
    }

    @Override
    public FormRuleStatisticsVO getStatistics() {
        return formRuleConfigMapper.getStatistics();
    }

    @Override
    public void notifyRuleUpdate(String formCode) {
        notificationService.notifyRuleUpdate(formCode, this.getByFormCode(formCode));
    }

    @Override
    public void clearCache(String formCode) {
        formRuleCacheService.removeFromCache(formCode);
    }

    @Override
    public void warmupCache(List<String> formCodes) {
        for (String formCode : formCodes) {
            try {
                this.getByFormCode(formCode);
            } catch (Exception e) {
                log.warn("预热缓存失败: formCode={}", formCode, e);
            }
        }
    }

    /**
     * 实体转DTO
     */
    private FormRuleConfigDTO convertToDTO(FormRuleConfig entity) {
        FormRuleConfigDTO dto = new FormRuleConfigDTO();
        BeanUtils.copyProperties(entity, dto);
        
        // 解析JSON字段
        if (StringUtils.isNotBlank(entity.getFieldRules())) {
            List<FieldRuleDTO> fieldRules = JSON.parseArray(entity.getFieldRules(), FieldRuleDTO.class);
            dto.setFieldRules(fieldRules);
        }
        
        if (StringUtils.isNotBlank(entity.getDependencyRules())) {
            List<DependencyRuleDTO> dependencyRules = JSON.parseArray(entity.getDependencyRules(), DependencyRuleDTO.class);
            dto.setDependencyRules(dependencyRules);
        }
        
        dto.setLastUpdated(entity.getUpdateTime() != null ? 
                entity.getUpdateTime().getTime() : System.currentTimeMillis());
        
        return dto;
    }
}
