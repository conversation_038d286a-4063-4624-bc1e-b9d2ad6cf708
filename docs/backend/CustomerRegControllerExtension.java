// 在CustomerRegController中添加的方法

/**
 * 添加带检查部位的项目组合
 *
 * @param request
 * @return
 */
@AutoLog(value = "体检登记-添加带检查部位的项目组合")
@ApiOperation(value = "添加带检查部位的项目组合", notes = "添加带检查部位的项目组合")
@PostMapping(value = "/addItemGroupWithCheckParts")
public Result<String> addItemGroupWithCheckParts(@RequestBody AddItemGroupWithCheckPartsRequest request) {
    try {
        customerRegService.addItemGroupWithCheckParts(request);
        return Result.OK("添加成功");
    } catch (Exception e) {
        log.error("添加带检查部位的项目组合失败", e);
        return Result.error("添加失败：" + e.getMessage());
    }
}

// 请求DTO类
package org.jeecg.modules.reg.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 添加带检查部位的项目组合请求
 */
@Data
@ApiModel(value = "AddItemGroupWithCheckPartsRequest", description = "添加带检查部位的项目组合请求")
public class AddItemGroupWithCheckPartsRequest {
    
    @ApiModelProperty(value = "体检登记ID", required = true)
    private String customerRegId;
    
    @ApiModelProperty(value = "项目组合ID", required = true)
    private String itemGroupId;
    
    @ApiModelProperty(value = "检查部位ID列表", required = true)
    private List<String> checkPartIds;
}

// 在CustomerRegService接口中添加的方法
/**
 * 添加带检查部位的项目组合
 * @param request 请求参数
 */
void addItemGroupWithCheckParts(AddItemGroupWithCheckPartsRequest request);

// 在CustomerRegServiceImpl中添加的实现方法
@Override
@Transactional(rollbackFor = Exception.class)
public void addItemGroupWithCheckParts(AddItemGroupWithCheckPartsRequest request) {
    // 1. 验证参数
    if (request == null || StringUtils.isEmpty(request.getCustomerRegId()) 
        || StringUtils.isEmpty(request.getItemGroupId()) 
        || CollectionUtils.isEmpty(request.getCheckPartIds())) {
        throw new JeecgBootException("参数不能为空");
    }
    
    // 2. 获取项目组合信息
    ItemGroup itemGroup = itemGroupService.getById(request.getItemGroupId());
    if (itemGroup == null) {
        throw new JeecgBootException("项目组合不存在");
    }
    
    // 3. 获取体检登记信息
    CustomerReg customerReg = getById(request.getCustomerRegId());
    if (customerReg == null) {
        throw new JeecgBootException("体检登记不存在");
    }
    
    // 4. 验证项目是否需要部位选择
    if (!"1".equals(itemGroup.getHasCheckPart())) {
        throw new JeecgBootException("该项目不需要选择检查部位");
    }
    
    // 5. 按部位创建多条记录
    String parentGroupId = IdUtil.simpleUUID();
    List<CustomerRegItemGroup> groupList = new ArrayList<>();
    
    for (String checkPartId : request.getCheckPartIds()) {
        CheckPartDict checkPart = checkPartDictService.getById(checkPartId);
        if (checkPart == null) {
            log.warn("检查部位不存在: {}", checkPartId);
            continue;
        }
        
        // 检查是否已存在相同的项目-部位组合
        LambdaQueryWrapper<CustomerRegItemGroup> existQuery = new LambdaQueryWrapper<>();
        existQuery.eq(CustomerRegItemGroup::getCustomerRegId, request.getCustomerRegId())
                 .eq(CustomerRegItemGroup::getItemGroupId, request.getItemGroupId())
                 .eq(CustomerRegItemGroup::getCheckPartId, checkPartId)
                 .ne(CustomerRegItemGroup::getAddMinusFlag, -1); // 排除已减项的
        
        CustomerRegItemGroup existGroup = customerRegItemGroupService.getOne(existQuery);
        if (existGroup != null) {
            log.warn("项目-部位组合已存在: {} - {}", itemGroup.getName(), checkPart.getName());
            continue;
        }
        
        CustomerRegItemGroup regGroup = createRegItemGroup(customerReg, itemGroup);
        
        // 设置部位信息
        regGroup.setCheckPartId(checkPartId);
        regGroup.setCheckPartName(checkPart.getName());
        regGroup.setParentGroupId(parentGroupId);
        regGroup.setItemGroupName(itemGroup.getName() + "-" + checkPart.getName());
        
        groupList.add(regGroup);
    }
    
    if (groupList.isEmpty()) {
        throw new JeecgBootException("没有可添加的项目-部位组合");
    }
    
    // 6. 批量保存
    customerRegItemGroupService.saveBatch(groupList);
    
    // 7. 异步更新使用频次
    checkPartDictService.updateUsageFrequency(request.getItemGroupId(), request.getCheckPartIds());
    
    log.info("成功添加{}个部位的检查项目", groupList.size());
}

/**
 * 创建登记项目组合对象
 */
private CustomerRegItemGroup createRegItemGroup(CustomerReg customerReg, ItemGroup itemGroup) {
    CustomerRegItemGroup regGroup = new CustomerRegItemGroup();
    regGroup.setUuid(IdUtil.simpleUUID());
    regGroup.setCustomerRegId(customerReg.getId());
    regGroup.setExamNo(customerReg.getExamNo());
    regGroup.setItemGroupId(itemGroup.getId());
    regGroup.setItemGroupName(itemGroup.getName());
    regGroup.setHisCode(itemGroup.getHisCode());
    regGroup.setHisName(itemGroup.getHisName());
    regGroup.setPlatCode(itemGroup.getPlatCode());
    regGroup.setPlatName(itemGroup.getPlatName());
    regGroup.setClassCode(itemGroup.getClassCode());
    regGroup.setType(itemGroup.getType() != null ? itemGroup.getType() : "健康项目");
    regGroup.setPrice(itemGroup.getPrice());
    regGroup.setPriceAfterDis(itemGroup.getPrice());
    regGroup.setDisRate(BigDecimal.ONE);
    regGroup.setDepartmentId(itemGroup.getDepartmentId());
    regGroup.setDepartmentName(itemGroup.getDepartmentName());
    regGroup.setDepartmentCode(itemGroup.getDepartmentCode());
    regGroup.setPayStatus("待支付");
    regGroup.setMinDiscountRate(itemGroup.getMinDiscountRate());
    regGroup.setPriceDisDiffAmount(BigDecimal.ZERO);
    
    // 设置加减项标志和支付方式
    regGroup.setAddMinusFlag(getAddMinusFlag4Add(customerReg));
    regGroup.setPayerType(getPayerType4Add(customerReg, regGroup.getAddMinusFlag()));
    
    return regGroup;
}
