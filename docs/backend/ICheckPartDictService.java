package org.jeecg.modules.basicinfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.basicinfo.entity.CheckPartDict;

import java.util.List;

/**
 * @Description: 检查部位字典
 * @Author: jeecg-boot
 * @Date: 2024-12-19
 * @Version: V1.0
 */
public interface ICheckPartDictService extends IService<CheckPartDict> {

    /**
     * 根据项目ID获取部位选项（带使用频次排序）
     * @param itemGroupId 项目组合ID
     * @param keyword 搜索关键字
     * @return 部位列表
     */
    List<CheckPartDict> listByItemGroupWithFrequency(String itemGroupId, String keyword);

    /**
     * 通用部位搜索接口
     * @param keyword 搜索关键字
     * @return 部位列表
     */
    List<CheckPartDict> searchByKeyword(String keyword);

    /**
     * 更新使用频次
     * @param itemGroupId 项目组合ID
     * @param checkPartIds 部位ID列表
     */
    void updateUsageFrequency(String itemGroupId, List<String> checkPartIds);
}
