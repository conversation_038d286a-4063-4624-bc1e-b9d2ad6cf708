package org.jeecg.modules.formrule.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 表单规则配置DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "FormRuleConfigDTO", description = "表单规则配置数据传输对象")
public class FormRuleConfigDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private String id;

    @NotBlank(message = "表单代码不能为空")
    @ApiModelProperty(value = "表单代码", required = true)
    private String formCode;

    @NotBlank(message = "表单名称不能为空")
    @ApiModelProperty(value = "表单名称", required = true)
    private String formName;

    @ApiModelProperty(value = "表单描述")
    private String description;

    @ApiModelProperty(value = "版本号")
    private Integer version;

    @ApiModelProperty(value = "状态：1启用，0禁用")
    private Integer status;

    @Valid
    @NotNull(message = "字段规则不能为空")
    @ApiModelProperty(value = "字段规则列表", required = true)
    private List<FieldRuleDTO> fieldRules;

    @Valid
    @ApiModelProperty(value = "联动规则列表")
    private List<DependencyRuleDTO> dependencyRules;

    @ApiModelProperty(value = "最后更新时间戳")
    private Long lastUpdated;
}

/**
 * 字段规则DTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "FieldRuleDTO", description = "字段规则数据传输对象")
class FieldRuleDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "字段代码不能为空")
    @ApiModelProperty(value = "字段代码", required = true)
    private String fieldCode;

    @NotBlank(message = "字段名称不能为空")
    @ApiModelProperty(value = "字段名称", required = true)
    private String fieldName;

    @ApiModelProperty(value = "是否必填")
    private Boolean isRequired;

    @ApiModelProperty(value = "必填提示信息")
    private String requiredMessage;

    @ApiModelProperty(value = "验证规则列表")
    private List<ValidationRuleDTO> validationRules;

    @ApiModelProperty(value = "是否可见")
    private Boolean visible;

    @ApiModelProperty(value = "是否禁用")
    private Boolean disabled;

    @ApiModelProperty(value = "排序")
    private Integer sortOrder;
}

/**
 * 验证规则DTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ValidationRuleDTO", description = "验证规则数据传输对象")
class ValidationRuleDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "验证规则类型不能为空")
    @ApiModelProperty(value = "验证规则类型", required = true)
    private String type;

    @ApiModelProperty(value = "验证规则值")
    private Object value;

    @NotBlank(message = "错误提示信息不能为空")
    @ApiModelProperty(value = "错误提示信息", required = true)
    private String message;
}

/**
 * 联动规则DTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "DependencyRuleDTO", description = "联动规则数据传输对象")
class DependencyRuleDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "规则ID不能为空")
    @ApiModelProperty(value = "规则ID", required = true)
    private String id;

    @NotBlank(message = "源字段不能为空")
    @ApiModelProperty(value = "源字段", required = true)
    private String sourceField;

    @NotBlank(message = "目标字段不能为空")
    @ApiModelProperty(value = "目标字段", required = true)
    private String targetField;

    @NotBlank(message = "联动类型不能为空")
    @ApiModelProperty(value = "联动类型", required = true)
    private String dependencyType;

    @NotBlank(message = "条件类型不能为空")
    @ApiModelProperty(value = "条件类型", required = true)
    private String conditionType;

    @ApiModelProperty(value = "条件值")
    private Object conditionValue;

    @ApiModelProperty(value = "动作值")
    private Object actionValue;

    @ApiModelProperty(value = "优先级")
    private Integer priority;
}

/**
 * 表单规则版本信息DTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "FormRuleVersionDTO", description = "表单规则版本信息")
class FormRuleVersionDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "表单代码")
    private String formCode;

    @ApiModelProperty(value = "当前版本号")
    private Integer version;

    @ApiModelProperty(value = "最后更新时间戳")
    private Long lastUpdated;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}

/**
 * 表单规则查询DTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "FormRuleQueryDTO", description = "表单规则查询条件")
class FormRuleQueryDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "表单代码")
    private String formCode;

    @ApiModelProperty(value = "表单名称")
    private String formName;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建开始时间")
    private Date createTimeStart;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建结束时间")
    private Date createTimeEnd;
}

/**
 * 表单规则统计VO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "FormRuleStatisticsVO", description = "表单规则统计信息")
class FormRuleStatisticsVO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "总表单数")
    private Long totalForms;

    @ApiModelProperty(value = "启用表单数")
    private Long enabledForms;

    @ApiModelProperty(value = "禁用表单数")
    private Long disabledForms;

    @ApiModelProperty(value = "今日更新数")
    private Long todayUpdated;

    @ApiModelProperty(value = "本周更新数")
    private Long weekUpdated;

    @ApiModelProperty(value = "平均字段数")
    private Double avgFieldCount;

    @ApiModelProperty(value = "平均联动规则数")
    private Double avgDependencyCount;
}
