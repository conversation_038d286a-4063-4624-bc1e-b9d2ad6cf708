package org.jeecg.modules.formrule.service.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.formrule.dto.FormRuleConfigDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Collections;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 表单规则缓存服务
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
@Slf4j
public class FormRuleCacheService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    // 缓存键前缀
    private static final String CACHE_KEY_PREFIX = "form_rule:";
    private static final String VERSION_KEY_PREFIX = "form_rule_version:";
    private static final String LOCK_KEY_PREFIX = "form_rule_lock:";
    
    // 缓存过期时间
    private static final Duration CACHE_EXPIRE_TIME = Duration.ofHours(24);
    private static final Duration VERSION_EXPIRE_TIME = Duration.ofHours(1);
    private static final Duration LOCK_EXPIRE_TIME = Duration.ofMinutes(5);

    /**
     * 从缓存获取表单规则配置
     * 
     * @param formCode 表单代码
     * @return 表单规则配置
     */
    public FormRuleConfigDTO getFromCache(String formCode) {
        try {
            String cacheKey = CACHE_KEY_PREFIX + formCode;
            Object cached = redisTemplate.opsForValue().get(cacheKey);
            
            if (cached != null) {
                FormRuleConfigDTO config = JSON.parseObject(cached.toString(), FormRuleConfigDTO.class);
                log.debug("从缓存获取表单规则成功: formCode={}", formCode);
                return config;
            }
            
            log.debug("缓存中不存在表单规则: formCode={}", formCode);
            return null;
        } catch (Exception e) {
            log.error("从缓存获取表单规则失败: formCode={}", formCode, e);
            return null;
        }
    }

    /**
     * 将表单规则配置放入缓存
     * 
     * @param formCode 表单代码
     * @param config 表单规则配置
     */
    public void putToCache(String formCode, FormRuleConfigDTO config) {
        try {
            String cacheKey = CACHE_KEY_PREFIX + formCode;
            String configJson = JSON.toJSONString(config);
            
            redisTemplate.opsForValue().set(cacheKey, configJson, CACHE_EXPIRE_TIME);
            
            // 同时缓存版本信息
            String versionKey = VERSION_KEY_PREFIX + formCode;
            redisTemplate.opsForValue().set(versionKey, config.getVersion(), VERSION_EXPIRE_TIME);
            
            log.debug("表单规则缓存成功: formCode={}, version={}", formCode, config.getVersion());
        } catch (Exception e) {
            log.error("表单规则缓存失败: formCode={}", formCode, e);
        }
    }

    /**
     * 从缓存中移除表单规则配置
     * 
     * @param formCode 表单代码
     */
    public void removeFromCache(String formCode) {
        try {
            String cacheKey = CACHE_KEY_PREFIX + formCode;
            String versionKey = VERSION_KEY_PREFIX + formCode;
            
            redisTemplate.delete(cacheKey);
            redisTemplate.delete(versionKey);
            
            log.debug("表单规则缓存清除成功: formCode={}", formCode);
        } catch (Exception e) {
            log.error("表单规则缓存清除失败: formCode={}", formCode, e);
        }
    }

    /**
     * 获取缓存中的版本号
     * 
     * @param formCode 表单代码
     * @return 版本号
     */
    public Integer getVersionFromCache(String formCode) {
        try {
            String versionKey = VERSION_KEY_PREFIX + formCode;
            Object version = redisTemplate.opsForValue().get(versionKey);
            
            if (version != null) {
                return Integer.valueOf(version.toString());
            }
            
            return null;
        } catch (Exception e) {
            log.error("从缓存获取版本号失败: formCode={}", formCode, e);
            return null;
        }
    }

    /**
     * 检查缓存是否存在
     * 
     * @param formCode 表单代码
     * @return 是否存在
     */
    public boolean existsInCache(String formCode) {
        try {
            String cacheKey = CACHE_KEY_PREFIX + formCode;
            return Boolean.TRUE.equals(redisTemplate.hasKey(cacheKey));
        } catch (Exception e) {
            log.error("检查缓存存在性失败: formCode={}", formCode, e);
            return false;
        }
    }

    /**
     * 获取分布式锁
     * 
     * @param formCode 表单代码
     * @param requestId 请求ID
     * @return 是否获取成功
     */
    public boolean tryLock(String formCode, String requestId) {
        try {
            String lockKey = LOCK_KEY_PREFIX + formCode;
            Boolean result = redisTemplate.opsForValue().setIfAbsent(
                    lockKey, requestId, LOCK_EXPIRE_TIME);
            
            if (Boolean.TRUE.equals(result)) {
                log.debug("获取分布式锁成功: formCode={}, requestId={}", formCode, requestId);
                return true;
            }
            
            log.debug("获取分布式锁失败: formCode={}, requestId={}", formCode, requestId);
            return false;
        } catch (Exception e) {
            log.error("获取分布式锁异常: formCode={}, requestId={}", formCode, requestId, e);
            return false;
        }
    }

    /**
     * 释放分布式锁
     * 
     * @param formCode 表单代码
     * @param requestId 请求ID
     * @return 是否释放成功
     */
    public boolean releaseLock(String formCode, String requestId) {
        try {
            String lockKey = LOCK_KEY_PREFIX + formCode;
            
            // Lua脚本确保原子性操作
            String luaScript = 
                "if redis.call('get', KEYS[1]) == ARGV[1] then " +
                "    return redis.call('del', KEYS[1]) " +
                "else " +
                "    return 0 " +
                "end";
            
            DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>();
            redisScript.setScriptText(luaScript);
            redisScript.setResultType(Long.class);
            
            Long result = redisTemplate.execute(redisScript, 
                    Collections.singletonList(lockKey), requestId);
            
            boolean success = result != null && result > 0;
            log.debug("释放分布式锁{}: formCode={}, requestId={}", 
                    success ? "成功" : "失败", formCode, requestId);
            
            return success;
        } catch (Exception e) {
            log.error("释放分布式锁异常: formCode={}, requestId={}", formCode, requestId, e);
            return false;
        }
    }

    /**
     * 批量清除缓存
     * 
     * @param pattern 缓存键模式
     */
    public void clearCacheByPattern(String pattern) {
        try {
            Set<String> keys = redisTemplate.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                log.info("批量清除缓存成功: pattern={}, count={}", pattern, keys.size());
            }
        } catch (Exception e) {
            log.error("批量清除缓存失败: pattern={}", pattern, e);
        }
    }

    /**
     * 清除所有表单规则缓存
     */
    public void clearAllFormRuleCache() {
        try {
            clearCacheByPattern(CACHE_KEY_PREFIX + "*");
            clearCacheByPattern(VERSION_KEY_PREFIX + "*");
            log.info("清除所有表单规则缓存成功");
        } catch (Exception e) {
            log.error("清除所有表单规则缓存失败", e);
        }
    }

    /**
     * 获取缓存统计信息
     * 
     * @return 缓存统计信息
     */
    public CacheStatistics getCacheStatistics() {
        try {
            Set<String> cacheKeys = redisTemplate.keys(CACHE_KEY_PREFIX + "*");
            Set<String> versionKeys = redisTemplate.keys(VERSION_KEY_PREFIX + "*");
            Set<String> lockKeys = redisTemplate.keys(LOCK_KEY_PREFIX + "*");
            
            CacheStatistics statistics = new CacheStatistics();
            statistics.setCacheCount(cacheKeys != null ? cacheKeys.size() : 0);
            statistics.setVersionCount(versionKeys != null ? versionKeys.size() : 0);
            statistics.setLockCount(lockKeys != null ? lockKeys.size() : 0);
            
            return statistics;
        } catch (Exception e) {
            log.error("获取缓存统计信息失败", e);
            return new CacheStatistics();
        }
    }

    /**
     * 设置缓存过期时间
     * 
     * @param formCode 表单代码
     * @param expireTime 过期时间
     */
    public void setExpire(String formCode, Duration expireTime) {
        try {
            String cacheKey = CACHE_KEY_PREFIX + formCode;
            redisTemplate.expire(cacheKey, expireTime);
            log.debug("设置缓存过期时间成功: formCode={}, expireTime={}", formCode, expireTime);
        } catch (Exception e) {
            log.error("设置缓存过期时间失败: formCode={}", formCode, e);
        }
    }

    /**
     * 获取缓存剩余过期时间
     * 
     * @param formCode 表单代码
     * @return 剩余过期时间（秒）
     */
    public Long getExpire(String formCode) {
        try {
            String cacheKey = CACHE_KEY_PREFIX + formCode;
            return redisTemplate.getExpire(cacheKey, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("获取缓存过期时间失败: formCode={}", formCode, e);
            return -1L;
        }
    }

    /**
     * 缓存统计信息内部类
     */
    public static class CacheStatistics {
        private int cacheCount;
        private int versionCount;
        private int lockCount;

        // getter and setter methods
        public int getCacheCount() { return cacheCount; }
        public void setCacheCount(int cacheCount) { this.cacheCount = cacheCount; }
        
        public int getVersionCount() { return versionCount; }
        public void setVersionCount(int versionCount) { this.versionCount = versionCount; }
        
        public int getLockCount() { return lockCount; }
        public void setLockCount(int lockCount) { this.lockCount = lockCount; }
    }
}

/**
 * 表单规则通知服务
 */
@Service
@Slf4j
class FormRuleNotificationService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String NOTIFICATION_CHANNEL = "form_rule_update";

    /**
     * 推送规则更新通知
     * 
     * @param formCode 表单代码
     * @param config 规则配置
     */
    public void notifyRuleUpdate(String formCode, FormRuleConfigDTO config) {
        try {
            NotificationMessage message = new NotificationMessage()
                    .setType("RULE_UPDATE")
                    .setFormCode(formCode)
                    .setVersion(config.getVersion())
                    .setTimestamp(System.currentTimeMillis())
                    .setData(config);

            redisTemplate.convertAndSend(NOTIFICATION_CHANNEL, JSON.toJSONString(message));
            log.info("推送规则更新通知成功: formCode={}, version={}", formCode, config.getVersion());
        } catch (Exception e) {
            log.error("推送规则更新通知失败: formCode={}", formCode, e);
        }
    }

    /**
     * 推送状态变更通知
     * 
     * @param formCode 表单代码
     * @param status 新状态
     */
    public void notifyStatusChange(String formCode, Integer status) {
        try {
            NotificationMessage message = new NotificationMessage()
                    .setType("STATUS_CHANGE")
                    .setFormCode(formCode)
                    .setTimestamp(System.currentTimeMillis())
                    .setData(Collections.singletonMap("status", status));

            redisTemplate.convertAndSend(NOTIFICATION_CHANNEL, JSON.toJSONString(message));
            log.info("推送状态变更通知成功: formCode={}, status={}", formCode, status);
        } catch (Exception e) {
            log.error("推送状态变更通知失败: formCode={}", formCode, e);
        }
    }

    /**
     * 通知消息内部类
     */
    public static class NotificationMessage {
        private String type;
        private String formCode;
        private Integer version;
        private Long timestamp;
        private Object data;

        // getter and setter methods with fluent interface
        public String getType() { return type; }
        public NotificationMessage setType(String type) { this.type = type; return this; }
        
        public String getFormCode() { return formCode; }
        public NotificationMessage setFormCode(String formCode) { this.formCode = formCode; return this; }
        
        public Integer getVersion() { return version; }
        public NotificationMessage setVersion(Integer version) { this.version = version; return this; }
        
        public Long getTimestamp() { return timestamp; }
        public NotificationMessage setTimestamp(Long timestamp) { this.timestamp = timestamp; return this; }
        
        public Object getData() { return data; }
        public NotificationMessage setData(Object data) { this.data = data; return this; }
    }
}
