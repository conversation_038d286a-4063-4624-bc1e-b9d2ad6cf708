# 保存配置接口调用修复说明

## 背景
点击“保存配置”按钮没有触发后端接口调用。排查后发现前端在使用封装的 defHttp 且参数 `{ isTransformResponse: false }` 时，返回的是原始 Axios 响应对象，需要以 `res.data` 方式读取；而界面原先直接使用 `result.success`，导致未按预期处理响应，表现为未看到成功提示，像是未请求。

## 修改点

1) 调整保存方法，兼容原始 Axios 响应
```ts
// src/views/system/FormRuleManagement.vue
const res = await formRuleApi.saveFormConfiguration(configuration);
const data = res?.data ?? res; // 兼容两种返回
if (data?.success) {
  message.success('配置保存成功');
} else {
  message.error(data?.message || '保存配置失败');
}
```

2) API 层已开启原始响应模式
```ts
// src/api/formRule.ts
export const saveFormConfiguration = (configuration: any) => {
  return defHttp.post({
    url: Api.saveFormConfiguration,
    data: configuration,
  }, { isTransformResponse: false });
};
```

3) 增加调试日志，便于定位
```ts
console.log('保存配置-请求参数:', configuration);
console.log('保存配置-响应:', data);
```

## 验证方式
- 打开浏览器开发者工具 → Network
- 点击“保存配置”
- 应看到请求：`POST /jeecgboot/api/form-rules/configuration`
- 响应体包含：`{"success": true|false, ...}`

## 注意
- 若未选择表单，将提示“请先选择表单”并不发起请求
- 本次仅修复调用和响应处理逻辑，不改变数据结构
- 前端为热更新，无需重启服务

