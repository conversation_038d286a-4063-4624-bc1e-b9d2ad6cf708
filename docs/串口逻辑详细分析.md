# 串口逻辑详细分析

## 概述

本文档详细分析体检系统中串口设备的完整逻辑流程，包括设备配置、连接建立、数据传输、解析处理等各个环节。

## 1. 串口设备架构概览

### 1.1 核心组件
- **WebSocketService**: WebSocket通信服务
- **ComEquipment**: 串口设备配置管理
- **ItemResultPannel**: 项目结果录入面板（主要串口逻辑）

### 1.2 数据流向
```
串口设备 → WebSocket服务器 → 前端WebSocket客户端 → 数据解析函数 → 项目结果赋值
```

## 2. 设备配置管理

### 2.1 设备类型分类
根据 `ComQuipment.data.ts` 配置：

**设备类型字典 (device_type)**:
- `串口设备`: 需要串口通信的设备
- `其他`: 不需要串口配置的设备

**串口设备子类型 (dicom_device)**:
- `健桥医电肺功能`: 特定医疗设备
- `普通串口`: 通用串口设备

### 2.2 串口参数配置
当设备类型为"串口设备"时，需要配置以下参数：

<augment_code_snippet path="src/views/basicinfo/ComQuipment.data.ts" mode="EXCERPT">
````typescript
{
  label: '串口描述符',
  field: 'portDescriptor',
  component: 'Input',
  dynamicRules: ({ model, schema }) => {
    // 只有当设备类型是"串口设备"时才必填
    if (model.deviceType === '串口设备') {
      return [{ required: true, message: '请输入串口描述符!' }];
    }
    return [];
  },
},
{
  label: '波特率',
  field: 'baudRate',
  component: 'InputNumber',
  dynamicRules: ({ model, schema }) => {
    if (model.deviceType === '串口设备') {
      return [{ required: true, message: '请输入波特率!' }];
    }
    return [];
  },
}
````
</augment_code_snippet>

### 2.3 设备配置字段详解
- **portDescriptor**: 串口描述符（如 COM1, COM2）
- **baudRate**: 波特率（如 9600, 115200）
- **dataBits**: 数据位（通常为 8）
- **stopBits**: 停止位（1, 1.5, 2）
- **verifyBits**: 校验位（无校验、奇校验、偶校验等）
- **safetySleepTime**: 安全睡眠时间（毫秒）
- **websocketUrl**: WebSocket服务器地址
- **cmdFunction**: 设备指令函数代码
- **dataFunction**: 数据解析函数代码

## 3. WebSocket通信机制

### 3.1 WebSocketService类
<augment_code_snippet path="src/utils/websocketService.ts" mode="EXCERPT">
````typescript
class WebSocketService {
  private socket: Socket | null = null;
  private url: string;

  connect(userId: string, eventHandlers: { [event: string]: (...args: any[]) => void }): void {
    console.log('Connecting to WebSocket server:', this.url);
    this.socket = io(this.url, {
      query: { userId },
    });

    for (const [event, handler] of Object.entries(eventHandlers)) {
      this.socket.on(event, handler);
    }
  }
````
</augment_code_snippet>

### 3.2 WebSocket事件类型
- **connect**: 连接成功事件
- **connect_error**: 连接错误事件
- **disconnect**: 断开连接事件
- **serialData**: 串口数据接收事件
- **portStatus**: 串口状态变化事件
- **portCmdStatus**: 串口指令状态事件

## 4. 串口设备初始化流程

### 4.1 设备筛选与分组
<augment_code_snippet path="src/views/station/ItemResultPannel.vue" mode="EXCERPT">
````javascript
function initComEquipment(groupList) {
  try {
    let comEquipments = [];
    groupList.value.forEach((group) => {
      if (group.checkStatus == '未检') {
        let comEquipment = comEquipmentList.value.find((item) => item.groupId == group.itemGroupId);
        if (comEquipment) {
          group.comEquipmentTip = '正在初始化串口设备...';
          group.comEquipmentStatus = '';
          comEquipments.push(comEquipment);
        }
      }
    });

    const groupedComEquipments = groupComEquipmentsByWebSocketUrl(comEquipments);
````
</augment_code_snippet>

### 4.2 WebSocket连接建立
系统按WebSocket地址对设备进行分组，为每个地址创建独立的连接：

<augment_code_snippet path="src/views/station/ItemResultPannel.vue" mode="EXCERPT">
````javascript
for (let [websocketUrl, comEquipments] of Object.entries(groupedComEquipments)) {
  const ws = websocketMap.get(websocketUrl) || new WebSocketService(websocketUrl);
  const eventHandlers = {
    connect: () => {
      console.log('成功连接到 WebSocket server');
      comEquipments.forEach((comEquipment) => {
        let extraConfig = comEquipment.extraConfig || {};
        let serialPortDesc = buildPhysicalExamConnectRequest(comEquipment, extraConfig);
        ws?.sendMessage('connectPort', serialPortDesc);
      });
    },
````
</augment_code_snippet>

## 5. PhysicalExamConnectRequest数据结构

### 5.1 完整数据结构构建
系统构建完整的设备连接请求对象：

<augment_code_snippet path="src/views/station/ItemResultPannel.vue" mode="EXCERPT">
````javascript
function buildPhysicalExamConnectRequest(comEquipment, extraConfig) {
  const currentTimestamp = Date.now();
  
  const physicalExamConnectRequest = {
    // 设备基本信息
    deviceType: comEquipment.deviceType || '',
    deviceModel: comEquipment.deviceModel || '',
    priority: comEquipment.priority || 1,
    requestTimestamp: currentTimestamp,
    autoStartMeasurement: comEquipment.autoStartMeasurement == '1' || true,
    
    // 嵌套对象
    patientInfo: buildPatientInfo(),
    examItemInfo: buildExamItemInfo(comEquipment),
    operatorInfo: buildOperatorInfo(),
    deviceProperties: buildDeviceProperties(comEquipment, parsedExtraConfig),
  };
````
</augment_code_snippet>

### 5.2 设备属性配置
<augment_code_snippet path="src/views/station/ItemResultPannel.vue" mode="EXCERPT">
````javascript
function buildDeviceProperties(comEquipment, parsedExtraConfig) {
  return {
    // 串口配置
    portDescriptor: comEquipment.portDescriptor,
    baudRate: parsedExtraConfig.baudRate || comEquipment.baudRate,
    dataBits: parsedExtraConfig.dataBits || comEquipment.dataBits,
    stopBits: parsedExtraConfig.stopBits || comEquipment.stopBits,
    parity: comEquipment.verifyBits,
    readTimeout: parsedExtraConfig.readTimeout || 5000,
    writeTimeout: parsedExtraConfig.writeTimeout || 5000,
    flowControl: parsedExtraConfig.flowControl || 'NONE',
    safetySleepTime: comEquipment.safetySleepTime,
````
</augment_code_snippet>

## 6. 串口数据处理核心逻辑

### 6.1 数据接收处理
<augment_code_snippet path="src/views/station/ItemResultPannel.vue" mode="EXCERPT">
````javascript
serialData: (comData: object) => {
  console.log('接收到串口数据:', comData);
  let comEquipment = comEquipments.find((item) => item.portDescriptor == comData.port);
  if (!comEquipment) return;
  
  let group = groupList.value.find((group) => group.itemGroupId == comEquipment.groupId);
  if (!group) return;
  
  try {
    let dataFunction = comEquipment.dataFunction ? createDataFunction(comEquipment.dataFunction) : null;
    if (!dataFunction) {
      message.error('未配置设备数据解析代码，将无法正常获取数据！');
      return;
    }
    
    group.comEquipmentTip = '设备数据解析中...';
    dataFunction(group, comData.data);
    group.comEquipmentTip = '设备数据解析完成';
  } catch (e) {
    console.log('解析设备数据异常', e);
    group.comEquipmentTip = '设备数据解析异常';
    message.error('解析设备数据异常：' + JSON.stringify(e));
  }
}
````
</augment_code_snippet>

### 6.2 动态函数创建
<augment_code_snippet path="src/views/station/ItemResultPannel.vue" mode="EXCERPT">
````javascript
function createCmdFunction(cmdCode: string): Function {
  return new Function('customerReg', cmdCode);
}

function createDataFunction(dataCode: string): Function {
  return new Function('group', 'comData', dataCode);
}
````
</augment_code_snippet>

## 7. 设备状态管理

### 7.1 连接状态处理
<augment_code_snippet path="src/views/station/ItemResultPannel.vue" mode="EXCERPT">
````javascript
portStatus: (status: any) => {
  console.log('串口状态:', status);
  let comEquipment = comEquipments.find((item) => item.portDescriptor == status.port);
  if (!comEquipment) return;
  
  let group = groupList.value.find((group) => group.itemGroupId == comEquipment.groupId);
  if (!group) return;
  
  if (status?.open) {
    group.comEquipmentStatus = 'success';
    group.comEquipmentTip = '设备连接成功';
    
    let cmdFunction = comEquipment.cmdFunction ? createCmdFunction(comEquipment.cmdFunction) : null;
    if (cmdFunction) {
      let cmdMessage = cmdFunction(currentReg.value);
      console.log('向设备发送指令：' + JSON.stringify(cmdMessage));
      if (cmdMessage) {
        ws?.sendMessage('sendPortCmd', cmdMessage);
      }
    }
  } else {
    message.error('设备串口打开失败！');
    group.comEquipmentTip = '设备串口打开失败!';
    group.comEquipmentStatus = 'error';
  }
}
````
</augment_code_snippet>

## 8. 特殊设备处理

### 8.1 特殊设备处理
系统支持多种特殊设备的处理逻辑，每种设备都有对应的数据处理方式。特殊设备通常需要：

- 专门的通信协议
- 特殊的数据格式处理
- 自定义的结果映射逻辑

## 9. 错误处理与异常管理

### 9.1 连接异常处理
- WebSocket连接失败时显示错误状态
- 串口打开失败时提示用户
- 数据解析异常时记录错误信息

### 9.2 设备状态指示
- `comEquipmentStatus`: 设备连接状态（success/error）
- `comEquipmentTip`: 设备状态提示文本

## 10. 总结

串口逻辑的核心特点：
1. **模块化设计**: 设备配置、通信、解析分离
2. **动态配置**: 支持不同设备类型的灵活配置
3. **实时通信**: 基于WebSocket的实时数据传输
4. **错误处理**: 完善的异常处理和状态管理
5. **扩展性**: 支持新设备类型的快速接入

这种设计使得系统能够灵活适配各种医疗设备，通过配置化的方式实现设备接入，无需修改核心代码。

## 11. 数据解析函数示例

### 11.1 典型解析函数结构
根据设备配置中的 `dataFunction` 字段，解析函数通常包含以下逻辑：

```javascript
// 示例：身高体重秤数据解析
console.log('========group ======', group);
console.log('=====comData =========', comData);

let data = JSON.parse(comData);  // 解析JSON数据
console.log('=====data =========', data);

// 根据项目名称找到对应的项目并赋值
let heightItem = group.itemList.find((item) => item.name == '身高');
if(heightItem) {
  heightItem.itemResult.value = data.height;
}

let weightItem = group.itemList.find((item) => item.name == '体重');
if(weightItem) {
  weightItem.itemResult.value = data.weight;
}
```

### 11.2 血压计数据解析示例
```javascript
// 血压计数据解析
let data = JSON.parse(comData);

// 收缩压
let sysItem = group.itemList.find((item) => item.name == '收缩压');
if(sysItem) {
  sysItem.itemResult.value = data.systolic;
  sysItem.itemResult.abnormalFlag = data.systolic > 140 ? '1' : '0';
}

// 舒张压
let diaItem = group.itemList.find((item) => item.name == '舒张压');
if(diaItem) {
  diaItem.itemResult.value = data.diastolic;
  diaItem.itemResult.abnormalFlag = data.diastolic > 90 ? '1' : '0';
}

// 心率
let hrItem = group.itemList.find((item) => item.name == '心率');
if(hrItem) {
  hrItem.itemResult.value = data.heartRate;
}
```

## 12. 指令函数配置

### 12.1 设备指令发送
`cmdFunction` 用于向设备发送控制指令：

```javascript
// 示例：向设备发送患者信息
return {
  port: "COM3",
  command: JSON.stringify({
    action: "setPatient",
    patientId: customerReg.examNo,
    patientName: customerReg.name,
    gender: customerReg.gender,
    age: customerReg.age
  })
};
```

### 12.2 指令执行时机
- 设备连接成功后自动执行
- 可以发送患者信息、测量参数等
- 支持设备初始化指令

## 13. 扩展配置 (extraConfig)

### 13.1 高级设备配置
`extraConfig` 字段支持JSON格式的扩展配置：

```json
{
  "timeoutMs": 30000,
  "retryCount": 3,
  "retryIntervalMs": 1000,
  "needsWarmup": true,
  "warmupTime": 5000,
  "measurementTime": 60,
  "needsBuffering": true,
  "dataEndMarkers": ["$", "\r\n"],
  "dataStartMarkers": ["RD", "B0", "B1"],
  "characteristicMarkers": ["RD", "$", "*B"],
  "maxBufferSize": 1024,
  "bufferTimeoutMs": 10000,
  "needsStartCommand": true,
  "startMeasurementCommand": "RD",
  "autoStartMeasurement": false,
  "measurementMode": "MANUAL",
  "needsActiveQuery": false,
  "queryResultCommand": "GET_RESULT",
  "queryIntervalMs": 1000,
  "maxQueryAttempts": 10,
  "queryTimeoutMs": 30000
}
```

### 13.2 配置参数说明
- **timeoutMs**: 连接超时时间
- **retryCount**: 重试次数
- **needsWarmup**: 是否需要预热
- **needsBuffering**: 是否需要数据缓冲
- **dataEndMarkers**: 数据结束标记
- **needsActiveQuery**: 是否需要主动查询结果

## 14. 设备管理界面

### 14.1 设备列表管理
系统提供专门的设备管理界面 `ComQuipment.vue`：
- 设备基本信息配置
- 串口参数设置
- 指令和解析函数编辑
- 设备状态监控

### 14.2 动态校验规则
根据设备类型动态调整表单校验：
- 串口设备：串口相关字段必填
- 其他设备：串口字段可选

## 15. 测试与调试

### 15.1 测试服务器
系统提供测试WebSocket服务器 (`tests/server/index.ts`)：
- 模拟串口数据发送
- WebSocket连接测试
- 数据格式验证

### 15.2 调试信息
系统在控制台输出详细的调试信息：
- WebSocket连接状态
- 串口数据接收日志
- 数据解析过程跟踪
- 错误异常详情

## 16. 性能优化

### 16.1 连接复用
- 按WebSocket地址分组设备
- 复用WebSocket连接
- 减少连接开销

### 16.2 数据缓冲
- 支持数据缓冲机制
- 处理不完整数据包
- 优化数据传输效率

## 17. 安全考虑

### 17.1 数据验证
- 串口数据格式验证
- 异常数据过滤
- 防止恶意数据注入

### 17.2 权限控制
- 设备访问权限管理
- 操作员身份验证
- 数据修改审计

这种完整的串口逻辑设计确保了系统的稳定性、可扩展性和安全性，为医疗设备的集成提供了强大的技术支撑。
