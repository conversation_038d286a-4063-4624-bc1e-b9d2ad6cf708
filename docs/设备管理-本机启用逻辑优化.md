# 设备管理 - 本机启用逻辑优化

## 概述

本文档描述了对串口设备管理中"本机启用"功能的逻辑优化，主要调整了 `handleAdd2Local` 方法的处理流程。

## 修改文件

- `src/views/basicinfo/ComQuipmentList.vue`

## 修改内容

### 原有逻辑

原来的 `handleAdd2Local` 方法采用以下逻辑：
1. 查找设备是否已存在于本地设备列表中
2. 如果不存在，直接添加到列表
3. 如果存在，替换原有记录

### 优化后逻辑

调整后的 `handleAdd2Local` 方法采用以下逻辑：
1. **先删除**：从本地设备列表中删除该设备（如果存在）
2. **后插入**：将设备添加到列表末尾，确保设备处于启用状态
3. 保存到本地存储并刷新列表

### 代码变更

```javascript
/**
 * 本机启用
 */
async function handleAdd2Local(record) {
  let localEquipmentList = JSON.parse(getSettingFromLocalStorage('comEquipmentList', '[]'));
  
  // 先删除该设备（如果存在）
  localEquipmentList = localEquipmentList.filter((item) => item.id !== record.id);
  
  // 创建启用状态的设备记录
  const enabledRecord = {
    ...record,
    // 确保设备处于启用状态（虽然没有专门的启用字段，但通过存在于本地列表中表示启用）
  };
  
  // 插入设备到列表中
  localEquipmentList.push(enabledRecord);
  
  localStorage.setItem('comEquipmentList', JSON.stringify(localEquipmentList));
  handleSuccess();
}
```

## 优化优势

1. **逻辑清晰**：先删除后插入的逻辑更加直观，避免了复杂的判断分支
2. **状态一致**：确保每次启用操作都会将设备置于启用状态
3. **数据整洁**：避免重复记录，保持本地设备列表的数据一致性
4. **操作简化**：统一的处理流程，减少了条件判断的复杂性

## 相关功能

### 本机状态显示

设备的本机状态通过 `afterFetch` 回调函数动态计算：

```javascript
afterFetch: (data) => {
  let localEquipmentList = JSON.parse(getSettingFromLocalStorage('comEquipmentList', '[]'));
  data.forEach((item) => {
    let isLocal = localEquipmentList.findIndex((localItem) => localItem.id === item.id) !== -1;
    item.localStatus = isLocal ? '已启用' : '未启用';
  });
  return data;
}
```

### 本机停用功能

对应的停用功能 `handleDeleteLocal` 保持不变：

```javascript
async function handleDeleteLocal(record) {
  let localEquipmentList = JSON.parse(getSettingFromLocalStorage('comEquipmentList', '[]'));
  let index = localEquipmentList.findIndex((item) => item.id === record.id);
  if (index !== -1) {
    localEquipmentList.splice(index, 1);
    localStorage.setItem('comEquipmentList', JSON.stringify(localEquipmentList));
    handleSuccess();
  }
}
```

## 数据存储

- **存储位置**：localStorage
- **存储键名**：`comEquipmentList`
- **数据格式**：JSON 字符串数组
- **数据结构**：设备完整信息对象数组

## 注意事项

1. 本地设备列表存储在浏览器的 localStorage 中，清除浏览器数据会丢失设置
2. 设备的启用状态通过是否存在于本地列表中来判断，没有专门的启用字段
3. 每次启用操作都会刷新整个设备列表显示
4. 修改不影响服务器端的设备数据，仅影响本地工作站的设备配置

## 测试建议

1. 测试首次启用设备的功能
2. 测试重复启用同一设备的功能
3. 测试启用后设备状态显示是否正确
4. 测试启用后停用功能是否正常
5. 验证本地存储数据的完整性和一致性
