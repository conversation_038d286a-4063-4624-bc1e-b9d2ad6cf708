# 结果录入依赖项目验证方案

## 需求分析

在结果录入时，需要考虑项目之间的依赖关系，确保：
1. **依赖项目已录入**：录入某项目结果前，其依赖的项目必须已有结果
2. **依赖验证提醒**：当依赖项目缺失时，给出明确提示
3. **智能引导**：提供便捷的依赖项目录入入口
4. **数据完整性**：确保计算型项目能获取到所需的依赖数据

## 业务场景

### 典型场景1：肺功能检查依赖身高体重
- **主项目**：肺功能检查
- **依赖项目**：内科检查 → 身高、体重
- **验证逻辑**：录入肺功能结果前，必须先录入身高、体重

### 典型场景2：BMI计算依赖身高体重
- **主项目**：BMI（计算型）
- **依赖项目**：身高、体重（数值型）
- **验证逻辑**：BMI自动计算需要身高、体重的数值

### 典型场景3：综合评估依赖多项检查
- **主项目**：心血管风险评估
- **依赖项目**：血压、血脂、心电图等
- **验证逻辑**：评估前需要相关检查结果完整

## 技术方案

### 1. 前端验证组件

#### 1.1 依赖项目检查器
```javascript
// DependencyChecker.js
class DependencyChecker {
  constructor(groupList, dependencyMap) {
    this.groupList = groupList;
    this.dependencyMap = dependencyMap;
  }

  // 检查项目的依赖关系
  checkDependencies(groupId) {
    const dependencies = this.dependencyMap.get(groupId);
    if (!dependencies || dependencies.length === 0) {
      return { valid: true, missing: [] };
    }

    const missing = [];
    dependencies.forEach(dep => {
      if (dep.relationItemType === 'GROUP') {
        // 检查大项依赖
        if (!this.isGroupCompleted(dep.relationGroupId)) {
          missing.push({
            type: 'GROUP',
            id: dep.relationGroupId,
            name: dep.relationGroupName
          });
        }
      } else if (dep.relationItemType === 'ITEM') {
        // 检查小项依赖
        if (!this.isItemCompleted(dep.relationGroupId, dep.relationItemId)) {
          missing.push({
            type: 'ITEM',
            groupId: dep.relationGroupId,
            groupName: dep.relationGroupName,
            itemId: dep.relationItemId,
            itemName: dep.relationItemName
          });
        }
      }
    });

    return {
      valid: missing.length === 0,
      missing: missing
    };
  }

  // 检查大项是否完成
  isGroupCompleted(groupId) {
    const group = this.groupList.find(g => g.itemGroupId === groupId);
    return group && group.checkStatus === 1; // 1表示已完成
  }

  // 检查小项是否完成
  isItemCompleted(groupId, itemId) {
    const group = this.groupList.find(g => g.itemGroupId === groupId);
    if (!group || !group.itemList) return false;
    
    const item = group.itemList.find(i => i.id === itemId);
    return item && item.itemResult && item.itemResult.value !== '';
  }
}
```

#### 1.2 依赖验证弹窗组件
```vue
<!-- DependencyValidationModal.vue -->
<template>
  <a-modal
    v-model:open="visible"
    title="依赖项目检查"
    width="600px"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="dependency-check-content">
      <a-alert
        message="检测到未完成的依赖项目"
        description="以下项目需要先录入结果才能继续："
        type="warning"
        show-icon
        style="margin-bottom: 16px"
      />
      
      <a-list :data-source="missingDependencies" size="small">
        <template #renderItem="{ item }">
          <a-list-item>
            <a-list-item-meta>
              <template #title>
                <span v-if="item.type === 'GROUP'">
                  {{ item.name }} (大项)
                </span>
                <span v-else>
                  {{ item.groupName }} → {{ item.itemName }} (小项)
                </span>
              </template>
              <template #description>
                <a-tag color="orange">待录入</a-tag>
              </template>
            </a-list-item-meta>
            <template #actions>
              <a @click="navigateToItem(item)">去录入</a>
            </template>
          </a-list-item>
        </template>
      </a-list>
    </div>
  </a-modal>
</template>

<script setup>
import { ref } from 'vue';

const visible = ref(false);
const missingDependencies = ref([]);
const currentGroup = ref(null);

const emit = defineEmits(['navigate', 'confirm', 'cancel']);

function open(group, missing) {
  currentGroup.value = group;
  missingDependencies.value = missing;
  visible.value = true;
}

function handleConfirm() {
  emit('confirm', currentGroup.value);
  visible.value = false;
}

function handleCancel() {
  emit('cancel');
  visible.value = false;
}

function navigateToItem(item) {
  emit('navigate', item);
  visible.value = false;
}

defineExpose({ open });
</script>
```

### 2. 后端API增强

#### 2.1 依赖关系查询API
```java
// ItemGroupRelationController.java
@ApiOperation(value = "查询项目依赖关系", notes = "根据项目组ID查询其依赖的项目")
@GetMapping(value = "/getDependenciesByGroupId")
public Result<?> getDependenciesByGroupId(@RequestParam String groupId) {
    try {
        List<ItemGroupRelation> dependencies = itemGroupRelationService.list(
            new LambdaQueryWrapper<ItemGroupRelation>()
                .eq(ItemGroupRelation::getGroupId, groupId)
                .eq(ItemGroupRelation::getRelation, "依赖")
        );
        return Result.OK(dependencies);
    } catch (Exception e) {
        return Result.error("查询依赖关系失败：" + e.getMessage());
    }
}

@ApiOperation(value = "批量查询项目依赖关系", notes = "根据多个项目组ID批量查询依赖关系")
@PostMapping(value = "/getDependenciesByGroupIds")
public Result<?> getDependenciesByGroupIds(@RequestBody List<String> groupIds) {
    try {
        List<ItemGroupRelation> dependencies = itemGroupRelationService.list(
            new LambdaQueryWrapper<ItemGroupRelation>()
                .in(ItemGroupRelation::getGroupId, groupIds)
                .eq(ItemGroupRelation::getRelation, "依赖")
        );
        
        // 按groupId分组返回
        Map<String, List<ItemGroupRelation>> dependencyMap = dependencies.stream()
            .collect(Collectors.groupingBy(ItemGroupRelation::getGroupId));
            
        return Result.OK(dependencyMap);
    } catch (Exception e) {
        return Result.error("批量查询依赖关系失败：" + e.getMessage());
    }
}
```

#### 2.2 依赖验证服务
```java
// ItemGroupRelationServiceImpl.java
@Override
public DependencyValidationResult validateDependencies(String regId, String groupId) {
    // 1. 查询项目的依赖关系
    List<ItemGroupRelation> dependencies = list(
        new LambdaQueryWrapper<ItemGroupRelation>()
            .eq(ItemGroupRelation::getGroupId, groupId)
            .eq(ItemGroupRelation::getRelation, "依赖")
    );
    
    if (dependencies.isEmpty()) {
        return DependencyValidationResult.success();
    }
    
    // 2. 查询体检人的项目完成情况
    List<CustomerRegItemGroup> regGroups = customerRegItemGroupService.list(
        new LambdaQueryWrapper<CustomerRegItemGroup>()
            .eq(CustomerRegItemGroup::getCustomerRegId, regId)
            .ne(CustomerRegItemGroup::getAddMinusFlag, -1)
    );
    
    // 3. 验证每个依赖项目
    List<MissingDependency> missing = new ArrayList<>();
    for (ItemGroupRelation dep : dependencies) {
        if ("GROUP".equals(dep.getRelationItemType())) {
            // 验证大项依赖
            if (!isGroupCompleted(regGroups, dep.getRelationGroupId())) {
                missing.add(new MissingDependency(dep));
            }
        } else if ("ITEM".equals(dep.getRelationItemType())) {
            // 验证小项依赖
            if (!isItemCompleted(regGroups, dep.getRelationGroupId(), dep.getRelationItemId())) {
                missing.add(new MissingDependency(dep));
            }
        }
    }
    
    return missing.isEmpty() ? 
        DependencyValidationResult.success() : 
        DependencyValidationResult.failure(missing);
}

private boolean isGroupCompleted(List<CustomerRegItemGroup> regGroups, String groupId) {
    return regGroups.stream()
        .anyMatch(g -> g.getItemGroupId().equals(groupId) && g.getCheckStatus() == 1);
}

private boolean isItemCompleted(List<CustomerRegItemGroup> regGroups, String groupId, String itemId) {
    // 这里需要查询具体的项目结果
    // 可以通过CustomerRegItemResult表查询
    return customerRegItemResultService.isItemCompleted(regGroups.get(0).getCustomerRegId(), itemId);
}
```

### 3. 前端集成方案

#### 3.1 在ItemResultPannel中集成依赖验证
```javascript
// ItemResultPannel.vue 增强
import DependencyChecker from '@/utils/DependencyChecker';
import DependencyValidationModal from './components/DependencyValidationModal.vue';

// 依赖关系数据
const dependencyMap = ref(new Map());
const dependencyChecker = ref(null);
const dependencyModal = ref(null);

// 加载依赖关系数据
async function loadDependencies() {
  try {
    const groupIds = groupList.value.map(g => g.itemGroupId);
    const response = await getDependenciesByGroupIds(groupIds);
    
    // 构建依赖关系映射
    dependencyMap.value.clear();
    Object.entries(response).forEach(([groupId, deps]) => {
      dependencyMap.value.set(groupId, deps);
    });
    
    // 初始化依赖检查器
    dependencyChecker.value = new DependencyChecker(groupList.value, dependencyMap.value);
  } catch (error) {
    console.error('加载依赖关系失败:', error);
  }
}

// 在录入结果前验证依赖
function beforeSaveResult(group, item) {
  if (!dependencyChecker.value) return true;
  
  const validation = dependencyChecker.value.checkDependencies(group.itemGroupId);
  if (!validation.valid) {
    // 显示依赖验证弹窗
    dependencyModal.value?.open(group, validation.missing);
    return false;
  }
  
  return true;
}

// 处理依赖验证结果
function handleDependencyNavigation(missingItem) {
  if (missingItem.type === 'GROUP') {
    // 导航到大项
    navigateToGroup(missingItem.id);
  } else {
    // 导航到小项
    navigateToItem(missingItem.groupId, missingItem.itemId);
  }
}

// 导航到指定项目组
function navigateToGroup(groupId) {
  const targetGroup = groupList.value.find(g => g.itemGroupId === groupId);
  if (targetGroup) {
    // 滚动到目标项目组
    const element = document.getElementById(`group-${groupId}`);
    element?.scrollIntoView({ behavior: 'smooth' });
    
    // 高亮显示
    highlightGroup(groupId);
  }
}

// 导航到指定小项
function navigateToItem(groupId, itemId) {
  navigateToGroup(groupId);
  
  // 延迟高亮小项
  setTimeout(() => {
    const element = document.getElementById(`item-${itemId}`);
    element?.scrollIntoView({ behavior: 'smooth' });
    highlightItem(itemId);
  }, 500);
}
```

#### 3.2 结果保存拦截
```javascript
// 修改保存结果的方法
async function handleSaveResult(group, item, value) {
  // 1. 依赖验证
  if (!beforeSaveResult(group, item)) {
    return; // 依赖验证失败，中断保存
  }
  
  // 2. 正常保存逻辑
  try {
    await saveItemResult({
      regId: currentReg.value.id,
      groupId: group.id,
      itemId: item.id,
      value: value
    });
    
    // 3. 更新本地数据
    item.itemResult.value = value;
    
    // 4. 触发计算型项目重新计算
    triggerCalculation(group);
    
    message.success('保存成功');
  } catch (error) {
    message.error('保存失败：' + error.message);
  }
}
```

### 4. 用户体验优化

#### 4.1 视觉提示
```css
/* 依赖项目缺失时的样式 */
.dependency-missing {
  border: 2px dashed #ff7875;
  background-color: #fff2f0;
}

.dependency-missing .ant-card-head-title::before {
  content: "⚠️ ";
  color: #ff7875;
}

/* 依赖项目完成时的样式 */
.dependency-satisfied {
  border-left: 4px solid #52c41a;
}

/* 高亮动画 */
.highlight-group {
  animation: highlight 2s ease-in-out;
}

@keyframes highlight {
  0% { background-color: #fff7e6; }
  50% { background-color: #ffd591; }
  100% { background-color: transparent; }
}
```

#### 4.2 智能提示
```javascript
// 智能提示组件
const DependencyTip = {
  template: `
    <a-tooltip v-if="hasDependencies" placement="top">
      <template #title>
        <div>
          <div>此项目依赖以下项目：</div>
          <ul>
            <li v-for="dep in dependencies" :key="dep.id">
              {{ dep.relationGroupName }}
              <span v-if="dep.relationItemName"> → {{ dep.relationItemName }}</span>
            </li>
          </ul>
        </div>
      </template>
      <a-icon type="info-circle" style="color: #1890ff; margin-left: 8px;" />
    </a-tooltip>
  `,
  props: ['dependencies'],
  computed: {
    hasDependencies() {
      return this.dependencies && this.dependencies.length > 0;
    }
  }
};
```

## 实施计划

### 阶段1：基础功能（1-2天）
1. ✅ 后端依赖关系查询API
2. ✅ 前端依赖检查器组件
3. ✅ 基础验证逻辑

### 阶段2：用户体验（2-3天）
1. 🔄 依赖验证弹窗
2. 🔄 智能导航功能
3. 🔄 视觉提示和高亮

### 阶段3：高级功能（3-4天）
1. ⏳ 批量依赖验证
2. ⏳ 智能推荐录入顺序
3. ⏳ 依赖关系可视化

### 阶段4：测试优化（1-2天）
1. ⏳ 功能测试
2. ⏳ 性能优化
3. ⏳ 用户体验调优

## 总结

这个方案通过以下方式解决了结果录入时的依赖项目问题：

1. **智能检测**：自动检测项目依赖关系，确保数据完整性
2. **友好提示**：清晰的依赖缺失提示，避免用户困惑
3. **便捷导航**：一键跳转到依赖项目，提升录入效率
4. **视觉引导**：通过颜色和动画引导用户操作
5. **渐进增强**：分阶段实施，确保稳定性和可维护性

该方案既保证了数据的准确性，又提供了良好的用户体验，是一个实用且可扩展的解决方案。
