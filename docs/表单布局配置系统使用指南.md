# 表单布局配置系统使用指南

## 概述

基于现有的表单规则管理系统，我们扩展了表单布局配置功能，使表单的布局设计变得可配置化。系统支持可视化拖拽设计、多种布局模板、响应式布局等特性。

## 核心功能

### 1. 可视化布局设计器
- **拖拽式设计**: 通过拖拽字段到设计画布来构建表单布局
- **实时预览**: 设计过程中可以实时预览表单效果
- **多列布局**: 支持1-4列的灵活布局配置
- **字段属性配置**: 可配置字段的占用列数、标签宽度、对齐方式等

### 2. 字段布局配置
- **占用列数**: 字段可以跨越多列显示
- **标签宽度**: 自定义字段标签的宽度
- **字段宽度**: 设置字段输入框的宽度
- **对齐方式**: 支持左对齐、居中、右对齐
- **排序权重**: 控制字段在表单中的显示顺序
- **自定义样式**: 支持添加CSS类名和内联样式

### 3. 全局样式配置
- **表单列数**: 设置整个表单的列数布局
- **标签宽度**: 全局标签宽度设置
- **字段间距**: 控制字段之间的间距
- **表单宽度**: 设置表单的整体宽度
- **表单对齐**: 表单在页面中的对齐方式
- **布局模式**: 水平布局、垂直布局、内联布局

### 4. 布局模板管理
- **预设模板**: 提供常用的布局模板
- **自定义模板**: 可以保存自定义的布局配置为模板
- **模板导入导出**: 支持模板的导入和导出功能
- **模板分类**: 按业务类型对模板进行分类管理

## 使用方法

### 1. 访问布局配置

在表单规则管理页面中，选择要配置的表单后，可以看到以下配置选项：

1. **基本属性**: 字段的基础配置
2. **布局配置**: 字段的布局相关配置（新增）
3. **验证规则**: 字段的验证规则配置
4. **选项配置**: 下拉框、单选框等的选项配置

### 2. 使用布局设计器

1. 点击"布局设计器"按钮打开可视化设计界面
2. 从左侧字段面板拖拽字段到中间的设计画布
3. 点击画布中的字段可以在右侧配置其属性
4. 使用顶部工具栏可以保存布局、预览效果、重置布局

### 3. 配置字段布局

在字段配置的"布局配置"标签页中，可以设置：

```typescript
{
  colspan: 1,        // 占用列数 (1-4)
  labelWidth: 120,   // 标签宽度 (60-300px)
  width: '100%',     // 字段宽度
  align: 'left',     // 对齐方式
  sortOrder: 1,      // 排序权重
  className: '',     // CSS类名
  style: ''          // 内联样式
}
```

### 4. 动态表单渲染

使用 `DynamicFormRenderer` 组件来渲染配置好的表单：

```vue
<template>
  <DynamicFormRenderer
    :layout-config="layoutConfig"
    :form-data="formData"
    :form-code="formCode"
    @submit="handleSubmit"
    @reset="handleReset"
  />
</template>
```

## 组件说明

### FormLayoutDesigner
可视化布局设计器组件，提供拖拽式表单设计功能。

**Props:**
- `formCode`: 表单代码
- `availableFields`: 可用字段列表

**Events:**
- `save`: 保存布局配置
- `preview`: 预览布局效果

### DynamicFormRenderer
动态表单渲染器，根据配置动态渲染表单。

**Props:**
- `layoutConfig`: 布局配置对象
- `formData`: 表单数据对象
- `formCode`: 表单代码（可选）
- `showActions`: 是否显示操作按钮
- `readonly`: 是否只读模式

**Events:**
- `submit`: 表单提交
- `reset`: 表单重置
- `change`: 表单数据变化

### FormLayoutManager
布局管理器，提供布局设计、字段配置、样式配置、模板管理等功能。

**Props:**
- `formCode`: 表单代码
- `availableFields`: 可用字段列表

**Events:**
- `save`: 保存配置
- `change`: 配置变化

## API接口

### 布局配置相关API

```typescript
// 获取表单布局配置
getFormLayout(formCode: string)

// 保存表单布局配置
saveFormLayout(formCode: string, layoutConfig: any)

// 获取布局模板列表
getLayoutTemplates()

// 保存布局模板
saveLayoutTemplate(template: any)
```

## 配置数据结构

### 布局配置对象

```typescript
interface LayoutConfig {
  formCode: string;
  columns: number;
  globalStyle: {
    labelWidth: number;
    gutter: number;
    formWidth: string;
    formAlign: string;
    layout: string;
  };
  layout: {
    rows: Array<Array<{
      field: FieldMetadata;
      layout: FieldLayoutConfig;
    }>>;
  };
}
```

### 字段布局配置

```typescript
interface FieldLayoutConfig {
  colspan: number;      // 占用列数
  labelWidth: number;   // 标签宽度
  width: string;        // 字段宽度
  align: string;        // 对齐方式
  sortOrder: number;    // 排序权重
  className: string;    // CSS类名
  style: string;        // 内联样式
}
```

## 最佳实践

### 1. 布局设计建议
- 合理使用列数，避免过多列导致在小屏幕上显示问题
- 重要字段放在前面，按业务逻辑分组
- 保持标签宽度一致，提升视觉效果

### 2. 响应式设计
- 系统自动在移动端切换为单列布局
- 可以通过CSS媒体查询进一步优化

### 3. 性能优化
- 大表单建议分页或分步骤显示
- 合理使用字段的显示/隐藏功能

## 示例

查看 `src/views/demo/DynamicFormDemo.vue` 文件，了解完整的使用示例。

## 扩展开发

如需扩展新的字段类型或布局功能，可以：

1. 在 `DynamicFormRenderer` 中添加新的字段组件
2. 在 `FormLayoutDesigner` 中添加新的配置选项
3. 扩展布局配置数据结构
4. 添加相应的API接口

## 注意事项

1. 布局配置会影响表单的验证规则执行
2. 修改布局后建议重新测试表单功能
3. 导入模板时注意检查字段兼容性
4. 自定义CSS样式可能影响响应式布局

## 技术支持

如有问题或建议，请联系开发团队或提交Issue。
