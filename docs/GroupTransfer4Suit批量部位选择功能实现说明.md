# GroupTransfer4Suit 批量部位选择功能实现说明

## 实现背景

参考 TeamGroupCard.vue 中的批量部位选择逻辑，在 GroupTransfer4Suit.vue 中实现了类似的批量部位选择功能，支持在添加大项到套餐时进行部位处理。

## 主要功能

### 1. 批量部位选择状态管理

添加了 `batchPartState` 响应式状态，用于管理批量部位选择的相关数据：

```typescript
const batchPartState = reactive({
  visible: false,
  loading: false,
  itemGroups: [] as Group[], // 需要选择部位的项目列表
  itemPartSelections: new Map<string, {
    options: Array<{label: string, value: string, frequency: number, code: string, name: string}>,
    selectedParts: string[],
    loading: boolean
  }>(), // 每个项目的部位选择状态
  source: '' as 'manual' | 'suit', // 来源：手动添加 或 套餐添加
});
```

### 2. 批量部位选择模态框

添加了完整的批量部位选择模态框，支持：
- 动态标题（单个项目显示"选择检查部位"，多个项目显示"批量选择检查部位"）
- 每个项目独立的部位选择下拉框
- 支持搜索和多选
- 实时显示已选择的部位
- 验证确保每个项目至少选择一个部位

### 3. 修改的 handleAdd 方法

重构了 `handleAdd` 方法，添加了部位选择逻辑：

```typescript
function handleAdd() {
  // 分离需要部位选择的项目和不需要的项目
  const needPartSelectionItems: Group[] = [];
  const addableItems: any[] = [];

  groupTableState.selectedRows.forEach((row) => {
    const canAdd = suitDataSource.value.findIndex((item) => item.groupId === row.id) == -1 || row.chargeItemOnlyFlag == '1';
    
    if (canAdd) {
      // 检查是否需要部位选择
      if (row.hasCheckPart === '1') {
        needPartSelectionItems.push(row);
      } else {
        // 直接添加不需要部位选择的项目
        addableItems.push(/* 项目数据 */);
      }
    }
  });

  // 添加不需要部位选择的项目
  if (addableItems.length > 0) {
    suitDataSource.value.push(...addableItems);
  }

  // 处理需要部位选择的项目
  if (needPartSelectionItems.length > 0) {
    showBatchPartSelector(needPartSelectionItems, 'manual');
  }
}
```

### 4. 核心辅助方法

#### 状态管理方法
- `getItemPartSelection(itemGroupId)` - 获取或创建项目的部位选择状态
- `hasAnyPartSelected()` - 检查是否有任何部位被选择
- `getBatchPartNameById(itemGroupId, partId)` - 获取部位名称
- `getBatchPartOptionById(itemGroupId, partId)` - 获取部位选项完整信息

#### 模态框控制方法
- `showBatchPartSelector(itemGroups, source)` - 显示批量部位选择器
- `closeBatchPartSelector()` - 关闭批量部位选择器

#### 数据加载方法
- `loadItemParts(itemGroupId, keyword?)` - 加载项目的部位选项
- `searchItemParts(itemGroupId, keyword)` - 搜索项目的部位（带防抖）

### 5. 批量确认添加方法

`confirmBatchAddItemsWithParts()` 方法实现了：

1. **验证完整性** - 确保每个项目至少选择一个部位
2. **批量创建记录** - 为每个项目的每个部位创建套餐记录
3. **重复检查** - 避免添加重复的项目-部位组合
4. **错误处理** - 包含回滚机制，确保数据一致性
5. **用户反馈** - 显示添加成功和跳过的数量

```typescript
async function confirmBatchAddItemsWithParts() {
  // 验证每个项目都至少选择了一个部位
  const unselectedItems = batchPartState.itemGroups.filter(item =>
    getItemPartSelection(item.id).selectedParts.length === 0
  );

  if (unselectedItems.length > 0) {
    const itemNames = unselectedItems.map(item => item.name).join('、');
    message.warn(`以下项目还未选择部位：${itemNames}`);
    return;
  }

  // 批量创建记录
  batchPartState.itemGroups.forEach(itemGroup => {
    const selection = getItemPartSelection(itemGroup.id);
    selection.selectedParts.forEach(partId => {
      // 创建套餐项目记录，包含部位信息
      suitDataSource.value.push({
        // ... 项目基本信息
        checkPartId: partId,
        checkPartName: partName,
        checkPartCode: partCode,
      });
    });
  });
}
```

## 用户体验优化

### 1. 智能分流
- 自动区分需要部位选择和不需要部位选择的项目
- 不需要部位选择的项目直接添加，提高效率

### 2. 批量操作
- 支持同时为多个项目选择部位
- 每个项目独立的部位选择，互不干扰

### 3. 实时反馈
- 显示已选择的部位标签
- 动态启用/禁用确认按钮
- 详细的操作结果提示

### 4. 错误处理
- 完善的验证机制
- 自动回滚机制
- 友好的错误提示

## 技术特点

### 1. 状态管理
- 使用 Map 结构高效管理多个项目的状态
- 响应式状态确保界面实时更新

### 2. 性能优化
- 懒加载部位选项（聚焦时才加载）
- 防抖搜索（300ms）
- 及时清理不需要的状态

### 3. 数据一致性
- 前端验证 + 后端事务保护
- 错误时自动回滚
- 重复检查避免数据冲突

## 项目关系处理

### 1. 导入项目关系管理工具

从 `@/utils/itemGroupRelationManager` 导入了完整的项目关系处理工具：

```typescript
import {
  checkItemMutex,
  formatConflictMessage,
  checkItemDependencies,
  formatDependencyMessage,
  getMissingDependencyDetails,
  preloadRelationData,
} from '@/utils/itemGroupRelationManager';
```

### 2. 项目可用性检查

添加了 `isItemGroupAvailable()` 方法，用于检查项目是否可以添加到套餐：

```typescript
function isItemGroupAvailable(itemGroup: Group): { isValid: boolean; errorMessage?: string } {
  // 基本的项目状态检查
  if (!itemGroup.id) {
    return { isValid: false, errorMessage: '项目ID无效' };
  }

  if (!itemGroup.name) {
    return { isValid: false, errorMessage: '项目名称无效' };
  }

  // 套餐中的项目基本都是可用的，这里主要做基础验证
  return { isValid: true };
}
```

### 3. 重复检查逻辑

实现了完整的重复检查逻辑，支持：
- 仅收费项目允许重复添加
- 考虑部位信息的重复检查
- 项目-部位组合的精确重复检查

```typescript
function checkItemGroupDuplicate(existingGroups: any[], newGroup: Group): boolean {
  // 仅收费项目允许重复添加
  if (newGroup.chargeItemOnlyFlag === '1') {
    return false;
  }

  // 根据是否需要部位选择进行不同的重复检查
  if (newGroup.hasCheckPart !== '1') {
    return existingGroups.some(item => item.groupId === newGroup.id);
  }

  // 有部位信息的项目需要检查项目-部位组合
  if (!newGroup.checkPartId) {
    return false;
  }

  return existingGroups.some(item =>
    item.groupId === newGroup.id && item.checkPartId === newGroup.checkPartId
  );
}
```

### 4. 互斥检查

在批量确认添加时进行互斥检查：

```typescript
// 前端互斥检查
const mutexCheck = await checkItemMutex(itemsToAdd, suitDataSource.value);
if (!mutexCheck.isValid) {
  const conflictMsg = formatConflictMessage(mutexCheck.conflicts);
  message.error('项目冲突：\n' + conflictMsg);
  return;
}
```

### 5. 依赖检查

添加项目后自动进行依赖检查：

```typescript
async function checkDependenciesAfterAdd(addedItems: any[]) {
  try {
    const dependencyCheck = await checkItemDependencies(addedItems, suitDataSource.value);

    if (!dependencyCheck.isValid) {
      const missingDependencyDetails = await getMissingDependencyDetails(dependencyCheck.missing);

      if (missingDependencyDetails.length > 0) {
        const projectNames = missingDependencyDetails.map(d => d.name).join('、');
        message.warning({
          content: `检测到依赖项目缺失：${projectNames}\n建议添加相关依赖项目以确保套餐完整性`,
          duration: 10,
        });
      }
    }
  } catch (error) {
    console.error('依赖检查失败:', error);
    message.warning('依赖检查失败，请注意项目依赖关系');
  }
}
```

### 6. 赠送项目处理

实现了完整的赠送项目自动添加功能：

#### 6.1 获取赠送项目

```typescript
async function getAndAddGiftItems(mainItems: any[]): Promise<any[]> {
  try {
    // 获取赠送项目ID列表
    const giftItemIds = await getGiftItems(mainItems);

    if (giftItemIds.length === 0) {
      return [];
    }

    // 获取项目字典，用于查找赠送项目的详细信息
    const allGroups = await getAllGroup({});
    const giftItems: any[] = [];

    for (const giftItemId of giftItemIds) {
      const giftGroup = allGroups.find((group: any) => group.id === giftItemId);

      if (giftGroup) {
        // 为每个主项目创建对应的赠送项目
        for (const mainItem of mainItems) {
          const giftItem = {
            uuid: uuidv4(),
            groupName: `${giftGroup.name}(赠送)`,
            groupId: giftGroup.id,
            // ... 其他字段
            price: 0, // 赠送项目价格为0
            priceAfterDis: 0, // 赠送项目折后价为0
            giftBaseId: mainItem.itemGroupId || mainItem.groupId, // 标记赠送来源
            isGiftItem: true, // 标记为赠送项目
          };
          giftItems.push(giftItem);
        }
      }
    }

    return giftItems;
  } catch (error) {
    console.error('获取赠送项目失败:', error);
    return [];
  }
}
```

#### 6.2 赠送项目特性

- **免费标识**：赠送项目价格自动设置为0
- **名称标识**：项目名称自动添加"(赠送)"后缀
- **来源追踪**：通过giftBaseId字段关联到赠送来源项目
- **关系检查**：赠送项目也参与互斥和依赖检查
- **视觉区分**：在表格中用绿色显示赠送项目

#### 6.3 赠送项目处理流程

```typescript
// 在批量确认添加时
const giftItems = await getAndAddGiftItems(itemsToAdd);
if (giftItems.length > 0) {
  // 对赠送项目进行互斥检查
  const giftMutexCheck = await checkItemMutex(giftItems, suitDataSource.value);
  if (!giftMutexCheck.isValid) {
    // 处理冲突，回滚主项目
    return;
  }

  // 添加赠送项目到套餐
  suitDataSource.value.push(...giftItems);

  // 对赠送项目进行依赖检查
  await checkDependenciesAfterAdd(giftItems);
}
```

## 完整的添加流程

### 1. 直接添加项目（无需部位选择）

```
用户选择项目 → 项目可用性检查 → 重复检查 → 直接添加 → 保存套餐
```

### 2. 批量部位选择流程

```
用户选择项目 → 项目可用性检查 → 重复检查 → 显示批量部位选择器 →
用户选择部位 → 验证完整性 → 互斥检查 → 添加项目 → 依赖检查 → 保存套餐
```

## 后端API集成

### 1. 使用后端API处理附属项目和赠送项目

修改了实现方案，不再使用前端逻辑处理附属项目和赠送项目，而是通过后端API `addItemGroupWithCheckParts` 来处理：

```typescript
// 调用后端API添加项目（包含附属项目和赠送项目的自动处理）
const params = {
  customerRegId: props.suitId, // 套餐场景下使用suitId
  itemGroups: itemGroups,
};

const res = await addItemGroupWithCheckParts(params);
```

### 2. 后端API的优势

- **完整性**：后端已经实现了完整的附属项目和赠送项目处理逻辑
- **一致性**：与 GroupListOfPannel.vue 使用相同的后端逻辑
- **可靠性**：后端处理包含事务保护和完整的验证
- **性能**：减少前端复杂逻辑，提高性能

### 3. 数据同步

添加项目后重新加载套餐数据以获取最新状态：

```typescript
// 重新加载套餐数据以获取最新的项目列表（包含附属项目和赠送项目）
await loadSuitData();

// 进行依赖检查
await checkAllDependencies(true);
```

## 依赖项目UI交互

### 1. 完全按照 GroupListOfPannel.vue 实现

参考 GroupListOfPannel.vue 的依赖项目UI交互，实现了：

#### 1.1 依赖项目提示区域
```vue
<div v-if="missingDependencies.length > 0" class="missing-dependencies-alert">
  <a-alert type="warning" show-icon :closable="false">
    <template #message>
      <div class="missing-dependencies-content">
        <span class="alert-title">检测到缺失的依赖项目</span>
        <div class="missing-projects-list">
          <a-tag
            v-for="dependency in missingDependencies"
            :key="dependency.dependentId"
            color="orange"
          >
            {{ dependency.dependentName }}
          </a-tag>
        </div>
      </div>
    </template>
    <template #action>
      <a-space>
        <a-button type="primary" size="small" @click="handleQuickAddAllDependencies">
          一键添加
        </a-button>
        <a-button size="small" @click="openDependencyQuickAddModal">
          选择添加
        </a-button>
      </a-space>
    </template>
  </a-alert>
</div>
```

#### 1.2 依赖项目快捷添加模态框
```vue
<DependencyQuickAddModal
  ref="dependencyQuickAddModalRef"
  @quick-add="handleDependencyQuickAdd"
  @confirm="handleDependencyConfirm"
  @cancel="handleDependencyCancel"
/>
```

### 2. 依赖检查逻辑

#### 2.1 自动依赖检查
- 套餐数据加载完成后自动进行依赖检查
- 添加项目后自动进行依赖检查
- 使用缓存机制避免频繁检查

#### 2.2 依赖检查方法
```typescript
// 检查所有项目的依赖关系
async function checkAllDependencies(forceCheck = false) {
  // 转换套餐项目为依赖检查格式
  const allItems = suitDataSource.value
    .filter(item => item.addMinusFlag !== -1)
    .map(item => ({
      itemGroupId: item.groupId,
      itemGroupName: item.groupName,
      checkPartId: item.checkPartId || '',
      checkPartName: item.checkPartName || '',
    }));

  // 使用统一的关系管理器检查依赖
  const dependencyCheck = await checkAllItemsDependencies(allItems);

  if (!dependencyCheck.isValid) {
    const mergedDependencies = mergeDependenciesByGroup(dependencyCheck.missing);
    missingDependencies.value = mergedDependencies;
  }
}
```

### 3. 用户交互功能

#### 3.1 一键添加所有依赖项目
```typescript
async function handleQuickAddAllDependencies() {
  // 搜索并添加所有缺失的依赖项目
  const projectsToAdd = [];

  for (const dependency of missingDependencies.value) {
    const searchResult = await getAllGroup({
      name: dependency.dependentName,
      pageNo: 1,
      pageSize: 10,
    });

    const foundProject = searchResult.records?.find(item => item.id === dependency.dependentId);
    if (foundProject) {
      projectsToAdd.push(foundProject);
    }
  }

  await handleAddBatch(projectsToAdd);
}
```

#### 3.2 选择性添加依赖项目
通过 `DependencyQuickAddModal` 组件让用户选择要添加的依赖项目。

## 与 TeamGroupCard.vue 和 GroupListOfPannel.vue 的一致性

本实现确保了与现有组件的完全一致性：

### 1. 批量部位选择
- 完全参考 TeamGroupCard.vue 的实现
- 相同的用户体验和操作流程

### 2. 项目关系处理
- 使用与 GroupListOfPannel.vue 相同的后端API
- 相同的依赖项目UI交互
- 统一的错误处理机制

### 3. 数据处理
- 使用后端API确保数据一致性
- 统一的项目关系处理逻辑
- 完整的事务保护

这样确保了整个系统中项目处理功能的一致性和可维护性，同时提供了最佳的用户体验。
