# 添加项目完整处理流程详解

## 概述

本文档结合实际代码，详细介绍添加项目时互斥检查、依赖检查、附属项目、赠送项目的完整处理逻辑。

## 1. 前端处理流程

### 1.1 入口方法：`confirmAddItemWithParts()`

**位置**：`src/views/reg/GroupListOfPannel.vue:2743`

```javascript
async function confirmAddItemWithParts() {
    // 1. 验证必要条件
    if (!checkPartState.currentItemGroup) {
        message.warn('项目信息丢失，请重新选择');
        closeCheckPartSelector();
        return;
    }

    if (checkPartState.selectedParts.length === 0) {
        message.warn('请至少选择一个检查部位');
        return;
    }
```

**功能**：验证用户选择的项目和部位信息是否完整。

### 1.2 数据拼装阶段

```javascript
    // 2. 前端负责数据拼装
    const itemGroups = [];
    const parentGroupId = uuidv4(); // 生成父组ID，用于关联同一项目的不同部位

    for (const partId of checkPartState.selectedParts) {
        const partName = getPartNameById(partId);
        const partInfo = checkPartState.options.find(opt => opt.value === partId);

        // 2.1 创建临时项目对象用于重复检查
        const tempItem = {
            ...checkPartState.currentItemGroup,
            checkPartId: partId,
            checkPartName: partName
        };

        // 2.2 前端重复检查（包含减项处理）
        const isDuplicate = checkItemGroupDuplicate(regGroupDataSource.value, tempItem);
        if (isDuplicate) {
            message.warn(`${checkPartState.currentItemGroup.name} - ${partName} 已存在`);
            continue;
        }

        // 2.3 使用现有方法生成基础数据
        const baseData = generateCustomerRegItemGroup(checkPartState.currentItemGroup);
        if (!baseData) {
            continue;
        }

        // 2.4 设置部位相关信息
        baseData.checkPartId = partId;
        baseData.checkPartName = partName;
        baseData.checkPartCode = partInfo?.code || '';
        baseData.parentGroupId = parentGroupId;
        baseData.itemGroupName = `${checkPartState.currentItemGroup.name}-${partName}`;

        itemGroups.push(baseData);
    }
```

**功能**：
- 为每个选择的部位创建项目记录
- 进行前端重复检查（包含减项处理逻辑）
- 生成完整的项目数据结构

### 1.3 前端互斥检查

```javascript
    // 3. 前端互斥检查
    const mutexCheck = await checkItemMutex(itemGroups, regGroupDataSource.value);
    if (!mutexCheck.isValid) {
        const conflictMsg = formatConflictMessage(mutexCheck.conflicts);
        message.error('项目冲突：\n' + conflictMsg);
        return;
    }

    if (mutexCheck.warning) {
        message.warning(mutexCheck.warning);
    }
```

**功能**：
- 调用 `checkItemMutex` 进行互斥检查
- 检查新项目与现有项目的冲突
- 检查新项目之间的冲突
- 提供友好的错误提示

#### 互斥检查详细逻辑（`src/utils/itemGroupMutexCheck.js`）

```javascript
export async function checkItemMutex(newItems, existingItems) {
    const conflicts = [];
    
    for (const newItem of newItems) {
        // 获取新项目的互斥关系（带缓存）
        const mutexItems = await getMutexItems(newItem.itemGroupId);
        
        if (mutexItems.length === 0) {
            continue;
        }
        
        // 检查是否与现有项目冲突
        for (const existingItem of existingItems) {
            if (existingItem.addMinusFlag === -1) {
                continue; // 跳过已减项的
            }
            
            // mutexItems 是字符串数组，包含互斥项目的ID
            const isConflict = mutexItems.includes(existingItem.itemGroupId);
            
            if (isConflict) {
                conflicts.push({
                    newItem: { id: newItem.itemGroupId, name: newItem.itemGroupName },
                    existingItem: { id: existingItem.itemGroupId, name: existingItem.itemGroupName },
                    reason: '项目互斥'
                });
            }
        }
        
        // 检查新项目之间是否互斥
        for (const otherNewItem of newItems) {
            if (otherNewItem === newItem) continue;
            
            const isConflict = mutexItems.includes(otherNewItem.itemGroupId);
            if (isConflict) {
                // 添加冲突记录
            }
        }
    }
    
    return {
        isValid: conflicts.length === 0,
        conflicts: conflicts
    };
}
```

### 1.4 发送请求到后端

```javascript
    // 4. 发送请求到后端
    const params = {
        customerRegId: customerRegId.value,
        itemGroups: itemGroups
    };

    const res = await addItemGroupWithCheckParts(params);
```

### 1.5 成功后的处理

```javascript
    if (res.success) {
        const partNames = selectedPartNames.join('、');
        message.success(`成功添加 ${checkPartState.currentItemGroup.name} - ${partNames}`);

        // 5. 刷新列表
        await fetchCustomerRegGroupList(selectedCustomerReg.value.id);

        // 6. 添加项目后进行依赖检查
        await checkDependenciesAfterAdd(itemGroups);

        closeCheckPartSelector();
    }
```

#### 前端依赖检查详细逻辑

```javascript
async function checkDependenciesAfterAdd(addedItems) {
    if (!dependencyChecker.value) {
        console.warn('依赖检查器未初始化');
        return;
    }

    const missingDependencies = [];
    
    for (const item of addedItems) {
        const validation = dependencyChecker.value.checkDependencies(item.itemGroupId);
        if (!validation.valid) {
            missingDependencies.push({
                itemName: item.itemGroupName,
                partName: item.checkPartName,
                missing: validation.missing
            });
        }
    }

    if (missingDependencies.length > 0) {
        // 格式化依赖提示信息
        const messages = missingDependencies.map(dep => {
            const itemDisplay = dep.partName ? `${dep.itemName}-${dep.partName}` : dep.itemName;
            const missingItems = dep.missing.map(m => m.name).join('、');
            return `项目"${itemDisplay}"缺少依赖项目：${missingItems}`;
        });
        
        message.warning({
            content: `检测到依赖项目缺失：\n${messages.join('\n')}\n建议添加相关依赖项目以确保检查流程完整`,
            duration: 8,
        });
    }
}
```

**功能**：
- 友好提醒用户注意依赖关系
- 不阻塞操作流程
- 提供详细的依赖缺失信息

## 2. 后端处理流程

### 2.1 入口方法：`addItemGroupWithCheckParts()`

**位置**：`CustomerRegServiceImpl.java:3280`

```java
@Override
@Transactional(rollbackFor = Exception.class)
public void addItemGroupWithCheckParts(AddItemGroupWithCheckPartsRequest request) {
    // 1. 验证参数
    if (request == null || StringUtils.isEmpty(request.getCustomerRegId())
            || CollectionUtils.isEmpty(request.getItemGroups())) {
        throw new JeecgBootException("参数不能为空");
    }

    // 2. 使用前端传入的项目列表（前端已完成数据拼装）
    List<CustomerRegItemGroup> customerRegItemGroupList = request.getItemGroups();

    // 3. 验证项目列表
    if (CollectionUtils.isEmpty(customerRegItemGroupList)) {
        throw new JeecgBootException("项目列表不能为空");
    }
```

### 2.2 主项目的互斥和依赖验证

```java
    // 4. 验证是否存在互斥项目
    try {
        itemGroupRelationService.checkIsHaveMutexes(customerRegItemGroupList);
    } catch (Exception e) {
        throw new JeecgBootException("项目互斥验证失败：" + e.getMessage());
    }

    // 5. 验证依赖项目
    try {
        itemGroupRelationService.checkDependentGroups(customerRegItemGroupList);
    } catch (Exception e) {
        throw new JeecgBootException("项目依赖验证失败：" + e.getMessage());
    }

    // 6. 批量保存主项目
    customerRegItemGroupService.saveBatch(customerRegItemGroupList);
```

**功能**：
- 后端最终的互斥验证（基于最新数据）
- 后端严格的依赖验证（阻止不合规添加）
- 保存主项目到数据库

### 2.3 附属项目处理

```java
    // 7. 获取附属项目
    List<CustomerRegItemGroup> attachItemGroups = itemGroupRelationService.getAttachGroups(customerRegItemGroupList);
    if (CollectionUtils.isNotEmpty(attachItemGroups)) {
        customerRegItemGroupList.addAll(attachItemGroups);
        
        // 7.1 验证附属项目的互斥关系
        try {
            itemGroupRelationService.checkIsHaveMutexes(attachItemGroups);
        } catch (Exception e) {
            throw new JeecgBootException("附属项目互斥验证失败：" + e.getMessage());
        }
        
        // 7.2 验证附属项目的依赖关系
        try {
            itemGroupRelationService.checkDependentGroups(attachItemGroups);
        } catch (Exception e) {
            throw new JeecgBootException("附属项目依赖验证失败：" + e.getMessage());
        }
        
        // 7.3 保存附属项目
        customerRegItemGroupService.saveBatch(attachItemGroups);
    }
```

**功能**：
- 根据配置自动获取附属项目
- 验证附属项目的互斥和依赖关系
- 保存附属项目

#### 附属项目获取逻辑（`ItemGroupRelationServiceImpl.getAttachGroups()`）

```java
@Override
public List<CustomerRegItemGroup> getAttachGroups(List<CustomerRegItemGroup> addingItemGroups) {
    List<CustomerRegItemGroup> attachItemGroups = new ArrayList<>();
    
    for (CustomerRegItemGroup addingGroup : addingItemGroups) {
        // 查询附属关系配置
        List<ItemGroupRelation> attachRelations = getAttachRelations(addingGroup.getItemGroupId());
        
        for (ItemGroupRelation relation : attachRelations) {
            // 检查部位匹配逻辑
            if (isPartMatched(addingGroup, relation)) {
                // 创建附属项目记录
                CustomerRegItemGroup attachGroup = createAttachGroup(addingGroup, relation);
                attachItemGroups.add(attachGroup);
            }
        }
    }
    
    return attachItemGroups;
}
```

### 2.4 赠送项目处理

```java
    // 8. 获取赠送项目（需要考虑主项目和附属项目的赠送项目）
    List<CustomerRegItemGroup> allItemGroups = new ArrayList<>(customerRegItemGroupList);
    List<CustomerRegItemGroup> giftItemGroups = itemGroupRelationService.getGiftGroups(allItemGroups);
    if (CollectionUtils.isNotEmpty(giftItemGroups)) {
        customerRegItemGroupList.addAll(giftItemGroups);
        
        // 8.1 验证赠送项目的互斥关系
        try {
            itemGroupRelationService.checkIsHaveMutexes(giftItemGroups);
        } catch (Exception e) {
            throw new JeecgBootException("赠送项目互斥验证失败：" + e.getMessage());
        }
        
        // 8.2 验证赠送项目的依赖关系
        try {
            itemGroupRelationService.checkDependentGroups(giftItemGroups);
        } catch (Exception e) {
            throw new JeecgBootException("赠送项目依赖验证失败：" + e.getMessage());
        }
        
        // 8.3 保存赠送项目
        customerRegItemGroupService.saveBatch(giftItemGroups);
    }
```

**功能**：
- 基于主项目和附属项目获取赠送项目
- 验证赠送项目的互斥和依赖关系
- 保存赠送项目

### 2.5 使用频次更新

```java
    // 9. 异步更新使用频次
    Set<String> itemGroupIds = customerRegItemGroupList.stream()
        .map(CustomerRegItemGroup::getItemGroupId)
        .collect(Collectors.toSet());
    Set<String> checkPartIds = customerRegItemGroupList.stream()
        .filter(item -> StringUtils.isNotBlank(item.getCheckPartId()))
        .map(CustomerRegItemGroup::getCheckPartId)
        .collect(Collectors.toSet());
    
    // 异步更新项目使用频次
    updateItemGroupUsageFrequency(itemGroupIds);
    // 异步更新部位使用频次
    updateCheckPartUsageFrequency(checkPartIds);
```

## 3. 完整流程图

```
前端处理流程：
1. 用户选择项目和部位
   ↓
2. 前端数据拼装
   ↓
3. 前端重复检查（包含减项处理）
   ↓
4. 前端互斥预检查（缓存机制）
   ↓
5. 发送请求到后端

后端处理流程：
6. 参数验证
   ↓
7. 主项目互斥验证（严格）
   ↓
8. 主项目依赖验证（严格）
   ↓
9. 保存主项目
   ↓
10. 获取附属项目
    ↓
11. 附属项目互斥验证
    ↓
12. 附属项目依赖验证
    ↓
13. 保存附属项目
    ↓
14. 获取赠送项目（基于主项目+附属项目）
    ↓
15. 赠送项目互斥验证
    ↓
16. 赠送项目依赖验证
    ↓
17. 保存赠送项目
    ↓
18. 更新使用频次
    ↓
19. 返回成功结果

前端后续处理：
20. 刷新项目列表
    ↓
21. 前端依赖检查提醒（友好）
    ↓
22. 完成
```

## 4. 关键特性

### 4.1 双重保护机制
- **前端预检查**：快速响应，提升用户体验
- **后端最终验证**：数据准确，确保安全

### 4.2 多层次验证
- **重复检查**：防止重复添加，包含减项处理
- **互斥检查**：防止冲突项目同时存在
- **依赖检查**：确保依赖关系完整

### 4.3 自动化处理
- **附属项目**：根据配置自动添加
- **赠送项目**：根据配置自动添加
- **部位逻辑**：正确处理部位继承和匹配

### 4.4 用户体验优化
- **友好提示**：清晰的错误信息和操作建议
- **自动恢复**：错误时自动刷新数据
- **性能优化**：缓存机制减少API调用

## 5. 重复检查详细逻辑

### 5.1 前端重复检查（包含减项处理）

**位置**：`src/views/reg/GroupListOfPannel.vue:2580`

```javascript
function checkItemGroupDuplicate(existingGroups: CustomerRegItemGroup[], newGroup: ItemGroup): boolean {
    // 仅收费项目允许重复添加
    if (newGroup.chargeItemOnlyFlag === '1') {
        console.log(`项目 ${newGroup.name} 是仅收费项目，允许重复添加`);
        return false;
    }

    // 分别处理减项和正常项目
    const minusItems = existingGroups.filter(item =>
        item.itemGroupId === newGroup.id &&
        item.addMinusFlag === -1 &&
        item.payStatus !== '退款成功'
    );

    const normalItems = existingGroups.filter(item =>
        item.itemGroupId === newGroup.id &&
        item.addMinusFlag !== -1 &&
        item.payStatus !== '退款成功'
    );

    // 如果项目不需要部位选择
    if (newGroup.hasCheckPart !== '1') {
        // 如果存在减项，提示反减
        if (minusItems.length > 0) {
            console.log(`项目 ${newGroup.name} 存在减项记录，建议进行反减操作`);
            message.warning(`项目"${newGroup.name}"存在减项记录，建议进行反减操作而不是重新添加`);
            return true; // 阻止添加
        }

        // 检查是否存在正常项目
        return normalItems.length > 0;
    }

    // 如果项目需要部位选择
    // 如果存在减项，提示反减
    if (minusItems.length > 0) {
        const minusPartNames = minusItems.map(item => item.checkPartName || '未知部位').join('、');
        console.log(`项目 ${newGroup.name} 存在减项记录（部位：${minusPartNames}），建议进行反减操作`);
        message.warning(`项目"${newGroup.name}"存在减项记录（部位：${minusPartNames}），建议进行反减操作而不是重新添加`);
        return true; // 阻止添加
    }

    // 检查是否已有该项目的正常记录
    if (normalItems.length > 0) {
        const partNames = normalItems.map(item => item.checkPartName || '未知部位').join('、');
        console.log(`项目 ${newGroup.name} 已存在以下部位：${partNames}，建议使用部位选择器添加其他部位`);
        return true;
    }

    return false;
}
```

**关键特性**：
- **耗材处理**：`chargeItemOnlyFlag === '1'` 的项目允许重复
- **减项处理**：存在减项时提示反减而不是重新添加
- **部位逻辑**：正确处理有部位和无部位的项目

## 6. 数据拼装详细逻辑

### 6.1 generateCustomerRegItemGroup 方法

**位置**：`src/views/reg/GroupListOfPannel.vue:2613`

```javascript
function generateCustomerRegItemGroup(itemGroup: ItemGroup): CustomerRegItemGroup | null {
    if (!selectedCustomerReg.value) {
        console.error('No customer registration selected');
        return null;
    }

    const now = new Date();
    const examNo = selectedCustomerReg.value.examNo;

    return {
        id: uuidv4(),
        customerRegId: selectedCustomerReg.value.id,
        itemGroupId: itemGroup.id,
        itemGroupName: itemGroup.name,
        itemGroupCode: itemGroup.code,
        examNo: examNo,

        // 价格信息
        originalPrice: itemGroup.price || 0,
        discountPrice: itemGroup.discountPrice || itemGroup.price || 0,
        actualPrice: itemGroup.actualPrice || itemGroup.discountPrice || itemGroup.price || 0,

        // 部门信息
        deptId: itemGroup.deptId,
        deptName: itemGroup.deptName,

        // 状态信息
        addMinusFlag: 0, // 0: 正常, 1: 加项, -1: 减项
        payStatus: '未支付',
        checkStatus: '未检查',

        // 时间信息
        createTime: now,
        updateTime: now,
        createBy: userStore.userInfo?.username || 'system',
        updateBy: userStore.userInfo?.username || 'system',

        // 其他信息
        remark: '',
        sort: 0
    };
}
```

**功能**：
- 生成完整的项目数据结构
- 设置默认的价格、状态、时间等信息
- 确保数据格式与后端实体类匹配

## 7. 错误处理机制

### 7.1 前端错误处理

```javascript
} catch (error) {
    console.error('Add item with parts error:', error);

    // 处理数据库约束错误（重复项目）
    if (error.message && (error.message.includes('Duplicate') || error.message.includes('已存在') || error.message.includes('重复'))) {
        message.warning('项目可能已被其他用户添加，正在刷新数据...');
        // 自动刷新数据
        await fetchCustomerRegGroupList(selectedCustomerReg.value.id);
        closeCheckPartSelector();
    } else {
        message.error('添加失败: ' + error.message);
    }
} finally {
    checkPartState.loading = false;
}
```

### 7.2 后端错误处理

```java
// 互斥验证失败
try {
    itemGroupRelationService.checkIsHaveMutexes(customerRegItemGroupList);
} catch (Exception e) {
    throw new JeecgBootException("项目互斥验证失败：" + e.getMessage());
}

// 依赖验证失败
try {
    itemGroupRelationService.checkDependentGroups(customerRegItemGroupList);
} catch (Exception e) {
    throw new JeecgBootException("项目依赖验证失败：" + e.getMessage());
}
```

## 8. 性能优化策略

### 8.1 前端缓存机制

```javascript
// 互斥关系缓存（5分钟）
let mutexCache = new Map();
let cacheExpireTime = 0;
const CACHE_DURATION = 5 * 60 * 1000;

async function getMutexItems(itemGroupId) {
    const now = Date.now();

    // 检查缓存
    if (mutexCache.has(itemGroupId) && now < cacheExpireTime) {
        return mutexCache.get(itemGroupId);
    }

    // 从API获取并缓存
    const res = await getRelationGroupsByMainId({ mainId: itemGroupId });
    const relationData = res?.result || res;
    const mutexItems = relationData?.exclusiveGroups || [];

    mutexCache.set(itemGroupId, mutexItems);
    return mutexItems;
}
```

### 8.2 批量处理

```java
// 批量保存主项目
customerRegItemGroupService.saveBatch(customerRegItemGroupList);

// 批量保存附属项目
customerRegItemGroupService.saveBatch(attachItemGroups);

// 批量保存赠送项目
customerRegItemGroupService.saveBatch(giftItemGroups);
```

### 8.3 异步处理

```java
// 异步更新使用频次
CompletableFuture.runAsync(() -> {
    updateItemGroupUsageFrequency(itemGroupIds);
    updateCheckPartUsageFrequency(checkPartIds);
});
```

## 9. 业务场景示例

### 9.1 典型添加流程

```
用户操作：选择"胸部CT"项目，选择"胸部"和"腹部"两个部位

前端处理：
1. 验证选择完整 ✓
2. 生成两条项目记录：
   - 胸部CT-胸部
   - 胸部CT-腹部
3. 重复检查：无重复 ✓
4. 互斥检查：无冲突 ✓
5. 发送到后端

后端处理：
6. 参数验证 ✓
7. 主项目互斥验证 ✓
8. 主项目依赖验证 ✓
9. 保存主项目 ✓
10. 获取附属项目：心电图
11. 附属项目验证 ✓
12. 保存附属项目 ✓
13. 获取赠送项目：血常规
14. 赠送项目验证 ✓
15. 保存赠送项目 ✓

前端后续：
16. 刷新列表 ✓
17. 依赖检查提醒：建议添加血压测量
18. 完成
```

### 9.2 冲突处理示例

```
用户操作：尝试添加"增强CT"，但已有"普通CT"

前端处理：
1. 互斥检查发现冲突
2. 提示：项目冲突："增强CT" 与 "普通CT" 互斥
3. 阻止添加，用户需要先移除普通CT

用户操作：移除普通CT后重新添加增强CT
结果：成功添加
```

这个完整的处理流程确保了添加项目操作的正确性、安全性和用户体验。
