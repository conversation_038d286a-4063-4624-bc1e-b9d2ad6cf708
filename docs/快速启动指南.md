# 表单规则管理系统 - 快速启动指南

## 🚀 快速开始

### 1. 数据库初始化（必须）

#### 步骤1：执行后端数据库脚本
```sql
-- 在MySQL中执行以下脚本
source physicalex-lkd/jeecg-module-physicalex/src/main/resources/db/form_rule_tables.sql
```

#### 步骤2：执行前端菜单脚本
```sql
-- 在MySQL中执行以下脚本
source admin-front/src/views/system/FormRuleManagement_menu_insert.sql
```

### 2. 后端配置（必须）

#### 步骤1：添加配置文件引用
在主配置文件 `application.yml` 中添加：
```yaml
spring:
  profiles:
    include: formrule
```

#### 步骤2：启动后端服务
```bash
# 启动JeecgBoot后端服务
cd physicalex-lkd
mvn spring-boot:run
```

### 3. 前端启动（必须）

```bash
# 启动前端开发服务器
cd admin-front
npm run dev
```

### 4. 访问系统

#### 方式1：通过菜单访问（推荐）
1. 登录系统：`http://localhost:3000`
2. 在左侧菜单中找到"表单规则管理"
3. 点击进入管理页面

#### 方式2：直接URL访问
```
http://localhost:3000/system/formRuleManagementList
```

## 📋 功能验证清单

### ✅ 基础功能验证

#### 1. 后端API测试
```bash
# 测试获取表单规则
curl http://localhost:8080/api/form-rules/customer_reg_form

# 测试获取版本信息
curl http://localhost:8080/api/form-rules/customer_reg_form/version

# 测试获取统计信息
curl http://localhost:8080/api/form-rules/statistics
```

#### 2. 前端页面验证
- [ ] 能够访问表单规则管理页面
- [ ] 能够查看规则列表
- [ ] 能够创建新的表单规则
- [ ] 能够编辑现有规则
- [ ] 能够删除规则
- [ ] 能够启用/禁用规则

#### 3. WebSocket连接测试
```javascript
// 在浏览器控制台中执行
const ws = new WebSocket('ws://localhost:8080/ws/form-rule');
ws.onopen = function() {
    console.log('WebSocket连接成功');
    ws.send(JSON.stringify({
        action: 'SUBSCRIBE',
        data: 'customer_reg_form'
    }));
};
ws.onmessage = function(event) {
    console.log('收到消息:', JSON.parse(event.data));
};
```

### ✅ 高级功能验证

#### 1. 缓存功能
- [ ] 规则配置能够正确缓存
- [ ] 版本变更时缓存能够更新
- [ ] 手动清除缓存功能正常

#### 2. 实时同步
- [ ] 多个浏览器窗口能够实时同步
- [ ] 规则变更能够实时推送
- [ ] 状态变更能够实时通知

#### 3. 表单集成
- [ ] 在客户登记表单中能够应用规则
- [ ] 字段必填状态能够动态控制
- [ ] 字段联动功能正常工作
- [ ] 验证规则能够正确执行

## 🎯 使用示例

### 1. 创建表单规则

#### 步骤1：进入管理页面
访问"表单规则管理"菜单

#### 步骤2：创建新规则
1. 点击"新增"按钮
2. 填写表单信息：
   - 表单代码：`test_form`
   - 表单名称：`测试表单`
   - 描述：`用于测试的表单规则`

#### 步骤3：配置字段规则
添加字段规则：
```json
{
  "fieldCode": "name",
  "fieldName": "姓名",
  "isRequired": true,
  "requiredMessage": "请输入姓名",
  "visible": true,
  "disabled": false,
  "sortOrder": 1
}
```

#### 步骤4：配置联动规则
添加联动规则：
```json
{
  "id": "dep_gender_pregnancy",
  "sourceField": "gender",
  "targetField": "pregnancyFlag",
  "dependencyType": "visible",
  "conditionType": "equals",
  "conditionValue": "女",
  "actionValue": true,
  "priority": 5
}
```

#### 步骤5：保存配置
点击"保存"按钮完成配置

### 2. 在表单中应用规则

#### 在Vue组件中使用：
```vue
<template>
  <a-form>
    <a-form-item 
      label="姓名"
      :rules="getFieldValidationRules('name')"
      v-show="isFieldVisible('name')"
    >
      <a-input 
        v-model:value="formData.name"
        :disabled="isFieldDisabled('name')"
      />
    </a-form-item>
  </a-form>
</template>

<script setup>
import { reactive } from 'vue';
import { useFormRule } from '@/components/FormRule';

const formData = reactive({
  name: '',
  gender: '',
  pregnancyFlag: false
});

const {
  getFieldValidationRules,
  isFieldVisible,
  isFieldDisabled
} = useFormRule({
  formCode: 'test_form',
  formData: formData
});
</script>
```

## 🔧 常见问题解决

### 1. 菜单不显示
**问题**：登录后看不到"表单规则管理"菜单

**解决方案**：
1. 确认已执行菜单插入SQL
2. 检查用户权限是否包含 `form:rule:view`
3. 重新登录系统

### 2. API调用失败
**问题**：前端调用后端API返回404错误

**解决方案**：
1. 确认后端服务已启动
2. 检查后端配置文件是否正确引入
3. 查看后端启动日志是否有错误

### 3. WebSocket连接失败
**问题**：实时通知功能不工作

**解决方案**：
1. 检查防火墙设置
2. 确认WebSocket端口未被占用
3. 查看浏览器控制台错误信息

### 4. 缓存不更新
**问题**：修改规则后前端显示的还是旧数据

**解决方案**：
1. 手动清除浏览器缓存
2. 在管理页面点击"清除缓存"
3. 重启前端开发服务器

### 5. 规则不生效
**问题**：配置的规则在表单中不生效

**解决方案**：
1. 检查表单代码是否正确
2. 确认规则状态为"启用"
3. 查看浏览器控制台是否有错误

## 📞 技术支持

### 日志查看
- **后端日志**：查看Spring Boot应用日志
- **前端日志**：打开浏览器开发者工具查看控制台
- **数据库日志**：查看MySQL慢查询日志

### 调试工具
- **API测试**：使用Postman或curl测试后端接口
- **Vue调试**：安装Vue DevTools浏览器插件
- **网络监控**：使用浏览器Network面板查看请求

### 性能监控
- **后端监控**：访问 `http://localhost:8080/actuator/health`
- **缓存监控**：查看Redis连接状态
- **数据库监控**：检查MySQL连接池状态

## 🎉 恭喜！

如果以上步骤都能正常执行，说明表单规则管理系统已经成功部署并运行！

您现在可以：
- ✅ 通过可视化界面管理表单规则
- ✅ 在任何表单中应用动态规则
- ✅ 享受实时同步和高性能缓存
- ✅ 使用完整的权限控制和审计功能

开始探索更多高级功能，让您的表单管理更加智能和高效！
