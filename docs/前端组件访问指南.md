# 表单规则管理系统 - 前端组件访问指南

## 概述

表单规则管理系统的前端组件已经完整实现，包括管理界面、配置组件和集成示例。本文档详细说明如何访问和使用这些组件。

## 前端组件位置

### 1. 核心组件位置
```
src/components/FormRule/
├── FormRuleEngine.ts              # 表单规则引擎
├── FormRuleCache.ts               # 前端缓存管理
├── FormRuleComposer.ts            # Vue组合式API
├── FormRuleConfigModal.vue        # 规则配置弹窗
├── FieldRuleEditModal.vue         # 字段规则编辑
├── DependencyRuleEditModal.vue    # 联动规则编辑
├── FormRuleAPI.ts                 # API接口定义
└── types.ts                       # 类型定义
```

### 2. 管理页面位置
```
src/views/system/
└── FormRuleManagement.vue         # 表单规则管理页面
```

### 3. 集成示例位置
```
src/views/reg/components/
└── CustomerRegFormOfPannelEnhanced.vue  # 增强版客户登记表单
```

### 4. 路由配置
```
src/router/routes/modules/
└── system.ts                      # 系统管理路由配置
```

### 5. 国际化配置
```
src/locales/lang/zh-CN/routes/
└── system.ts                      # 中文翻译配置
```

## 访问方式

### 1. 通过菜单访问

#### 步骤1：执行菜单SQL
首先需要在数据库中执行菜单插入SQL：
```sql
-- 执行文件：src/views/system/FormRuleManagement_menu_insert.sql
-- 这将在系统中创建"表单规则管理"菜单项
```

#### 步骤2：分配权限
确保当前用户具有以下权限：
- `form:rule:view` - 查看表单规则
- `form:rule:create` - 创建表单规则
- `form:rule:update` - 更新表单规则
- `form:rule:delete` - 删除表单规则
- `form:rule:config` - 配置表单规则

#### 步骤3：访问菜单
启动前端应用后，在左侧菜单中找到"表单规则管理"菜单项，点击即可访问。

### 2. 直接URL访问

```
http://localhost:3000/system/formRuleManagementList
```

### 3. 在现有表单中集成

#### 在客户登记表单中添加规则配置按钮：

```vue
<template>
  <!-- 在表单工具栏中添加配置按钮 -->
  <a-space>
    <a-button type="primary" @click="openRuleConfig">
      <SettingOutlined />
      配置表单规则
    </a-button>
  </a-space>

  <!-- 规则配置弹窗 -->
  <FormRuleConfigModal
    v-model:visible="ruleConfigVisible"
    :form-code="FORM_CODE"
    :form-name="FORM_NAME"
    @save="handleRuleConfigSave"
  />
</template>

<script setup>
import { ref } from 'vue';
import { FormRuleConfigModal } from '@/components/FormRule';
import { SettingOutlined } from '@ant-design/icons-vue';

const FORM_CODE = 'customer_reg_form';
const FORM_NAME = '客户登记表单';
const ruleConfigVisible = ref(false);

function openRuleConfig() {
  ruleConfigVisible.value = true;
}

function handleRuleConfigSave() {
  // 规则保存后的处理逻辑
  console.log('表单规则已保存');
}
</script>
```

## 功能使用指南

### 1. 表单规则管理页面

#### 主要功能：
- **查看规则列表**：显示所有表单的规则配置
- **创建新规则**：为新表单创建规则配置
- **编辑规则**：修改现有表单的规则
- **删除规则**：删除不需要的规则配置
- **启用/禁用**：控制规则的生效状态
- **复制规则**：将一个表单的规则复制到另一个表单
- **查看统计**：查看规则使用统计信息

#### 操作步骤：
1. 点击"新增"按钮创建新的表单规则
2. 填写表单代码、名称和描述
3. 配置字段规则（必填、验证、可见性等）
4. 配置联动规则（字段间的依赖关系）
5. 保存配置

### 2. 字段规则配置

#### 支持的规则类型：
- **必填控制**：设置字段是否必填
- **可见性控制**：控制字段是否显示
- **禁用控制**：控制字段是否可编辑
- **验证规则**：设置字段的验证逻辑
- **排序控制**：设置字段的显示顺序

#### 配置示例：
```javascript
{
  fieldCode: 'name',
  fieldName: '姓名',
  isRequired: true,
  requiredMessage: '请输入姓名',
  validationRules: [
    {
      type: 'minLength',
      value: 2,
      message: '姓名至少2个字符'
    }
  ],
  visible: true,
  disabled: false,
  sortOrder: 1
}
```

### 3. 联动规则配置

#### 支持的联动类型：
- **必填联动**：根据其他字段值控制必填状态
- **可见联动**：根据其他字段值控制显示状态
- **禁用联动**：根据其他字段值控制禁用状态

#### 支持的条件类型：
- **等于**：字段值等于指定值
- **不等于**：字段值不等于指定值
- **包含**：字段值包含指定值
- **大于**：字段值大于指定值
- **小于**：字段值小于指定值
- **为空**：字段值为空
- **不为空**：字段值不为空

#### 配置示例：
```javascript
{
  id: 'dep_gender_pregnancy',
  sourceField: 'gender',
  targetField: 'pregnancyFlag',
  dependencyType: 'visible',
  conditionType: 'equals',
  conditionValue: '女',
  actionValue: true,
  priority: 5
}
```

### 4. 在表单中使用规则

#### 使用组合式API：
```vue
<script setup>
import { useFormRule } from '@/components/FormRule';

const formData = reactive({
  name: '',
  gender: '',
  pregnancyFlag: false
});

const {
  getFieldValidationRules,
  isFieldVisible,
  isFieldDisabled,
  isFieldRequired
} = useFormRule({
  formCode: 'customer_reg_form',
  formData: formData
});
</script>

<template>
  <!-- 应用规则到表单字段 -->
  <a-form-item 
    label="姓名"
    :rules="getFieldValidationRules('name')"
    v-show="isFieldVisible('name')"
  >
    <a-input 
      v-model:value="formData.name"
      :disabled="isFieldDisabled('name')"
      :placeholder="isFieldRequired('name') ? '请输入姓名*' : '请输入姓名'"
    />
  </a-form-item>

  <a-form-item 
    label="是否备孕"
    v-show="isFieldVisible('pregnancyFlag')"
  >
    <a-switch 
      v-model:checked="formData.pregnancyFlag"
      :disabled="isFieldDisabled('pregnancyFlag')"
    />
  </a-form-item>
</template>
```

## 开发调试

### 1. 本地开发
```bash
# 启动前端开发服务器
npm run dev

# 访问地址
http://localhost:3000
```

### 2. 调试工具
- **浏览器开发者工具**：查看网络请求和控制台日志
- **Vue DevTools**：调试Vue组件状态
- **Redux DevTools**：查看状态管理

### 3. 常见问题排查

#### 菜单不显示
- 检查是否执行了菜单插入SQL
- 确认用户是否有相应权限
- 检查路由配置是否正确

#### 规则不生效
- 检查后端API是否正常
- 确认表单代码是否正确
- 查看浏览器控制台错误信息

#### 缓存问题
- 清除浏览器缓存
- 重启前端开发服务器
- 检查LocalForage存储

## 扩展开发

### 1. 添加新的验证规则类型
在`FormRuleEngine.ts`中添加新的验证逻辑：

```typescript
// 添加自定义验证规则
const customValidators = {
  phoneNumber: (value: string) => {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(value);
  }
};
```

### 2. 添加新的联动类型
在`FormRuleEngine.ts`中扩展联动处理逻辑：

```typescript
// 添加新的联动类型
function handleCustomDependency(rule: DependencyRule, formData: any) {
  // 自定义联动逻辑
}
```

### 3. 集成到其他表单
参考`CustomerRegFormOfPannelEnhanced.vue`的实现方式，将规则引擎集成到其他表单中。

## 总结

表单规则管理系统提供了完整的前端解决方案，通过可视化界面可以轻松配置和管理表单规则。系统支持动态必填、字段联动、验证规则等功能，大大提高了表单开发和维护的效率。

主要访问方式：
1. **管理界面**：通过系统菜单访问表单规则管理页面
2. **配置弹窗**：在现有表单中添加规则配置按钮
3. **API集成**：通过组合式API在表单中应用规则

系统具有良好的扩展性，可以根据业务需求添加新的规则类型和联动逻辑。
