# 体检管理系统环境变量配置示例

# 是否打开mock
VITE_USE_MOCK = true

# 发布路径
VITE_PUBLIC_PATH = /

# 网站标题
VITE_GLOB_APP_TITLE = 体检管理系统

# 简称，用于配置文件名字 不要出现空格、数字开头等特殊字符
VITE_GLOB_APP_SHORT_NAME = 体检管理系统

# 后台接口全路径地址(必填)
VITE_GLOB_DOMAIN_URL=http://localhost:8090/jeecgboot

# 后台接口父地址(必填)
VITE_GLOB_API_URL=/jeecgboot

# 接口前缀
VITE_GLOB_API_URL_PREFIX=

# 单点登录服务端地址
VITE_GLOB_APP_CAS_BASE_URL=http://cas.test.com:8443/cas

# 是否开启单点登录
VITE_GLOB_APP_OPEN_SSO = false

# 开启微前端模式
VITE_GLOB_APP_OPEN_QIANKUN=false

# 文件预览地址
VITE_GLOB_ONLINE_VIEW_URL=http://physicalex.yingyangyun.cn/jeecgboot

# ==================== 文件存储配置 ====================

# 文件上传的类型: aliyun, minio, local
VITE_GLOB_UPLOAD_TYPE=minio

# ==================== MinIO配置方案 ====================

# 方案一：单一URL配置（传统方式）
# 系统会自动检测网络环境并尝试连接
VITE_GLOB_MINIO_URL=http://************:9000

# 方案二：多URL配置（推荐）
# 支持多个URL，用逗号分隔，系统会自动选择可用的URL
# VITE_GLOB_MINIO_URL=http://************:9000,https://minio.example.com

# 方案三：明确指定内外网地址（最佳实践）
# 内网MinIO地址（局域网访问）
VITE_GLOB_MINIO_INTERNAL_URL=http://************:9000

# 外网MinIO地址（公网访问，通过Nginx代理）
VITE_GLOB_MINIO_EXTERNAL_URL=https://minio.example.com

# ==================== 网络环境配置说明 ====================

# 内网环境示例：
# - 直连MinIO服务：http://************:9000
# - 内网域名：http://minio.internal.com
# - 本地开发：http://localhost:9000

# 外网环境示例：
# - 通过Nginx代理：https://files.example.com/minio
# - CDN加速：https://cdn.example.com/files
# - 云服务商：https://oss.aliyuncs.com

# ==================== 高级配置 ====================

# 跨域代理配置（开发环境）
VITE_PROXY = [["/jeecgboot","http://localhost:8090/jeecgboot"],["/upload","http://localhost:3300/upload"]]

# 微前端qiankun应用配置
VITE_APP_SUB_jeecg-app-1 = '//localhost:8092'

# ==================== 生产环境配置示例 ====================

# 生产环境通常使用HTTPS和域名
# VITE_GLOB_DOMAIN_URL=https://api.example.com/jeecgboot
# VITE_GLOB_MINIO_INTERNAL_URL=http://*************:9000
# VITE_GLOB_MINIO_EXTERNAL_URL=https://files.example.com/minio

# ==================== 配置说明 ====================

# 1. 网络环境自动检测：
#    系统会自动检测客户端网络环境，优先使用内网地址，
#    如果内网不通则自动切换到外网地址。

# 2. URL配置优先级：
#    VITE_GLOB_MINIO_INTERNAL_URL + VITE_GLOB_MINIO_EXTERNAL_URL > 
#    VITE_GLOB_MINIO_URL（多URL） > 
#    VITE_GLOB_MINIO_URL（单URL）

# 3. 连通性测试：
#    系统会定期测试URL连通性，确保使用最佳可用地址。

# 4. 缓存机制：
#    URL连通性测试结果会被缓存，避免频繁网络请求。

# 5. 错误处理：
#    如果所有URL都不可用，系统会回退到配置的默认URL。
