import type { Dayjs } from 'dayjs';
export type RangeValue = [Dayjs, Dayjs];
/**表格列的key*/
export type Key = string | number;

export interface Depart {
  id: string;
  departName: string;
}

/** 检查部位字典 */
export interface CheckPartDict {
  id: string;
  code?: string; // 检查部位编码（对应现有字段）
  name: string; // 检查部位名称（对应现有字段）
  helpChar?: string;
  category?: string;
  sortOrder?: number;
  enableFlag?: string;
  remark?: string;
  frequency?: number; // 使用频次（非持久化字段）
}
export interface ItemGroup {
  id: string;
  sort: number;
  name: string;
  helpChar: string;
  hisName: string;
  hasCheckPart?: string; // 是否有检查部位(1-是,0-否)
  hisCode: string;
  platName: string;
  platCode: string;
  sexLimit: string;
  minAge: number;
  maxAge: number;
  minDiscountRate: number;
  price: number;
  costPrice: number;
  departmentId: string;
  departmentName: string;
  departmentCode: string;
  department: string;
  feeType: string;
  capacityPerDay: number;
  combineSummaryFormat: string;
  normalSummaryFormat: string;
  groupApplyFlag: number;
  chargeItemOnlyFlag: number;
  sysItemFlag: number;
  itemSumableFlag: number;
  itemNormalvalSumableFlag: number;
  privacyFlag: number;
  drawBloodFlag: number;
  fastsFlag: number;
  gynecologicalFlag: number;
  pregnancyFlag: number;
  breakfastFlag: number;
  vipFlag: number;
  outwardDeliveryFlag: number;
  enableFlag: number;
  inquireVoluntarilyFlag: number;
  statCount: number;
  notice: string;
  intro: string;
  remark: string;
  updateTime: Date;
  updateBy: string;
  createBy: string;
  createTime: Date;
  delFlag: number;
  classCode: string;
  // 套餐相关字段
  priceAfterDisOfSuit: number;
  priceDisDiffAmountOfSuit: number;
  minDiscountRateOfSuit: number;
  disRateOfSuit: number;
  // 部位相关字段（套餐中的项目可能包含预设部位）
  checkPartId?: string;
  checkPartName?: string;
  checkPartCode?: string;
  clinicalType: string;
  clinicalTypeCode: string;
  unit: string;
  reportPicAttachable: string;
  itemList: ItemInfo[];
  isRecommend: boolean;
  mustCheckAdded?: boolean;
}

export interface ItemStandard {
  id: string;
  itemId: string;
  departmentId: string;
  enableFlag: number;
  sort: number;
  helpChar: string;
  wubiChar: string;
  diseaseType: string;
  diseaseSeverity: string;
  severityDegree: string;
  abnormalFlag: string;
  symbo: string;
  symboChar: string;
  conclusion: string;
  minVal: string;
  maxVal: string;
  sexLlimit: string;
  minAge: number;
  maxAge: number;
  defaultFlag: number;
  frequency: number;
  updateTime: Date;
  createTime: Date;
  createBy: string;
  updateBy: string;
  operator: string;
  expression: string;
  groovyExpression: string;
  baseOn: string;
}

export interface ItemInfo {
  id: string;
  departmentId: string;
  departmentName: string;
  departmentCode: string;
  sort: number;
  name: string;
  helpChar: string;
  hisCode: string;
  hisName: string;
  platName: string;
  platCode: string;
  wubiChar: string;
  sex: string;
  pregnancyFlag: string;
  minAge: number;
  maxAge: number;
  itemType: string;
  clinicalType: string;
  unit: string;
  summaryFormat: string;
  decimalPlaces: number;
  mutuallyExclusiveItem: string;
  notice: string;
  remark: string;
  formula: string;
  limitWord: string;
  enableFlag: number;
  chargeOnlyFlag: number;
  printReportFlag: number;
  complexDiagFlag: number;
  picItemFlag: number;
  requiredFlag: number;
  sumableFlag: number;
  sumableNormalvalFlag: number;
  sumableBoolFlag: number;
  updateTime: Date;
  updateBy: string;
  createTy: string;
  createTime: Date;
  normalRef: string;
  delFlag: number;
  itemResult: ICustomerRegItemResult;
  itemStandardList: ItemStandard[];
  normalStandard: string;
  matchedStandard: ItemStandard;
  checkStatus: string;
  normalDefaultValue: string;
  groupId: string;
  seq: number;
}

export interface ItemSuit {
  id: string;
  sort: number;
  name: string;
  helpChar: string;
  wubiChar: string;
  sexLimit: string;
  minAge: number;
  maxAge: number;
  price: number;
  costPrice: number;
  diffPrice: number;
  examinationType: string;
  suitType: string;
  healthCertIndustry: string;
  adjustType: string;
  updateBy: string;
  riskFactor: string;
  questId: string;
  job: string;
  pregnancyFlag: number;
  notice: string;
  remark: string;
  enableFlag: number;
  updateTime: Date;
  createBy: string;
  createTime: Date;
  delFlag: string;
  categoryId: string;
  suitPicture: string;
  pubAvailable: string;
}

/** 登记项目组合*/
export interface CustomerRegItemGroup {
  uuid?: string;
  id?: string;
  examNo?: string;
  customerRegId: string;
  itemGroupId: string;
  itemGroupName: string;
  payStatus: string;
  hisCode: string;
  hisName: string;
  platCode: string;
  platName: string;
  departmentId: string;
  departmentName: string;
  type: string;
  itemSuitId?: string | null;
  itemSuitName?: string | null;
  addMinusFlag: number;
  price: number;
  disRate: number;
  priceAfterDis: number;
  payerType: string;
  actionType?: string;
  itemList?: ItemInfo[];
  checkStatus?: string;
  abandonFlag?: number;
  statusStatList?: Recordable[];
  checkStatusColor?: string;
  matchedStandard?: ItemStandard;
  minDiscountRate: number;
  priceDisDiffAmount: number;
  minPriceAfterDis?: number;
  availableAmount4Dis?: number;
  classCode?: string;
  departmentCode: string;
  reportPics?: string[];
  lockByRefund?: string;
  comEquipmentTip?: string;
  comEquipmentStatus?: string;
  teamId?: string;
  firstCheckAfterPayFlag?: string;
  attachGroupIds?: string[];
  checkPartId?: string; // 检查部位ID
  checkPartName?: string; // 检查部位名称
  checkPartCode?: string; // 检查部位名称
  parentGroupId?: string; // 父项目组ID（用于关联同一项目的不同部位）
}

/** 体检单位分组项目组合*/
export interface CompanyTeamItemGroup {
  companyRegId: string;
  teamId: string;
  itemGroupId: string;
  itemGroupName: string;
  hisCode: string;
  hisName: string;
  platCode: string;
  platName: string;
  departmentId: string;
  departmentName: string;
  itemGroupCategory: string;
  itemSuitId?: string;
  itemSuitName?: string;
  addMinusFlag: number;
  price: number;
  disRate: number;
  priceAfterDis: number;
  payerType: string;
  minDiscountRate: number;
  priceDisDiffAmount: number;
  minPriceAfterDis?: number;
  availableAmount4Dis?: number;
  classCode: string;
  departmentCode: string;
  checkPartId?: string;
  checkPartName?: string;
  checkPartCode?: string;
}
export interface CompanyReg {
  id: string;
  companyId: string;
  companyName: string;
  regName: string;
  helpChar: string;
  examType: string;
  personCategory: string;
  linkMan: string;
  startCheckDate: Date;
  endCheckDate: Date;
  personCount: number;
  serviceManager: string;
  checkoutStatus: number;
  lockStatus: number;
  createBy: string;
  companyReportNo: string;
  companyReportDate: Date;
  according: string;
  opinion: string;
  remark: string;
  updateTime: Date;
  updateBy: string;
  cnameDisplayInReport: string;
  printGuidance: string;
  printApply: string;
  teamPrice: number;
}
export interface CompanyTeam {
  id: string;
  teamNum: string;
  companyRegId: string;
  post: string;
  name: string;
  helpChar: string;
  examCategory: string;
  sexLimit: string;
  minAge: number;
  maxAge: number;
  examPlace: string;
  payerType: string;
  addItemPayerType: string;
  teamPrice: number;
  teamDiscountPrice: number;
  teamDiscountRate: number;
  lockStatus: number;
  risks: string;
  workShop: string;
  workType: string;
  pregnancyFlag: number;
  healthManageFlag: number;
  remark: string;
  updateTime: Date;
  updateBy: string;
  createTy: string;
  createTime: Date;
  limitAmount: number;
  itemGroupList: CompanyTeamItemGroup[];
  companyReg: CompanyReg;
  companyTeamItemGroupList: CompanyTeamItemGroup[];
  companyTeamItemGroupNames: string;
  allowTransferAnother: string;
  allowTransferCard: string;
}
/** 体检项目组合*/
export interface Group {
  id: string;
  name: string;
  departmentId: string;
  departmentName: string;
  price: number;
  costPrice: number;
  helpChar: string;
  type?: string;
  checkStatus?: number;
  itemList?: ItemInfo[];
  abandonFlag?: number;
  minDiscountRate: number;
  hisCode: string;
  hisName: string;
  platCode: string;
  platName: string;
  disRateOfSuit?: number;
  priceAfterDisOfSuit?: number;
  minDiscountRateOfSuit?: number;
  priceDisDiffAmountOfSuit?: number;
  itemSumableFlag?: string;
  itemNormalvalSumableFlag?: string;
  classCode: string;
  departmentCode: string;
}

/** 体检项目套餐*/
export interface Suit {
  uuid?: string;
  groupId: string;
  groupName: string;
  departmentName: string;
  departmentId: string;
  type: string;
  disRate: number;
  price: number;
  priceAfterDis: number;
  group?: Group;
  helpChar?: string;
  minDiscountRate: number;
  priceDisDiffAmount: number;
}

/**体检项目*/
/*export interface ItemInfo {
  id?: string;
  departmentId: string;
  sort: number;
  name: string;
  helpChar: string;
  hisCode?: string;
  hisName?: string;
  wubiChar?: string;
  sex: string;
  minAge: number;
  maxAge: number;
  itemType: string;
  clinicalType: string;
  unit: string;
  summaryFormat: string;
  decimalPlaces: number;
  mutuallyExclusiveItem: string;
  notice: string;
  remark: string;
  formula?: string;
  limitWord?: string;
  enableFlag: number;
  chargeOnlyFlag: number;
  printReportFlag: number;
  complexDiagFlag: number;
  picItemFlag: number;
  requiredFlag: number;
  sumableFlag: number;
  sumableNormalvalFlag: number;
  sumableBoolFlag: number;
  updateTime: Date;
  updateBy: string;
  createTy: string;
  createTime: Date;
  delFlag: number;
  itemResult: ICustomerRegItemResult; // 这里的类型需要根据实际情况进行修改
  normalStandard?: string;
  normalRef: string;
  departmentCode: string;
}*/

/** 体检项目结果*/
export interface ICustomerRegItemResult {
  id?: string;
  createBy?: string;
  createTime?: Date;
  updateBy?: string;
  updateTime?: Date;
  sysOrgCode?: string;
  customerRegId: string;
  archivesNum: string;
  idCard: string;
  itemGroupId: string;
  itemGroupName: string;
  itemId?: string;
  itemName: string;
  itemCode?: string;
  valueType: string;
  value?: string | number | null;
  valueSource: string;
  doctorId: string;
  doctorName: string;
  pic: string[];
  instrument?: string;
  unit: string;
  valueIndicator?: string;
  valueRefRange?: string;
  checkConclusion?: string;
  checkPurpose?: string;
  checkParts?: string;
  checkObservations?: string;
  abandonFlag: number;
  abnormalFlag: string;
  abnormalFlagDesc: string;
  abnormalSymbol: string;
  criticalFlag: string;
  criticalDegree: string;
  departmentId: string;
  groupHisCode?: string;
  groupHisName?: string;
  itemHisCode?: string;
  itemHisName?: string;
  abnormalFlagManual?: string;
  abnormalFlagDescManual?: string;
  sortNo?: number;
  examNo?: string;
  seq?: number;
  customerRegItemGroupId?: string;
  checkPartCode?: string;
  checkPartName?: string;

}

/**科室-组合树*/
interface DepartGroupTree {
  name: string;
  id: string;
  type: string;
  retrieveStatus: string;
  checkStatus: string;
  abandonStatus: string;
  children: DepartGroupTree[];
}

/**单位预约*/
export interface ICompanyReg {
  id?: string;
  companyId: string;
  regName: string;
  helpChar: string;
  examType: string;
  personCategory: string;
  linkMan: string;
  startCheckDate: Date;
  endCheckDate: Date;
  personCount: number;
  serviceManager: string;
  checkoutStatus: number;
  lockStatus: number;
  createBy: string;
  companyReportNo: string;
  companyReportDate: Date;
  according: string;
  opinion: string;
  remark: string;
  updateTime: Date;
  updateBy: string;
  companyName: string;
  printGuidance: string;
  printApply: string;
}

/**体检登记*/
export interface ICustomerReg {
  id: string;
  examNo: string;
  status: string;
  customerAvatar: string;
  customerId: string;
  customerRegNum: string;
  hisPid: string;
  hisInpatientId: string;
  name: string;
  gender: string;
  age: number;
  ageUnit: string;
  birthday: Date;
  phone: string;
  marriageStatus: string;
  nation: string;
  bloodType: string;
  cardType: string;
  idCard: string;
  examCardNo: string;
  medicalCardNo: string;
  healthNo: string;
  address: string;
  postCode: string;
  email: string;
  customerCategory: string;
  examCategory: string;
  teamId: string;
  teamName: string;
  bookingDate: Date;
  companyDeptId: string;
  companyDeptName: string;
  country: string;
  industry: string;
  workNo: string;
  career: string;
  jobStatus: string;
  workType: string;
  workType_dictText?: string;
  workShop: string;
  riskFactor: string;
  workYears: string;
  workMonths: string;
  riskYears: string;
  riskMonths: string;
  reExamStatus: number;
  reExamRemark: string;
  monitoringType: string;
  occIrradiation: string;
  recipeTitle: string;
  eduLevel: string;
  introducer: string;
  remark: string;
  companyRegId: string;
  companyRegName: string;
  checkDate: Date;
  creatorBy: string;
  checkState: string;
  infoSource: number;
  paymentState: string;
  operId: string;
  operName: string;
  operTime: Date;
  auditDate: Date;
  originCustomer: string;
  supplyFlag: number;
  prePayFlag: number;
  webQueryCode: string;
  companyName: string;
  companyId: string;
  createTime: Date;
  regTime: Date;
  guidancePrintTimes: number;
  serialNo: number;
  itemGroupList: any[]; // 这里的类型需要根据实际情况进行修改
  companyTeam: any; // 这里的类型需要根据实际情况进行修改
  companyReg?: any; // 这里的类型需要根据实际情况进行修改
  delFlag: string;
  archivesNum: string;
  retrieveImg: string;
  retrieveStatus: string;
  totalPrice?: number;
  payedAmount?: number;
  remainAmount?: number;
  classCode: string;
  personCompanyName?: string;
  summaryStatus?: string;
  reportEditLockFlag?: string;
  reportEditLockBy?: string;
  reportEditLockTime?: string;
  reportEditLocker?: string;
  pregnancyFlag?: string;
  itemGoups?: CustomerRegItemGroup[];
  limitAmount?: number;
  reportTemplateId?: string;
}

interface IDiagnosisComplex {
  id: string;
  departmentId: string;
  departmentName: string;
  name: string;
  genderLimit: string;
  minAge: number;
  maxAge: number;
  marriageLimit: string;
  enableFlag: number;
  formula: string;
  createBy: string;
  creatTime: Date;
  updateBy: string;
  updateTime: Date;
  itemRules: IDiagnosisComplexItemRule[];
}

interface IDiagnosisComplexItemRule {
  itemId: string;
  itemName: string;
  itemType: number;
  fieldName: string;
  fieldType: string;
  fieldUnit: string;
  fieldValue: string;
  operator: string;
  title: string;
}

interface CustomerRegCriticalItem {
  id: string;
  createBy: string;
  createTime: Date;
  updateBy: string;
  updateTime: Date;
  sysOrgCode: string;
  customerRegId: string;
  criticalId: string;
  triggerType: string;
  diagnosisComplexId: string;
  complexItemResults: any[]; // Consider replacing 'any' with a more specific type
  itemGroupId: string;
  itemGroupName: string;
  itemId: string;
  itemName: string;
  itemResultId: string;
  itemValue: string;
  itemStandardId: string;
  source: string;
  severityDegree: string;
  advice: string;
  confirmStatus: string;
  confirmTime: Date;
  confirmBy: string;
  notifyStatus: string;
  notifyTime: Date;
  notifyBy: string;
  postVisitTime: Date;
  postVisitStatus: string;
  postVistResult: string;
  postVisitBy: string;
  baseOn: string;
  checkPartName: string;
}

export interface CustomerRegDepartTip {
  id: string;
  createBy: string;
  creator: string;
  departmentName: string;
  createTime: Date;
  updateBy: string;
  updateTime: Date;
  sysOrgCode: string;
  content: string;
  departmentId: string;
  customerRegId: string;
  timeTip?: string;
}

export interface ItemDict {
  id: string;
  seq: number;
  dictText: string;
  helpChar: string;
  diagnose: string;
  diagnosisClass: string;
  degree: string;
  defaultFlag: string;
  itemId: string;
  departmentId: string;
  useCount: number;
  createBy: string;
  createTime: Date;
  updateBy: string;
  updateTime: Date;
}

export interface SummaryAdvice {
  id: string;
  sort: string;
  riskFactor: string;
  departmentId: string;
  sexLimit: string;
  keywords: string;
  helpChar: string;
  groovyExpression: string;
  departmentName: string;
  marriageLimit: string;
  minAge: number;
  maxAge: number;
  jobCategory: string;
  diseaseSeverity: string;
  diseaseType: string;
  adviceContent: string;
  teamHiddenFlag: number;
  idcCode: string;
  wmIll: string;
  followUpPeriod: number;
  departmentAdvice: string;
  teamAdvice: string;
  medicalAdvice: string;
  dietAdvice: string;
  healthAdvice: string;
  reviewPeriod: number;
  positiveFlag: number;
  lifeRelatedFlag: number;
  createTime: Date;
  createBy: string;
}

export interface CustomerRegDepartSummary {
  id: string;
  customerRegId: string;
  departmentId: string;
  characterSummary: string;
  diagnosisSummary: string;
  departmentName: string;
  createBy: string;
  creatorName: string;
  checkDate: Date;
  checkBy: string;
  checkerName: string;
  auditBy: string;
  auditorName: string;
  auditTime: Date;
  createTime: Date;
  updateTime: Date;
  updateBy: string;
}

export interface AdviceBean {
  seq: number;
  name: string;
  content: string;
  chk: boolean;
  source?: string;
  key: string;
  text?: string;
  diagnosticCriteria?: string;
  valid?: boolean; // 标记建议是否完整
  // 审阅相关字段
  reviewInfo?: {
    hasReview: boolean;
    reviewStatus: 'pending' | 'reviewed' | 'corrected';
    annotations: ReviewAnnotation[];
    correctedContent?: string;
    reviewTime?: string;
    reviewer?: string;
    reviewComment?: string;
  };
}

export interface CustomerRegSummary {
  id: string;
  customerRegId: string;
  characterSummary: string;
  summaryJson?: AdviceBean[];
  diagnosisSummary: string;
  advice: string;
  createTime: Date;
  createBy: string;
  creatorName: string;
  updateBy: string;
  updator: string;
  updateTime: Date;
  confirmTime: Date;
  auditeBy: string;
  auditorName: string;
  preAuditor: string;
  preAuditeBy: string;
  preAuditTime: Date;
  hisDoctorId: string;
  status: string;
  healthCardResult: string;
  departSummaryList?: CustomerRegDepartSummary[];
  lockFlag?: string;
  lockReason?: string;
  lockTime?: string;
  unlockTime?: string;
  initailDoctor?: string;
  initailDoctorUsername?: string;
  chiefDoctor?: string;
  chiefDoctorUsername?: string;
  initailTime?: string;
  auditTime?: string;
  chiefTime?: string;
  auditor?: string;
  // 新增结构化数据字段
  abnormalSummaryData?: AbnormalSummary[];
  diagnosisList?: string[];
  adviceStructuredData?: AdviceBean[];
}

export interface IBarcodeSetting {
  id: string;
  name: string;
  barNumSource: string;
  barNumPrefix: string;
  text: string;
  barPage: number;
  sampleType: string;
  testType: string;
  tubeColor: string;
  sort: number;
  templateId: string;
  useConsumable: string;
  printLocation: string;
  enableFlag: number;
  updateBy: string;
  updateTime: Date;
  createBy: string;
  createTime: Date;
  checkDepart: string;
}

export interface ICustomerRegBarcode {
  id: string;
  customerRegId: string;
  archivesNum: string;
  barSettingId: string;
  bloodStatus: number;
  bloodTime: Date;
  bloodBy: string;
  barText: string;
  barNo: string;
  printTime: Date;
  printCount: number;
  checkFlag: string;
  acceptTime: Date;
  createTime: Date;
  createBy: string;
  updateTime: Date;
  updateBy: string;
  barcodeTemplateId?: string;
  barcodeSetting?: IBarcodeSetting;
  barTemplateId: string;
}

interface CompanyFeeStat {
  companyName: string;
  companyId: string;
  totalAmount: number;
  totalCount: number;
  totalRegCount: number;
  totalPayedAmount: number;
  totalRefundAmount: number;
  totalAmountOfReg: number;
  totalRemainAmount: number;
  teamFeeStats: TeamFeeStat[];
  itemGroupFeeStats: ItemGroupFeeStat[];
}

interface TeamFeeStat {
  teamId: string;
  teamName: string;
  companyId: string;
  companyName: string;
  totalAmount: number;
  remainAmount: number;
  payedAmount: number;
  totalCount: number;
  checkStatus: string;
}

export interface ItemGroupFeeStat {
  itemGroupId: string;
  itemGroupName: string;
  totalCount: number;
  totalAmount: number;
  totalCountOfReg: number;
  totalAmountOfReg: number;
}

export interface BarcodePannelSetting {
  id?: string;
  createBy?: string;
  createTime?: Date;
  updateBy?: string;
  updateTime?: Date;
  sysOrgCode?: string;
  autoBloodBatch: string;
  autoPrintBatch: string;
}

// src/types/Camunda.d.ts

export interface TaskDto {
  id: string;
  name: string;
  assignee: string;
  processInstanceId: string;
  processDefinitionId: string;
  variables: Record<string, any>;
  formKey?: string;
}

export interface ProcessInstanceDto {
  id: string;
  businessKey: string;
  processDefinitionId: string;
  processDefinitionKey: string;
  tenantId: string;
  suspended: string;
}

export interface Page<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

export interface HistoricalUserTask {
  id: string;
  name: string;
  assignee: string;
  taskDefinitionKey: string;
  startTime: string; // ISO 格式的日期字符串
  endTime: string; // ISO 格式的日期字符串
  variables?: Record<string, any>;
}

export interface ItemResultChangeApplyDto {
  changeApply: ItemResultChangeApply;
  customerReg: ICustomerReg;
  customerRegSummary: CustomerRegSummary;
}

export interface ItemResultChangeApply {
  id: string;
  createBy: string;
  createTime: Date;
  updateBy: string;
  updateTime: Date;
  sysOrgCode: string;
  examNo: string;
  customerName: string;
  applyReason: string;
  status: string;
  customerRegItemGroupId: string;
  applyDepartmentId: string;
  creator: string;
  applyDepartment: string;
  summaryId: string;
  confirmer: string;
  confirmBy: string;
  confirmTime: Date;
  remark: string;
  customerRegId: string;
}

export interface ReportCallback {
  id?: string;
  bussinessKey: string;
  bussinessType: string;
  createBy?: string;
  createTime?: Date;
  updateBy?: string;
  updateTime?: Date;
  sysOrgCode?: string;
  examNo?: string;
  applyBy?: string;
  applyer?: string;
  callbackReason?: string;
  result?: string;
  callbackBy?: string;
  callbackOperator?: string;
  callbackTime?: Date;
  remark?: string;
  summaryId?: string;
  customerRegId?: string;
  processInsId?: string;
  taskId?: string;
  agreeFlag?: string;
}

export interface CardGoods {
  id?: string;
  createBy?: string;
  createTime?: Date;
  updateBy?: string;
  updateTime?: Date;
  sysOrgCode?: string;
  name?: string;
  seq?: number;
  saleOnlineFlag?: string;
  enableFlag?: string;
  stock?: number;
  originPrice?: number;
  price?: number;
  discountRate?: number;
  icon?: string;
  headPics?: string;
  detail?: string;
  delFlag?: string;
  version?: number;
  tenantId?: string;
}

export interface Card {
  id?: string;
  goodsId?: string;
  orderId?: string;
  cardNo?: string;
  category?: string;
  publishTime?: Date;
  effectiveTime?: Date;
  discountRate?: number;
  denomination?: number;
  status?: string;
  remark?: string;
  customerId?: string;
  customerName?: string;
  balance?: number;
  amount?: number;
  createBy?: string;
  createTime?: Date;
  settleStatus?: string;
  creator?: string;
  companyId?: string;
  companyName?: string;
  companyRegId?: string;
  source?: string;
  pwd?: string;
  qrContent?: string;
  qrFile?: string;
  releaseTime?: Date;
  releaseBy?: string;
  saleTime?: Date;
  saleBy?: string;
  produceTime?: Date;
  produceBy?: string;
  lockTime?: Date;
  lockBy?: string;
  lockReason?: string;
  invalidTime?: Date;
  invalidBy?: string;
  cardNumber?: number;
  batchNumber?: string;
  version?: number;
  prefix?: string;
  totalCount?: number;
  cardColor?: string;
  writeStatus?: string;
}

export interface DepartStat {
  departmentId: string;
  departmentName: string;
  summaryStatus: string;
  depart: Depart;
}

export interface CustomerRegRecipe {
  id?: string;
  createBy?: string;
  creator?: string;
  createTime?: Date;
  updateBy?: string;
  updateTime?: Date;
  sysOrgCode?: string;
  status?: string;
  ip?: string;
  examNo?: string;
  customerId?: string;
  customerRegId?: string;
  bizName?: string;
  amount?: number;
  paidAmount?: number;
  unpaidAmount?: number;
  customerRegItemGroupIds?: string[];
  payRecords?: FeePayRecord[];
  refundItemGroupIds?: string[];
  applyRefundAmount?: number;
  refundDone?: boolean;
}

export interface CustomerRegBill {
  id?: string;
  billNo?: string;
  createBy?: string;
  creator?: string;
  createTime?: Date;
  updateBy?: string;
  updateTime?: Date;
  sysOrgCode?: string;
  status?: string;
  ip?: string;
  examNo?: string;
  customerId?: string;
  customerRegId?: string;
  bizName?: string;
  amount?: number;
  paidAmount?: number;
  unpaidAmount?: number;
  customerRegItemGroupIds?: string[];
  payRecords?: FeePayRecord[];
  refundItemGroupIds?: string[];
  applyRefundAmount?: number;
  refundDone?: boolean;
  customerRegItemGroups?: CustomerRegItemGroup[];
  giveAwayFlag?: string;
  afterPayFlag?: string;
  haveRefundFlag?: boolean;
}

export interface FeePayRecord {
  id?: string;
  customerRegId?: string;
  archivesNum?: string;
  companyRegId?: string;
  companyId?: string;
  payChannel?: string;
  payChannelWay?: string;
  amount?: number;
  currency?: string;
  state?: string;
  clientIp?: string;
  channelOrderNo?: string;
  refundState?: string;
  refundTimes?: number;
  refundAmount?: number;
  errCode?: string;
  successTime?: Date;
  createdTime?: Date;
  updatedTime?: Date;
  createBy?: string;
  creator?: string;
  examNo?: string;
  name?: string;
  componyName?: string;
  errMsg?: string;
  payerType?: string;
  hisApplyNo?: string;
  bizType?: string;
  bizId?: string;
  pid?: string;
  billId?: string;
  wayCode?: string;
  authCode?: string;
  pwd?: string;
  cardNo?: string;
  customerRegItemGroupIds?: string[];
  refundItemGroupIds?: string[];
  applyRefundAmount?: number;
  refundDone?: boolean;
}

export interface AbnormalSummary {
  id?: string;
  seq?: string;
  text: string;
  title?: string;
  summaryText?: string;
  format?: string;
  orderReason?: string;
  severityOrder?: number;
  itemSummaryTextList?: string[];
  relatedItemResults?: any[];
  relatedItemGroupList?: any[];
  // 审阅相关字段
  reviewInfo?: {
    hasReview: boolean;
    reviewStatus: 'pending' | 'reviewed' | 'corrected';
    annotations: ReviewAnnotation[];
    correctedContent?: string;
    reviewTime?: string;
    reviewer?: string;
    reviewComment?: string;
  };
}

// 审阅注解接口
export interface ReviewAnnotation {
  id: string;
  type: 'error' | 'warning' | 'suggestion';
  severity: 'low' | 'medium' | 'high';
  content: string;
  selectedText?: string;
  position?: string;
  startIndex?: number;
  endIndex?: number;
  createTime: string;
  reviewer: string;
  reviewerId: string;
  status: 'pending' | 'handled';
  handleComment?: string;
  handleTime?: string;
  handleBy?: string;
}

/**
 * Interface for abnormal item in paragraph block
 */
export interface AbnormalItem {
  code: string;
  name: string;
}

/**
 * Interface for the extra field in paragraph block
 */
export interface ParagraphExtra {
  id?: string;
  title?: string;
  content?: string;
  seq?: string;
  type?: '异常汇总' | '总检建议';
  abnormalType?: '诊断' | '异常';
  abnormalItem?: AbnormalItem[];
  summaryText?: string;
  format?: string;
  orderReason?: string;
  severityOrder?: number;
  code?: string;
}

/**
 * Interface for EditorJS paragraph block data
 */
export interface EditorJSParagraphData {
  text: string;
  extra?: ParagraphExtra;
}

/**
 * Interface for EditorJS paragraph block
 */
export interface EditorJSParagraphBlock {
  id?: string;
  type: 'paragraph';
  data: EditorJSParagraphData;
}

/**
 * Interface for the specific abnormal summary paragraph block
 * This represents the structure shown in the example:
 * {
 *   type: 'paragraph',
 *   data: {
 *     text: '',
 *     extra: {
 *       type: '异常汇总',
 *       abnormalType: '异常',
 *       abnormalItem: [],
 *     },
 *   },
 * }
 */
export interface AbnormalSummaryParagraphBlock extends EditorJSParagraphBlock {
  data: {
    text: string;
    extra: {
      type: '异常汇总';
      abnormalType: '异常' | '诊断';
      abnormalItem: AbnormalItem[];
    };
  };
}
