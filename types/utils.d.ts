import type { ComputedRef, Ref } from 'vue';

export type DynamicProps<T> = {
  [P in keyof T]: Ref<T[P]> | T[P] | ComputedRef<T[P]>;
};

// src/types/idcSvr.d.ts
export interface IdcSvr {
  send: (cmd: Record<string, unknown>) => void;
  connect: (url: string) => void;
  debug: () => void;
  event: {};
  on: (event: string, callback: (data: any) => void) => IdcSvr;
  off: (event: string, callback: (data: any) => void) => IdcSvr;
}

export interface IdcData {
  data: Record<string, any>;
  ok: boolean;
  msg: string;
  state: string;
  action: string;
}
