/**
 * Lucene索引健康报告数据结构
 */
export interface LuceneHealthReport {
  /** 索引是否健康 */
  healthy: boolean;
  
  /** 最后检查时间（时间戳） */
  lastCheckTime: number;
  
  /** 搜索操作总次数 */
  totalSearchCount: number;
  
  /** 搜索操作失败次数 */
  failedSearchCount: number;
  
  /** 索引操作总次数 */
  totalIndexCount: number;
  
  /** 索引操作失败次数 */
  failedIndexCount: number;
  
  /** 搜索成功率（0-1之间的小数） */
  searchSuccessRate: number;
  
  /** 索引成功率（0-1之间的小数） */
  indexSuccessRate: number;
  
  /** 索引是否存在 */
  indexExists: boolean;
  
  /** 索引文档数量 */
  indexDocumentCount: number;
  
  /** 索引大小信息 */
  indexSizeInfo: string;
}

/**
 * API响应结构
 */
export interface LuceneHealthResponse {
  success: boolean;
  message: string;
  code: number;
  result: LuceneHealthReport;
  timestamp: number;
}

/**
 * 任务状态枚举
 */
export enum TaskStatus {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING', 
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

/**
 * 任务类型枚举
 */
export enum TaskType {
  CREATE_INDEX = 'CREATE_INDEX',
  UPDATE_INDEX = 'UPDATE_INDEX',
  UPDATE_NECESSARY_INDEX = 'UPDATE_NECESSARY_INDEX',
  REBUILD_INDEX = 'REBUILD_INDEX',
  REBUILD_ALL_INDEX = 'REBUILD_ALL_INDEX',
  REPAIR_INDEX = 'REPAIR_INDEX',
  HEALTH_CHECK = 'HEALTH_CHECK'
}

/**
 * 任务信息
 */
export interface TaskInfo {
  /** 任务ID */
  taskId: string;
  
  /** 任务类型 */
  taskType: TaskType;
  
  /** 任务状态 */
  status: TaskStatus;
  
  /** 任务开始时间 */
  startTime: number;
  
  /** 任务结束时间 */
  endTime?: number;
  
  /** 任务进度（0-100） */
  progress: number;
  
  /** 任务消息 */
  message?: string;
  
  /** 错误信息 */
  error?: string;
}
