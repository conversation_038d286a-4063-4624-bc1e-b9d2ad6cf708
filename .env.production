# 是否启用mock
VITE_USE_MOCK = true

# 发布路径
VITE_PUBLIC_PATH = /

#后台接口全路径地址(必填)
#VITE_GLOB_DOMAIN_URL=http://physicalex.yingyangyun.cn/jeecgboot
VITE_GLOB_DOMAIN_URL=http://localhost:8090/jeecgboot

# 是否启用gzip或brotli压缩
# 选项值: gzip | brotli | none
# 如果需要多个可以使用“,”分隔
VITE_BUILD_COMPRESS = 'gzip'

# 使用压缩时是否删除原始文件，默认为false
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE = false

#后台接口父地址(必填)
VITE_GLOB_API_URL=/jeecgboot

#后台接口全路径地址(必填)
#VITE_GLOB_DOMAIN_URL=http://physicalex.yingyangyun.cn/jeecgboot
VITE_GLOB_DOMAIN_URL=http://localhost:8090/jeecgboot

# 接口父路径前缀
VITE_GLOB_API_URL_PREFIX=

#文件上传的类型,aliyun,minio,local
VITE_GLOB_UPLOAD_TYPE=minio

# ==================== MinIO内外网分离配置 ====================
# 内网地址（HTTP，用于局域网内访问，避免证书问题）
VITE_GLOB_MINIO_INTERNAL_URL=http://************:9000

# 外网地址（HTTPS，用于公网访问）
VITE_GLOB_MINIO_EXTERNAL_URL=https://etkq.yingyangyun.cn/minio

# 注释掉原有的单一配置，使用上面的内外网分离配置
# VITE_GLOB_MINIO_URL=https://************:8888/minio

# 是否兼容旧版浏览器。开启后打包时间会慢一倍左右。会多打出旧浏览器兼容包,且会根据浏览器兼容性自动使用相应的版本
VITE_LEGACY = false

