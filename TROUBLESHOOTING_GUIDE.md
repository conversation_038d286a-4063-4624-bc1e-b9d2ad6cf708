# 问题排查指南

## 🐛 当前问题：点击职业病问卷按钮没有反应

### 📊 已完成的修复步骤

1. ✅ **检查组件导入** - 改为直接导入避免异步加载问题
2. ✅ **创建稳定版本** - `StableInquiryAdapter.vue` 作为稳定的适配器
3. ✅ **添加调试信息** - 在 `openInquiry` 方法中加入详细日志
4. ✅ **修复API调用错误** - 移除了可能导致运行时错误的API调用

### 🔍 排查步骤

#### 步骤1：检查浏览器控制台
1. 打开开发者工具 (F12)
2. 查看Console选项卡
3. 点击"职业病问卷"按钮
4. 观察是否有错误信息或日志输出

**预期输出**：
```
openInquiry 被调用 {customer data}
inquiryModal.value: {component instance}
稳定适配器打开: {customer data} false
```

#### 步骤2：检查组件加载状态
如果控制台显示 `inquiryModal.value 为空`，说明组件没有正确加载。

**解决方案**：
1. 检查文件路径是否正确
2. 确认 `StableInquiryAdapter.vue` 文件存在
3. 检查是否有TypeScript编译错误

#### 步骤3：检查按钮点击事件
如果没有看到 `openInquiry 被调用` 日志，说明点击事件没有触发。

**可能原因**：
1. 按钮被其他元素遮挡
2. 事件绑定不正确
3. 页面JavaScript错误阻止了事件执行

### 🚀 快速验证方案

#### 方案1：使用测试按钮
在页面上添加一个测试按钮直接调用方法：

```vue
<!-- 在 CustomerRegPannel.vue 模板中临时添加 -->
<a-button @click="testInquiry" type="danger" style="position: fixed; top: 10px; right: 10px; z-index: 9999;">
  测试问诊
</a-button>
```

```js
// 在 script 中添加测试方法
function testInquiry() {
  console.log('测试按钮被点击');
  console.log('当前登记:', currentReg.value);
  console.log('问诊组件:', inquiryModal.value);
  
  if (inquiryModal.value) {
    inquiryModal.value.open({ 
      id: '123', 
      name: '测试患者', 
      age: 30, 
      gender: '男' 
    }, false);
  } else {
    alert('问诊组件未加载');
  }
}
```

#### 方案2：简化为最基本的模态框
如果复杂组件有问题，可以临时使用最简单的测试组件：

```vue
<!-- TestModal.vue -->
<template>
  <a-modal v-model:open="visible" title="测试">
    <p>测试模态框正常工作</p>
  </a-modal>
</template>

<script setup>
import { ref } from 'vue'
const visible = ref(false)
function open() { visible.value = true }
defineExpose({ open })
</script>
```

### 🔧 常见问题和解决方案

#### 问题1：组件路径错误
**现象**：控制台显示模块加载失败
**解决**：检查文件路径，确保文件存在

#### 问题2：Vue组件注册问题
**现象**：`inquiryModal.value` 为 null
**解决**：检查模板中的 `ref="inquiryModal"` 是否正确

#### 问题3：权限或路由问题
**现象**：按钮存在但点击无效果
**解决**：检查用户权限和路由配置

#### 问题4：CSS样式冲突
**现象**：按钮被遮挡或不可点击
**解决**：使用开发者工具检查元素层级

### 📝 调试检查清单

- [ ] 浏览器控制台无错误信息
- [ ] `openInquiry` 方法被正确调用
- [ ] `inquiryModal.value` 不为空
- [ ] `currentReg.value` 有正确的数据
- [ ] 模态框组件正确显示
- [ ] 可以正常关闭模态框

### 🆘 如果问题仍然存在

1. **回退到原始组件**：临时使用原来的 `ZyInquiryModal`
2. **联系支持**：提供详细的错误日志和页面截图
3. **检查环境**：确认开发环境配置正确

### 📞 获取帮助

如果按照以上步骤仍无法解决问题，请提供以下信息：

1. 浏览器控制台的完整错误日志
2. Network选项卡中的请求状态
3. Vue DevTools中的组件树状态
4. 当前的Vue和相关依赖版本

---

💡 **提示**：大多数情况下问题出现在组件路径或异步加载上，使用直接导入通常可以解决问题。