# 职业病问卷系统替换指南

## 概述
本文档详细说明如何将现有的职业病问卷系统替换为新的优雅问卷系统，确保平滑过渡且不影响现有业务逻辑。

## 替换策略

### 1. 渐进式替换（推荐）
采用特性开关（Feature Toggle）的方式，允许新老系统并存，逐步切换。

### 2. 直接替换
完全替换现有组件，风险较高但迁移彻底。

## 详细实施步骤

### 阶段一：准备工作

#### 1.1 数据库表准备
确保以下表已存在且结构正确：

```sql
-- questionnaire_common_options 表（如果不存在需要创建）
CREATE TABLE IF NOT EXISTS questionnaire_common_options (
  id VARCHAR(32) PRIMARY KEY,
  option_type VARCHAR(50) NOT NULL COMMENT '选项类型',
  option_label VARCHAR(100) NOT NULL COMMENT '选项显示文本',
  option_value VARCHAR(100) NOT NULL COMMENT '选项值',
  usage_count INT DEFAULT 0 COMMENT '使用次数',
  sort_order INT DEFAULT 0 COMMENT '排序',
  status VARCHAR(1) DEFAULT '1' COMMENT '状态',
  remark VARCHAR(500) COMMENT '备注',
  create_by VARCHAR(50) COMMENT '创建人',
  create_time DATETIME COMMENT '创建时间',
  update_by VARCHAR(50) COMMENT '更新人',
  update_time DATETIME COMMENT '更新时间'
);

-- 插入基础数据
INSERT INTO questionnaire_common_options (id, option_type, option_label, option_value, usage_count, sort_order, status) VALUES
('gender_1', 'gender', '男', '男', 100, 1, '1'),
('gender_2', 'gender', '女', '女', 95, 2, '1'),
('smoking_1', 'smoking_habit', '不吸烟', '不吸烟', 200, 1, '1'),
('smoking_2', 'smoking_habit', '偶尔吸烟', '偶尔吸烟', 50, 2, '1'),
('smoking_3', 'smoking_habit', '经常吸烟', '经常吸烟', 30, 3, '1'),
('smoking_4', 'smoking_habit', '已戒烟', '已戒烟', 20, 4, '1');
```

#### 1.2 后端API适配（如需要）
如果需要新的API接口，添加以下控制器：

```java
@RestController
@RequestMapping("/physicalex/questionnaireCommonOptions")
public class QuestionnaireCommonOptionsController {
    
    @PostMapping("/updateUsageCount")
    public Result<?> updateUsageCount(@RequestBody Map<String, String> params) {
        // 更新使用频次逻辑
        return Result.ok("更新成功");
    }
}
```

### 阶段二：组件集成

#### 2.1 创建特性开关配置文件
```typescript
// src/config/features.ts
export const FEATURES = {
  ELEGANT_QUESTIONNAIRE: process.env.VUE_APP_ELEGANT_QUESTIONNAIRE === 'true' || false
}
```

#### 2.2 修改环境配置
```bash
# .env.development
VUE_APP_ELEGANT_QUESTIONNAIRE=true

# .env.production  
VUE_APP_ELEGANT_QUESTIONNAIRE=false  # 生产环境先保持false
```

#### 2.3 创建组件适配器
```vue
<!-- src/views/occu/components/QuestionnaireAdapter.vue -->
<template>
  <div>
    <!-- 新版问卷 -->
    <ElegantQuestionnaire 
      v-if="useElegantQuestionnaire"
      @submit="handleSubmit"
      @save-draft="handleSaveDraft"
    />
    
    <!-- 旧版问卷 -->
    <div v-else class="legacy-questionnaire">
      <ZyInquirySymptomList />
      <ZyInquiryDiseaseHistoryList />
      <!-- 其他现有组件 -->
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { FEATURES } from '@/config/features'
import ElegantQuestionnaire from './ElegantQuestionnaire.vue'
import ZyInquirySymptomList from './ZyInquirySymptomList.vue'
import ZyInquiryDiseaseHistoryList from './ZyInquiryDiseaseHistoryList.vue'

const useElegantQuestionnaire = computed(() => FEATURES.ELEGANT_QUESTIONNAIRE)

function handleSubmit(data) {
  // 统一的提交处理逻辑
  console.log('新版问卷提交:', data)
}

function handleSaveDraft(data) {
  // 统一的草稿保存逻辑
  console.log('保存草稿:', data)
}
</script>
```

### 阶段三：路由配置更新

#### 3.1 修改现有路由
```typescript
// src/router/modules/occu.ts
{
  path: '/occu/inquiry',
  name: 'ZyInquiry',
  component: () => import('@/views/occu/ZyInquiryAdapter.vue'), // 使用适配器
  meta: {
    title: '职业病问诊',
    keepAlive: true
  }
}

// 添加新版问卷演示路由
{
  path: '/occu/elegant-questionnaire',
  name: 'ElegantQuestionnaire',
  component: () => import('@/views/occu/ElegantQuestionnaireDemo.vue'),
  meta: {
    title: '新版职业病问卷',
    keepAlive: true
  }
}
```

#### 3.2 创建主页面适配器
```vue
<!-- src/views/occu/ZyInquiryAdapter.vue -->
<template>
  <div class="inquiry-adapter">
    <!-- 版本切换提示 -->
    <a-alert 
      v-if="showVersionSwitch"
      type="info" 
      show-icon 
      closable
      style="margin-bottom: 16px"
    >
      <template #message>
        <span>新版问卷系统已上线！</span>
        <a-button type="link" @click="switchToNew">立即体验</a-button>
      </template>
    </a-alert>

    <!-- 问卷内容 -->
    <QuestionnaireAdapter />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import QuestionnaireAdapter from './components/QuestionnaireAdapter.vue'

const router = useRouter()
const showVersionSwitch = ref(true)

function switchToNew() {
  router.push('/occu/elegant-questionnaire')
}
</script>
```

### 阶段四：数据兼容性处理

#### 4.1 创建数据转换工具
```typescript
// src/utils/questionnaireDataConverter.ts
export class QuestionnaireDataConverter {
  
  // 新版数据转换为旧版格式
  static newToLegacy(newData: any): any {
    return {
      // 基本信息映射
      customerName: newData.name,
      customerAge: newData.age,
      customerGender: newData.gender,
      
      // 症状数据转换
      symptoms: newData.symptoms?.map(symptomId => ({
        symptom: symptomId,
        severity: '中',
        remark: ''
      })) || [],
      
      // 其他字段映射...
    }
  }
  
  // 旧版数据转换为新版格式
  static legacyToNew(legacyData: any): any {
    return {
      name: legacyData.customerName,
      age: legacyData.customerAge,
      gender: legacyData.customerGender,
      symptoms: legacyData.symptoms?.map(item => item.symptom) || [],
      // 其他字段映射...
    }
  }
}
```

#### 4.2 API适配层
```typescript
// src/api/questionnaireAdapter.ts
import { QuestionnaireDataConverter } from '@/utils/questionnaireDataConverter'
import { zyInquirySymptomSaveOrUpdate } from '@/views/occu/ZyInquiry.api'

export const questionnaireAdapter = {
  
  async submitQuestionnaire(data: any, useNewFormat = false) {
    let submitData = data
    
    if (useNewFormat) {
      // 新版数据需要转换
      submitData = QuestionnaireDataConverter.newToLegacy(data)
    }
    
    // 调用现有API
    return await zyInquirySymptomSaveOrUpdate(submitData, false)
  }
}
```

### 阶段五：测试与验证

#### 5.1 单元测试
```typescript
// tests/unit/QuestionnaireDataConverter.test.ts
import { QuestionnaireDataConverter } from '@/utils/questionnaireDataConverter'

describe('QuestionnaireDataConverter', () => {
  test('should convert new format to legacy format', () => {
    const newData = {
      name: '张三',
      age: 30,
      gender: '男',
      symptoms: ['10002', '10003']
    }
    
    const legacyData = QuestionnaireDataConverter.newToLegacy(newData)
    
    expect(legacyData.customerName).toBe('张三')
    expect(legacyData.symptoms).toHaveLength(2)
  })
})
```

#### 5.2 集成测试清单
- [ ] 新版问卷数据提交测试
- [ ] 数据格式转换测试
- [ ] 旧版系统兼容性测试
- [ ] 特性开关切换测试
- [ ] 草稿保存与恢复测试

### 阶段六：部署与切换

#### 6.1 灰度发布计划
```bash
# 第一周：内部测试
VUE_APP_ELEGANT_QUESTIONNAIRE=true (仅开发环境)

# 第二周：部分用户试用
VUE_APP_ELEGANT_QUESTIONNAIRE=true (测试环境)

# 第三周：生产环境灰度
通过用户ID或角色控制特性开关

# 第四周：全量切换
VUE_APP_ELEGANT_QUESTIONNAIRE=true (生产环境)
```

#### 6.2 回滚方案
如果发现问题，立即设置：
```bash
VUE_APP_ELEGANT_QUESTIONNAIRE=false
```

### 阶段七：清理工作

#### 7.1 移除旧代码（切换完成后）
```bash
# 删除不再使用的组件
rm src/views/occu/components/ZyInquirySymptomForm.vue
rm src/views/occu/components/ZyInquirySymptomModal.vue
# ... 其他旧组件

# 删除适配器代码
rm src/views/occu/components/QuestionnaireAdapter.vue
rm src/utils/questionnaireDataConverter.ts
```

#### 7.2 更新文档
- 更新用户使用手册
- 更新开发文档
- 更新API文档

## 风险控制

### 高风险项
1. **数据不一致**：新旧系统数据格式差异
2. **功能缺失**：新系统可能缺少某些旧功能
3. **性能问题**：新组件可能存在性能瓶颈

### 风险缓解措施
1. **数据备份**：切换前备份相关数据表
2. **功能对比**：详细对比新旧功能清单
3. **性能测试**：切换前进行充分的性能测试
4. **快速回滚**：准备快速回滚方案

## 监控指标

### 业务指标
- 问卷提交成功率
- 用户操作完成时间
- 用户满意度评分

### 技术指标
- 页面加载时间
- API响应时间
- 错误率统计

## 时间计划

| 阶段 | 预计时间 | 主要工作 |
|------|----------|----------|
| 准备 | 1天 | 数据库准备、环境配置 |
| 集成 | 2天 | 组件适配、路由配置 |
| 测试 | 3天 | 功能测试、兼容性测试 |
| 部署 | 1周 | 灰度发布、监控调优 |
| 清理 | 1天 | 删除旧代码、更新文档 |

## 联系支持

如在替换过程中遇到问题，请联系：
- 技术支持：开发团队
- 业务支持：产品团队
- 紧急联系：项目经理

---

> 注意：此迁移过程需要谨慎执行，建议先在测试环境完整验证后再在生产环境实施。