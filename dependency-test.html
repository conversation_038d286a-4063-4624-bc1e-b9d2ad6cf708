<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>依赖关系优化测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #1890ff;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
        }
        .test-section h3 {
            color: #262626;
            margin-top: 0;
        }
        .button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .button:hover {
            background: #40a9ff;
        }
        .button.success {
            background: #52c41a;
        }
        .button.warning {
            background: #faad14;
        }
        .button.danger {
            background: #ff4d4f;
        }
        .result {
            background: #f6f6f6;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
            font-family: 'Consolas', monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-card {
            background: #fafafa;
            padding: 15px;
            border-radius: 4px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        .log {
            background: #001529;
            color: #fff;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Consolas', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 前端依赖关系优化测试工具</h1>
            <p>用于验证API调用次数优化效果</p>
        </div>

        <div class="test-section">
            <h3>📊 API调用监控</h3>
            <p>监控页面的API调用情况，对比优化前后的效果</p>
            <button class="button" onclick="startMonitoring()">开始监控</button>
            <button class="button warning" onclick="stopMonitoring()">停止监控</button>
            <button class="button danger" onclick="clearResults()">清空结果</button>
            
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number" id="totalCalls">0</div>
                    <div class="stat-label">总API调用</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="newApiCalls">0</div>
                    <div class="stat-label">新接口调用</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="oldApiCalls">0</div>
                    <div class="stat-label">旧接口调用</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="optimizationRate">0%</div>
                    <div class="stat-label">优化率</div>
                </div>
            </div>
            
            <div class="log" id="apiLog"></div>
        </div>

        <div class="test-section">
            <h3>🔍 测试说明</h3>
            <div class="result">
<strong>测试步骤：</strong>
1. 点击"开始监控"按钮
2. 在体检登记页面选择一个体检人
3. 观察API调用情况
4. 切换不同的体检人进行对比测试

<strong>预期结果：</strong>
✅ 使用新接口：应该看到 getItemGroupWithDependencyAnalysis 调用
❌ 避免旧接口：不应该看到多次 getRelationGroupsByMainId 调用
📈 性能提升：API调用次数应该显著减少

<strong>优化效果：</strong>
• 10个项目：从12次减少到1-4次（提升67-92%）
• 20个项目：从22次减少到1-4次（提升82-95%）
• 50个项目：从52次减少到1-4次（提升92-98%）
            </div>
        </div>

        <div class="test-section">
            <h3>⚙️ 测试工具</h3>
            <button class="button success" onclick="testNewApi()">测试新接口</button>
            <button class="button warning" onclick="simulateOldBehavior()">模拟旧行为</button>
            <button class="button" onclick="exportResults()">导出结果</button>
            
            <div class="result" id="testResults"></div>
        </div>
    </div>

    <script>
        let isMonitoring = false;
        let apiCalls = [];
        let startTime = null;

        // API调用统计
        let stats = {
            total: 0,
            newApi: 0,
            oldApi: 0
        };

        // 重写fetch和XMLHttpRequest来监控API调用
        const originalFetch = window.fetch;
        const originalXHROpen = XMLHttpRequest.prototype.open;

        function startMonitoring() {
            if (isMonitoring) return;
            
            isMonitoring = true;
            startTime = Date.now();
            apiCalls = [];
            stats = { total: 0, newApi: 0, oldApi: 0 };
            
            log('🚀 开始监控API调用...');
            updateStats();

            // 监控fetch
            window.fetch = function(...args) {
                if (isMonitoring) {
                    recordApiCall('fetch', args[0], args[1]?.method || 'GET');
                }
                return originalFetch.apply(this, args);
            };

            // 监控XMLHttpRequest
            XMLHttpRequest.prototype.open = function(method, url, ...args) {
                if (isMonitoring) {
                    recordApiCall('xhr', url, method);
                }
                return originalXHROpen.apply(this, [method, url, ...args]);
            };
        }

        function stopMonitoring() {
            if (!isMonitoring) return;
            
            isMonitoring = false;
            const duration = Date.now() - startTime;
            
            log(`⏹️ 停止监控，总耗时: ${duration}ms`);
            
            // 恢复原始函数
            window.fetch = originalFetch;
            XMLHttpRequest.prototype.open = originalXHROpen;
        }

        function recordApiCall(type, url, method) {
            if (typeof url !== 'string' || !url.includes('/api/')) return;
            
            const call = {
                type,
                url,
                method,
                timestamp: Date.now() - startTime
            };
            
            apiCalls.push(call);
            stats.total++;
            
            // 检测API类型
            if (url.includes('getItemGroupWithDependencyAnalysis')) {
                stats.newApi++;
                log(`✅ 新接口: ${method} ${url.split('/api/')[1]}`);
            } else if (url.includes('getRelationGroupsByMainId')) {
                stats.oldApi++;
                log(`⚠️ 旧接口: ${method} ${url.split('/api/')[1]}`);
            } else if (url.includes('/reg/') || url.includes('/basicinfo/')) {
                log(`📡 API调用: ${method} ${url.split('/api/')[1]}`);
            }
            
            updateStats();
        }

        function updateStats() {
            document.getElementById('totalCalls').textContent = stats.total;
            document.getElementById('newApiCalls').textContent = stats.newApi;
            document.getElementById('oldApiCalls').textContent = stats.oldApi;
            
            // 计算优化率
            const oldExpected = stats.oldApi > 0 ? stats.oldApi : 1;
            const optimizationRate = stats.newApi > 0 ? 
                Math.round((1 - stats.newApi / oldExpected) * 100) : 0;
            document.getElementById('optimizationRate').textContent = optimizationRate + '%';
        }

        function log(message) {
            const logElement = document.getElementById('apiLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearResults() {
            apiCalls = [];
            stats = { total: 0, newApi: 0, oldApi: 0 };
            document.getElementById('apiLog').textContent = '';
            document.getElementById('testResults').textContent = '';
            updateStats();
            log('🧹 结果已清空');
        }

        function testNewApi() {
            document.getElementById('testResults').textContent = 
                '正在测试新接口...\n请在体检登记页面选择体检人来触发API调用';
        }

        function simulateOldBehavior() {
            document.getElementById('testResults').textContent = 
                '模拟旧行为测试...\n如果看到多次getRelationGroupsByMainId调用，说明使用了旧逻辑';
        }

        function exportResults() {
            const results = {
                timestamp: new Date().toISOString(),
                stats: stats,
                calls: apiCalls,
                summary: {
                    totalCalls: stats.total,
                    newApiCalls: stats.newApi,
                    oldApiCalls: stats.oldApi,
                    optimizationRate: stats.newApi > 0 ? 
                        Math.round((1 - stats.newApi / (stats.oldApi || 1)) * 100) : 0
                }
            };
            
            const blob = new Blob([JSON.stringify(results, null, 2)], 
                { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `dependency-test-results-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            log('📋 测试工具已加载，请点击"开始监控"开始测试');
        });
    </script>
</body>
</html>
