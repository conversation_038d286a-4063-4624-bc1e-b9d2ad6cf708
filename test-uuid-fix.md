# UUID修复和批量保存问题修复说明

## 问题描述
1. 点击一个卡片的保存按钮，结果所有卡片的按钮都变成loading状态
2. 批量保存功能无法正常工作
3. 前端给新记录赋值ID会导致后端逻辑错误

## 问题原因分析
1. **编辑状态管理混乱**：使用record.id管理编辑状态，但新记录的ID处理不当
2. **前端不应设置ID**：新记录的ID应该由后端生成，前端设置会导致后端逻辑错误
3. **状态管理不一致**：模板key使用UUID，但状态管理使用ID

## 完整修复方案

### 1. 新记录不设置ID字段
```javascript
// 自动创建卡片 - 不设置id字段
const uuid = buildUUID();
const newRecord = {
  // 不设置id字段，让后端生成
  uuid: uuid, // 使用UUID作为唯一标识
  riskFactorId: risk.id,
  // ... 其他字段
  isNew: true,
  autoCreated: true,
};

// 手动创建卡片 - 不设置id字段
const uuid = buildUUID();
const newRecord = {
  // 不设置id字段，让后端生成
  uuid: uuid, // 使用UUID作为唯一标识
  // ... 其他字段
  isNew: true,
  manualCreated: true,
};
```

### 2. 使用UUID管理编辑状态
```javascript
// 编辑状态管理改为使用UUID
const editingRows = ref<Set<string>>(new Set());
const savingRows = ref<Set<string>>(new Set());

// 添加到编辑状态
editingRows.value.add(record.uuid);

// 添加到保存状态
savingRows.value.add(record.uuid);
```

### 3. 修复保存逻辑
```javascript
async function handleSaveRow(record: any) {
  try {
    savingRows.value.add(record.uuid); // 使用UUID管理保存状态

    // 准备保存数据
    const saveData = { ...record };
    delete saveData.editable;
    delete saveData.isNew;
    delete saveData.onEdit;
    delete saveData.uuid; // 不发送UUID到后端

    // 判断是新增还是更新
    const isUpdate = !record.isNew && record.id; // 有ID且不是新记录就是更新
    const result = await saveOrUpdate(saveData, isUpdate);

    if (result.success) {
      if (record.isNew) {
        // 保存成功后，设置后端返回的真实ID
        const newId = result.result?.id || result.result;
        if (newId) {
          record.id = newId;
        }
        record.isNew = false;
      }

      record.editable = false;
      editingRows.value.delete(record.uuid); // 使用UUID移除编辑状态

      message.success('保存成功');
      return true;
    }
  } finally {
    savingRows.value.delete(record.uuid); // 使用UUID移除保存状态
  }
}
```

### 4. 修复批量保存逻辑
```javascript
async function saveAllEditing() {
  const editingRowUuids = Array.from(editingRows.value);

  for (const uuid of editingRowUuids) {
    const record = dataSource.value.find((item) => item.uuid === uuid);
    if (record) {
      await handleSaveRow(record);
    }
  }
}
```

### 5. 修复loading状态显示
```vue
<ZyConclusionCard
  v-for="record in dataSource"
  :key="record.uuid"
  :record="record"
  :loading="savingRows.has(record.uuid)"
  :saving="savingRows.has(record.uuid)"
  @edit="handleEditRow"
  @save="handleSaveRow"
  @cancel="handleCancelRow"
  @delete="handleDeleteRow"
/>
```

### 6. 统一使用UUID进行记录查找和管理
```javascript
// 取消编辑
function handleCancelRow(record: any) {
  if (record.isNew) {
    const index = dataSource.value.findIndex((item) => item.uuid === record.uuid);
    if (index > -1) {
      dataSource.value.splice(index, 1);
    }
  } else {
    record.editable = false;
  }
  editingRows.value.delete(record.uuid);
}

// 删除记录
async function handleDeleteRow(record: any) {
  if (record.isNew || !record.id) {
    // 删除未保存的记录
    const index = dataSource.value.findIndex((item) => item.uuid === record.uuid);
    if (index > -1) {
      dataSource.value.splice(index, 1);
    }
    editingRows.value.delete(record.uuid);
  } else {
    // 删除已保存的记录
    const result = await deleteOne({ id: record.id });
    if (result.success) {
      const index = dataSource.value.findIndex((item) => item.uuid === record.uuid);
      if (index > -1) {
        dataSource.value.splice(index, 1);
      }
      editingRows.value.delete(record.uuid);
    }
  }
}
```

## 修复效果
- ✅ 新记录不设置ID，避免后端逻辑错误
- ✅ 使用UUID统一管理编辑状态和保存状态
- ✅ 修复了批量保存功能
- ✅ 解决了loading状态显示问题
- ✅ 保持了与后端API的兼容性
- ✅ 确保每个卡片的状态管理独立

## 测试建议
1. 创建新的手动卡片，点击保存，确认只有当前卡片显示loading状态
2. 自动创建危害因素卡片，分别保存不同卡片，确认loading状态独立
3. 测试批量保存功能，确认能正确保存所有编辑中的卡片
4. 测试取消编辑和删除功能，确认状态管理正确
