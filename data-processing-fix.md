# 数据处理逻辑修复说明

## 问题描述
当前组件的数据处理逻辑有问题：在体检记录发生变化后，应该先获取已经保存的记录，再补充没有匹配危害因素的记录。

## 原有问题
1. **数据加载顺序错误**：先清理未保存记录，再加载已保存记录，最后创建新记录
2. **可能丢失用户编辑**：清理时可能删除用户正在编辑的重要数据
3. **逻辑不合理**：没有考虑危害因素变化对未保存记录的影响

## 修复后的正确逻辑

### 1. 数据加载流程优化
```javascript
const loadData = async (append = false) => {
  // 1. 先获取已保存的记录
  const result = await list(params);
  const savedRecords = result.records.map(record => ({
    ...record,
    uuid: record.uuid || buildUUID()
  }));

  if (!append) {
    // 2. 保留当前正在编辑且对应危害因素仍存在的未保存记录
    const validUnsavedRecords = preserveValidUnsavedRecords();
    
    // 3. 合并已保存记录和有效的未保存记录
    dataSource.value = [...savedRecords, ...validUnsavedRecords];
    
    // 4. 自动为缺失的危害因素创建对应的卡片
    await autoCreateRiskFactorCards();
  }
};
```

### 2. 智能保留未保存记录
```javascript
const preserveValidUnsavedRecords = () => {
  // 获取当前危害因素ID列表
  const currentRiskFactorIds = new Set(
    riskFactorList.value?.map(risk => risk.id) || []
  );

  // 找出需要保留的未保存记录
  const validUnsavedRecords = dataSource.value.filter(record => {
    if (!record.isNew) return false;

    // 手动创建的记录总是保留
    if (record.manualCreated) return true;

    // 自动创建的记录，只有当对应的危害因素仍然存在时才保留
    if (record.autoCreated && record.riskFactorId) {
      const shouldKeep = currentRiskFactorIds.has(record.riskFactorId);
      if (!shouldKeep) {
        // 从编辑状态中移除无效记录
        editingRows.value.delete(record.uuid);
      }
      return shouldKeep;
    }

    return false;
  });

  return validUnsavedRecords;
};
```

### 3. 基于完整数据源补充缺失记录
```javascript
const autoCreateRiskFactorCards = async () => {
  // 获取当前所有记录中已存在的危害因素ID（包括已保存的和未保存的）
  const existingRiskFactorIds = new Set(
    dataSource.value
      .map(item => item.riskFactorId)
      .filter(id => id !== null && id !== undefined)
  );

  // 找出需要补充的危害因素
  const missingRiskFactors = riskFactorList.value.filter(risk => 
    !existingRiskFactorIds.has(risk.id)
  );

  // 为缺失的危害因素创建卡片
  const newCards = missingRiskFactors.map(risk => ({
    uuid: buildUUID(),
    riskFactorId: risk.id,
    riskCode: risk.code,
    riskFactor: risk.name,
    mainFlag: riskFactorList.value.findIndex(r => r.id === risk.id) === 0 ? '1' : '0',
    // ... 其他字段
    editable: true,
    isNew: true,
    autoCreated: true,
  }));

  // 添加到数据源并标记为编辑状态
  dataSource.value = [...dataSource.value, ...newCards];
  newCards.forEach(card => editingRows.value.add(card.uuid));
};
```

### 4. 客户切换时的清理逻辑
```javascript
// 只有在切换到不同客户时才清理所有未保存的卡片
watch(() => customerReg4Summary.value?.id, (newValue, oldValue) => {
  if (oldValue && newValue && oldValue !== newValue) {
    // 切换客户时，清理所有未保存的卡片
    cleanupInvalidUnsavedCards();
  }
  
  if (newValue) {
    loadData(); // 重新加载数据
  }
});
```

## 修复效果

### ✅ 优化后的数据处理流程
1. **先加载已保存记录**：确保已保存的数据优先显示
2. **智能保留未保存记录**：
   - 手动创建的记录总是保留（用户可能正在编辑自定义结论）
   - 自动创建的记录只有在对应危害因素仍存在时才保留
3. **基于完整数据补充**：基于已保存+有效未保存记录来判断需要补充的危害因素
4. **避免数据丢失**：不会意外删除用户正在编辑的重要数据

### ✅ 解决的问题
- **数据一致性**：确保显示的数据与当前危害因素列表一致
- **用户体验**：保护用户正在编辑的数据不被意外清理
- **逻辑合理性**：数据处理顺序更加合理和安全
- **状态管理**：正确维护编辑状态，避免状态混乱

## 测试建议
1. 测试体检记录变化时，已保存记录正确显示
2. 测试危害因素变化时，相关未保存记录的保留/清理逻辑
3. 测试手动创建的记录在各种情况下都能正确保留
4. 测试客户切换时的数据清理功能
