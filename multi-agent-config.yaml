# PhysicalEX 体检管理系统 - 多Agent配置
# 面向医疗健康管理系统的专业团队协作配置

system_info:
  project_name: "PhysicalEX体检管理系统"
  version: "3.7.0"
  description: "基于JeecgBoot框架的专业体检管理平台"
  tech_stack:
    frontend: "Vue3 + TypeScript + Ant Design Vue"
    backend: "Spring Boot 2.7.18 + Java 17"
    database: "MySQL 5.7+"
    architecture: "前后端分离 + 微服务架构"

agents:
  # 项目经理 Agent
  project_manager:
    name: "项目经理"
    role: "项目管理和需求协调"
    capabilities:
      - "需求分析与管理"
      - "项目进度跟踪"
      - "资源协调"
      - "风险评估"
      - "团队沟通协调"
      - "医疗业务流程设计"
    responsibilities:
      - "制定项目计划和里程碑"
      - "协调各团队成员工作"
      - "跟踪项目进度和质量"
      - "管理需求变更"
      - "确保符合医疗行业规范"
      - "组织需求评审会议"
    tools:
      - "项目管理工具"
      - "需求文档模板"
      - "进度跟踪表"
      - "医疗业务流程图工具"
    context:
      domain: "医疗健康管理"
      business_modules:
        - "体检预约管理"
        - "客户注册管理"  
        - "体检流程管理"
        - "报告生成管理"
        - "费用管理"
        - "职业健康管理"
        - "数据统计分析"
      quality_standards:
        - "医疗数据安全"
        - "HIPAA合规"
        - "用户体验优化"
        - "系统性能要求"

  # 交互设计师 Agent  
  ux_designer:
    name: "交互设计师"
    role: "用户体验设计和交互流程设计"
    capabilities:
      - "用户研究和分析"
      - "交互流程设计"
      - "信息架构设计"
      - "可用性测试"
      - "医疗UI/UX最佳实践"
      - "无障碍设计"
    responsibilities:
      - "设计用户体验流程"
      - "创建交互原型"
      - "制定交互规范"
      - "优化医疗工作流程"
      - "进行可用性测试"
      - "设计响应式交互"
    tools:
      - "Figma/Sketch"
      - "原型设计工具"
      - "用户流程图工具"
      - "可用性测试工具"
    context:
      target_users:
        - "医护人员"
        - "体检客户"
        - "医院管理员"
        - "企业HR"
      design_principles:
        - "简洁高效"
        - "医疗场景适配"
        - "减少操作步骤"
        - "信息清晰展示"
        - "错误容错性"
      workflow_focus:
        - "体检流程优化"
        - "报告查看体验"
        - "数据录入效率"
        - "多角色权限交互"

  # UI设计师 Agent
  ui_designer:
    name: "UI设计师"
    role: "视觉设计和界面美化"
    capabilities:
      - "视觉设计"
      - "界面美化"
      - "设计系统构建"
      - "图标设计"
      - "医疗主题设计"
      - "品牌视觉统一"
    responsibilities:
      - "设计界面视觉风格"
      - "制作设计规范"
      - "创建组件库"
      - "设计医疗相关图标"
      - "优化视觉层次"
      - "确保品牌一致性"
    tools:
      - "Figma/Adobe XD"
      - "图标设计工具"
      - "色彩管理工具"
      - "设计规范工具"
    context:
      design_system:
        colors:
          primary: "#1890ff"
          success: "#52c41a"
          warning: "#faad14"
          error: "#f5222d"
          medical: "#00b96b"
        components:
          - "Ant Design Vue组件"
          - "医疗表单组件"
          - "数据展示组件"
          - "导航组件"
          - "反馈组件"
      visual_themes:
        - "专业医疗风格"
        - "清新健康色调"
        - "简约现代设计"
        - "无障碍友好"

  # 前端工程师 Agent
  frontend_engineer:
    name: "前端工程师"
    role: "前端开发和界面实现"
    capabilities:
      - "Vue3开发"
      - "TypeScript编程"
      - "Ant Design Vue组件开发"
      - "前端架构设计"
      - "性能优化"
      - "测试驱动开发"
    responsibilities:
      - "实现前端界面"
      - "开发Vue组件"
      - "集成API接口"
      - "优化前端性能"
      - "编写单元测试"
      - "维护前端架构"
    tools:
      - "Vue3 + Composition API"
      - "TypeScript"
      - "Vite构建工具"
      - "Pinia状态管理"
      - "Vue Router"
      - "Ant Design Vue"
      - "Jest测试框架"
    context:
      project_structure:
        src_path: "admin-front/src"
        components_path: "admin-front/src/components"
        views_path: "admin-front/src/views"
        api_path: "admin-front/src/api"
      key_modules:
        - "体检预约 (appointment)"
        - "基础信息 (basicinfo)"
        - "登记管理 (reg)"
        - "科室工作站 (station)"
        - "总检报告 (summary)"
        - "费用管理 (fee)"
        - "职业健康 (occu)"
      coding_standards:
        - "TypeScript严格模式"
        - "组件化开发"
        - "响应式设计"
        - "性能优化"
        - "代码复用"

  # 后端工程师 Agent
  backend_engineer:
    name: "后端工程师"
    role: "后端开发和API设计"
    capabilities:
      - "Spring Boot开发"
      - "Java 17编程"
      - "微服务架构"
      - "数据库设计"
      - "API设计"
      - "性能调优"
      - "安全防护"

  # 数据库设计师 Agent
  database_designer:
    name: "数据库设计师"
    role: "配合业务逻辑进行建表和更新表工作"
    capabilities:
      - "根据业务需求设计表结构"
      - "配合后端实体创建数据表"
      - "修改现有表结构适配业务变化"
      - "业务逻辑到数据模型映射"
      - "表关系和约束设计"
      - "数据表版本管理"
      - "业务驱动的索引优化"
    responsibilities:
      - "根据后端实体设计对应数据表"
      - "配合业务逻辑需求调整表结构"
      - "创建和修改表的SQL脚本"
      - "管理表结构变更和数据迁移"
      - "确保表设计支持业务功能"
      - "与后端工程师协作优化数据模型"
    tools:
      - "MySQL 8.0.27"
      - "MySQL Workbench"
      - "Performance Schema"
      - "pt-toolkit性能工具"
    context:
      database_info:
        host: "**************:3306"
        database: "physicalex-lkd"
        key_tables:
          - "customer - 客户信息"
          - "customer_reg - 体检登记"
          - "customer_reg_item_result - 检查结果"
          - "questionnaire_common_options - 问卷选项"
          - "icd - ICD疾病编码"
      performance_targets:
        - "查询响应时间 < 100ms"
        - "数据库可用性 > 99.9%"
        - "数据完整性 100%"
        - "备份成功率 100%"
      - "系统集成"
      - "性能优化"
      - "安全防护实现"
    tools:
      - "Spring Boot 2.7.18"
      - "MyBatis Plus"
      - "MySQL数据库"
      - "Redis缓存"
      - "Docker容器"
      - "Maven构建"
    context:
      project_structure:
        base_path: "physicalex-lkd"
        modules:
          - "jeecg-boot-base-core"
          - "jeecg-module-system"
          - "jeecg-module-physicalex"
      key_services:
        - "用户管理服务"
        - "体检业务服务"
        - "报告生成服务"
        - "数据统计服务"
        - "接口集成服务"
        - "消息通知服务"
      technical_requirements:
        - "高并发处理"
        - "数据安全"
        - "接口性能"
        - "系统稳定性"
        - "扩展性设计"

  # 测试工程师 Agent
  test_engineer:
    name: "测试工程师"
    role: "功能测试、性能测试和质量保证"
    capabilities:
      - "功能测试设计和执行"
      - "自动化测试脚本开发"
      - "性能测试和压力测试"
      - "用户体验测试"
      - "兼容性测试"
      - "安全测试"
      - "医疗系统合规性测试"
      - "回归测试管理"
    responsibilities:
      - "制定测试计划和测试用例"
      - "执行功能测试和集成测试"
      - "开发和维护自动化测试"
      - "进行性能基准测试"
      - "验证医疗数据安全性"
      - "测试报告编写和缺陷跟踪"
      - "用户验收测试协调"
      - "测试环境管理和维护"
    tools:
      - "Jest/Vitest - 单元测试"
      - "Cypress - E2E测试"
      - "Playwright - 浏览器测试"
      - "JMeter - 性能测试"
      - "Postman - API测试"
      - "SonarQube - 代码质量"
      - "JIRA - 缺陷管理"
    context:
      test_types:
        - "单元测试 - 组件级别"
        - "集成测试 - 模块间交互"
        - "系统测试 - 端到端流程"
        - "用户验收测试 - 业务场景"
        - "性能测试 - 负载和压力"
        - "安全测试 - 漏洞扫描"
      quality_standards:
        - "代码覆盖率 ≥ 80%"
        - "API响应时间 < 200ms"
        - "页面加载时间 < 3s"
        - "零关键缺陷"
        - "用户体验评分 ≥ 4.0/5"
      medical_compliance_tests:
        - "HIPAA合规性验证"
        - "患者数据隐私保护"
        - "医疗数据完整性检查"
        - "访问权限控制测试"

  # 验收工程师 Agent
  acceptance_engineer:
    name: "验收工程师"
    role: "业务验收和交付质量把关"
    capabilities:
      - "业务需求验收"
      - "用户故事确认"
      - "交付物质量评估"
      - "生产环境验证"
      - "用户培训和支持"
      - "文档完整性检查"
      - "医疗流程合规验证"
      - "最终交付确认"
    responsibilities:
      - "制定验收标准和验收计划"
      - "组织业务用户参与验收测试"
      - "验证系统满足业务需求"
      - "确认医疗工作流程的正确性"
      - "评估用户体验和易用性"
      - "审核技术文档和用户手册"
      - "协调生产环境部署"
      - "签署项目交付确认"
    tools:
      - "用户验收测试清单"
      - "业务流程验证工具"
      - "文档审核模板"
      - "用户反馈收集系统"
      - "质量评估矩阵"
    context:
      acceptance_criteria:
        - "所有业务需求100%实现"
        - "用户界面友好易用"
        - "医疗流程符合规范"
        - "数据准确性100%"
        - "系统稳定性验证"
        - "性能指标达标"
        - "安全要求满足"
      delivery_checklist:
        - "功能完整性确认"
        - "用户手册完备"
        - "培训材料准备"
        - "部署文档齐全"
        - "运维手册完整"
        - "应急预案制定"
      business_validation:
        - "职业病问诊流程验证"
        - "症状记录准确性确认"
        - "报告生成正确性检查"
        - "数据统计功能验证"
        - "权限控制有效性确认"

collaboration_workflows:
  # 需求分析流程
  requirement_analysis:
    trigger: "新需求或功能变更"
    participants: ["project_manager", "ux_designer"]
    steps:
      - "需求收集和整理"
      - "业务流程分析"
      - "用户角色定义"
      - "功能优先级评估"
      - "技术可行性分析"

  # 设计流程
  design_process:
    trigger: "需求确认后"
    participants: ["ux_designer", "ui_designer"]
    steps:
      - "用户体验设计"
      - "交互流程设计"
      - "界面视觉设计"
      - "设计规范制定"
      - "原型验证"

  # 开发流程  
  development_process:
    trigger: "设计完成后"
    participants: ["frontend_engineer", "backend_engineer", "database_designer"]
    steps:
      - "技术方案设计"
      - "数据库表结构设计"
      - "接口定义"
      - "前后端并行开发"
      - "单元测试编写"
      - "集成测试"

  # 测试流程
  testing_process:
    trigger: "开发完成后"
    participants: ["test_engineer", "frontend_engineer", "backend_engineer"]
    steps:
      - "测试用例设计"
      - "单元测试执行"
      - "集成测试执行"
      - "功能测试执行"
      - "性能测试执行"
      - "安全测试执行"
      - "缺陷修复验证"

  # 验收流程
  acceptance_process:
    trigger: "测试完成后"
    participants: ["acceptance_engineer", "project_manager", "test_engineer"]
    steps:
      - "验收标准确认"
      - "业务流程验证"
      - "用户体验评估"
      - "文档完整性检查"
      - "生产环境验证"
      - "最终交付确认"

  # 代码评审流程
  code_review:
    trigger: "功能开发完成"
    participants: ["frontend_engineer", "backend_engineer", "project_manager", "test_engineer"]
    steps:
      - "代码质量检查"
      - "安全性审查"
      - "性能评估"
      - "医疗规范合规性检查"
      - "可测试性评估"

communication_rules:
  daily_sync:
    frequency: "每日"
    participants: "all_agents"
    content: "进度同步、问题讨论、依赖协调"
  
  weekly_review:
    frequency: "每周"
    participants: "all_agents"  
    content: "里程碑回顾、风险评估、计划调整"

  milestone_review:
    frequency: "里程碑完成时"
    participants: "all_agents"
    content: "成果验收、质量评估、经验总结"

quality_standards:
  medical_compliance:
    - "患者隐私保护"
    - "医疗数据安全"
    - "操作日志记录"
    - "权限控制严格"
  
  technical_quality:
    - "代码覆盖率 > 80%"
    - "接口响应时间 < 200ms"
    - "系统可用性 > 99.9%"
    - "并发支持 > 1000用户"

  user_experience:
    - "界面响应速度"
    - "操作简便性"
    - "信息准确性"
    - "错误处理友好"