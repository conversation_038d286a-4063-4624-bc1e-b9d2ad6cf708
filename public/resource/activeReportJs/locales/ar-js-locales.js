!function(e){var t={};function a(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,a),o.l=!0,o.exports}a.m=e,a.c=t,a.d=function(e,t,r){a.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.t=function(e,t){if(1&t&&(e=a(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(a.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)a.d(r,o,function(t){return e[t]}.bind(null,o));return r},a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,"a",t),t},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.p="",a(a.s=29)}([function(e,t,a){(function(e){e.exports=function(){"use strict";var t,r;function o(){return t.apply(null,arguments)}function n(e){return e instanceof Array||"[object Array]"===Object.prototype.toString.call(e)}function s(e){return null!=e&&"[object Object]"===Object.prototype.toString.call(e)}function i(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function l(e){if(Object.getOwnPropertyNames)return 0===Object.getOwnPropertyNames(e).length;var t;for(t in e)if(i(e,t))return!1;return!0}function u(e){return void 0===e}function d(e){return"number"==typeof e||"[object Number]"===Object.prototype.toString.call(e)}function c(e){return e instanceof Date||"[object Date]"===Object.prototype.toString.call(e)}function m(e,t){var a,r=[],o=e.length;for(a=0;a<o;++a)r.push(t(e[a],a));return r}function p(e,t){for(var a in t)i(t,a)&&(e[a]=t[a]);return i(t,"toString")&&(e.toString=t.toString),i(t,"valueOf")&&(e.valueOf=t.valueOf),e}function h(e,t,a,r){return xt(e,t,a,r,!0).utc()}function g(e){return null==e._pf&&(e._pf={empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidEra:null,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],era:null,meridiem:null,rfc2822:!1,weekdayMismatch:!1}),e._pf}function _(e){var t=null,a=!1,o=e._d&&!isNaN(e._d.getTime());return o&&(t=g(e),a=r.call(t.parsedDateParts,(function(e){return null!=e})),o=t.overflow<0&&!t.empty&&!t.invalidEra&&!t.invalidMonth&&!t.invalidWeekday&&!t.weekdayMismatch&&!t.nullInput&&!t.invalidFormat&&!t.userInvalidated&&(!t.meridiem||t.meridiem&&a),e._strict&&(o=o&&0===t.charsLeftOver&&0===t.unusedTokens.length&&void 0===t.bigHour)),null!=Object.isFrozen&&Object.isFrozen(e)?o:(e._isValid=o,e._isValid)}function f(e){var t=h(NaN);return null!=e?p(g(t),e):g(t).userInvalidated=!0,t}r=Array.prototype.some?Array.prototype.some:function(e){var t,a=Object(this),r=a.length>>>0;for(t=0;t<r;t++)if(t in a&&e.call(this,a[t],t,a))return!0;return!1};var b=o.momentProperties=[],M=!1;function D(e,t){var a,r,o,n=b.length;if(u(t._isAMomentObject)||(e._isAMomentObject=t._isAMomentObject),u(t._i)||(e._i=t._i),u(t._f)||(e._f=t._f),u(t._l)||(e._l=t._l),u(t._strict)||(e._strict=t._strict),u(t._tzm)||(e._tzm=t._tzm),u(t._isUTC)||(e._isUTC=t._isUTC),u(t._offset)||(e._offset=t._offset),u(t._pf)||(e._pf=g(t)),u(t._locale)||(e._locale=t._locale),n>0)for(a=0;a<n;a++)u(o=t[r=b[a]])||(e[r]=o);return e}function y(e){D(this,e),this._d=new Date(null!=e._d?e._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),!1===M&&(M=!0,o.updateOffset(this),M=!1)}function Y(e){return e instanceof y||null!=e&&null!=e._isAMomentObject}function T(e){!1===o.suppressDeprecationWarnings&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+e)}function L(e,t){var a=!0;return p((function(){if(null!=o.deprecationHandler&&o.deprecationHandler(null,e),a){var r,n,s,l=[],u=arguments.length;for(n=0;n<u;n++){if(r="","object"==typeof arguments[n]){for(s in r+="\n["+n+"] ",arguments[0])i(arguments[0],s)&&(r+=s+": "+arguments[0][s]+", ");r=r.slice(0,-2)}else r=arguments[n];l.push(r)}T(e+"\nArguments: "+Array.prototype.slice.call(l).join("")+"\n"+(new Error).stack),a=!1}return t.apply(this,arguments)}),t)}var x,S={};function v(e,t){null!=o.deprecationHandler&&o.deprecationHandler(e,t),S[e]||(T(t),S[e]=!0)}function N(e){return"undefined"!=typeof Function&&e instanceof Function||"[object Function]"===Object.prototype.toString.call(e)}function w(e,t){var a,r=p({},e);for(a in t)i(t,a)&&(s(e[a])&&s(t[a])?(r[a]={},p(r[a],e[a]),p(r[a],t[a])):null!=t[a]?r[a]=t[a]:delete r[a]);for(a in e)i(e,a)&&!i(t,a)&&s(e[a])&&(r[a]=p({},r[a]));return r}function k(e){null!=e&&this.set(e)}function A(e,t,a){var r=""+Math.abs(e),o=t-r.length;return(e>=0?a?"+":"":"-")+Math.pow(10,Math.max(0,o)).toString().substr(1)+r}o.suppressDeprecationWarnings=!1,o.deprecationHandler=null,x=Object.keys?Object.keys:function(e){var t,a=[];for(t in e)i(e,t)&&a.push(t);return a};var P=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,H=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,W={},C={};function V(e,t,a,r){var o=r;"string"==typeof r&&(o=function(){return this[r]()}),e&&(C[e]=o),t&&(C[t[0]]=function(){return A(o.apply(this,arguments),t[1],t[2])}),a&&(C[a]=function(){return this.localeData().ordinal(o.apply(this,arguments),e)})}function E(e,t){return e.isValid()?(t=O(t,e.localeData()),W[t]=W[t]||function(e){var t,a,r,o=e.match(P);for(t=0,a=o.length;t<a;t++)C[o[t]]?o[t]=C[o[t]]:o[t]=(r=o[t]).match(/\[[\s\S]/)?r.replace(/^\[|\]$/g,""):r.replace(/\\/g,"");return function(t){var r,n="";for(r=0;r<a;r++)n+=N(o[r])?o[r].call(t,e):o[r];return n}}(t),W[t](e)):e.localeData().invalidDate()}function O(e,t){var a=5;function r(e){return t.longDateFormat(e)||e}for(H.lastIndex=0;a>=0&&H.test(e);)e=e.replace(H,r),H.lastIndex=0,a-=1;return e}var z={D:"date",dates:"date",date:"date",d:"day",days:"day",day:"day",e:"weekday",weekdays:"weekday",weekday:"weekday",E:"isoWeekday",isoweekdays:"isoWeekday",isoweekday:"isoWeekday",DDD:"dayOfYear",dayofyears:"dayOfYear",dayofyear:"dayOfYear",h:"hour",hours:"hour",hour:"hour",ms:"millisecond",milliseconds:"millisecond",millisecond:"millisecond",m:"minute",minutes:"minute",minute:"minute",M:"month",months:"month",month:"month",Q:"quarter",quarters:"quarter",quarter:"quarter",s:"second",seconds:"second",second:"second",gg:"weekYear",weekyears:"weekYear",weekyear:"weekYear",GG:"isoWeekYear",isoweekyears:"isoWeekYear",isoweekyear:"isoWeekYear",w:"week",weeks:"week",week:"week",W:"isoWeek",isoweeks:"isoWeek",isoweek:"isoWeek",y:"year",years:"year",year:"year"};function R(e){return"string"==typeof e?z[e]||z[e.toLowerCase()]:void 0}function F(e){var t,a,r={};for(a in e)i(e,a)&&(t=R(a))&&(r[t]=e[a]);return r}var j,I={date:9,day:11,weekday:11,isoWeekday:11,dayOfYear:4,hour:13,millisecond:16,minute:14,month:8,quarter:7,second:15,weekYear:1,isoWeekYear:1,week:5,isoWeek:5,year:1},Q=/\d/,U=/\d\d/,B=/\d{3}/,G=/\d{4}/,Z=/[+-]?\d{6}/,q=/\d\d?/,J=/\d\d\d\d?/,K=/\d\d\d\d\d\d?/,$=/\d{1,3}/,X=/\d{1,4}/,ee=/[+-]?\d{1,6}/,te=/\d+/,ae=/[+-]?\d+/,re=/Z|[+-]\d\d:?\d\d/gi,oe=/Z|[+-]\d\d(?::?\d\d)?/gi,ne=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i,se=/^[1-9]\d?/,ie=/^([1-9]\d|\d)/;function le(e,t,a){j[e]=N(t)?t:function(e,r){return e&&a?a:t}}function ue(e,t){return i(j,e)?j[e](t._strict,t._locale):new RegExp(de(e.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,(function(e,t,a,r,o){return t||a||r||o}))))}function de(e){return e.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function ce(e){return e<0?Math.ceil(e)||0:Math.floor(e)}function me(e){var t=+e,a=0;return 0!==t&&isFinite(t)&&(a=ce(t)),a}j={};var pe={};function he(e,t){var a,r,o=t;for("string"==typeof e&&(e=[e]),d(t)&&(o=function(e,a){a[t]=me(e)}),r=e.length,a=0;a<r;a++)pe[e[a]]=o}function ge(e,t){he(e,(function(e,a,r,o){r._w=r._w||{},t(e,r._w,r,o)}))}function _e(e,t,a){null!=t&&i(pe,e)&&pe[e](t,a._a,a,e)}function fe(e){return e%4==0&&e%100!=0||e%400==0}function be(e){return fe(e)?366:365}V("Y",0,0,(function(){var e=this.year();return e<=9999?A(e,4):"+"+e})),V(0,["YY",2],0,(function(){return this.year()%100})),V(0,["YYYY",4],0,"year"),V(0,["YYYYY",5],0,"year"),V(0,["YYYYYY",6,!0],0,"year"),le("Y",ae),le("YY",q,U),le("YYYY",X,G),le("YYYYY",ee,Z),le("YYYYYY",ee,Z),he(["YYYYY","YYYYYY"],0),he("YYYY",(function(e,t){t[0]=2===e.length?o.parseTwoDigitYear(e):me(e)})),he("YY",(function(e,t){t[0]=o.parseTwoDigitYear(e)})),he("Y",(function(e,t){t[0]=parseInt(e,10)})),o.parseTwoDigitYear=function(e){return me(e)+(me(e)>68?1900:2e3)};var Me,De=ye("FullYear",!0);function ye(e,t){return function(a){return null!=a?(Te(this,e,a),o.updateOffset(this,t),this):Ye(this,e)}}function Ye(e,t){if(!e.isValid())return NaN;var a=e._d,r=e._isUTC;switch(t){case"Milliseconds":return r?a.getUTCMilliseconds():a.getMilliseconds();case"Seconds":return r?a.getUTCSeconds():a.getSeconds();case"Minutes":return r?a.getUTCMinutes():a.getMinutes();case"Hours":return r?a.getUTCHours():a.getHours();case"Date":return r?a.getUTCDate():a.getDate();case"Day":return r?a.getUTCDay():a.getDay();case"Month":return r?a.getUTCMonth():a.getMonth();case"FullYear":return r?a.getUTCFullYear():a.getFullYear();default:return NaN}}function Te(e,t,a){var r,o,n,s,i;if(e.isValid()&&!isNaN(a)){switch(r=e._d,o=e._isUTC,t){case"Milliseconds":return void(o?r.setUTCMilliseconds(a):r.setMilliseconds(a));case"Seconds":return void(o?r.setUTCSeconds(a):r.setSeconds(a));case"Minutes":return void(o?r.setUTCMinutes(a):r.setMinutes(a));case"Hours":return void(o?r.setUTCHours(a):r.setHours(a));case"Date":return void(o?r.setUTCDate(a):r.setDate(a));case"FullYear":break;default:return}n=a,s=e.month(),i=29!==(i=e.date())||1!==s||fe(n)?i:28,o?r.setUTCFullYear(n,s,i):r.setFullYear(n,s,i)}}function Le(e,t){if(isNaN(e)||isNaN(t))return NaN;var a,r=(t%(a=12)+a)%a;return e+=(t-r)/12,1===r?fe(e)?29:28:31-r%7%2}Me=Array.prototype.indexOf?Array.prototype.indexOf:function(e){var t;for(t=0;t<this.length;++t)if(this[t]===e)return t;return-1},V("M",["MM",2],"Mo",(function(){return this.month()+1})),V("MMM",0,0,(function(e){return this.localeData().monthsShort(this,e)})),V("MMMM",0,0,(function(e){return this.localeData().months(this,e)})),le("M",q,se),le("MM",q,U),le("MMM",(function(e,t){return t.monthsShortRegex(e)})),le("MMMM",(function(e,t){return t.monthsRegex(e)})),he(["M","MM"],(function(e,t){t[1]=me(e)-1})),he(["MMM","MMMM"],(function(e,t,a,r){var o=a._locale.monthsParse(e,r,a._strict);null!=o?t[1]=o:g(a).invalidMonth=e}));var xe="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),Se="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),ve=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,Ne=ne,we=ne;function ke(e,t,a){var r,o,n,s=e.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],r=0;r<12;++r)n=h([2e3,r]),this._shortMonthsParse[r]=this.monthsShort(n,"").toLocaleLowerCase(),this._longMonthsParse[r]=this.months(n,"").toLocaleLowerCase();return a?"MMM"===t?-1!==(o=Me.call(this._shortMonthsParse,s))?o:null:-1!==(o=Me.call(this._longMonthsParse,s))?o:null:"MMM"===t?-1!==(o=Me.call(this._shortMonthsParse,s))||-1!==(o=Me.call(this._longMonthsParse,s))?o:null:-1!==(o=Me.call(this._longMonthsParse,s))||-1!==(o=Me.call(this._shortMonthsParse,s))?o:null}function Ae(e,t){if(!e.isValid())return e;if("string"==typeof t)if(/^\d+$/.test(t))t=me(t);else if(!d(t=e.localeData().monthsParse(t)))return e;var a=t,r=e.date();return r=r<29?r:Math.min(r,Le(e.year(),a)),e._isUTC?e._d.setUTCMonth(a,r):e._d.setMonth(a,r),e}function Pe(e){return null!=e?(Ae(this,e),o.updateOffset(this,!0),this):Ye(this,"Month")}function He(){function e(e,t){return t.length-e.length}var t,a,r,o,n=[],s=[],i=[];for(t=0;t<12;t++)a=h([2e3,t]),r=de(this.monthsShort(a,"")),o=de(this.months(a,"")),n.push(r),s.push(o),i.push(o),i.push(r);n.sort(e),s.sort(e),i.sort(e),this._monthsRegex=new RegExp("^("+i.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+s.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+n.join("|")+")","i")}function We(e,t,a,r,o,n,s){var i;return e<100&&e>=0?(i=new Date(e+400,t,a,r,o,n,s),isFinite(i.getFullYear())&&i.setFullYear(e)):i=new Date(e,t,a,r,o,n,s),i}function Ce(e){var t,a;return e<100&&e>=0?((a=Array.prototype.slice.call(arguments))[0]=e+400,t=new Date(Date.UTC.apply(null,a)),isFinite(t.getUTCFullYear())&&t.setUTCFullYear(e)):t=new Date(Date.UTC.apply(null,arguments)),t}function Ve(e,t,a){var r=7+t-a;return-(7+Ce(e,0,r).getUTCDay()-t)%7+r-1}function Ee(e,t,a,r,o){var n,s,i=1+7*(t-1)+(7+a-r)%7+Ve(e,r,o);return i<=0?s=be(n=e-1)+i:i>be(e)?(n=e+1,s=i-be(e)):(n=e,s=i),{year:n,dayOfYear:s}}function Oe(e,t,a){var r,o,n=Ve(e.year(),t,a),s=Math.floor((e.dayOfYear()-n-1)/7)+1;return s<1?r=s+ze(o=e.year()-1,t,a):s>ze(e.year(),t,a)?(r=s-ze(e.year(),t,a),o=e.year()+1):(o=e.year(),r=s),{week:r,year:o}}function ze(e,t,a){var r=Ve(e,t,a),o=Ve(e+1,t,a);return(be(e)-r+o)/7}function Re(e,t){return e.slice(t,7).concat(e.slice(0,t))}V("w",["ww",2],"wo","week"),V("W",["WW",2],"Wo","isoWeek"),le("w",q,se),le("ww",q,U),le("W",q,se),le("WW",q,U),ge(["w","ww","W","WW"],(function(e,t,a,r){t[r.substr(0,1)]=me(e)})),V("d",0,"do","day"),V("dd",0,0,(function(e){return this.localeData().weekdaysMin(this,e)})),V("ddd",0,0,(function(e){return this.localeData().weekdaysShort(this,e)})),V("dddd",0,0,(function(e){return this.localeData().weekdays(this,e)})),V("e",0,0,"weekday"),V("E",0,0,"isoWeekday"),le("d",q),le("e",q),le("E",q),le("dd",(function(e,t){return t.weekdaysMinRegex(e)})),le("ddd",(function(e,t){return t.weekdaysShortRegex(e)})),le("dddd",(function(e,t){return t.weekdaysRegex(e)})),ge(["dd","ddd","dddd"],(function(e,t,a,r){var o=a._locale.weekdaysParse(e,r,a._strict);null!=o?t.d=o:g(a).invalidWeekday=e})),ge(["d","e","E"],(function(e,t,a,r){t[r]=me(e)}));var Fe="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),je="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),Ie="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),Qe=ne,Ue=ne,Be=ne;function Ge(e,t,a){var r,o,n,s=e.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],r=0;r<7;++r)n=h([2e3,1]).day(r),this._minWeekdaysParse[r]=this.weekdaysMin(n,"").toLocaleLowerCase(),this._shortWeekdaysParse[r]=this.weekdaysShort(n,"").toLocaleLowerCase(),this._weekdaysParse[r]=this.weekdays(n,"").toLocaleLowerCase();return a?"dddd"===t?-1!==(o=Me.call(this._weekdaysParse,s))?o:null:"ddd"===t?-1!==(o=Me.call(this._shortWeekdaysParse,s))?o:null:-1!==(o=Me.call(this._minWeekdaysParse,s))?o:null:"dddd"===t?-1!==(o=Me.call(this._weekdaysParse,s))||-1!==(o=Me.call(this._shortWeekdaysParse,s))||-1!==(o=Me.call(this._minWeekdaysParse,s))?o:null:"ddd"===t?-1!==(o=Me.call(this._shortWeekdaysParse,s))||-1!==(o=Me.call(this._weekdaysParse,s))||-1!==(o=Me.call(this._minWeekdaysParse,s))?o:null:-1!==(o=Me.call(this._minWeekdaysParse,s))||-1!==(o=Me.call(this._weekdaysParse,s))||-1!==(o=Me.call(this._shortWeekdaysParse,s))?o:null}function Ze(){function e(e,t){return t.length-e.length}var t,a,r,o,n,s=[],i=[],l=[],u=[];for(t=0;t<7;t++)a=h([2e3,1]).day(t),r=de(this.weekdaysMin(a,"")),o=de(this.weekdaysShort(a,"")),n=de(this.weekdays(a,"")),s.push(r),i.push(o),l.push(n),u.push(r),u.push(o),u.push(n);s.sort(e),i.sort(e),l.sort(e),u.sort(e),this._weekdaysRegex=new RegExp("^("+u.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+l.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+i.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+s.join("|")+")","i")}function qe(){return this.hours()%12||12}function Je(e,t){V(e,0,0,(function(){return this.localeData().meridiem(this.hours(),this.minutes(),t)}))}function Ke(e,t){return t._meridiemParse}V("H",["HH",2],0,"hour"),V("h",["hh",2],0,qe),V("k",["kk",2],0,(function(){return this.hours()||24})),V("hmm",0,0,(function(){return""+qe.apply(this)+A(this.minutes(),2)})),V("hmmss",0,0,(function(){return""+qe.apply(this)+A(this.minutes(),2)+A(this.seconds(),2)})),V("Hmm",0,0,(function(){return""+this.hours()+A(this.minutes(),2)})),V("Hmmss",0,0,(function(){return""+this.hours()+A(this.minutes(),2)+A(this.seconds(),2)})),Je("a",!0),Je("A",!1),le("a",Ke),le("A",Ke),le("H",q,ie),le("h",q,se),le("k",q,se),le("HH",q,U),le("hh",q,U),le("kk",q,U),le("hmm",J),le("hmmss",K),le("Hmm",J),le("Hmmss",K),he(["H","HH"],3),he(["k","kk"],(function(e,t,a){var r=me(e);t[3]=24===r?0:r})),he(["a","A"],(function(e,t,a){a._isPm=a._locale.isPM(e),a._meridiem=e})),he(["h","hh"],(function(e,t,a){t[3]=me(e),g(a).bigHour=!0})),he("hmm",(function(e,t,a){var r=e.length-2;t[3]=me(e.substr(0,r)),t[4]=me(e.substr(r)),g(a).bigHour=!0})),he("hmmss",(function(e,t,a){var r=e.length-4,o=e.length-2;t[3]=me(e.substr(0,r)),t[4]=me(e.substr(r,2)),t[5]=me(e.substr(o)),g(a).bigHour=!0})),he("Hmm",(function(e,t,a){var r=e.length-2;t[3]=me(e.substr(0,r)),t[4]=me(e.substr(r))})),he("Hmmss",(function(e,t,a){var r=e.length-4,o=e.length-2;t[3]=me(e.substr(0,r)),t[4]=me(e.substr(r,2)),t[5]=me(e.substr(o))}));var $e,Xe=ye("Hours",!0),et={calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},longDateFormat:{LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},invalidDate:"Invalid date",ordinal:"%d",dayOfMonthOrdinalParse:/\d{1,2}/,relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",w:"a week",ww:"%d weeks",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},months:xe,monthsShort:Se,week:{dow:0,doy:6},weekdays:Fe,weekdaysMin:Ie,weekdaysShort:je,meridiemParse:/[ap]\.?m?\.?/i},tt={},at={};function rt(e,t){var a,r=Math.min(e.length,t.length);for(a=0;a<r;a+=1)if(e[a]!==t[a])return a;return r}function ot(e){return e?e.toLowerCase().replace("_","-"):e}function nt(t){var r=null;if(void 0===tt[t]&&void 0!==e&&e&&e.exports&&function(e){return!(!e||!e.match("^[^/\\\\]*$"))}(t))try{r=$e._abbr,a(31)("./"+t),st(r)}catch(e){tt[t]=null}return tt[t]}function st(e,t){var a;return e&&((a=u(t)?lt(e):it(e,t))?$e=a:"undefined"!=typeof console&&console.warn&&console.warn("Locale "+e+" not found. Did you forget to load it?")),$e._abbr}function it(e,t){if(null!==t){var a,r=et;if(t.abbr=e,null!=tt[e])v("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),r=tt[e]._config;else if(null!=t.parentLocale)if(null!=tt[t.parentLocale])r=tt[t.parentLocale]._config;else{if(null==(a=nt(t.parentLocale)))return at[t.parentLocale]||(at[t.parentLocale]=[]),at[t.parentLocale].push({name:e,config:t}),null;r=a._config}return tt[e]=new k(w(r,t)),at[e]&&at[e].forEach((function(e){it(e.name,e.config)})),st(e),tt[e]}return delete tt[e],null}function lt(e){var t;if(e&&e._locale&&e._locale._abbr&&(e=e._locale._abbr),!e)return $e;if(!n(e)){if(t=nt(e))return t;e=[e]}return function(e){for(var t,a,r,o,n=0;n<e.length;){for(t=(o=ot(e[n]).split("-")).length,a=(a=ot(e[n+1]))?a.split("-"):null;t>0;){if(r=nt(o.slice(0,t).join("-")))return r;if(a&&a.length>=t&&rt(o,a)>=t-1)break;t--}n++}return $e}(e)}function ut(e){var t,a=e._a;return a&&-2===g(e).overflow&&(t=a[1]<0||a[1]>11?1:a[2]<1||a[2]>Le(a[0],a[1])?2:a[3]<0||a[3]>24||24===a[3]&&(0!==a[4]||0!==a[5]||0!==a[6])?3:a[4]<0||a[4]>59?4:a[5]<0||a[5]>59?5:a[6]<0||a[6]>999?6:-1,g(e)._overflowDayOfYear&&(t<0||t>2)&&(t=2),g(e)._overflowWeeks&&-1===t&&(t=7),g(e)._overflowWeekday&&-1===t&&(t=8),g(e).overflow=t),e}var dt=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,ct=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d|))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,mt=/Z|[+-]\d\d(?::?\d\d)?/,pt=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/],["YYYYMM",/\d{6}/,!1],["YYYY",/\d{4}/,!1]],ht=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],gt=/^\/?Date\((-?\d+)/i,_t=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/,ft={UT:0,GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function bt(e){var t,a,r,o,n,s,i=e._i,l=dt.exec(i)||ct.exec(i),u=pt.length,d=ht.length;if(l){for(g(e).iso=!0,t=0,a=u;t<a;t++)if(pt[t][1].exec(l[1])){o=pt[t][0],r=!1!==pt[t][2];break}if(null==o)return void(e._isValid=!1);if(l[3]){for(t=0,a=d;t<a;t++)if(ht[t][1].exec(l[3])){n=(l[2]||" ")+ht[t][0];break}if(null==n)return void(e._isValid=!1)}if(!r&&null!=n)return void(e._isValid=!1);if(l[4]){if(!mt.exec(l[4]))return void(e._isValid=!1);s="Z"}e._f=o+(n||"")+(s||""),Tt(e)}else e._isValid=!1}function Mt(e){var t=parseInt(e,10);return t<=49?2e3+t:t<=999?1900+t:t}function Dt(e){var t,a,r,o,n,s,i,l,u=_t.exec(e._i.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,""));if(u){if(a=u[4],r=u[3],o=u[2],n=u[5],s=u[6],i=u[7],l=[Mt(a),Se.indexOf(r),parseInt(o,10),parseInt(n,10),parseInt(s,10)],i&&l.push(parseInt(i,10)),t=l,!function(e,t,a){return!e||je.indexOf(e)===new Date(t[0],t[1],t[2]).getDay()||(g(a).weekdayMismatch=!0,a._isValid=!1,!1)}(u[1],t,e))return;e._a=t,e._tzm=function(e,t,a){if(e)return ft[e];if(t)return 0;var r=parseInt(a,10),o=r%100;return(r-o)/100*60+o}(u[8],u[9],u[10]),e._d=Ce.apply(null,e._a),e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),g(e).rfc2822=!0}else e._isValid=!1}function yt(e,t,a){return null!=e?e:null!=t?t:a}function Yt(e){var t,a,r,n,s,i=[];if(!e._d){for(r=function(e){var t=new Date(o.now());return e._useUTC?[t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate()]:[t.getFullYear(),t.getMonth(),t.getDate()]}(e),e._w&&null==e._a[2]&&null==e._a[1]&&function(e){var t,a,r,o,n,s,i,l,u;null!=(t=e._w).GG||null!=t.W||null!=t.E?(n=1,s=4,a=yt(t.GG,e._a[0],Oe(St(),1,4).year),r=yt(t.W,1),((o=yt(t.E,1))<1||o>7)&&(l=!0)):(n=e._locale._week.dow,s=e._locale._week.doy,u=Oe(St(),n,s),a=yt(t.gg,e._a[0],u.year),r=yt(t.w,u.week),null!=t.d?((o=t.d)<0||o>6)&&(l=!0):null!=t.e?(o=t.e+n,(t.e<0||t.e>6)&&(l=!0)):o=n),r<1||r>ze(a,n,s)?g(e)._overflowWeeks=!0:null!=l?g(e)._overflowWeekday=!0:(i=Ee(a,r,o,n,s),e._a[0]=i.year,e._dayOfYear=i.dayOfYear)}(e),null!=e._dayOfYear&&(s=yt(e._a[0],r[0]),(e._dayOfYear>be(s)||0===e._dayOfYear)&&(g(e)._overflowDayOfYear=!0),a=Ce(s,0,e._dayOfYear),e._a[1]=a.getUTCMonth(),e._a[2]=a.getUTCDate()),t=0;t<3&&null==e._a[t];++t)e._a[t]=i[t]=r[t];for(;t<7;t++)e._a[t]=i[t]=null==e._a[t]?2===t?1:0:e._a[t];24===e._a[3]&&0===e._a[4]&&0===e._a[5]&&0===e._a[6]&&(e._nextDay=!0,e._a[3]=0),e._d=(e._useUTC?Ce:We).apply(null,i),n=e._useUTC?e._d.getUTCDay():e._d.getDay(),null!=e._tzm&&e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),e._nextDay&&(e._a[3]=24),e._w&&void 0!==e._w.d&&e._w.d!==n&&(g(e).weekdayMismatch=!0)}}function Tt(e){if(e._f!==o.ISO_8601)if(e._f!==o.RFC_2822){e._a=[],g(e).empty=!0;var t,a,r,n,s,i,l,u=""+e._i,d=u.length,c=0;for(l=(r=O(e._f,e._locale).match(P)||[]).length,t=0;t<l;t++)n=r[t],(a=(u.match(ue(n,e))||[])[0])&&((s=u.substr(0,u.indexOf(a))).length>0&&g(e).unusedInput.push(s),u=u.slice(u.indexOf(a)+a.length),c+=a.length),C[n]?(a?g(e).empty=!1:g(e).unusedTokens.push(n),_e(n,a,e)):e._strict&&!a&&g(e).unusedTokens.push(n);g(e).charsLeftOver=d-c,u.length>0&&g(e).unusedInput.push(u),e._a[3]<=12&&!0===g(e).bigHour&&e._a[3]>0&&(g(e).bigHour=void 0),g(e).parsedDateParts=e._a.slice(0),g(e).meridiem=e._meridiem,e._a[3]=function(e,t,a){var r;return null==a?t:null!=e.meridiemHour?e.meridiemHour(t,a):null!=e.isPM?((r=e.isPM(a))&&t<12&&(t+=12),r||12!==t||(t=0),t):t}(e._locale,e._a[3],e._meridiem),null!==(i=g(e).era)&&(e._a[0]=e._locale.erasConvertYear(i,e._a[0])),Yt(e),ut(e)}else Dt(e);else bt(e)}function Lt(e){var t=e._i,a=e._f;return e._locale=e._locale||lt(e._l),null===t||void 0===a&&""===t?f({nullInput:!0}):("string"==typeof t&&(e._i=t=e._locale.preparse(t)),Y(t)?new y(ut(t)):(c(t)?e._d=t:n(a)?function(e){var t,a,r,o,n,s,i=!1,l=e._f.length;if(0===l)return g(e).invalidFormat=!0,void(e._d=new Date(NaN));for(o=0;o<l;o++)n=0,s=!1,t=D({},e),null!=e._useUTC&&(t._useUTC=e._useUTC),t._f=e._f[o],Tt(t),_(t)&&(s=!0),n+=g(t).charsLeftOver,n+=10*g(t).unusedTokens.length,g(t).score=n,i?n<r&&(r=n,a=t):(null==r||n<r||s)&&(r=n,a=t,s&&(i=!0));p(e,a||t)}(e):a?Tt(e):function(e){var t=e._i;u(t)?e._d=new Date(o.now()):c(t)?e._d=new Date(t.valueOf()):"string"==typeof t?function(e){var t=gt.exec(e._i);null===t?(bt(e),!1===e._isValid&&(delete e._isValid,Dt(e),!1===e._isValid&&(delete e._isValid,e._strict?e._isValid=!1:o.createFromInputFallback(e)))):e._d=new Date(+t[1])}(e):n(t)?(e._a=m(t.slice(0),(function(e){return parseInt(e,10)})),Yt(e)):s(t)?function(e){if(!e._d){var t=F(e._i),a=void 0===t.day?t.date:t.day;e._a=m([t.year,t.month,a,t.hour,t.minute,t.second,t.millisecond],(function(e){return e&&parseInt(e,10)})),Yt(e)}}(e):d(t)?e._d=new Date(t):o.createFromInputFallback(e)}(e),_(e)||(e._d=null),e))}function xt(e,t,a,r,o){var i,u={};return!0!==t&&!1!==t||(r=t,t=void 0),!0!==a&&!1!==a||(r=a,a=void 0),(s(e)&&l(e)||n(e)&&0===e.length)&&(e=void 0),u._isAMomentObject=!0,u._useUTC=u._isUTC=o,u._l=a,u._i=e,u._f=t,u._strict=r,(i=new y(ut(Lt(u))))._nextDay&&(i.add(1,"d"),i._nextDay=void 0),i}function St(e,t,a,r){return xt(e,t,a,r,!1)}o.createFromInputFallback=L("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",(function(e){e._d=new Date(e._i+(e._useUTC?" UTC":""))})),o.ISO_8601=function(){},o.RFC_2822=function(){};var vt=L("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",(function(){var e=St.apply(null,arguments);return this.isValid()&&e.isValid()?e<this?this:e:f()})),Nt=L("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",(function(){var e=St.apply(null,arguments);return this.isValid()&&e.isValid()?e>this?this:e:f()}));function wt(e,t){var a,r;if(1===t.length&&n(t[0])&&(t=t[0]),!t.length)return St();for(a=t[0],r=1;r<t.length;++r)t[r].isValid()&&!t[r][e](a)||(a=t[r]);return a}var kt=["year","quarter","month","week","day","hour","minute","second","millisecond"];function At(e){var t=F(e),a=t.year||0,r=t.quarter||0,o=t.month||0,n=t.week||t.isoWeek||0,s=t.day||0,l=t.hour||0,u=t.minute||0,d=t.second||0,c=t.millisecond||0;this._isValid=function(e){var t,a,r=!1,o=kt.length;for(t in e)if(i(e,t)&&(-1===Me.call(kt,t)||null!=e[t]&&isNaN(e[t])))return!1;for(a=0;a<o;++a)if(e[kt[a]]){if(r)return!1;parseFloat(e[kt[a]])!==me(e[kt[a]])&&(r=!0)}return!0}(t),this._milliseconds=+c+1e3*d+6e4*u+1e3*l*60*60,this._days=+s+7*n,this._months=+o+3*r+12*a,this._data={},this._locale=lt(),this._bubble()}function Pt(e){return e instanceof At}function Ht(e){return e<0?-1*Math.round(-1*e):Math.round(e)}function Wt(e,t){V(e,0,0,(function(){var e=this.utcOffset(),a="+";return e<0&&(e=-e,a="-"),a+A(~~(e/60),2)+t+A(~~e%60,2)}))}Wt("Z",":"),Wt("ZZ",""),le("Z",oe),le("ZZ",oe),he(["Z","ZZ"],(function(e,t,a){a._useUTC=!0,a._tzm=Vt(oe,e)}));var Ct=/([\+\-]|\d\d)/gi;function Vt(e,t){var a,r,o=(t||"").match(e);return null===o?null:0===(r=60*(a=((o[o.length-1]||[])+"").match(Ct)||["-",0,0])[1]+me(a[2]))?0:"+"===a[0]?r:-r}function Et(e,t){var a,r;return t._isUTC?(a=t.clone(),r=(Y(e)||c(e)?e.valueOf():St(e).valueOf())-a.valueOf(),a._d.setTime(a._d.valueOf()+r),o.updateOffset(a,!1),a):St(e).local()}function Ot(e){return-Math.round(e._d.getTimezoneOffset())}function zt(){return!!this.isValid()&&this._isUTC&&0===this._offset}o.updateOffset=function(){};var Rt=/^(-|\+)?(?:(\d*)[. ])?(\d+):(\d+)(?::(\d+)(\.\d*)?)?$/,Ft=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;function jt(e,t){var a,r,o,n,s,l,u=e,c=null;return Pt(e)?u={ms:e._milliseconds,d:e._days,M:e._months}:d(e)||!isNaN(+e)?(u={},t?u[t]=+e:u.milliseconds=+e):(c=Rt.exec(e))?(a="-"===c[1]?-1:1,u={y:0,d:me(c[2])*a,h:me(c[3])*a,m:me(c[4])*a,s:me(c[5])*a,ms:me(Ht(1e3*c[6]))*a}):(c=Ft.exec(e))?(a="-"===c[1]?-1:1,u={y:It(c[2],a),M:It(c[3],a),w:It(c[4],a),d:It(c[5],a),h:It(c[6],a),m:It(c[7],a),s:It(c[8],a)}):null==u?u={}:"object"==typeof u&&("from"in u||"to"in u)&&(n=St(u.from),s=St(u.to),o=n.isValid()&&s.isValid()?(s=Et(s,n),n.isBefore(s)?l=Qt(n,s):((l=Qt(s,n)).milliseconds=-l.milliseconds,l.months=-l.months),l):{milliseconds:0,months:0},(u={}).ms=o.milliseconds,u.M=o.months),r=new At(u),Pt(e)&&i(e,"_locale")&&(r._locale=e._locale),Pt(e)&&i(e,"_isValid")&&(r._isValid=e._isValid),r}function It(e,t){var a=e&&parseFloat(e.replace(",","."));return(isNaN(a)?0:a)*t}function Qt(e,t){var a={};return a.months=t.month()-e.month()+12*(t.year()-e.year()),e.clone().add(a.months,"M").isAfter(t)&&--a.months,a.milliseconds=+t-+e.clone().add(a.months,"M"),a}function Ut(e,t){return function(a,r){var o;return null===r||isNaN(+r)||(v(t,"moment()."+t+"(period, number) is deprecated. Please use moment()."+t+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),o=a,a=r,r=o),Bt(this,jt(a,r),e),this}}function Bt(e,t,a,r){var n=t._milliseconds,s=Ht(t._days),i=Ht(t._months);e.isValid()&&(r=null==r||r,i&&Ae(e,Ye(e,"Month")+i*a),s&&Te(e,"Date",Ye(e,"Date")+s*a),n&&e._d.setTime(e._d.valueOf()+n*a),r&&o.updateOffset(e,s||i))}jt.fn=At.prototype,jt.invalid=function(){return jt(NaN)};var Gt=Ut(1,"add"),Zt=Ut(-1,"subtract");function qt(e){return"string"==typeof e||e instanceof String}function Jt(e){return Y(e)||c(e)||qt(e)||d(e)||function(e){var t=n(e),a=!1;return t&&(a=0===e.filter((function(t){return!d(t)&&qt(e)})).length),t&&a}(e)||function(e){var t,a,r=s(e)&&!l(e),o=!1,n=["years","year","y","months","month","M","days","day","d","dates","date","D","hours","hour","h","minutes","minute","m","seconds","second","s","milliseconds","millisecond","ms"],u=n.length;for(t=0;t<u;t+=1)a=n[t],o=o||i(e,a);return r&&o}(e)||null==e}function Kt(e){var t,a=s(e)&&!l(e),r=!1,o=["sameDay","nextDay","lastDay","nextWeek","lastWeek","sameElse"];for(t=0;t<o.length;t+=1)r=r||i(e,o[t]);return a&&r}function $t(e,t){if(e.date()<t.date())return-$t(t,e);var a=12*(t.year()-e.year())+(t.month()-e.month()),r=e.clone().add(a,"months");return-(a+(t-r<0?(t-r)/(r-e.clone().add(a-1,"months")):(t-r)/(e.clone().add(a+1,"months")-r)))||0}function Xt(e){var t;return void 0===e?this._locale._abbr:(null!=(t=lt(e))&&(this._locale=t),this)}o.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",o.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";var ea=L("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",(function(e){return void 0===e?this.localeData():this.locale(e)}));function ta(){return this._locale}function aa(e,t){return(e%t+t)%t}function ra(e,t,a){return e<100&&e>=0?new Date(e+400,t,a)-126227808e5:new Date(e,t,a).valueOf()}function oa(e,t,a){return e<100&&e>=0?Date.UTC(e+400,t,a)-126227808e5:Date.UTC(e,t,a)}function na(e,t){return t.erasAbbrRegex(e)}function sa(){var e,t,a,r,o,n=[],s=[],i=[],l=[],u=this.eras();for(e=0,t=u.length;e<t;++e)a=de(u[e].name),r=de(u[e].abbr),o=de(u[e].narrow),s.push(a),n.push(r),i.push(o),l.push(a),l.push(r),l.push(o);this._erasRegex=new RegExp("^("+l.join("|")+")","i"),this._erasNameRegex=new RegExp("^("+s.join("|")+")","i"),this._erasAbbrRegex=new RegExp("^("+n.join("|")+")","i"),this._erasNarrowRegex=new RegExp("^("+i.join("|")+")","i")}function ia(e,t){V(0,[e,e.length],0,t)}function la(e,t,a,r,o){var n;return null==e?Oe(this,r,o).year:(t>(n=ze(e,r,o))&&(t=n),ua.call(this,e,t,a,r,o))}function ua(e,t,a,r,o){var n=Ee(e,t,a,r,o),s=Ce(n.year,0,n.dayOfYear);return this.year(s.getUTCFullYear()),this.month(s.getUTCMonth()),this.date(s.getUTCDate()),this}V("N",0,0,"eraAbbr"),V("NN",0,0,"eraAbbr"),V("NNN",0,0,"eraAbbr"),V("NNNN",0,0,"eraName"),V("NNNNN",0,0,"eraNarrow"),V("y",["y",1],"yo","eraYear"),V("y",["yy",2],0,"eraYear"),V("y",["yyy",3],0,"eraYear"),V("y",["yyyy",4],0,"eraYear"),le("N",na),le("NN",na),le("NNN",na),le("NNNN",(function(e,t){return t.erasNameRegex(e)})),le("NNNNN",(function(e,t){return t.erasNarrowRegex(e)})),he(["N","NN","NNN","NNNN","NNNNN"],(function(e,t,a,r){var o=a._locale.erasParse(e,r,a._strict);o?g(a).era=o:g(a).invalidEra=e})),le("y",te),le("yy",te),le("yyy",te),le("yyyy",te),le("yo",(function(e,t){return t._eraYearOrdinalRegex||te})),he(["y","yy","yyy","yyyy"],0),he(["yo"],(function(e,t,a,r){var o;a._locale._eraYearOrdinalRegex&&(o=e.match(a._locale._eraYearOrdinalRegex)),a._locale.eraYearOrdinalParse?t[0]=a._locale.eraYearOrdinalParse(e,o):t[0]=parseInt(e,10)})),V(0,["gg",2],0,(function(){return this.weekYear()%100})),V(0,["GG",2],0,(function(){return this.isoWeekYear()%100})),ia("gggg","weekYear"),ia("ggggg","weekYear"),ia("GGGG","isoWeekYear"),ia("GGGGG","isoWeekYear"),le("G",ae),le("g",ae),le("GG",q,U),le("gg",q,U),le("GGGG",X,G),le("gggg",X,G),le("GGGGG",ee,Z),le("ggggg",ee,Z),ge(["gggg","ggggg","GGGG","GGGGG"],(function(e,t,a,r){t[r.substr(0,2)]=me(e)})),ge(["gg","GG"],(function(e,t,a,r){t[r]=o.parseTwoDigitYear(e)})),V("Q",0,"Qo","quarter"),le("Q",Q),he("Q",(function(e,t){t[1]=3*(me(e)-1)})),V("D",["DD",2],"Do","date"),le("D",q,se),le("DD",q,U),le("Do",(function(e,t){return e?t._dayOfMonthOrdinalParse||t._ordinalParse:t._dayOfMonthOrdinalParseLenient})),he(["D","DD"],2),he("Do",(function(e,t){t[2]=me(e.match(q)[0])}));var da=ye("Date",!0);V("DDD",["DDDD",3],"DDDo","dayOfYear"),le("DDD",$),le("DDDD",B),he(["DDD","DDDD"],(function(e,t,a){a._dayOfYear=me(e)})),V("m",["mm",2],0,"minute"),le("m",q,ie),le("mm",q,U),he(["m","mm"],4);var ca=ye("Minutes",!1);V("s",["ss",2],0,"second"),le("s",q,ie),le("ss",q,U),he(["s","ss"],5);var ma,pa,ha=ye("Seconds",!1);for(V("S",0,0,(function(){return~~(this.millisecond()/100)})),V(0,["SS",2],0,(function(){return~~(this.millisecond()/10)})),V(0,["SSS",3],0,"millisecond"),V(0,["SSSS",4],0,(function(){return 10*this.millisecond()})),V(0,["SSSSS",5],0,(function(){return 100*this.millisecond()})),V(0,["SSSSSS",6],0,(function(){return 1e3*this.millisecond()})),V(0,["SSSSSSS",7],0,(function(){return 1e4*this.millisecond()})),V(0,["SSSSSSSS",8],0,(function(){return 1e5*this.millisecond()})),V(0,["SSSSSSSSS",9],0,(function(){return 1e6*this.millisecond()})),le("S",$,Q),le("SS",$,U),le("SSS",$,B),ma="SSSS";ma.length<=9;ma+="S")le(ma,te);function ga(e,t){t[6]=me(1e3*("0."+e))}for(ma="S";ma.length<=9;ma+="S")he(ma,ga);pa=ye("Milliseconds",!1),V("z",0,0,"zoneAbbr"),V("zz",0,0,"zoneName");var _a=y.prototype;function fa(e){return e}_a.add=Gt,_a.calendar=function(e,t){1===arguments.length&&(arguments[0]?Jt(arguments[0])?(e=arguments[0],t=void 0):Kt(arguments[0])&&(t=arguments[0],e=void 0):(e=void 0,t=void 0));var a=e||St(),r=Et(a,this).startOf("day"),n=o.calendarFormat(this,r)||"sameElse",s=t&&(N(t[n])?t[n].call(this,a):t[n]);return this.format(s||this.localeData().calendar(n,this,St(a)))},_a.clone=function(){return new y(this)},_a.diff=function(e,t,a){var r,o,n;if(!this.isValid())return NaN;if(!(r=Et(e,this)).isValid())return NaN;switch(o=6e4*(r.utcOffset()-this.utcOffset()),t=R(t)){case"year":n=$t(this,r)/12;break;case"month":n=$t(this,r);break;case"quarter":n=$t(this,r)/3;break;case"second":n=(this-r)/1e3;break;case"minute":n=(this-r)/6e4;break;case"hour":n=(this-r)/36e5;break;case"day":n=(this-r-o)/864e5;break;case"week":n=(this-r-o)/6048e5;break;default:n=this-r}return a?n:ce(n)},_a.endOf=function(e){var t,a;if(void 0===(e=R(e))||"millisecond"===e||!this.isValid())return this;switch(a=this._isUTC?oa:ra,e){case"year":t=a(this.year()+1,0,1)-1;break;case"quarter":t=a(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":t=a(this.year(),this.month()+1,1)-1;break;case"week":t=a(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":t=a(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":t=a(this.year(),this.month(),this.date()+1)-1;break;case"hour":t=this._d.valueOf(),t+=36e5-aa(t+(this._isUTC?0:6e4*this.utcOffset()),36e5)-1;break;case"minute":t=this._d.valueOf(),t+=6e4-aa(t,6e4)-1;break;case"second":t=this._d.valueOf(),t+=1e3-aa(t,1e3)-1}return this._d.setTime(t),o.updateOffset(this,!0),this},_a.format=function(e){e||(e=this.isUtc()?o.defaultFormatUtc:o.defaultFormat);var t=E(this,e);return this.localeData().postformat(t)},_a.from=function(e,t){return this.isValid()&&(Y(e)&&e.isValid()||St(e).isValid())?jt({to:this,from:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()},_a.fromNow=function(e){return this.from(St(),e)},_a.to=function(e,t){return this.isValid()&&(Y(e)&&e.isValid()||St(e).isValid())?jt({from:this,to:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()},_a.toNow=function(e){return this.to(St(),e)},_a.get=function(e){return N(this[e=R(e)])?this[e]():this},_a.invalidAt=function(){return g(this).overflow},_a.isAfter=function(e,t){var a=Y(e)?e:St(e);return!(!this.isValid()||!a.isValid())&&("millisecond"===(t=R(t)||"millisecond")?this.valueOf()>a.valueOf():a.valueOf()<this.clone().startOf(t).valueOf())},_a.isBefore=function(e,t){var a=Y(e)?e:St(e);return!(!this.isValid()||!a.isValid())&&("millisecond"===(t=R(t)||"millisecond")?this.valueOf()<a.valueOf():this.clone().endOf(t).valueOf()<a.valueOf())},_a.isBetween=function(e,t,a,r){var o=Y(e)?e:St(e),n=Y(t)?t:St(t);return!!(this.isValid()&&o.isValid()&&n.isValid())&&(("("===(r=r||"()")[0]?this.isAfter(o,a):!this.isBefore(o,a))&&(")"===r[1]?this.isBefore(n,a):!this.isAfter(n,a)))},_a.isSame=function(e,t){var a,r=Y(e)?e:St(e);return!(!this.isValid()||!r.isValid())&&("millisecond"===(t=R(t)||"millisecond")?this.valueOf()===r.valueOf():(a=r.valueOf(),this.clone().startOf(t).valueOf()<=a&&a<=this.clone().endOf(t).valueOf()))},_a.isSameOrAfter=function(e,t){return this.isSame(e,t)||this.isAfter(e,t)},_a.isSameOrBefore=function(e,t){return this.isSame(e,t)||this.isBefore(e,t)},_a.isValid=function(){return _(this)},_a.lang=ea,_a.locale=Xt,_a.localeData=ta,_a.max=Nt,_a.min=vt,_a.parsingFlags=function(){return p({},g(this))},_a.set=function(e,t){if("object"==typeof e){var a,r=function(e){var t,a=[];for(t in e)i(e,t)&&a.push({unit:t,priority:I[t]});return a.sort((function(e,t){return e.priority-t.priority})),a}(e=F(e)),o=r.length;for(a=0;a<o;a++)this[r[a].unit](e[r[a].unit])}else if(N(this[e=R(e)]))return this[e](t);return this},_a.startOf=function(e){var t,a;if(void 0===(e=R(e))||"millisecond"===e||!this.isValid())return this;switch(a=this._isUTC?oa:ra,e){case"year":t=a(this.year(),0,1);break;case"quarter":t=a(this.year(),this.month()-this.month()%3,1);break;case"month":t=a(this.year(),this.month(),1);break;case"week":t=a(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":t=a(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":t=a(this.year(),this.month(),this.date());break;case"hour":t=this._d.valueOf(),t-=aa(t+(this._isUTC?0:6e4*this.utcOffset()),36e5);break;case"minute":t=this._d.valueOf(),t-=aa(t,6e4);break;case"second":t=this._d.valueOf(),t-=aa(t,1e3)}return this._d.setTime(t),o.updateOffset(this,!0),this},_a.subtract=Zt,_a.toArray=function(){var e=this;return[e.year(),e.month(),e.date(),e.hour(),e.minute(),e.second(),e.millisecond()]},_a.toObject=function(){var e=this;return{years:e.year(),months:e.month(),date:e.date(),hours:e.hours(),minutes:e.minutes(),seconds:e.seconds(),milliseconds:e.milliseconds()}},_a.toDate=function(){return new Date(this.valueOf())},_a.toISOString=function(e){if(!this.isValid())return null;var t=!0!==e,a=t?this.clone().utc():this;return a.year()<0||a.year()>9999?E(a,t?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):N(Date.prototype.toISOString)?t?this.toDate().toISOString():new Date(this.valueOf()+60*this.utcOffset()*1e3).toISOString().replace("Z",E(a,"Z")):E(a,t?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ")},_a.inspect=function(){if(!this.isValid())return"moment.invalid(/* "+this._i+" */)";var e,t,a,r="moment",o="";return this.isLocal()||(r=0===this.utcOffset()?"moment.utc":"moment.parseZone",o="Z"),e="["+r+'("]',t=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY",a=o+'[")]',this.format(e+t+"-MM-DD[T]HH:mm:ss.SSS"+a)},"undefined"!=typeof Symbol&&null!=Symbol.for&&(_a[Symbol.for("nodejs.util.inspect.custom")]=function(){return"Moment<"+this.format()+">"}),_a.toJSON=function(){return this.isValid()?this.toISOString():null},_a.toString=function(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")},_a.unix=function(){return Math.floor(this.valueOf()/1e3)},_a.valueOf=function(){return this._d.valueOf()-6e4*(this._offset||0)},_a.creationData=function(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}},_a.eraName=function(){var e,t,a,r=this.localeData().eras();for(e=0,t=r.length;e<t;++e){if(a=this.clone().startOf("day").valueOf(),r[e].since<=a&&a<=r[e].until)return r[e].name;if(r[e].until<=a&&a<=r[e].since)return r[e].name}return""},_a.eraNarrow=function(){var e,t,a,r=this.localeData().eras();for(e=0,t=r.length;e<t;++e){if(a=this.clone().startOf("day").valueOf(),r[e].since<=a&&a<=r[e].until)return r[e].narrow;if(r[e].until<=a&&a<=r[e].since)return r[e].narrow}return""},_a.eraAbbr=function(){var e,t,a,r=this.localeData().eras();for(e=0,t=r.length;e<t;++e){if(a=this.clone().startOf("day").valueOf(),r[e].since<=a&&a<=r[e].until)return r[e].abbr;if(r[e].until<=a&&a<=r[e].since)return r[e].abbr}return""},_a.eraYear=function(){var e,t,a,r,n=this.localeData().eras();for(e=0,t=n.length;e<t;++e)if(a=n[e].since<=n[e].until?1:-1,r=this.clone().startOf("day").valueOf(),n[e].since<=r&&r<=n[e].until||n[e].until<=r&&r<=n[e].since)return(this.year()-o(n[e].since).year())*a+n[e].offset;return this.year()},_a.year=De,_a.isLeapYear=function(){return fe(this.year())},_a.weekYear=function(e){return la.call(this,e,this.week(),this.weekday()+this.localeData()._week.dow,this.localeData()._week.dow,this.localeData()._week.doy)},_a.isoWeekYear=function(e){return la.call(this,e,this.isoWeek(),this.isoWeekday(),1,4)},_a.quarter=_a.quarters=function(e){return null==e?Math.ceil((this.month()+1)/3):this.month(3*(e-1)+this.month()%3)},_a.month=Pe,_a.daysInMonth=function(){return Le(this.year(),this.month())},_a.week=_a.weeks=function(e){var t=this.localeData().week(this);return null==e?t:this.add(7*(e-t),"d")},_a.isoWeek=_a.isoWeeks=function(e){var t=Oe(this,1,4).week;return null==e?t:this.add(7*(e-t),"d")},_a.weeksInYear=function(){var e=this.localeData()._week;return ze(this.year(),e.dow,e.doy)},_a.weeksInWeekYear=function(){var e=this.localeData()._week;return ze(this.weekYear(),e.dow,e.doy)},_a.isoWeeksInYear=function(){return ze(this.year(),1,4)},_a.isoWeeksInISOWeekYear=function(){return ze(this.isoWeekYear(),1,4)},_a.date=da,_a.day=_a.days=function(e){if(!this.isValid())return null!=e?this:NaN;var t=Ye(this,"Day");return null!=e?(e=function(e,t){return"string"!=typeof e?e:isNaN(e)?"number"==typeof(e=t.weekdaysParse(e))?e:null:parseInt(e,10)}(e,this.localeData()),this.add(e-t,"d")):t},_a.weekday=function(e){if(!this.isValid())return null!=e?this:NaN;var t=(this.day()+7-this.localeData()._week.dow)%7;return null==e?t:this.add(e-t,"d")},_a.isoWeekday=function(e){if(!this.isValid())return null!=e?this:NaN;if(null!=e){var t=function(e,t){return"string"==typeof e?t.weekdaysParse(e)%7||7:isNaN(e)?null:e}(e,this.localeData());return this.day(this.day()%7?t:t-7)}return this.day()||7},_a.dayOfYear=function(e){var t=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==e?t:this.add(e-t,"d")},_a.hour=_a.hours=Xe,_a.minute=_a.minutes=ca,_a.second=_a.seconds=ha,_a.millisecond=_a.milliseconds=pa,_a.utcOffset=function(e,t,a){var r,n=this._offset||0;if(!this.isValid())return null!=e?this:NaN;if(null!=e){if("string"==typeof e){if(null===(e=Vt(oe,e)))return this}else Math.abs(e)<16&&!a&&(e*=60);return!this._isUTC&&t&&(r=Ot(this)),this._offset=e,this._isUTC=!0,null!=r&&this.add(r,"m"),n!==e&&(!t||this._changeInProgress?Bt(this,jt(e-n,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,o.updateOffset(this,!0),this._changeInProgress=null)),this}return this._isUTC?n:Ot(this)},_a.utc=function(e){return this.utcOffset(0,e)},_a.local=function(e){return this._isUTC&&(this.utcOffset(0,e),this._isUTC=!1,e&&this.subtract(Ot(this),"m")),this},_a.parseZone=function(){if(null!=this._tzm)this.utcOffset(this._tzm,!1,!0);else if("string"==typeof this._i){var e=Vt(re,this._i);null!=e?this.utcOffset(e):this.utcOffset(0,!0)}return this},_a.hasAlignedHourOffset=function(e){return!!this.isValid()&&(e=e?St(e).utcOffset():0,(this.utcOffset()-e)%60==0)},_a.isDST=function(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()},_a.isLocal=function(){return!!this.isValid()&&!this._isUTC},_a.isUtcOffset=function(){return!!this.isValid()&&this._isUTC},_a.isUtc=zt,_a.isUTC=zt,_a.zoneAbbr=function(){return this._isUTC?"UTC":""},_a.zoneName=function(){return this._isUTC?"Coordinated Universal Time":""},_a.dates=L("dates accessor is deprecated. Use date instead.",da),_a.months=L("months accessor is deprecated. Use month instead",Pe),_a.years=L("years accessor is deprecated. Use year instead",De),_a.zone=L("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",(function(e,t){return null!=e?("string"!=typeof e&&(e=-e),this.utcOffset(e,t),this):-this.utcOffset()})),_a.isDSTShifted=L("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",(function(){if(!u(this._isDSTShifted))return this._isDSTShifted;var e,t={};return D(t,this),(t=Lt(t))._a?(e=t._isUTC?h(t._a):St(t._a),this._isDSTShifted=this.isValid()&&function(e,t,a){var r,o=Math.min(e.length,t.length),n=Math.abs(e.length-t.length),s=0;for(r=0;r<o;r++)(a&&e[r]!==t[r]||!a&&me(e[r])!==me(t[r]))&&s++;return s+n}(t._a,e.toArray())>0):this._isDSTShifted=!1,this._isDSTShifted}));var ba=k.prototype;function Ma(e,t,a,r){var o=lt(),n=h().set(r,t);return o[a](n,e)}function Da(e,t,a){if(d(e)&&(t=e,e=void 0),e=e||"",null!=t)return Ma(e,t,a,"month");var r,o=[];for(r=0;r<12;r++)o[r]=Ma(e,r,a,"month");return o}function ya(e,t,a,r){"boolean"==typeof e?(d(t)&&(a=t,t=void 0),t=t||""):(a=t=e,e=!1,d(t)&&(a=t,t=void 0),t=t||"");var o,n=lt(),s=e?n._week.dow:0,i=[];if(null!=a)return Ma(t,(a+s)%7,r,"day");for(o=0;o<7;o++)i[o]=Ma(t,(o+s)%7,r,"day");return i}ba.calendar=function(e,t,a){var r=this._calendar[e]||this._calendar.sameElse;return N(r)?r.call(t,a):r},ba.longDateFormat=function(e){var t=this._longDateFormat[e],a=this._longDateFormat[e.toUpperCase()];return t||!a?t:(this._longDateFormat[e]=a.match(P).map((function(e){return"MMMM"===e||"MM"===e||"DD"===e||"dddd"===e?e.slice(1):e})).join(""),this._longDateFormat[e])},ba.invalidDate=function(){return this._invalidDate},ba.ordinal=function(e){return this._ordinal.replace("%d",e)},ba.preparse=fa,ba.postformat=fa,ba.relativeTime=function(e,t,a,r){var o=this._relativeTime[a];return N(o)?o(e,t,a,r):o.replace(/%d/i,e)},ba.pastFuture=function(e,t){var a=this._relativeTime[e>0?"future":"past"];return N(a)?a(t):a.replace(/%s/i,t)},ba.set=function(e){var t,a;for(a in e)i(e,a)&&(N(t=e[a])?this[a]=t:this["_"+a]=t);this._config=e,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)},ba.eras=function(e,t){var a,r,n,s=this._eras||lt("en")._eras;for(a=0,r=s.length;a<r;++a){switch(typeof s[a].since){case"string":n=o(s[a].since).startOf("day"),s[a].since=n.valueOf()}switch(typeof s[a].until){case"undefined":s[a].until=1/0;break;case"string":n=o(s[a].until).startOf("day").valueOf(),s[a].until=n.valueOf()}}return s},ba.erasParse=function(e,t,a){var r,o,n,s,i,l=this.eras();for(e=e.toUpperCase(),r=0,o=l.length;r<o;++r)if(n=l[r].name.toUpperCase(),s=l[r].abbr.toUpperCase(),i=l[r].narrow.toUpperCase(),a)switch(t){case"N":case"NN":case"NNN":if(s===e)return l[r];break;case"NNNN":if(n===e)return l[r];break;case"NNNNN":if(i===e)return l[r]}else if([n,s,i].indexOf(e)>=0)return l[r]},ba.erasConvertYear=function(e,t){var a=e.since<=e.until?1:-1;return void 0===t?o(e.since).year():o(e.since).year()+(t-e.offset)*a},ba.erasAbbrRegex=function(e){return i(this,"_erasAbbrRegex")||sa.call(this),e?this._erasAbbrRegex:this._erasRegex},ba.erasNameRegex=function(e){return i(this,"_erasNameRegex")||sa.call(this),e?this._erasNameRegex:this._erasRegex},ba.erasNarrowRegex=function(e){return i(this,"_erasNarrowRegex")||sa.call(this),e?this._erasNarrowRegex:this._erasRegex},ba.months=function(e,t){return e?n(this._months)?this._months[e.month()]:this._months[(this._months.isFormat||ve).test(t)?"format":"standalone"][e.month()]:n(this._months)?this._months:this._months.standalone},ba.monthsShort=function(e,t){return e?n(this._monthsShort)?this._monthsShort[e.month()]:this._monthsShort[ve.test(t)?"format":"standalone"][e.month()]:n(this._monthsShort)?this._monthsShort:this._monthsShort.standalone},ba.monthsParse=function(e,t,a){var r,o,n;if(this._monthsParseExact)return ke.call(this,e,t,a);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),r=0;r<12;r++){if(o=h([2e3,r]),a&&!this._longMonthsParse[r]&&(this._longMonthsParse[r]=new RegExp("^"+this.months(o,"").replace(".","")+"$","i"),this._shortMonthsParse[r]=new RegExp("^"+this.monthsShort(o,"").replace(".","")+"$","i")),a||this._monthsParse[r]||(n="^"+this.months(o,"")+"|^"+this.monthsShort(o,""),this._monthsParse[r]=new RegExp(n.replace(".",""),"i")),a&&"MMMM"===t&&this._longMonthsParse[r].test(e))return r;if(a&&"MMM"===t&&this._shortMonthsParse[r].test(e))return r;if(!a&&this._monthsParse[r].test(e))return r}},ba.monthsRegex=function(e){return this._monthsParseExact?(i(this,"_monthsRegex")||He.call(this),e?this._monthsStrictRegex:this._monthsRegex):(i(this,"_monthsRegex")||(this._monthsRegex=we),this._monthsStrictRegex&&e?this._monthsStrictRegex:this._monthsRegex)},ba.monthsShortRegex=function(e){return this._monthsParseExact?(i(this,"_monthsRegex")||He.call(this),e?this._monthsShortStrictRegex:this._monthsShortRegex):(i(this,"_monthsShortRegex")||(this._monthsShortRegex=Ne),this._monthsShortStrictRegex&&e?this._monthsShortStrictRegex:this._monthsShortRegex)},ba.week=function(e){return Oe(e,this._week.dow,this._week.doy).week},ba.firstDayOfYear=function(){return this._week.doy},ba.firstDayOfWeek=function(){return this._week.dow},ba.weekdays=function(e,t){var a=n(this._weekdays)?this._weekdays:this._weekdays[e&&!0!==e&&this._weekdays.isFormat.test(t)?"format":"standalone"];return!0===e?Re(a,this._week.dow):e?a[e.day()]:a},ba.weekdaysMin=function(e){return!0===e?Re(this._weekdaysMin,this._week.dow):e?this._weekdaysMin[e.day()]:this._weekdaysMin},ba.weekdaysShort=function(e){return!0===e?Re(this._weekdaysShort,this._week.dow):e?this._weekdaysShort[e.day()]:this._weekdaysShort},ba.weekdaysParse=function(e,t,a){var r,o,n;if(this._weekdaysParseExact)return Ge.call(this,e,t,a);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),r=0;r<7;r++){if(o=h([2e3,1]).day(r),a&&!this._fullWeekdaysParse[r]&&(this._fullWeekdaysParse[r]=new RegExp("^"+this.weekdays(o,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[r]=new RegExp("^"+this.weekdaysShort(o,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[r]=new RegExp("^"+this.weekdaysMin(o,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[r]||(n="^"+this.weekdays(o,"")+"|^"+this.weekdaysShort(o,"")+"|^"+this.weekdaysMin(o,""),this._weekdaysParse[r]=new RegExp(n.replace(".",""),"i")),a&&"dddd"===t&&this._fullWeekdaysParse[r].test(e))return r;if(a&&"ddd"===t&&this._shortWeekdaysParse[r].test(e))return r;if(a&&"dd"===t&&this._minWeekdaysParse[r].test(e))return r;if(!a&&this._weekdaysParse[r].test(e))return r}},ba.weekdaysRegex=function(e){return this._weekdaysParseExact?(i(this,"_weekdaysRegex")||Ze.call(this),e?this._weekdaysStrictRegex:this._weekdaysRegex):(i(this,"_weekdaysRegex")||(this._weekdaysRegex=Qe),this._weekdaysStrictRegex&&e?this._weekdaysStrictRegex:this._weekdaysRegex)},ba.weekdaysShortRegex=function(e){return this._weekdaysParseExact?(i(this,"_weekdaysRegex")||Ze.call(this),e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(i(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=Ue),this._weekdaysShortStrictRegex&&e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)},ba.weekdaysMinRegex=function(e){return this._weekdaysParseExact?(i(this,"_weekdaysRegex")||Ze.call(this),e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(i(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=Be),this._weekdaysMinStrictRegex&&e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)},ba.isPM=function(e){return"p"===(e+"").toLowerCase().charAt(0)},ba.meridiem=function(e,t,a){return e>11?a?"pm":"PM":a?"am":"AM"},st("en",{eras:[{since:"0001-01-01",until:1/0,offset:1,name:"Anno Domini",narrow:"AD",abbr:"AD"},{since:"0000-12-31",until:-1/0,offset:1,name:"Before Christ",narrow:"BC",abbr:"BC"}],dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(e){var t=e%10;return e+(1===me(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th")}}),o.lang=L("moment.lang is deprecated. Use moment.locale instead.",st),o.langData=L("moment.langData is deprecated. Use moment.localeData instead.",lt);var Ya=Math.abs;function Ta(e,t,a,r){var o=jt(t,a);return e._milliseconds+=r*o._milliseconds,e._days+=r*o._days,e._months+=r*o._months,e._bubble()}function La(e){return e<0?Math.floor(e):Math.ceil(e)}function xa(e){return 4800*e/146097}function Sa(e){return 146097*e/4800}function va(e){return function(){return this.as(e)}}var Na=va("ms"),wa=va("s"),ka=va("m"),Aa=va("h"),Pa=va("d"),Ha=va("w"),Wa=va("M"),Ca=va("Q"),Va=va("y"),Ea=Na;function Oa(e){return function(){return this.isValid()?this._data[e]:NaN}}var za=Oa("milliseconds"),Ra=Oa("seconds"),Fa=Oa("minutes"),ja=Oa("hours"),Ia=Oa("days"),Qa=Oa("months"),Ua=Oa("years"),Ba=Math.round,Ga={ss:44,s:45,m:45,h:22,d:26,w:null,M:11};function Za(e,t,a,r,o){return o.relativeTime(t||1,!!a,e,r)}var qa=Math.abs;function Ja(e){return(e>0)-(e<0)||+e}function Ka(){if(!this.isValid())return this.localeData().invalidDate();var e,t,a,r,o,n,s,i,l=qa(this._milliseconds)/1e3,u=qa(this._days),d=qa(this._months),c=this.asSeconds();return c?(e=ce(l/60),t=ce(e/60),l%=60,e%=60,a=ce(d/12),d%=12,r=l?l.toFixed(3).replace(/\.?0+$/,""):"",o=c<0?"-":"",n=Ja(this._months)!==Ja(c)?"-":"",s=Ja(this._days)!==Ja(c)?"-":"",i=Ja(this._milliseconds)!==Ja(c)?"-":"",o+"P"+(a?n+a+"Y":"")+(d?n+d+"M":"")+(u?s+u+"D":"")+(t||e||l?"T":"")+(t?i+t+"H":"")+(e?i+e+"M":"")+(l?i+r+"S":"")):"P0D"}var $a=At.prototype;return $a.isValid=function(){return this._isValid},$a.abs=function(){var e=this._data;return this._milliseconds=Ya(this._milliseconds),this._days=Ya(this._days),this._months=Ya(this._months),e.milliseconds=Ya(e.milliseconds),e.seconds=Ya(e.seconds),e.minutes=Ya(e.minutes),e.hours=Ya(e.hours),e.months=Ya(e.months),e.years=Ya(e.years),this},$a.add=function(e,t){return Ta(this,e,t,1)},$a.subtract=function(e,t){return Ta(this,e,t,-1)},$a.as=function(e){if(!this.isValid())return NaN;var t,a,r=this._milliseconds;if("month"===(e=R(e))||"quarter"===e||"year"===e)switch(t=this._days+r/864e5,a=this._months+xa(t),e){case"month":return a;case"quarter":return a/3;case"year":return a/12}else switch(t=this._days+Math.round(Sa(this._months)),e){case"week":return t/7+r/6048e5;case"day":return t+r/864e5;case"hour":return 24*t+r/36e5;case"minute":return 1440*t+r/6e4;case"second":return 86400*t+r/1e3;case"millisecond":return Math.floor(864e5*t)+r;default:throw new Error("Unknown unit "+e)}},$a.asMilliseconds=Na,$a.asSeconds=wa,$a.asMinutes=ka,$a.asHours=Aa,$a.asDays=Pa,$a.asWeeks=Ha,$a.asMonths=Wa,$a.asQuarters=Ca,$a.asYears=Va,$a.valueOf=Ea,$a._bubble=function(){var e,t,a,r,o,n=this._milliseconds,s=this._days,i=this._months,l=this._data;return n>=0&&s>=0&&i>=0||n<=0&&s<=0&&i<=0||(n+=864e5*La(Sa(i)+s),s=0,i=0),l.milliseconds=n%1e3,e=ce(n/1e3),l.seconds=e%60,t=ce(e/60),l.minutes=t%60,a=ce(t/60),l.hours=a%24,s+=ce(a/24),o=ce(xa(s)),i+=o,s-=La(Sa(o)),r=ce(i/12),i%=12,l.days=s,l.months=i,l.years=r,this},$a.clone=function(){return jt(this)},$a.get=function(e){return e=R(e),this.isValid()?this[e+"s"]():NaN},$a.milliseconds=za,$a.seconds=Ra,$a.minutes=Fa,$a.hours=ja,$a.days=Ia,$a.weeks=function(){return ce(this.days()/7)},$a.months=Qa,$a.years=Ua,$a.humanize=function(e,t){if(!this.isValid())return this.localeData().invalidDate();var a,r,o=!1,n=Ga;return"object"==typeof e&&(t=e,e=!1),"boolean"==typeof e&&(o=e),"object"==typeof t&&(n=Object.assign({},Ga,t),null!=t.s&&null==t.ss&&(n.ss=t.s-1)),a=this.localeData(),r=function(e,t,a,r){var o=jt(e).abs(),n=Ba(o.as("s")),s=Ba(o.as("m")),i=Ba(o.as("h")),l=Ba(o.as("d")),u=Ba(o.as("M")),d=Ba(o.as("w")),c=Ba(o.as("y")),m=n<=a.ss&&["s",n]||n<a.s&&["ss",n]||s<=1&&["m"]||s<a.m&&["mm",s]||i<=1&&["h"]||i<a.h&&["hh",i]||l<=1&&["d"]||l<a.d&&["dd",l];return null!=a.w&&(m=m||d<=1&&["w"]||d<a.w&&["ww",d]),(m=m||u<=1&&["M"]||u<a.M&&["MM",u]||c<=1&&["y"]||["yy",c])[2]=t,m[3]=+e>0,m[4]=r,Za.apply(null,m)}(this,!o,n,a),o&&(r=a.pastFuture(+this,r)),a.postformat(r)},$a.toISOString=Ka,$a.toString=Ka,$a.toJSON=Ka,$a.locale=Xt,$a.localeData=ta,$a.toIsoString=L("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",Ka),$a.lang=ea,V("X",0,0,"unix"),V("x",0,0,"valueOf"),le("x",ae),le("X",/[+-]?\d+(\.\d{1,3})?/),he("X",(function(e,t,a){a._d=new Date(1e3*parseFloat(e))})),he("x",(function(e,t,a){a._d=new Date(me(e))})),
//! moment.js
o.version="2.30.1",t=St,o.fn=_a,o.min=function(){var e=[].slice.call(arguments,0);return wt("isBefore",e)},o.max=function(){var e=[].slice.call(arguments,0);return wt("isAfter",e)},o.now=function(){return Date.now?Date.now():+new Date},o.utc=h,o.unix=function(e){return St(1e3*e)},o.months=function(e,t){return Da(e,t,"months")},o.isDate=c,o.locale=st,o.invalid=f,o.duration=jt,o.isMoment=Y,o.weekdays=function(e,t,a){return ya(e,t,a,"weekdays")},o.parseZone=function(){return St.apply(null,arguments).parseZone()},o.localeData=lt,o.isDuration=Pt,o.monthsShort=function(e,t){return Da(e,t,"monthsShort")},o.weekdaysMin=function(e,t,a){return ya(e,t,a,"weekdaysMin")},o.defineLocale=it,o.updateLocale=function(e,t){if(null!=t){var a,r,o=et;null!=tt[e]&&null!=tt[e].parentLocale?tt[e].set(w(tt[e]._config,t)):(null!=(r=nt(e))&&(o=r._config),t=w(o,t),null==r&&(t.abbr=e),(a=new k(t)).parentLocale=tt[e],tt[e]=a),st(e)}else null!=tt[e]&&(null!=tt[e].parentLocale?(tt[e]=tt[e].parentLocale,e===st()&&st(e)):null!=tt[e]&&delete tt[e]);return tt[e]},o.locales=function(){return x(tt)},o.weekdaysShort=function(e,t,a){return ya(e,t,a,"weekdaysShort")},o.normalizeUnits=R,o.relativeTimeRounding=function(e){return void 0===e?Ba:"function"==typeof e&&(Ba=e,!0)},o.relativeTimeThreshold=function(e,t){return void 0!==Ga[e]&&(void 0===t?Ga[e]:(Ga[e]=t,"s"===e&&(Ga.ss=t-1),!0))},o.calendarFormat=function(e,t){var a=e.diff(t,"days",!0);return a<-6?"sameElse":a<-1?"lastWeek":a<0?"lastDay":a<1?"sameDay":a<2?"nextDay":a<7?"nextWeek":"sameElse"},o.prototype=_a,o.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"},o}()}).call(this,a(30)(e))},function(e,t,a){!function(e){"use strict";
//! moment.js locale configuration
function t(e,t,a,r){var o={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[e+" Tage",e+" Tagen"],w:["eine Woche","einer Woche"],M:["ein Monat","einem Monat"],MM:[e+" Monate",e+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[e+" Jahre",e+" Jahren"]};return t?o[a][0]:o[a][1]}e.defineLocale("de",{months:"Januar_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So._Mo._Di._Mi._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},calendar:{sameDay:"[heute um] LT [Uhr]",sameElse:"L",nextDay:"[morgen um] LT [Uhr]",nextWeek:"dddd [um] LT [Uhr]",lastDay:"[gestern um] LT [Uhr]",lastWeek:"[letzten] dddd [um] LT [Uhr]"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",ss:"%d Sekunden",m:t,mm:"%d Minuten",h:t,hh:"%d Stunden",d:t,dd:t,w:t,ww:"%d Wochen",M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(a(0))},function(e,t,a){!function(e){"use strict";
//! moment.js locale configuration
var t="ene._feb._mar._abr._may._jun._jul._ago._sep._oct._nov._dic.".split("_"),a="ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic".split("_"),r=[/^ene/i,/^feb/i,/^mar/i,/^abr/i,/^may/i,/^jun/i,/^jul/i,/^ago/i,/^sep/i,/^oct/i,/^nov/i,/^dic/i],o=/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre|ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i;e.defineLocale("es",{months:"enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre".split("_"),monthsShort:function(e,r){return e?/-MMM-/.test(r)?a[e.month()]:t[e.month()]:t},monthsRegex:o,monthsShortRegex:o,monthsStrictRegex:/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i,monthsShortStrictRegex:/^(ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i,monthsParse:r,longMonthsParse:r,shortMonthsParse:r,weekdays:"domingo_lunes_martes_miércoles_jueves_viernes_sábado".split("_"),weekdaysShort:"dom._lun._mar._mié._jue._vie._sáb.".split("_"),weekdaysMin:"do_lu_ma_mi_ju_vi_sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY H:mm",LLLL:"dddd, D [de] MMMM [de] YYYY H:mm"},calendar:{sameDay:function(){return"[hoy a la"+(1!==this.hours()?"s":"")+"] LT"},nextDay:function(){return"[mañana a la"+(1!==this.hours()?"s":"")+"] LT"},nextWeek:function(){return"dddd [a la"+(1!==this.hours()?"s":"")+"] LT"},lastDay:function(){return"[ayer a la"+(1!==this.hours()?"s":"")+"] LT"},lastWeek:function(){return"[el] dddd [pasado a la"+(1!==this.hours()?"s":"")+"] LT"},sameElse:"L"},relativeTime:{future:"en %s",past:"hace %s",s:"unos segundos",ss:"%d segundos",m:"un minuto",mm:"%d minutos",h:"una hora",hh:"%d horas",d:"un día",dd:"%d días",w:"una semana",ww:"%d semanas",M:"un mes",MM:"%d meses",y:"un año",yy:"%d años"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4},invalidDate:"Fecha inválida"})}(a(0))},function(e,t,a){!function(e){"use strict";
//! moment.js locale configuration
var t=/(janv\.?|févr\.?|mars|avr\.?|mai|juin|juil\.?|août|sept\.?|oct\.?|nov\.?|déc\.?|janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i,a=[/^janv/i,/^févr/i,/^mars/i,/^avr/i,/^mai/i,/^juin/i,/^juil/i,/^août/i,/^sept/i,/^oct/i,/^nov/i,/^déc/i];e.defineLocale("fr",{months:"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre".split("_"),monthsShort:"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.".split("_"),monthsRegex:t,monthsShortRegex:t,monthsStrictRegex:/^(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i,monthsShortStrictRegex:/(janv\.?|févr\.?|mars|avr\.?|mai|juin|juil\.?|août|sept\.?|oct\.?|nov\.?|déc\.?)/i,monthsParse:a,longMonthsParse:a,shortMonthsParse:a,weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"di_lu_ma_me_je_ve_sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Aujourd’hui à] LT",nextDay:"[Demain à] LT",nextWeek:"dddd [à] LT",lastDay:"[Hier à] LT",lastWeek:"dddd [dernier à] LT",sameElse:"L"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",ss:"%d secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",w:"une semaine",ww:"%d semaines",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},dayOfMonthOrdinalParse:/\d{1,2}(er|)/,ordinal:function(e,t){switch(t){case"D":return e+(1===e?"er":"");default:case"M":case"Q":case"DDD":case"d":return e+(1===e?"er":"e");case"w":case"W":return e+(1===e?"re":"e")}},week:{dow:1,doy:4}})}(a(0))},function(e,t,a){!function(e){"use strict";
//! moment.js locale configuration
e.defineLocale("ja",{eras:[{since:"2019-05-01",offset:1,name:"令和",narrow:"㋿",abbr:"R"},{since:"1989-01-08",until:"2019-04-30",offset:1,name:"平成",narrow:"㍻",abbr:"H"},{since:"1926-12-25",until:"1989-01-07",offset:1,name:"昭和",narrow:"㍼",abbr:"S"},{since:"1912-07-30",until:"1926-12-24",offset:1,name:"大正",narrow:"㍽",abbr:"T"},{since:"1873-01-01",until:"1912-07-29",offset:6,name:"明治",narrow:"㍾",abbr:"M"},{since:"0001-01-01",until:"1873-12-31",offset:1,name:"西暦",narrow:"AD",abbr:"AD"},{since:"0000-12-31",until:-1/0,offset:1,name:"紀元前",narrow:"BC",abbr:"BC"}],eraYearOrdinalRegex:/(元|\d+)年/,eraYearOrdinalParse:function(e,t){return"元"===t[1]?1:parseInt(t[1]||e,10)},months:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"日曜日_月曜日_火曜日_水曜日_木曜日_金曜日_土曜日".split("_"),weekdaysShort:"日_月_火_水_木_金_土".split("_"),weekdaysMin:"日_月_火_水_木_金_土".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日 HH:mm",LLLL:"YYYY年M月D日 dddd HH:mm",l:"YYYY/MM/DD",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日(ddd) HH:mm"},meridiemParse:/午前|午後/i,isPM:function(e){return"午後"===e},meridiem:function(e,t,a){return e<12?"午前":"午後"},calendar:{sameDay:"[今日] LT",nextDay:"[明日] LT",nextWeek:function(e){return e.week()!==this.week()?"[来週]dddd LT":"dddd LT"},lastDay:"[昨日] LT",lastWeek:function(e){return this.week()!==e.week()?"[先週]dddd LT":"dddd LT"},sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}日/,ordinal:function(e,t){switch(t){case"y":return 1===e?"元年":e+"年";case"d":case"D":case"DDD":return e+"日";default:return e}},relativeTime:{future:"%s後",past:"%s前",s:"数秒",ss:"%d秒",m:"1分",mm:"%d分",h:"1時間",hh:"%d時間",d:"1日",dd:"%d日",M:"1ヶ月",MM:"%dヶ月",y:"1年",yy:"%d年"}})}(a(0))},function(e,t,a){!function(e){"use strict";
//! moment.js locale configuration
e.defineLocale("ko",{months:"1월_2월_3월_4월_5월_6월_7월_8월_9월_10월_11월_12월".split("_"),monthsShort:"1월_2월_3월_4월_5월_6월_7월_8월_9월_10월_11월_12월".split("_"),weekdays:"일요일_월요일_화요일_수요일_목요일_금요일_토요일".split("_"),weekdaysShort:"일_월_화_수_목_금_토".split("_"),weekdaysMin:"일_월_화_수_목_금_토".split("_"),longDateFormat:{LT:"A h:mm",LTS:"A h:mm:ss",L:"YYYY.MM.DD.",LL:"YYYY년 MMMM D일",LLL:"YYYY년 MMMM D일 A h:mm",LLLL:"YYYY년 MMMM D일 dddd A h:mm",l:"YYYY.MM.DD.",ll:"YYYY년 MMMM D일",lll:"YYYY년 MMMM D일 A h:mm",llll:"YYYY년 MMMM D일 dddd A h:mm"},calendar:{sameDay:"오늘 LT",nextDay:"내일 LT",nextWeek:"dddd LT",lastDay:"어제 LT",lastWeek:"지난주 dddd LT",sameElse:"L"},relativeTime:{future:"%s 후",past:"%s 전",s:"몇 초",ss:"%d초",m:"1분",mm:"%d분",h:"한 시간",hh:"%d시간",d:"하루",dd:"%d일",M:"한 달",MM:"%d달",y:"일 년",yy:"%d년"},dayOfMonthOrdinalParse:/\d{1,2}(일|월|주)/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+"일";case"M":return e+"월";case"w":case"W":return e+"주";default:return e}},meridiemParse:/오전|오후/,isPM:function(e){return"오후"===e},meridiem:function(e,t,a){return e<12?"오전":"오후"}})}(a(0))},function(e,t,a){!function(e){"use strict";
//! moment.js locale configuration
var t="jan._feb._mrt._apr._mei_jun._jul._aug._sep._okt._nov._dec.".split("_"),a="jan_feb_mrt_apr_mei_jun_jul_aug_sep_okt_nov_dec".split("_"),r=[/^jan/i,/^feb/i,/^(maart|mrt\.?)$/i,/^apr/i,/^mei$/i,/^jun[i.]?$/i,/^jul[i.]?$/i,/^aug/i,/^sep/i,/^okt/i,/^nov/i,/^dec/i],o=/^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december|jan\.?|feb\.?|mrt\.?|apr\.?|ju[nl]\.?|aug\.?|sep\.?|okt\.?|nov\.?|dec\.?)/i;e.defineLocale("nl",{months:"januari_februari_maart_april_mei_juni_juli_augustus_september_oktober_november_december".split("_"),monthsShort:function(e,r){return e?/-MMM-/.test(r)?a[e.month()]:t[e.month()]:t},monthsRegex:o,monthsShortRegex:o,monthsStrictRegex:/^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december)/i,monthsShortStrictRegex:/^(jan\.?|feb\.?|mrt\.?|apr\.?|mei|ju[nl]\.?|aug\.?|sep\.?|okt\.?|nov\.?|dec\.?)/i,monthsParse:r,longMonthsParse:r,shortMonthsParse:r,weekdays:"zondag_maandag_dinsdag_woensdag_donderdag_vrijdag_zaterdag".split("_"),weekdaysShort:"zo._ma._di._wo._do._vr._za.".split("_"),weekdaysMin:"zo_ma_di_wo_do_vr_za".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD-MM-YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[vandaag om] LT",nextDay:"[morgen om] LT",nextWeek:"dddd [om] LT",lastDay:"[gisteren om] LT",lastWeek:"[afgelopen] dddd [om] LT",sameElse:"L"},relativeTime:{future:"over %s",past:"%s geleden",s:"een paar seconden",ss:"%d seconden",m:"één minuut",mm:"%d minuten",h:"één uur",hh:"%d uur",d:"één dag",dd:"%d dagen",w:"één week",ww:"%d weken",M:"één maand",MM:"%d maanden",y:"één jaar",yy:"%d jaar"},dayOfMonthOrdinalParse:/\d{1,2}(ste|de)/,ordinal:function(e){return e+(1===e||8===e||e>=20?"ste":"de")},week:{dow:1,doy:4}})}(a(0))},function(e,t,a){!function(e){"use strict";
//! moment.js locale configuration
e.defineLocale("zh-cn",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"周日_周一_周二_周三_周四_周五_周六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日Ah点mm分",LLLL:"YYYY年M月D日ddddAh点mm分",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},meridiemParse:/凌晨|早上|上午|中午|下午|晚上/,meridiemHour:function(e,t){return 12===e&&(e=0),"凌晨"===t||"早上"===t||"上午"===t?e:"下午"===t||"晚上"===t?e+12:e>=11?e:e+12},meridiem:function(e,t,a){var r=100*e+t;return r<600?"凌晨":r<900?"早上":r<1130?"上午":r<1230?"中午":r<1800?"下午":"晚上"},calendar:{sameDay:"[今天]LT",nextDay:"[明天]LT",nextWeek:function(e){return e.week()!==this.week()?"[下]dddLT":"[本]dddLT"},lastDay:"[昨天]LT",lastWeek:function(e){return this.week()!==e.week()?"[上]dddLT":"[本]dddLT"},sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}(日|月|周)/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+"日";case"M":return e+"月";case"w":case"W":return e+"周";default:return e}},relativeTime:{future:"%s后",past:"%s前",s:"几秒",ss:"%d 秒",m:"1 分钟",mm:"%d 分钟",h:"1 小时",hh:"%d 小时",d:"1 天",dd:"%d 天",w:"1 周",ww:"%d 周",M:"1 个月",MM:"%d 个月",y:"1 年",yy:"%d 年"},week:{dow:1,doy:4}})}(a(0))},,,,,,,,,,,,,,,,,,,,,,function(e,t,a){"use strict";a.r(t);var r=a(0);a(4),a(7),a(5),a(6),a(1),a(32),a(33),a(3),a(2);window.arjsViewer=window.arjsViewer||{};var o=window.arjsViewer.loadLocalizations;window.arjsViewer.loadLocalizations=function(){o&&o(),function(){if(window.arjsViewer.addMomentLocale&&window.arjsViewer.addMomentLocale.apply)for(var e=0,t=Object(r.locales)();e<t.length;e++){var o=t[e];window.arjsViewer.addMomentLocale(o,Object(r.localeData)(o)._config)}window.arjsViewer.addLocalization(a(34)),window.arjsViewer.addLocalization(a(35)),window.arjsViewer.addLocalization(a(36)),window.arjsViewer.addLocalization(a(37)),window.arjsViewer.addLocalization(a(38)),window.arjsViewer.addLocalization(a(39)),window.arjsViewer.addLocalization(a(40)),window.arjsViewer.addLocalization(a(41)),window.arjsViewer.addLocalization(a(42))}()},window.arjsViewer.addLocalization&&window.arjsViewer.loadLocalizations()},function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},function(e,t,a){var r={"./de":1,"./de.js":1,"./es":2,"./es.js":2,"./fr":3,"./fr.js":3,"./ja":4,"./ja.js":4,"./ko":5,"./ko.js":5,"./nl":6,"./nl.js":6,"./zh-cn":7,"./zh-cn.js":7};function o(e){var t=n(e);return a(t)}function n(e){if(!a.o(r,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return r[e]}o.keys=function(){return Object.keys(r)},o.resolve=n,e.exports=o,o.id=31},function(e,t,a){!function(e){"use strict";
//! moment.js locale configuration
e.defineLocale("pt-br",{months:"janeiro_fevereiro_março_abril_maio_junho_julho_agosto_setembro_outubro_novembro_dezembro".split("_"),monthsShort:"jan_fev_mar_abr_mai_jun_jul_ago_set_out_nov_dez".split("_"),weekdays:"domingo_segunda-feira_terça-feira_quarta-feira_quinta-feira_sexta-feira_sábado".split("_"),weekdaysShort:"dom_seg_ter_qua_qui_sex_sáb".split("_"),weekdaysMin:"do_2ª_3ª_4ª_5ª_6ª_sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY [às] HH:mm",LLLL:"dddd, D [de] MMMM [de] YYYY [às] HH:mm"},calendar:{sameDay:"[Hoje às] LT",nextDay:"[Amanhã às] LT",nextWeek:"dddd [às] LT",lastDay:"[Ontem às] LT",lastWeek:function(){return 0===this.day()||6===this.day()?"[Último] dddd [às] LT":"[Última] dddd [às] LT"},sameElse:"L"},relativeTime:{future:"em %s",past:"há %s",s:"poucos segundos",ss:"%d segundos",m:"um minuto",mm:"%d minutos",h:"uma hora",hh:"%d horas",d:"um dia",dd:"%d dias",M:"um mês",MM:"%d meses",y:"um ano",yy:"%d anos"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",invalidDate:"Data inválida"})}(a(0))},function(e,t,a){!function(e){"use strict";
//! moment.js locale configuration
e.defineLocale("it",{months:"gennaio_febbraio_marzo_aprile_maggio_giugno_luglio_agosto_settembre_ottobre_novembre_dicembre".split("_"),monthsShort:"gen_feb_mar_apr_mag_giu_lug_ago_set_ott_nov_dic".split("_"),weekdays:"domenica_lunedì_martedì_mercoledì_giovedì_venerdì_sabato".split("_"),weekdaysShort:"dom_lun_mar_mer_gio_ven_sab".split("_"),weekdaysMin:"do_lu_ma_me_gi_ve_sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:function(){return"[Oggi a"+(this.hours()>1?"lle ":0===this.hours()?" ":"ll'")+"]LT"},nextDay:function(){return"[Domani a"+(this.hours()>1?"lle ":0===this.hours()?" ":"ll'")+"]LT"},nextWeek:function(){return"dddd [a"+(this.hours()>1?"lle ":0===this.hours()?" ":"ll'")+"]LT"},lastDay:function(){return"[Ieri a"+(this.hours()>1?"lle ":0===this.hours()?" ":"ll'")+"]LT"},lastWeek:function(){switch(this.day()){case 0:return"[La scorsa] dddd [a"+(this.hours()>1?"lle ":0===this.hours()?" ":"ll'")+"]LT";default:return"[Lo scorso] dddd [a"+(this.hours()>1?"lle ":0===this.hours()?" ":"ll'")+"]LT"}},sameElse:"L"},relativeTime:{future:"tra %s",past:"%s fa",s:"alcuni secondi",ss:"%d secondi",m:"un minuto",mm:"%d minuti",h:"un'ora",hh:"%d ore",d:"un giorno",dd:"%d giorni",w:"una settimana",ww:"%d settimane",M:"un mese",MM:"%d mesi",y:"un anno",yy:"%d anni"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}})}(a(0))},function(e){e.exports=JSON.parse('[{"lng":"zh","ns":"core","resources":{"barcode-symbology-not-supported":"不支持此条码类型\\"{{symbology}}\\" ","default-invalid-barcode-text":"错误","errors":{"compile":"报表编译错误：{{details}}.","csvdataprovider":{"header-parse":"无法解析列标题 \\"{{headerValue}}\\""},"data-processing":"数据处理错误{{details}}.","dataprovider":{"commandtext-invalid":"数据提供程序 CommandText 无效：{{commandText}}。","connectstring-invalid":"数据源连接字符串无效：{{connectString}}。","no-data":"数据来源设置无效，未指定数据源或数据"},"fetch-failed":"无法从指定 \\"{{uri}}\\": {{responseStatus}} {{responseText}}加载数据","generic-error":"{{errorMessage}}","invalid-datasetname":"无效的数据集名称： \\"{{dataSetName}}\\"","invalid-definition":"非法的报表格式 {{details}}.","json-parse":"JSON解析失败: {{errorMessage}}. 原始字符串: \\"{{jsonString}}\\".","jsondataprovider":{"no-global-data":"No data available for DataSet \\"{{datasetName}}\\" - {{globalPath}} = {{globalValue}}"},"layout":"报表布局错误： {{details}}.","no-datasets":"未找到定义的数据集","print":{"cancel-print-message":"用户取消了打印过程","first-page-render-failed":"首页渲染失败","open-window-failed":"无法打开新窗口"},"report-invalid":"报表 \\"{{reportUri}}\\" 格式有误: {{error}}","report-not-available":"报表 \\"{{reportUri}}\\" 不可用: {{error}}","reportPartsLibraries-invalid":"Report Parts library \\"{{reportUri}}\\" is invalid: {{error}}","reportPartsLibraries-not-available":"Report Parts library \\"{{reportUri}}\\" is not available: {{error}}","reportPartsLibraries-unknown":"Unknown Report Parts library \\"{{libraryName}}\\"","subreport-invalid":"子报表 \\"{{reportUri}}\\" 无效： {{error}}","subreport-not-available":"子报表 \\"{{reportUri}}\\" 无效: {{error}}","unknown-dataprovider":"未知的数据源提供程序\\"{{providerName}}\\"","unknown-datasource":"未知的数据源\\"{{dataSourceName}}\\""},"report-item-not-supported":"在线设计器目前还不支持\\"{{itemType}}\\" 数据控件"}},{"lng":"zh","ns":"export","resources":{"boolTextFalse":"否","boolTextTrue":"是","cancel-btn":"取消","emptyTextPlaceholder":"<空>","file-format-label":"格式化","inprogress-lbl":"正在导出","nodocument":"未选择导出文档","start-btn":"导出","titleDecrease":"减少","titleIncrease":"增加","titleToggle":"展开"}},{"lng":"zh","ns":"params","resources":{"bool-false-label":"否","bool-true-label":"是","datePlaceholder":"请选择日期..","daysViewHeaderFormat":"MMMM YYYY","ddpTextNull":"(空)","ddpTextSelectAllValues":"(全选)","ddpTextSelectValue":"(请选择值)","null":"空","paramsTextInvalidValue":"无效值","preview-btn-title":"预览"}},{"lng":"zh","ns":"paramsValidation","resources":{"integerTooBig":"* 值不能超过53位整数","invalidBoolean":"* 值无效。 请确保输入的值合法。","invalidDateTime":"* 值无效。 请输入有效的日期值","invalidFloat":"* 值无效。 确保输入有效的浮点数","invalidInteger":"* 值无效。 确保输入有效的整数","outstandingDependencies":"* 参数互相依赖","requiredSingleValue":"* 允许单值","requiredValue":"* 期望输入的值","stringEmpty":"* 值无效。不允许输入空值。","unknown":"! 未知错误","valueIsNull":"* 值不能为空","valueOutOfRange":"*值无效。 请从下拉框中选择正确的值"}},{"lng":"zh","ns":"paramsView","resources":{"boolTextFalse":"否","boolTextTrue":"是","boolTextUnspecified":"未指定","btnTextClear":"清除","btnTextPreview":"预览","btnTextReset":"重置","dateTimeRange":{"rangeTypes":{"Current_Day":"Current Day","Current_Hour":"Current Hour","Current_Minute":"Current Minute","Current_Month":"Current Month","Current_Quarter":"Current Quarter","Current_Week":"Current Week","Current_Year":"Current Year","LastSimple_Day":"Last Day","LastSimple_Hour":"Last Hour","LastSimple_Minute":"Last Minute","LastSimple_Month":"Last Month","LastSimple_Quarter":"Last Quarter","LastSimple_Second":"Last Second","LastSimple_Week":"Last Week","LastSimple_Year":"Last Year","LastToDateSimple_Day":"Last Day To Date","LastToDateSimple_Month":"Last Month To Date","LastToDateSimple_Quarter":"Last Quarter To Date","LastToDateSimple_Week":"Last Week To Date","LastToDateSimple_Year":"Last Year To Date","LastToDate_Day":"Last {{count}} Days To Date","LastToDate_Day_plural":"Last {{count}} Days To Date","LastToDate_Month":"Last {{count}} Months To Date","LastToDate_Month_plural":"Last {{count}} Months To Date","LastToDate_Quarter":"Last {{count}} Quarters To Date","LastToDate_Quarter_plural":"Last {{count}} Quarters To Date","LastToDate_Week":"Last {{count}} Weeks To Date","LastToDate_Week_plural":"Last {{count}} Weeks To Date","LastToDate_Year":"Last {{count}} Years To Date","LastToDate_Year_plural":"Last {{count}} Years To Date","Last_Day":"Last {{count}} Days","Last_Day_plural":"Last {{count}} Days","Last_Hour":"Last {{count}} Hours","Last_Hour_plural":"Last {{count}} Hours","Last_Minute":"Last {{count}} Minutes","Last_Minute_plural":"Last {{count}} Minutes","Last_Month":"Last {{count}} Months","Last_Month_plural":"Last {{count}} Months","Last_Quarter":"Last {{count}} Quarters","Last_Quarter_plural":"Last {{count}} Quarters","Last_Second":"Last {{count}} Seconds","Last_Second_plural":"Last {{count}} Seconds","Last_Week":"Last {{count}} Weeks","Last_Week_plural":"Last {{count}} Weeks","Last_Year":"Last {{count}} Years","Last_Year_plural":"Last {{count}} Years","NextSimple_Day":"Next Day","NextSimple_Hour":"Next Hour","NextSimple_Minute":"Next Minute","NextSimple_Month":"Next Month","NextSimple_Quarter":"Next Quarter","NextSimple_Second":"Next Second","NextSimple_Week":"Next Week","NextSimple_Year":"Next Year","Next_Day":"Next {{count}} Days","Next_Day_plural":"Next {{count}} Days","Next_Hour":"Next {{count}} Hours","Next_Hour_plural":"Next {{count}} Hours","Next_Minute":"Next {{count}} Minutes","Next_Minute_plural":"Next {{count}} Minutes","Next_Month":"Next {{count}} Months","Next_Month_plural":"Next {{count}} Months","Next_Quarter":"Next {{count}} Quarters","Next_Quarter_plural":"Next {{count}} Quarters","Next_Second":"Next {{count}} Seconds","Next_Second_plural":"Next {{count}} Seconds","Next_Week":"Next {{count}} Weeks","Next_Week_plural":"Next {{count}} Weeks","Next_Year":"Next {{count}} Years","Next_Year_plural":"Next {{count}} Years","ToDate_Day":"Day-To-Date","ToDate_Month":"Month-To-Date","ToDate_Quarter":"Quarter-To-Date","ToDate_Week":"Week-To-Date","ToDate_Year":"Year-To-Date"}},"ddTitleToggle":"展开日历","ddeTextAllValues":"(全部)","ddeTextNull":"(null)","ddeTextSelectAllValues":"(全选)","ddeTextSelectValue":"(请选择)","drDaysViewHeaderFormat":"MMMM YYYY","drMonthYearOrder":"M-Y","drPlaceholderDateEnd":"结束","drPlaceholderDateStart":"开始","drShortcutsLabelLastMonth":"上月","drShortcutsLabelLastMonthToDate":"Last month to date","drShortcutsLabelLastWeek":"上周","drShortcutsLabelLastWeekToDate":"Last week to date","drShortcutsLabelLastYear":"去年","drShortcutsLabelLastYearToDate":"Last year to date","drShortcutsLabelMonthToDate":"本月至今","drShortcutsLabelWeekToDate":"本周至今","drShortcutsLabelYearToDate":"本年至今","drTabLabelAnnually":"年","drTabLabelDaily":"天","drTabLabelMonthly":"月","drTabLabelTime":"Time","drTextBack":"返回日历","drTextShortcutsList":"常用日期范围","drTitleToggle":"展开日历","dtDaysViewHeaderFormat":"MMMM YYYY","dtMonthYearOrder":"M-Y","dtTextBack":"返回日历","dtTextClear":"清除","dtTextSelectDate":"请选择日期:","dtTextSelectFromDate":"Select \\"From\\" Date...","dtTextSelectToDate":"Select \\"To\\" Date...","dtTextToday":"今天","dtTitleQuickRangePicker":"Common Ranges","dtTitleToggle":"展开日历","lblTextInvalidValue":"无效值","lblTextNoAvailableValues":"无可选值","listTextSelectAllValues":"(全选)","nwrpTextNull":"空"}},{"lng":"zh","ns":"singleDateFormat","resources":{"D":"dddd, MMMM D, YYYY","F":"dddd, MMMM D, YYYY h:mm:ss A","G":"M/D/YYYY h:mm:ss A","M":"MMMM D","O":"YYYY-MM-DDTHH:mm:ss.SSSZ","R":"ddd, DD MMM YYYY HH:mm:ss [GMT]","T":"h:mm:ss A","U":"dddd, MMMM D, YYYY h:mm:ss A","Y":"MMMM YYYY","d":"MM/D/YYYY","f":"dddd, MMMM D, YYYY h:mm A","g":"M/D/YYYY h:mm A","m":"MMMM D","o":"YYYY-MM-DDTHH:mm:ss.SSSZ","r":"ddd, DD MMM YYYY HH:mm:ss [GMT]","s":"YYYY-MM-DDTHH:mm:ss","t":"h:mm A","u":"YYYY-MM-DD HH:mm:ss[Z]","y":"MMMM YYYY"}},{"lng":"zh","ns":"viewer","resources":{"cancel-btn":"取消","document-view":{"aria-label":"文档视图"},"error":{"btnShowDetails":"Show Details","dismiss":"忽略","dismiss-all":"忽略全部信息","notificationsDetails":"Notifications details","showAll":"Show All","textError":"Error","textError_plural":"Errors","textNotification":"Notification","textNotification_plural":"Notifications","textWarning":"Warning","textWarning_plural":"Warnings","titleCancelTask":"Cancel this task","titleCollapse":"Collapse","titleExpand":"Expand"},"errors":{"noHostElement":"无法加载主元素"},"menu":{"aria-label":"菜单","pin-button-title":"固定","toogleText":"展开菜单"},"progress":{"page":"页面"},"search":{"cancel-btn":"取消","clear-btn":"清楚","didn-find-msg":"未找到任何相关信息","match-case":"匹配大小写","more-results-btn":"更多结果","paneltitle":"查找","search-cancelled-msg":"查询已经在第{{page}}页取消","search-results":"查找结果","start-search-btn":"查找","whole-word":"全字查找"},"sidebar":{"aria-label":"侧边栏","collapse-btn":"折叠","expand-btn":"展开"},"toolbar":{"aria-label":"工具栏","cancel":"取消","expand":"展开工具栏","fullscreen":"全屏展示","gotofirst":"第一页","gotolast":"最后一页","gotonext":"下一页","gotoprevious":"上一页","hist-back":"历史: 返回上一级","hist-fwd":"历史: 跳转下一级","hist-parent":"历史: 返回主报表","movetool":"移动工具","refresh":"刷新","zoom-fitpage":"整页","zoom-fitwidth":"页宽","zoom-menu-header":"缩放模式","zoom-zoomin":"放大","zoom-zoomout":"缩小"},"top-bottom-panel":{"aria-label":"控制面板"}}},{"lng":"zh","ns":"arjsv","resources":{"about!label":"关于","errors":{"cant-refresh-needparams":"刷新失败 (有的参数未指定值)","cant-render-needparams":"无法加载报表（有的参数未指定值）","invalid-hidden-param":"某些隐藏参数的值无效","no-current-doc":"没有当前的文件！","open-bad-fileformat":"错误: 仅支持 JSON 报表定义","unknown-export-format":"无效的格式键或功能， {{format}} 格式无法加载"},"export":{"progress-message":"页码 {{msg}}/{{total}}...","progress-message-starting":"开始导出..."},"panels":{"report-sections":"显示/隐藏报表页面"},"reportlist":{"openfile":"打开文件"},"sidebar":{"export-title":"导出","openreport-title":"打开报表","parameters-title":"参数","toc-title":"目录"},"toolbar":{"continuous":"连续模式","galleymode":"画廊模式","preparingprint":"准备打印中...","print":"打印","singlepage":"单页视图"}}},{"lng":"zh","ns":"exporthtml","resources":{"friendlyName":"HTML 文件","messages":{"cancel-message":"用户取消了导出过程","embedimages-is-zip-only":"将图像作为外部文件嵌入仅适用于压缩包 \\"zip\\" 输出类型","first-page-render-failed":"首页渲染失败","multipage-is-zip-only":"多页导出仅适用于压缩包 \\"zip\\""},"settings":{"autoPrint":{"category":"","label":"打开时打印"},"embedImages":{"category":"输出","enum":{"embed":"内嵌","external":"外部","none":"空"},"label":"内嵌图像"},"filename":{"category":"","label":"文件名"},"multiPage":{"category":"输出","label":"多页"},"outputType":{"category":"输出","enum":{"auto":"自动","html":"HTML","zip":"ZIP"},"label":"输出类型"},"title":{"category":"","label":"标题"}}}},{"lng":"zh","ns":"exportpdf","resources":{"friendlyName":"PDF 文件","messages":{"cancel-message":"用户取消了导出过程","first-page-render-failed":"第一页渲染失败","pdfa-fonts-embed-failure":"导出PDF/A 时需要注册所有字体，但未找到字体：\\"{{font}}\\" "},"settings":{"annotating":{"category":"安全性","label":"允许批注"},"author":{"category":"信息","label":"作者"},"autoPrint":{"category":"","label":"打开时打印"},"contentAccessibility":{"category":"安全性","label":"允许内容可访问性"},"copying":{"category":"安全性","label":"允许复制"},"documentAssembly":{"category":"安全性","label":"允许文档程序集"},"filename":{"category":"","label":"文件名称"},"keywords":{"category":"信息","label":"关键词"},"modifying":{"category":"安全性","label":"允许修改"},"ownerPassword":{"category":"安全性","label":"作者密码"},"pdfVersion":{"category":"","label":"PDF 版本"},"printing":{"category":"安全性","enum":{"highResolution":"高分辨率","lowResolution":"低分辨率","none":"空值"},"label":"允许打印"},"subject":{"category":"信息","label":"主题"},"title":{"category":"信息","label":"标题"},"userPassword":{"category":"安全性","label":"用户密码"}}}},{"lng":"zh","ns":"exporttabular-data","resources":{"friendlyName":"表格类数据","settings":{"colSeparator":{"category":"csv","label":"列分隔符"},"filename":{"category":"","label":"文件名称"},"outputType":{"category":"输出","enum":{"plain":"常规","zip":"ZIP"},"label":"输出类型"},"quotationSymbol":{"category":"csv","label":"引号"},"rowSeparator":{"category":"csv","label":"行分隔符"},"tableSeparator":{"category":"csv","label":"表格分隔符"}}}},{"lng":"zh","ns":"exportxlsx","resources":{"friendlyName":"Excel workbook (xlsx)","messages":{"cancel-message":"用户取消了导出过程","first-page-render-failed":"首页渲染失败"},"settings":{"creator":{"category":"信息","label":"作者"},"filename":{"category":"","label":"文件名称"},"orientation":{"category":"页面","enum":{"landscape":"横向","portrait":"纵向"},"label":"方向"},"password":{"category":"","label":"密码"},"sheetName":{"category":"","label":"表单名称"},"size":{"category":"页面","enum":{"A4":"A4","A5":"A5","B5":"B5 (JIS)","Double_Japan_Postcard_Rotated":"Double Japan Postcard Rotated","Envelope_10":"Envelope #10","Envelope_B5":"Envelope B5","Envelope_C5":"Envelope C5","Envelope_DL":"Envelope DL","Envelope_Monarch":"Envelope Monarch","Executive":"Executive","K16_197x273_mm":"16K 197x273 m","Legal":"Legal","Letter":"信纸"},"label":"尺寸"}}}}]')},function(e){e.exports=JSON.parse('[{"lng":"ja","ns":"core","resources":{"barcode-symbology-not-supported":"\\"{{symbology}}\\" はサポートされていません","default-invalid-barcode-text":"エラー","errors":{"compile":"コンパイルエラーが発生しました: {{details}}.","csvdataprovider":{"header-parse":"列ヘッダを解析できませんでした: \\"{{headerValue}}\\""},"data-processing":"データプロセスでエラーが発生しました: {{details}}.","dataprovider":{"commandtext-invalid":"無効なデータプロバイダコマンドです: {{commandText}}","connectstring-invalid":"接続文字列が無効です: {{connectString}}","no-data":"データプロバイダ設定が無効です ソースまたはデータが指定されていません"},"fetch-failed":"データを読み込むことができませんでした \\"{{uri}}\\": {{responseStatus}} {{responseText}}.","generic-error":"{{errorMessage}}","invalid-datasetname":"無効なデータセット: \\"{{dataSetName}}\\"","invalid-definition":"レポート定義が無効です: {{details}}.","json-parse":"JSONの解析に失敗しました: {{errorMessage}}. ソース: \\"{{jsonString}}\\".","jsondataprovider":{"no-global-data":"データセットにデータがありません \\"{{datasetName}}\\" - {{globalPath}} = {{globalValue}}"},"layout":"レポートレイアウトでエラーが発生しました: {{details}}.","no-datasets":"データセットが定義されていません","print":{"cancel-print-message":"ユーザーにより印刷はキャンセルされました","first-page-render-failed":"開始ページのレンダリングに失敗しました","open-window-failed":"新しいウィンドウを表示できませんでした"},"report-invalid":"レポート \\"{{reportUri}}\\" は無効です: {{error}}","report-not-available":"レポート \\"{{reportUri}}\\" は利用できません: {{error}}","reportPartsLibraries-invalid":"Report Parts library \\"{{reportUri}}\\" is invalid: {{error}}","reportPartsLibraries-not-available":"Report Parts library \\"{{reportUri}}\\" is not available: {{error}}","reportPartsLibraries-unknown":"Unknown Report Parts library \\"{{libraryName}}\\"","subreport-invalid":"サブレポート \\"{{reportUri}}\\" は無効です: {{error}}","subreport-not-available":"サブレポート \\"{{reportUri}}\\" は利用できません: {{error}}","unknown-dataprovider":"不明なデータプロバイダ \\"{{providerName}}\\" が指定されています","unknown-datasource":"不明なデータソース \\"{{dataSourceName}}\\" が指定されています"},"report-item-not-supported":"レポートアイテム \\"{{itemType}}\\" はサポートされていません"}},{"lng":"ja","ns":"export","resources":{"boolTextFalse":"いいえ","boolTextTrue":"はい","cancel-btn":"キャンセル","emptyTextPlaceholder":"<空>","file-format-label":"ファイル形式","inprogress-lbl":"エクスポート中","nodocument":"ドキュメントが選択されていません","start-btn":"エクスポート","titleDecrease":"減らす","titleIncrease":"増やす","titleToggle":"展開..."}},{"lng":"ja","ns":"params","resources":{"bool-false-label":"いいえ","bool-true-label":"はい","datePlaceholder":"日付を選択してください..","daysViewHeaderFormat":"YYYY年MM月","ddpTextNull":"(null)","ddpTextSelectAllValues":"(すべて選択)","ddpTextSelectValue":"(値を選択してください)","null":"Null","paramsTextInvalidValue":"無効な値","preview-btn-title":"プレビュー"}},{"lng":"ja","ns":"paramsValidation","resources":{"integerTooBig":"* 値は53ビット整数を超えることはできません","invalidBoolean":"* 値が無効です  有効な真理値を入力してください","invalidDateTime":"* 値が無効です  有効な日付を入力してください","invalidFloat":"* 値が無効です  有効な浮動小数点数を入力してください","invalidInteger":"* 値が無効です  有効な整数を入力してください","outstandingDependencies":"* パラメータに未解決の依存関係があります","requiredSingleValue":"* 単一値が期待される値に複数値が検出されました","requiredValue":"* 期待値","stringEmpty":"* 値が無効です  入力した値が有効であることを確認してください","unknown":"! 不明なエラー","valueIsNull":"* 値をnullにすることはできません","valueOutOfRange":"* 値が無効です  リストから正しい値を選択してください"}},{"lng":"ja","ns":"paramsView","resources":{"boolTextFalse":"いいえ","boolTextTrue":"はい","boolTextUnspecified":"指定なし","btnTextClear":"クリア","btnTextPreview":"プレビュー","btnTextReset":"リセット","dateTimeRange":{"rangeTypes":{"Current_Day":"Current Day","Current_Hour":"Current Hour","Current_Minute":"Current Minute","Current_Month":"Current Month","Current_Quarter":"Current Quarter","Current_Week":"Current Week","Current_Year":"Current Year","LastSimple_Day":"Last Day","LastSimple_Hour":"Last Hour","LastSimple_Minute":"Last Minute","LastSimple_Month":"Last Month","LastSimple_Quarter":"Last Quarter","LastSimple_Second":"Last Second","LastSimple_Week":"Last Week","LastSimple_Year":"Last Year","LastToDateSimple_Day":"Last Day To Date","LastToDateSimple_Month":"Last Month To Date","LastToDateSimple_Quarter":"Last Quarter To Date","LastToDateSimple_Week":"Last Week To Date","LastToDateSimple_Year":"Last Year To Date","LastToDate_Day":"Last {{count}} Days To Date","LastToDate_Day_plural":"Last {{count}} Days To Date","LastToDate_Month":"Last {{count}} Months To Date","LastToDate_Month_plural":"Last {{count}} Months To Date","LastToDate_Quarter":"Last {{count}} Quarters To Date","LastToDate_Quarter_plural":"Last {{count}} Quarters To Date","LastToDate_Week":"Last {{count}} Weeks To Date","LastToDate_Week_plural":"Last {{count}} Weeks To Date","LastToDate_Year":"Last {{count}} Years To Date","LastToDate_Year_plural":"Last {{count}} Years To Date","Last_Day":"Last {{count}} Days","Last_Day_plural":"Last {{count}} Days","Last_Hour":"Last {{count}} Hours","Last_Hour_plural":"Last {{count}} Hours","Last_Minute":"Last {{count}} Minutes","Last_Minute_plural":"Last {{count}} Minutes","Last_Month":"Last {{count}} Months","Last_Month_plural":"Last {{count}} Months","Last_Quarter":"Last {{count}} Quarters","Last_Quarter_plural":"Last {{count}} Quarters","Last_Second":"Last {{count}} Seconds","Last_Second_plural":"Last {{count}} Seconds","Last_Week":"Last {{count}} Weeks","Last_Week_plural":"Last {{count}} Weeks","Last_Year":"Last {{count}} Years","Last_Year_plural":"Last {{count}} Years","NextSimple_Day":"Next Day","NextSimple_Hour":"Next Hour","NextSimple_Minute":"Next Minute","NextSimple_Month":"Next Month","NextSimple_Quarter":"Next Quarter","NextSimple_Second":"Next Second","NextSimple_Week":"Next Week","NextSimple_Year":"Next Year","Next_Day":"Next {{count}} Days","Next_Day_plural":"Next {{count}} Days","Next_Hour":"Next {{count}} Hours","Next_Hour_plural":"Next {{count}} Hours","Next_Minute":"Next {{count}} Minutes","Next_Minute_plural":"Next {{count}} Minutes","Next_Month":"Next {{count}} Months","Next_Month_plural":"Next {{count}} Months","Next_Quarter":"Next {{count}} Quarters","Next_Quarter_plural":"Next {{count}} Quarters","Next_Second":"Next {{count}} Seconds","Next_Second_plural":"Next {{count}} Seconds","Next_Week":"Next {{count}} Weeks","Next_Week_plural":"Next {{count}} Weeks","Next_Year":"Next {{count}} Years","Next_Year_plural":"Next {{count}} Years","ToDate_Day":"Day-To-Date","ToDate_Month":"Month-To-Date","ToDate_Quarter":"Quarter-To-Date","ToDate_Week":"Week-To-Date","ToDate_Year":"Year-To-Date"}},"ddTitleToggle":"カレンダーを開く","ddeTextAllValues":"(すべて)","ddeTextNull":"(null)","ddeTextSelectAllValues":"(すべて選択)","ddeTextSelectValue":"(値を選択してください)","drDaysViewHeaderFormat":"YYYY年MM月","drMonthYearOrder":"M-Y","drPlaceholderDateEnd":"終わり","drPlaceholderDateStart":"初め","drShortcutsLabelLastMonth":"先月","drShortcutsLabelLastMonthToDate":"Last month to date","drShortcutsLabelLastWeek":"先週","drShortcutsLabelLastWeekToDate":"Last week to date","drShortcutsLabelLastYear":"昨年","drShortcutsLabelLastYearToDate":"Last year to date","drShortcutsLabelMonthToDate":"月計","drShortcutsLabelWeekToDate":"週計","drShortcutsLabelYearToDate":"年計","drTabLabelAnnually":"年次","drTabLabelDaily":"日次","drTabLabelMonthly":"月次","drTabLabelTime":"Time","drTextBack":"カレンダーに戻る","drTextShortcutsList":"共通の範囲","drTitleToggle":"カレンダーを開く","dtDaysViewHeaderFormat":"YYYY年MM月","dtMonthYearOrder":"M-Y","dtTextBack":"カレンダーに戻る","dtTextClear":"クリア","dtTextSelectDate":"日付を選択してください...","dtTextSelectFromDate":"Select \\"From\\" Date...","dtTextSelectToDate":"Select \\"To\\" Date...","dtTextToday":"今日","dtTitleQuickRangePicker":"Common Ranges","dtTitleToggle":"カレンダーを開く","lblTextInvalidValue":"無効な値","lblTextNoAvailableValues":"利用可能な値がありません","listTextSelectAllValues":"(すべて選択)","nwrpTextNull":"Null"}},{"lng":"ja","ns":"singleDateFormat","resources":{"D":"dddd, MMMM D, YYYY","F":"dddd, MMMM D, YYYY h:mm:ss A","G":"M/D/YYYY h:mm:ss A","M":"MMMM D","O":"YYYY-MM-DDTHH:mm:ss.SSSZ","R":"ddd, DD MMM YYYY HH:mm:ss [GMT]","T":"h:mm:ss A","U":"dddd, MMMM D, YYYY h:mm:ss A","Y":"MMMM YYYY","d":"MM/D/YYYY","f":"dddd, MMMM D, YYYY h:mm A","g":"M/D/YYYY h:mm A","m":"MMMM D","o":"YYYY-MM-DDTHH:mm:ss.SSSZ","r":"ddd, DD MMM YYYY HH:mm:ss [GMT]","s":"YYYY-MM-DDTHH:mm:ss","t":"h:mm A","u":"YYYY-MM-DD HH:mm:ss[Z]","y":"MMMM YYYY"}},{"lng":"ja","ns":"viewer","resources":{"cancel-btn":"キャンセル","document-view":{"aria-label":"ドキュメントビュー"},"error":{"btnShowDetails":"Show Details","dismiss":"通知を削除","dismiss-all":"全ての通知を削除","notificationsDetails":"Notifications details","showAll":"Show All","textError":"Error","textError_plural":"Errors","textNotification":"Notification","textNotification_plural":"Notifications","textWarning":"Warning","textWarning_plural":"Warnings","titleCancelTask":"Cancel this task","titleCollapse":"Collapse","titleExpand":"Expand"},"errors":{"noHostElement":"指定されたHTML要素が見つかりません"},"menu":{"aria-label":"メニュー","pin-button-title":"ピン留め","toogleText":"メニューを開く"},"progress":{"page":"ページ"},"search":{"cancel-btn":"キャンセル","clear-btn":"クリア","didn-find-msg":"一致する情報が見つかりませんでした","match-case":"大文字と小文字を区別","more-results-btn":"さらに検索結果を表示","paneltitle":"検索","search-cancelled-msg":"{{page}}ページで検索をキャンセルしました","search-results":"検索結果","start-search-btn":"検索","whole-word":"単語単位で検索"},"sidebar":{"aria-label":"サイドバー","collapse-btn":"折りたたむ","expand-btn":"展開"},"toolbar":{"aria-label":"ツールバー","cancel":"キャンセル","expand":"ツールバーを展開する","fullscreen":"フルスクリーン","gotofirst":"先頭ページ","gotolast":"最終ページ","gotonext":"次ページ","gotoprevious":"前ページ","hist-back":"戻る","hist-fwd":"進む","hist-parent":"親レポートに戻る","movetool":"移動","refresh":"更新","zoom-fitpage":"ページ全体","zoom-fitwidth":"ページ幅","zoom-menu-header":"ズーム","zoom-zoomin":"拡大","zoom-zoomout":"縮小"},"top-bottom-panel":{"aria-label":"その他パネル"}}},{"lng":"ja","ns":"arjsv","resources":{"about!label":"Viewerについて","errors":{"cant-refresh-needparams":"更新に失敗しました（パラメータが不足しています）","cant-render-needparams":"レポートを表示できません（パラメータが不足しています）","invalid-hidden-param":"非表示パラメータの一部に無効な値があります","no-current-doc":"ドキュメントが選択されていません！","open-bad-fileformat":"エラー: JSONレポート定義のみがサポートされています","unknown-export-format":"無効なファイル形式または機能のため、{{format}}をロードできませんでした"},"export":{"progress-message":"ページ {{msg}}/{{total}}...","progress-message-starting":"出力中..."},"panels":{"report-sections":"レポートエリアの表示/非表示"},"reportlist":{"openfile":"レポートファイルを開く"},"sidebar":{"export-title":"エクスポート","openreport-title":"レポートファイルを開く","parameters-title":"パラメータ","toc-title":"目次"},"toolbar":{"continuous":"連続ページ","galleymode":"ゲラモード","preparingprint":"印刷の準備...","print":"印刷...","singlepage":"単一ページ"}}},{"lng":"ja","ns":"exporthtml","resources":{"friendlyName":"Webページ(HTML)","messages":{"cancel-message":"ユーザーによりエクスポートはキャンセルされました","embedimages-is-zip-only":"画像の埋め込みはzip形式でのみ利用可能です","first-page-render-failed":"開始ページのレンダリングに失敗しました","multipage-is-zip-only":"複数ページをまとめて保存はzip形式でのみ利用可能です"},"settings":{"autoPrint":{"category":"その他","label":"表示時に印刷"},"embedImages":{"category":"出力","enum":{"embed":"埋込","external":"別ファイルに保存","none":"なし"},"label":"画像の埋め込み"},"filename":{"category":"その他","label":"ファイル名"},"multiPage":{"category":"出力","label":"複数ページをまとめて保存"},"outputType":{"category":"出力","enum":{"auto":"自動","html":"HTML形式で保存","zip":"ZIP形式で保存"},"label":"出力形式"},"title":{"category":"概要","label":"タイトル"}}}},{"lng":"ja","ns":"exportpdf","resources":{"friendlyName":"PDFファイル(PDF)","messages":{"cancel-message":"ユーザーによりエクスポートはキャンセルされました","first-page-render-failed":"開始ページのレンダリングに失敗しました","pdfa-fonts-embed-failure":"PDF/Aでは使用する全てのフォントを登録する必要がありますが、フォント名 \\"{{font}}\\" が見つかりませんでした。"},"settings":{"annotating":{"category":"セキュリティ","label":"注釈"},"author":{"category":"概要","label":"作成者"},"autoPrint":{"category":"その他","label":"表示時に印刷"},"contentAccessibility":{"category":"セキュリティ","label":"アクセシビリティを有効にする"},"copying":{"category":"セキュリティ","label":"内容のコピー"},"documentAssembly":{"category":"セキュリティ","label":"文書アセンブリ"},"filename":{"category":"その他","label":"ファイル名"},"keywords":{"category":"概要","label":"キーワード"},"modifying":{"category":"セキュリティ","label":"文書の変更"},"ownerPassword":{"category":"セキュリティ","label":"権限パスワード"},"pdfVersion":{"category":"その他","label":"PDFのバージョン"},"printing":{"category":"セキュリティ","enum":{"highResolution":"高解像度","lowResolution":"低解像度","none":"許可しない"},"label":"印刷"},"subject":{"category":"概要","label":"サブタイトル"},"title":{"category":"概要","label":"タイトル"},"userPassword":{"category":"セキュリティ","label":"文書を開くパスワード"}}}},{"lng":"ja","ns":"exporttabular-data","resources":{"friendlyName":"CSVデータ(CSV)","settings":{"colSeparator":{"category":"CSV","label":"区切り文字"},"filename":{"category":"","label":"ファイル名"},"outputType":{"category":"出力","enum":{"plain":"テキスト","zip":"ZIP"},"label":"出力形式"},"quotationSymbol":{"category":"CSV","label":"テキスト修飾子"},"rowSeparator":{"category":"CSV","label":"改行コード"},"tableSeparator":{"category":"CSV","label":"テーブル区切り文字"}}}},{"lng":"ja","ns":"exportxlsx","resources":{"friendlyName":"Microsoft Excelブック(xlsx)","messages":{"cancel-message":"ユーザーによりエクスポートはキャンセルされました","first-page-render-failed":"開始ページのレンダリングに失敗しました"},"settings":{"creator":{"category":"概要","label":"作成者"},"filename":{"category":"その他","label":"ファイル名"},"orientation":{"category":"印刷設定","enum":{"landscape":"横","portrait":"縦"},"label":"印刷の向き"},"password":{"category":"その他","label":"パスワード"},"sheetName":{"category":"その他","label":"シート名"},"size":{"category":"印刷設定","enum":{"A4":"A4","A5":"A5","B5":"B5 (JIS)","Double_Japan_Postcard_Rotated":"往復はがき 横","Envelope_10":"封筒 10","Envelope_B5":"封筒 B5","Envelope_C5":"封筒 C5","Envelope_DL":"封筒 DL","Envelope_Monarch":"封筒 Monarch","Executive":"エグゼクティヴ","K16_197x273_mm":"16K 用紙","Legal":"リーガル","Letter":"レター"},"label":"用紙サイズ"}}}}]')},function(e){e.exports=JSON.parse('[{"lng":"ko","ns":"core","resources":{"barcode-symbology-not-supported":"\\"{{symbology}}\\"은(는) 지원되지 않습니다.","default-invalid-barcode-text":"오류","errors":{"compile":"보고서 컴파일 오류: {{details}}.","csvdataprovider":{"header-parse":"열 헤더를 구문 분석할 수 없음: \\"{{headerValue}}\\""},"data-processing":"데이터 처리 오류: {{details}}.","dataprovider":{"commandtext-invalid":"잘못된 데이터 공급자 CommandText: {{commandText}}.","connectstring-invalid":"잘못된 데이터 소스 ConnectString: {{connectString}}.","no-data":"잘못된 데이터 공급자 설정. 소스 또는 데이터가 지정되지 않았습니다."},"fetch-failed":"\\"{{uri}}\\": {{responseStatus}} {{responseText}}에서 데이터를 로드할 수 없습니다.","generic-error":"{{errorMessage}}","invalid-datasetname":"잘못된 DataSetName: \\"{{dataSetName}}\\"","invalid-definition":"보고서 정의가 유효하지 않음: {{details}}.","json-parse":"JSON 파싱에 실패했습니다: {{errorMessage}}. 소스 문자열: \\"{{jsonString}}\\".","jsondataprovider":{"no-global-data":"데이터셋\\"{{datasetName}}\\" - {{globalPath}} = {{globalValue}}에서 사용가능한 데이터가 없습니다."},"layout":"보고서 레이아웃 오류: {{details}}.","no-datasets":"정의된 데이터 집합이 없습니다.","print":{"cancel-print-message":"사용자가 인쇄 프로세스를 취소했습니다.","first-page-render-failed":"첫 번째 페이지 렌더링 실패","open-window-failed":"새 창을 열 수 없음"},"report-invalid":"\\"{{reportUri}}\\" 보고서가 잘못됨: {{error}}","report-not-available":"\\"{{reportUri}}\\" 보고서가 사용할 수 없음: {{error}}","reportPartsLibraries-invalid":"Report Parts library \\"{{reportUri}}\\" is invalid: {{error}}","reportPartsLibraries-not-available":"Report Parts library \\"{{reportUri}}\\" is not available: {{error}}","reportPartsLibraries-unknown":"Unknown Report Parts library \\"{{libraryName}}\\"","subreport-invalid":"\\"{{reportUri}}\\" 하위 보고서가 잘못됨: {{error}}","subreport-not-available":"\\"{{reportUri}}\\" 하위 보고서를 사용할 수 없음: {{error}}","unknown-dataprovider":"알 수 없는 데이터 공급자 \\"{{providerName}}\\"","unknown-datasource":"알 수 없는 데이터 소스 \\"{{dataSourceName}}\\""},"report-item-not-supported":"\\"{{itemType}}\\" 보고서 항목이 지원되지 않습니다."}},{"lng":"ko","ns":"export","resources":{"boolTextFalse":"거짓","boolTextTrue":"참","cancel-btn":"취소","emptyTextPlaceholder":"<비어있음>","file-format-label":"서식","inprogress-lbl":"내보내기 진행 중","nodocument":"선택한 문서 없음","start-btn":"내보내기","titleDecrease":"감소","titleIncrease":"증가","titleToggle":"확장..."}},{"lng":"ko","ns":"params","resources":{"bool-false-label":"거짓","bool-true-label":"참","datePlaceholder":"날짜 선택","daysViewHeaderFormat":"MMMM YYYY","ddpTextNull":"(null)","ddpTextSelectAllValues":"(모두 선택)","ddpTextSelectValue":"(값 선택)","null":"Null","paramsTextInvalidValue":"잘못된 값","preview-btn-title":"미리 보기"}},{"lng":"ko","ns":"paramsValidation","resources":{"integerTooBig":"* 값이 53비트 정수를 초과할 수 없습니다.","invalidBoolean":"* 값이 잘못되었습니다. 유효한 부울을 입력했는지 확인하십시오.","invalidDateTime":"* 값이 잘못되었습니다. 유효한 날짜를 입력했는지 확인하십시오.","invalidFloat":"* 값이 잘못되었습니다. 유효한 부동을 입력했는지 확인하십시오.","invalidInteger":"* 값이 잘못되었습니다. 유효한 정수를 입력했는지 확인하십시오.","outstandingDependencies":"* 매개 변수에 종속성이 있습니다.","requiredSingleValue":"* 단일 값이 필요합니다.","requiredValue":"* 값이 필요합니다.","stringEmpty":"* 값이 잘못되었습니다. 빈 문자열은 허용되지 않습니다.","unknown":"! 알 수 없는 유효성 검사 오류","valueIsNull":"* 값은 null일 수 없습니다.","valueOutOfRange":"* 값이 잘못되었습니다. 목록의 오른쪽에 있는 항목을 선택하십시오."}},{"lng":"ko","ns":"paramsView","resources":{"boolTextFalse":"거짓","boolTextTrue":"참","boolTextUnspecified":"지정되지 않음","btnTextClear":"지우기","btnTextPreview":"미리 보기","btnTextReset":"다시 설정","dateTimeRange":{"rangeTypes":{"Current_Day":"Current Day","Current_Hour":"Current Hour","Current_Minute":"Current Minute","Current_Month":"Current Month","Current_Quarter":"Current Quarter","Current_Week":"Current Week","Current_Year":"Current Year","LastSimple_Day":"Last Day","LastSimple_Hour":"Last Hour","LastSimple_Minute":"Last Minute","LastSimple_Month":"Last Month","LastSimple_Quarter":"Last Quarter","LastSimple_Second":"Last Second","LastSimple_Week":"Last Week","LastSimple_Year":"Last Year","LastToDateSimple_Day":"Last Day To Date","LastToDateSimple_Month":"Last Month To Date","LastToDateSimple_Quarter":"Last Quarter To Date","LastToDateSimple_Week":"Last Week To Date","LastToDateSimple_Year":"Last Year To Date","LastToDate_Day":"Last {{count}} Days To Date","LastToDate_Day_plural":"Last {{count}} Days To Date","LastToDate_Month":"Last {{count}} Months To Date","LastToDate_Month_plural":"Last {{count}} Months To Date","LastToDate_Quarter":"Last {{count}} Quarters To Date","LastToDate_Quarter_plural":"Last {{count}} Quarters To Date","LastToDate_Week":"Last {{count}} Weeks To Date","LastToDate_Week_plural":"Last {{count}} Weeks To Date","LastToDate_Year":"Last {{count}} Years To Date","LastToDate_Year_plural":"Last {{count}} Years To Date","Last_Day":"Last {{count}} Days","Last_Day_plural":"Last {{count}} Days","Last_Hour":"Last {{count}} Hours","Last_Hour_plural":"Last {{count}} Hours","Last_Minute":"Last {{count}} Minutes","Last_Minute_plural":"Last {{count}} Minutes","Last_Month":"Last {{count}} Months","Last_Month_plural":"Last {{count}} Months","Last_Quarter":"Last {{count}} Quarters","Last_Quarter_plural":"Last {{count}} Quarters","Last_Second":"Last {{count}} Seconds","Last_Second_plural":"Last {{count}} Seconds","Last_Week":"Last {{count}} Weeks","Last_Week_plural":"Last {{count}} Weeks","Last_Year":"Last {{count}} Years","Last_Year_plural":"Last {{count}} Years","NextSimple_Day":"Next Day","NextSimple_Hour":"Next Hour","NextSimple_Minute":"Next Minute","NextSimple_Month":"Next Month","NextSimple_Quarter":"Next Quarter","NextSimple_Second":"Next Second","NextSimple_Week":"Next Week","NextSimple_Year":"Next Year","Next_Day":"Next {{count}} Days","Next_Day_plural":"Next {{count}} Days","Next_Hour":"Next {{count}} Hours","Next_Hour_plural":"Next {{count}} Hours","Next_Minute":"Next {{count}} Minutes","Next_Minute_plural":"Next {{count}} Minutes","Next_Month":"Next {{count}} Months","Next_Month_plural":"Next {{count}} Months","Next_Quarter":"Next {{count}} Quarters","Next_Quarter_plural":"Next {{count}} Quarters","Next_Second":"Next {{count}} Seconds","Next_Second_plural":"Next {{count}} Seconds","Next_Week":"Next {{count}} Weeks","Next_Week_plural":"Next {{count}} Weeks","Next_Year":"Next {{count}} Years","Next_Year_plural":"Next {{count}} Years","ToDate_Day":"Day-To-Date","ToDate_Month":"Month-To-Date","ToDate_Quarter":"Quarter-To-Date","ToDate_Week":"Week-To-Date","ToDate_Year":"Year-To-Date"}},"ddTitleToggle":"확장","ddeTextAllValues":"(모두)","ddeTextNull":"(null)","ddeTextSelectAllValues":"(모두 선택)","ddeTextSelectValue":"(값 선택)","drDaysViewHeaderFormat":"MMMM YYYY","drMonthYearOrder":"M-Y","drPlaceholderDateEnd":"종료","drPlaceholderDateStart":"시작","drShortcutsLabelLastMonth":"지난달","drShortcutsLabelLastMonthToDate":"Last month to date","drShortcutsLabelLastWeek":"지난주","drShortcutsLabelLastWeekToDate":"Last week to date","drShortcutsLabelLastYear":"지난해","drShortcutsLabelLastYearToDate":"Last year to date","drShortcutsLabelMonthToDate":"월간 누계","drShortcutsLabelWeekToDate":"주간 누계","drShortcutsLabelYearToDate":"연간 누계","drTabLabelAnnually":"연간","drTabLabelDaily":"매일","drTabLabelMonthly":"매월","drTabLabelTime":"Time","drTextBack":"달력으로 돌아가기","drTextShortcutsList":"공통 범위","drTitleToggle":"달력 사용","dtDaysViewHeaderFormat":"MMMM YYYY","dtMonthYearOrder":"M-Y","dtTextBack":"달력으로 돌아가기","dtTextClear":"지우기","dtTextSelectDate":"날짜 선택...","dtTextSelectFromDate":"Select \\"From\\" Date...","dtTextSelectToDate":"Select \\"To\\" Date...","dtTextToday":"오늘","dtTitleQuickRangePicker":"Common Ranges","dtTitleToggle":"달력 사용","lblTextInvalidValue":"잘못된 값","lblTextNoAvailableValues":"사용이 불가능한 값","listTextSelectAllValues":"(모두 선택)","nwrpTextNull":"Null"}},{"lng":"ko","ns":"singleDateFormat","resources":{"D":"dddd, MMMM D, YYYY","F":"dddd, MMMM D, YYYY h:mm:ss A","G":"M/D/YYYY h:mm:ss A","M":"MMMM D","O":"YYYY-MM-DDTHH:mm:ss.SSSZ","R":"ddd, DD MMM YYYY HH:mm:ss [GMT]","T":"h:mm:ss A","U":"dddd, MMMM D, YYYY h:mm:ss A","Y":"MMMM YYYY","d":"MM/D/YYYY","f":"dddd, MMMM D, YYYY h:mm A","g":"M/D/YYYY h:mm A","m":"MMMM D","o":"YYYY-MM-DDTHH:mm:ss.SSSZ","r":"ddd, DD MMM YYYY HH:mm:ss [GMT]","s":"YYYY-MM-DDTHH:mm:ss","t":"h:mm A","u":"YYYY-MM-DD HH:mm:ss[Z]","y":"MMMM YYYY"}},{"lng":"ko","ns":"viewer","resources":{"cancel-btn":"취소","document-view":{"aria-label":"문서 보기"},"error":{"btnShowDetails":"Show Details","dismiss":"해제","dismiss-all":"모두 해제","notificationsDetails":"Notifications details","showAll":"Show All","textError":"Error","textError_plural":"Errors","textNotification":"Notification","textNotification_plural":"Notifications","textWarning":"Warning","textWarning_plural":"Warnings","titleCancelTask":"Cancel this task","titleCollapse":"Collapse","titleExpand":"Expand"},"errors":{"noHostElement":"호스트 요소를 찾을 수 없습니다."},"menu":{"aria-label":"메뉴","pin-button-title":"고정","toogleText":"메뉴 보기"},"progress":{"page":"페이지"},"search":{"cancel-btn":"취소","clear-btn":"지우기","didn-find-msg":"아무것도 찾을 수 없습니다.","match-case":"대/소문자 구분","more-results-btn":"더 많은 결과 보기","paneltitle":"검색","search-cancelled-msg":"{{page}}페이지에서 검색이 취소됨","search-results":"검색 결과","start-search-btn":"검색","whole-word":"단어 단위로"},"sidebar":{"aria-label":"세로 막대","collapse-btn":"축소","expand-btn":"확장"},"toolbar":{"aria-label":"도구 모음","cancel":"취소","expand":"툴바 보기","fullscreen":"전체 화면 토글","gotofirst":"처음으로 이동","gotolast":"마지막으로 이동","gotonext":"다음으로 이동","gotoprevious":"이전으로 이동","hist-back":"이전 내역 이동","hist-fwd":"다음 내역 이동","hist-parent":"상위 내역 이동","movetool":"이동 도구","refresh":"새로 고침","zoom-fitpage":"페이지 맞춤","zoom-fitwidth":"너비에 맞추기","zoom-menu-header":"확대/축소 모드","zoom-zoomin":"확대","zoom-zoomout":"축소"},"top-bottom-panel":{"aria-label":"추가 제어판"}}},{"lng":"ko","ns":"arjsv","resources":{"about!label":"정보","errors":{"cant-refresh-needparams":"새로 고침 실패(일부 매개 변수가 지정되지 않음)","cant-render-needparams":"렌더링할 수 없음(일부 매개 변수가 지정되지 않음)","invalid-hidden-param":"숨겨진 매개 변수 중 일부에 잘못된 값이 있습니다.","no-current-doc":"현재 문서가 없습니다!","open-bad-fileformat":"오류: JSON 보고서 정의만 지원됩니다.","unknown-export-format":"서식 키가 잘못되었거나 {{format}}에 필요한 모듈이 로드되지 않았습니다."},"export":{"progress-message":"{{msg}}/{{total}}페이지","progress-message-starting":"시작하는 중..."},"panels":{"report-sections":"보이기/숨기기 리포트 섹션"},"reportlist":{"openfile":"파일 열기"},"sidebar":{"export-title":"내보내기","openreport-title":"보고서 열기","parameters-title":"매개 변수","toc-title":"목차"},"toolbar":{"continuous":"스크롤 사용","galleymode":"갤러리 모드","preparingprint":"인쇄 준비 중...","print":"인쇄","singlepage":"한 페이지 보기"}}},{"lng":"ko","ns":"exporthtml","resources":{"friendlyName":"HTML 파일","messages":{"cancel-message":"사용자가 내보내기 프로세스를 취소했습니다.","embedimages-is-zip-only":"외부 파일로 이미지 포함은 \\"zip\\" 출력 형식에만 사용할 수 있습니다.","first-page-render-failed":"첫 번째 페이지 렌더링 실패","multipage-is-zip-only":"여러 페이지 내보내기는 \\"zip\\" 출력 형식에만 사용할 수 있습니다."},"settings":{"autoPrint":{"category":"","label":"열 때 인쇄"},"embedImages":{"category":"출력","enum":{"embed":"포함","external":"외부","none":"없음"},"label":"이미지 포함"},"filename":{"category":"","label":"파일 이름"},"multiPage":{"category":"출력","label":"여러 페이지"},"outputType":{"category":"출력","enum":{"auto":"자동","html":"HTML","zip":"우편번호"},"label":"출력 형식"},"title":{"category":"","label":"제목"}}}},{"lng":"ko","ns":"exportpdf","resources":{"friendlyName":"PDF 문서","messages":{"cancel-message":"사용자가 내보내기 프로세스를 취소했습니다.","first-page-render-failed":"첫 번째 페이지 렌더링 실패","pdfa-fonts-embed-failure":"PDF/A 는 모든 폰트가 등록되어 있어야 합니다만 \\"{{font}}\\"을 찾을 수 없습니다"},"settings":{"annotating":{"category":"보안","label":"주석 허용"},"author":{"category":"정보","label":"작성자"},"autoPrint":{"category":"","label":"열 때 인쇄"},"contentAccessibility":{"category":"보안","label":"콘텐츠 접근성 허용"},"copying":{"category":"보안","label":"복사 허용"},"documentAssembly":{"category":"보안","label":"문서 어셈블리 허용"},"filename":{"category":"","label":"파일 이름"},"keywords":{"category":"정보","label":"키워드"},"modifying":{"category":"보안","label":"수정 허용"},"ownerPassword":{"category":"보안","label":"소유자 암호"},"pdfVersion":{"category":"","label":"PDF 버전"},"printing":{"category":"보안","enum":{"highResolution":"높은 해상도","lowResolution":"낮은 해상도","none":"없음"},"label":"인쇄 허용"},"subject":{"category":"정보","label":"제목"},"title":{"category":"정보","label":"제목"},"userPassword":{"category":"보안","label":"사용자 암호"}}}},{"lng":"ko","ns":"exporttabular-data","resources":{"friendlyName":"테이블 형식 데이터","settings":{"colSeparator":{"category":"csv","label":"열 구분자"},"filename":{"category":"","label":"파일 이름"},"outputType":{"category":"출력","enum":{"plain":"Plain","zip":"ZIP"},"label":"출력 유형"},"quotationSymbol":{"category":"csv","label":"인용 기호"},"rowSeparator":{"category":"csv","label":"행 구분자"},"tableSeparator":{"category":"csv","label":"표 구분자"}}}},{"lng":"ko","ns":"exportxlsx","resources":{"friendlyName":"Excel 통합 문서(xlsx)","messages":{"cancel-message":"사용자가 내보내기 프로세스를 취소했습니다.","first-page-render-failed":"첫 번째 페이지 렌더링 실패"},"settings":{"creator":{"category":"정보","label":"작성자"},"filename":{"category":"","label":"파일 이름"},"orientation":{"category":"페이지","enum":{"landscape":"가로","portrait":"세로"},"label":"방향"},"password":{"category":"","label":"암호"},"sheetName":{"category":"","label":"시트 이름"},"size":{"category":"페이지","enum":{"A4":"A4","A5":"A5","B5":"B5(JIS)","Double_Japan_Postcard_Rotated":"Double Japan Postcard Rotated","Envelope_10":"봉투 #10","Envelope_B5":"봉투 B5","Envelope_C5":"봉투 C5","Envelope_DL":"봉투 DL","Envelope_Monarch":"Monarch 봉투","Executive":"Executive","K16_197x273_mm":"16K 197x273m","Legal":"Legal","Letter":"Letter"},"label":"크기"}}}}]')},function(e){e.exports=JSON.parse('[{"lng":"nl","ns":"core","resources":{"barcode-symbology-not-supported":"\\"{{symbology}}\\" wordt niet ondersteund","default-invalid-barcode-text":"FOUT","errors":{"compile":"Rapportage compileer fout: {{details}}.","csvdataprovider":{"header-parse":"Kan de header kolom niet verwerken: \\"{{headerValue}}\\""},"data-processing":"Gegevensverwerkingsfout: {{details}}.","dataprovider":{"commandtext-invalid":"Invalid data provider CommandText: {{commandText}}.","connectstring-invalid":"Invalid data source ConnectString: {{connectString}}.","no-data":"Invalid data provider settings. Source or data is not specified."},"fetch-failed":"Kan gegevens niet laden van \\"{{uri}}\\": {{responseStatus}} {{responseText}}.","generic-error":"{{errorMessage}}","invalid-datasetname":"Ongeldige DataSetName: \\"{{dataSetName}}\\"","invalid-definition":"Rapportage definitie is niet geldig: {{details}}.","json-parse":"Failed to parse JSON: {{errorMessage}}. Source string: \\"{{jsonString}}\\".","jsondataprovider":{"no-global-data":"No data available for DataSet \\"{{datasetName}}\\" - {{globalPath}} = {{globalValue}}"},"layout":"Rapportage lay-out fout: {{details}}.","no-datasets":"Geen DataSets gedefinieerd.","print":{"cancel-print-message":"Afdrukproces is geannuleerd door gebruiker","first-page-render-failed":"Weergave van eerste pagina mislukt","open-window-failed":"Kan geen nieuw venster openen"},"report-invalid":"Rapportage \\"{{reportUri}}\\" is ongeldig: {{error}}","report-not-available":"Rapportage \\"{{reportUri}}\\" is niet beschikbaar: {{error}}","reportPartsLibraries-invalid":"Report Parts library \\"{{reportUri}}\\" is invalid: {{error}}","reportPartsLibraries-not-available":"Report Parts library \\"{{reportUri}}\\" is not available: {{error}}","reportPartsLibraries-unknown":"Unknown Report Parts library \\"{{libraryName}}\\"","subreport-invalid":"Subrapport \\"{{reportUri}}\\" is ongeldig: {{error}}","subreport-not-available":"Subrapport \\"{{reportUri}}\\" is niet beschikbaar: {{error}}","unknown-dataprovider":"Onbekende data provider \\"{{providerName}}\\"","unknown-datasource":"Onbekende gegevensbron \\"{{dataSourceName}}\\""},"report-item-not-supported":"\\"{{itemType}}\\" rapportage onderdeel wordt niet ondersteund"}},{"lng":"nl","ns":"export","resources":{"boolTextFalse":"Nee","boolTextTrue":"Ja","cancel-btn":"Annuleren","emptyTextPlaceholder":"<leeg>","file-format-label":"Formatteren","inprogress-lbl":"Bezig met exporteren","nodocument":"Geen document geselecteerd","start-btn":"Exporteren","titleDecrease":"Verlaag met","titleIncrease":"Verhoog met","titleToggle":"Uitklappen..."}},{"lng":"nl","ns":"params","resources":{"bool-false-label":"Nee","bool-true-label":"Ja","datePlaceholder":"Selecteer datum..","daysViewHeaderFormat":"MMMM YYYY","ddpTextNull":"(null)","ddpTextSelectAllValues":"(selecteer alles)","ddpTextSelectValue":"(selecteer waarde)","null":"Null","paramsTextInvalidValue":"Ongeldige waarde","preview-btn-title":"Voorbeeld"}},{"lng":"nl","ns":"paramsValidation","resources":{"integerTooBig":"* Waarde mag niet groter zijn dan 53-bits geheel getal","invalidBoolean":"* Verkeerde waarde. Zorg ervoor dat u een geldige boolean invoert","invalidDateTime":"* Verkeerde waarde. Zorg ervoor dat u een geldige datum invoert","invalidFloat":"* Verkeerde waarde. Zorg ervoor dat u een geldige float invoert","invalidInteger":"* Verkeerde waarde. Zorg ervoor dat u een geldig geheel getal invoert","outstandingDependencies":"* Parameter openstaande afhankelijkheden","requiredSingleValue":"* Enkele waarde verwacht","requiredValue":"* Waarde verwacht","stringEmpty":"* Verkeerde waarde. Lege strings zijn niet toegestaan","unknown":"! Onbekende validatiefout","valueIsNull":"* Waarde kan niet null zijn","valueOutOfRange":"* Verkeerde waarde. Kies de juiste uit de lijst"}},{"lng":"nl","ns":"paramsView","resources":{"boolTextFalse":"Nee","boolTextTrue":"Ja","boolTextUnspecified":"Ongespecificeerd","btnTextClear":"Wissen","btnTextPreview":"Voorbeeld","btnTextReset":"Resetten","dateTimeRange":{"rangeTypes":{"Current_Day":"Current Day","Current_Hour":"Current Hour","Current_Minute":"Current Minute","Current_Month":"Current Month","Current_Quarter":"Current Quarter","Current_Week":"Current Week","Current_Year":"Current Year","LastSimple_Day":"Last Day","LastSimple_Hour":"Last Hour","LastSimple_Minute":"Last Minute","LastSimple_Month":"Last Month","LastSimple_Quarter":"Last Quarter","LastSimple_Second":"Last Second","LastSimple_Week":"Last Week","LastSimple_Year":"Last Year","LastToDateSimple_Day":"Last Day To Date","LastToDateSimple_Month":"Last Month To Date","LastToDateSimple_Quarter":"Last Quarter To Date","LastToDateSimple_Week":"Last Week To Date","LastToDateSimple_Year":"Last Year To Date","LastToDate_Day":"Last {{count}} Days To Date","LastToDate_Day_plural":"Last {{count}} Days To Date","LastToDate_Month":"Last {{count}} Months To Date","LastToDate_Month_plural":"Last {{count}} Months To Date","LastToDate_Quarter":"Last {{count}} Quarters To Date","LastToDate_Quarter_plural":"Last {{count}} Quarters To Date","LastToDate_Week":"Last {{count}} Weeks To Date","LastToDate_Week_plural":"Last {{count}} Weeks To Date","LastToDate_Year":"Last {{count}} Years To Date","LastToDate_Year_plural":"Last {{count}} Years To Date","Last_Day":"Last {{count}} Days","Last_Day_plural":"Last {{count}} Days","Last_Hour":"Last {{count}} Hours","Last_Hour_plural":"Last {{count}} Hours","Last_Minute":"Last {{count}} Minutes","Last_Minute_plural":"Last {{count}} Minutes","Last_Month":"Last {{count}} Months","Last_Month_plural":"Last {{count}} Months","Last_Quarter":"Last {{count}} Quarters","Last_Quarter_plural":"Last {{count}} Quarters","Last_Second":"Last {{count}} Seconds","Last_Second_plural":"Last {{count}} Seconds","Last_Week":"Last {{count}} Weeks","Last_Week_plural":"Last {{count}} Weeks","Last_Year":"Last {{count}} Years","Last_Year_plural":"Last {{count}} Years","NextSimple_Day":"Next Day","NextSimple_Hour":"Next Hour","NextSimple_Minute":"Next Minute","NextSimple_Month":"Next Month","NextSimple_Quarter":"Next Quarter","NextSimple_Second":"Next Second","NextSimple_Week":"Next Week","NextSimple_Year":"Next Year","Next_Day":"Next {{count}} Days","Next_Day_plural":"Next {{count}} Days","Next_Hour":"Next {{count}} Hours","Next_Hour_plural":"Next {{count}} Hours","Next_Minute":"Next {{count}} Minutes","Next_Minute_plural":"Next {{count}} Minutes","Next_Month":"Next {{count}} Months","Next_Month_plural":"Next {{count}} Months","Next_Quarter":"Next {{count}} Quarters","Next_Quarter_plural":"Next {{count}} Quarters","Next_Second":"Next {{count}} Seconds","Next_Second_plural":"Next {{count}} Seconds","Next_Week":"Next {{count}} Weeks","Next_Week_plural":"Next {{count}} Weeks","Next_Year":"Next {{count}} Years","Next_Year_plural":"Next {{count}} Years","ToDate_Day":"Day-To-Date","ToDate_Month":"Month-To-Date","ToDate_Quarter":"Quarter-To-Date","ToDate_Week":"Week-To-Date","ToDate_Year":"Year-To-Date"}},"ddTitleToggle":"Uitklappen","ddeTextAllValues":"(all)","ddeTextNull":"(null)","ddeTextSelectAllValues":"(selecteer alles)","ddeTextSelectValue":"(selecteer waarde)","drDaysViewHeaderFormat":"MMMM YYYY","drMonthYearOrder":"M-Y","drPlaceholderDateEnd":"Eind","drPlaceholderDateStart":"Start","drShortcutsLabelLastMonth":"Vorige maand","drShortcutsLabelLastMonthToDate":"Last month to date","drShortcutsLabelLastWeek":"Vorige week","drShortcutsLabelLastWeekToDate":"Last week to date","drShortcutsLabelLastYear":"Vorig jaar","drShortcutsLabelLastYearToDate":"Last year to date","drShortcutsLabelMonthToDate":"Maand tot nu","drShortcutsLabelWeekToDate":"Week tot nu","drShortcutsLabelYearToDate":"Jaar tot nu","drTabLabelAnnually":"Jaarlijks","drTabLabelDaily":"Dagelijks","drTabLabelMonthly":"Maandelijks","drTabLabelTime":"Time","drTextBack":"Terug naar de kalender","drTextShortcutsList":"Veel voorkomende bereiken","drTitleToggle":"Kalender uitklappen","dtDaysViewHeaderFormat":"MMMM YYYY","dtMonthYearOrder":"M-Y","dtTextBack":"Terug naar de kalender","dtTextClear":"Wissen","dtTextSelectDate":"Selecteer datum...","dtTextSelectFromDate":"Select \\"From\\" Date...","dtTextSelectToDate":"Select \\"To\\" Date...","dtTextToday":"Vandaag","dtTitleQuickRangePicker":"Common Ranges","dtTitleToggle":"Kalender uitklappen","lblTextInvalidValue":"Invalid Value","lblTextNoAvailableValues":"Geen waarden beschikbaar","listTextSelectAllValues":"(selecteer alles)","nwrpTextNull":"Null"}},{"lng":"nl","ns":"singleDateFormat","resources":{"D":"dddd, MMMM D, YYYY","F":"dddd, MMMM D, YYYY h:mm:ss A","G":"M/D/YYYY h:mm:ss A","M":"MMMM D","O":"YYYY-MM-DDTHH:mm:ss.SSSZ","R":"ddd, DD MMM YYYY HH:mm:ss [GMT]","T":"h:mm:ss A","U":"dddd, MMMM D, YYYY h:mm:ss A","Y":"MMMM YYYY","d":"MM/D/YYYY","f":"dddd, MMMM D, YYYY h:mm A","g":"M/D/YYYY h:mm A","m":"MMMM D","o":"YYYY-MM-DDTHH:mm:ss.SSSZ","r":"ddd, DD MMM YYYY HH:mm:ss [GMT]","s":"YYYY-MM-DDTHH:mm:ss","t":"h:mm A","u":"YYYY-MM-DD HH:mm:ss[Z]","y":"MMMM YYYY"}},{"lng":"nl","ns":"viewer","resources":{"cancel-btn":"Annuleren","document-view":{"aria-label":"Documentweergave"},"error":{"btnShowDetails":"Show Details","dismiss":"Afwijzen","dismiss-all":"Alles afwijzen","notificationsDetails":"Notifications details","showAll":"Show All","textError":"Error","textError_plural":"Errors","textNotification":"Notification","textNotification_plural":"Notifications","textWarning":"Warning","textWarning_plural":"Warnings","titleCancelTask":"Cancel this task","titleCollapse":"Collapse","titleExpand":"Expand"},"errors":{"noHostElement":"Kan het hostelement niet vinden."},"menu":{"aria-label":"Menu","pin-button-title":"Pin","toogleText":"Menu uitklappen"},"progress":{"page":"Pagina"},"search":{"cancel-btn":"Annuleren","clear-btn":"Wissen","didn-find-msg":"Niets gevonden.","match-case":"Identieke hoofdletters/kleine letters","more-results-btn":"Meer resultaten","paneltitle":"Zoeken","search-cancelled-msg":"Zoekopdracht geannuleerd op pagina {{page}}","search-results":"Zoekresultaten","start-search-btn":"Zoek","whole-word":"Heel woord"},"sidebar":{"aria-label":"Zijbalk","collapse-btn":"Inklappen","expand-btn":"Uitklappen"},"toolbar":{"aria-label":"Werkbalk","cancel":"Annuleren","expand":"Toolbar uitklappen","fullscreen":"Volledig scherm activeren","gotofirst":"Ga naar de eerste","gotolast":"Ga naar de vorige","gotonext":"Ga naar de volgende","gotoprevious":"Ga naar de laatste","hist-back":"Geschiedenis: Terug","hist-fwd":"Geschiedenis: Volgende","hist-parent":"Geschiedenis: Terug naar bovenliggende","movetool":"Verplaatsingstool","refresh":"Vernieuwen","zoom-fitpage":"Aanpassen aan de pagina","zoom-fitwidth":"Aanpassen aan breedte","zoom-menu-header":"Zoommodus","zoom-zoomin":"Inzoomen","zoom-zoomout":"Uitzoomen"},"top-bottom-panel":{"aria-label":"Extra bedieningspaneel"}}},{"lng":"nl","ns":"arjsv","resources":{"about!label":"Over","errors":{"cant-refresh-needparams":"Vernieuwen mislukt (niet alle parameters opgegeven)","cant-render-needparams":"Kan niet weergeven (niet alle parameters opgegeven)","invalid-hidden-param":"Sommige verborgen parameters hebben een ongeldige waarde.","no-current-doc":"Geen huidig document!","open-bad-fileformat":"Fout: alleen JSON-rapportdefinities worden ondersteund.","unknown-export-format":"Onbekend export type {{format}} of het is niet geladen."},"export":{"progress-message":"Pagina {{msg}} van {{total}}...","progress-message-starting":"Opstarten..."},"panels":{"report-sections":"Show/Hide Report Sections"},"reportlist":{"openfile":"Open bestand"},"sidebar":{"export-title":"Exporteren","openreport-title":"Rapport openen","parameters-title":"Parameters","toc-title":"Inhoudsopgave"},"toolbar":{"continuous":"Doorlopend","galleymode":"Galerijmodus","preparingprint":"Afdrukken voorbereiden...","print":"Afdrukken...","singlepage":"Enkele paginaweergave"}}},{"lng":"nl","ns":"exporthtml","resources":{"friendlyName":"HTML bestand","messages":{"cancel-message":"Exportproces is geannuleerd door gebruiker","embedimages-is-zip-only":"Afbeeldingen insluiten als externe bestanden is alleen beschikbaar voor het uitvoertype \\"zip\\"","first-page-render-failed":"Weergeven van eerste pagina mislukt","multipage-is-zip-only":"Export van meerdere pagina\'s is alleen beschikbaar voor het bestandsformaat \\"zip\\""},"settings":{"autoPrint":{"category":"","label":"Afdrukken bij openen"},"embedImages":{"category":"output","enum":{"embed":"Integreren","external":"Extern","none":"Geen"},"label":"Afbeeldingen insluiten"},"filename":{"category":"","label":"Bestandsnaam"},"multiPage":{"category":"output","label":"Meerdere pagina\'s"},"outputType":{"category":"output","enum":{"auto":"Automatisch","html":"HTML","zip":"ZIP"},"label":"Bestandsformaat"},"title":{"category":"","label":"Titel"}}}},{"lng":"nl","ns":"exportpdf","resources":{"friendlyName":"PDF document","messages":{"cancel-message":"Exportproces is geannuleerd door gebruiker","first-page-render-failed":"Weergave van eerste pagina mislukt","pdfa-fonts-embed-failure":"PDF/A requires all fonts to be registered, but \\"{{font}}\\" was not found"},"settings":{"annotating":{"category":"beveiliging","label":"Annoteren toestaan"},"author":{"category":"informatie","label":"Auteur"},"autoPrint":{"category":"","label":"Afdrukken bij openen"},"contentAccessibility":{"category":"beveiliging","label":"Toegankelijkheid van inhoud toestaan"},"copying":{"category":"beveiliging","label":"Kopiëren toestaan"},"documentAssembly":{"category":"beveiliging","label":"Document samenstellen toestaan"},"filename":{"category":"","label":"Bestandsnaam"},"keywords":{"category":"informatie","label":"Trefwoorden"},"modifying":{"category":"beveiliging","label":"Bewerken toestaan"},"ownerPassword":{"category":"beveiliging","label":"Eigenaar wachtwoord"},"pdfVersion":{"category":"","label":"PDF Versie"},"printing":{"category":"beveiliging","enum":{"highResolution":"Hoge resolutie","lowResolution":"Lage resolutie","none":"Geen"},"label":"Afdrukken toestaan"},"subject":{"category":"informatie","label":"Onderwerp"},"title":{"category":"informatie","label":"Titel"},"userPassword":{"category":"beveiliging","label":"Gebruikerswachtwoord"}}}},{"lng":"nl","ns":"exporttabular-data","resources":{"friendlyName":"Tabular data","settings":{"colSeparator":{"category":"csv","label":"Columns Separator"},"filename":{"category":"","label":"File Name"},"outputType":{"category":"output","enum":{"plain":"Plain","zip":"ZIP"},"label":"Output Type"},"quotationSymbol":{"category":"csv","label":"Quotation Symbol"},"rowSeparator":{"category":"csv","label":"Rows Separator"},"tableSeparator":{"category":"csv","label":"Tables Separator"}}}},{"lng":"nl","ns":"exportxlsx","resources":{"friendlyName":"Excel-werkmap (xlsx)","messages":{"cancel-message":"Exportproces is geannuleerd door gebruiker","first-page-render-failed":"Weergave van eerste pagina mislukt"},"settings":{"creator":{"category":"informatie","label":"Auteur"},"filename":{"category":"","label":"Bestandsnaam"},"orientation":{"category":"pagina","enum":{"landscape":"Landschap","portrait":"Portret"},"label":"Oriëntatie"},"password":{"category":"","label":"Wachtwoord"},"sheetName":{"category":"","label":"Bladnaam"},"size":{"category":"pagina","enum":{"A4":"A4","A5":"A5","B5":"B5 (JIS)","Double_Japan_Postcard_Rotated":"Double Japan Postcard Rotated","Envelope_10":"Envelope #10","Envelope_B5":"Envelope B5","Envelope_C5":"Envelope C5","Envelope_DL":"Envelope DL","Envelope_Monarch":"Envelope Monarch","Executive":"Executive","K16_197x273_mm":"16K 197x273 m","Legal":"Legal","Letter":"Letter"},"label":"Grootte"}}}}]')},function(e){e.exports=JSON.parse('[{"lng":"de","ns":"core","resources":{"barcode-symbology-not-supported":"\\"{{symbology}}\\" wird nicht unterstützt","default-invalid-barcode-text":"FEHLER","errors":{"compile":"Report Kompilierfehler: {{details}}.","csvdataprovider":{"header-parse":"Spaltenkopf kann nicht geparst werden: \\"{{headerValue}}\\""},"data-processing":"Datenverarbeitungsfehler: {{details}}.","dataprovider":{"commandtext-invalid":"Invalid data provider CommandText: {{commandText}}.","connectstring-invalid":"Invalid data source ConnectString: {{connectString}}.","no-data":"Invalid data provider settings. Source or data is not specified."},"fetch-failed":"Daten können nicht geladen werden von \\"{{uri}}\\": {{responseStatus}} {{responseText}}.","generic-error":"{{errorMessage}}","invalid-datasetname":"Ungültiger Datensatzname: \\"{{dataSetName}}\\"","invalid-definition":"Reportdefinition ist ungültig: {{details}}.","json-parse":"Failed to parse JSON: {{errorMessage}}. Source string: \\"{{jsonString}}\\".","jsondataprovider":{"no-global-data":"No data available for DataSet \\"{{datasetName}}\\" - {{globalPath}} = {{globalValue}}"},"layout":"Fehler im Report-Layout: {{details}}.","no-datasets":"Keine Datensätze definiert.","print":{"cancel-print-message":"Druckvorgang wurde vom Benutzer abgebrochen","first-page-render-failed":"Fehler beim rendern der ersten Seite","open-window-failed":"Neues Fenster kann nicht geöffnet werden"},"report-invalid":"Report \\"{{reportUri}}\\" ist ungültig: {{error}}","report-not-available":"Report \\"{{reportUri}}\\" ist nicht verfügbar: {{error}}","reportPartsLibraries-invalid":"Report Parts library \\"{{reportUri}}\\" is invalid: {{error}}","reportPartsLibraries-not-available":"Report Parts library \\"{{reportUri}}\\" is not available: {{error}}","reportPartsLibraries-unknown":"Unknown Report Parts library \\"{{libraryName}}\\"","subreport-invalid":"Unterreport \\"{{reportUri}}\\" ist ungültig: {{error}}","subreport-not-available":"Unterreport \\"{{reportUri}}\\" ist nicht verfügbar: {{error}}","unknown-dataprovider":"Unbekannter Datenprovider \\"{{providerName}}\\"","unknown-datasource":"Unbekannte Datenquelle \\"{{dataSourceName}}\\""},"report-item-not-supported":"\\"{{itemType}}\\" Reportkomponente wird nicht unterstützt"}},{"lng":"de","ns":"export","resources":{"boolTextFalse":"Nein","boolTextTrue":"Ja","cancel-btn":"Abbrechen","emptyTextPlaceholder":"<leer>","file-format-label":"Format","inprogress-lbl":"Export läuft","nodocument":"Kein Dokument ausgewählt","start-btn":"Exportieren","titleDecrease":"Decrease by","titleIncrease":"Increase by","titleToggle":"Expand..."}},{"lng":"de","ns":"params","resources":{"bool-false-label":"Nein","bool-true-label":"Ja","datePlaceholder":"Datum auswählen..","daysViewHeaderFormat":"MMMM YYYY","ddpTextNull":"(leer)","ddpTextSelectAllValues":"(alles auswählen)","ddpTextSelectValue":"(Wert auswählen)","null":"Leer","paramsTextInvalidValue":"Ungültiger Wert","preview-btn-title":"Vorschau"}},{"lng":"de","ns":"paramsValidation","resources":{"integerTooBig":"* Wert darf 53-Bit-Ganzzahl nicht überschreiten","invalidBoolean":"* Falscher Wert. Stellen Sie sicher, dass Sie einen gültigen booleschen Wert eingeben","invalidDateTime":"* Falscher Wert. Stellen Sie sicher, dass Sie ein gültiges Datum eingeben","invalidFloat":"* Falscher Wert. Stellen Sie sicher, dass Sie eine gültige Gleitkommazahl eingeben","invalidInteger":"* Falscher Wert. Stellen Sie sicher, dass Sie eine gültige ganze Zahl eingeben","outstandingDependencies":"* Parameter hat offene Abhängigkeiten","requiredSingleValue":"* Einzelner Wert erwartet","requiredValue":"* Wert erwartet","stringEmpty":"* Falscher Wert. Leere Zeichenfolgen sind nicht zulässig","unknown":"! Unbekannter Validierungsfehler","valueIsNull":"* Wert darf nicht leer sein","valueOutOfRange":"* Falscher Wert. Wählen Sie den richtigen aus der Liste"}},{"lng":"de","ns":"paramsView","resources":{"boolTextFalse":"Nein","boolTextTrue":"Ja","boolTextUnspecified":"Nicht spezifiziert","btnTextClear":"Löschen","btnTextPreview":"Vorschau","btnTextReset":"Zurücksetzen","dateTimeRange":{"rangeTypes":{"Current_Day":"Current Day","Current_Hour":"Current Hour","Current_Minute":"Current Minute","Current_Month":"Current Month","Current_Quarter":"Current Quarter","Current_Week":"Current Week","Current_Year":"Current Year","LastSimple_Day":"Last Day","LastSimple_Hour":"Last Hour","LastSimple_Minute":"Last Minute","LastSimple_Month":"Last Month","LastSimple_Quarter":"Last Quarter","LastSimple_Second":"Last Second","LastSimple_Week":"Last Week","LastSimple_Year":"Last Year","LastToDateSimple_Day":"Last Day To Date","LastToDateSimple_Month":"Last Month To Date","LastToDateSimple_Quarter":"Last Quarter To Date","LastToDateSimple_Week":"Last Week To Date","LastToDateSimple_Year":"Last Year To Date","LastToDate_Day":"Last {{count}} Days To Date","LastToDate_Day_plural":"Last {{count}} Days To Date","LastToDate_Month":"Last {{count}} Months To Date","LastToDate_Month_plural":"Last {{count}} Months To Date","LastToDate_Quarter":"Last {{count}} Quarters To Date","LastToDate_Quarter_plural":"Last {{count}} Quarters To Date","LastToDate_Week":"Last {{count}} Weeks To Date","LastToDate_Week_plural":"Last {{count}} Weeks To Date","LastToDate_Year":"Last {{count}} Years To Date","LastToDate_Year_plural":"Last {{count}} Years To Date","Last_Day":"Last {{count}} Days","Last_Day_plural":"Last {{count}} Days","Last_Hour":"Last {{count}} Hours","Last_Hour_plural":"Last {{count}} Hours","Last_Minute":"Last {{count}} Minutes","Last_Minute_plural":"Last {{count}} Minutes","Last_Month":"Last {{count}} Months","Last_Month_plural":"Last {{count}} Months","Last_Quarter":"Last {{count}} Quarters","Last_Quarter_plural":"Last {{count}} Quarters","Last_Second":"Last {{count}} Seconds","Last_Second_plural":"Last {{count}} Seconds","Last_Week":"Last {{count}} Weeks","Last_Week_plural":"Last {{count}} Weeks","Last_Year":"Last {{count}} Years","Last_Year_plural":"Last {{count}} Years","NextSimple_Day":"Next Day","NextSimple_Hour":"Next Hour","NextSimple_Minute":"Next Minute","NextSimple_Month":"Next Month","NextSimple_Quarter":"Next Quarter","NextSimple_Second":"Next Second","NextSimple_Week":"Next Week","NextSimple_Year":"Next Year","Next_Day":"Next {{count}} Days","Next_Day_plural":"Next {{count}} Days","Next_Hour":"Next {{count}} Hours","Next_Hour_plural":"Next {{count}} Hours","Next_Minute":"Next {{count}} Minutes","Next_Minute_plural":"Next {{count}} Minutes","Next_Month":"Next {{count}} Months","Next_Month_plural":"Next {{count}} Months","Next_Quarter":"Next {{count}} Quarters","Next_Quarter_plural":"Next {{count}} Quarters","Next_Second":"Next {{count}} Seconds","Next_Second_plural":"Next {{count}} Seconds","Next_Week":"Next {{count}} Weeks","Next_Week_plural":"Next {{count}} Weeks","Next_Year":"Next {{count}} Years","Next_Year_plural":"Next {{count}} Years","ToDate_Day":"Day-To-Date","ToDate_Month":"Month-To-Date","ToDate_Quarter":"Quarter-To-Date","ToDate_Week":"Week-To-Date","ToDate_Year":"Year-To-Date"}},"ddTitleToggle":"Expand","ddeTextAllValues":"(all)","ddeTextNull":"(leer)","ddeTextSelectAllValues":"(alles auswählen)","ddeTextSelectValue":"(Wert auswählen)","drDaysViewHeaderFormat":"MMMM YYYY","drMonthYearOrder":"M-Y","drPlaceholderDateEnd":"Ende","drPlaceholderDateStart":"Start","drShortcutsLabelLastMonth":"Letzter Monat","drShortcutsLabelLastMonthToDate":"Last month to date","drShortcutsLabelLastWeek":"Letzte Woche","drShortcutsLabelLastWeekToDate":"Last week to date","drShortcutsLabelLastYear":"Letztes Jahr","drShortcutsLabelLastYearToDate":"Last year to date","drShortcutsLabelMonthToDate":"Monat bis datum","drShortcutsLabelWeekToDate":"Woche bis datum","drShortcutsLabelYearToDate":"Jahr bis datum","drTabLabelAnnually":"Jährlich","drTabLabelDaily":"Täglich","drTabLabelMonthly":"Monatlich","drTabLabelTime":"Time","drTextBack":"Zurück zum Kalender","drTextShortcutsList":"Häufig vorkommende Bereiche","drTitleToggle":"Expand Calendar","dtDaysViewHeaderFormat":"MMMM YYYY","dtMonthYearOrder":"M-Y","dtTextBack":"Zurück zum Kalender","dtTextClear":"Löschen","dtTextSelectDate":"Datum auswählen...","dtTextSelectFromDate":"Select \\"From\\" Date...","dtTextSelectToDate":"Select \\"To\\" Date...","dtTextToday":"Heute","dtTitleQuickRangePicker":"Common Ranges","dtTitleToggle":"Expand Calendar","lblTextInvalidValue":"Ungültiger Wert","lblTextNoAvailableValues":"No available values","listTextSelectAllValues":"(alles auswählen)","nwrpTextNull":"Leer"}},{"lng":"de","ns":"singleDateFormat","resources":{"D":"dddd, MMMM D, YYYY","F":"dddd, MMMM D, YYYY h:mm:ss A","G":"M/D/YYYY h:mm:ss A","M":"MMMM D","O":"YYYY-MM-DDTHH:mm:ss.SSSZ","R":"ddd, DD MMM YYYY HH:mm:ss [GMT]","T":"h:mm:ss A","U":"dddd, MMMM D, YYYY h:mm:ss A","Y":"MMMM YYYY","d":"MM/D/YYYY","f":"dddd, MMMM D, YYYY h:mm A","g":"M/D/YYYY h:mm A","m":"MMMM D","o":"YYYY-MM-DDTHH:mm:ss.SSSZ","r":"ddd, DD MMM YYYY HH:mm:ss [GMT]","s":"YYYY-MM-DDTHH:mm:ss","t":"h:mm A","u":"YYYY-MM-DD HH:mm:ss[Z]","y":"MMMM YYYY"}},{"lng":"de","ns":"viewer","resources":{"cancel-btn":"Abbrechen","document-view":{"aria-label":"Dokument-Ansicht"},"error":{"btnShowDetails":"Show Details","dismiss":"Ablehnen","dismiss-all":"Alles ablehnen","notificationsDetails":"Notifications details","showAll":"Show All","textError":"Error","textError_plural":"Errors","textNotification":"Notification","textNotification_plural":"Notifications","textWarning":"Warning","textWarning_plural":"Warnings","titleCancelTask":"Cancel this task","titleCollapse":"Collapse","titleExpand":"Expand"},"errors":{"noHostElement":"Das Host-Element kann nicht gefunden werden."},"menu":{"aria-label":"Menü","pin-button-title":"Anheften","toogleText":"Expand menu"},"progress":{"page":"Seite"},"search":{"cancel-btn":"Abbrechen","clear-btn":"Löschen","didn-find-msg":"Nichts gefunden.","match-case":"Groß-/Kleinschreibung beachten","more-results-btn":"Weitere Ergebnisse","paneltitle":"Suchen","search-cancelled-msg":"Suche abgebrochen auf Seite {{page}}","search-results":"Suchergebnisse","start-search-btn":"Suchen","whole-word":"Nur ganze Wörter suchen"},"sidebar":{"aria-label":"Seitenleiste","collapse-btn":"Einklappen","expand-btn":"Ausklappen"},"toolbar":{"aria-label":"Symbolleiste","cancel":"Abbrechen","expand":"Expand toolbar","fullscreen":"Vollbildmodus umschalten","gotofirst":"Erste Seite","gotolast":"Letzte Seite","gotonext":"Nächste Seite","gotoprevious":"Vorige Seite","hist-back":"Verlauf: Zurück","hist-fwd":"Verlauf: Weiter","hist-parent":"Verlauf: Zurück zum Anfang","movetool":"Verschiebungswerkzeug","refresh":"Aktualisieren","zoom-fitpage":"An Seite anpassen","zoom-fitwidth":"An Breite anpassen","zoom-menu-header":"Zoommodus","zoom-zoomin":"Heranzoomen","zoom-zoomout":"Herauszoomen"},"top-bottom-panel":{"aria-label":"Zusätzliches Bedienfeld"}}},{"lng":"de","ns":"arjsv","resources":{"about!label":"Über","errors":{"cant-refresh-needparams":"Aktualisierung fehlgeschlagen (nicht alle Parameter angegeben)","cant-render-needparams":"Kann nicht gerendert werden (nicht alle Parameter angegeben)","invalid-hidden-param":"Einige versteckte Paramter haben ungültige Werte.","no-current-doc":"Kein aktuelles Dokument!","open-bad-fileformat":"Fehler: nur JSON-Reportdefinitionen werden unterstützt.","unknown-export-format":"Ungültiges Format oder Modul für {{format}} konnte nicht geladen werden."},"export":{"progress-message":"Seite {{msg}} von {{total}}...","progress-message-starting":"Starten..."},"panels":{"report-sections":"Show/Hide Report Sections"},"reportlist":{"openfile":"Datei öffnen"},"sidebar":{"export-title":"Exportieren","openreport-title":"Report öffnen","parameters-title":"Parameter","toc-title":"Inhaltsverzeichnis"},"toolbar":{"continuous":"Fortlaufend","galleymode":"Galeriemodus","preparingprint":"Druck vorbereiten...","print":"Drucken...","singlepage":"Einzelseitenansicht"}}},{"lng":"de","ns":"exporthtml","resources":{"friendlyName":"HTML Datei","messages":{"cancel-message":"Der Exportvorgang wurde vom Benutzer abgebrochen","embedimages-is-zip-only":"Eingebettete Bilder als externe Dateien sind nur für das Ausgabeformat \\"zip\\" verfügbar","first-page-render-failed":"Fehler beim rendern der ersten Seite","multipage-is-zip-only":"Mehrseitiger Export ist nur für das Ausgabeformat \\"zip\\" verfügbar"},"settings":{"autoPrint":{"category":"","label":"Drucken beim Öffnen"},"embedImages":{"category":"Ausgabe","enum":{"embed":"Eingebettet","external":"Extern","none":"Keine"},"label":"Eingebettete Bilder"},"filename":{"category":"","label":"Dateiname"},"multiPage":{"category":"Ausgabe","label":"Mehrere Seiten"},"outputType":{"category":"Ausgabe","enum":{"auto":"Automatisch","html":"HTML","zip":"ZIP"},"label":"Ausgabeformat"},"title":{"category":"","label":"Titel"}}}},{"lng":"de","ns":"exportpdf","resources":{"friendlyName":"PDF Dokument","messages":{"cancel-message":"Der Exportvorgang wurde vom Benutzer abgebrochen","first-page-render-failed":"Fehler beim rendern der ersten Seite","pdfa-fonts-embed-failure":"PDF/A requires all fonts to be registered, but \\"{{font}}\\" was not found"},"settings":{"annotating":{"category":"Sicherheit","label":"Kommentare zulassen"},"author":{"category":"Dokument Information","label":"Autor"},"autoPrint":{"category":"","label":"Beim Öffnen drucken"},"contentAccessibility":{"category":"Sicherheit","label":"Zugriff auf Inhalt zulassen"},"copying":{"category":"Sicherheit","label":"Kopieren zulassen"},"documentAssembly":{"category":"Sicherheit","label":"Zusammenfügen zulassen"},"filename":{"category":"","label":"Dateiname"},"keywords":{"category":"Dokument Information","label":"Suchbegriffe"},"modifying":{"category":"Sicherheit","label":"Änderungen zulassen"},"ownerPassword":{"category":"Sicherheit","label":"Ersteller Passwort"},"pdfVersion":{"category":"","label":"PDF Version"},"printing":{"category":"Sicherheit","enum":{"highResolution":"Hohe Auflösung","lowResolution":"Niedrige Auflösung","none":"Nicht zulassen"},"label":"Drucken zulassen"},"subject":{"category":"Dokument Information","label":"Thema"},"title":{"category":"Dokument Information","label":"Titel"},"userPassword":{"category":"Sicherheit","label":"Benutzer Passwort"}}}},{"lng":"de","ns":"exporttabular-data","resources":{"friendlyName":"Tabular data","settings":{"colSeparator":{"category":"csv","label":"Columns Separator"},"filename":{"category":"","label":"File Name"},"outputType":{"category":"output","enum":{"plain":"Plain","zip":"ZIP"},"label":"Output Type"},"quotationSymbol":{"category":"csv","label":"Quotation Symbol"},"rowSeparator":{"category":"csv","label":"Rows Separator"},"tableSeparator":{"category":"csv","label":"Tables Separator"}}}},{"lng":"de","ns":"exportxlsx","resources":{"friendlyName":"Excel Arbeitsmappe (xlsx)","messages":{"cancel-message":"Der Exportvorgang wurde vom Benutzer abgebrochen","first-page-render-failed":"Fehler beim rendern der ersten Seite"},"settings":{"creator":{"category":"Dokument Information","label":"Autor"},"filename":{"category":"","label":"Dateiname"},"orientation":{"category":"Seite","enum":{"landscape":"Querformat","portrait":"Hochformat"},"label":"Ausrichtung"},"password":{"category":"","label":"Passwort"},"sheetName":{"category":"","label":"Blattname"},"size":{"category":"Seite","enum":{"A4":"A4","A5":"A5","B5":"B5 (JIS)","Double_Japan_Postcard_Rotated":"Double Japan Postcard Rotated","Envelope_10":"Umschlag #10","Envelope_B5":"Umschlag B5","Envelope_C5":"Umschlag C5","Envelope_DL":"Umschlag DL","Envelope_Monarch":"Umschlag Monarch","Executive":"Executive","K16_197x273_mm":"16K 197x273 mm","Legal":"Legal","Letter":"Letter"},"label":"Größe"}}}}]')},function(e){e.exports=JSON.parse('[{"lng":"pt-BR","ns":"core","resources":{"barcode-symbology-not-supported":"\\"{{symbology}}\\" não suportado","default-invalid-barcode-text":"ERRO","errors":{"compile":"Erro de compilação de relatório: {{details}}.","csvdataprovider":{"header-parse":"Impossível converter a seguinte coluna do cabeçalho: \\"{{headerValue}}\\""},"data-processing":"Erro no processamento de dados: {{details}}.","dataprovider":{"commandtext-invalid":"Invalid data provider CommandText: {{commandText}}.","connectstring-invalid":"Invalid data source ConnectString: {{connectString}}.","no-data":"Invalid data provider settings. Source or data is not specified."},"fetch-failed":"Impossível carregar os dados do \\"{{uri}}\\": {{responseStatus}} {{responseText}}.","generic-error":"{{errorMessage}}","invalid-datasetname":"DataSetName inválido: \\"{{dataSetName}}\\"","invalid-definition":"As definições do relatório não são válidas: {{details}}.","json-parse":"Failed to parse JSON: {{errorMessage}}. Source string: \\"{{jsonString}}\\".","jsondataprovider":{"no-global-data":"Não existe dados disponíveis para o DataSet \\"{{datasetName}}\\" - {{globalPath}} = {{globalValue}}"},"layout":"Erro de layout do relatório: {{details}}.","no-datasets":"Sem definição para os DataSets.","print":{"cancel-print-message":"O processo de impressão foi cancelado pelo usuário","first-page-render-failed":"A renderização da primeira página falhou","open-window-failed":"Não pode abrir uma nova janela"},"report-invalid":"O relatório \\"{{reportUri}}\\" está inválido: {{error}}","report-not-available":"O relatório \\"{{reportUri}}\\" não está disponível: {{error}}","reportPartsLibraries-invalid":"Report Parts library \\"{{reportUri}}\\" is invalid: {{error}}","reportPartsLibraries-not-available":"Report Parts library \\"{{reportUri}}\\" is not available: {{error}}","reportPartsLibraries-unknown":"Unknown Report Parts library \\"{{libraryName}}\\"","subreport-invalid":"O sub-relatório \\"{{reportUri}}\\" está inválido: {{error}}","subreport-not-available":"O sub-relatório \\"{{reportUri}}\\" não está disponível: {{error}}","unknown-dataprovider":"Dados desconhecidos do provider \\"{{providerName}}\\"","unknown-datasource":"Datasource desconhecido \\"{{dataSourceName}}\\""},"report-item-not-supported":"\\"{{itemType}}\\" item não suportado no relatório"}},{"lng":"pt-BR","ns":"export","resources":{"boolTextFalse":"Falso","boolTextTrue":"Verdadeiro","cancel-btn":"Cancelar","emptyTextPlaceholder":"<vazio>","file-format-label":"Formato","inprogress-lbl":"Exportação in progress","nodocument":"Nenhum documento selecionado","start-btn":"Exportar","titleDecrease":"Diminuir em","titleIncrease":"Aumentar em","titleToggle":"Mostrar..."}},{"lng":"pt-BR","ns":"params","resources":{"bool-false-label":"Falso","bool-true-label":"Verdadeiro","datePlaceholder":"Selecionde a data..","daysViewHeaderFormat":"MMMM AAAA","ddpTextNull":"(null)","ddpTextSelectAllValues":"(todos)","ddpTextSelectValue":"(selecione a opção)","null":"Null","paramsTextInvalidValue":"Opção inválida","preview-btn-title":"Visualizar"}},{"lng":"pt-BR","ns":"paramsValidation","resources":{"integerTooBig":"* O valor não pode exceder 53-bit integer","invalidBoolean":"* Valor errado. Certifique-se de inserir um boolean válido","invalidDateTime":"* Valor errado. Certifique-se de inserir um date válido","invalidFloat":"* Valor errado. Certifique-se de inserir um float válido","invalidInteger":"* Valor errado. Certifique-se de inserir um integer válido","outstandingDependencies":"* O parâmetro tem dependências pendentes","requiredSingleValue":"* Apenas um único valor é obrigatório","requiredValue":"* Valor obrigatório","stringEmpty":"* Valor errado. Strings vazías não são permitidas","unknown":"! Erro de validação desconhecido","valueIsNull":"* A valor não pode ser null","valueOutOfRange":"* Valor errado. Escolha uma opção válida na lista"}},{"lng":"pt-BR","ns":"paramsView","resources":{"boolTextFalse":"Falso","boolTextTrue":"Verdadeiro","boolTextUnspecified":" Não especificado","btnTextClear":"Limpar","btnTextPreview":"Visualizar","btnTextReset":"Redefinir","dateTimeRange":{"rangeTypes":{"Current_Day":"Current Day","Current_Hour":"Current Hour","Current_Minute":"Current Minute","Current_Month":"Current Month","Current_Quarter":"Current Quarter","Current_Week":"Current Week","Current_Year":"Current Year","LastSimple_Day":"Last Day","LastSimple_Hour":"Last Hour","LastSimple_Minute":"Last Minute","LastSimple_Month":"Last Month","LastSimple_Quarter":"Last Quarter","LastSimple_Second":"Last Second","LastSimple_Week":"Last Week","LastSimple_Year":"Last Year","LastToDateSimple_Day":"Last Day To Date","LastToDateSimple_Month":"Last Month To Date","LastToDateSimple_Quarter":"Last Quarter To Date","LastToDateSimple_Week":"Last Week To Date","LastToDateSimple_Year":"Last Year To Date","LastToDate_Day":"Last {{count}} Days To Date","LastToDate_Day_plural":"Last {{count}} Days To Date","LastToDate_Month":"Last {{count}} Months To Date","LastToDate_Month_plural":"Last {{count}} Months To Date","LastToDate_Quarter":"Last {{count}} Quarters To Date","LastToDate_Quarter_plural":"Last {{count}} Quarters To Date","LastToDate_Week":"Last {{count}} Weeks To Date","LastToDate_Week_plural":"Last {{count}} Weeks To Date","LastToDate_Year":"Last {{count}} Years To Date","LastToDate_Year_plural":"Last {{count}} Years To Date","Last_Day":"Last {{count}} Days","Last_Day_plural":"Last {{count}} Days","Last_Hour":"Last {{count}} Hours","Last_Hour_plural":"Last {{count}} Hours","Last_Minute":"Last {{count}} Minutes","Last_Minute_plural":"Last {{count}} Minutes","Last_Month":"Last {{count}} Months","Last_Month_plural":"Last {{count}} Months","Last_Quarter":"Last {{count}} Quarters","Last_Quarter_plural":"Last {{count}} Quarters","Last_Second":"Last {{count}} Seconds","Last_Second_plural":"Last {{count}} Seconds","Last_Week":"Last {{count}} Weeks","Last_Week_plural":"Last {{count}} Weeks","Last_Year":"Last {{count}} Years","Last_Year_plural":"Last {{count}} Years","NextSimple_Day":"Next Day","NextSimple_Hour":"Next Hour","NextSimple_Minute":"Next Minute","NextSimple_Month":"Next Month","NextSimple_Quarter":"Next Quarter","NextSimple_Second":"Next Second","NextSimple_Week":"Next Week","NextSimple_Year":"Next Year","Next_Day":"Next {{count}} Days","Next_Day_plural":"Next {{count}} Days","Next_Hour":"Next {{count}} Hours","Next_Hour_plural":"Next {{count}} Hours","Next_Minute":"Next {{count}} Minutes","Next_Minute_plural":"Next {{count}} Minutes","Next_Month":"Next {{count}} Months","Next_Month_plural":"Next {{count}} Months","Next_Quarter":"Next {{count}} Quarters","Next_Quarter_plural":"Next {{count}} Quarters","Next_Second":"Next {{count}} Seconds","Next_Second_plural":"Next {{count}} Seconds","Next_Week":"Next {{count}} Weeks","Next_Week_plural":"Next {{count}} Weeks","Next_Year":"Next {{count}} Years","Next_Year_plural":"Next {{count}} Years","ToDate_Day":"Day-To-Date","ToDate_Month":"Month-To-Date","ToDate_Quarter":"Quarter-To-Date","ToDate_Week":"Week-To-Date","ToDate_Year":"Year-To-Date"}},"ddTitleToggle":"Mostrar","ddeTextAllValues":"(all)","ddeTextNull":"(null)","ddeTextSelectAllValues":"(todos)","ddeTextSelectValue":"(selecione a opção)","drDaysViewHeaderFormat":"MMMM AAAA","drMonthYearOrder":"M-A","drPlaceholderDateEnd":"Fim","drPlaceholderDateStart":"Início","drShortcutsLabelLastMonth":"Último mês","drShortcutsLabelLastMonthToDate":"Last month to date","drShortcutsLabelLastWeek":"Última semana","drShortcutsLabelLastWeekToDate":"Last week to date","drShortcutsLabelLastYear":"Último ano","drShortcutsLabelLastYearToDate":"Last year to date","drShortcutsLabelMonthToDate":"Mês até hoje","drShortcutsLabelWeekToDate":"Semana até hoje","drShortcutsLabelYearToDate":"Ano até hoje","drTabLabelAnnually":"Anualmente","drTabLabelDaily":"Diariamente","drTabLabelMonthly":"Mensalmente","drTabLabelTime":"Time","drTextBack":"Voltar ao calendário","drTextShortcutsList":"Intervalos comuns","drTitleToggle":"Mostrar Calendário","dtDaysViewHeaderFormat":"MMMM AAAA","dtMonthYearOrder":"M-A","dtTextBack":"Voltar ao calendário","dtTextClear":"Limpar","dtTextSelectDate":"Selecione a Data...","dtTextSelectFromDate":"Select \\"From\\" Date...","dtTextSelectToDate":"Select \\"To\\" Date...","dtTextToday":"Hoje","dtTitleQuickRangePicker":"Common Ranges","dtTitleToggle":"Mostrar Calendário","lblTextInvalidValue":"Valor inválido","lblTextNoAvailableValues":"Sem valores disponíveis","listTextSelectAllValues":"(todos)","nwrpTextNull":"Null"}},{"lng":"pt-BR","ns":"singleDateFormat","resources":{"D":"dddd, MMMM D, YYYY","F":"dddd, MMMM D, YYYY h:mm:ss A","G":"M/D/YYYY h:mm:ss A","M":"MMMM D","O":"YYYY-MM-DDTHH:mm:ss.SSSZ","R":"ddd, DD MMM YYYY HH:mm:ss [GMT]","T":"h:mm:ss A","U":"dddd, MMMM D, YYYY h:mm:ss A","Y":"MMMM YYYY","d":"MM/D/YYYY","f":"dddd, MMMM D, YYYY h:mm A","g":"M/D/YYYY h:mm A","m":"MMMM D","o":"YYYY-MM-DDTHH:mm:ss.SSSZ","r":"ddd, DD MMM YYYY HH:mm:ss [GMT]","s":"YYYY-MM-DDTHH:mm:ss","t":"h:mm A","u":"YYYY-MM-DD HH:mm:ss[Z]","y":"MMMM YYYY"}},{"lng":"pt-BR","ns":"viewer","resources":{"cancel-btn":"Cancelar","document-view":{"aria-label":"Visualização do documento"},"error":{"btnShowDetails":"Show Details","dismiss":"Recusar","dismiss-all":"Recusar tudo","notificationsDetails":"Notifications details","showAll":"Show All","textError":"Error","textError_plural":"Errors","textNotification":"Notification","textNotification_plural":"Notifications","textWarning":"Warning","textWarning_plural":"Warnings","titleCancelTask":"Cancel this task","titleCollapse":"Collapse","titleExpand":"Expand"},"errors":{"noHostElement":"Não é possível encontrar o elemento host."},"menu":{"aria-label":"Menu","pin-button-title":"Pin","toogleText":"Mostrar menu"},"progress":{"page":"Página"},"search":{"cancel-btn":"Cancelar","clear-btn":"Limpar","didn-find-msg":"Não encontrou nada.","match-case":"Caso de compatibilidade","more-results-btn":"Mais resultados","paneltitle":"Procurar","search-cancelled-msg":"Pesquisa cancelada na página {{page}}","search-results":"Procurar Resultados","start-search-btn":"Procurar","whole-word":"Palavra inteira"},"sidebar":{"aria-label":"Barra Lateral","collapse-btn":"Esconder","expand-btn":"Mostrar"},"toolbar":{"aria-label":"Toolbar","cancel":"Cancelar","expand":"Mostrar toolbar","fullscreen":"Alternar o modo tela cheia","gotofirst":"Primeira","gotolast":"Última","gotonext":"Próxima","gotoprevious":"Anterior","hist-back":"História: Voltar","hist-fwd":"História: Siga em frente","hist-parent":"Histórico: Voltar ao pai","movetool":"Mover","refresh":"Atualizar","zoom-fitpage":"Ajustar página","zoom-fitwidth":"Ajustar à largura","zoom-menu-header":"Zoom","zoom-zoomin":"Mais Zoom","zoom-zoomout":"Menos Zoom"},"top-bottom-panel":{"aria-label":"Painel de controle adicional"}}},{"lng":"pt-BR","ns":"arjsv","resources":{"about!label":"Sobre","errors":{"cant-refresh-needparams":"Falha na atualização (nem todos os parâmetros especificados)","cant-render-needparams":"Não é possível renderizar (nem todos os parâmetros especificados)","invalid-hidden-param":"Alguns dos parâmetros ocultos têm valor inválido.","no-current-doc":"Nenhum documento atual!","open-bad-fileformat":"Erro: apenas as definições de relatório JSON são compatíveis.","unknown-export-format":"O formato da chave ou do módulo está inválido para {{format}} por não estar carregado"},"export":{"progress-message":"Página {{msg}} de {{total}}...","progress-message-starting":"Iniciando..."},"panels":{"report-sections":"Show/Hide Report Sections"},"reportlist":{"openfile":"Abrir Arquivo"},"sidebar":{"export-title":"Exportação","openreport-title":"Abrir Relatório","parameters-title":"Parâmetros","toc-title":"Índices"},"toolbar":{"continuous":"Contínuo","galleymode":"Modo Galeria","preparingprint":"Preparando impressão...","print":"Imprimir...","singlepage":"Visualização de página única"}}},{"lng":"pt-BR","ns":"exporthtml","resources":{"friendlyName":"Arquivo HTML","messages":{"cancel-message":"O processo de exportação foi cancelado pelo usuário","embedimages-is-zip-only":"A incorporação de imagens como arquivos externos está disponível apenas para o tipo de saída \\"zip\\"","first-page-render-failed":"Falha na renderização da primeira página","multipage-is-zip-only":"A exportação de várias páginas está disponível apenas para o tipo de saída \\"zip\\""},"settings":{"autoPrint":{"category":"","label":"Imprimir ao abrir"},"embedImages":{"category":"saída","enum":{"embed":"Incorporar","external":"Externo","none":"Nenhum(a)"},"label":"Incorporar Imagens"},"filename":{"category":"","label":"Nome do arquivo"},"multiPage":{"category":"saída","label":"Paginas multiplas"},"outputType":{"category":"saída","enum":{"auto":"Auto","html":"HTML","zip":"ZIP"},"label":"Tipo de Saída"},"title":{"category":"","label":"Título"}}}},{"lng":"pt-BR","ns":"exportpdf","resources":{"friendlyName":"Dcumento PDF","messages":{"cancel-message":"O processo de exportação foi cancelado pelo usuário","first-page-render-failed":"Falha na renderização da primeira página","pdfa-fonts-embed-failure":"PDF/A requer que todas as fontes estejam registradas, porém a fonte \\"{{font}}\\" não foi encontrada"},"settings":{"annotating":{"category":"segurança","label":"Permitir anotação"},"author":{"category":"info","label":"Autor"},"autoPrint":{"category":"","label":"Imprimir ao abrir"},"contentAccessibility":{"category":"segurança","label":"Permitir acessibilidade de conteúdo"},"copying":{"category":"segurança","label":"Permitir cópia"},"documentAssembly":{"category":"segurança","label":"Permitir montagem de documentos"},"filename":{"category":"","label":"Nome do arquivo"},"keywords":{"category":"info","label":"Palavras-chave"},"modifying":{"category":"segurança","label":"Permitir modificação"},"ownerPassword":{"category":"segurança","label":"Senha do proprietário"},"pdfVersion":{"category":"","label":"Versão do PDF"},"printing":{"category":"segurança","enum":{"highResolution":"Alta resolução","lowResolution":"Baixa resolução","none":"Nenhum(a)"},"label":"Permitir impressão"},"subject":{"category":"info","label":"Sujeito"},"title":{"category":"info","label":"Título"},"userPassword":{"category":"segurança","label":"Senha do usuário"}}}},{"lng":"pt-BR","ns":"exporttabular-data","resources":{"friendlyName":"Dados tabulares","settings":{"colSeparator":{"category":"csv","label":"Separador de Colunas"},"filename":{"category":"","label":"Nome do arquivo"},"outputType":{"category":"saída","enum":{"plain":"Plain","zip":"ZIP"},"label":"Tipo de saída"},"quotationSymbol":{"category":"csv","label":"Símbolo de cotação"},"rowSeparator":{"category":"csv","label":"Separador de Linhas"},"tableSeparator":{"category":"csv","label":"Separador de Tabelas"}}}},{"lng":"pt-BR","ns":"exportxlsx","resources":{"friendlyName":"Pasta de trabalho do Excel (xlsx)","messages":{"cancel-message":"O processo de exportação foi cancelado pelo usuário","first-page-render-failed":"Falha na renderização da primeira página"},"settings":{"creator":{"category":"info","label":"Autor"},"filename":{"category":"","label":"Nome do arquivo"},"orientation":{"category":"página","enum":{"landscape":"Panorama","portrait":"Retrato"},"label":"Orientação"},"password":{"category":"","label":"Senha"},"sheetName":{"category":"","label":"Nome da planilha"},"size":{"category":"página","enum":{"A4":"A4","A5":"A5","B5":"B5 (JIS)","Double_Japan_Postcard_Rotated":"Double Japan Postcard Rotated","Envelope_10":"Envelope #10","Envelope_B5":"Envelope B5","Envelope_C5":"Envelope C5","Envelope_DL":"Envelope DL","Envelope_Monarch":"Envelope Monarch","Executive":"Executive","K16_197x273_mm":"16K 197x273 m","Legal":"Legal","Letter":"Letter"},"label":"Tamanho"}}}}]')},function(e){e.exports=JSON.parse('[{"lng":"it","ns":"core","resources":{"barcode-symbology-not-supported":"\\"{{symbology}}\\" non è supportato","default-invalid-barcode-text":"ERRORE","errors":{"compile":"Errore di compilazione del report: {{details}}.","csvdataprovider":{"header-parse":"Impossibile analizzare l\'header della colonna: \\"{{headerValue}}\\""},"data-processing":"Errore di elaborazione dati: {{details}}.","dataprovider":{"commandtext-invalid":"Invalid data provider CommandText: {{commandText}}.","connectstring-invalid":"Invalid data source ConnectString: {{connectString}}.","no-data":"Invalid data provider settings. Source or data is not specified."},"fetch-failed":"Impossibile scaricare i dati da \\"{{uri}}\\": {{responseStatus}} {{responseText}}.","generic-error":"{{errorMessage}}","invalid-datasetname":"Nome DataSet non valido: \\"{{dataSetName}}\\"","invalid-definition":"La definizione del report non è valida: {{details}}.","json-parse":"Failed to parse JSON: {{errorMessage}}. Source string: \\"{{jsonString}}\\".","jsondataprovider":{"no-global-data":"Nessun dato disponibile per il DataSet \\"{{datasetName}}\\" - {{globalPath}} = {{globalValue}}"},"layout":"Errore di layout del report: {{details}}.","no-datasets":"Nessun DataSet è stato definito.","print":{"cancel-print-message":"Il processo di stampa è stato cancellato dall\'utente","first-page-render-failed":"Il primo rendering della pagina è fallito","open-window-failed":"Non si può aprire una nuova finestra"},"report-invalid":"Il report \\"{{reportUri}}\\" non è valido: {{error}}","report-not-available":"Il report \\"{{reportUri}}\\" non è disponibile: {{error}}","reportPartsLibraries-invalid":"Report Parts library \\"{{reportUri}}\\" is invalid: {{error}}","reportPartsLibraries-not-available":"Report Parts library \\"{{reportUri}}\\" is not available: {{error}}","reportPartsLibraries-unknown":"Unknown Report Parts library \\"{{libraryName}}\\"","subreport-invalid":"Il subreport \\"{{reportUri}}\\" non è valido: {{error}}","subreport-not-available":"Subreport \\"{{reportUri}}\\" non è disponibile: {{error}}","unknown-dataprovider":"Provider di dati sconosciuto \\"{{providerName}}\\"","unknown-datasource":"DataSource sconosciuto \\"{{dataSourceName}}\\""},"report-item-not-supported":"\\"{{itemType}}\\" il report item non è supportato"}},{"lng":"it","ns":"export","resources":{"boolTextFalse":"Falso","boolTextTrue":"Vero","cancel-btn":"Annulla","emptyTextPlaceholder":"<empty>","file-format-label":"Formato","inprogress-lbl":"Esportazione in corso","nodocument":"Nessun documento selezionato","start-btn":"Esportazione","titleDecrease":"Diminuisci di","titleIncrease":"Incrementa di","titleToggle":"Espandi..."}},{"lng":"it","ns":"params","resources":{"bool-false-label":"Falso","bool-true-label":"Vero","datePlaceholder":"Seleziona data..","daysViewHeaderFormat":"MMMM YYYY","ddpTextNull":"(vuoto)","ddpTextSelectAllValues":"(seleziona tutto)","ddpTextSelectValue":"(seleziona valore)","null":"Vuoto","paramsTextInvalidValue":"Valore non valido","preview-btn-title":"Anteprima"}},{"lng":"it","ns":"paramsValidation","resources":{"integerTooBig":"* Il valore non può superare un valore intero di 53-bit","invalidBoolean":"* Valore errato. Assicurati di inserire un valore booleano valido","invalidDateTime":"* Valore errato. Assicurati di inserire una data valida","invalidFloat":"*Valore errato. Assicurati di inserire un valore decimale valido","invalidInteger":"* Valore errato. Assicurati di inserire un valore intero valido","outstandingDependencies":"* Il parametro ha delle dipendenze esterne","requiredSingleValue":"* E\' richiesto un valore singolo","requiredValue":"* Valore richiesto","stringEmpty":"* Valore errato. Non sono permesse stringhe vuote","unknown":"! Errore di validazione sconosciuto","valueIsNull":"* Il valore non può essere vuoto","valueOutOfRange":"*Valore errato. Seleziona il valore corretto dalla lista"}},{"lng":"it","ns":"paramsView","resources":{"boolTextFalse":"Falso","boolTextTrue":"Vero","boolTextUnspecified":"Non specificato","btnTextClear":"Svuota","btnTextPreview":"Anteprima","btnTextReset":"Reimposta","dateTimeRange":{"rangeTypes":{"Current_Day":"Current Day","Current_Hour":"Current Hour","Current_Minute":"Current Minute","Current_Month":"Current Month","Current_Quarter":"Current Quarter","Current_Week":"Current Week","Current_Year":"Current Year","LastSimple_Day":"Last Day","LastSimple_Hour":"Last Hour","LastSimple_Minute":"Last Minute","LastSimple_Month":"Last Month","LastSimple_Quarter":"Last Quarter","LastSimple_Second":"Last Second","LastSimple_Week":"Last Week","LastSimple_Year":"Last Year","LastToDateSimple_Day":"Last Day To Date","LastToDateSimple_Month":"Last Month To Date","LastToDateSimple_Quarter":"Last Quarter To Date","LastToDateSimple_Week":"Last Week To Date","LastToDateSimple_Year":"Last Year To Date","LastToDate_Day":"Last {{count}} Days To Date","LastToDate_Day_plural":"Last {{count}} Days To Date","LastToDate_Month":"Last {{count}} Months To Date","LastToDate_Month_plural":"Last {{count}} Months To Date","LastToDate_Quarter":"Last {{count}} Quarters To Date","LastToDate_Quarter_plural":"Last {{count}} Quarters To Date","LastToDate_Week":"Last {{count}} Weeks To Date","LastToDate_Week_plural":"Last {{count}} Weeks To Date","LastToDate_Year":"Last {{count}} Years To Date","LastToDate_Year_plural":"Last {{count}} Years To Date","Last_Day":"Last {{count}} Days","Last_Day_plural":"Last {{count}} Days","Last_Hour":"Last {{count}} Hours","Last_Hour_plural":"Last {{count}} Hours","Last_Minute":"Last {{count}} Minutes","Last_Minute_plural":"Last {{count}} Minutes","Last_Month":"Last {{count}} Months","Last_Month_plural":"Last {{count}} Months","Last_Quarter":"Last {{count}} Quarters","Last_Quarter_plural":"Last {{count}} Quarters","Last_Second":"Last {{count}} Seconds","Last_Second_plural":"Last {{count}} Seconds","Last_Week":"Last {{count}} Weeks","Last_Week_plural":"Last {{count}} Weeks","Last_Year":"Last {{count}} Years","Last_Year_plural":"Last {{count}} Years","NextSimple_Day":"Next Day","NextSimple_Hour":"Next Hour","NextSimple_Minute":"Next Minute","NextSimple_Month":"Next Month","NextSimple_Quarter":"Next Quarter","NextSimple_Second":"Next Second","NextSimple_Week":"Next Week","NextSimple_Year":"Next Year","Next_Day":"Next {{count}} Days","Next_Day_plural":"Next {{count}} Days","Next_Hour":"Next {{count}} Hours","Next_Hour_plural":"Next {{count}} Hours","Next_Minute":"Next {{count}} Minutes","Next_Minute_plural":"Next {{count}} Minutes","Next_Month":"Next {{count}} Months","Next_Month_plural":"Next {{count}} Months","Next_Quarter":"Next {{count}} Quarters","Next_Quarter_plural":"Next {{count}} Quarters","Next_Second":"Next {{count}} Seconds","Next_Second_plural":"Next {{count}} Seconds","Next_Week":"Next {{count}} Weeks","Next_Week_plural":"Next {{count}} Weeks","Next_Year":"Next {{count}} Years","Next_Year_plural":"Next {{count}} Years","ToDate_Day":"Day-To-Date","ToDate_Month":"Month-To-Date","ToDate_Quarter":"Quarter-To-Date","ToDate_Week":"Week-To-Date","ToDate_Year":"Year-To-Date"}},"ddTitleToggle":"Espandi","ddeTextAllValues":"(all)","ddeTextNull":"(vuoto)","ddeTextSelectAllValues":"(seleziona tutto)","ddeTextSelectValue":"(seleziona valore)","drDaysViewHeaderFormat":"MMMM YYYY","drMonthYearOrder":"M-Y","drPlaceholderDateEnd":"Fine","drPlaceholderDateStart":"Inizio","drShortcutsLabelLastMonth":"Mese scorso","drShortcutsLabelLastMonthToDate":"Last month to date","drShortcutsLabelLastWeek":"Settimana scorsa","drShortcutsLabelLastWeekToDate":"Last week to date","drShortcutsLabelLastYear":"Anno scorso","drShortcutsLabelLastYearToDate":"Last year to date","drShortcutsLabelMonthToDate":"Da mese a data","drShortcutsLabelWeekToDate":"Da settimana a data","drShortcutsLabelYearToDate":"Da anno a data","drTabLabelAnnually":"Annuale","drTabLabelDaily":"Giornaliero","drTabLabelMonthly":"Mensile","drTabLabelTime":"Time","drTextBack":"Torna al Calendario","drTextShortcutsList":"Intervalli frequenti","drTitleToggle":"Espandi Calendario","dtDaysViewHeaderFormat":"MMMM YYYY","dtMonthYearOrder":"M-Y","dtTextBack":"Torna al Calendario","dtTextClear":"Svuota","dtTextSelectDate":"Seleziona Data...","dtTextSelectFromDate":"Select \\"From\\" Date...","dtTextSelectToDate":"Select \\"To\\" Date...","dtTextToday":"Oggi","dtTitleQuickRangePicker":"Common Ranges","dtTitleToggle":"Espandi Calendario","lblTextInvalidValue":"Valore non valido","lblTextNoAvailableValues":"Nessun valore disponibile","listTextSelectAllValues":"(seleziona tutto)","nwrpTextNull":"Vuoto"}},{"lng":"it","ns":"singleDateFormat","resources":{"D":"dddd, MMMM D, YYYY","F":"dddd, MMMM D, YYYY h:mm:ss A","G":"M/D/YYYY h:mm:ss A","M":"MMMM D","O":"YYYY-MM-DDTHH:mm:ss.SSSZ","R":"ddd, DD MMM YYYY HH:mm:ss [GMT]","T":"h:mm:ss A","U":"dddd, MMMM D, YYYY h:mm:ss A","Y":"MMMM YYYY","d":"MM/D/YYYY","f":"dddd, MMMM D, YYYY h:mm A","g":"M/D/YYYY h:mm A","m":"MMMM D","o":"YYYY-MM-DDTHH:mm:ss.SSSZ","r":"ddd, DD MMM YYYY HH:mm:ss [GMT]","s":"YYYY-MM-DDTHH:mm:ss","t":"h:mm A","u":"YYYY-MM-DD HH:mm:ss[Z]","y":"MMMM YYYY"}},{"lng":"it","ns":"viewer","resources":{"cancel-btn":"Annulla","document-view":{"aria-label":"Visualizzazione documento"},"error":{"btnShowDetails":"Show Details","dismiss":"Svuota","dismiss-all":"Svuota tutto","notificationsDetails":"Notifications details","showAll":"Show All","textError":"Error","textError_plural":"Errors","textNotification":"Notification","textNotification_plural":"Notifications","textWarning":"Warning","textWarning_plural":"Warnings","titleCancelTask":"Cancel this task","titleCollapse":"Collapse","titleExpand":"Expand"},"errors":{"noHostElement":"Impossibile trovare l\'elemento host."},"menu":{"aria-label":"Menu","pin-button-title":"Pin","toogleText":"Espandi menù"},"progress":{"page":"Page"},"search":{"cancel-btn":"Annulla","clear-btn":"Svuota","didn-find-msg":"Non è stato trovato nulla.","match-case":"Corrispondenza","more-results-btn":"Più risultati","paneltitle":"Cerca","search-cancelled-msg":"Ricerca annullata nella pagina {{page}}","search-results":"Risultati ricerca","start-search-btn":"Cerca","whole-word":"Tutta la parola"},"sidebar":{"aria-label":"Sidebar","collapse-btn":"Restringi","expand-btn":"Espandi"},"toolbar":{"aria-label":"Toolbar","cancel":"Annulla","expand":"Espandi toolbar","fullscreen":"Attiva/disattiva Schermo Intero","gotofirst":"Vai alla prima","gotolast":"Vai all\'ultima","gotonext":"Vai alla prossima","gotoprevious":"Vai alla precedente","hist-back":"Cronologia: Vai indietro","hist-fwd":"Cronologia: Vai avanti","hist-parent":"Cronologia: Torna al Padre","movetool":"Strumento di spostamento","refresh":"Ricarica","zoom-fitpage":"Adatta alla pagina","zoom-fitwidth":"Adatta alla larghezza","zoom-menu-header":"Modalità zoom","zoom-zoomin":"Aumenta zoom","zoom-zoomout":"Diminuisci zoom"},"top-bottom-panel":{"aria-label":"Pannello di controllo addizionale"}}},{"lng":"it","ns":"arjsv","resources":{"about!label":"Informazioni","errors":{"cant-refresh-needparams":"Ricarica fallita (non sono stati specificati tutti i parametri)","cant-render-needparams":"Impossibile renderizzare (non sono stati specificati tutti i parametri)","invalid-hidden-param":"Alcuni dei parametri nascosti hanno valori non validi.","no-current-doc":"Nessun documento corrente!","open-bad-fileformat":"Errore: Sono supportate unicamente le definizioni del JSON del report.","unknown-export-format":"Formato chiave non valido o modulo per {{format}} non caricato correttamente"},"export":{"progress-message":"Pagina {{msg}} di {{total}}...","progress-message-starting":"In avvio..."},"panels":{"report-sections":"Show/Hide Report Sections"},"reportlist":{"openfile":"Apri file"},"sidebar":{"export-title":"Esportazione","openreport-title":"Apri Report","parameters-title":"Parametri","toc-title":"Tabella dei contenuti"},"toolbar":{"continuous":"Continuativo","galleymode":"Modalità galleria","preparingprint":"Preparazione stampa...","print":"Stampa...","singlepage":"Vista Pagina Singola"}}},{"lng":"it","ns":"exporthtml","resources":{"friendlyName":"File HTML","messages":{"cancel-message":"Il processo di esportazione è stato cancellato dall\'utente","embedimages-is-zip-only":"Le immagini incorporate come file esterni sono disponibili solo per il tipo di output \\"zip\\"","first-page-render-failed":"Rendering della prima pagina fallito","multipage-is-zip-only":"L\'esportazione multi pagina è disponibile solo per il tipo di output \\"zip\\""},"settings":{"autoPrint":{"category":"","label":"Stampa all\'Apertura"},"embedImages":{"category":"output","enum":{"embed":"Incorporato","external":"Esterno","none":"Nessuno"},"label":"Immagini incorporate"},"filename":{"category":"","label":"Nome File"},"multiPage":{"category":"output","label":"Multi pagina"},"outputType":{"category":"output","enum":{"auto":"Auto","html":"HTML","zip":"ZIP"},"label":"Tipo di Output"},"title":{"category":"","label":"Titolo"}}}},{"lng":"it","ns":"exportpdf","resources":{"friendlyName":"Documento PDF","messages":{"cancel-message":"Il processo di esportazione è stato cancellato dall\'utente","first-page-render-failed":"Rendering della prima pagina fallito","pdfa-fonts-embed-failure":"PDF/A richiede che tutti i font siano registrati, ma \\"{{font}}\\" non è stato trovato"},"settings":{"annotating":{"category":"sicurezza","label":"Permetti Annotazione"},"author":{"category":"info","label":"Autore"},"autoPrint":{"category":"","label":"Stampa all\'Apertura"},"contentAccessibility":{"category":"sicurezza","label":"Permetti l\'Accessibilità del Contenuto"},"copying":{"category":"sicurezza","label":"Permetti Copia"},"documentAssembly":{"category":"sicurezza","label":"Permetti Assemblaggio Documento"},"filename":{"category":"","label":"Nome File"},"keywords":{"category":"info","label":"Parole Chiave"},"modifying":{"category":"sicurezza","label":"Permetti Modifica"},"ownerPassword":{"category":"sicurezza","label":"Password del Proprietario"},"pdfVersion":{"category":"","label":"Versione PDF"},"printing":{"category":"sicurezza","enum":{"highResolution":"Alta Risoluzione","lowResolution":"Bassa Risoluzione","none":"No"},"label":"Permetti Stampa"},"subject":{"category":"info","label":"Soggetto"},"title":{"category":"info","label":"Titolo"},"userPassword":{"category":"sicurezza","label":"Password dell\'Utente"}}}},{"lng":"it","ns":"exporttabular-data","resources":{"friendlyName":"Dati tabulari","settings":{"colSeparator":{"category":"csv","label":"Separatore Colonne"},"filename":{"category":"","label":"Nome File"},"outputType":{"category":"output","enum":{"plain":"Semplice","zip":"ZIP"},"label":"Tipo di Output"},"quotationSymbol":{"category":"csv","label":"Virgolette"},"rowSeparator":{"category":"csv","label":"Separatori di righe"},"tableSeparator":{"category":"csv","label":"Separatori di tabelle"}}}},{"lng":"it","ns":"exportxlsx","resources":{"friendlyName":"Cartella di lavoro di Excel (xlsx)","messages":{"cancel-message":"Processo di esportazione cancellato dall\'utente","first-page-render-failed":"Primo rendering della pagina fallito"},"settings":{"creator":{"category":"info","label":"Autore"},"filename":{"category":"","label":"Nome File"},"orientation":{"category":"pagina","enum":{"landscape":"Orizzontale","portrait":"Verticale"},"label":"Orientamento"},"password":{"category":"","label":"Password"},"sheetName":{"category":"","label":"Nome Foglio"},"size":{"category":"pagina","enum":{"A4":"A4","A5":"A5","B5":"B5 (JIS)","Double_Japan_Postcard_Rotated":"Doppia cartolina Giapponese Ruotata","Envelope_10":"Busta #10","Envelope_B5":"Busta B5","Envelope_C5":"Busta C5","Envelope_DL":"Busta DL","Envelope_Monarch":"Busta Monarch","Executive":"Esecutivo","K16_197x273_mm":"16K 197x273 m","Legal":"Legale","Letter":"Lettera"},"label":"Dimensione"}}}}]')},function(e){e.exports=JSON.parse('[{"lng":"fr","ns":"core","resources":{"barcode-symbology-not-supported":"\\"{{symbology}}\\" n\'est pas supporté","default-invalid-barcode-text":"ERREUR","errors":{"compile":"Erreur de compilation: {{details}}.","csvdataprovider":{"header-parse":"Impossible d\'analyser l\'en-tête de colonne: \\"{{headerValue}}\\""},"data-processing":"Erreur de traitement des données: {{details}}.","dataprovider":{"commandtext-invalid":"Invalid data provider CommandText: {{commandText}}.","connectstring-invalid":"Invalid data source ConnectString: {{connectString}}.","no-data":"Invalid data provider settings. Source or data is not specified."},"fetch-failed":"Impossible de charger les données de \\"{{uri}}\\": {{responseStatus}} {{responseText}}.","generic-error":"{{errorMessage}}","invalid-datasetname":"Nom de Dataset invalide: \\"{{dataSetName}}\\"","invalid-definition":"La définition de rapport n\'est pas valide: {{details}}.","json-parse":"Echec de l\'analyse du JSON: {{errorMessage}}. Chaîne source: \\"{{jsonString}}\\".","jsondataprovider":{"no-global-data":"Aucune donnée disponible pour le Dataset \\"{{datasetName}}\\" - {{globalPath}} = {{globalValue}}"},"layout":"Erreur de mise en page: {{details}}.","no-datasets":"Aucun Dataset défini.","print":{"cancel-print-message":"L\'impression a été annulée par l\'utilisateur","first-page-render-failed":"Le rendu de la 1ère page a échoué","open-window-failed":"Impossible d\'ouvrir une nouvelle fenêtre"},"report-invalid":"Le rapport \\"{{reportUri}}\\" est invalide: {{error}}","report-not-available":"Le rapport \\"{{reportUri}}\\" n\'est pas disponible: {{error}}","reportPartsLibraries-invalid":"Report Parts library \\"{{reportUri}}\\" is invalid: {{error}}","reportPartsLibraries-not-available":"Report Parts library \\"{{reportUri}}\\" is not available: {{error}}","reportPartsLibraries-unknown":"Unknown Report Parts library \\"{{libraryName}}\\"","subreport-invalid":"Le sous-rapport \\"{{reportUri}}\\" est invalide: {{error}}","subreport-not-available":"Le sous-rapport \\"{{reportUri}}\\" n\'est pas disponible: {{error}}","unknown-dataprovider":"Fournisseur de données inconnu \\"{{providerName}}\\"","unknown-datasource":"Source de données inconnue \\"{{dataSourceName}}\\""},"report-item-not-supported":"\\"{{itemType}}\\" n\'est pas un élément de rapport pris en charge"}},{"lng":"fr","ns":"export","resources":{"boolTextFalse":"Vrai","boolTextTrue":"Faux","cancel-btn":"Annuler","emptyTextPlaceholder":"<vide>","file-format-label":"Format","inprogress-lbl":"Export en cours","nodocument":"Aucun document sélectionné","start-btn":"Export","titleDecrease":"Dimminuer de","titleIncrease":"augmenter de","titleToggle":"Développer..."}},{"lng":"fr","ns":"params","resources":{"bool-false-label":"Faux","bool-true-label":"Vrai","datePlaceholder":"Choisir Date..","daysViewHeaderFormat":"MMMM YYYY","ddpTextNull":"(aucun)","ddpTextSelectAllValues":"(tout sélectionner)","ddpTextSelectValue":"(choisir valeur)","null":"Null","paramsTextInvalidValue":"Valeur invalide","preview-btn-title":"Aperçu"}},{"lng":"fr","ns":"paramsValidation","resources":{"integerTooBig":"* La valeur ne peut pas excéder un entier de 53-bits","invalidBoolean":"* Valeur incorrecte. Assurez-vous de renseigner un booléen valide","invalidDateTime":"* Valeur incorrecte. Assurez-vous de renseigner une date valide","invalidFloat":"* Valeur incorrecte. Assurez-vous de renseigner un flottant valide","invalidInteger":"* Valeur incorrecte. Assurez-vous de renseigner un entier valide","outstandingDependencies":"* Le paramètre possède des dépendances non résolues","requiredSingleValue":"* Valeur single attendue","requiredValue":"* Valeur attendue","stringEmpty":"* Valeur incorrecte. Les chaînes de caractères vides ne sont pas autorisées","unknown":"! Erreur de validation inconnue","valueIsNull":"* La valeur ne peut pas être nulle","valueOutOfRange":"* Valeur incorrecte. Choisissez-en une dans la liste"}},{"lng":"fr","ns":"paramsView","resources":{"boolTextFalse":"Faux","boolTextTrue":"Vrai","boolTextUnspecified":"Non spécifié","btnTextClear":"Effacer","btnTextPreview":"Aperçu","btnTextReset":"Réinitialiser","dateTimeRange":{"rangeTypes":{"Current_Day":"Current Day","Current_Hour":"Current Hour","Current_Minute":"Current Minute","Current_Month":"Current Month","Current_Quarter":"Current Quarter","Current_Week":"Current Week","Current_Year":"Current Year","LastSimple_Day":"Last Day","LastSimple_Hour":"Last Hour","LastSimple_Minute":"Last Minute","LastSimple_Month":"Last Month","LastSimple_Quarter":"Last Quarter","LastSimple_Second":"Last Second","LastSimple_Week":"Last Week","LastSimple_Year":"Last Year","LastToDateSimple_Day":"Last Day To Date","LastToDateSimple_Month":"Last Month To Date","LastToDateSimple_Quarter":"Last Quarter To Date","LastToDateSimple_Week":"Last Week To Date","LastToDateSimple_Year":"Last Year To Date","LastToDate_Day":"Last {{count}} Days To Date","LastToDate_Day_plural":"Last {{count}} Days To Date","LastToDate_Month":"Last {{count}} Months To Date","LastToDate_Month_plural":"Last {{count}} Months To Date","LastToDate_Quarter":"Last {{count}} Quarters To Date","LastToDate_Quarter_plural":"Last {{count}} Quarters To Date","LastToDate_Week":"Last {{count}} Weeks To Date","LastToDate_Week_plural":"Last {{count}} Weeks To Date","LastToDate_Year":"Last {{count}} Years To Date","LastToDate_Year_plural":"Last {{count}} Years To Date","Last_Day":"Last {{count}} Days","Last_Day_plural":"Last {{count}} Days","Last_Hour":"Last {{count}} Hours","Last_Hour_plural":"Last {{count}} Hours","Last_Minute":"Last {{count}} Minutes","Last_Minute_plural":"Last {{count}} Minutes","Last_Month":"Last {{count}} Months","Last_Month_plural":"Last {{count}} Months","Last_Quarter":"Last {{count}} Quarters","Last_Quarter_plural":"Last {{count}} Quarters","Last_Second":"Last {{count}} Seconds","Last_Second_plural":"Last {{count}} Seconds","Last_Week":"Last {{count}} Weeks","Last_Week_plural":"Last {{count}} Weeks","Last_Year":"Last {{count}} Years","Last_Year_plural":"Last {{count}} Years","NextSimple_Day":"Next Day","NextSimple_Hour":"Next Hour","NextSimple_Minute":"Next Minute","NextSimple_Month":"Next Month","NextSimple_Quarter":"Next Quarter","NextSimple_Second":"Next Second","NextSimple_Week":"Next Week","NextSimple_Year":"Next Year","Next_Day":"Next {{count}} Days","Next_Day_plural":"Next {{count}} Days","Next_Hour":"Next {{count}} Hours","Next_Hour_plural":"Next {{count}} Hours","Next_Minute":"Next {{count}} Minutes","Next_Minute_plural":"Next {{count}} Minutes","Next_Month":"Next {{count}} Months","Next_Month_plural":"Next {{count}} Months","Next_Quarter":"Next {{count}} Quarters","Next_Quarter_plural":"Next {{count}} Quarters","Next_Second":"Next {{count}} Seconds","Next_Second_plural":"Next {{count}} Seconds","Next_Week":"Next {{count}} Weeks","Next_Week_plural":"Next {{count}} Weeks","Next_Year":"Next {{count}} Years","Next_Year_plural":"Next {{count}} Years","ToDate_Day":"Day-To-Date","ToDate_Month":"Month-To-Date","ToDate_Quarter":"Quarter-To-Date","ToDate_Week":"Week-To-Date","ToDate_Year":"Year-To-Date"}},"ddTitleToggle":"Développer","ddeTextAllValues":"(all)","ddeTextNull":"(null)","ddeTextSelectAllValues":"(Sélectionner tout)","ddeTextSelectValue":"(Sélectionner une valeur)","drDaysViewHeaderFormat":"MMMM YYYY","drMonthYearOrder":"M-Y","drPlaceholderDateEnd":"Fin","drPlaceholderDateStart":"Début","drShortcutsLabelLastMonth":"Mois dernier","drShortcutsLabelLastMonthToDate":"Last month to date","drShortcutsLabelLastWeek":"Semaine dernière","drShortcutsLabelLastWeekToDate":"Last week to date","drShortcutsLabelLastYear":"Année dernière","drShortcutsLabelLastYearToDate":"Last year to date","drShortcutsLabelMonthToDate":"Mois courant","drShortcutsLabelWeekToDate":"Semaine courante","drShortcutsLabelYearToDate":"Année courante","drTabLabelAnnually":"Annuel","drTabLabelDaily":"Quotidien","drTabLabelMonthly":"Mensuel","drTabLabelTime":"Time","drTextBack":"Retour au calendrier","drTextShortcutsList":"Plages communes","drTitleToggle":"Développer calendrier","dtDaysViewHeaderFormat":"MMMM YYYY","dtMonthYearOrder":"M-Y","dtTextBack":"Retour au calendrier","dtTextClear":"Effacer","dtTextSelectDate":"Choisir une date...","dtTextSelectFromDate":"Select \\"From\\" Date...","dtTextSelectToDate":"Select \\"To\\" Date...","dtTextToday":"Aujourd\'hui","dtTitleQuickRangePicker":"Common Ranges","dtTitleToggle":"Développer calendrier","lblTextInvalidValue":"Valeur invalide","lblTextNoAvailableValues":"Aucune donnée disponible","listTextSelectAllValues":"(Sélectionner tout)","nwrpTextNull":"Null"}},{"lng":"fr","ns":"singleDateFormat","resources":{"D":"dddd, MMMM D, YYYY","F":"dddd, MMMM D, YYYY h:mm:ss A","G":"M/D/YYYY h:mm:ss A","M":"MMMM D","O":"YYYY-MM-DDTHH:mm:ss.SSSZ","R":"ddd, DD MMM YYYY HH:mm:ss [GMT]","T":"h:mm:ss A","U":"dddd, MMMM D, YYYY h:mm:ss A","Y":"MMMM YYYY","d":"MM/D/YYYY","f":"dddd, MMMM D, YYYY h:mm A","g":"M/D/YYYY h:mm A","m":"MMMM D","o":"YYYY-MM-DDTHH:mm:ss.SSSZ","r":"ddd, DD MMM YYYY HH:mm:ss [GMT]","s":"YYYY-MM-DDTHH:mm:ss","t":"h:mm A","u":"YYYY-MM-DD HH:mm:ss[Z]","y":"MMMM YYYY"}},{"lng":"fr","ns":"viewer","resources":{"cancel-btn":"Annuler","document-view":{"aria-label":"Vue document"},"error":{"btnShowDetails":"Show Details","dismiss":"Rejeter","dismiss-all":"Tout rejeter","notificationsDetails":"Notifications details","showAll":"Show All","textError":"Error","textError_plural":"Errors","textNotification":"Notification","textNotification_plural":"Notifications","textWarning":"Warning","textWarning_plural":"Warnings","titleCancelTask":"Cancel this task","titleCollapse":"Collapse","titleExpand":"Expand"},"errors":{"noHostElement":"Impossible de trouver l\'élément hôte."},"menu":{"aria-label":"Menu","pin-button-title":"Épingler","toogleText":"Développer menu"},"progress":{"page":"Page"},"search":{"cancel-btn":"Annuler","clear-btn":"Effacer","didn-find-msg":"Aucune occurence trouvée.","match-case":"Sensible à la casse","more-results-btn":"Plus de résultats","paneltitle":"Rechercher","search-cancelled-msg":"Recherche annulée à la page {{page}}","search-results":"Résultats de la recherche","start-search-btn":"Rechercher","whole-word":"Mot entier"},"sidebar":{"aria-label":"Barre latérale","collapse-btn":"Réduire","expand-btn":"Développer"},"toolbar":{"aria-label":"Barre d\'outils","cancel":"Annuler","expand":"Développer barre d\'outils","fullscreen":"Passer en plein écran","gotofirst":"Aller au premier","gotolast":"Aller au dernier","gotonext":"Aller au suivant","gotoprevious":"Aller au précédent","hist-back":"Historique: aller en arrière","hist-fwd":"Historique: aller en avant","hist-parent":"Historique: retour au parent","movetool":"Outil de déplacement","refresh":"Rafraîchir","zoom-fitpage":"Remplir page","zoom-fitwidth":"Ajuster à la largeur","zoom-menu-header":"Mode zoom","zoom-zoomin":"Zoomer","zoom-zoomout":"Dézoomer"},"top-bottom-panel":{"aria-label":"Panneau de contrôle additionnel"}}},{"lng":"fr","ns":"arjsv","resources":{"about!label":"A propos","errors":{"cant-refresh-needparams":"Le raffraichissement a échoué (tous les paramètres ne sont pas spécifiés)","cant-render-needparams":"Rendu impossible (tous les paramètres ne sont pas spécifiés)","invalid-hidden-param":"Certains paramètres cachés ont des valeurs incorrectes.","no-current-doc":"Aucun document courant!","open-bad-fileformat":"Erreur: seules les définition de rapport JSON sont supportées.","unknown-export-format":"Clef de format invalide ou le module de {{format}} n\'est pas chargé"},"export":{"progress-message":"Page {{msg}} sur {{total}}...","progress-message-starting":"Démarrage..."},"panels":{"report-sections":"Afficher/cacher les sections de rapport"},"reportlist":{"openfile":"Ouvrir fichier"},"sidebar":{"export-title":"Export","openreport-title":"Ouvrir rapport","parameters-title":"Paramètres","toc-title":"Table des matières"},"toolbar":{"continuous":"Continue","galleymode":"Mode gallerie","preparingprint":"Préparation de l\'impression...","print":"Imprimer...","singlepage":"Vue page unique"}}},{"lng":"fr","ns":"exporthtml","resources":{"friendlyName":"Fichier HTML","messages":{"cancel-message":"L\'export a été annulé par l\'utilisateur","embedimages-is-zip-only":"Intégrer des images comme fichiers externes n\'est autorisé que pour les types de sortie \\"zip\\"","first-page-render-failed":"Le rendu de la 1ère page a échoué","multipage-is-zip-only":"L\'export multi-pages n\'est disponible que pour le type de sortie \\"zip\\""},"settings":{"autoPrint":{"category":"","label":"Imprimer à l\'ouverture"},"embedImages":{"category":"Sortie","enum":{"embed":"Intégré","external":"Externe","none":"Aucun"},"label":"Images intégrées"},"filename":{"category":"","label":"Nom de fichier"},"multiPage":{"category":"Sortie","label":"Multi-pages"},"outputType":{"category":"Sortie","enum":{"auto":"Auto","html":"HTML","zip":"ZIP"},"label":"Type de sortie"},"title":{"category":"","label":"Titre"}}}},{"lng":"fr","ns":"exportpdf","resources":{"friendlyName":"Document PDF","messages":{"cancel-message":"L\'export a été annulé par l\'utilisateur","first-page-render-failed":"Le rendu de la 1ère page a échoué","pdfa-fonts-embed-failure":"PDF/A requiert que toutes les polices soient inscrites, mais \\"{{font}}\\" n\'a pas été trouvé"},"settings":{"annotating":{"category":"Sécurité","label":"Autoriser annotation"},"author":{"category":"Info","label":"Auteur"},"autoPrint":{"category":"","label":"Imprimer à l\'ouverture"},"contentAccessibility":{"category":"Sécurité","label":"Autoriser l\'accès au contenu"},"copying":{"category":"Sécurité","label":"Autoriser la copie"},"documentAssembly":{"category":"Sécurité","label":"Autoriser l\'assemblage de documents"},"filename":{"category":"","label":"Nom de fichier"},"keywords":{"category":"Info","label":"Mots-clefs"},"modifying":{"category":"Sécurité","label":"Autoriser modification"},"ownerPassword":{"category":"Sécurité","label":"Mot de passe propriétaire"},"pdfVersion":{"category":"","label":"Version PDF"},"printing":{"category":"Sécurité","enum":{"highResolution":"Haute résolution","lowResolution":"Basse résolution","none":"Aucun"},"label":"Autoriser l\'impression"},"subject":{"category":"Info","label":"Sujet"},"title":{"category":"Info","label":"titre"},"userPassword":{"category":"Sécurité","label":"Mot de passe utilisateur"}}}},{"lng":"fr","ns":"exporttabular-data","resources":{"friendlyName":"Données tabulaires","settings":{"colSeparator":{"category":"csv","label":"Séparateur de colonnes"},"filename":{"category":"","label":"Nom de fichier"},"outputType":{"category":"Sortie","enum":{"plain":"Plein","zip":"ZIP"},"label":"Type de sortie"},"quotationSymbol":{"category":"csv","label":"Symbole de citation"},"rowSeparator":{"category":"csv","label":"Séparateur de lignes"},"tableSeparator":{"category":"csv","label":"Séparateur de tables"}}}},{"lng":"fr","ns":"exportxlsx","resources":{"friendlyName":"Classeur Excel (xlsx)","messages":{"cancel-message":"L\'export a été annulé par l\'utilisateur","first-page-render-failed":"Le rendu de la 1ère page a échoué"},"settings":{"creator":{"category":"Info","label":"Auteur"},"filename":{"category":"","label":"Nom de fichier"},"orientation":{"category":"Page","enum":{"landscape":"Paysage","portrait":"Portrait"},"label":"Orientation"},"password":{"category":"","label":"Mot de passe"},"sheetName":{"category":"","label":"Nom de la feuille"},"size":{"category":"Page","enum":{"A4":"A4","A5":"A5","B5":"B5 (JIS)","Double_Japan_Postcard_Rotated":"Double carte postale du Japon pivotée","Envelope_10":"Envelope #10","Envelope_B5":"Envelope B5","Envelope_C5":"Envelope C5","Envelope_DL":"Envelope DL","Envelope_Monarch":"Enveloppe monarque","Executive":"Executive","K16_197x273_mm":"16K 197x273 m","Legal":"Legal","Letter":"Lettre"},"label":"Taille"}}}}]')},function(e){e.exports=JSON.parse('[{"lng":"es","ns":"core","resources":{"barcode-symbology-not-supported":"\\"{{symbology}}\\" no es soportado","default-invalid-barcode-text":"ERROR","errors":{"compile":"Reporte de error de compilación: {{details}}.","csvdataprovider":{"header-parse":"No se ha podido analizar la cabecera de la columna \\"{{headerValue}}\\""},"data-processing":"Error de procesamiento de datos: {{details}}.","dataprovider":{"commandtext-invalid":"Invalid data provider CommandText: {{commandText}}.","connectstring-invalid":"Invalid data source ConnectString: {{connectString}}.","no-data":"Invalid data provider settings. Source or data is not specified."},"fetch-failed":"No se pueden cargar datos de \\"{{uri}}\\": {{responseStatus}} {{responseText}}.","generic-error":"{{errorMessage}}","invalid-datasetname":"DataSetName inválido: \\"{{dataSetName}}\\"","invalid-definition":"La definición del informe inválida: {{details}}.","json-parse":"Error al analizar JSON: {{errorMessage}}. Cadena de origen: \\"{{jsonString}}\\".","jsondataprovider":{"no-global-data":"No hay dato disponible para DataSet \\"{{datasetName}}\\" - {{globalPath}} = {{globalValue}}"},"layout":"Informar de error de diseño: {{details}}.","no-datasets":"Ningún DataSets definido.","print":{"cancel-print-message":"Proceso de imprimir cancelado por el usuario","first-page-render-failed":"No se pudo mostrar la primera página","open-window-failed":"No se pudo abrir una nueva ventana"},"report-invalid":"Reporte \\"{{reportUri}}\\" es invalido: {{error}}","report-not-available":"Reporte \\"{{reportUri}}\\" no disponible: {{error}}","reportPartsLibraries-invalid":"Report Parts library \\"{{reportUri}}\\" is invalid: {{error}}","reportPartsLibraries-not-available":"Report Parts library \\"{{reportUri}}\\" is not available: {{error}}","reportPartsLibraries-unknown":"Unknown Report Parts library \\"{{libraryName}}\\"","subreport-invalid":"Subreporte \\"{{reportUri}}\\" es inválido: {{error}}","subreport-not-available":"Subreporte \\"{{reportUri}}\\" no disponible: {{error}}","unknown-dataprovider":"Proveedor de datos desconocido \\"{{providerName}}\\"","unknown-datasource":"Fuente de datos desconocido \\"{{dataSourceName}}\\""},"report-item-not-supported":"Ítem del reporte \\"{{itemType}}\\" no soportado"}},{"lng":"es","ns":"export","resources":{"boolTextFalse":"Falso","boolTextTrue":"Verdadero","cancel-btn":"Cancelar","emptyTextPlaceholder":"<vacío>","file-format-label":"Formato","inprogress-lbl":"Exportación en progreso","nodocument":"Documento sin seleccionar","start-btn":"Exportar","titleDecrease":"Disminuir en","titleIncrease":"Incrementar en","titleToggle":"Expandir..."}},{"lng":"es","ns":"params","resources":{"bool-false-label":"Falso","bool-true-label":"Verdadero","datePlaceholder":"Seleccionar Fecha...","daysViewHeaderFormat":"MMMM AAAA","ddpTextNull":"(nulo)","ddpTextSelectAllValues":"(seleccionar todo)","ddpTextSelectValue":"(seleccionar valor)","null":"Nulo","paramsTextInvalidValue":"Valor inválido","preview-btn-title":"Previsualización"}},{"lng":"es","ns":"paramsValidation","resources":{"integerTooBig":"* Valor no puede exceder entero de 53-bit","invalidBoolean":"* Valor equivocado. Asegúrese de ingresar un booleano válido","invalidDateTime":"* Valor equivocado. Asegúrese de ingresar una fecha válida","invalidFloat":"* Valor equivocado. Asegúrese de ingresar un flotante válido","invalidInteger":"* Valor equivocado. Asegúrese de ingresar un entero válido","outstandingDependencies":"* Parámetro con dependencias pendientes","requiredSingleValue":"* Valor único esperado","requiredValue":"* Valor esperado","stringEmpty":"* Valor equivocado. Cadenas de caracteres vacías no permitidas","unknown":"! Error de validación desconocido","valueIsNull":"* Valor no puede ser nulo","valueOutOfRange":"* Valor equivocado. Seleccione el correcto de la lista"}},{"lng":"es","ns":"paramsView","resources":{"boolTextFalse":"Falso","boolTextTrue":"Verdadero","boolTextUnspecified":"Sin especificar","btnTextClear":"Limpiar","btnTextPreview":"Previsualización","btnTextReset":"Resetear","dateTimeRange":{"rangeTypes":{"Current_Day":"Current Day","Current_Hour":"Current Hour","Current_Minute":"Current Minute","Current_Month":"Current Month","Current_Quarter":"Current Quarter","Current_Week":"Current Week","Current_Year":"Current Year","LastSimple_Day":"Last Day","LastSimple_Hour":"Last Hour","LastSimple_Minute":"Last Minute","LastSimple_Month":"Last Month","LastSimple_Quarter":"Last Quarter","LastSimple_Second":"Last Second","LastSimple_Week":"Last Week","LastSimple_Year":"Last Year","LastToDateSimple_Day":"Last Day To Date","LastToDateSimple_Month":"Last Month To Date","LastToDateSimple_Quarter":"Last Quarter To Date","LastToDateSimple_Week":"Last Week To Date","LastToDateSimple_Year":"Last Year To Date","LastToDate_Day":"Last {{count}} Days To Date","LastToDate_Day_plural":"Last {{count}} Days To Date","LastToDate_Month":"Last {{count}} Months To Date","LastToDate_Month_plural":"Last {{count}} Months To Date","LastToDate_Quarter":"Last {{count}} Quarters To Date","LastToDate_Quarter_plural":"Last {{count}} Quarters To Date","LastToDate_Week":"Last {{count}} Weeks To Date","LastToDate_Week_plural":"Last {{count}} Weeks To Date","LastToDate_Year":"Last {{count}} Years To Date","LastToDate_Year_plural":"Last {{count}} Years To Date","Last_Day":"Last {{count}} Days","Last_Day_plural":"Last {{count}} Days","Last_Hour":"Last {{count}} Hours","Last_Hour_plural":"Last {{count}} Hours","Last_Minute":"Last {{count}} Minutes","Last_Minute_plural":"Last {{count}} Minutes","Last_Month":"Last {{count}} Months","Last_Month_plural":"Last {{count}} Months","Last_Quarter":"Last {{count}} Quarters","Last_Quarter_plural":"Last {{count}} Quarters","Last_Second":"Last {{count}} Seconds","Last_Second_plural":"Last {{count}} Seconds","Last_Week":"Last {{count}} Weeks","Last_Week_plural":"Last {{count}} Weeks","Last_Year":"Last {{count}} Years","Last_Year_plural":"Last {{count}} Years","NextSimple_Day":"Next Day","NextSimple_Hour":"Next Hour","NextSimple_Minute":"Next Minute","NextSimple_Month":"Next Month","NextSimple_Quarter":"Next Quarter","NextSimple_Second":"Next Second","NextSimple_Week":"Next Week","NextSimple_Year":"Next Year","Next_Day":"Next {{count}} Days","Next_Day_plural":"Next {{count}} Days","Next_Hour":"Next {{count}} Hours","Next_Hour_plural":"Next {{count}} Hours","Next_Minute":"Next {{count}} Minutes","Next_Minute_plural":"Next {{count}} Minutes","Next_Month":"Next {{count}} Months","Next_Month_plural":"Next {{count}} Months","Next_Quarter":"Next {{count}} Quarters","Next_Quarter_plural":"Next {{count}} Quarters","Next_Second":"Next {{count}} Seconds","Next_Second_plural":"Next {{count}} Seconds","Next_Week":"Next {{count}} Weeks","Next_Week_plural":"Next {{count}} Weeks","Next_Year":"Next {{count}} Years","Next_Year_plural":"Next {{count}} Years","ToDate_Day":"Day-To-Date","ToDate_Month":"Month-To-Date","ToDate_Quarter":"Quarter-To-Date","ToDate_Week":"Week-To-Date","ToDate_Year":"Year-To-Date"}},"ddTitleToggle":"Expandir","ddeTextAllValues":"(todos)","ddeTextNull":"(nulo)","ddeTextSelectAllValues":"(seleccionar todo)","ddeTextSelectValue":"(seleccionar valor)","drDaysViewHeaderFormat":"MMMM AAAA","drMonthYearOrder":"M-A","drPlaceholderDateEnd":"Fin","drPlaceholderDateStart":"Comienzo","drShortcutsLabelLastMonth":"Último mes","drShortcutsLabelLastMonthToDate":"Last month to date","drShortcutsLabelLastWeek":"Última semana","drShortcutsLabelLastWeekToDate":"Last week to date","drShortcutsLabelLastYear":"Último año","drShortcutsLabelLastYearToDate":"Last year to date","drShortcutsLabelMonthToDate":"Mes a la fecha","drShortcutsLabelWeekToDate":"Semana a la fecha","drShortcutsLabelYearToDate":"Año a la fecha","drTabLabelAnnually":"Anualmente","drTabLabelDaily":"Diario","drTabLabelMonthly":"Mensualmente","drTabLabelTime":"Time","drTextBack":"Volver al Calendario","drTextShortcutsList":"Rangos Comunes","drTitleToggle":"Expandir Calendario","dtDaysViewHeaderFormat":"MMMM AAAA","dtMonthYearOrder":"M-A","dtTextBack":"Volver al Calendario","dtTextClear":"Limpiar","dtTextSelectDate":"Seleccionar Fecha...","dtTextSelectFromDate":"Select \\"From\\" Date...","dtTextSelectToDate":"Select \\"To\\" Date...","dtTextToday":"Hoy","dtTitleQuickRangePicker":"Common Ranges","dtTitleToggle":"Expandir Calendario","lblTextInvalidValue":"Valor Inválido","lblTextNoAvailableValues":"Valores no disponibles","listTextSelectAllValues":"(seleccione todo)","nwrpTextNull":"Nulo"}},{"lng":"es","ns":"singleDateFormat","resources":{"D":"dddd, MMMM D, YYYY","F":"dddd, MMMM D, YYYY h:mm:ss A","G":"M/D/YYYY h:mm:ss A","M":"MMMM D","O":"YYYY-MM-DDTHH:mm:ss.SSSZ","R":"ddd, DD MMM YYYY HH:mm:ss [GMT]","T":"h:mm:ss A","U":"dddd, MMMM D, YYYY h:mm:ss A","Y":"MMMM YYYY","d":"MM/D/YYYY","f":"dddd, MMMM D, YYYY h:mm A","g":"M/D/YYYY h:mm A","m":"MMMM D","o":"YYYY-MM-DDTHH:mm:ss.SSSZ","r":"ddd, DD MMM YYYY HH:mm:ss [GMT]","s":"YYYY-MM-DDTHH:mm:ss","t":"h:mm A","u":"YYYY-MM-DD HH:mm:ss[Z]","y":"MMMM YYYY"}},{"lng":"es","ns":"viewer","resources":{"cancel-btn":"Cancelar","document-view":{"aria-label":"Vista del documento"},"error":{"btnShowDetails":"Show Details","dismiss":"Descartar","dismiss-all":"Descartar Todo","notificationsDetails":"Notifications details","showAll":"Show All","textError":"Error","textError_plural":"Errors","textNotification":"Notification","textNotification_plural":"Notifications","textWarning":"Warning","textWarning_plural":"Warnings","titleCancelTask":"Cancel this task","titleCollapse":"Collapse","titleExpand":"Expand"},"errors":{"noHostElement":"No se encuentra el elemento anfitrión."},"menu":{"aria-label":"Menú","pin-button-title":"Pin","toogleText":"Expandir menú"},"progress":{"page":"Página"},"search":{"cancel-btn":"Cancelar","clear-btn":"Limpiar","didn-find-msg":"No se encontró nada.","match-case":"Caso de Coincidencia","more-results-btn":"Mas Resultados","paneltitle":"Buscar","search-cancelled-msg":"Buscar cancelados en página {{page}}","search-results":"Buscar Resultados","start-search-btn":"Buscar","whole-word":"Palabra completa"},"sidebar":{"aria-label":"Barra lateral","collapse-btn":"Colapsar","expand-btn":"Expandir"},"toolbar":{"aria-label":"Barra de herramientas","cancel":"Cancelar","expand":"Expandir barra de herramientas","fullscreen":"Alternar Pantalla Completa","gotofirst":"Ir Al Principio","gotolast":"Ir Al Final","gotonext":"Ir Al Siguiente","gotoprevious":"Ir Al Anterior","hist-back":"Historial: Ir Atrás","hist-fwd":"Historial: Ir Adelante","hist-parent":"Historial: Volver Al Parental","movetool":"Mover Herramienta","refresh":"Refrescar","zoom-fitpage":"Ajustar Página","zoom-fitwidth":"Ajustar al Ancho","zoom-menu-header":"Modo de Zoom","zoom-zoomin":"Acercar","zoom-zoomout":"Alejar"},"top-bottom-panel":{"aria-label":"Panel de control adicional"}}},{"lng":"es","ns":"arjsv","resources":{"about!label":"Acerca de","errors":{"cant-refresh-needparams":"Falla al refrescar (no todos los parámetros son especificados)","cant-render-needparams":"No es posible renderizar (no todos los parámetros son especificados)","invalid-hidden-param":"Algunos parámetros ocultos tienen valores inválidos.","no-current-doc":"Sin documento actual!","open-bad-fileformat":"Error: Sólo se admiten definiciones de reportes JSON.","unknown-export-format":"Clave de formato inválido o el módulo requerido para {{format}} no está cargado."},"export":{"progress-message":"Página {{msg}} de {{total}}...","progress-message-starting":"Empezando..."},"panels":{"report-sections":"Mostrar/Ocultar Sección de Reportes"},"reportlist":{"openfile":"Abrir Archivo"},"sidebar":{"export-title":"Exportar","openreport-title":"Abrir Reporte","parameters-title":"Parámetros","toc-title":"Tabla de Contenido"},"toolbar":{"continuous":"Continuo","galleymode":"Modo Reducido","preparingprint":"Preparando impresión...","print":"Imprimir...","singlepage":"Vista de página individual"}}},{"lng":"es","ns":"exporthtml","resources":{"friendlyName":"Archivo HTML","messages":{"cancel-message":"Proceso de exportación cancelado por el usuario","embedimages-is-zip-only":"Incluir imágenes como archivos externos sólo está disponible para el formato de salida \\"zip\\".","first-page-render-failed":"No se pudo mostrar la primera página","multipage-is-zip-only":"La exportación multipágina sólo está disponible para el formato de salida \\"zip\\"."},"settings":{"autoPrint":{"category":"","label":"Imprimir al Abrir"},"embedImages":{"category":"salida","enum":{"embed":"Insertar","external":"Externo","none":"Ninguno"},"label":"Insertar Imágenes"},"filename":{"category":"","label":"Nombre del Archivo"},"multiPage":{"category":"salida","label":"Multi Página"},"outputType":{"category":"salida","enum":{"auto":"Auto","html":"HTML","zip":"ZIP"},"label":"Tipo de Salida"},"title":{"category":"","label":"Título"}}}},{"lng":"es","ns":"exportpdf","resources":{"friendlyName":"Documento PDF","messages":{"cancel-message":"Proceso de exportación cancelado por el usuario","first-page-render-failed":"No se pudo mostrar la primera página","pdfa-fonts-embed-failure":"PDF/A exige que todas las fuentes estén registradas, pero \\"{{font}}\\" no fue encontrado"},"settings":{"annotating":{"category":"seguridad","label":"Permitir Anotaciones"},"author":{"category":"info","label":"Autor"},"autoPrint":{"category":"","label":"Imprimir al Abrir"},"contentAccessibility":{"category":"seguridad","label":"Permitir Accesibilidad de Contentidos"},"copying":{"category":"seguridad","label":"Permitir Copiar"},"documentAssembly":{"category":"seguridad","label":"Permitir Añadir Documentos"},"filename":{"category":"","label":"Nombre de Archivo"},"keywords":{"category":"info","label":"Palabras Clave"},"modifying":{"category":"seguridad","label":"Permitir Modificar"},"ownerPassword":{"category":"seguridad","label":"Contraseña del Propietario"},"pdfVersion":{"category":"","label":"Versión de PDF"},"printing":{"category":"seguridad","enum":{"highResolution":"Alta Resolución","lowResolution":"Baja Resolución","none":"Ninguna"},"label":"Permitir Imprimir"},"subject":{"category":"info","label":"Asunto"},"title":{"category":"info","label":"Título"},"userPassword":{"category":"seguridad","label":"Contraseña de Usuario"}}}},{"lng":"es","ns":"exporttabular-data","resources":{"friendlyName":"Datos en tabla","settings":{"colSeparator":{"category":"csv","label":"Separador de Columnas"},"filename":{"category":"","label":"Nombre de Archivo"},"outputType":{"category":"salida","enum":{"plain":"Plano","zip":"ZIP"},"label":"Tipo de Salida"},"quotationSymbol":{"category":"csv","label":"Comillas"},"rowSeparator":{"category":"csv","label":"Separador de Filas"},"tableSeparator":{"category":"csv","label":"Separador de Tablas"}}}},{"lng":"es","ns":"exportxlsx","resources":{"friendlyName":"Libro de Excel (xlsx)","messages":{"cancel-message":"Proceso de exportación cancelado por el usuario","first-page-render-failed":"No se pudo mostrar la primera página"},"settings":{"creator":{"category":"info","label":"Autor"},"filename":{"category":"","label":"Nombre del Archivo"},"orientation":{"category":"página","enum":{"landscape":"Panorama","portrait":"Retrato"},"label":"Orientación"},"password":{"category":"","label":"Contraseña"},"sheetName":{"category":"","label":"Nombre de Hoja"},"size":{"category":"página","enum":{"A4":"A4","A5":"A5","B5":"B5 (JIS)","Double_Japan_Postcard_Rotated":"Double Japan Postcard Rotated","Envelope_10":"Sobre #10","Envelope_B5":"Sobre B5","Envelope_C5":"Sobre C5","Envelope_DL":"Sobre DL","Envelope_Monarch":"Sobre Monarch","Executive":"Ejecutivo","K16_197x273_mm":"16K 197x273 m","Legal":"Legal","Letter":"Carta"},"label":"Tamaño"}}}}]')}]);