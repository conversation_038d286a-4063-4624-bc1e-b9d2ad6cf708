{"name": "smn-fi", "months": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kuovâmáán<PERSON>", "njuhč<PERSON><PERSON><PERSON><PERSON><PERSON>", "cu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "v<PERSON><PERSON><PERSON><PERSON><PERSON>", "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "syeinimáánu", "porge<PERSON><PERSON><PERSON><PERSON>", "č<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roovv<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skammâ<PERSON><PERSON><PERSON><PERSON>", "juovl<PERSON><PERSON><PERSON><PERSON><PERSON>", ""], "monthsDeclined": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kuovâmáán<PERSON>", "njuhč<PERSON><PERSON><PERSON><PERSON><PERSON>", "cu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "v<PERSON><PERSON><PERSON><PERSON><PERSON>", "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "syeinimáánu", "porge<PERSON><PERSON><PERSON><PERSON>", "č<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roovv<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skammâ<PERSON><PERSON><PERSON><PERSON>", "juovl<PERSON><PERSON><PERSON><PERSON><PERSON>", ""], "monthsShort": ["<PERSON><PERSON><PERSON>", "kuov", "njuh", "cu<PERSON><PERSON>", "vyes", "kesi", "syei", "porg", "<PERSON><PERSON><PERSON>", "roov", "skam", "juov", ""], "days": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "v<PERSON><PERSON><PERSON><PERSON>", "majebargâ", "koskokko", "tuor<PERSON><PERSON><PERSON><PERSON>", "vástuppeivi", "lá<PERSON>rdâh"], "daysShort": ["pas", "vuo", "maj", "kos", "tuo", "vás", "láv"], "quarters": ["1", "2", "3", "4"], "amPm": ["", ""], "amPmShort": ["", ""], "era": "A.D.", "thousandSeparator": " ", "decimalSeparator": ",", "formats": {"Percent": "###,###,##0.00 %;-###,###,##0.00 %", "Currency": "###,###,##0.00 €;-###,###,##0.00 €", "Short date": "d<PERSON><PERSON><PERSON>yyyy", "Long date": "MMMM d\". p. \"yyyy", "Month/day": "MMMM d\". p. \"", "Short time": "H:mm", "Long time": "H:mm:ss", "Year/month": "MMMM yyyy"}}