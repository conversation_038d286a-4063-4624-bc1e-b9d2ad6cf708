!function(e){var t={};function a(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,a),o.l=!0,o.exports}a.m=e,a.c=t,a.d=function(e,t,r){a.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.t=function(e,t){if(1&t&&(e=a(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(a.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)a.d(r,o,function(t){return e[t]}.bind(null,o));return r},a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,"a",t),t},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.p="",a(a.s=44)}({25:function(e){e.exports=JSON.parse('[{"lng":"ko","ns":"adorners","resources":{"bandedList":{"detailsLabel":"<세부 정보>","groupsLabel":"그룹"},"bullet":{"targetValue":"대상 값","value":"값"},"chart":{"categoryEncoding":{"fieldStub":"범주 {{index}}"},"categoryFields":"범주 필드","colorEncoding":{"fieldStub":"색 {{index}}","title":"색"},"dataFields":"데이터 필드","detailEncoding":{"fieldStub":"세부 정보 {{index}}","title":"세부 정보"},"encodingFields":"인코딩","gaugeLabel":{"fieldStub":"라벨 {{index}}"},"gaugePointer":{"fieldStub":"포인트 {{index}}"},"gaugeRanges":"계기 범위","labels":"라벨","multiValueField":"{{firstFieldValue}}, ...","shapeEncoding":{"fieldStub":"도형 {{index}}","title":"도형"},"sizeEncoding":{"fieldStub":"크기 {{index}}","title":"크기"},"textEncoding":{"title":"텍스트"},"valueEncoding":{"fieldStub":"값 {{index}}"}},"common":{"dropFieldsAndValues":"여기로 필드 및 값 끌어서 놓기"},"formattedText":{"previewError":"XHTML 구문 분석 오류가 발생했습니다. Html 속성 값 및 병합 필드 값을 확인하십시오."},"shapeRoundingTip":{"multiCornerMode":"\\"Single Corner\\" 모드로 전환하려면 \\"Alt\\" 키를 누르십시오","singleCornerMode":"\\"Multi-Corner\\" 모드로 전환하려면 \\"Alt\\" 키를 누르십시오"},"sparkline":{"groupingExpressions":"그룹화 식","seriesValue":"계열 값"},"table":{"detailsGroupLabel":"<세부 정보 그룹>","groupsLabel":"그룹","newGroupLabel":"<새 그룹>"},"tableOfContents":{"addItem":"항목 추가"}}},{"lng":"ko","ns":"captions","resources":{"barcodeUnsupportedSymbology":"[{{symbology}}] \'{{itemName}}\'은(는) 미리보기가 디자인 타임에서는 제한되어 있습니다.","basicSupportReportItemInfo":"{{itemLabel}} \'{{itemName}}\'은(는) 디자인 타임에서는 제한적으로 지원됩니다.","dvchartAxis":{"category":"범주","value":"값"},"dvchartChartTitle":"차트 제목","dvchartColor":"색상 [{{index}}]","dvchartDetails":"세부정보 [{{index}}]","dvchartShape":"모양 [{{index}}]","overflowPlaceholder":"{{overflowedItemName}} 오버플로우 영역 표시"}},{"lng":"ko","ns":"chartWizard","resources":{"buttons":{"btnBack":"이전","btnCancel":"취소","btnFinish":"완료","btnNext":"다음"},"customization":{"labels":{"area":"영역","axisX":"X축","axisY":"Y축","footer":"푸터","header":"헤더","legend":"범례","plot":"그래프 설정"}},"labelSelectDataSet":"데이터 셋 선택","labelSelectPalette":"차트 팔레트 선택","settings":{"categories":{"category":"데이터 카테고리","subcategory":"데이터 하위 범주","values":"데이터 값"},"labels":{"aggregate":"집계","field":"필드","fieldClose":"필드 닫기","fieldEnd":"마지막 필드","fieldHigh":"하이 필드","fieldLow":"로두 필드","fieldOpen":"필드 열기","fieldSize":"필드 크기","fieldStart":"시작 필드","fieldX":"X 필드","fieldY":"Y 필드","fields":"필드","gaugeLabel":"게이지 라벨","gaugeRanges":"계기 범위","group":"분류 방법","lower":"Lower","pointer":"게이지 포인터","sortDirection":"정렬 방향","upper":"Upper"}},"templates":{"types":{"area":"영역","bar":"가로 막대","bubble":"버블","candlestick":"캔들","column":"세로 막대","doughnut":"도넛","funnel":"깔대기","gantt":"간트","gauge":"게이지","highLowClose":"고가-저가-종가","highLowOpenClose":"고가-저가-시가-종가","line":"선","pie":"파이","polarBar":"극좌표","polarColumn":"나선","pyramid":"피라미드","radarArea":"레이더 영역","radarBubble":"레이더 버블","radarLine":"레이더 선","radarScatter":"레이더 산점","rangeArea":"범위 영역","rangeBar":"범위 막대","rangeColumn":"범위","scatter":"산점도"}},"textAdvancedCustomization":"고급 사용자 정의","textCustom":"사용자 정의","textEmpty":"비어있음","textPreviewStep":"미리보기","textTypeStep":"데이터 및 유형","titleChartWizard":"차트 마법사","titlePreview":"미리보기","titleSelectType":"데이터 및 차트 유형 선택","titleSettings":"설정"}},{"lng":"ko","ns":"common","resources":{"btnCancel":"취소","btnOk":"OK","btnSave":"저장","textCollapse":"접기","textDelete":"삭제","textExpand":"펼치기","textOpen":"열기...","units":{"cm":{"textFullName":"센티미터","textShortName":"cm"},"in":{"textFullName":"인치","textShortName":"in"}}}},{"lng":"ko","ns":"components-RPX","resources":{"appBar":{"btnScript":"Script"},"dataTab":{"titleDeleteDataSource":"Delete Data Source","titleEditDataSource":"Edit Data Source...","titleMoveParameterDown":"Move Down","titleMoveParameterUp":"Move Up"},"menu":{"btnReportExplorer":"Explorer"},"propertyGrid":{"placeholderSearchBox":"enter property name here...","textAlphabetical":"Alphabetical","textCategorized":"Categorized","textMultipleTypes":"<여러 형식>","textSort":"Sort"},"scriptEditor":{"placeholder":"Enter {{ScriptLanguage}} code"},"stylesTab":{"textBasedOn":"Based on \\"{{parentName}}\\"","textRootStyle":"No Parent Style","titleAddStyle":"Add Style Based on \\"{{parentName}}\\"","titleDeleteStyle":"Delete Style"},"toolbar":{"home":{"backColor":"Back Color","fontFamily":"Font Family","fontSize":"Font Size","fontStyle":"Font Style Italic","fontWeight":"Font Weight Bold","foreColor":"Fore Color","textDecoration":"Text Decoration Underline","titleAlignCenter":"Text Align Center","titleAlignJustify":"Text Align Justify","titleAlignLeft":"Text Align Left","titleAlignRight":"Text Align Right","verticalAlignBottom":"Vertical Align Bottom","verticalAlignMiddle":"Vertical Align Middle","verticalAlignTop":"Vertical Align Top"},"script":{"events":{"report":{"textDataInitialize":"DataInitialize","textFetchData":"FetchData","textNoData":"NoData","textPageEnd":"PageEnd","textPageStart":"PageStart","textReportEnd":"ReportEnd","textReportStart":"ReportStart"},"section":{"textAfterPrint":"AfterPrint","textBeforePrint":"BeforePrint","textFormat":"Format"}},"textEvent":"Event","textObject":"Object","titleEvent":"Event","titleObject":"Object"}}}},{"lng":"ko","ns":"components","resources":{"appBar":{"btnFile":"파일","btnHome":"홈","btnInsert":"삽입","btnParameters":"매개변수","btnPreview":"미리보기","btnSaveAs":"다른 이름으로 저장","textUnsavedChanges":"저장되지 않은 변경 내용","titleNew":"새로 만들기","titleOpen":"열기","titleRedo":"다시 실행","titleSave":"저장","titleUndo":"실행 취소"},"chartPaletteDropdown":{"headingExtraPalettes":"테마 팔레트","headingStandardPalettes":"표준 팔레트"},"dataFieldPickerDropdown":{"semantic":{"noMatchingAttributesRelationsFound":"일치하는 항목을 찾을 수 없음","searchPlaceholder":"여기에 항목 이름 입력..."}},"dataPanel":{"commonValues":{"currentDateTime":"현재 날짜 및 시간","pageNM":"총 M 페이지 중 N","pageNMCumulative":"총 M 페이지 중 N (누적)","pageNMSection":" 총 M 페이지 중 N(섹션)","pageNofMLabel":"\\"총  \\" & {{totalPages}} & \\" 페이지 중 \\" & {{pageNumber}}","pageNumber":"페이지 번호","pageNumberCumulative":"페이지 번호(누적)","pageNumberSection":"페이지 번호(섹션)","reportFolder":"보고서 폴더","reportName":"보고서 이름","totalPages":"총 페이지 수","totalPagesCumulative":"총 페이지 수(누적)","totalPagesSection":"총 페이지 수(섹션)","userContext":"사용자 컨텍스트","userId":"사용자 ID","userLanguage":"사용자 언어"},"dataSets":{"placeholderEnterFieldName":"여기에 필드 이름 입력...","semantic":{"editDataSet":"데이터 집합 편집...","loading":"불러오는 중...","noMatchingAttributesRelationsFound":"일치하는 특성 또는 관계를 찾을 수 없음","searchPlaceholder":"여기서 특성 또는 관계 이름 입력..."},"textNoMatchingFieldsFound":"일치하는 필드를 찾을 수 없음"},"fieldVariations":{"Date":[{"format":"=Year({fieldExpression})","label":"년"},{"format":"=Year({fieldExpression}) & \\" Q\\" & DatePart(\\"q\\", {fieldExpression})","label":"년-분기"},{"format":"=Year({fieldExpression}) & \\" \\" & MonthName(Month({fieldExpression}))","label":"년-월"},{"format":"=Year({fieldExpression}) & \\" \\" & MonthName(Month({fieldExpression})) & \\" \\" & Day({fieldExpression})","label":"년-월-일"},{"format":"=Year({fieldExpression}) & \\" W\\" & DatePart(\\"ww\\", {fieldExpression})","label":"년-주"},{"format":"=DatePart(\\"q\\", {fieldExpression})","label":"분기"},{"format":"=Month({fieldExpression})","label":"월"},{"format":"=MonthName(Month({fieldExpression}))","label":"월이름"},{"format":"=DatePart(\\"ww\\", {fieldExpression})","label":"주"},{"format":"=Day({fieldExpression})","label":"일"},{"format":"=WeekdayName(Weekday({fieldExpression}))","label":"요일"}],"DateTime":[{"format":"=Year({fieldExpression})","label":"년"},{"format":"=Year({fieldExpression}) & \\" Q\\" & DatePart(\\"q\\", {fieldExpression})","label":"년-분기"},{"format":"=Year({fieldExpression}) & \\" \\" & MonthName(Month({fieldExpression}))","label":"년-월"},{"format":"=Year({fieldExpression}) & \\" \\" & MonthName(Month({fieldExpression})) & \\" \\" & Day({fieldExpression})","label":"년-월-일"},{"format":"=Year({fieldExpression}) & \\" W\\" & DatePart(\\"ww\\", {fieldExpression})","label":"년-주"},{"format":"=DatePart(\\"q\\", {fieldExpression})","label":"분기"},{"format":"=Month({fieldExpression})","label":"월"},{"format":"=MonthName(Month({fieldExpression}))","label":"월이름"},{"format":"=DatePart(\\"ww\\", {fieldExpression})","label":"주"},{"format":"=Day({fieldExpression})","label":"일"},{"format":"=WeekdayName(Weekday({fieldExpression}))","label":"요일"}]},"headingEditParameter":"매개변수 편집","semantic":{"textAttributesCount_0":"{{count}}개 특성","textNoAttributes":"특성 없음","textNoRelations":"관계 없음","textRelationsCount_0":"{{count}}개 관계"},"textBasedOnDataSource":"{{dataSourceName}} 기준","textFieldsCount_0":"{{count}}개 필드","textModelVersion":"버전: {{version}}","textSharedReference":"Shared Reference","titleAddDataSet":"데이터 집합 추가...","titleEditDataSet":"데이터 집합 편집...","titleEditDataSource":"데이터 소스 편집...","titleMore":"더 보기...","titleMoveParameterDown":"아래로 이동","titleMoveParameterUp":"위로 이동","titleSelectFields":"필드 선택..."},"fieldPicker":{"placeholderEnterFieldName":"여기에 필드 이름 입력..."},"layerList":{"btnAddLayer":"레이어 추가","titleDefaultLayerCannotBeDeleted":"기본 레이어는 삭제할 수 없습니다","titleDeleteLayer":"레이어 삭제","titleEditLayer":"레이어 편집","titleHideLayer":"레이어 숨기기","titleLockLayer":"레이어 잠금","titleShowLayer":"레이어 표시","titleUnlockLayer":"레이어 잠금 해제"},"libraries":{"textEmpty":"빈","textError":"오류","textNoLibraries":"사용 가능한 라이브러리가 없습니다","textNoReportParts":"라이브러리에 리포트 구성요소가 없습니다"},"menu":{"btnBack":"이전","btnClose":"닫기","btnGroupEditor":"그룹 편집기","btnLayerList":"레이어","btnLibrariesList":"라이브러리","btnReportExplorer":"탐색기","titleBack":"이전","titlePin":"고정"},"notifications":{"btnDismiss":"해제","btnDismissAll":"모두 해제","headingError_0":"{{count}} 오류","headingNotification_0":"{{count}} 알림","headingWarning_0":"{{count}} 경고","titleNotifications":"알림"},"propertyGrid":{"options":{"categories":"Categories","collapse":"Collapse All","expand":"Expand All","hideAdvancedProperty":"Hide Advanced Properties","showAdvancedProperty":"Show Advanced Properties"},"placeholderSearchBox":"여기에 속성 이름 입력...","textMultipleTypes":"<여러 형식>","textReportItems":"<보고서 항목>"},"statusBar":{"btnPropertiesMode":"속성 모드","btnShowAdvancedProperties":"고급 속성 표시","btnShowBasicProperties":"기본 속성 표시","common":{"textDisabled":"비활성화","textEnabled":"활성화"},"snapSettings":{"labelGridSize":"그리드 크기","labelSnapToGrid":"그리드에 맞춤","labelSnapToGuides":"안내선에 맞춤","titleSnapDisabled":"맞추기 비활성화","titleSnapEnabled":"맞추기 활성화"},"themePicker":{"themes":{"activeReports":"ActiveReports","activeReportsDark":"ActiveReports Dark","darkOled":"OLED 검정","default":"기본","defaultDark":"기본 검정","highContrast":"고대비","highContrastDark":"어두운 고대비","system":"시스템 테마"},"titleTheme":"테마"},"titleHideGrid":"그리드 숨기기","titleHideRulers":"눈금자 숨기기","titleShowGrid":"그리드 표시","titleShowRulers":"눈금자 표시","titleZoomIn":"확대","titleZoomOut":"축소"},"tabs":{"actions":{"textDuplicate":"복사","textHide":"숨기기","textInsert":"삽입","textMDelete":"숨기기","textMoveLeft":"왼쪽으로 이동","textMoveRight":"오른쪽으로 이동","textShow":"보이기"},"btnAddPage":"페이지 추가","btnAddSection":"섹션 추가","textPage":"페이지"},"themeEditor":{"labelNone":"<없음>"},"title":{"textUntitled":"제목 없음"},"toolbar":{"home":{"textExpression":"<표현식>","textExpressionCompact":"<𝑓>","titleAlignCenter":"텍스트 가운데 맞춤","titleAlignJustify":"텍스트 양쪽 맞춤","titleAlignLeft":"텍스트 왼쪽 맞춤","titleAlignRight":"텍스트 오른쪽 맞춤","titleBackgroundColor":"배경색","titleCopy":"복사","titleCut":"잘라내기","titleFontFamily":"글꼴 종류","titleFontSize":"글꼴 크기","titleFontStyle":"글꼴 스타일","titleFontWeight":"글꼴 두께","titlePaste":"붙여넣기","titleTextColor":"텍스트 색","titleTextDecoration":"텍스트 장식 밑줄","titleVerticalAlignBottom":"텍스트 세로 아래쪽 맞춤","titleVerticalAlignMiddle":"텍스트 세로 가운데 맞춤","titleVerticalAlignTop":"텍스트 세로 위쪽 맞춤"},"titleExpand":"Expand"}}},{"lng":"ko","ns":"contextActions-RPX","resources":{"labels":{"addGroupHeaderFooter":"Add Group Header/Footer","addPageHeaderFooter":"Add Page Header/Footer","addReportHeaderFooter":"Add Report Header/Footer","copy":"Copy","cut":"Cut","delete":"Delete","deletePageHeaderFooter":"Delete Page Header/Footer","deleteReportHeaderFooter":"Delete Report Header/Footer","layout":{"alignToBottoms":"하단 정렬","alignToCenters":"중간으로 정렬","alignToGrid":"그리드에 정렬","alignToLefts":"왼쪽으로 정렬","alignToMiddles":"중간 정렬","alignToRights":"오른쪽으로 정렬","alignToTops":"상단 정렬","bringToFront":"앞으로 가져오기","horizontal":{"decreaseSpacing":"수평 간격 감소","increaseSpacing":"수평 간격 증가","makeSpacingEqual":"동일한 수평간격 만들기","removeSpacing":"수평 간격 제거"},"makeSameHeight":"같은 높이로 만들기","makeSameSize":"같은 크기로 만들기","makeSameWidth":"같은 너비로 만들기","sendToBack":"뒤로보내기","separator":{"corelateControlSizing":"컨트롤 크기 동기화","horizontalAlignment":"수평 정렬","horizontalSpacing":"수평 간격","sortControls":"컨트롤 정렬","verticalAlignment":"수직 정렬","verticalSpacing":"수직 간격"},"sizeToGrid":"그리드 크기에 맞게 정렬","title":"레이아웃","vertical":{"decreaseSpacing":"수직 간격 감소","increaseSpacing":"수직 간격 증가","makeSpacingEqual":"동일한 수직 간격 만들기","removeSpacing":"수직 간격 제거"}},"pageFooter":"PageFooter","pageHeader":"PageHeader","paste":"Paste","report":"Report","reportFooter":"ReportFooter","reportHeader":"ReportHeader"}}},{"lng":"ko","ns":"contextActions","resources":{"bandedList":{"addFooter":"바닥글 추가","addGroupFooter":"그룹 바닥글 추가","addGroupHeader":"그룹 헤더 추가","addHeader":"머리글 추가","deleteGroup":"그룹 삭제","groupTitle":"그룹","insertGroup":"그룹 삽입","removeFooter":"바닥글 제거","removeGroupFooter":"그룹 바닥글 제거","removeGroupHeader":"그룹 헤더 제거","removeHeader":"머리글 제거","title":"밴드 목록"},"chart":"Chart","container":{"delete":"삭제","expression":"표현식"},"dashboard":{"duplicateSection":"섹션 복사","hideSection":"섹션 숨기기","moveSectionLeft":"왼쪽으로 섹션 이동","moveSectionRight":"오른쪽으로 섹션 이동","removeSection":"섹션 삭제","showSection":"섹션 보이기","switchTheme":"테마 전환","title":"대시보드"},"dvchart":{"palette":"차트 팔레트 설정","presetGroups":{"area":"영역형","bar":"가로 막대형","column":"세로 막대형","line":"선","misc":"기타","pie":"원형","polarBar":"극좌표형","polarColumn":"나선형","radar":"방사형","range":"범위"},"presets":{"area":"영역형","areaPercentStacked":"백분율 누적 영역형","areaStacked":"누적 영역형","bar":"가로 막대형","barPercentStacked":"백분율 누적 가로 막대형","barStacked":"누적 가로 막대형","bubble":"거품형","candlestick":"원통형","column":"세로 막대형","columnPercentStacked":"백분율 누적 세로 막대형","columnStacked":"누적 세로 막대형","doughnut":"도넛형","funnel":"깔때기형","gantt":"간트","gauge":"게이지","highLowClose":"고가 저가 종가","highLowOpenClose":"고가 저가 시가 종가","line":"꺾은선형","lineSmooth":"곡선형","pie":"원형","polarBar":"극좌표형","polarBarPercentStacked":"백분율 누적 극좌표형","polarBarStacked":"누적 극좌표형","polarColumn":"나선형","polarColumnPercentStacked":"백분율 누적 나선형","polarColumnStacked":"누적 나선형","pyramid":"피라미드형","radarArea":"방사 영역형","radarBubble":"방사 거품형","radarLine":"방사 꺾은선형","radarScatter":"방사 분산형","rangeArea":"범위 영역","rangeBar":"범위 막대","rangeColumn":"범위","scatter":"분산형","title":"그림 템플릿 설정"}},"report":{"addContinuousSection":"연속형 섹션 추가","addFixedPageSection":"고정형 페이지 섹션 추가","changeMasterReport":"마스터 리포트로 변환","convertToMasterReport":"마스터 리포트로 변환","deletePage":"페이지 삭제","duplicatePage":"페이지 복제","duplicateSection":"섹션 복사","hidePage":"페이지 숨기기","hideSection":"섹션 숨기기","insertPage":"페이지 삽입","insertSection":"섹션 삽입","layout":{"alignToBottoms":"하단 정렬","alignToCenters":"중간으로 정렬","alignToGrid":"그리드에 정렬","alignToLefts":"왼쪽으로 정렬","alignToMiddles":"중간 정렬","alignToRights":"오른쪽으로 정렬","alignToTops":"상단 정렬","bringToFront":"앞으로 가져오기","horizontal":{"decreaseSpacing":"수평 간격 감소","increaseSpacing":"수평 간격 증가","makeSpacingEqual":"동일한 수평간격 만들기","removeSpacing":"수평 간격 제거"},"horizontalAlignment":"수평 정렬","makeSameHeight":"같은 높이로 만들기","makeSameSize":"같은 크기로 만들기","makeSameWidth":"같은 너비로 만들기","sendToBack":"뒤로보내기","separator":{"corelateControlSizing":"컨트롤 크기 동기화","horizontalSpacing":"수평 간격","sortControls":"컨트롤 정렬","verticalSpacing":"수직 간격"},"sizeToGrid":"그리드 크기에 맞게 정렬","title":"레이아웃","vertical":{"decreaseSpacing":"수직 간격 감소","increaseSpacing":"수직 간격 증가","makeSpacingEqual":"동일한 수직 간격 만들기","removeSpacing":"수직 간격 제거"},"verticalAlignment":"수직 정렬"},"movePageBackward":"페이지 뒤로 이동","movePageForward":"페이지 앞으로 이동","moveSectionLeft":"왼쪽으로 섹션 이동","moveSectionRight":"오른쪽으로 섹션 이동","pages":"페이지","removeSection":"섹션 삭제","reportParts":{"title":"보고서 구성요소","titleCreateReportPart":"보고서 구성요소 만들기"},"setMasterReport":"마스터 리포트로 설정","showPage":"페이지 보이기","showSection":"섹션 보이기","switchTheme":"테마 전환","title":"보고서"},"reportSection":{"addFooter":"푸터 추가","addHeader":"헤더 추가","removeFooter":"푸터 제거","removeHeader":"헤더 제거","title":"섹션"},"table":{"addDetails":"세부 정보 추가","addFooter":"바닥글 추가","addGroupFooter":"그룹 바닥글 추가","addGroupHeader":"그룹 헤더 추가","addHeader":"머리글 추가","cellsTitle":"셀","columnTitle":"세로 막대형","deleteColumn":"열 삭제","deleteGroup":"그룹 삭제","deleteRow":"행 삭제","expression":"셀 식","groupTitle":"그룹","insertColumn":{"left":"왼쪽","right":"오른쪽"},"insertColumnTitle":"열 삽입","insertGroup":"그룹 삽입","insertRow":{"above":"초과","below":"미만"},"insertRowTitle":"행 삽입","mergeCells":"셀 병합","more":"더 보기...","removeDetails":"세부 정보 제거","removeFooter":"바닥글 제거","removeGroupFooter":"그룹 바닥글 제거","removeGroupHeader":"그룹 헤더 제거","removeHeader":"머리글 제거","rowTitle":"행","splitCells":"셀 분할","title":"테이블"},"tablix":{"addGroup":{"adjacentAfter":"뒤에 인접","adjacentBefore":"앞에 인접","child":"자식","parent":"부모"},"addGroupTitle":"그룹 추가","addTotal":{"contextMenuAfter":"뒤에 합계 추가","contextMenuBefore":"앞에 합계 추가"},"cellsTitle":"셀","columnGroup":"열 그룹","columnTitle":"열","delete":"삭제","deleteColumn":"열 삭제","deleteRow":"행 삭제","disableGroup":"그룹 사용 안 함","enableGroup":"그룹 사용","expression":"셀 식","insertColumn":{"insideGroupLeft":"내부 그룹 - 왼쪽","insideGroupRight":"내부 그룹 - 오른쪽","left":"왼쪽","outsideGroupLeft":"외부 그룹 - 왼쪽","outsideGroupRight":"외부 그룹 - 오른쪽","right":"오른쪽"},"insertColumnTitle":"열 삽입","insertRow":{"above":"초과","below":"미만","insideGroupAbove":"내부 그룹 - 위","insideGroupBelow":"내부 그룹 - 아래","outsideGroupAbove":"외부 그룹 - 위","outsideGroupBelow":"외부 그룹 - 아래"},"insertRowTitle":"행 삽입","mergeCells":"셀 병합","more":"더 보기...","rowGroup":"행 그룹","rowTitle":"행","splitCells":"셀 분할","totalTitle":"합계"}}},{"lng":"ko","ns":"defaults","resources":{"chart":{"innerRadius":0.5,"startAngle":0}}},{"lng":"ko","ns":"dialogs","resources":{"btnCancel":"취소","btnInsert":"삽입","common":{"textCancel":"취소"},"dataVisualizer":{"title":"데이터 시각화 도우미"},"expressionEditor":{"headingExpression":"식","headingFunctions":"함수","headingInfo":"정보","headingValues":"값","infoPanel":{"labelConstant":"상수:","labelDescription":"설명:","labelExample":"예:","labelName":"이름:","labelSyntax":"구문:"},"placeholderExpression":"식","search":{"placeholderSearch":"검색...","textNoResults":"\\"{{query}}\\"에 대한 결과가 없습니다.","textStartTyping":"결과를 보려면 입력을 시작하세요"},"subtitle":"표현식 편집기"},"headingInsertColumns":"열 삽입","headingInsertRows":"행 삽입","labelCount":"개수","labelPosition":"위치"}},{"lng":"ko","ns":"documentItems-RPX","resources":{"Barcode":"Barcode","CheckBox":"CheckBox","CrossSectionBox":"CrossSectionBox","CrossSectionLine":"CrossSectionLine","Detail":"Detail","GroupFooter":"Group Footer","GroupHeader":"Group Header","InputFieldCheckBox":"InputFieldCheckBox","InputFieldText":"InputFieldText","Label":"Label","Line":"Line","PageBreak":"PageBreak","PageFooter":"Page Footer","PageHeader":"Page Header","Picture":"Picture","Report":"Report","ReportFooter":"Report Footer","ReportHeader":"Report Header","ReportInfo":"ReportInfo","RichTextBox":"RichTextBox","Shape":"Shape","SubReport":"SubReport","TextBox":"TextBox","Unknown":"Unknown","captions":{"basicSupportReportItemInfo":"{{itemLabel}} \'{{itemName}}\' has limited support in design-time."}}},{"lng":"ko","ns":"documentsAPI","resources":{"romLabels":{"chart":"Chart","dvchart":"DV.Chart","matrix":"Matrix","table":"Table","tablix":"Tablix"},"textOpenDocumentWarnings":"Document opened with warnings","textRenamedItemPrefix":"Content_{{originalName}}","textReportConversion":"There have been issues while opening this report","transform":{"helpLink":"Please find more information on report items transformation at {{link}}.","textBadReportItem":"✘ {{SourceType}} \\"{{Name}}\\" couldn\'t have been transformed to {{ResultType}} due to internal issues:","textError":"– [{{ErrorType}}] {{Message}}","textReport":"The report \\"{{reportName}}\\" has been transformed.","textReportItem":"✔ {{SourceType}} \\"{{Name}}\\" has been transformed to {{ResultType}}."}}},{"lng":"ko","ns":"enums-RPX","resources":{"background_style":{"Gradient":"Gradient","Pattern":"Pattern","Solid":"Solid"},"barcode_caption_position":{"Above":"Above","Below":"Below","None":"None"},"barcode_rotation":{"None":"None","Rotate180Degrees":"Rotate180Degrees","Rotate270Degrees":"Rotate270Degrees","Rotate90Degrees":"Rotate90Degrees"},"barcode_style":{"Ansi39":"Ansi39","Ansi39x":"Ansi39x","Aztec":"Aztec","BC412":"BC412","Codabar":"Codabar","Code25intlv":"Code25intlv","Code25mat":"Code25mat","Code39":"Code39","Code39x":"Code39x","Code49":"Code49","Code93x":"Code93x","Code_11":"Code_11","Code_128_A":"Code_128_A","Code_128_B":"Code_128_B","Code_128_C":"Code_128_C","Code_128auto":"Code_128auto","Code_2_of_5":"Code_2_of_5","Code_93":"Code_93","DataMatrix":"DataMatrix","EAN128FNC1":"EAN128FNC1","EAN_13":"EAN_13","EAN_8":"EAN_8","GS1DataMatrix":"GS1DataMatrix","GS1QRCode":"GS1QRCode","HIBCCode128":"HIBCCode128","HIBCCode39":"HIBCCode39","IATA_2_of_5":"IATA_2_of_5","ISBN":"ISBN","ISMN":"ISMN","ISSN":"ISSN","ITF14":"ITF14","IntelligentMail":"IntelligentMail","IntelligentMailPackage":"IntelligentMailPackage","JapanesePostal":"JapanesePostal","MSI":"MSI","Matrix_2_of_5":"Matrix_2_of_5","MaxiCode":"MaxiCode","MicroPDF417":"MicroPDF417","MicroQRCode":"MicroQRCode","None":"None","PZN":"PZN","Pdf417":"Pdf417","Pharmacode":"Pharmacode","Plessey":"Plessey","PostNet":"PostNet","QRCode":"QRCode","RM4SCC":"RM4SCC","RSS14":"RSS14","RSS14Stacked":"RSS14Stacked","RSS14StackedOmnidirectional":"RSS14StackedOmnidirectional","RSS14Truncated":"RSS14Truncated","RSSExpanded":"RSSExpanded","RSSExpandedStacked":"RSSExpandedStacked","RSSLimited":"RSSLimited","SSCC_18":"SSCC_18","Telepen":"Telepen","UCCEAN128":"UCCEAN128","UPC_A":"UPC_A","UPC_E0":"UPC_E0","UPC_E1":"UPC_E1"},"border_style":{"Dash":"Dash","DashDot":"DashDot","Dot":"Dot","Double":"Double","ExtraThickSolid":"ExtraThickSolid","None":"None","Solid":"Solid","ThickDash":"ThickDash","ThickDashDot":"ThickDashDot","ThickDashDotDot":"ThickDashDotDot","ThickDot":"ThickDot","ThickDouble":"ThickDouble","ThickSolid":"ThickSolid"},"border_style_inputfield":{"Dashed":"Dashed","Inset":"Inset","None":"None","Solid":"Solid"},"calculated_field_type":{"Boolean":"Boolean","Date":"Date","Double":"Double","Float":"Float","Int32":"Int32","None":"None","String":"String"},"calendar":{"Gregorian":"Gregorian","GregorianArabic":"Gregorian Arabic","GregorianMiddleEastFrench":"Gregorian Middle East French","GregorianTransliteratedEnglish":"Gregorian Transliterated English","GregorianTransliteratedFrench":"Gregorian Transliterated French","GregorianUSEnglish":"Gregorian US English","Hebrew":"Hebrew","Hijri":"Hijri","Japanese":"Japanese","Korea":"Korea","Taiwan":"Taiwan","ThaiBuddhist":"Thai Buddhist"},"check_style":{"Check":"Check","Circle":"Circle","Cross":"Cross","Diamond":"Diamond","Square":"Square","Star":"Star"},"collate":{"Collate":"Collate","Default":"Default","DontCollate":"DontCollate"},"column_direction":{"AcrossDown":"AcrossDown","DownAcross":"DownAcross"},"compatibility_mode":{"CrossPlatform":"CrossPlatform","GDI":"GDI"},"content_alignment":{"BottomCenter":"BottomCenter","BottomLeft":"BottomLeft","BottomRight":"BottomRight","MiddleCenter":"MiddleCenter","MiddleLeft":"MiddleLeft","MiddleRight":"MiddleRight","TopCenter":"TopCenter","TopLeft":"TopLeft","TopRight":"TopRight"},"culture":{"af-ZA":"Afrikaans (South Africa)","ar-AE":"Arabic (U.A.E.)","ar-BH":"Arabic (Bahrain)","ar-DZ":"Arabic (Algeria)","ar-EG":"Arabic (Egypt)","ar-IQ":"Arabic (Iraq)","ar-JO":"Arabic (Jordan)","ar-KW":"Arabic (Kuwait)","ar-LB":"Arabic (Lebanon)","ar-LY":"Arabic (Libya)","ar-MA":"Arabic (Morocco)","ar-OM":"Arabic (Oman)","ar-QA":"Arabic (Qatar)","ar-SA":"Arabic (Saudi Arabia)","ar-SY":"Arabic (Syria)","ar-TN":"Arabic (Tunisia)","ar-YE":"Arabic (Yemen)","az-Cyrl-AZ":"Azeri (Cyrillic, Azerbaijan)","az-Latn-AZ":"Azeri (Latin, Azerbaijan)","be-BY":"Belarusian (Belarus)","bg-BG":"Bulgarian (Bulgaria)","ca-ES":"Catalan (Catalan)","cs-CZ":"Czech (Czech Republic)","da-DK":"Danish (Denmark)","de-AT":"German (Austria)","de-CH":"German (Switzerland)","de-DE":"German (Germany)","de-LI":"German (Liechtenstein)","de-LU":"German (Luxembourg)","dv-MV":"Divehi (Maldives)","el-GR":"Greek (Greece)","en-029":"English (Caribbean)","en-AU":"English (Australia)","en-BZ":"English (Belize)","en-CA":"English (Canada)","en-GB":"English (United Kingdom)","en-IE":"English (Ireland)","en-JM":"English (Jamaica)","en-NZ":"English (New Zealand)","en-PH":"English (Republic of the Philippines)","en-TT":"English (Trinidad and Tobago)","en-US":"English (United States)","en-ZA":"English (South Africa)","en-ZW":"English (Zimbabwe)","es-AR":"Spanish (Argentina)","es-BO":"Spanish (Bolivia)","es-CL":"Spanish (Chile)","es-CO":"Spanish (Colombia)","es-CR":"Spanish (Costa Rica)","es-DO":"Spanish (Dominican Republic)","es-EC":"Spanish (Ecuador)","es-ES":"Spanish (Spain)","es-GT":"Spanish (Guatemala)","es-HN":"Spanish (Honduras)","es-MX":"Spanish (Mexico)","es-NI":"Spanish (Nicaragua)","es-PA":"Spanish (Panama)","es-PE":"Spanish (Peru)","es-PR":"Spanish (Puerto Rico)","es-PY":"Spanish (Paraguay)","es-SV":"Spanish (El Salvador)","es-UY":"Spanish (Uruguay)","es-VE":"Spanish (Venezuela)","et-EE":"Estonian (Estonia)","eu-ES":"Basque (Basque)","fa-IR":"Persian (Iran)","fi-FI":"Finnish (Finland)","fo-FO":"Faroese (Faroe Islands)","fr-BE":"French (Belgium)","fr-CA":"French (Canada)","fr-CH":"French (Switzerland)","fr-FR":"French (France)","fr-LU":"French (Luxembourg)","fr-MC":"French (Principality of Monaco)","gl-ES":"Galician (Galician)","gu-IN":"Gujarati (India)","he-IL":"Hebrew (Israel)","hi-IN":"Hindi (India)","hr-BA":"Croatian (Bosnia and Herzegovina)","hr-HR":"Croatian (Croatia)","hu-HU":"Hungarian (Hungary)","hy-AM":"Armenian (Armenia)","id-ID":"Indonesian (Indonesia)","is-IS":"Icelandic (Iceland)","it-CH":"Italian (Switzerland)","it-IT":"Italian (Italy)","ja-JP":"Japanese (Japan)","ka-GE":"Georgian (Georgia)","kk-KZ":"Kazakh (Kazakhstan)","kn-IN":"Kannada (India)","ko-KR":"Korean (Korea)","kok-IN":"Konkani (India)","ky-KG":"Kyrgyz (Kyrgyzstan)","lt-LT":"Lithuanian (Lithuania)","lv-LV":"Latvian (Latvia)","mk-MK":"Macedonian (Former Yugoslav Republic of Macedonia)","mn-MN":"Mongolian (Cyrillic, Mongolia)","mr-IN":"Marathi (India)","ms-BN":"Malay (Brunei Darussalam)","ms-MY":"Malay (Malaysia)","nb-NO":"Norwegian, BokmÃ¥l (Norway)","nl-BE":"Dutch (Belgium)","nl-NL":"Dutch (Netherlands)","nn-NO":"Norwegian, Nynorsk (Norway)","pa-IN":"Punjabi (India)","pl-PL":"Polish (Poland)","pt-BR":"Portuguese (Brazil)","pt-PT":"Portuguese (Portugal)","ro-RO":"Romanian (Romania)","ru-RU":"Russian (Russia)","sa-IN":"Sanskrit (India)","sk-SK":"Slovak (Slovakia)","sl-SI":"Slovenian (Slovenia)","sq-AL":"Albanian (Albania)","sv-FI":"Swedish (Finland)","sv-SE":"Swedish (Sweden)","sw-KE":"Kiswahili (Kenya)","syr-SY":"Syriac (Syria)","ta-IN":"Tamil (India)","te-IN":"Telugu (India)","th-TH":"Thai (Thailand)","tr-TR":"Turkish (Turkey)","tt-RU":"Tatar (Russia)","uk-UA":"Ukrainian (Ukraine)","ur-PK":"Urdu (Islamic Republic of Pakistan)","uz-Cyrl-UZ":"Uzbek (Cyrillic, Uzbekistan)","uz-Latn-UZ":"Uzbek (Latin, Uzbekistan)","vi-VN":"Vietnamese (Vietnam)","zh-CN":"Chinese (People\'s Republic of China)","zh-HK":"Chinese (Hong Kong S.A.R.)","zh-MO":"Chinese (Macao S.A.R.)","zh-SG":"Chinese (Singapore)","zh-TW":"Chinese (Taiwan)"},"ddo_char_set":{"Arab":"Arab","Baltic":"Baltic","CentralEuropean":"Central European","Cyrillic":"Cyrillic","Greek":"Greek","Hebrew":"Hebrew","Turkish":"Turkish","Vietnamese":"Vietnamese","Western":"Western"},"duplex":{"Default":"Default","Horizontal":"Horizontal","Simplex":"Simplex","Vertical":"Vertical"},"field_type":{"Boolean":"Boolean","Date":"Date","Double":"Double","Float":"Float","Int32":"Int32","Integer":"Integer","None":"None","String":"String"},"font_size":{"10pt":"10pt","11pt":"11pt","12pt":"12pt","14pt":"14pt","16pt":"16pt","18pt":"18pt","20pt":"20pt","22pt":"22pt","24pt":"24pt","26pt":"26pt","28pt":"28pt","36pt":"36pt","48pt":"48pt","72pt":"72pt","8pt":"8pt","9pt":"9pt"},"font_style":{"Italic":"Italic","Normal":"Normal"},"font_weight":{"Bold":"Bold","Normal":"Normal"},"format_string":{"Page_PageNumber_of_PageCount":"","Page_PageNumber_of_PageCount_on_RunDateTime":"Page {PageNumber} of {PageCount} on {RunDateTime}","RunDateTime":"{RunDateTime:}","RunDateTime_MMMM_d_yyyy":"{RunDateTime:MMMM d, yyyy}","RunDateTime_MMMM_yy":"{RunDateTime:MMMM-yy}","RunDateTime_MMMM_yyyy":"{RunDateTime:MMMM-yyyy}","RunDateTime_MMM_yy":"{RunDateTime:MMM-yy}","RunDateTime_MMM_yyyy":"{RunDateTime:MMM-yyyy}","RunDateTime_MM_dd_yy":"{RunDateTime:MM/dd/yy}","RunDateTime_MM_dd_yyyy":"{RunDateTime:MM/dd/yyyy}","RunDateTime_M_d":"{RunDateTime:M/d}","RunDateTime_M_d_yy":"{RunDateTime:M/d/yy}","RunDateTime_M_d_yy_h_mm":"{RunDateTime:M/d/yy h:mm}","RunDateTime_M_d_yy_h_mm_tt":"{RunDateTime:M/d/yy h:mm tt}","RunDateTime_M_d_yyyy":"{RunDateTime:M/d/yyyy}","RunDateTime_M_d_yyyy_h_mm":"{RunDateTime:M/d/yyyy h:mm}","RunDateTime_M_d_yyyy_h_mm_tt":"{RunDateTime:M/d/yyyy h:mm tt}","RunDateTime_M_d_yyyy}":"{RunDateTime:M/d/yyyy}","RunDateTime_d_MMM":"{RunDateTime:d-MMM}","RunDateTime_d_MMM_yy":"{RunDateTime:d-MMM-yy}","RunDateTime_d_MMM_yyyy":"{RunDateTime:d-MMM-yyyy}","RunDateTime_dd_MMM_yy":"{RunDateTime:dd-MMM-yy}","RunDateTime_dd_MMM_yyyy":"{RunDateTime:dd-MMM-yyyy}"},"gradient_style":{"DiagonalDown":"DiagonalDown","DiagonalUp":"DiagonalUp","FromCenter":"FromCenter","FromCorner":"FromCorner","Horizontal":"Horizontal","Vertical":"Vertical"},"group_keep_together":{"All":"All","FirstDetail":"FirstDetail","None":"None"},"hatch_style":{"BackwardDiagonal":"BackwardDiagonal","DarkDownwardDiagonal":"DarkDownwardDiagonal","DarkHorizontal":"DarkHorizontal","DarkUpwardDiagonal":"DarkUpwardDiagonal","DarkVertical":"DarkVertical","DashedDownwardDiagonal":"DashedDownwardDiagonal","DashedHorizontal":"DashedHorizontal","DashedUpwardDiagonal":"DashedUpwardDiagonal","DashedVertical":"DashedVertical","DiagonalBrick":"DiagonalBrick","DiagonalCross":"DiagonalCross","Divot":"Divot","DottedDiamond":"DottedDiamond","DottedGrid":"DottedGrid","ForwardDiagonal":"ForwardDiagonal","Horizontal":"Horizontal","HorizontalBrick":"HorizontalBrick","LargeCheckerBoard":"LargeCheckerBoard","LargeConfetti":"LargeConfetti","LargeGrid":"LargeGrid","LightDownwardDiagonal":"LightDownwardDiagonal","LightHorizontal":"LightHorizontal","LightUpwardDiagonal":"LightUpwardDiagonal","LightVertical":"LightVertical","NarrowHorizontal":"NarrowHorizontal","NarrowVertical":"NarrowVertical","OutlinedDiamond":"OutlinedDiamond","Percent05":"Percent05","Percent10":"Percent10","Percent20":"Percent20","Percent25":"Percent25","Percent30":"Percent30","Percent40":"Percent40","Percent50":"Percent50","Percent60":"Percent60","Percent70":"Percent70","Percent75":"Percent75","Percent80":"Percent80","Percent90":"Percent90","Plaid":"Plaid","Shingle":"Shingle","SmallCheckerBoard":"SmallCheckerBoard","SmallConfetti":"SmallConfetti","SmallGrid":"SmallGrid","SolidDiamond":"SolidDiamond","Sphere":"Sphere","Trellis":"Trellis","Vertical":"Vertical","Wave":"Wave","Weave":"Weave","WideDownwardDiagonal":"WideDownwardDiagonal","WideUpwardDiagonal":"WideUpwardDiagonal","ZigZag":"ZigZag"},"kinsoku":{"Auto":"Auto","None":"None","True":"True"},"line_style":{"Dash":"Dash","DashDot":"DashDot","DashDotDot":"DashDotDot","Dot":"Dot","Double":"Double","Solid":"Solid","Transparent":"Transparent"},"mime_type":{"image/bmp":"image/bmp","image/gif":"image/gif","image/jpeg":"image/jpeg","image/png":"image/png","image/x-emf":"image/x-emf","image/x-wmf":"image/x-wmf"},"new_column":{"After":"After","Before":"Before","BeforeAfter":"BeforeAfter","None":"None"},"new_page":{"After":"After","Before":"Before","BeforeAfter":"BeforeAfter","None":"None"},"page_orientation":{"Default":"Default","Landscape":"Landscape","Portrait":"Portrait"},"paper_kind":{"A2":"A2","A3":"A3","A3Extra":"A3Extra","A3ExtraTransverse":"A3ExtraTransverse","A3Rotated":"A3Rotated","A3Transverse":"A3Transverse","A4":"A4","A4Extra":"A4Extra","A4Plus":"A4Plus","A4Rotated":"A4Rotated","A4Small":"A4Small","A4Transverse":"A4Transverse","A5":"A5","A5Extra":"A5Extra","A5Rotated":"A5Rotated","A5Transverse":"A5Transverse","A6":"A6","A6Rotated":"A6Rotated","APlus":"APlus","B4":"B4","B4Envelope":"B4Envelope","B4JisRotated":"B4JisRotated","B5":"B5","B5Envelope":"B5Envelope","B5Extra":"B5Extra","B5JisRotated":"B5JisRotated","B5Transverse":"B5Transverse","B6Envelope":"B6Envelope","B6Jis":"B6Jis","B6JisRotated":"B6JisRotated","BPlus":"BPlus","C3Envelope":"C3Envelope","C4Envelope":"C4Envelope","C5Envelope":"C5Envelope","C65Envelope":"C65Envelope","C6Envelope":"C6Envelope","CSheet":"CSheet","Custom":"Custom","DLEnvelope":"DLEnvelope","DSheet":"DSheet","ESheet":"ESheet","Executive":"Executive","Folio":"Folio","GermanLegalFanfold":"GermanLegalFanfold","GermanStandardFanfold":"GermanStandardFanfold","InviteEnvelope":"InviteEnvelope","IsoB4":"IsoB4","ItalyEnvelope":"ItalyEnvelope","JapaneseDoublePostcard":"JapaneseDoublePostcard","JapaneseDoublePostcardRotated":"JapaneseDoublePostcardRotated","JapaneseEnvelopeChouNumber3":"JapaneseEnvelopeChouNumber3","JapaneseEnvelopeChouNumber3Rotated":"JapaneseEnvelopeChouNumber3Rotated","JapaneseEnvelopeChouNumber4":"JapaneseEnvelopeChouNumber4","JapaneseEnvelopeChouNumber4Rotated":"JapaneseEnvelopeChouNumber4Rotated","JapaneseEnvelopeKakuNumber2":"JapaneseEnvelopeKakuNumber2","JapaneseEnvelopeKakuNumber2Rotated":"JapaneseEnvelopeKakuNumber2Rotated","JapaneseEnvelopeKakuNumber3":"JapaneseEnvelopeKakuNumber3","JapaneseEnvelopeKakuNumber3Rotated":"JapaneseEnvelopeKakuNumber3Rotated","JapaneseEnvelopeYouNumber4":"JapaneseEnvelopeYouNumber4","JapaneseEnvelopeYouNumber4Rotated":"JapaneseEnvelopeYouNumber4Rotated","JapanesePostcard":"JapanesePostcard","JapanesePostcardRotated":"JapanesePostcardRotated","Ledger":"Ledger","Legal":"Legal","LegalExtra":"LegalExtra","Letter":"Letter","LetterExtra":"LetterExtra","LetterExtraTransverse":"LetterExtraTransverse","LetterPlus":"LetterPlus","LetterRotated":"LetterRotated","LetterSmall":"LetterSmall","LetterTransverse":"LetterTransverse","MonarchEnvelope":"MonarchEnvelope","Note":"Note","Number10Envelope":"Number10Envelope","Number11Envelope":"Number11Envelope","Number12Envelope":"Number12Envelope","Number14Envelope":"Number14Envelope","Number9Envelope":"Number9Envelope","PersonalEnvelope":"PersonalEnvelope","Prc16K":"Prc16K","Prc16KRotated":"Prc16KRotated","Prc32K":"Prc32K","Prc32KBig":"Prc32KBig","Prc32KBigRotated":"Prc32KBigRotated","Prc32KRotated":"Prc32KRotated","PrcEnvelopeNumber1":"PrcEnvelopeNumber1","PrcEnvelopeNumber10":"PrcEnvelopeNumber10","PrcEnvelopeNumber10Rotated":"PrcEnvelopeNumber10Rotated","PrcEnvelopeNumber1Rotated":"PrcEnvelopeNumber1Rotated","PrcEnvelopeNumber2":"PrcEnvelopeNumber2","PrcEnvelopeNumber2Rotated":"PrcEnvelopeNumber2Rotated","PrcEnvelopeNumber3":"PrcEnvelopeNumber3","PrcEnvelopeNumber3Rotated":"PrcEnvelopeNumber3Rotated","PrcEnvelopeNumber4":"PrcEnvelopeNumber4","PrcEnvelopeNumber4Rotated":"PrcEnvelopeNumber4Rotated","PrcEnvelopeNumber5":"PrcEnvelopeNumber5","PrcEnvelopeNumber5Rotated":"PrcEnvelopeNumber5Rotated","PrcEnvelopeNumber6":"PrcEnvelopeNumber6","PrcEnvelopeNumber6Rotated":"PrcEnvelopeNumber6Rotated","PrcEnvelopeNumber7":"PrcEnvelopeNumber7","PrcEnvelopeNumber7Rotated":"PrcEnvelopeNumber7Rotated","PrcEnvelopeNumber8":"PrcEnvelopeNumber8","PrcEnvelopeNumber8Rotated":"PrcEnvelopeNumber8Rotated","PrcEnvelopeNumber9":"PrcEnvelopeNumber9","PrcEnvelopeNumber9Rotated":"PrcEnvelopeNumber9Rotated","Quarto":"Quarto","Standard10x11":"Standard10x11","Standard10x14":"Standard10x14","Standard11x17":"Standard11x17","Standard12x11":"Standard12x11","Standard15x11":"Standard15x11","Standard9x11":"Standard9x11","Statement":"Statement","Tabloid":"Tabloid","TabloidExtra":"TabloidExtra","USStandardFanfold":"USStandardFanfold"},"paper_size":{"A3":"A3","A4":"A4","A5":"A5","A6":"A6","Custom":"Custom","Executive":"Executive","ISOB5":"B5 (ISO)","JISB4":"B4 (JIS)","JISB5":"B5 (JIS)","JISB6":"B6 (JIS)","Legal":"Legal","Letter":"Letter","Tabloid":"Tabloid"},"paper_source":{"AutomaticFeed":"Automatic Feed","Cassette":"Cassette","Manual":"Manual"},"paper_source_kind":{"AutomaticFeed":"AutomaticFeed","Cassette":"Cassette","Custom":"Custom","Envelope":"Envelope","FormSource":"FormSource","LargeCapacity":"LargeCapacity","LargeFormat":"LargeFormat","Lower":"Lower","Manual":"Manual","ManualFeed":"ManualFeed","Middle":"Middle","SmallFormat":"SmallFormat","TractorFeed":"TractorFeed","Upper":"Upper"},"parameter_type":{"Boolean":"Boolean","Date":"Date","String":"String"},"picture_alignment":{"BottomLeft":"BottomLeft","BottomRight":"BottomRight","Center":"Center","TopLeft":"TopLeft","TopRight":"TopRight"},"repeat_style":{"All":"All","None":"None","OnColumn":"OnColumn","OnPage":"OnPage","OnPageIncludeNoDetail":"OnPageIncludeNoDetail"},"richrext_rendering_type":{"Metafile":"Metafile","PNG":"PNG","RTF":"RTF","TransparentPNG":"TransparentPNG"},"script_language":{"C#":"C#","VBNET":"VB.NET"},"shape_style":{"Ellipse":"Ellipse","Rectangle":"Rectangle","RoundRect":"RoundRect"},"size_mode":{"Clip":"Clip","Stretch":"Stretch","Zoom":"Zoom"},"string_alignment":{"Center":"Center","Far":"Far","Near":"Near"},"string_alignment_inputfield":{"Center":"Center","Left":"Left","Right":"Right"},"summary_func":{"Avg":"Avg","Count":"Count","DAvg":"DAvg","DCount":"DCount","DStdDev":"DStdDev","DStdDevP":"DStdDevP","DSum":"DSum","DVar":"DVar","DVarP":"DVarP","Max":"Max","Min":"Min","StdDev":"StdDev","StdDevP":"StdDevP","Sum":"Sum","Var":"Var","VarP":"VarP"},"summary_running":{"All":"All","Group":"Group","None":"None"},"summary_type":{"GrandTotal":"GrandTotal","None":"None","PageCount":"PageCount","PageTotal":"PageTotal","SubTotal":"SubTotal"},"text_align":{"Center":"Center","Justify":"Justify","Left":"Left","Right":"Right"},"text_justify":{"Auto":"Auto","Distribute":"Distribute","Distribute_all_lines":"DistributeAllLines"},"vertical_align":{"Bottom":"Bottom","Middle":"Middle","Top":"Top"},"white_space":{"NoWrap":"NoWrap","Normal":"Normal","Pre":"Pre"},"word_wrap":{"CharWrap":"CharWrap","NoWrap":"NoWrap","WordWrap":"WordWrap"}}},{"lng":"ko","ns":"enums","resources":{"action":{"ApplyParameters":"적용 매개 변수","BookmarkLink":"책갈피로 이동","Drillthrough":"보고서로 이동","Hyperlink":"URL로 이동","None":"없음"},"action_apply_value_cmd":{"Reset":"Reset","Set":"Set","Toggle":"Toggle"},"auto_merge_mode":{"Always":"항상","Never":"안 함","Restricted":"제한됨"},"auto_width":{"None":"없음","Proportional":"비례"},"axis_location":{"Left":"왼쪽","Right":"오른쪽"},"axis_mode":{"Cartesian":"카티전","Polygonal":"다각형","Radial":"방사형"},"barcode_caption_location":{"Above":"초과","Below":"미만","None":"없음"},"barcode_ecc000_140_symbol_size":{"Auto":"Auto","Square11":"Square11","Square13":"Square13","Square15":"Square15","Square17":"Square17","Square19":"Square19","Square21":"Square21","Square23":"Square23","Square25":"Square25","Square27":"Square27","Square29":"Square29","Square31":"Square31","Square33":"Square33","Square35":"Square35","Square37":"Square37","Square39":"Square39","Square41":"Square41","Square43":"Square43","Square45":"Square45","Square47":"Square47","Square49":"Square49","Square9":"Square9"},"barcode_ecc200_encoding_mode":{"ASCII":"ASCII","Auto":"Auto","Base256":"Base256","C40":"C40","EDIFACT":"EDIFACT","Text":"Text","X12":"X12"},"barcode_ecc200_symbol_size":{"Rectangular12x26":"Rectangular12x26","Rectangular12x36":"Rectangular12x36","Rectangular16x36":"Rectangular16x36","Rectangular16x48":"Rectangular16x48","Rectangular8x18":"Rectangular8x18","Rectangular8x32":"Rectangular8x32","RectangularAuto":"RectangularAuto","Square10":"Square10","Square104":"Square104","Square12":"Square12","Square120":"Square120","Square132":"Square132","Square14":"Square14","Square144":"Square144","Square16":"Square16","Square18":"Square18","Square20":"Square20","Square22":"Square22","Square24":"Square24","Square26":"Square26","Square32":"Square32","Square36":"Square36","Square40":"Square40","Square44":"Square44","Square48":"Square48","Square52":"Square52","Square64":"Square64","Square72":"Square72","Square80":"Square80","Square88":"Square88","Square96":"Square96","SquareAuto":"SquareAuto"},"barcode_ecc_mode":{"ECC000":"ECC000","ECC050":"ECC050","ECC080":"ECC080","ECC100":"ECC100","ECC140":"ECC140","ECC200":"ECC200"},"barcode_encoding":{"37":"IBM EBCDIC (US-Canada) 37","437":"OEM United States 437","500":"IBM EBCDIC (International) 500","708":"Arabic (ASMO 708) 708","720":"Arabic (DOS) 720","737":"Greek (DOS) 737","775":"Baltic (DOS) 775","850":"Western European (DOS) 850","852":"Central European (DOS) 852","855":"OEM Cyrillic 855","857":"Turkish (DOS) 857","858":"OEM Multilingual Latin I 858","860":"Portuguese (DOS) 860","861":"Icelandic (DOS) 861","862":"Hebrew (DOS) 862","863":"French Canadian (DOS) 863","864":"Arabic (864) 864","865":"Nordic (DOS) 865","866":"Cyrillic (DOS) 866","869":"Greek, Modern (DOS) 869","870":"IBM EBCDIC (Multilingual Latin-2) 870","874":"Thai (Windows) 874","875":"IBM EBCDIC (Greek Modern) 875","932":"Japanese (Shift-JIS) 932","936":"Chinese Simplified (GB2312) 936","949":"Korean 949","950":"Chinese Traditional (Big5) 950","1026":"IBM EBCDIC (Turkish Latin-5) 1026","1047":"IBM Latin-1 1047","1140":"IBM EBCDIC (US-Canada-Euro) 1140","1141":"IBM EBCDIC (Germany-Euro) 1141","1142":"IBM EBCDIC (Denmark-Norway-Euro) 1142","1143":"IBM EBCDIC (Finland-Sweden-Euro) 1143","1144":"IBM EBCDIC (Italy-Euro) 1144","1145":"IBM EBCDIC (Spain-Euro) 1145","1146":"IBM EBCDIC (UK-Euro) 1146","1147":"IBM EBCDIC (France-Euro) 1147","1148":"IBM EBCDIC (International-Euro) 1148","1149":"IBM EBCDIC (Icelandic-Euro) 1149","1200":"Unicode 1200","1201":"Unicode (Big-Endian) 1201","1250":"Central European (Windows) 1250","1251":"Cyrillic (Windows) 1251","1252":"Western European (Windows) 1252","1253":"Greek (Windows) 1253","1254":"Turkish (Windows) 1254","1255":"Hebrew (Windows) 1255","1256":"Arabic (Windows) 1256","1257":"Baltic (Windows) 1257","1258":"Vietnamese (Windows) 1258","1361":"Korean (Johab) 1361","10000":"Western European (Mac) 10000","10001":"Japanese (Mac) 10001","10002":"Chinese Traditional (Mac) 10002","10003":"Korean (Mac) 10003","10004":"Arabic (Mac) 10004","10005":"Hebrew (Mac) 10005","10006":"Greek (Mac) 10006","10007":"Cyrillic (Mac) 10007","10008":"Chinese Simplified (Mac) 10008","10010":"Romanian (Mac) 10010","10017":"Ukrainian (Mac) 10017","10021":"Thai (Mac) 10021","10029":"Central European (Mac) 10029","10079":"Icelandic (Mac) 10079","10081":"Turkish (Mac) 10081","10082":"Croatian (Mac) 10082","12000":"Unicode (UTF-32) 12000","12001":"Unicode (UTF-32 Big-Endian) 12001","20000":"Chinese Traditional (CNS) 20000","20001":"TCA Taiwan 20001","20002":"Chinese Traditional (Eten) 20002","20003":"IBM5550 Taiwan 20003","20004":"TeleText Taiwan 20004","20005":"Wang Taiwan 20005","20105":"Western European (IA5) 20105","20106":"German (IA5) 20106","20107":"Swedish (IA5) 20107","20108":"Norwegian (IA5) 20108","20127":"US-ASCII 20127","20261":"T.61 20261","20269":"ISO-6937 20269","20273":"IBM EBCDIC (Germany) 20273","20277":"IBM EBCDIC (Denmark-Norway) 20277","20278":"IBM EBCDIC (Finland-Sweden) 20278","20280":"IBM EBCDIC (Italy) 20280","20284":"IBM EBCDIC (Spain) 20284","20285":"IBM EBCDIC (UK) 20285","20290":"IBM EBCDIC (Japanese katakana) 20290","20297":"IBM EBCDIC (France) 20297","20420":"IBM EBCDIC (Arabic) 20420","20423":"IBM EBCDIC (Greek) 20423","20424":"IBM EBCDIC (Hebrew) 20424","20833":"IBM EBCDIC (Korean Extended) 20833","20838":"IBM EBCDIC (Thai) 20838","20866":"Cyrillic (KOI8-R) 20866","20871":"IBM EBCDIC (Icelandic) 20871","20880":"IBM EBCDIC (Cyrillic Russian) 20880","20905":"IBM EBCDIC (Turkish) 20905","20924":"IBM Latin-1 20924","20932":"Japanese (JIS 0208-1990 and 0212-1990) 20932","20936":"Chinese Simplified (GB2312-80) 20936","20949":"Korean Wansung 20949","21025":"IBM EBCDIC (Cyrillic Serbian-Bulgarian) 21025","21866":"Cyrillic (KOI8-U) 21866","28591":"Western European (ISO) 28591","28592":"Central European (ISO) 28592","28593":"Latin 3 (ISO) 28593","28594":"Baltic (ISO) 28594","28595":"Cyrillic (ISO) 28595","28596":"Arabic (ISO) 28596","28597":"Greek (ISO) 28597","28598":"Hebrew (ISO-Visual) 28598","28599":"Turkish (ISO) 28599","28603":"Estonian (ISO) 28603","28605":"Latin 9 (ISO) 28605","29001":"Europa 29001","38598":"Hebrew (ISO-Logical) 38598","50220":"Japanese (JIS) 50220","50221":"Japanese (JIS-Allow 1 byte Kana) 50221","50222":"Japanese (JIS-Allow 1 byte Kana - SO/SI) 50222","50225":"Korean (ISO) 50225","50227":"Chinese Simplified (ISO-2022) 50227","51932":"Japanese (EUC) 51932","51936":"Chinese Simplified (EUC) 51936","51949":"Korean (EUC) 51949","52936":"Chinese Simplified (HZ) 52936","54936":"Chinese Simplified (GB18030) 54936","57002":"ISCII Devanagari 57002","57003":"ISCII Bengali 57003","57004":"ISCII Tamil 57004","57005":"ISCII Telugu 57005","57006":"ISCII Assamese 57006","57007":"ISCII Oriya 57007","57008":"ISCII Kannada 57008","57009":"ISCII Malayalam 57009","57010":"ISCII Gujarati 57010","57011":"ISCII Punjabi 57011","65000":"Unicode (UTF-7) 65000","65001":"Unicode (UTF-8) 65001"},"barcode_gs1_composite_type":{"CCA":"CCA","None":"None"},"barcode_maxicode_mode":{"Mode2":"Mode2","Mode3":"Mode3","Mode4":"Mode4","Mode5":"Mode5","Mode6":"Mode6"},"barcode_micro_pdf417_compaction_mode":{"Auto":"Auto","ByteCompactionMode":"ByteCompactionMode","NumericCompactionMode":"NumericCompactionMode","TextCompactionMode":"TextCompactionMode"},"barcode_micro_pdf417_version":{"ColumnPriorAuto":"ColumnPriorAuto","RowPriorAuto":"RowPriorAuto","Version1X11":"Version1X11","Version1X14":"Version1X14","Version1X17":"Version1X17","Version1X20":"Version1X20","Version1X24":"Version1X24","Version1X28":"Version1X28","Version2X11":"Version2X11","Version2X14":"Version2X14","Version2X17":"Version2X17","Version2X20":"Version2X20","Version2X23":"Version2X23","Version2X26":"Version2X26","Version2X8":"Version2X8","Version3X10":"Version3X10","Version3X12":"Version3X12","Version3X15":"Version3X15","Version3X20":"Version3X20","Version3X26":"Version3X26","Version3X32":"Version3X32","Version3X38":"Version3X38","Version3X44":"Version3X44","Version3X6":"Version3X6","Version3X8":"Version3X8","Version4X10":"Version4X10","Version4X12":"Version4X12","Version4X15":"Version4X15","Version4X20":"Version4X20","Version4X26":"Version4X26","Version4X32":"Version4X32","Version4X38":"Version4X38","Version4X4":"Version4X4","Version4X44":"Version4X44","Version4X6":"Version4X6","Version4X8":"Version4X8"},"barcode_micro_qrcode_error_level":{"L":"L","M":"M","Q":"Q"},"barcode_micro_qrcode_mask":{"Auto":"Auto","Mask00":"Mask00","Mask01":"Mask01","Mask10":"Mask10","Mask11":"Mask11"},"barcode_micro_qrcode_version":{"Auto":"Auto","M1":"M1","M2":"M2","M3":"M3","M4":"M4"},"barcode_pdf417_error_correction_level":{"Level0":"Level0","Level1":"Level1","Level2":"Level2","Level3":"Level3","Level4":"Level4","Level5":"Level5","Level6":"Level6","Level7":"Level7","Level8":"Level8"},"barcode_pdf417_type":{"Normal":"Normal","Simple":"Simple"},"barcode_qrcode_error_level":{"H":"H","L":"L","M":"M","Q":"Q"},"barcode_qrcode_mask":{"Auto":"Auto","Mask000":"Mask000","Mask001":"Mask001","Mask010":"Mask010","Mask011":"Mask011","Mask100":"Mask100","Mask101":"Mask101","Mask110":"Mask110","Mask111":"Mask111"},"barcode_qrcode_model":{"Model1":"Model1","Model2":"Model2"},"barcode_rotation":{"None":"없음","Rotate180Degrees":"Rotate180Degrees","Rotate270Degrees":"Rotate270Degrees","Rotate90Degrees":"Rotate90Degrees"},"barcode_symbology":{"Ansi39":"ANSI 3 of 9","Ansi39x":"ANSI Extended 3 of 9","Aztec":"Aztec","BC412":"BC412","Codabar":"Codabar","Code25intlv":"Interleaved 2 of 5","Code39":"Code 39","Code39x":"Extended Code 39","Code49":"Code 49","Code93x":"Extended Code 93","Code_11":"Code 11","Code_128_A":"Code 128 A","Code_128_B":"Code 128 B","Code_128_C":"Code 128 C","Code_128auto":"Code 128 Auto","Code_2_of_5":"Code 2 of 5","Code_93":"Code 93","DataMatrix":"Data Matrix","EAN128FNC1":"EAN-128FNC1","EAN_13":"EAN-13","EAN_8":"EAN-8","GS1DataMatrix":"GS1 Data Matrix","GS1QRCode":"GS1 QR Code","HIBCCode128":"HIBC Code 128","HIBCCode39":"HIBC Code 39","IATA_2_of_5":"IATA 2 of 5","ISBN":"ISBN(International Standard Book Number)","ISMN":"ISMN(Internationally Standard Music Number)","ISSN":"ISSN(International Standard Serial Number)","ITF14":"ITF-14","IntelligentMail":"Intelligent Mail","IntelligentMailPackage":"Intelligent Mail Package","JapanesePostal":"Japanese Postal","MSI":"MSI Code","Matrix_2_of_5":"Matrix 2 of 5","MaxiCode":"MaxiCode","MicroPDF417":"Micro PDF417","MicroQRCode":"Micro QR Code","None":"없음","PZN":"PZN(Pharmaceutical Central Number)","Pdf417":"PDF417","Pharmacode":"Pharmacode","Plessey":"Plessey","PostNet":"PostNet","QRCode":"QR 코드","RM4SCC":"RM4SCC(Royal Mail)","RSS14":"RSS-14","RSS14Stacked":"RSS-14 Stacked","RSS14StackedOmnidirectional":"RSS-14 Stacked Omnidirectional","RSS14Truncated":"RSS-14 Truncated","RSSExpanded":"RSS Expanded","RSSExpandedStacked":"RSS Expanded Stacked","RSSLimited":"RSS Limited","SSCC_18":"SSCC-18","Telepen":"Telepen","UCCEAN128":"UCC/EAN–128","UPC_A":"UPC-A","UPC_E0":"UPC-E0","UPC_E1":"UPC-E1"},"border_style":{"DashDot":"일점쇄선","DashDotDot":"이점쇄선","Dashed":"파선","Dotted":"점선","Double":"이중 실선","Groove":"오목","Inset":"오목","None":"없음","Outset":"빼기","Ridge":"볼록","Solid":"단색","WindowInset":"WindowInset"},"bullet_tick_marks":{"Inside":"내부","None":"없음","Outside":"외부"},"calendar":{"Gregorian":"양력","GregorianArabic":"양력 아랍어","GregorianMiddleEastFrench":"양력 중동 프랑스어","GregorianTransliteratedEnglish":"양력 자역 영어","GregorianTransliteratedFrench":"양력 자역 프랑스어","GregorianUSEnglish":"양력 미국 영어","Hebrew":"히브리어","Hijri":"회교식","Japanese":"일본어","Korean":"한국어","Taiwan":"대만","ThaiBuddhist":"태국 불교식"},"check_alignment":{"BottomCenter":"BottomCenter","BottomLeft":"BottomLeft","BottomRight":"BottomRight","MiddleCenter":"MiddleCenter","MiddleLeft":"MiddleLeft","MiddleRight":"MiddleRight","TopCenter":"TopCenter","TopLeft":"TopLeft","TopRight":"TopRight"},"check_style":{"Check":"확인 표시","Circle":"원","Cross":"교차","Diamond":"다이아몬드","Square":"정사각형","Star":"별"},"collate_by":{"Simple":"단순","Value":"값","ValueIndex":"ValueIndex"},"collation":{"Albanian":"알바니아어","Arabic":"아랍어","Chinese_PRC":"중국어(중국)","Chinese_PRC_Stroke":"중국어(중국) 스트로크","Chinese_Taiwan_Bopomofo":"중국어(대만) 보포모포","Chinese_Taiwan_Stroke":"중국어(대만) 스트로크","Croatian":"크로아티아어","Cyrillic_General":"키릴 자모 일반","Czech":"체코어","Danish_Norwegian":"덴마크어(노르웨이)","Default":"기본값","Estonian":"에스토니아어","FYRO_Macedonian":"마케도니아어(FYRO)","Finnish_Swedish":"핀란드어(스웨덴)","French":"프랑스어","Georgian_Modern_Sort":"그루지야어(현대 정렬)","German_PhoneBook":"독일어 전화번호부","Greek":"그리스어","Hebrew":"히브리어","Hindi":"힌디어","Hungarian":"헝가리어","Hungarian_Technical":"헝가리어 전문 용어","Icelandic":"아이슬란드어","Japanese":"일본어","Japanese_Unicode":"일본어 유니코드","Korean_Wansung":"한국어 완성","Korean_Wansung_Unicode":"한국어 완성 유니코드","Latin1_General":"Latin-1 General","Latvian":"라트비아어","Lithuanian":"리투아니아어","Lithuanian_Classic":"리투아니아어(정통)","Polish":"폴란드어","Romanian":"루마니아어","Slovak":"슬로바키아어","Slovenian":"슬로베니아어","Spanish_Mexican_Trad":"스페인어(멕시코)(전통)","Spanish_Modern":"스페인어(현대)","Thai":"태국어","Turkish":"터키어","Ukrainian":"우크라이나어","Vietnamese":"베트남어"},"data_element_output":{"Auto":"자동","ContentsOnly":"ContentsOnly","NoOutput":"NoOutput","Output":"출력"},"data_element_output_aon":{"Auto":"자동","NoOutput":"NoOutput","Output":"출력"},"data_element_output_on":{"NoOutput":"NoOutput","Output":"출력"},"data_element_style":{"AttributeNormal":"AttributeNormal","Auto":"자동","ElementNormal":"ElementNormal"},"data_label_position":{"Auto":"자동","Bottom":"아래쪽","BottomLeft":"좌하단","BottomRight":"우하단","Center":"가운데","Left":"왼쪽","Right":"오른쪽","Top":"위쪽","TopLeft":"좌상단","TopRight":"우상단"},"data_visualizer_color_type":{"colorScale":"색조"},"data_visualizer_gradient_type":{"DiagonalDown":"DiagonalDown","DiagonalUp":"DiagonalUp","FromCenter":"FromCenter","FromCorner":"FromCorner","Horizontal":"가로","Vertical":"세로"},"data_visualizer_hatch_style":{"BackwardDiagonal":"역방향 대각선","Cross":"교차","DarkDownwardDiagonal":"DarkDownwardDiagonal","DarkHorizontal":"DarkHorizontal","DarkUpwardDiagonal":"DarkUpwardDiagonal","DarkVertical":"DarkVertical","DashedDownwardDiagonal":"DashedDownwardDiagonal","DashedHorizontal":"DashedHorizontal","DashedUpwardDiagonal":"DashedUpwardDiagonal","DashedVertical":"DashedVertical","DiagonalBrick":"DiagonalBrick","DiagonalCross":"대각선","Divot":"양방향 사선","DottedDiamond":"DottedDiamond","DottedGrid":"DottedGrid","ForwardDiagonal":"정방향 대각선","Horizontal":"가로","HorizontalBrick":"HorizontalBrick","LargeCheckerBoard":"LargeCheckerBoard","LargeConfetti":"LargeConfetti","LargeGrid":"LargeGrid","LightDownwardDiagonal":"LightDownwardDiagonal","LightHorizontal":"LightHorizontal","LightUpwardDiagonal":"LightUpwardDiagonal","LightVertical":"LightVertical","NarrowHorizontal":"NarrowHorizontal","NarrowVertical":"NarrowVertical","OutlinedDiamond":"OutlinedDiamond","Percent05":"Percent05","Percent10":"Percent10","Percent20":"Percent20","Percent25":"Percent25","Percent30":"Percent30","Percent40":"Percent40","Percent50":"Percent50","Percent60":"Percent60","Percent70":"Percent70","Percent75":"Percent75","Percent80":"Percent80","Percent90":"Percent90","Plaid":"격자 무늬","Shingle":"지붕 널","SmallCheckerBoard":"SmallCheckerBoard","SmallConfetti":"SmallConfetti","SmallGrid":"SmallGrid","SolidDiamond":"SolidDiamond","Sphere":"구","Trellis":"트렐리스","Vertical":"세로","Wave":"물결","Weave":"평직","WideDownwardDiagonal":"WideDownwardDiagonal","WideUpwardDiagonal":"WideUpwardDiagonal","ZigZag":"지그재그"},"data_visualizer_icon_set_type":{"3TrafficLights":"3TrafficLights","Arrows":"화살표","Blank":"공백","Checkbox":"체크박스","Flags":"플래그","GrayArrows":"GrayArrows","Quarters":"4분의 1","Ratings":"등급","RedToBlack":"RedToBlack","Signs":"모양","Symbols1":"기호 1","Symbols2":"기호 2","TrafficLights":"TrafficLights"},"data_visualizer_icon_set_value":{"false":"False","true":"True"},"data_visualizer_image_type":{"dataBar":"데이터 막대","gradient":"그라데이션","hatch":"Hatch","iconSet":"아이콘 집합","rangeBar":"범위 가로 막대형"},"dataset_option":{"Auto":"자동","false":"False","true":"True"},"dataset_query_command_type":{"StoredProcedure":"StoredProcedure","Text":"텍스트"},"direction":{"LTR":"Ltr","RTL":"Rtl"},"display_type":{"Galley":"갤리","Page":"페이지"},"documentmap_numbering_style":{"CircledNumber":"①, ②, ③, ④, ⑤","Decimal":"1, 2, 3, 4, 5","DecimalLeadingZero":"01, 02, 03, 04, 05","Georgian":"ა, ბ, გ, დ, ე","Katakana":"ア, イ, ウ, エ, オ","KatakanaBrackets":"(ア), (イ), (ウ), (エ), (オ)","KatakanaIroha":"イ, ロ, ハ, ニ, ホ","KatakanaIrohaBrackets":"イ), ロ), ハ), ニ), ホ)","KatakanaIrohaLower":"ｨ, ﾛ, ﾊ, ﾆ, ﾎ","KatakanaLower":"ｱ, ｲ, ｳ, ｴ, ｵ","LowerArmenian":"ա, բ, գ, դ, ե","LowerGreek":"α, β, γ, δ, ε","LowerLatin":"a, b, c, d, e","LowerRoman":"i, ii, iii, iv, v","None":"없음","UpperArmenian":"Ա, Բ, Գ, Դ, Ե","UpperGreek":"Α, Β, Γ, Δ, Ε","UpperLatin":"A, B, C, D, E","UpperRoman":"I, II, III, IV, V"},"documentmap_source":{"All":"모두","Headings":"제목","Labels":"레이블","None":"없음"},"dvchart_aggregate_type":{"Average":"평균","Count":"개수","CountDistinct":"CountDistinct","CountOfAll":"전체갯수","List":"목록","Max":"최대값","Min":"최소값","None":"없음","PopulationStandardDeviation":"모집단 표준편차","PopulationVariance":"모분산","Range":"범위","StandardDeviation":"표준편차","Sum":"합계","Variance":"분산"},"dvchart_axis_date_mode":{"Day":"일","Default":"기본","Hour":"시간","Millisecond":"밀리초","Minute":"분","Month":"월","Second":"초","Week":"주","Year":"년도"},"dvchart_axis_overlapping_labels":{"Auto":"자동","Show":"표시"},"dvchart_axis_position":{"Far":"멀리","Near":"가까이","None":"없음"},"dvchart_axis_type":{"X":"X","Y":"Y"},"dvchart_clipping_mode":{"Clip":"클립","Fit":"맞춤","None":"없음"},"dvchart_encoding_field_type":{"Complex":"복합","Simple":"단순"},"dvchart_encoding_sort":{"Ascending":"오름차순","Descending":"내림차순","None":"없음"},"dvchart_group_type":{"Cluster":"클러스터","None":"없음","Stack":"스택"},"dvchart_group_type_wizard":{"Cluster":"클러스터","PercentStacked":"백분율 스택","Stacked":"스택"},"dvchart_halign":{"Center":"가운데","Left":"왼쪽","Right":"오른쪽"},"dvchart_legend_position":{"Bottom":"아래쪽","Left":"왼쪽","Right":"오른쪽","Top":"위쪽"},"dvchart_line_aspect":{"Default":"기본값","Spline":"스플라인","StepCenter":"StepCenter","StepLeft":"StepLeft","StepRight":"StepRight"},"dvchart_line_position":{"Auto":"자동","Center":"가운데"},"dvchart_orientation":{"Horizontal":"가로","Vertical":"세로"},"dvchart_overlapping_labels":{"Auto":"자동","Hide":"Hide","Show":"표시"},"dvchart_palette":{"Aspect":"Aspect","Blue":"Blue","Blue2":"Blue2","BlueGreen":"BlueGreen","BlueWarm":"BlueWarm","Cerulan":"Cerulan","Cocoa":"Cocoa","Coral":"Coral","Custom":"사용자 정의","Cyborg":"Cyborg","Dark":"Dark","Darkly":"Darkly","Flatly":"Flatly","Grayscale":"Grayscale","Green":"Green","GreenYellow":"GreenYellow","HighContrast":"HighContrast","Light":"Light","Marquee":"Marquee","Median":"Median","Midnight":"Midnight","Modern":"Modern","Office":"Office","Office2010":"Office2010","Orange":"Orange","OrangeRed":"OrangeRed","Organic":"Organic","Paper":"Paper","Red":"Red","RedOrange":"RedOrange","RedViolet":"RedViolet","Slate":"Slate","Slipstream":"Slipstream","Standard":"Standard","Superhero":"Superhero","Violet":"Violet","Violet2":"Violet2","Yellow":"Yellow","YellowOrange":"YellowOrange","Zen":"Zen"},"dvchart_plot_overlay_aggregate_type":{"Average":"Average","Count":"Count","Max":"Max","Median":"Median","Min":"Min","Percentile":"Percentile","Sum":"Sum"},"dvchart_plot_overlay_axis":{"X":"X","Y":"Y"},"dvchart_plot_overlay_detail_level":{"Group":"Group","Total":"Total"},"dvchart_plot_overlay_display":{"Back":"Back","Front":"Front"},"dvchart_plot_overlay_type":{"CumulativeMovingAverageTrendline":"CumulativeMovingAverageTrendline","ExponentialMovingAverageTrendline":"ExponentialMovingAverageTrendline","ExponentialTrendline":"ExponentialTrendline","FourierTrendline":"FourierTrendline","LinearTrendline":"LinearTrendline","LogarithmicTrendline":"LogarithmicTrendline","MovingAnnualTotalTrendline":"MovingAnnualTotalTrendline","MovingAverageTrendline":"MovingAverageTrendline","PolynomialTrendline":"PolynomialTrendline","PowerTrendline":"PowerTrendline","ReferenceBand":"ReferenceBand","ReferenceLine":"ReferenceLine","WeightedMovingAverageTrendline":"WeightedMovingAverageTrendline"},"dvchart_plot_rule_properties":{"BackgroundColor":"배경색","LabelTemplate":"라벨 템플릿","LineColor":"선 색상","LineStyle":"라인 스타일","LineWidth":"선폭","SymbolBackgroundColor":"심볼 배경색","SymbolLineColor":"심볼 라인 색상","SymbolLineStyle":"심볼 선 스타일","SymbolLineWidth":"심볼 선 두께","TooltipTemplate":"툴팁 템플릿"},"dvchart_plot_type":{"Area":"영역형","Bar":"가로 막대형","Candlestick":"원통형","HighLowOpenClose":"HighLowOpenClose","Line":"선","Scatter":"분산형"},"dvchart_scale":{"Linear":"선형","Logarithmic":"로그","Ordinal":"서수","Percentage":"백분율"},"dvchart_show_nulls":{"Connected":"연결됨","Gaps":"간격","Zeros":"0"},"dvchart_symbol_shape":{"Auto":"자동","Box":"상자","Dash":"파선","Diamond":"다이아몬드","Dot":"점","Plus":"더하기","Triangle":"삼각형","X":"X"},"dvchart_target":{"Label":"라벨","ToolTip":"툴팁"},"dvchart_template":{"PercentageCategory_p0":"범주의 백분율","PercentageDetail_p0":"세부 정보의 백분율","categoryField":{"name":"범주 필드 이름","value":"범주 필드 값"},"colorField":{"name":"색 필드 이름","value":"색 필드 값"},"detailFields":{"name":"세부 정보 필드 이름","value":"세부 정보 필드 값"},"shapeField":{"name":"도형 필드 이름","value":"도형 필드 값"},"sizeField":{"name":"크기 필드 이름","value":"크기 필드 값"},"valueField":{"name":"값 필드 이름","value":"값 필드 값"}},"dvchart_text_position":{"Auto":"자동","Center":"가운데","Inside":"내부","Outside":"외부"},"dvchart_tick_mark":{"Cross":"교차","Inside":"내부","None":"없음","Outside":"외부"},"dvchart_valign":{"Bottom":"아래쪽","Middle":"가운데","Top":"위쪽"},"end_cap":{"Arrow":"화살표","None":"없음"},"font_size":{"10pt":"10pt","11pt":"11pt","12pt":"12pt","14pt":"14pt","16pt":"16pt","18pt":"18pt","20pt":"20pt","22pt":"22pt","24pt":"24pt","26pt":"26pt","28pt":"28pt","36pt":"36pt","48pt":"48pt","72pt":"72pt","8pt":"8pt","9pt":"9pt"},"font_style":{"Italic":"기울임꼴","Normal":"보통"},"font_weight":{"Bold":"굵게","Bolder":"더 굵게","ExtraBold":"아주 굵게","ExtraLight":"가늘게","Heavy":"가장 굵게","Light":"약간 가늘게","Lighter":"더 얇게","Medium":"중간","Normal":"보통","SemiBold":"약간 굵게","Thin":"아주 가늘게"},"gradient_type":{"Center":"가운데","DiagonalLeft":"대각선 왼쪽","DiagonalRight":"대각선 오른쪽","HorizontalCenter":"수평 중앙","LeftRight":"왼쪽 오른쪽","None":"없음","TopBottom":"상단 하단","VerticalCenter":"수직 중잉"},"grid_mode":{"Dots":"점","Lines":"선"},"grow_direction":{"Column":"Column","ColumnReverse":"ColumnReverse","Row":"Row","RowReverse":"RowReverse"},"horizontal_align":{"Center":"가운데","Left":"왼쪽","Right":"오른쪽"},"image_repeat":{"NoRepeat":"NoRepeat","Repeat":"반복","RepeatX":"RepeatX","RepeatY":"RepeatY"},"image_sizing":{"AutoSize":"크기 자동 조정","Clip":"클립","Fit":"맞춤","FitProportional":"비례 맞춤"},"image_source":{"Database":"데이터베이스","Embedded":"포함됨","External":"외부"},"input_type":{"CheckBox":"체크박스","Text":"텍스트"},"keep_with_group":{"After":"뒤쪽","Before":"앞쪽","None":"없음"},"label_font_style":{"Bold":"굵게","Italic":"기울임꼴","Regular":"보통","Strikeout":"취소선","Underline":"밑줄"},"labels_text_orientation":{"Angled":"각진","Auto":"자동","Horizontal":"가로","Rotated270":"Rotated270","Rotated90":"Rotated90","Stacked":"누적형"},"language":{"Default":"기본값","af-ZA":"아프리칸스어 - 남아프리카","am-ET":"암하라어 - 에티오피아","ar-AE":"아랍어 - 아랍에미리트","ar-BH":"아랍어 - 바레인","ar-DZ":"아랍어 - 알제리","ar-EG":"아랍어 - 이집트","ar-IQ":"아랍어 - 이라크","ar-JO":"아랍어 - 요르단","ar-KW":"아랍어 - 쿠웨이트","ar-LB":"아랍어 - 레바논","ar-LY":"아랍어 - 리비아","ar-MA":"아랍어 - 모로코","ar-OM":"아랍어 - 오만","ar-QA":"아랍어 - 카타르","ar-SA":"아랍어 - 사우디아라비아","ar-SY":"아랍어 - 시리아","ar-TN":"아랍어 - 튀니지","ar-YE":"아랍어 - 예멘","arn-CL":"마푸둥군어 - 칠레","as-IN":"아샘어 - 인도","az-AZ-Cyrl":"아제르바이잔어(키릴 자모) - 아제르바이잔","az-AZ-Latn":"아제르바이잔어(라틴 문자) - 아제르바이잔","ba-RU":"바슈키르어 - 러시아","be-BY":"벨라루스어 - 벨라루스","bg-BG":"불가리아어 - 불가리아","bn-BD":"벵골어 - 방글라데시","bn-IN":"벵골어 - 인도","bo-CN":"티베트어 - 중국","br-FR":"브르타뉴어 - 프랑스","bs-Cyrl-BA":"보스니아어(키릴 자모) - 보스니아 헤르체고비나","bs-Latn-BA":"보스니아어(라틴 문자) - 보스니아 헤르체고비나","ca-ES":"카탈루냐어 - 카탈루냐","co-FR":"코르시카어 - 프랑스","cs-CZ":"체코어 - 체코","cy-GB":"웨일스어 - 영국","da-DK":"덴마크어 - 덴마크","de-AT":"독일어 - 오스트리아","de-CH":"독일어 - 스위스","de-DE":"독일어 - 독일","de-LI":"독일어 - 리히텐슈타인","de-LU":"독일어 - 룩셈부르크","div-MV":"디베히어 - 몰디브","el-GR":"그리스어 - 그리스","en-AU":"영어 - 호주","en-BZ":"영어 - 벨리즈","en-CA":"영어 - 캐나다","en-CB":"영어 - 카리브해","en-GB":"영어 - 영국","en-IE":"영어 - 아일랜드","en-IN":"영어 - 인도","en-JM":"영어 - 자메이카","en-MY":"영어 - 말레이시아","en-NZ":"영어 - 뉴질랜드","en-PH":"영어 - 필리핀","en-SG":"영어 - 싱가포르","en-TT":"영어 - 트리니다드 토바고","en-US":"영어 - 미국","en-ZA":"영어 - 남아프리카","en-ZW":"영어 - 짐바브웨","es-AR":"스페인어 - 아르헨티나","es-BO":"스페인어 - 볼리비아","es-CL":"스페인어 - 칠레","es-CO":"스페인어 - 콜롬비아","es-CR":"스페인어 - 코스타리카","es-DO":"스페인어 - 도미니카 공화국","es-EC":"스페인어 - 에콰도르","es-ES":"스페인어 - 스페인","es-GT":"스페인어 - 과테말라","es-HN":"스페인어 - 온두라스","es-MX":"스페인어 - 멕시코","es-NI":"스페인어 - 니카라과","es-PA":"스페인어 - 파나마","es-PE":"스페인어 - 레루","es-PR":"스페인어 - 푸에르토리코","es-PY":"스페인어 - 파라과이","es-SV":"스페인어 - 엘살바도르","es-US":"스페인어 - 미국","es-UY":"스페인어 - 우루과이","es-VE":"스페인어 - 베네수엘라","et-EE":"에스토니아어 - 에스토니아","eu-ES":"바스크어 - 바스크","fa-IR":"이란어 - 이란","fi-FI":"핀란드어 - 핀란드","fil-PH":"필리핀어 - 필리핀","fo-FO":"페로어 - 페로 제도","fr-BE":"프랑스어 - 벨기에","fr-CA":"프랑스어 - 캐나다","fr-CH":"프랑스어 - 스위스","fr-FR":"프랑스어 - 프랑스","fr-LU":"프랑스어 - 룩셈부르크","fr-MC":"프랑스어 - 모나코","fy-NL":"프랑스어 - 네덜란드","ga-IE":"아일랜드어 - 아일랜드","gd-GB":"스코틀랜드 게일어 - 영국","gl-ES":"갈리시아어 - 갈리시아","gsw-FR":"알사스어 - 프랑스","gu-IN":"구자라트어 - 인도","ha-Latn-NG":"하우사어(라틴 문자) - 나이지리아","he-IL":"히브리어 - 이스라엘","hi-IN":"힌디어 - 인도","hr-BA":"크로아티아어(라틴 문자) - 보스니아 헤르체고비나","hr-HR":"크로아티아어 - 크로아티아","hu-HU":"헝가리어 - 헝가리","hy-AM":"아르메니아어 - 아르메니아","id-ID":"인도네시아어 - 인도네시아","ig-NG":"이그보어 - 나이지리아","ii-CN":"이어 - 중국","is-IS":"아이슬란드어 - 아이슬란드","it-CH":"이탈리아어 - 스위스","it-IT":"이탈리아어 - 이탈리아","iu-Cans-CA":"이누크티투트어(음절형) - 캐나다","iu-Latn-CA":"이누크티투트어(라틴 문자) - 캐나다","ja-JP":"일본어 - 일본","ka-GE":"그루지야어 - 그루지야","kk-KZ":"카자흐어 - 카자흐스탄","kl-GL":"그린란드어 - 그린란드","km-KH":"캄보디아어 - 캄보디아","kn-IN":"칸나다어 - 인도","ko-KR":"한국어 - 대한민국","kok-IN":"콘칸어 - 인도","ky-KG":"키르기스어 - 키르기스스탄","lb-LU":"룩셈부르크어 - 룩셈부르크","lo-LA":"라오스어 - 라오 인민민주주의공화국","lt-LT":"리투아니아어 - 리투아니아","lv-LV":"라트비아어 - 라트비아","mi-NZ":"마오리어 - 뉴질랜드","mk-MK":"마케도니아어 - 마케도니아","ml-IN":"말라얄람어 - 인도","mn-MN":"몽골어(키릴 자모) - 몽골","mn-Mong-CN":"몽골어(전통 몽골어) - 중국","mn-Mong-MN":"몽골어 - 몽골","moh-CA":"모호크어 - 모호크","mr-IN":"마라티어 - 인도","ms-BN":"말레이어 - 브루나이","ms-MY":"말레이어 - 말레이시아","mt-MT":"몰타어 - 몰타","nb-NO":"노르웨이어(북몰) - 노르웨이","ne-NP":"네팔어 - 네팔","nl-BE":"네덜란드어 - 벨기에","nl-NL":"네덜란드어 - 네덜란드","nn-NO":"노르웨이어(니노르스크) - 노르웨이","nso-ZA":"북부 소토어 - 남아프리카","oc-FR":"오크어 - 프랑스","or-IN":"오리야어 - 인도","pa-IN":"펀자브어 - 인도","pl-PL":"폴란드어 - 폴란드","prs-AF":"다리어 - 아프가니스탄","ps-AF":"파슈토어 - 아프가니스탄","pt-BR":"포르투갈어 - 브라질","pt-PT":"포르투갈어 - 포르투갈","qut-GT":"키체어 - 과테말라","quz-BO":"케추아어 - 볼리비아","quz-EC":"케추아어 - 에콰도르","quz-PE":"케추아어 - 페루","rm-CH":"루마니아어 - 스위스","ro-RO":"루마니아어 - 루마니아","ru-RU":"러시아어 - 러시아","rw-RW":"키냐르완다어 - 르완다","sa-IN":"산스크리트어 - 인도","sah-RU":"야쿠트어 - 러시아","se-FI":"북부 라프어 - 핀란드","se-NO":"북부 라프어 - 노르웨이","se-SE":"북부 라프어 - 스웨덴","si-LK":"싱할라어 - 스리랑카","sk-SK":"슬로바키아어 - 슬로바키아","sl-SI":"슬로베니아어 - 슬로베니아","sma-NO":"남부 라프어 - 노르웨이","sma-SE":"남부 라프어 - 스웨덴","smj-NO":"룰레 라프어 - 노르웨이","smj-SE":"룰레 라프어 - 스웨덴","smn-FI":"이나리 라프어 - 핀란드","sms-FI":"남부 라프어 - 핀란드","sq-AL":"알바니아어 - 알바니아","sr-SP-Cyrl":"세르비아어(키릴 자모) - 세르비아","sr-SP-Latn":"세르비아어(라틴 문자) - 세르비아","sv-FI":"스웨덴어 - 핀란드","sv-SE":"스웨덴어 - 스웨덴","sw-KE":"스와힐리어 - 케냐","syr-SY":"시리아어 - 시리아","ta-IN":"타밀어 - 인도","te-IN":"텔루구어 - 인도","tg-Cyrl-TJ":"타지키스탄어(키릴 자모) - 타지키스탄","th-TH":"태국어 - 태국","tk-TM":"투르크멘어 - 투르크메니스탄","tn-ZA":"츠와나어 - 남아프리카","tr-TR":"터키어 - 터키","tt-RU":"타타르어 - 러시아","tzm-Latn-DZ":"타마지트어(라틴 문자) - 알제리","ug-CN":"위구르어 - 중국","uk-UA":"우크라이나어 - 우크라이나","ur-PK":"우르두어 - 파키스탄","uz-UZ-Cyrl":"우즈베크어(키릴 자모) - 우즈베키스탄","uz-UZ-Latn":"우즈베크어(라틴 문자) - 우즈베키스탄","vi-VN":"베트남어 - 베트남","wee-DE":"저지대 슬라브어 - 독일","wen-DE":"고지 소르브어 - 독일","wo-SN":"월라프어 - 세네갈","xh-ZA":"코사어 - 남아프리카","yo-NG":"요루바어 - 나이지리아","zh-CN":"중국어 - 중국","zh-HK":"중국어 - 홍콩 특별 행정구","zh-MO":"중국어 - 마카오 특별 행정구","zh-SG":"중국어 - 싱가포르","zh-TW":"중국어 - 대만","zu-ZA":"줄루어 - 남아프리카"},"layout_direction":{"LTR":"Ltr","RTL":"Rtl"},"layout_order":{"NOrder":"N-순서","ZOrder":"Z-순서"},"legend_layout":{"Column":"열","Row":"행","Table":"테이블"},"legend_position":{"BottomCenter":"중앙하단","BottomLeft":"좌측하단","BottomRight":"우측하단","LeftBottom":"하단좌측","LeftCenter":"중앙좌측","LeftTop":"상단좌측","RightBottom":"하단우측","RightCenter":"중앙우측","RightTop":"상단우측","TopCenter":"중앙상단","TopLeft":"좌측상단","TopRight":"우측상단"},"line_control_style":{"DashDot":"일점쇄선","DashDotDot":"이점쇄선","Dashed":"파선","Dotted":"점선","Double":"이중 실선","None":"투명","Solid":"단색"},"marker_type":{"Auto":"자동","Circle":"원","Cross":"교차","Diamond":"다이아몬드","None":"없음","Square":"정사각형","Triangle":"삼각형"},"mime_type":{"image/bmp":"image/bmp","image/gif":"image/gif","image/jpeg":"image/jpeg","image/png":"image/png","image/x-emf":"image/x-emf","image/x-wmf":"image/x-wmf"},"new_page":{"Even":"짝수 페이지","Next":"다음 페이지","Odd":"홀수 페이지"},"numeral_variant":{"1":"1","2":"2","3":"3","4":"4","5":"5","6":"6","7":"7"},"operator":{"Between":"Between","BottomN":"BottomN","BottomPercent":"BottomPercent","Equal":"Equal","GreaterThan":"GreaterThan","GreaterThanOrEqual":"GreaterThanOrEqual","In":"In","LessThan":"LessThan","LessThanOrEqual":"LessThanOrEqual","Like":"Like","NotEqual":"NotEqual","TopN":"TopN","TopPercent":"TopPercent"},"order_by_condition":{"Label":"라벨","None":"없음","Value":"값"},"orientation":{"Horizontal":"가로","Vertical":"세로"},"overflow":{"Auto":"자동","Clip":"줄이기","Grow":"늘리기","Scroll":"스크롤"},"page_break":{"End":"종료","None":"없음","Start":"시작","StartAndEnd":"시작과 마지막"},"page_break_with_between":{"Between":"중간","End":"종료","None":"없음","Start":"시작","StartAndEnd":"시작과 마지막"},"page_orientation":{"Landscape":"가로 보기","Portrait":"세로 보기"},"plot_type":{"Auto":"자동","Line":"선","Point":"점"},"projection_mode":{"Orthographic":"직교","Perspective":"투영"},"pve_datetime_range_type":{"Current":"현재","Last":"이전","LastToDate":"LastToDate","Next":"다음","ToDate":"ToDate"},"pve_datetime_range_type_second":{"Last":"이전","Next":"다음"},"pve_datetime_range_type_time":{"Current":"현재","Last":"이전","Next":"다음"},"pve_datetime_range_unit":{"Day":"일","Hour":"시간","Minute":"분","Month":"월","Quarter":"분기","Second":"초","Week":"주","Year":"연도"},"repeat_blank_rows":{"FillGroup":"FillGroup","FillPage":"FillPage","None":"없음"},"reportparameter_datatype":{"Boolean":"T/F논리식","Date":"날짜","DateTime":"날짜/시간","Float":"Float","Integer":"정수","String":"문자열"},"reportparts_property_type":{"boolean":"부울","borderStyle":"테두리 스타일","color":"색상","float":"실수","fontFamily":"글꼴 패밀리","fontSize":"글꼴 크기","fontStyle":"글꼴 스타일","fontWeight":"글꼴 두께","integer":"정수","length":"길이","lineStyle":"선 스타일","lineWidth":"선 너비","string":"문자열","textDecoration":"텍스트 장식"},"reportparts_sizemode":{"Fixed":"고정된","Resizable":"유동적"},"rich_text_markup":{"HTML":"HTML"},"shading":{"None":"없음","Real":"실제","Simple":"단순"},"shape_styles":{"Ellipse":"타원","Rectangle":"직사각형","RoundRect":"둥근모서리 직사각형"},"size_type":{"Default":"기본값","FitToPage":"페이지에 맞춤","FitToWidth":"너비에 맞추기"},"sparkline_type":{"Area":"영역형","Columns":"열","Line":"선","StackedBar":"스택바","Whiskers":"수염"},"target_shape":{"Dot":"점","Line":"선","Square":"정사각형"},"text_align":{"Center":"가운데","General":"일반","Justify":"양쪽 맞춤","Left":"왼쪽","Right":"오른쪽"},"text_align_glcr":{"Center":"가운데","General":"일반","Left":"왼쪽","Right":"오른쪽"},"text_decoration":{"DoubleUnderline":"이중 밑줄","LineThrough":"취소선","None":"없음","Overline":"윗줄","Underline":"밑줄"},"text_justify":{"Auto":"자동","Distribute":"균등 분할","DistributeAllLines":"DistributeAllLines"},"text_orientation":{"Auto":"자동","Horizontal":"가로","Rotated270":"Rotated270","Rotated90":"Rotated90","Stacked":"누적형"},"tick_mark":{"Cross":"교차","Inside":"내부","None":"없음","Outside":"외부"},"title_position":{"Center":"가운데","Far":"멀리","Near":"가까이"},"unicode_bidi":{"BidiOverride":"BidiOverride","Embed":"포함","Normal":"보통"},"upright_in_vertical_text":{"Digits":"숫자","DigitsAndLatinLetters":"숫자와 라틴문자","None":"없음"},"vertical_align":{"Bottom":"아래쪽","Middle":"가운데","Top":"위쪽"},"wrap_mode":{"CharWrap":"글자 단위 줄 바꿈","NoWrap":"줄 바꿈 없음","WordWrap":"단어 던위 줄 바꿈"},"writing_mode":{"lr-tb":"lr-tb","tb-rl":"tb-rl"}}},{"lng":"ko","ns":"error","resources":{"errorLabel":"오류","expressionNode":{"argumentIsNotValid":"\'{{functionName}}\' 함수의 \'{{argument}}\' 인수가 잘못되었습니다. {{details}}","argumentValueNotFitFunctionValueDataType":"\'{{functionName}}\' 함수의 \'{{argument}}\' 인수는 {{expectedTypes}} 형식 중 하나여야 하지만 {{actualType}} 형식입니다.","argumentsShouldHaveSameDataType":"\'{{functionName}}\' 함수의 \'{{argument}}\' 인수는 데이터 형식이 같아야 합니다.","typeMismatch":"\'{{value}}\' 값을 {{expectedType}}(으)로 변환할 수 없습니다."},"themes":{"textCannotLoad":"Report theme cannot be loaded","textNoThemes":"Please note there are no other themes available so now the report has no theme specified.","textNotFound":"The theme \\"{{theme}}\\" used in this report is not found.","textTotFoundOrInvalid":"The theme {{theme}} used in this report either is not found or has invalid content.","textUseDefault":"The default theme \\"{{theme}}\\" will be used instead."},"upgradeReportSemanticModel":{"entityIsAbsent":"● \'{{entityName}}\' 엔티티의 데이터 셋인 \'{{dataSetName}}\'은(는) 최신 시멘틱 모델에 없습니다.","fieldAtQueryFilterIsAbsent":"● \'{{fieldName}}\' 필드의 데이터 셋인 \'{{dataSetName}}\'쿼리 필터는 최신 시멘틱 모델에 없습니다.","fieldInControlGroupIsAbsent":"● \'{{fieldName}}\' 필드에 {{controlName}} 그룹 {{groupName}} \'{{propertyLabel}}\' 속성은 최신 시멘틱 모델에 없습니다.","fieldInControlIsAbsent":"● \'{{fieldName}}\' 필드의 {{controlName}} \'{{propertyLabel}}\' 속성은 최신 시멘틱 모델에 없습니다.","fieldInReportIsAbsent":"● 리포트의 \'{{fieldName}}\' 필드에 \'{{propertyLabel}}\' 속성은 최신 시멘틱 모델에 없습니다."}}},{"lng":"ko","ns":"expressionFields","resources":{"chart":{"Current":{"Category":{"description":"대상 요소 범주 필드 값을 나타냅니다.","example":"=Chart!CurrentCategory=1; =Chart!CurrentCategory>10; =Chart!CurrentCategory=\\"Value1\\";","label":"카테고리","syntax":"Chart!CurrentCategory <Comparison operator> <Value>"},"Data":{"description":"대상 요소 데이터 필드 값을 나타냅니다.","example":"=Chart!CurrentData=1; =Chart!CurrentData>10; =Chart!CurrentData=\\"Value1\\";","label":"데이터","syntax":"Chart!CurrentData <Comparison operator> <Value>"},"DataClose":{"description":"재무 차트에서 대상 요소의 \'종가\' 값을 나타냅니다.","example":"=Chart!CurrentDataClose=1; =Chart!CurrentDataClose>10; =Chart!CurrentDataClose=\\"Value1\\"; ","label":"종가 데이터","syntax":"Chart!CurrentDataClose <Comparison operator> <Value>"},"DataHigh":{"description":"재무 차트에서 대상 요소의 \'고가\' 값을 나타냅니다.","example":"=Chart!CurrentDataHigh=1; =Chart!CurrentDataHigh>10; =Chart!CurrentDataHigh=\\"Value1\\"; ","label":"고가 데이터","syntax":"Chart!CurrentDataHigh <Comparison operator> <Value>"},"DataLow":{"description":"재무 차트에서 대상 요소의 \'저가\' 값을 나타냅니다.","example":"=Chart!CurrentDataLow=1; =Chart!CurrentDataLow>10; =Chart!CurrentDataLow=\\"Value1\\"; ","label":"저가 데이터","syntax":"Chart!CurrentDataLow <Comparison operator> <Value>"},"DataLower":{"description":"간트 차트에서 대상 요소의 \'하단\' 값을 나타냅니다.","example":"=Chart!CurrentDataLower=1; =Chart!CurrentDataLower>10; =Chart!CurrentDataLower=\\"Value1\\"; ","label":"데이터 하단","syntax":"Chart!CurrentDataLower <Comparison operator> <Value>"},"DataOpen":{"description":"재무 차트에서 대상 요소의 \'시가\' 값을 나타냅니다.","example":"=Chart!CurrentDataOpen=1; =Chart!CurrentDataOpen>10; =Chart!CurrentDataOpen=\\"Value1\\"; ","label":"시가 데이터","syntax":"Chart!CurrentDataOpen <Comparison operator> <Value>"},"DataUpper":{"description":"간트 차트에서 대상 요소의 \'상단\' 값을 나타냅니다.","example":"=Chart!CurrentDataUpper=1; =Chart!CurrentDataUpper>10; =Chart!CurrentDataUpper=\\"Value1\\"; ","label":"데이터 상단","syntax":"Chart!CurrentDataUpper <Comparison operator> <Value>"},"Detail":{"description":"대상 요소 세부 정보 필드 인코딩 값을 나타냅니다.","example":"=Chart!CurrentDetail=1; =Chart!CurrentDetail>10; =Chart!CurrentDetail=\\"Value1\\";","label":"세부 사항","syntax":"Chart!CurrentDetail <Comparison operator> <Value>"},"label":"현재 아이템"},"Functions":{"CurrentValue":{"description":"지정된 플롯에서 데이터 포인트의 현재 값을 나타냅니다.","example":"=CurrentValue(\\"plot1\\"); =CurrentValue(\\"plot1\\",\\"high\\"); ","label":"현재값","syntax":"CurrentValue(\\"플롯 이름\\" , \\"선택적 값 식별자(고가, 저가, 시가, 종가, 상한, 하한)\\")"},"NextValue":{"description":"지정된 플롯의 현재 값 옆에 있는 데이터 포인트 값을 나타냅니다.","example":"=NextValue(\\"plot1\\"); =NextValue(\\"plot1\\",\\"high\\"); ","label":"다음값","syntax":"NextValue(\\"플롯 이름\\" , \\"선택적 값 식별자(고가, 저가, 시가, 종가, 상한, 하한)\\")"},"PreviousValue":{"description":"지정된 플롯에서 현재 값 이전의 데이터 포인트 값을 나타냅니다.","example":"=PreviousValue(\\"plot1\\"); =PreviousValue(\\"plot1\\",\\"high\\"); ","label":"이전값","syntax":"PreviousValue(\\"플롯 이름\\" , \\"선택적 값 식별자(고가, 저가, 시가, 종가, 상한, 하한)\\")"},"label":"함수"},"Next":{"Category":{"description":"대상 요소 다음의 범주 필드 값을 나타냅니다.","example":"=Chart!NextCategory=1; =Chart!NextCategory>10; =Chart!NextCategory=\\"Value1\\";","label":"카테고리","syntax":"Chart!NextCategory <Comparison operator> <Value>"},"Data":{"description":"대상 요소 다음의 데이터 필드를 나타냅니다.","example":"=Chart!NextData=1; =Chart!NextData>10; =Chart!NextData=\\"Value1\\";","label":"데이터","syntax":"Chart!NextData <Comparison operator> <Value>"},"DataClose":{"description":"재무 차트에서 대상 요소 다음의 \'종가\' 값을 나타냅니다.","example":"=Chart!NextDataClose=1; =Chart!NextDataClose>10; =Chart!NextDataClose=\\"Value1\\"; ","label":"종가 데이터","syntax":"Chart!NextDataClose <Comparison operator> <Value>"},"DataHigh":{"description":"재무 차트에서 대상 요소 다음의 \'고가\' 값을 나타냅니다.","example":"=Chart!NextDataHigh=1; =Chart!NextDataHigh>10; =Chart!NextDataHigh=\\"Value1\\"; ","label":"고가 데이터","syntax":"Chart!NextDataHigh <Comparison operator> <Value>"},"DataLow":{"description":"재무 차트에서 대상 요소 다음의 \'저가\' 값을 나타냅니다.","example":"=Chart!NextDataLow=1; =Chart!NextDataLow>10; =Chart!NextDataLow=\\"Value1\\"; ","label":"저가 데이터","syntax":"Chart!NextDataLow <Comparison operator> <Value>"},"DataLower":{"description":"간트 차트에서 대상 요소 다음의 \'하단\' 값을 나타냅니다.","example":"=Chart!NextDataLower=1; =Chart!NextDataLower>10; =Chart!NextDataLower=\\"Value1\\"; ","label":"하단 데이터","syntax":"Chart!NextDataLower <Comparison operator> <Value>"},"DataOpen":{"description":"재무 차트에서 대상 요소 다음의 \'시가\' 값을 나타냅니다.","example":"=Chart!NextDataOpen=1; =Chart!NextDataOpen>10; =Chart!NextDataOpen=\\"Value1\\"; ","label":"시가 데이터","syntax":"Chart!NextDataOpen <Comparison operator> <Value>"},"DataUpper":{"description":"간트 차트에서 대상 요소 다음의 \'상단\' 값을 나타냅니다.","example":"=Chart!NextDataUpper=1; =Chart!NextDataUpper>10; =Chart!NextDataUpper=\\"Value1\\"; ","label":"상단 데이터","syntax":"Chart!NextDataUpper <Comparison operator> <Value>"},"Detail":{"description":"대상 요소 다음의 세부 정보 필드 인코딩 값을 나타냅니다.","example":"=Chart!NextDetail=1; =Chart!NextDetail>10; =Chart!NextDetail=\\"Value1\\";","label":"세부 사항","syntax":"Chart!NextDetail <Comparison operator> <Value>"},"label":"다음 아이템"},"Previous":{"Category":{"description":"대상 요소 이전의 범주 필드 값을 나타냅니다.","example":"=Chart!PreviousCategory=1; =Chart!PreviousCategory>10; =Chart!PreviousCategory=\\"Value1\\";","label":"카테고리","syntax":"Chart!PreviousCategory <Comparison operator> <Value>"},"Data":{"description":"대상 요소 이전의 데이터 필드 값을 나타냅니다.","example":"=Chart!PreviousData=1; =Chart!PreviousData>10; =Chart!PreviousData=\\"Value1\\";","label":"데이터","syntax":"Chart!PreviousData <Comparison operator> <Value>"},"DataClose":{"description":"재무 차트에서 대상 요소 이전의 \'종가\' 값을 나타냅니다.","example":"=Chart!PreviousDataClose=1; =Chart!PreviousDataClose>10; =Chart!PreviousDataClose=\\"Value1\\"; ","label":"종가 데이터","syntax":"Chart!PreviousDataClose <Comparison operator> <Value>"},"DataHigh":{"description":"재무 차트에서 대상 요소 이전의 \'고가\' 값을 나타냅니다.","example":"=Chart!PreviousDataHigh=1; =Chart!PreviousDataHigh>10; =Chart!PreviousDataHigh=\\"Value1\\"; ","label":"고가 데이터","syntax":"Chart!PreviousDataHigh <Comparison operator> <Value>"},"DataLow":{"description":"재무 차트에서 대상 요소 이전의 \'저가\' 값을 나타냅니다.","example":"=Chart!PreviousDataLow=1; =Chart!PreviousDataLow>10; =Chart!PreviousDataLow=\\"Value1\\"; ","label":"저가 데이터","syntax":"Chart!PreviousDataLow <Comparison operator> <Value>"},"DataLower":{"description":"간트 차트에서 대상 요소 이전의 \'하단\' 값을 나타냅니다.","example":"=Chart!PreviousDataLower=1; =Chart!PreviousDataLower>10; =Chart!PreviousDataLower=\\"Value1\\"; ","label":"하단 데이터","syntax":"Chart!PreviousDataLower <Comparison operator> <Value>"},"DataOpen":{"description":"재무 차트에서 대상 요소 이전의 \'시가\' 값을 나타냅니다.","example":"=Chart!PreviousDataOpen=1; =Chart!PreviousDataOpen>10; =Chart!PreviousDataOpen=\\"Value1\\"; ","label":"시가 데이터","syntax":"Chart!PreviousDataOpen <Comparison operator> <Value>"},"DataUpper":{"description":"간트 차트에서 대상 요소 이전의 \'상단\' 값을 나타냅니다.","example":"=Chart!PreviousDataUpper=1; =Chart!PreviousDataUpper>10; =Chart!PreviousDataUpper=\\"Value1\\"; ","label":"상단 데이터","syntax":"Chart!PreviousDataUpper <Comparison operator> <Value>"},"Detail":{"description":"대상 요소 이전의 세부 정보 필드 인코딩 값을 나타냅니다.","example":"=Chart!PreviousDetail=1; =Chart!PreviousDetail>10; =Chart!PreviousDetail=\\"Value1\\";","label":"세부 사항","syntax":"Chart!PreviousDetail <Comparison operator> <Value>"},"label":"이전 아이템"},"label":"차트"},"commonValues":{"info":{"currentDateTime":{"description":"현재 날짜 및 시간을 표시합니다. 페이지 머리글 및 페이지 바닥글에 사용할 수 있습니다."},"pageNM":{"description":"현재 페이지 번호(N)와 총 페이지 수(M)를 \'N/M\' 형식으로 표시합니다. 페이지 머리글 및 페이지 바닥글에 사용할 수 있습니다."},"pageNMCumulative":{"description":"보고서에서 현재 페이지와 총 누적 페이지 수를 모두 표시합니다. N/M 페이지(누적)는 보고서에서 데이터 정렬을 사용할 때 페이지 번호 매기기에 적용됩니다."},"pageNMSection":{"description":"함수가 속한 섹션의 현재 페이지 번호(N)와 총 페이지 수(M)를 \'N/M\' 형식으로 표시합니다. 섹션은 보고서 또는 데이터 영역일 수 있습니다."},"pageNumber":{"description":"현재 페이지 번호를 표시합니다. 페이지 머리글 및 페이지 바닥글에 사용할 수 있습니다."},"pageNumberCumulative":{"description":"현재 누적 페이지 번호를 표시합니다 페이지 번호(누적)는 보고서에서 데이터 정렬을 사용할 때 페이지 번호 매기기에 적용됩니다."},"pageNumberSection":{"description":"함수가 속한 섹션의 현재 페이지 번호를 표시합니다. 섹션은 보고서 또는 데이터 영역일 수 있습니다."},"reportName":{"description":"보고서 이름을 표시합니다."},"textboxValue":{"description":"이 변수는 저장할 때 현재 텍스트 박스 값으로 대체됩니다.","example":"=Sum($$)","example_i11n":"{Sum($$$)}","label":"현재 텍스트박스 값"},"totalPages":{"description":"총 페이지 수를 표시합니다. 페이지 머리글 및 페이지 바닥글에 사용할 수 있습니다."},"totalPagesCumulative":{"description":"보고서에서 총 누적 페이지 수를 표시합니다. 총 페이지 수(누적)는 보고서에서 데이터 정렬을 사용할 때 페이지 번호 매기기에 적용됩니다."},"totalPagesSection":{"description":"함수가 속한 섹션의 총 페이지 수를 표시합니다. 섹션은 보고서 또는 데이터 영역일 수 있습니다."},"userContext":{"description":"함수에만 사용합니다(예: UserContext.GetValue(\\"name\\"), UserContext.NumberToWords(123))."},"userId":{"description":"보고서를 미리 보는 사용자의 사용자 ID를 표시합니다."},"userLanguage":{"description":"보고서를 미리 보는 사용자의 언어를 시스템 설정에 따라 표시합니다."}},"titles":{"label":"공통 값"}},"constants":{"dvchart_template":{"PercentageCategory_p0":{"description":"범주 내에서 값 필드의 백분율을 표시합니다."},"PercentageDetail_p0":{"description":"세부 정보 내에서 값 필드의 백분율을 표시합니다."},"categoryField":{"name":{"description":"범주 필드 이름을 표시합니다."},"value":{"description":"범주 필드 값을 표시합니다."}},"colorField":{"name":{"description":"색 필드 이름을 표시합니다."},"value":{"description":"색 필드 값을 표시합니다."}},"detailFields":{"name":{"description":"세부 정보 필드 이름을 표시합니다."},"value":{"description":"세부 정보 필드 값을 표시합니다."}},"shapeField":{"name":{"description":"도형 필드 이름을 표시합니다."},"value":{"description":"도형 필드 값을 표시합니다."}},"sizeField":{"name":{"description":"크기 필드 이름을 표시합니다."},"value":{"description":"크기 필드 값을 표시합니다."}},"valueField":{"name":{"description":"값 필드 이름을 표시합니다."},"value":{"description":"값 필드 값을 표시합니다."}}},"titles":{"label":"상수"}},"dataSets":{"titles":{"label":"데이터 집합"}},"documentMap":{"info":{"path":{"description":"TOC 수준의 경로를 반환합니다.","example":"=DocumentMap.Path & \\"이것은 제목 1 입니다.\\"","label":"경로"}},"titles":{"label":"문서 맵"}},"functions":{"info":{"aggregate":{"aggregateIf":{"description":"T/F논리식이 지정된 조건을 충족하면 지정된 식을 통해 값의 집계를 계산합니다.","example":"=AggregateIf(Fields!Discontinued.Value = True, \\"Sum\\", Fields!InStock.Value)","label":"AggregateIf","syntax":"AggregateIf(<Condition>, <AggregateFunction>, <AggregateArguments>)"},"aggregateIfWithScope":{"description":"지정된 범위 내에서 T/F논리식이 지정된 조건을 충족하면 지정된 식을 통해 값의 집계를 계산합니다.","example":"=AggregateIf(Fields!Discontinued.Value = True, \\"Sum\\", Fields!InStock.Value, \\"Category\\")","label":"AggregateIf (with scope)","syntax":"AggregateIf(<Condition>, <AggregateFunction>, <AggregateArguments>, <Scope>)"},"avg":{"description":"지정된 식을 통해 null이 아닌 모든 숫자 값의 평균을 계산합니다.","example":"=Avg(Fields!LifeExpentancy.Value)","label":"Avg","syntax":"Avg(<Values>)"},"avgWithScope":{"description":"지정된 범위 내에서 지정된 식을 통해 null이 아닌 모든 숫자 값의 평균을 계산합니다.","example":"=Avg(Fields!LifeExpentancy.Value, \\"GroupByCountry\\")","label":"Avg (with scope)","syntax":"Avg(<Values>, <Scope>)"},"count":{"description":"지정된 식을 통해 null이 아닌 값의 수를 계산합니다.","example":"=Count(Fields!EmployeeID.Value)","label":"Count","syntax":"Count(<Values>)"},"countDistinct":{"description":"지정된 식을 통해 반복되지 않는 값의 수를 계산합니다.","example":"=CountDistinct(Fields!OrderID.Value)","label":"CountDistinct","syntax":"CountDistinct(<Values>)"},"countDistinctWithScope":{"description":"지정된 범위 내에서 지정된 식을 통해 반복되지 않는 값의 수를 계산합니다.","example":"=CountDistinct(Fields!OrderID.Value, \\"GroupByCategory\\")","label":"CountDistinct (with scope)","syntax":"CountDistinct(<Values>, <Scope>)"},"countRows":{"description":"행 수를 계산합니다.","example":"=CountRows()","label":"CountRows","syntax":"CountRows()"},"countRowsWithScope":{"description":"지정된 범위 내에서 행 수를 계산합니다.","example":"=CountRows(\\"Title\\")","label":"CountRows (with scope)","syntax":"CountRows(<Scope>)"},"countWithScope":{"description":"지정된 범위 내에서 지정된 식을 통해 null이 아닌 값의 수를 계산합니다.","example":"=Count(Fields!EmployeeID.Value, \\"Title\\")","label":"Count (with scope)","syntax":"Count(<Values>, <Scope>)"},"crossAggregate":{"description":"지정된 행과 열의 교차에 인수로 지정된 표현식을 사용하여 지정된 함수를 계산합니다.","example":"=CrossAggregate(Fields!Amount.Value, \\"Sum\\", \\"YearGroup\\", \\"CategoryGroup\\")","label":"CrossAggregate","syntax":"CrossAggregate(<Expression>, <FunctionName>, <ColumnGroupName>, <RowGroupName>)"},"cumulativeTotal":{"description":"현재 및 이전 페이지에 대해 식에서 반환된 페이지 수준 집계의 합계를 계산합니다.","example":"=CumulativeTotal(Fields!OrderID.Value, \\"Count\\")","label":"CumulativeTotal","syntax":"CumulativeTotal(<Expression>, <Aggregate>)"},"distinctSum":{"description":"다른 식의 값이 반복되지 않는 경우 지정된 식을 통해 값을 합계를 계산합니다.","example":"=DistinctSum(Fields!OrderID.Value, Fields!OrderFreight.Value)","label":"DistinctSum","syntax":"DistinctSum(<Values>, <Value>)"},"distinctSumWithScope":{"description":"지정된 범위 내에서 다른 식의 값이 반복되지 않는 경우 지정된 식을 통해 값을 합계를 계산합니다.","example":"=DistinctSum(Fields!OrderID.Value, Fields!OrderFreight.Value, \\"Order\\")","label":"DistinctSum (with scope)","syntax":"DistinctSum(<Values>, <Value>, <Scope>)"},"first":{"description":"지정된 식을 통해 첫 번째 값을 반환합니다.","example":"=First(Fields!ProductNumber.Value)","label":"First","syntax":"First(<Values>)"},"firstWithScope":{"description":"지정된 범위 내에서 지정된 식을 통해 첫 번째 값을 반환합니다.","example":"=First(Fields!ProductNumber.Value, \\"Category\\")","label":"First (with scope)","syntax":"First(<Values>, <Scope>)"},"last":{"description":"지정된 식을 통해 마지막 값을 반환합니다.","example":"=Last(Fields!ProductNumber.Value)","label":"Last","syntax":"Last(<Values>)"},"lastWithScope":{"description":"지정된 범위 내에서 지정된 식을 통해 마지막 값을 반환합니다.","example":"=Last(Fields!ProductNumber.Value, \\"Category\\")","label":"Last (with scope)","syntax":"Last(<Values>, <Scope>)"},"max":{"description":"지정된 식을 통해 null이 아닌 최댓값을 반환합니다.","example":"=Max(Fields!OrderTotal.Value)","label":"Max","syntax":"Max(<Values>)"},"maxWithScope":{"description":"지정된 범위 내에서 지정된 식을 통해 null이 아닌 최댓값을 반환합니다.","example":"=Max(Fields!OrderTotal.Value, \\"Year\\")","label":"Max (with scope)","syntax":"Max(<Values>, <Scope>)"},"median":{"description":"지정된 식에서 값의 중간값인 값을 반환합니다. 중앙값은 값 순서에서 가운데 값입니다.","example":"=Median(Fields!OrderTotal.Value)","label":"Median","syntax":"Median(<Values>)"},"medianWithScope":{"description":"지정된 범위 내에서 지정된 식에서 정렬된 값의 중간값인 값을 반환합니다. 중앙값은 값 순서에서 가운데 값입니다.","example":"=Median(Fields!OrderTotal.Value, \\"Year\\")","label":"Median (with scope)","syntax":"Median(<Values>, <Scope>)"},"min":{"description":"지정된 식을 통해 null이 아닌 최솟값을 반환합니다.","example":"=Min(Fields!OrderTotal.Value)","label":"Min","syntax":"Min(<Values>)"},"minWithScope":{"description":"지정된 범위 내에서 지정된 식을 통해 null이 아닌 최솟값을 반환합니다.","example":"=Min(Fields!OrderTotal.Value, \\"Year\\")","label":"Min (with scope)","syntax":"Min(<Values>, <Scope>)"},"mode":{"description":"지정된 식을 통해 가장 자주 발생하는 값을 반환합니다.","example":"=Mode(Fields!OrderTotal.Value)","label":"Mode","syntax":"Mode(<Values>)"},"modeWithScope":{"description":"지정된 범위 내에서 지정된 식을 통해 가장 자주 발생하는 값을 반환합니다.","example":"=Mode(Fields!OrderTotal.Value, \\"Year\\")","label":"Mode (with scope)","syntax":"Mode(<Values>, <Scope>)"},"runningValue":{"description":"다른 집계 함수를 매개변수로 사용하여 지정된 식을 통해 null이 아닌 모든 숫자 값의 실행 집계를 계산합니다.","example":"=RunningValue(Fields!Price.Value, \\"Sum\\")","label":"RunningValue","syntax":"RunningValue(<Values>, <AggregateFunction>)"},"runningValueWithScope":{"description":"지정된 범위 내에서 다른 집계 함수를 매개변수로 사용하여 지정된 식을 통해 null이 아닌 모든 숫자 값의 실행 집계를 계산합니다.","example":"=RunningValue(Fields!Price.Value, \\"Sum\\", \\"Nwind\\")","label":"RunningValue (with scope)","syntax":"RunningValue(<Values>, <AggregateFunction>, <Scope>)"},"stDev":{"description":"지정된 식의 null이 아닌 모든 값의 표준 편차를 계산합니다.","example":"=StDev(Fields!LineTotal.Value)","label":"StDev","syntax":"StDev(<Values>)"},"stDevP":{"description":"지정된 식의 null이 아닌 모든 값의 모집단 표준 편차를 계산합니다.","example":"=StDevP(Fields!LineTotal.Value)","label":"StDevP","syntax":"StDevP(<Values>)"},"stDevPWithScope":{"description":"지정된 범위 내에서 지정된 식의 null이 아닌 모든 값의 모집단 표준 편차를 계산합니다.","example":"=StDevP(Fields!LineTotal.Value, \\"Order\\")","label":"StDevP (with scope)","syntax":"StDevP(<Values>, <Scope>)"},"stDevWithScope":{"description":"지정된 범위 내에서 지정된 식의 null이 아닌 모든 값의 표준 편차를 계산합니다.","example":"=StDev(Fields!LineTotal.Value, \\"Nwind\\")","label":"StDev (with scope)","syntax":"StDev(<Values>, <Scope>)"},"sum":{"description":"지정된 식의 값 합계를 계산합니다.","example":"=Sum(Fields!Price.Value)","label":"Sum","syntax":"Sum(<Values>)"},"sumWithScope":{"description":"지정된 범위 내에서 지정된 식의 값 합계를 계산합니다.","example":"=Sum(Fields!Price.Value, \\"Category\\")","label":"Sum (with scope)","syntax":"Sum(<Values>, <Scope>)"},"var":{"description":"지정된 식의 null이 아닌 모든 값의 분산(제곱 표준 편차)을 계산합니다.","example":"=Var(Fields!LineTotal.Value)","label":"Var","syntax":"Var(<Values>)"},"varP":{"description":"지정된 식의 null이 아닌 모든 값의 모집단 분산(모집단 제곱 표준 편차)을 계산합니다.","example":"=VarP(Fields!LineTotal.Value)","label":"VarP","syntax":"VarP(<Values>)"},"varPWithScope":{"description":"지정된 범위 내에서 지정된 식의 null이 아닌 모든 값의 모집단 분산(모집단 제곱 표준 편차)을 계산합니다.","example":"=VarP(Fields!LineTotal.Value, \\"Order\\")","label":"VarP (with scope)","syntax":"VarP(<Values>, <Scope>)"},"varWithScope":{"description":"지정된 식의 null이 아닌 모든 값의 분산(제곱 표준 편차)을 계산합니다.","example":"=Var(Fields!LineTotal.Value, \\"Order\\")","label":"Var (with scope)","syntax":"Var(<Values>, <Scope>)"}},"conversion":{"format":{"description":"값을 지정된 형식으로 설정합니다.","example":"=Format(Fields!OrderDate.Value, \\"dd MMM yyyy\\")","label":"Format","syntax":"Format(<Value>, <String>)"},"toBoolean":{"description":"지정된 값을 T/F논리식으로 변환합니다.","example":"=ToBoolean(Fields!HouseOwnerFlag.Value)","label":"ToBoolean","syntax":"ToBoolean(<Value>)"},"toByte":{"description":"지정된 값을 바이트로 변환합니다.","example":"=ToByte(Fields!ProductNumber.Value)","label":"ToByte","syntax":"ToByte(<Value>)"},"toChar":{"description":"지정된 값을 Char로 변환합니다.","example":"=ToChar(Fields!OrderStatus.Value); =ToChar(?쏦ello??","label":"ToChar","syntax":"ToChar(<Value>)"},"toDateTime":{"description":"지정된 값을 날짜 및 시간 값으로 변환합니다.","example":"=ToDateTime(Fields!SaleDate.Value); =ToDateTime(\\"1 January, 2017\\")","label":"ToDateTime","syntax":"ToDateTime(<Value>)"},"toDecimal":{"description":"지정된 값을 Decimal로 변환합니다.","example":"=ToDecimal(Fields!Sales.Value)","label":"ToDecimal","syntax":"ToDecimal(<Value>)"},"toDouble":{"description":"지정된 값을 Double로 변환합니다.","example":"=ToDouble(Fields!AnnualSales.Value); =ToDouble(535.85 * .2691 * 67483)","label":"ToDouble","syntax":"ToDouble(<Value>)"},"toInt16":{"description":"지정된 값을 16비트 부호 있는 정수로 변환합니다.","example":"=ToInt16(Fields!AnnualSales.Value); =ToInt16(535.85)","label":"ToInt16","syntax":"ToInt16(<Value>)"},"toInt32":{"description":"지정된 값을 32비트 부호 있는 정수로 변환합니다.","example":"=ToInt32(Fields!AnnualSales.Value)","label":"ToInt32","syntax":"ToInt32(<Value>)"},"toInt64":{"description":"지정된 값을 64비트 부호 있는 정수로 변환합니다.","example":"=ToInt64(Fields!AnnualSales.Value)","label":"ToInt64","syntax":"ToInt64(<Value>)"},"toSingle":{"description":"지정된 값을 단정밀도 부동 소수점 숫자로 변환합니다.","example":"=ToSingle(Fields!AnnualSales.Value); =ToSingle(15.*********)","label":"ToSingle","syntax":"ToSingle(<Value>)"},"toStringDot":{"description":"값을 지정된 형식의 문자열로 변환합니다.","example":"=Fields!OrderDate.Value.ToString(\\"dd MMM yyyy\\")","label":".ToString","syntax":"<Value>.ToString(<String>)"},"toStringKey":{"description":"지정된 값을 문자열로 변환합니다.","example":"=ToString(Fields!YearlyIncome.Value); =ToString(13.50)","label":"ToString","syntax":"ToString(<Value>)"},"toUInt16":{"description":"지정된 값을 16비트 부호 없는 정수로 변환합니다.","example":"=ToUInt16(Fields!AnnualSales.Value)","label":"ToUInt16","syntax":"ToUInt16(<Value>)"},"toUInt32":{"description":"지정된 값을 32비트 부호 없는 정수로 변환합니다.","example":"=ToUInt32(Fields!AnnualSales.Value)","label":"ToUInt32","syntax":"ToUInt32(<Value>)"},"toUInt64":{"description":"지정된 값을 64비트 부호 없는 정수로 변환합니다.","example":"=ToUInt64(Fields!AnnualSales.Value)","label":"ToUInt64","syntax":"ToUInt64(<Value>)"}},"customFunctions":{"getValue":{"description":"지정된 속성의 사용자 컨텍스트 값(예: \\"name\\",\\"email\\")을 표시합니다.","example":"=UserContext.getValue(\\"name\\")","label":"GetUserValue","syntax":"UserContext.getValue(<String>)"},"numberToWords":{"description":"지정된 값을 단어로 변환합니다. 단일 인수 함수는 포털의 현재 언어를 사용합니다. 2개 인수를 포함하는 함수는 두 번째 인수에서 전달한 언어를 사용합니다(지원되는 문화권: \\"zh-cn\\", \\"en-us\\", \\"ja-jp\\"). ","example":"=UserContext.NumberToWords(123.5); =UserContext.NumberToWords(981, \\"zh-CN\\")","label":"NumberToWords","syntax":"UserContext.NumberToWords(<Number>, <String>)"}},"dateTime":{"addDays":{"description":"날짜 간격(일)을 더한 결과인 날짜 및 시간 값을 반환합니다. 지정된 날짜 간격은 음수일 수 있습니다.","example":"=Fields!OrderDate.Value.AddDays(5)","label":"AddDays","syntax":"<DateTime>.AddDays(<Number>)"},"addHours":{"description":"시간 간격(시간)을 더한 결과인 날짜 및 시간 값을 반환합니다. 지정된 시간 간격은 음수일 수 있습니다.","example":"=Fields!OrderDate.Value.AddHours(12)","label":"AddHours","syntax":"<DateTime>.AddHours(<Number>)"},"addMilliseconds":{"description":"시간 간격(밀리초)을 더한 결과인 날짜 및 시간 값을 반환합니다. 지정된 시간 간격은 음수일 수 있습니다.","example":"=Fields!OrderDate.Value.AddMilliseconds(500)","label":"AddMilliseconds","syntax":"<DateTime>.AddMilliseconds(<Number>)"},"addMinutes":{"description":"시간 간격(분)을 더한 결과인 날짜 및 시간 값을 반환합니다. 지정된 시간 간격은 음수일 수 있습니다.","example":"=Fields!OrderDate.Value.AddMinutes(30)","label":"AddMinutes","syntax":"<DateTime>.AddMinutes(<Number>)"},"addMonths":{"description":"날짜 간격(월)을 더한 결과인 날짜 및 시간 값을 반환합니다. 지정된 날짜 간격은 음수일 수 있습니다.","example":"=Fields!OrderDate.Value.AddMonths(2)","label":"AddMonths","syntax":"<DateTime>.AddMonths(<Number>)"},"addSeconds":{"description":"시간 간격(초)을 더한 결과인 날짜 및 시간 값을 반환합니다. 지정된 시간 간격은 음수일 수 있습니다.","example":"=Fields!OrderDate.Value.AddSeconds(30)","label":"AddSeconds","syntax":"<DateTime>.AddSeconds(<Number>)"},"addYears":{"description":"날짜 간격(년)을 더한 결과인 날짜 및 시간 값을 반환합니다. 지정된 날짜 간격은 음수일 수 있습니다.","example":"=Fields!OrderDate.Value.AddYears(3)","label":"AddYears","syntax":"<DateTime>.AddYears(<Number>)"},"dateAdd":{"description":"지정된 단위의 날짜 및 시간 필드에 간격을 더한 결과인 날짜 및 시간 값을 반환합니다.","example":"=DateAdd(\\"d\\", 5, Fields!SaleDate.Value); =DateAdd(DateInterval.Day, 5, Fields!SaleDate.Value)","label":"DateAdd","syntax":"DateAdd(<DateInterval>, <Number>, <DateTime>)"},"dateDiff":{"description":"지정된 단위의 시작 날짜 및 시간과 종료 날짜 및 시간 간의 차이를 반환합니다.","example":"=DateDiff(\\"yyyy\\", Fields!SaleDate.Value, \\"1/1/2015\\"); =DateDiff(DateInterval.Year, Fields!SaleDate.Value, \\"1/1/2015\\")","label":"DateDiff","syntax":"DateDiff(<DateInterval>, <DateTime1>, <DateTime2>[, <DayOfWeek>[, <WeekOfYear>]])"},"datePart":{"description":"지정된 날짜의 지정된 부분을 나타내는 정수 값을 반환합니다.","example":"=DatePart(\\"m\\", Fields!SaleDate.Value)","label":"DatePart","syntax":"DatePart(<DateInterval>, <DateTime>[, <FirstDayOfWeek>[, <FirstWeekOfYear>]])"},"dateSerial":{"description":"시간 정보가 자정(00:00:00)으로 설정된 상태에서 지정된 연도, 월, 일을 나타내는 날짜 값을 반환합니다.","example":"=DateSerial(DatePart(\\"yyyy\\", Fields!SaleDate.Value) - 10, DatePart(\\"m\\", Fields!SaleDate.Value) + 5, DatePart(\\"d\\", Fields!SaleDate.Value) - 1)","label":"DateSerial","syntax":"DateSerial(<Year Number>, <Month Number>, <Day Number>)"},"dateString":{"description":"시스템의 현재 날짜를 나타내는 문자열 값을 반환합니다.","example":"=DateString(); =DatePart(\\"m\\", DateString())","label":"DateString","syntax":"DateString()"},"dateValue":{"description":"시간이 자정(00:00:00)으로 설정된 상태에서 문자열로 표현된 날짜의 정보를 포함하는 날짜 값을 반환합니다.","example":"=DateValue(\\"December 12, 2015\\")","label":"DateValue","syntax":"DateValue(<StringDate>)"},"day":{"description":"일을 나타내는 1부터 31 사이의 정수 값을 반환합니다.","example":"=Day(Fields!SaleDate.Value)","label":"Day","syntax":"Day(<DateTime>)"},"hour":{"description":"시간을 나타내는 0부터 23 사이의 정수 값을 반환합니다.","example":"=Hour(Fields!SaleDate.Value)","label":"Hour","syntax":"Hour(<DateTime>)"},"minute":{"description":"분을 나타내는 0부터 59 사이의 정수 값을 반환합니다.","example":"=Minute(Fields!SaleDate.Value)","label":"Minute","syntax":"Minute(<DateTime>)"},"month":{"description":"월을 나타내는 1부터 12 사이의 정수 값을 반환합니다.","example":"=Month(Fields!SaleDate.Value)","label":"Month","syntax":"Month(<DateTime>)"},"monthName":{"description":"날짜에 지정된 월의 이름을 문자열로 반환합니다.","example":"=MonthName(Fields!MonthNumber.Value)","label":"MonthName","syntax":"MonthName(<Month Number>[, <Abbreviate>])"},"now":{"description":"시스템의 현재 날짜와 시간을 반환합니다.","example":"=Now()","label":"Now","syntax":"Now()"},"parse":{"description":"지정된 문자열 값을 날짜 및 시간 값으로 변환합니다.","example":"=DateTime.Parse(\\"01/01/1970\\")","label":"DateTime.Parse","syntax":"DateTime.Parse(<String>[, <String>])"},"quarter":{"description":"분기를 나타내는 1부터 4 사이의 정수 값을 반환합니다.","example":"=Quarter(Fields!SaleDate.Value)","label":"Quarter","syntax":"Quarter(<DateTime>)"},"quarterName":{"description":"분기를 나타내는 문자열 값을 반환합니다.","example":"=QuarterName(Fields!SaleDate.Value)","label":"QuarterName","syntax":"QuarterName(<DateTime>)"},"second":{"description":"초를 나타내는 0부터 59 사이의 정수 값을 반환합니다.","example":"=Second(Fields!SaleDate.Value)","label":"Second","syntax":"Second(<DateTime>)"},"timeOfDay":{"description":"시스템의 현재 시간을 포함하는 날짜 값을 반환합니다.","example":"=TimeOfDay()","label":"TimeOfDay","syntax":"TimeOfDay()"},"timeSerial":{"description":"날짜 정보가 0001년 1월 1일을 기준으로 설정된 상태에서 지정한 시, 분, 초를 나타내는 날짜 값을 반환합니다.","example":"=TimeSerial(DatePart(\\"h\\", Fields!SaleDate.Value), DatePart(\\"n\\", Fields!SaleDate.Value), DatePart(\\"s\\", Fields!SaleDate.Value))","label":"TimeSerial","syntax":"TimeSerial(<Hour Number>, <Minute Number>, <Second Number>)"},"timeString":{"description":"시스템의 현재 시간을 나타내는 문자열 값을 반환합니다.","example":"=TimeString()","label":"TimeString","syntax":"TimeString()"},"timeValue":{"description":"날짜가 0001년 1월 1일로 설정된 상태에서 문자열로 표현된 시간의 정보를 포함하는 날짜 값을 반환합니다.","example":"=TimeValue(\\"15:25:45\\"); =TimeValue(Fields!SaleDate.Value)","label":"TimeValue","syntax":"TimeValue(<StringTime>)"},"timer":{"description":"자정 이후 경과 시간(초)을 나타내는 Double 값을 반환합니다.","example":"=Timer()","label":"Timer","syntax":"Timer()"},"today":{"description":"시스템의 현재 날짜를 포함하는 날짜 값을 반환합니다.","example":"=Today()","label":"Today","syntax":"Today()"},"weekday":{"description":"요일을 나타내는 숫자를 포함하는 정수 값을 반환합니다.","example":"=Weekday(Fields!SaleDate.Value, 0)","label":"Weekday","syntax":"Weekday(<DateTime>[, <DayOfWeek>])"},"weekdayName":{"description":"지정된 요일의 이름을 포함하는 문자열 값을 반환합니다.","example":"=WeekdayName(3, True, 0); =WeekDayName(DatePart(\\"w\\", Fields!SaleDate.Value), True, 0)","label":"WeekdayName","syntax":"WeekdayName(<WeekDay>[, <Abbreviate>[, <FirstDayOfWeek>]])"},"year":{"description":"연도를 나타내는 1부터 9999 사이의 정수 값을 반환합니다.","example":"=Year(Fields!SaleDate.Value)","label":"Year","syntax":"Year(<DateTime>)"}},"inspection":{"dbNull":{"description":"값이 DBNull 값인지 확인할 수 있게 합니다.","example":"=IIF(Fields!Organization.Value is DBNull.Value, \\"<NULL>\\", Fields!Organization.Value)","label":"DBNull.Value","syntax":"DBNull.Value"},"isArray":{"description":"식을 배열로 계산할 수 있으면 True를 반환합니다.","example":"=IsArray(Parameters!Initials.Value)","label":"IsArray","syntax":"IsArray(<Expression>)"},"isDBNull":{"description":"식이 null로 계산되면 True를 반환합니다.","example":"=IsDBNull(Fields!MonthlySales.Value)","label":"IsDBNull","syntax":"IsDBNull(<Expression>)"},"isDate":{"description":"식이 유효한 날짜 값을 나타내면 True를 반환합니다.","example":"=IsDate(Fields!BirthDate.Value); =IsDate(\\"31/12/2010\\")","label":"IsDate","syntax":"IsDate(<Expression>)"},"isError":{"description":"식이 오류로 평가되면 True를 반환합니다.","example":"=IsError(Fields!AnnualSales.Value = 80000)","label":"IsError","syntax":"IsError(<Expression>)"},"isNothing":{"description":"식이 nothing으로 평가되면 True를 반환합니다.","example":"=IsNothing(Fields!MiddleInitial.Value)","label":"IsNothing","syntax":"IsNothing(<Expression>)"},"isNumeric":{"description":"식을 숫자로 계산할 수 있으면 True를 반환합니다.","example":"=IsNumeric(Fields!AnnualSales.Value)","label":"IsNumeric","syntax":"IsNumeric(<Expression>)"}},"math":{"abs":{"description":"단정밀도 부동 소수점 숫자의 절댓값 또는 양수 값을 반환합니다.","example":"=Abs(-5.5); =Abs(Fields!YearlyIncome.Value - 80000)","label":"Abs","syntax":"Abs(<Number>)"},"acos":{"description":"지정한 수가 코사인인 각도를 반환합니다.","example":"=Acos(.5); =Acos(Fields!Angle.Value)","label":"Acos","syntax":"Acos(<Number>)"},"asin":{"description":"지정한 수가 사인인 각도를 반환합니다.","example":"=Asin(.5); =Asin(Fields!Angle.Value)","label":"Asin","syntax":"Asin(<Number>)"},"atan":{"description":"지정한 수가 탄젠트인 각도를 반환합니다.","example":"=Atan(.5); =Atan(Fields!Angle.Value)","label":"Atan","syntax":"Atan(<Number>)"},"atan2":{"description":"지정한 두 수의 몫이 탄젠트인 각도를 반환합니다.","example":"=Atan2(3,7); =Atan2(Fields!CoordinateY.Value, Fields!CoordinateX.Value)","label":"Atan2","syntax":"Atan2(<Number1>, <Number2>)"},"bigMul":{"description":"두 32비트 수의 곱을 반환합니다.","example":"=BigMul(4294967295,-2147483647); =BigMul(Fields!Int32Value.Value, Fields!Int32Value.Value)","label":"BigMul","syntax":"BigMul(<Number1>, <Number2>)"},"ceiling":{"description":"지정한 배정밀도 부동 소수점 숫자보다 크거나 같은 가장 작은 정수를 반환합니다.","example":"=Ceiling(98.4331); =Ceiling(Fields!AnnualSales.Value / 6)","label":"Ceiling","syntax":"Ceiling(<Number>)"},"cos":{"description":"지정한 배정밀도 부동 소수점 숫자보다 크거나 같은 가장 작은 정수를 반환합니다.","example":"=Cos(60)","label":"Cos","syntax":"Cos(<Number>)"},"cosh":{"description":"지정한 각도의 쌍곡 코사인을 반환합니다.","example":"=Cosh(60)","label":"Cosh","syntax":"Cosh(<Number>)"},"e":{"description":"E의 값 2.71828182845905를 반환합니다.","example":"=E * 2","label":"E","syntax":"E"},"exp":{"description":"e를 지정한 지수의 거듭제곱으로 반환합니다(e는 오일러 수). Log 함수의 역함수입니다.","example":"=Exp(3); =Exp(Fields!IntegerCounter.Value)","label":"Exp","syntax":"Exp(<Number>)"},"fix":{"description":"수의 정수 부분을 반환합니다.","example":"=Fix(-7.15); =Fix(Fields!AnnualSales.Value / -5)","label":"Fix","syntax":"Fix(<Number>)"},"floor":{"description":"지정한 배정밀도 부동 소수점 숫자보다 작거나 같은 가장 큰 정수를 반환합니다.","example":"=Floor(4.67); =Floor(Fields!AnnualSales.Value / 12)","label":"Floor","syntax":"Floor(<Number>)"},"ieeeRemainder":{"description":"IEEE 표준에 따라 한 숫자를 다른 숫자를 나눈 나머지를 반환합니다.","example":"=IEEERemainder(9, 8)","label":"IEEERemainder","syntax":"IEEERemainder(<Number1>, <Number2>)"},"log":{"description":"지정한 수의 로그값을 반환합니다.","example":"=Log(20.5); =Log(Fields!NumberValue.Value)","label":"Log","syntax":"Log(<Number>)"},"log10":{"description":"10을 밑으로 지정한 수의 로그값을 반환합니다.","example":"=Log10(20.5); =Log10(Fields!NumberValue.Value)","label":"Log10","syntax":"Log10(<Number>)"},"max":{"description":"지정된 식을 통해 null이 아닌 최댓값을 반환합니다.","example":"=Max(Fields!OrderTotal.Value)","label":"Max","syntax":"Max(<Values>)"},"min":{"description":"지정된 식을 통해 null이 아닌 최솟값을 반환합니다.","example":"=Min(Fields!OrderTotal.Value)","label":"Min","syntax":"Min(<Values>)"},"pi":{"description":"PI의 값 3.14159265358979를 반환합니다.","example":"=2 * PI * Fields!Radius.Value","label":"PI","syntax":"PI"},"pow":{"description":"다른 수만큼 거듭제곱한 한 수를 반환합니다.","example":"=Pow(Fields!Quantity.Value, 2)","label":"Pow","syntax":"Pow(<Number1>, <Number2>)"},"round":{"description":"10진수를 가장 가까운 정수 또는 지정된 숫자에 가장 가까운 10진수로 반올립니다.","example":"=Round(12.456); =Round(Fields!AnnualSales.Value / 12.3)","label":"Round","syntax":"Round(<Number>)"},"sign":{"description":"8비트 부호 있는 정수의 부호를 나타내는 값을 반환합니다.","example":"=Sign(Fields!AnnualSales.Value - 60000)","label":"Sign","syntax":"Sign(<Number>)"},"sin":{"description":"지정한 수의 사인을 반환합니다.","example":"=Sin(60)","label":"Sin","syntax":"Sin(<Number>)"},"sinh":{"description":"지정한 각도의 쌍곡 사인을 반환합니다.","example":"=Sinh(60)","label":"Sinh","syntax":"Sinh(<Number>)"},"sqrt":{"description":"지정한 수의 제곱근을 반환합니다.","example":"=Sqrt(121)","label":"Sqrt","syntax":"Sqrt(<Number>)"},"tan":{"description":"지정한 수의 탄젠트를 반환합니다.","example":"=Tan(60)","label":"Tan","syntax":"Tan(<Number>)"},"tanh":{"description":"지정한 각도의 쌍곡 탄젠트를 반환합니다.","example":"=Tanh(60)","label":"Tanh","syntax":"Tanh(<Number>)"},"truncate":{"description":"반올림하지 않고 소수점 뒤의 숫자를 제거하고 정수 값을 반환합니다.","example":"=Truncate(Fields!UnitPrice.Value)","label":"Truncate","syntax":"Truncate(<Number>)"}},"miscellaneous":{"getFields":{"description":"필드 컬렉션의 현재 내용을 포함하는 IDictionary<string,Field> 개체를 반환합니다. 데이터 영역 내에서 사용하는 경우에만 유효합니다. 이 함수를 사용하면 복잡한 조건을 처리하는 코드를 쉽게 작성할 수 있습니다. 동일한 함수를 GetFields() 없이 작성하려면 쿼리되는 각 필드 값을 여러 필드를 처리할 때 금지될 수 있는 메서드에 전달해야 합니다.","example":"=GetFields(); =Code.DisplayAccountID(GetFields())","label":"GetFields","syntax":"GetFields()"},"getLength":{"description":"지정된 배열의 요소 수를 반환합니다.","example":"=Parameters!MultiValueParameter.Value.GetLength(0)","label":"GetLength","syntax":"<Collection>.GetLength(<Number>)"},"groupIndex":{"description":"현재 그룹에 있는 요소의 인덱스를 반환합니다.","example":"=GroupIndex()","label":"GroupIndex","syntax":"GroupIndex()"},"groupIndexWithScope":{"description":"특정 그룹에 있는 요소의 인덱스를 반환합니다.","example":"=GroupIndex(Group1)","label":"GroupIndex (with scope)","syntax":"GroupIndex(<Group>)"},"inScope":{"description":"현재 값이 지정된 범위에 있는지 여부에 따라 true 또는 false로 평가됩니다.","example":"=InScope(\\"Order\\")","label":"InScope","syntax":"InScope(<Scope>)"},"indexOf":{"description":" 배열에서 주어진 요소를 찾을 수 있다면 첫 번째 인덱스를 반환하거나, 없으면 -1을 반환합니다.","example":"=IndexOf(Parameters!pContinent.Value, Fields!ContinentName.Value) >= 0","example_i11n":"{IndexOf(@pContinent, ContinentName) >= 0}","label":"IndexOf","syntax":"IndexOf(<Source>, <SearchElement>)"},"item":{"description":"Fields/Parameters/ReportItems에서 항목을 해당 이름으로 반환합니다.","example":"=Fields.Item(\\"Company Name\\").Name; =Parameters.Item(\\"Parameter1\\").Name; =ReportItems.Item(\\"TextBox1\\").Value","example_i11n":"{Fields.Item(\\"Company Name\\").Name}; {Parameters.Item(\\"Parameter1\\").Name}; {ReportItems.Item(\\"TextBox1\\").Value}","label":"Item","syntax":"<Object | Record>.Item(<String>)"},"join":{"description":"배열의 요소를 조인한 결과인 문자열을 요소 사이에 지정한 구분 기호를 사용하여 반환합니다.","example":"=Join(Parameters!MultiValueParameter.Value, \\", \\")","label":"Join","syntax":"Join(<Values>, <String>)"},"level":{"description":"현재 범위의 재귀 계층 구조에서 현재 깊이 수준을 나타내는 0부터 시작하는 정수를 반환합니다. 계층 구조의 첫 번째 수준은 0입니다.","example":"=Level()","label":"Level","syntax":"Level()"},"levelWithScope":{"description":"지정한 범위의 재귀 계층 구조에서 현재 깊이 수준을 나타내는 0부터 시작하는 정수를 반환합니다. 계층 구조의 첫 번째 수준은 0입니다.","example":"=Level(\\"Order\\")","label":"Level (with scope)","syntax":"Level(<Scope>)"},"lookup":{"description":"이름 및 값 쌍이 포함된 데이터 집합에서 지정된 이름과 일치하는 첫 번째 값을 반환합니다.","example":"=Lookup(Fields!ProductID.Value, Fields!ProductID.Value, Fields!Quantity.Value, \\"DataSet2\\")","label":"Lookup","syntax":"Lookup(<Source>, <Destination>, <Result>, <DataSet>)"},"lookupSet":{"description":"이름/값 쌍을 포함하는 데이터 집합에서 지정된 이름과 일치하는 값 집합을 반환합니다.","example":"=LookupSet(Fields!ProductID.Value, Fields!ProductID.Value, Fields!Quantity.Value, \\"DataSet2\\")","label":"LookupSet","syntax":"LookupSet(<Source>, <Destination>, <Result>, <DataSet>)"},"previous":{"description":"이전 데이터 행에 대해 식의 값을 계산합니다.","example":"=Previous(Fields!OrderID.Value)","label":"Previous","syntax":"Previous(<Value>)"},"previousWithScope":{"description":"지정된 범위 내에서 이전 데이터 행에 대한 표현식 값을 계산합니다.","example":"=Previous(Fields!OrderID.Value, \\"Order\\")","label":"Previous (with scope)","syntax":"Previous(<Expression>, <Scope>)"},"rowNumber":{"description":"모든 행의 실행 개수를 반환합니다.","example":"=RowNumber()","label":"RowNumber","syntax":"RowNumber()"},"rowNumberWithScope":{"description":"지정한 범위에서 모든 행의 실행 개수를 반환합니다.","example":"=RowNumber(\\"OrderID\\")","label":"RowNumber (with scope)","syntax":"RowNumber(<Scope>)"}},"programFlow":{"choose":{"description":"인수의 목록에서 값을 반환합니다.","example":"=Choose(3, \\"10\\", \\"15\\", \\"20\\", \\"25\\")","label":"Choose","syntax":"Choose(<Index>, <Value1>[, <Value2>,...[, <ValueN>]])"},"iif":{"description":"식이 True로 평가되면 첫 번째 값을 반환하고 식이 False로 평가되면 두 번째 값을 반환합니다.","example":"=IIF(Fields!AnnualSales.Value >= 80000, \\"Above Average\\", \\"Below Average\\")","label":"IIF","syntax":"IIF(<Condition>, <TruePart>, <FalsePart>)"},"partition":{"description":"지정한 간격을 기준으로 지정한 수를 포함하는 계산된 범위를 나타내는 문자열(x : y 형식)을 반환합니다. ","example":"=Partition(1999, 1980, 2000, 10)","label":"Partition","syntax":"Partition(<Value>, <Start>, <End>, <Interval>)"},"switch":{"description":"식 목록에서 True로 평가되는 첫 번째 수식의 값을 반환합니다.","example":"=Switch(Fields!FirstName.Value = \\"Abraham\\", \\"Adria\\", Fields!FirstName.Value = \\"Charelotte\\", \\"Cherrie\\")","label":"Switch","syntax":"Switch(<Condition1>, <Value1>[, <Condition2>, <Value2>,...[, <ConditionN>, <ValueN>]])"}},"text":{"contains":{"description":"문자열에 지정한 하위 문자열이 포함되어 있으면 True를 반환합니다.","example":"=Fields!ShipAddress.Value.Contains(\\"street\\")","label":"Contains","syntax":"<String>.Contains(<String>)"},"endsWith":{"description":"문자열이 지정한 하위 문자열로 끝나면 True를 반환합니다.","example":"=Fields!Description.Value.EndsWith(\\"documents\\")","label":"EndsWith","syntax":"<String>.EndsWith(<String>)"},"inStr":{"description":"문자열 내에서 지정한 하위 문자열이 처음으로 나타나는 시작 위치를 반환합니다.","example":"=InStr(Fields!Description.Value, \\"documents\\")","label":"InStr","syntax":"InStr(<String>, <String>)"},"lastIndexOf":{"description":"문자열 내에서 지정한 하위 문자열이 마지막으로 나타나는 위치의 인덱스를 반환합니다.","example":"=Fields!Description.Value.LastIndexOf(\\"documents\\")","label":"LastIndexOf","syntax":"<String>.LastIndexOf(<String>[, <Number>])"},"replace":{"description":"문자열 내에서 나타나는 처음 지정한 하위 문자열을 모두 지정한 두 번째 하위 문자열로 바꿉니다.","example":"=Fields!Description.Value.Replace(\\"documents\\", \\"invoices\\")","label":"Replace","syntax":"<String>.Replace(<String>, <String>)"},"startsWith":{"description":"문자열이 지정한 하위 문자열로 시작하면 True를 반환합니다.","example":"=Fields!Description.Value.StartsWith(\\"Invoice\\")","label":"StartsWith","syntax":"<String>.StartsWith(<String>)"},"substring":{"description":"지정한 위치(0부터 시작)의 하위 문자열을 지정한 길이로 반환합니다.","example":"=Fields!Description.Value.Substring(1, 10)","label":"Substring","syntax":"<String>.Substring(<Number>, <Number>)"},"toLower":{"description":"지정한 문자열을 소문자로 반환합니다.","example":"=Fields!ShipCountry.Value.ToLower()","label":"ToLower","syntax":"<String>.ToLower()"},"toUpper":{"description":"지정한 문자열을 대문자로 반환합니다.","example":"=Fields!ShipCountry.Value.ToUpper()","label":"ToUpper","syntax":"<String>.ToUpper()"},"trim":{"description":"지정한 문자열의 시작과 끝 모두에서 공백을 모두 제거한 후 해당 문자열을 반환합니다.","example":"=Parameters!Info.Value.Trim()","label":"Trim","syntax":"<String>.Trim()"},"trimEnd":{"description":"지정한 문자열의 끝에서 공백을 모두 제거한 후 해당 문자열을 반환합니다.","example":"=Parameters!Info.Value.TrimEnd()","label":"TrimEnd","syntax":"<String>.TrimEnd()"},"trimStart":{"description":"지정한 문자열의 시작에서 공백을 모두 제거한 후 해당 문자열을 반환합니다.","example":"=Parameters!Info.Value.TrimStart()","label":"TrimStart","syntax":"<String>.TrimStart()"}}},"titles":{"aggregate":"집계","conversion":"변환","dateTime":"날짜 & 시간","inspection":"검사","label":"일반 함수","math":"수학","miscellaneous":"기타","programFlow":"프로그램 흐름","text":"텍스트"}},"operations":{"info":{"arithmetic":{"add":{"description":"두 수의 합계를 계산하거나 두 문자열을 연결합니다.","example":"=Fields!Quantity.Value + 2","label":"+","syntax":"<Value1> + <Value2>"},"divide":{"description":"두 수를 나누고(분자를 분모로) 몫을 부동 소수점 숫자로 반환합니다.","example":"=Fields!AnnualSales.Value / 2","label":"/","syntax":"<Number1> / <Number2>"},"integerDivide":{"description":"두 수를 나누고 정수 결과를 반환합니다.","example":"=Fields!AnnualSales.Value \\\\ 2","label":"\\\\","syntax":"<Number1> \\\\ <Number2>"},"mod":{"description":"두 수를 나누고 나머지를 반환합니다.","example":"=Fields!AnnualSales.Value Mod 12","label":"Mod","syntax":"<Number1> Mod <Number2>"},"multiply":{"description":"두 수의 곱을 계산합니다.","example":"=Fields!Quantity.Value * 5","label":"*","syntax":"<Number1> * <Number2>"},"power":{"description":"다른 수만큼 거듭제곱한 한 수를 반환합니다.","example":"=Fields!Quantity.Value ^ 2","label":"^","syntax":"<Number1> ^ <Number2>"},"subtract":{"description":"두 수의 차이를 계산하거나 숫자 식의 값을 부정합니다.","example":"=Fields!Quantity.Value - 2","label":"-","syntax":"<Number1> - <Number2>"}},"bitShift":{"leftShift":{"description":"비트 패턴에 산술 왼쪽 시프트를 수행합니다.","example":"=Fields!RegionID.Value << 2","label":"<<","syntax":"<Number1> << <Number2>"},"rightShift":{"description":"비트 패턴에 산술 오른쪽 시프트를 수행합니다.","example":"=Fields!RegionID.Value >> 2","label":">>","syntax":"<Number1> >> <Number2>"}},"comparison":{"equal":{"description":"왼쪽 피연산자가 오른쪽 피연산자와 같으면 True를 반환합니다.","example":"=Fields!AnnualSales.Value = 80000","label":"=","syntax":"<Value1> = <Value2>"},"greaterThan":{"description":"왼쪽 피연산자가 오른쪽 피연산자보다 크면 True를 반환합니다.","example":"=Fields!AnnualSales.Value > 80000","label":">","syntax":"<Value1> > <Value2>"},"greaterThanOrEqual":{"description":"왼쪽 피연산자가 오른쪽 피연산자보다 크거나 같으면 True를 반환합니다.","example":"=Fields!AnnualSales.Value >= 80000","label":">=","syntax":"<Value1> >= <Value2>"},"is":{"description":"두 개체 참조를 비교하고 왼쪽 피연산자가 오른쪽 피연자와 동일하면 True를 반환합니다.","example":"=Fields!FirstName.Value Is Fields!LastName.Value","label":"Is","syntax":"<Value1> Is <Value2>"},"like":{"description":"두 문자열을 비교하고 왼쪽 피연산자가 오른쪽 피연자와 같으면 True를 반환합니다.","example":"=Fields!FirstName.Value Like \\"A*\\"","label":"Like","syntax":"<String1> Like <String2>"},"lowerThan":{"description":"왼쪽 피연산자가 오른쪽 피연산자보다 작으면 True를 반환합니다.","example":"=Fields!AnnualSales.Value < 80000","label":"<","syntax":"<Value1> < <Value2>"},"lowerThanOrEqual":{"description":"왼쪽 피연산자가 오른쪽 피연산자보다 작거나 같으면 True를 반환합니다.","example":"=Fields!AnnualSales.Value <= 80000","label":"<=","syntax":"<Value1> <= <Value2>"},"notEqual":{"description":"왼쪽 피연산자가 오른쪽 피연산자와 같지 않으면 True를 반환합니다.","example":"=Fields!AnnualSales.Value <> 80000","label":"<>","syntax":"<Value1> <> <Value2>"}},"concatenation":{"add":{"description":"두 수의 합계를 계산하거나 두 문자열을 연결합니다.","example":"=Fields!FirstName.Value + \\" \\" + Fields!LastName.Value","label":"+","syntax":"<String1> + <String2>"},"concat":{"description":"각각 문자열로 계산되는 두 식을 연결한 문자열 값을 반환합니다.","example":"=Fields!FirstName.Value & \\" \\" & Fields!LastName.Value","example_i11n":"{FirstName & \\" \\" & LastName}","label":"&","syntax":"<String1> & <String2>"}},"logicalBitwise":{"and":{"description":"두 T/F논리식의 논리 결합이나 두 숫자 식의 비트 결합을 반환합니다.","example":"=(Fields!AnnualSales.Value > 80000) And (Fields!Quantity.Value > 5)","label":"And","syntax":"<Value1> And <Value2>"},"andAlso":{"description":"첫 번째 식의 평가에서 결과를 제공하는 경우 다른 식의 평가를 건너뛰고 두 T/F논리식의 논리 결합을 반환합니다.","example":"=(Fields!AnnualSales.Value > 80000) AndAlso (Fields!Quantity.Value > 1)","label":"AndAlso","syntax":"<Boolean1> AndAlso <Boolean2>"},"not":{"description":"T/F논리식의 논리 부정이나 숫자 식의 비트 부정을 반환합니다.","example":"=Not (Fields!AnnualSales.Value > 80000)","label":"Not","syntax":"Not <Value>"},"or":{"description":"두 T/F논리식의 논리 분리나 두 숫자 식의 비트 분리를 반환합니다.","example":"=(Fields!AnnualSales.Value > 80000) Or (Fields!Quantity.Value > 5)","label":"Or","syntax":"<Value1> Or <Value2>"},"orElse":{"description":"두 T/F논리식 중 다른 식의 평가에서 결과를 제공하는 경우 한 식의 평가를 건너뛰고 두 T/F논리식의 논리 분리를 반환합니다.","example":"=(Fields!AnnualSales.Value > 80000) OrElse (Fields!Quantity.Value > 1)","label":"OrElse","syntax":"<Boolean1> OrElse <Boolean2>"},"xor":{"description":"두 T/F논리식의 논리 제외나 두 숫자 식의 비트 제외를 반환합니다.","example":"=(Fields!AnnualSales.Value > 80000) Xor (Fields!Quantity.Value) > 5","label":"Xor","syntax":"<Value1> Xor <Value2>"}}},"titles":{"arithmetic":"산술","bitShift":"비트 시프트","comparison":"비교","concatenation":"연결","label":"연산","logicalBitwise":"논리/비트"}},"parameters":{"titles":{"label":"매개변수"}},"reportItems":{"titles":{"label":"보고서 항목"}},"reportPartProperties":{"info":{"example":"=PartProperties!<PropertyName>.Value","example_i11n":"{PartProperties.<PropertyName>.Value}, {PartProperties!<PropertyName>.Value}"},"titles":{"label":"보고서 구성요소 속성"}},"slicers":{"titles":{"label":"Slicers"}},"textEncodingFields":{"titles":{"label":"텍스트 인코딩 필드"}},"theme":{"titles":{"color":"색","constant":"상수","font":"글꼴","image":"이미지","label":"테마","majorFont":"주 글꼴","minorFont":"보조 글꼴"}}}},{"lng":"ko","ns":"filters","resources":{"add":"추가...","addCriterion":"조건 추가","addGroup":"그룹 추가","addItem":"항목 추가","allOf":"모두","anyOf":"하나 이상","delete":"삭제","edit":"편집...","expressionText":"표현식...","listItemsCount_0":"{{count}}개 항목","newParameter":"새 매개변수","operators":{"beginsWith":"시작 문자","between":"Between","bottomN":"하위 N","bottomPercent":"하위 %","contains":"포함","doesNotBeginWith":"제외할 시작 문자","doesNotContain":"포함 안 함","equalTo":"같음","greaterThan":"보다 큼","greaterThanOrEqualTo":"보다 크거나 같음","in":"In","lessThan":"보다 작음","lessThanOrEqualTo":"보다 작거나 같음","like":"유사","notEqualTo":"같지 않음","notIn":"포함하지 않음","topN":"상위 N","topPercent":"상위 %"},"reset":"다시 설정"}},{"lng":"ko","ns":"glyphs-RPX","resources":{"barcode":{"textError":"Error","unsupportedSymbology":"[{{symbology}}] \\"{{itemName}}\\" preview is limited in design-time."}}},{"lng":"ko","ns":"groupEditor","resources":{"addGroup":{"btnAdjacentAfter":"뒤에 인접","btnAdjacentBefore":"앞에 인접","btnChild":"하위","btnParent":"상위"},"addTotal":{"btnAfter":"뒤쪽","btnBefore":"앞쪽"},"btnDelete":"삭제","btnDisableGroup":"그룹 사용 안 함","btnEditExpression":"표현식 편집","btnEnableGroup":"그룹 사용","headingAddGroup":"그룹 추가","headingAddTotal":"합계 추가","headingColumnGroups":"열 그룹","headingRowGroups":"행 그룹","labelAdvancedMode":"고급 모드","textHiddenStatic":"(정적)","textSelectTablix":"해당 그룹을 편집할 테이블릭스 선택"}},{"lng":"ko","ns":"labels","resources":{"body":"본문","dvchartPlotRuleNoCondition":"빈 조건","dvchartXAxis":"X 축","dvchartYAxis":"Y 축","footer":"푸터","header":"헤더","pageFooter":"페이지 바닥글","pageHeader":"페이지 머리글","total":"합계"}},{"lng":"ko","ns":"marginsSizes","resources":{"values":[{"_name":"없음","cm":{"bottom":"0cm","left":"0cm","right":"0cm","top":"0cm"},"in":{"bottom":"0in","left":"0in","right":"0in","top":"0in"}},{"_name":"좁게","cm":{"bottom":"1.25cm","left":"1.25cm","right":"1.25cm","top":"1.25cm"},"in":{"bottom":"0.5in","left":"0.5in","right":"0.5in","top":"0.5in"}},{"_name":"보통","cm":{"bottom":"2.5cm","left":"2.5cm","right":"2.5cm","top":"2.5cm"},"in":{"bottom":"1in","left":"1in","right":"1in","top":"1in"}},{"_name":"넓게","cm":{"bottom":"2.5cm","left":"5cm","right":"5cm","top":"2.5cm"},"in":{"bottom":"1in","left":"2in","right":"2in","top":"1in"}}]}},{"lng":"ko","ns":"nameTemplate-RPX","resources":{"Barcode":"Barcode","CheckBox":"CheckBox","CrossSectionBox":"CrossSectionBox","CrossSectionLine":"CrossSectionLine","Detail":"Detail","GroupFooter":"GroupFooter","GroupHeader":"GroupHeader","InputFieldCheckBox":"InputFieldCheckBox","InputFieldText":"InputFieldText","Label":"Label","Line":"Line","PageBreak":"PageBreak","PageFooter":"PageFooter","PageHeader":"PageHeader","Picture":"Picture","ReportFooter":"ReportFooter","ReportHeader":"ReportHeader","ReportInfo":"ReportInfo","RichTextBox":"RichTextBox","Shape":"Shape","Style":"Style","SubReport":"SubReport","TextBox":"TextBox","Unknown":"Unknown"}},{"lng":"ko","ns":"nameTemplates","resources":{"Continuous":"연속형섹션","Unknown":"아이템","bandedList":"밴드리스트","bandedListGroup":"그룹","barcode":"바코드","bullet":"글머리기호","calculatedField":"계산필드","categoryGroup":"카테고리영역","chart":"차트","checkbox":"체크박스","columnGroup":"컬럼그룹","container":"컨테이너","contentplaceholder":"컨텐츠자리표시자","dashboardSection":"섹션","dataset":"데이터집합","detailsGroup":"상세그룹","dvchart":"차트","dvchartEncodingField":"필드","dvchartPlot":"그림","dvchartoverlay":"오버레이","dvchartrule":"Rule","dvcharttextencoding":"텍스트","field":"필드","fixedPage":"고정페이지","formattedText":"포멧텍스트","group":"그룹","image":"이미지","inputField":"입력필드","label":"라벨","layer":"계층","line":"꺾은선형","list":"목록","mailMergeField":"필드","overflowPlaceholder":"오버플로우영역표시","parameter":"매개변수","parameterCollection":"파라메터","partItem":"구성요소항목","pointer":"포인트","reportParameter":"매개변수","reportPart":"보고서구성요소","reportPartProperty":"속성","reportSlicer":"리포트슬라이서","richtext":"서식있는텍스트","rowGroup":"행그룹","seriesGroup":"범례 그룹","seriesValue":"값","shape":"도형","sparkline":"스파크라인","subreport":"하위보고서","table":"테이블","tableDetailsGroup":"상세그룹","tableGroup":"테이블그룹","tableOfContents":"목차","tablix":"테이블릭스","textbox":"텍스트상자","tocLevel":"수준"}},{"lng":"ko","ns":"notifications","resources":{"addReportItem":{"caption":"\'{{reportItemType}}\' 형식의 보고서 항목을 {{containerType}}에 추가할 수 없습니다."},"contentPlaceholderSize":{"caption":"작업을 수행할 수 없습니다","text":"콘텐츠가 콘텐츠 자리표시자 크기를 초과합니다."},"contentPlaceholderSizeWithDetails":{"text":"콘텐츠가 콘텐츠 자리표시자 크기를 초과합니다. Please try manually reducing the size or position of {{items}} in the {{contentPlaceholder}}."},"convertTableForClient":{"text":"✘ 테이블 \'{{name}}\'은(는) 잘못된 구조가 있으며 테이블릭스로 변환되었습니다."},"deleteRowColumn":{"caption":"{{rowColumn}}을(를) 추가할 수 없습니다.","text":"{{itemType}} \'{{itemName}}\'의 결과가 잘못된 크기이므로 {{rowColumn}}을(를) 삭제할 수 없습니다."},"fixedPageSize":{"caption":"작업을 수행할 수 없습니다.","text":"보고서 항목이 페이지 크기를 초과할 수 없습니다."},"fplPasteFailed":{"caption":"붙여넣기 작업이 실패했습니다.","text":"보고서 항목을 붙여넣을 공간이 부족합니다."},"innerException":{"caption":"내부 예외가 발생했습니다."},"invalidPageSizes":{"caption":"이 작업으로 페이지 크기가 잘못됩니다."},"libraries":{"dataSetNotFound":{"text":"필수 데이터셋 \\"{{dataSetName}}\\"을(를) 찾을 수 없음"},"dataSourceNotFound":{"text":"필수 데이터 소스 \\"{{dataSourceName}}\\"을(를) 찾을 수 없음"},"duplicateLibrary":{"text":"Library with {{libProp}} \\"{{libValue}}\\" already exists. Library {{libProp}} must be unique"},"importFailed":{"caption":"라이브러리 \\"{{path}}\\" 가져오기 실패"},"invalidLibraryName":{"text":"Invalid library name \\"{{libName}}\\". Use alphanumeric characters and underscore only"},"loadingFailed":{"caption":"라이브러리 \\"{{path}}\\" 로드 실패"},"noDataSetsFound":{"text":"라이브러리에 데이터셋이 없습니다"},"nonMslLibrary":{"text":"MSL이 아닌 보고서를 라이브러리로 구문 분석할 수 없습니다"},"unknownLibrary":{"caption":"리포트가 의존하고 있는 라이브러리를 알 수 없음","text":"이 보고서에 사용된 라이브러리 \\"{{libName}}\\"을(를) 찾을 수 없습니다."}},"listRowsOrColumnsCount":{"caption":"Action cannot be performed","text":"Content exceeds list column/row size"},"lockLayout":{"caption":"보고서 레이아웃이 잠겨 있습니다.","text":"보고서 항목 크기 조정, 이동, 삭제, 추가와 레이아웃 속성 변경이 제한됩니다."},"masterReports":{"dataSetsWereRenamed":{"caption":"Data sets were renamed","text":"Data sets which conflict with those in the master report were renamed: {{dataSetNames}}."},"dataSourcesWereRenamed":{"caption":"Data sources were renamed","text":"Data sources which conflict with those in the master report were renamed: {{dataSourceNames}}."},"embeddedImagesWereRenamed":{"caption":"Embedded Images were renamed","text":"Embedded Images which conflict with those in the master report were renamed: {{embeddedImagesNames}}."},"layersWereRenamed":{"caption":"Layers were renamed","text":"Layers which conflict with those in the master report were renamed: {{layersNames}}."},"parametersWereRenamed":{"caption":"Report parameters were renamed","text":"Parameters which conflict with those in the master report were renamed: {{parameterNames}}."},"reportItemsWereMovedOrResized":{"caption":"Report items were moved or resized","details":"{{num}}. {{items}} in the {{contentPlaceholder}}\\n","text":"The items in the content placeholders moved or resized to fit into the updated content placeholders of master report:\\n{{outsideItems}}"},"reportItemsWereRenamed":{"caption":"Report items were renamed","text":"Report items which conflict with those in the master report were renamed: {{reportItemNames}}."}},"pasteFailed":{"caption":"붙여넣을 유효하지 않은 항목: {{elementName}}","text":"고정 페이지 보고서에만 오버플로우 영역 표시를 붙여넣을 수 있습니다."},"pasteFailedDecode":{"caption":"붙여넣는 동안 디코딩 오류: {{reason}}"},"pasteWarningDecode":{"caption":"붙여넣는 동안 디코딩 경고","text":"{{warning}}"},"removeDefaultLayer":{"caption":"기본 레이어를 제거할 수 없습니다."},"removeLastValidValue":{"caption":"바인딩된 매개변수의 마지막 유효한 값은 제거할 수 없습니다."},"transaction":{"caption":"트랜잭션 실패: {{innerExceprion}}"}}},{"lng":"ko","ns":"pageSizes","resources":{"values":[{"_name":"Letter","cm":{"height":"27.9cm","width":"21.6cm"},"in":{"height":"11in","width":"8.5in"}},{"_name":"Tabloid","cm":{"height":"43.2cm","width":"27.9cm"},"in":{"height":"17in","width":"11in"}},{"_name":"Legal","cm":{"height":"35.6cm","width":"21.6cm"},"in":{"height":"14in","width":"8.5in"}},{"_name":"Executive","cm":{"height":"26.7cm","width":"18.4cm"},"in":{"height":"10.5in","width":"7.25in"}},{"_name":"A3","cm":{"height":"42cm","width":"29.7cm"},"in":{"height":"16.54in","width":"11.69in"}},{"_name":"A4","cm":{"height":"29.7cm","width":"21cm"},"in":{"height":"11.69in","width":"8.27in"}},{"_name":"A5","cm":{"height":"21cm","width":"14.8cm"},"in":{"height":"8.27in","width":"5.83in"}},{"_name":"A6","cm":{"height":"14.8cm","width":"10.5cm"},"in":{"height":"5.83in","width":"4.13in"}},{"_name":"B4 (JIS)","cm":{"height":"36.4cm","width":"25.7cm"},"in":{"height":"14.33in","width":"10.12in"}},{"_name":"B5 (JIS)","cm":{"height":"25.7cm","width":"18.2cm"},"in":{"height":"10.12in","width":"7.17in"}},{"_name":"B6 (JIS)","cm":{"height":"18.2cm","width":"12.8cm"},"in":{"height":"7.17in","width":"5.04in"}},{"_name":"B5 (ISO)","cm":{"height":"25cm","width":"17.6cm"},"in":{"height":"9.84in","width":"6.93in"}}]}},{"lng":"ko","ns":"parametersViewEditor","resources":{"alignmentEnum":{"bottom":"아래쪽","center":"가운데","horizontal":"가로","justify":"양쪽 맞춤","left":"왼쪽","none":"없음","right":"오른쪽","top":"위쪽","vertical":"세로"},"bindingAdorner":{"headingBinding":"바인딩","textUnspecified":"지정되지 않음"},"bindingProperties":{"any":"모두","boolean":"T/F논리식","date":"날짜","dateDateTime":"날짜, 날짜/시간","integerFloat":"정수, Float"},"booleanProperties":{"checkbox":"체크박스","falseText":"False","radio":"라디오","toggle":"토글","trueText":"True","undefinedText":"정의되지 않음"},"boundParameter":{"allowBlank":"빈 값 허용","defaultValue":"기본값","multiValue":"다중 값","multiline":"여러 줄","nullable":"Null가능","parameter":"{{name}} 매개 변수","text":"매개 변수 바인딩"},"buttonProperties":{"clear":"지우기","preview":"미리보기","reset":"다시 설정"},"canvas":"캔버스","dataType":{"boolean":"T/F논리식","date":"날짜","dateTime":"날짜/시간","float":"소수","integer":"정수","string":"문자열"},"dateRangeParameterLabels":{"monthYearOrder":"M-Y","placeholderDateEnd":"종료","placeholderDateStart":"시작","shortcuts":{"labelLastMonth":"지난달","labelLastWeek":"지난주","labelLastYear":"지난해","labelMonthToDate":"월간 누계","labelWeekToDate":"주간 누계","labelYearToDate":"연간 누계"},"tabLabelAnnually":"연간","tabLabelDaily":"매일","tabLabelMonthly":"월간","textBack":"달력으로 돌아가기","textShortcutsList":"공통 범위"},"dateTimeParameterLabels":{"monthYearOrder":"M-Y","textBack":"달력으로 돌아가기","textClear":"지우기","textToday":"오늘"},"dateTimeRange":{"rangeTypes":{"Current_Day":"현재 일","Current_Hour":"현재 시간","Current_Minute":"현재 분","Current_Month":"현재 월","Current_Quarter":"현재 분기","Current_Week":"현재 주","Current_Year":"현재 연도","LastSimple_Day":"지난 날","LastSimple_Hour":"지난 시간","LastSimple_Minute":"지난 분","LastSimple_Month":"지난 달","LastSimple_Quarter":"지난 분기","LastSimple_Second":"지난 초","LastSimple_Week":"지난 주","LastSimple_Year":"지난 해","LastToDateSimple_Day":"지난 일 누계","LastToDateSimple_Month":"지난 달 누계","LastToDateSimple_Quarter":"지난 분기 누계","LastToDateSimple_Week":"지난 주 누계","LastToDateSimple_Year":"작년 누계","LastToDate_Day_0":"지난 {{count}} 일 누계","LastToDate_Month_0":"지난{{count}} 월 누계","LastToDate_Quarter_0":"지난 {{count}} 분기 누계","LastToDate_Week_0":"지난 {{count}} 주 누계","LastToDate_Year_0":"지난 {{count}} 년 누계","Last_Day_0":"지난 {{count}} 일","Last_Hour_0":"지난 {{count}} 시간","Last_Minute_0":"지난 {{count}} 분","Last_Month_0":"지난 {{count}} 월","Last_Quarter_0":"지난 {{count}} 분기","Last_Second_0":"지난 {{count}} 초","Last_Week_0":"지난 {{count}} 주","Last_Year_0":"지난 {{count}} 년","NextSimple_Day":"다음 날","NextSimple_Hour":"다음 시간","NextSimple_Minute":"다음 분","NextSimple_Month":"다음 달","NextSimple_Quarter":"다음 분기","NextSimple_Second":"다음 초","NextSimple_Week":"다음 주","NextSimple_Year":"다음 해","Next_Day_0":"다음 {{count}} 일","Next_Hour_0":"다음 {{count}} 시간","Next_Minute_0":"다음 {{count}} 분","Next_Month_0":"다음 {{count}} 월","Next_Quarter_0":"다음 {{count}} 분기","Next_Second_0":"다음 {{count}} 초","Next_Week_0":"다음 {{count}} 주","Next_Year_0":"다음 {{count}} 년","ToDate_Day":"일간 누계","ToDate_Month":"월간 누계","ToDate_Quarter":"분기 누계","ToDate_Week":"주간 누계","ToDate_Year":"연간 누계"}},"dateViewProperties":{"accent":"강조","days":"일","default":"기본값","error":"오류","months":"월","none":"없음","warning":"경고","years":"년"},"editors":{"nameBoolean":"T/F논리식 편집기","nameButton":"버튼","nameDateRange":"날짜 범위 편집기","nameDateTime":"날짜/시간 편집기","nameDateTimeRange":"날짜/시간 범위 편집기","nameDropdown":"드롭다운 편집기","nameHeading":"제목","nameList":"목록 편집기","nameMultivalue":"다중값 편집기","nameNumber":"숫자 편집기","nameNumberRange":"숫자 범위 편집기","namePlainText":"일반 텍스트","nameText":"텍스트 편집기","nameTreeView":"트리 보기","textCheckRangeType":"두 매개 변수의 데이터 형식이 같아야 합니다.","textHeading":"제목 텍스트","textNull":"Null"},"fieldsProperties":{"addBtnText":"추가","addBtnTitle":"항목 추가","closeBtnTitle":"종료","collectionIsEmpty":"컬렉션이 비어 있습니다.","items":"항목","showItem":"항목 표시"},"labels":{"removeDefaultValue":"기본값 제거"},"msgString":{"controlNotSupportParameters":"이 컨트롤은 사용 가능한 값이 정의된 매개 변수를 지원하지 않습니다.","controlNotSupportSingle":"이 컨트롤에서는 단일 값 매개변수를 지원하지 않습니다.","controlRequires":"이 컨트롤에는 사용 가능한 값 목록이 필요합니다.","notSupControl":"이 컨트롤에서 지원되지 않습니다."},"parameters":{"labelBoolean":"T/F논리식 매개 변수","labelDateRange":"날짜 범위 매개 변수","labelDateTime":"날짜/시간 매개 변수","labelDateTimeRange":"DateTimeRange 매개변수","labelDropdown":"드롭다운 매개 변수","labelHierarchical":"계층 구조 매개 변수","labelList":"목록 매개 변수","labelMultivalue":"다중값 파라메터","labelNumber":"숫자 매개 변수","labelNumberRange":"숫자 범위 매개 변수","labelSingleLineParameter":"한 줄 매개 변수"},"parametersPanel":{"headingEditParameter":"매개 변수 편집","headingEditRange":"범위 편집","headingEditable":"표시","headingHidden":"숨김","titleEditParameter":"파라메터 편집..."},"parametersText":"매개 변수","placeholderEmpty":"<비어 있음>","placeholderMultipleValues":"<여러 값>","properties":{"categories":{"advanced":"고급","appearance":"모양","binding":"바인딩","common":"공통","details":"세부 정보","label":"레이블","locationAndSize":"위치 및 크기","preview":"미리보기"},"labels":{"action":"작업","alignment":"맞춤","amount":"금액","anchorDate":"앵커 날짜","background":"배경","binding":"바인딩","color":"색","columns":"열","daysHeaderFormat":"일 머리글 형식","display":"표시","dropdown":"드롭다운","endpoint":"끝점","endpointStates":{"textFalse":"제외","textTrue":"포함"},"falseText":"\\"False\\" 텍스트","fields":"필드","from":"시작","groupBy":"분류 방법","height":"높이","label":"라벨","layout":"레이아웃","left":"왼쪽","list":"목록","max":"최댓값","maxRange":"최대 범위","min":"최솟값","mode":"모드","multiline":"여러 줄","offset":"오프셋","pathString":"경로 문자열","placeholder":"값","placeholderFrom":"\\"From\\",자리표시자","placeholderTo":"\\"To\\",자리표시자","range":"범위","ranges":"범위","recursive":"재귀","roundInputToStep":"단계적으로 입력 반올림","showDefaultRanges":{"label":"기본 범위","textFalse":"숨김","textTrue":"보임"},"slider":"슬라이더","step":"단계","strikeThrough":"취소선","text":"텍스트","to":"To","top":"위쪽","trueText":"\\"True\\" 텍스트","type":"유형","unit":"단위","upDownEditor":"위-아래 편집기","value":"값","viewMode":"보기 모드","width":"너비"}},"propertiesText":"속성","propertyGrid":{"btnCloseSearch":"종료","placeholderSearch":"여기에 속성 이름 입력...","textEmptyList":"속성을 확인할 항목 선택","textNoCommonProperties":"공통 속성이 없습니다.","textUnknownProperty":"알 수 없는 속성:"},"sideBar":{"collapse":"축소","expand":"확장"},"surface":{"btnAutoGenerate":"자동 생성","btnGenerate":"생성","emptySurfaceBlock":{"textDescriptionButtonAfter":"사용 가능한 매개 변수에서 가져오거나 도구 상자의 컨트롤을 사용하여 새로 시작합니다.","textDescriptionButtonBefore":"다음을 수행할 수 있습니다.","textDescriptionOne":"이 보고서에는 맞춤 매개 변수보기가 없습니다.","textDescriptionTwo":"도구 상자의 컨트롤을 사용하여 만들 수 있습니다."},"messageBlock":{"textDescriptionButtonAfter":"해당 항목을 위한 편집기입니다.","textDescriptionButtonBefore":"다음을 수행할 수 있습니다.","textDescriptionOne":"일부 보고서 매개 변수가 바인딩되지 않았습니다."},"scrollBar":{"textMessagesOne":"매개 변수는 뷰어의 사이드 바에 표시 될 때 이러한 방식으로 표시됩니다.","textMessagesTwo":"구성 요소를 끌어 순서를 변경할 수 있습니다."}},"toolbar":{"btnDelete":"삭제","btnDuplicate":"중복","btnGenerateView":"보기 생성","btnHighlightRequired":"강조 표시 필요","btnLayoutFreeForm":"자유 양식","btnLayoutStack":"스택","btnRemoveView":"보기 제거","btnResetView":"보기 다시 설정"}}},{"lng":"ko","ns":"properties-RPX","resources":{"categories":{"appearance":"Appearance","behavior":"Behavior","border":"Border","common":"Common","data":"Data","design":"Design","format":"Format","layout":"Layout","misc":"Misc","page":"Page","pdf":"PDF","printing":"Printing","summary":"Summary","text":"Text","watermark":"Watermark"},"labels":{"MultiLine":"Multiline","alignment":"Alignment","anchorBottom":"Anchor Bottom","angle":"Angle","autoReplaceFields":"Auto Replace Fields","autoSize":"Auto Size","backColor":"Background Color","backColor2":"Background Color 2","backgroundPattern":"Background Pattern","backgroundStyle":"Background Style","barCodeStyle":"Style","barHeight":"Bar Height","barWidth":"Bar Width","border":{"color":"테두리 색상","style":"테두리 스타일","width":"테두리 너비"},"calcFieldDefaultValue":"Default Value","calcFieldFormula":"Formula","calcFieldName":"Name","calcFieldType":"Type","calendar":"Calendar","canGrow":"Can Grow","canShrink":"Can Shrink","captionGrouping":"Caption Grouping","captionPosition":"Caption Position","characterSpacing":"Character Spacing","checkAlignment":"Check Alignment","checkSize":"크기 확인","checkStyle":"스타일 확인","checkSumEnabled":"Checksum Enabled","checked":"Checked","colKeepTogether":"Column Group Keep Together","collate":"Collate","columnCount":"Column Count","columnDirection":"Column Direction","columnLayout":"Column Layout","columnSpacing":"Column Spacing","control":{"bottom":"Bottom","dataField":"Data Field","height":"Height","left":"Left","name":"Name","right":"Right","tag":"Tag","top":"Top","visible":"Visible","width":"Width"},"countNullValues":"Count Null Values","crossSectionBoxRadius":"Radius","culture":"Culture","dataField":"Data Field","description":"Description","distinctField":"Distinct Field","duplex":"Duplex","enabled":"Enabled","expressionErrorMessage":"Expression Error Message","fieldName":"필드 이름","fontFamily":"Font Family","fontLinethrough":"Text Strikethrough","fontSize":"Font Size","fontStyle":"Font Style","fontUnderline":"Text Underline","fontWeight":"Font Weight","foreColor":"Fore Color","formatString":"Format String","gradientStyle":"Gradient Style","groupKeepTogether":"Group Keep Together","gutterMargin":"Gutter","height":"Height","htmlValue":"HTML Value","hyperLink":"Hyperlink","image":"Image","keepTogether":"Keep Together","lineColor":"Line Color","lineSpacing":"Line Spacing","lineStyle":"Line Style","lineWeight":"Line Weight","margin":"Margin","maxLength":"Max Length","maxPages":"Max Pages","minCondenseRate":"Min Condense Rate","mirrorMargins":"Mirror Margins","multiLine":"Multiline","name":"Name","newColumn":"New Column","newPage":"New Page","nwratio":"NW Ratio","orientation":"Orientation","outputFormat":"Output Format","padding":"Padding","paperHeight":"Paper Height","paperSize":"Paper Size","paperWidth":"Paper Width","paramUI":"Show Parameters UI","parameterDefaultValue":"Default Value","parameterFormat":"Format","parameterName":"Name","parameterParameterType":"Parameter Type","parameterPrompt":"Prompt","parameterPromptUser":"Prompt User","password":"비밀번호","pictureAlignment":"Picture Alignment","printAtBottom":"Print At Bottom","printWidth":"Print Width","quietZoneBottom":"Quiet Zone Bottom","quietZoneLeft":"Quiet Zone Left","quietZoneRight":"Quiet Zone Right","quietZoneTop":"Quiet Zone Top","radius":{"default":"Default"},"readonly":"읽기 전용","repeatStyle":"Repeat Style","repeatToFill":"Repeat To Fill","reportName":"Report Name","required":"필수","rightToLeft":"Right to Left","rotation":"Rotation","roundingRadius":"Rounding Radius","script":"Script","scriptLanguage":"Script Language","shape":"Shape","sizeMode":"Size Mode","spellCheck":"맞춤법 확인","style":{"backColor":"Background Color","className":"Class Name","ddoCharSet":"Script","fontFamily":"Font Family","fontLinethrough":"Text Strikethrough","fontSize":"Font Size","fontStyle":"Font Style","fontUnderline":"Text Underline","fontWeight":"Font Weight","foreColor":"Color","kinsoku":"Kinsoku","shrinkToFit":"Shrink To Fit","styleName":"Name","textAlign":"Text Alignment","textJustify":"Text Justify","verticalAlign":"Vertical Alignment","verticalText":"Vertical Text","wrapMode":"Wrap Mode"},"styles":"Styles","summaryFunc":"Summary Func","summaryGroup":"Summary Group","summaryRunning":"Summary Running","summaryType":"Summary Type","supplementBarHeight":"Supplement Bar Height","supplementCaptionPosition":"Supplement Caption Position","supplementDataField":"Supplement Data Field","supplementSpacing":"Supplement Spacing","supplementText":"Supplement Text","tabIndex":"탭 인덱스","tag":"Tag","text":"Text","title":"Title","underlayNext":"Underlay Next","userData":"User Data","version":"Version","visible":"Visible","watermarkAlignment":"Alignment","watermarkImage":"Image","watermarkPrintOnPages":"Print On Pages","watermarkSizeMode":"Size Mode","x1":"X1","x2":"X2","y1":"Y1","y2":"Y2"}}},{"lng":"ko","ns":"propertyDescriptors","resources":{"categories":{"action":"작업","appearance":"모양","availableValues":"사용 가능한 값","background":"배경","backgroundAndBorders":"배경 및 테두리","bar":"막대 설정","border":"테두리","borderColor":"테두리 색","borderRoundingRadius":"궁근 테두리 반경","borderStyle":"테두리 스타일","borderWidth":"테두리 너비","borders":"테두리","column":"열","common":"공통","configurations":"구성","content":"내용","data":"데이터","dataLabel":"데이터 레이블","dataLabelText":"데이터 레이블 텍스트","defaultValue":"기본값","dimensions":"치수","documentMap":"문서 구조","dvchartLabelBorder":"레이블 테두리","dvchartLabelLine":"레이블 선","dvchartLabelText":"레이블 텍스트","dvchartLegend":"범례","dvchartLegendBackground":"범례 배경","dvchartLegendBorder":"범례 테두리","dvchartLegendText":"범례 텍스트","dvchartLegendTitle":"범례 제목","dvchartPlotColorEncodings":"인코딩 - 색상","dvchartPlotConfig":"구성","dvchartPlotEncodings":"인코딩","dvchartPlotShapeEncodings":"인코등 - 모양","dvchartPlotSizeEncodings":"인코딩 - 사이즈","dvchartPlotStyle":"스타일","dvchartPlotSymbols":"기호","fields":"필드","fillStyle":"채우기 스타일","font":"글꼴","general":"일반","grid":"눈금선","group":"그룹","input":"입력","international":"국제","labelStyle":"레이블 스타일","labels":"레이블","layout":"레이아웃","line":"꺾은선형","majorGrid":"주 눈금선","majorTicks":"주 눈금","margins":"여백","marker":"표식","minorGrid":"보조 눈금선","minorTicks":"보조 눈금","misc":"기타","noData":"데이터 없음","options":"옵션","pageSize":"페이지 크기","preview":"미리보기","range":"범위","referenceLine":"참조 선","scale":"배율","seriesLineStyle":"계열 선 스타일","settings":"설정","staticMembers":"정적 멤버","symbology":"기호","tableFooter":"바닥글","tableHeader":"머리글","targetDevice":"대상 장치","targetStyle":"대상 스타일","text":"텍스트","threeDProperties":"3D 속성","tickStyle":"눈금 스타일","title":"제목","userSort":"사용자 정렬","valueStyle":"값 스타일","visibility":"표시 여부"},"labels":{"action":{"applyParameters":"파라메터 적용","jumpToBookmark":"책갈피로 이동","jumpToReport":"보고서로 이동","jumpToUrl":"URL로 이동","parameters":"매개변수","slice":"Slicers"},"bandedList":{"canGrow":"확장 가능","canShrink":"축소 가능","consumeWhiteSpace":"공백 사용","preventOrphanedFooter":"분리된 바닥글 방지","preventOrphanedHeader":"분리된 머리글 방지","printAtBottom":"맨 아래에 인쇄","repeatOnNewPage":"새 페이지에서 반복"},"barcode":{"aztecOptions":{"encoding":"인코딩","errorCorrection":"오류 정정","layers":"레이어"},"barHeight":"막대 높이","captionGrouping":"캡션 그룹화","captionLocation":"캡션 위치","checksum":"체크섬","code49Options":{"groupNumber":"그룹 번호","grouping":"그룹화"},"dataMatrixOptions":{"ecc000_140SymbolSize":"Ecc000_140 기호 크기","ecc200EncodingMode":"Ecc200 인코딩 모드","ecc200SymbolSize":"Ecc200 기호 크기","eccMode":"Ecc 모드","encoding":"인코딩","encodingMode":"인코딩 모드","fileIdentifier":"파일 식별자","structureNumber":"구조 번호","structuredAppend":"구조화된 추가","symbolSize":"기호 크기"},"ean128Fnc1Options":{"barAdjust":"막대 조정","moduleSize":"모듈 크기","resolution":"DPI"},"gs1CompositeOptions":{"type":"복합 형식","value":"복합 값"},"gs1QrCodeOptions":{"encoding":"인코딩","errorLevel":"오류 수준","mask":"마스크","version":"버전"},"invalidBarcodeText":"잘못된 바코드 텍스트","maxiCodeOptions":{"mode":"모드"},"microPdf417Options":{"compactionMode":"압축 모드","fileId":"파일 ID","segmentCount":"세그먼트 수","segmentIndex":"세그먼트 인덱스","version":"버전"},"microQrCodeOptions":{"encoding":"인코딩","errorLevel":"오류 수준","mask":"마스크","version":"버전"},"narrowBarWidth":"좁은 막대 너비","nwRatio":"NW 비율","nwRatio_help":{"text":"N 치수라고도 하는 이 값은 너비가 2개뿐인 막대를 포함하는 기호에서 좁은 막대와 넓은 막대 간 비율의 배수를 정의합니다."},"pdf417Options":{"columns":"열","errorCorrectionLevel":"오류 수정 수준","rows":"행","type":"PDF417 형식"},"qrCodeOptions":{"connection":"연결","connectionNumber":"연결 번호","encoding":"인코딩","errorLevel":"오류 수준","mask":"마스크","model":"모델","version":"버전"},"quietZone":"자동 영역","rotation":"회전","rssExpandedStacked":{"rowCount":"행 수"},"supplementOptions":{"barHeight":"부가 막대 높이","captionLocation":"부가 캡션 위치","spacing":"부가 간격","value":"부가 값"},"symbology":"유형","value":"값"},"border":{"bottom":"아래쪽","color":"색","default":"기본값","left":"왼쪽","right":"오른쪽","style":"스타일","top":"위쪽","width":"너비"},"bullet":{"bestValue":"최상위 값","interval":"간격","labelFontColor":"글꼴 색","labelFontFamily":"글꼴 종류","labelFontSize":"글꼴 크기","labelFontStyle":"글꼴 스타일","labelFormat":"레이블 형식","orientation":"방향","range1Boundary":"범위 1 경계","range2Boundary":"범위 2 경계","showLabels":"레이블 표시","targetLineColor":"색","targetLineWidth":"선 두께","targetShape":"도형","targetValue":"대상 값","tickMarks":"눈금","ticksLineColor":"색","ticksLineWidth":"선 두께","value":"값","valueColor":"값 색","worstValue":"최하위 값"},"checkBox":{"checkAlignment":"맞춤 확인","checked":"선택됨","text":"텍스트"},"container":{"canGrow":"확장 가능","consumeWhiteSpace":"공백 사용","gridMode":"그리드 모드","linkToChild":"자식에 연결","newPage":"새 페이지","overflow":"오버플로우","pageBreak":"페이지 나누기"},"contentPlaceHolder":{"consumeWhiteSpace":"공백 사용","text":"텍스트"},"dashboardSection":{"displayName":"디스플레이 이름"},"data":{"dataElementName":"요소 이름","dataElementOutput":"요소 출력","dataElementStyle":"요소 스타일"},"dataRegion":{"dataSetName":"데이터 집합 이름","dataSetParameters":"데이터 집합 매개변수","dataSetParameters_help":{"text":"DataSetParameters 속성에 매개변수를 설정하면 다른 데이터 집합에 바인딩된 중첩된 데이터 영역에 데이터를 표시하기 위한 관계를 추가할 수 있습니다."},"filters":"필터","newPage":"새 페이지","newSection":"새 섹션","noRowsMessage":"메시지","overflowName":"오버플로우 이름","pageBreak":"페이지 나누기","repeatToFill":"채우기 반복","sortExpressions":"정렬 식","throwIfPlaceHoldersEmpty":"자리 표시자가 비어 있는 경우 throw"},"dataSet":{"accentSensitivity":"악센트 구분","boundFields":"바인딩된 필드","calculatedFields":"계산된 필드","caseSensitivity":"대/소문자 구분","collation":"데이터 정렬","commandType":"명령 유형","fields":"필드","filters":"필터","kanatypeSensitivity":"일본어 가나 구분","name":"이름","parameters":"매개변수","query":"쿼리","widthSensitivity":"전자/반자 구분"},"dataSetParameter":{"name":"이름","value":"값"},"dataVisualizer":{"colorScale":{"maximum":"최댓값","maximumColor":"최대 색","middle":"가운데","middleColor":"중간 색","minimum":"최소값","minimumColor":"최소 색","useMiddleColor":"중간 색 사용","value":"값"},"dataBar":{"alternateColor":"대체 색","color":"색","maximum":"최댓값","minimum":"최솟값","useAlternateColor":"대체 색 사용","useAlternateColor_help":{"text":"설정할 경우 \'값\'이 \'0 값\'보다 작으면 \'대체 색\'이 사용됩니다."},"value":"값","zeroValue":"0 값"},"gradient":{"color1":"색 1","color2":"색 2","gradientType":"그라데이션 유형"},"hatch":{"color1":"색 1","color2":"색 2","hatchStyle":"Hatch 스타일"},"iconSet":{"icon1Value":"아이콘 1 값","icon2Value":"아이콘 2 값","icon3Value":"아이콘 3 값","icon4Value":"아이콘 4 값","icon5Value":"아이콘 5 값","iconSet":"아이콘 집합"},"rangeBar":{"color":"색","displayProgressIndicator":"진행률 표시기 표시","length":"길이","maximum":"최대값","minimum":"최소값","progressIndicatorColor":"진행률 표시기 색","progressIndicatorLength":"진행률 표시기 길이","startingValue":"시작 값"},"visualizerType":"데이터 시각화 유형"},"dimensions":{"bottom":"아래쪽","endPointX":"끝점 X","endPointY":"끝점 Y","fixedHeight":"고정 높이","fixedSize":"고정 크기","fixedWidth":"고정 너비","height":"높이","left":"왼쪽","location":"회사 위치","right":"오른쪽","size":"크기","startPointX":"시작점 X","startPointY":"시작점 Y","top":"위쪽","width":"너비"},"dvChartPlotCustomLabels":{"offsetX":"옵셋 X","offsetY":"옵셋 Y","text":"텍스트"},"dvChartPlotPointers":{"end":"끝부분","needlePinWidth":"바늘 핀 넓이","needleWidth":"바늘 넓이"},"dvPlotOverlays":{"aggregateType":"Aggregate Type","axis":"Axis","backgroundColor":"배경 색상","backwardForecastPeriod":"Backward Forecast Period","detailLevel":"세부 단계","display":"Display","end":"종료","field":"필드","forwardForecastPeriod":"Forward Forecast Period","intercept":"Intercept","legendLabel":"범례 표시","lineColor":"선 색상","lineStyle":"선 스타일","lineWidth":"Line Width","name":"이름","order":"Order","period":"Period","start":"시작","type":"Type","value":"값"},"dvchart":{"bar":{"bottomWidth":"아래쪽 너비","neckHeight":"목 높이","overlap":"겹치기","topWidth":"위쪽 너비","width":"너비"},"customPaletteColors":"사용자 정의 팔레트 색","legendHidden":"숨김","legendOrientation":"방향","legendPosition":"위치","legendWrapping":"줄 바꿈","palette":"팔레트","plotTemplate":"그림 템플릿","plotTemplateDropdown":"선택...","plots":"그림"},"dvchartAxis":{"axisType":"축 유형","dateMode":"날짜 모드","gridLinesStyle":"그리드 선 스타일","height":"높이","labelAngle":"레이블 각도","labelField":"레이블 필드","lineStyle":"선 스타일","logBase":"로그 기준","majorInterval":"주 간격","max":"최댓값","maxHeight":"최대 높이","maxWidth":"최대 넓이","min":"최솟값","minorInterval":"부 간격","origin":"원점","overlappingLabels":"겹치는 레이블","plots":"그림","position":"위치","reversed":"바뀜","scale":"배율 유형","showGridLines":"그리드 선 표시","showMajorGridLines":"주 그리드 선","showMinorGridLines":"부 그리드 선","tickMarks":"눈금 표시","tickSize":"눈금 크기","tickStyle":"눈금 스타일","title":"제목","visible":"표시","width":"너비"},"dvchartEncoding":{"aggregateType":"집계","excludeNulls":"Null 제외","fieldType":"필드 유형","fieldValue":"값","group":"그룹","sort":"정렬 방향","sortingAggregate":"정렬 집계","sortingField":"정렬 식","target":"적용 분야","templateKey":"템플릿 키","value":"값"},"dvchartHeaderFooter":{"caption":"캡션","height":"높이"},"dvchartLegend":{"hidden":"숨김","iconColor":"아이콘 색","maxHeight":"최대 높이","maxWidth":"최대 넓이","orientation":"방향","position":"위치","ranges":"범위","title":"제목"},"dvchartPlot":{"action":"액션 타입","axisMode":"축 모드","bar":"막대 선","category":"범주","categorySort":"범주 정렬 방향","categorySortingAggregate":"범주 정렬 집계","categorySortingField":"범주 정렬 식","clippingMode":"클리핑 모드","color":"색","colorAggregate":"색 집계","customLabels":"게이지 라벨","details":"세부 정보","gaugeRanges":"계기 범위","innerRadius":"내부 반경","lineAspect":"선 모양","lineColor":"선 색","lineStyle":"선 스타일","lineWidth":"선 두께","offset":"오프셋","opacity":"불투명","overlays":"오버레이","plotStyle":"그림 스타일","pointers":"게이지 포인터","radial":"방사형","rules":"규칙","shape":"도형","shapeAggregate":"도형 집계","showNulls":"Null 표시","showValuesNamesInLegend":"범례에 값 이름 표시","size":"크기","sizeAggregate":"크기 집계","startAngle":"시작 각도","swapAxes":"축 바꾸기","sweep":"비우기","symbolBackgroundColor":"배경색","symbolOpacity":"기호 불투명도","symbolShape":"기호 도형","symbolStyle":"기호 스타일","symbols":"기호 표시","text":"텍스트","textBackgroundColorStyle":"배경색","textConnectingLine":"연결 선","textLinePosition":"위치","textOffset":"오프셋","textOverlappingLabels":"겹치는 레이블","textPosition":"위치","textTemplate":"템플릿","texts":"텍스트 인코딩","tooltip":"도구 설명","tooltipTemplate":"도구 설명 템플릿","type":"그림 유형","unpivotData":"데이터 피벗 해제","values":"값"},"dvchartPlotArea":{"axes":"축"},"dvchartPlotRules":{"condition":"조건","name":"이름","ruleProperties":"규칙 속성","targetProperty":"대상 속성","valueRuleProperties":"값"},"dvchartValueEncoding":{"field":{"caption":"캡션","close":"닫기","high":"높은","key":"키","low":"낮은","lower":"아래쪽","open":"열기","upper":"위쪽","value":"값"}},"empty":"<비어 있음>","filter":{"filterExpression":"필터 식","filterValues":"필터 값","operator":"연산자","value":"값"},"font":{"fontFamily":"글꼴 종류","fontSize":"글꼴 크기","fontStyle":"글꼴 스타일","fontWeight":"글꼴 두께"},"formattedText":{"encodeMailMergeFields":"병합 필드 인코딩","html":"Html","mailMergeFields":"병합 필드"},"fplPage":{"orientation":"방향","size":"크기"},"fplReport":{"fixedElementName":"고정 페이지 요소 이름","fixedElementOutput":"고정 페이지 요소 출력"},"group":{"dataCollectionName":"데이터 컬렉션 이름","dataElementName":"데이터 요소 이름","dataElementOutput":"데이터 요소 출력","documentMapLabel":"레이블","filters":"필터","groupExpressions":"그룹 식","name":"이름","newPage":"새 페이지","newSection":"새 섹션","pageBreak":"페이지 나누기","pageBreakDisabled":"페이지 나누기 비활성화","parent":"부모","printFooterAtBottom":"맨 아래에 바닥글 인쇄"},"image":{"backgroundRepeat":"반복","horizontalAlignment":"가로 정렬","imageLabel":"이미지","mimeType":"MIME 형식","sizing":"이미지 크기 조정","source":"원본","value":"값","verticalAlignment":"세로 정렬"},"inputField":{"checkSize":"크기 확인","checkStyle":"스타일 확인","checked":"선택됨","fieldName":"필드 이름","inputType":"유형","maxLength":"최대 길이","multiline":"여러 줄","password":"암호","readonly":"읽기 전용","required":"필수","spellCheck":"맞춤법 검사","tabIndex":"탭 인덱스","value":"값"},"layer":{"designerDataFieldVisible":"필드 선택기 사용","designerLock":"디자이너 잠금","designerTransparency":"디자이너 투명도","designerVisible":"디자이너 표시","name":"이름","targetDevice":{"all":"모두","export":"내보내기","paper":"용지","screen":"화면"}},"line":{"endPoint":"끝점","lineColor":"선 색","lineStyle":"선 스타일","lineWidth":"선 두께","startPoint":"시작점"},"list":{"consumeWhiteSpace":"공백 사용","dataInstanceElementOutput":"데이터 인스턴스 요소 출력","dataInstanceName":"데이터 인스턴스 이름","gridMode":"그리드 모드","growDirection":"증가 방향","rowsOrColumnsCount":"행 또는 열의 갯수"},"margins":{"bottom":"아래쪽","left":"왼쪽","right":"오른쪽","top":"위쪽"},"multipleValues":"<여러 값>","overflowPlaceHolder":{"overflowName":"오버플로우 이름"},"padding":{"bottom":"아래쪽","left":"왼쪽","right":"오른쪽","top":"위쪽"},"pageSection":{"printOnFirstPage":"첫 페이지에 인쇄","printOnLastPage":"마지막 페이지에 인쇄"},"parameter":{"omit":"생략","parametername":"매개변수 이름","parameters":"매개변수","value":"값"},"partItem":{"library":"라이브러리","reportPart":"보고서 구성요소"},"report":{"author":"작성자","collapseWhiteSpace":"공백 축소","collateBy":"정렬/분류 기준","columnSpacing":"열 간격","columns":"열","consumeContainerWhitespace":"공백 사용","description":"설명","displayType":"표시 유형","embeddedImages":"포함된 이미지","language":"언어","layers":"레이어","layoutOrder":"레이아웃 순서","level":"수준","levels":"수준","marginsSizes":"크기","marginsStyle":"스타일","nameMasterReport":"마스터 리포트,","numberingStyle":"번호 매기기 스타일","pageHeight":"페이지 높이","pageOrientation":"페이지 방향","pageSize":"페이지 크기","pageWidth":"페이지 너비","reportPart":{"description":"설명","displayName":"디스플레이 이름","partProperties":"속성","properties":{"category":"범주","defaultValue":"기본값","description":"설명","displayName":"디스플레이 이름","type":"유형"},"reportItemName":"보고서 아이템 이름","sizeMode":"크기 모드"},"reportParts":"보고서 구성요소","sizeType":"크기 유형","source":"원본","startPageNumber":"시작 페이지 번호","theme":"테마","themes":"테마"},"reportItem":{"accessibleDescription":"대체 설명 텍스트","actionType":"유형","bookmark":"책갈피","keepTogether":"함께 유지","label":"레이블","layerName":"계층 이름","name":"이름","pageName":"페이지 이름","style":"스타일","toolTip":"도구 설명","visibility":"표시 여부","zIndex":"Z-인덱스"},"reportParameter":{"allowBlankValue":"빈 값 허용","allowNullValue":"Null 값 허용","dataSetName":"데이터 집합 이름","dataType":"데이터 형식","hidden":"숨김","label":"레이블","labelField":"레이블 필드","multiline":"여러 줄","multivalue":"다중 값","name":"이름","orderBy":"정렬 순서","parameterFormat":"서식","parameterValues":"매개변수 값","prompt":"프롬프트","selectAllValue":"모두 값 선택","selectAllValue_help":{"text":"모두 선택이 되었다면 매개변수값에 전달할 값을 결정합니다."},"value":"값","valueField":"값 필드","values":"값"},"reportSlicer":{"allowBlankValue":"Allow Blank Value","allowNullValue":"Allow Null Value","dataSetName":"Data Set Name","multivalue":"Multivalue","name":"Name"},"richtext":{"canGrow":"확장 가능","markup":"마크업 유형","value":"값"},"roundingRadius":{"bottomLeft":"왼쪽 아래","bottomRight":"오른쪽 아래","default":"기본값","label":"둥근 테두리 반경","topLeft":"왼쪽 위","topRight":"오른쪽 위"},"sparkline":{"fillColor":"채우기 색","gradientEndColor":"그라데이션 마지막 색","gradientStyle":"그라데이션 유형","lineColor":"선 색","lineWidth":"선 두께","markerColor":"표식 색","markerVisibility":"표식 표시 여부","maximumColumnWidth":"최대 너비","rangeFillColor":"채우기 색","rangeGradientEndColor":"그라데이션 마지막 색","rangeGradientStyle":"그라데이션 유형","rangeLowerBound":"하한","rangeUpperBound":"상한","rangeVisibility":"표시 여부","seriesValue":"계열 값","sparklineType":"스파크라인 유형"},"style":{"angle":"각","backgroundAndBorders":"배경 및 테두리","backgroundColor":"색","backgroundGradientEndColor":"그라데이션 마지막 색","backgroundGradientType":"그라데이션 유형","backgroundImage":"이미지","border":"테두리","calendar":"달력","characterSpacing":"문자 간격","color":"색","corner":"모서리","direction":"방향","font":"글꼴","format":"서식","headingLevel":"제목 수준","language":"언어","lineHeight":"줄 높이","lineSpacing":"줄 간격","maxLevel":"최고 수준","minCondenseRate":"최소 축소 비율","minCondenseRate_help":{"text":"가로 축소의 최소 비율을 백분율로 지정합니다. 10에서 100 사이여야 합니다."},"numeralLanguage":"숫자 언어","numeralVariant":"숫자 변형","padding":"안쪽 여백","shapeStyle":"도형 스타일","shrinkToFit":"축소 맞춤","textAlign":"텍스트 맞춤","textDecoration":"텍스트 장식","textIndent":"텍스트 들여쓰기","textJustify":"텍스트 양쪽 맞춤","unicodeBiDi":"Unicode BiDi","uprightInVerticalText":"수직 텍스트에서 세로 항목","verticalAlign":"세로 맞춤","wrapMode":"줄 바꿈 모드","writingMode":"쓰기 모드"},"subreport":{"inheritStyleSheet":"스타일시트 상속","mergeTransactions":"트랜잭션 병합","reportName":"보고서 이름","reportParameters":"리포트 매개변수","substituteThemeOnSubreport":"하위 보고서에서 테마 바꾸기"},"table":{"autoWidth":"자동 폭","detailsDataCollectionName":"세부 정보 데이터 컬렉션 이름","detailsDataElementName":"세부 정보 데이터 요소 이름","detailsDataElementOutput":"세부 정보 데이터 요소 출력","keepTogether":"함께 유지","preventOrphanedFooter":"분리된 바닥글 방지","printAtBottom":"맨 아래에 인쇄","repeatBlankRows":"빈 행 반복","repeatOnNewPage":"새 페이지에서 반복"},"tablix":{"frozenColumns":"고정된 열","frozenRows":"고정된 행","groupsBeforeRowHeaders":"행 머리글 앞의 그룹","layoutDirection":"레이아웃 방향","repeatColumnHeaders":"열 머리글 반복","repeatRowHeaders":"행 머리글 반복"},"tablixBodyCell":{"autoMergeMode":"자동 병합 모드","autoMergeMode_help":{"text":"열에서 내용이 같은 두 개 이상의 연속 셀을 병합할지 여부를 나타냅니다. 텍스트 상자 셀에만 적용됩니다."}},"tablixMember":{"groupEnabled":"활성화","keepWithGroup":"그룹으로 유지","repeatOnNewPage":"새 페이지에서 반복"},"textbox":{"canGrow":"확장 가능","canShrink":"축소 가능","initialToggleState":"초기 토글 상태","value":"값"},"tocLevel":{"displayFillCharacters":"채우기 문자 표시","displayPageNumber":"페이지 번호 표시","fillCharacter":"채우기 문자","label":"레이블"},"userSort":{"sortExpression":"정렬 식","sortExpressionScope":"정렬 식 범위","sortTarget":"정렬 대상"},"visibility":{"hidden":"숨김","toggleItem":"항목 토글"}}}},{"lng":"ko","ns":"propertyEditors-RPX","resources":{"image":{"textChange":"Change...","textPick":"Pick..."},"statusWrapper":{"btnReset":"Reset","titleDefault":"","titleError":"Invalid","titleInherited":"Inherited from Parent Style","titleModified":"Modified"},"style":{"drillCaptionStyle":"Style","textEdit":"Edit"}}},{"lng":"ko","ns":"propertyEditors","resources":{"boolean":{"textFalse":"False","textTrue":"True","textUndefined":"정의되지 않음"},"chartComplexEncodingFieldCollectionEditor":{"prefix":"필드"},"chartSimpleEncodingFieldCollectionEditor":{"captionHeader":"캡션","keyHeader":"키","valueHeader":"값"},"collection":{"btnAdd":"추가","textEmpty":"컬렉션이 비어있습니다.","textItemsCount_0":"{{count}}아이템","titleAdd":"아이템 추가","titleClose":"닫기","titleDelete":"삭제","titleShowItems":"아이템 보여주기"},"colorDropdown":{"btnColorPicker":"직접 지정","btnPalettes":"팔레트","btnWebColors":"웹 색상","headingStandard":"표준 색상","headingTheme":"테마 색","labelHex":"헥스","labelHue":"색조","labelLightness":"밝기","labelSaturation":"채도","themeColors":{"titleBase":"{{colorKey}}","titleDarker25":"{{colorKey}} - 25% 어둡게","titleDarker50":"{{colorKey}} - 50% 어둡게","titleLighter25":"{{colorKey}} - 25% 밝게","titleLighter50":"{{colorKey}} - 50% 밝게","titleLighter75":"{{colorKey}} - 75% 밝게"}},"common":{"textEmpty":"<비어 있음>","textExpression":"<표현식>","textMultipleValues":"<여러 값>","textNone":"<없음>","titleCollapse":"축소","titleExpand":"확장"},"dataSetFieldCollection":{"dataFieldHeader":"데이터 필드","dataFieldPlaceholder":"<데이터 필드>","fieldHeader":"필드 이름","fieldPlaceholder":"<이름>","valueHeader":"값","valuePlaceholder":"<값>"},"dataSetParameterCollection":{"nameHeader":"매개변수 이름","namePlaceholder":"<이름>","valueHeader":"값","valuePlaceholder":"<값>"},"dataSetQuery":{"placeholder":"여기에 쿼리 입력..."},"dvcartLegendRangeOptionsCollection":{"titleHeader":"제목","titlePlaceholder":"<제목>","toHeader":"To"},"dvchartEncodingCollection":{"aggregate":{"prefix":"집계","propertiesTitle":"집계 속성"},"color":{"valuesName":"범례에 값 이름 표시"},"details":{"prefix":"세부 정보","propertiesTitle":"세부 정보 속성"},"fieldPlaceholder":"<키>","text":{"prefix":"텍스트","propertiesTitle":"텍스트 속성"},"value":{"prefix":"값","propertiesTitle":"값 속성"},"valuePlaceholder":"<값>"},"dvchartPlotTemplate":{"textSelect":"선택..."},"dvchartRuleProperties":{"headingTargetProperty":"대상 속성","headingValueProperty":"값"},"dvchartTemplate":{"custom":"<사용자 정의>"},"format":{"$locale":"en-US","$localeCurrency":"USD","currency":"통화","custom":"(사용자 정의)","customFormatMask":"(###) ###-####","decimal":"10진수","default":"<기본값>","digitsLabel":"숫자","fixedPoint":"고정 소수점","fullDateShortTime":"전체 날짜/짧은 시간","general":"일반","generalDateLongTime":"일반 날짜/긴 시간","generalDateShortTime":"일반 날짜/짧은 시간","hexadecimal":"16진수","longDate":"긴 날짜","longTime":"긴 시간","monthDay":"월 일","number":"숫자","percent":"백분율","scientific":"지수","shortDate":"짧은 날짜","shortTime":"짧은 시간","yearMonth":"년 월"},"image":{"btnDatabase":"데이터베이스","btnEmbedded":"포함됨","btnShared":"공유","textLoad":"불러오기...","textNoDataFieldsFound":"데이터 필드를 찾을 수 없음","textNoImagesFound":"이미지를 찾을 수 없음","titleRemove":"\'{{name}}\' 제거..."},"layerCollection":{"propertiesTitle":"계층 속성: {{layerName}}"},"mailMergeFieldsCollection":{"nameHeader":"필드 이름","namePlaceholder":"<이름>","valueHeader":"값","valuePlaceholder":"<값>"},"marginsSizes":{"custom":"(사용자 정의)"},"pageSize":{"custom":"(사용자 정의)"},"palette":{"customPaletteLabel":"<사용자 정의>","extraPalettesHeader":"테마 팔레트","standardPalettesHeader":"표준 팔레트"},"parameterCollection":{"titleProperties":"매개변수 속성"},"parameterValuesOrder":{"ascending":"오름차순","descending":"내림차순"},"picker":{"btnDataVisualizer":"데이터 시각화 도우미...","btnExpression":"표현식...","btnPickData":"데이터 선택...","btnReset":"다시 설정","headingParameters":"매개변수","titleDataBinding":"데이터 바인딩","warnings":{"groupingByAggregate":"집계 특성으로 그룹화하지 않는 것이 좋습니다.","groupingIsDiscouraged":"이 특성으로 그룹화하지 않는 것이 좋습니다.","masterReportAttributes":"only attributes used in the master report are available for content reports"}},"reportParameter":{"labelFromQuery":"시작 쿼리","labelNonQueried":"쿼리 사용 안 함","labelSource":"원본","placeholderEmpty":"<비어 있음>","placeholderLabel":"레이블","placeholderValue":"값"},"reportPartPropertiesCollection":{"propertiesTitle":"보고서 구성요소: {{reportPartName}}"},"reportPartsCollection":{"propertiesTitle":"보고서 구성요소: {{reportPartName}}"},"reports":{"textLoading":"불러오는 중...","textLoadingError":"오류 {{status}}: {{statusText}}"},"simple":{"backgroundColor":{"label":"색상","title":"배경색"},"borders":{"borderColor":{"label":"색상","title":"테두리 색"},"borderStyle":{"label":"스타일","title":"테두리 스타일"},"borderWidth":{"label":"너비","title":"테두리 너비"},"borders":"테두리","sides":{"all":"모두","bottom":"아래쪽","left":"왼쪽","reset":"다시 설정","right":"오른쪽","top":"위쪽"}},"common":{"textExpressionCompact":"<𝑓>"},"font":{"fontFamily":{"label":"패밀리","title":"글꼴 종류"},"fontSize":{"label":"크기","title":"글꼴 크기"},"fontStyle":{"label":"스타일","title":"글꼴 스타일"},"fontWeight":{"label":"두께","title":"글꼴 두께"},"textColor":{"label":"색상","title":"텍스트 색"},"textDecoration":{"label":"장식","title":"텍스트 장식"}}},"subreport":{"parameter":"매개변수","parameterNameHeader":"이름","parameterNamePlaceholder":"<이름>","parameterValueHeader":"값","parameterValuePlaceholder":"<값>"},"toggleState":{"textCollapsed":"축소됨","textExpanded":"확장됨"},"validationErrors":{"expression":{"disabledFields":"경고 : 이 표현식 (\'{{token}}\'{{positionInfo}})에서 \'Fields\' 토큰 유형을 사용하는 것은 금지되어 있습니다.","disabledReportItems":"경고 : 이 표현식 (\'{{token}}\'{{positionInfo}})에서 \'ReportItems\' 토큰 유형을 사용하는 것은 금지되어 있습니다.","errorPosition":"{{line}} 줄, {{column}} 열에서","parseError":"구문 오류: 이 보간 구문 식을 rdl 구문으로 변환할 수 없습니다.","syntaxError":"구문 오류: 예기치 않은 토큰 \'{{token}}\' {{positionInfo}}","unknown":"알 수 없는 오류 : \'{{positionInfo}}\'","unknownField":"경고: \'{{token}}\' {{positionInfo}}의 알 수 없는 필드 이름","unknownFunction":"경고 : 알 수없는 함수 이름 \'{{token}}\'{{positionInfo}}","unknownParameter":"경고: \'{{token}}\' {{positionInfo}}의 알 수 없는 매개변수 이름","unknownReportItem":"경고: \'{{token}}\' {{positionInfo}}의 알 수 없는 보고서 항목 이름","unknownThemeImage":"경고: \'{{token}}\' {{positionInfo}}의 알 수 없는 토큰 이미지 이름","warning":"경고: 알 수 없는 토큰 \'{{token}}\' {{positionInfo}}"}}}},{"lng":"ko","ns":"reportItems","resources":{"Page":"페이지","Report":"보고서","bandedList":"밴드 목록","bandedListDetails":"세부 정보","bandedListFooter":"바닥글","bandedListGroup":"그룹","bandedListHeader":"머리글","barcode":"바코드","body":"본문","bullet":"글머리 기호","checkbox":"체크박스","container":"컨테이너","contentPlaceholderText":"Set the Text property to tell Content Report authors what to add here","contentplaceholder":"컨텐츠 자리 표시자","continuousSection":"연속형 섹션","dashboard":"대시보드","dashboardPageFooter":"푸터","dashboardPageHeader":"헤더","dashboardSection":"섹션","dvchart":"차트","dvchartAggregateEncoding":"집계 인코딩","dvchartAxis":"축","dvchartCategoryEncoding":"범주 인코딩","dvchartColorLegend":"범례 - 색","dvchartDetailsEncoding":"세부 정보 인코딩","dvchartEncodingValue":"인코딩 값","dvchartFooter":"바닥글","dvchartGlobalLegend":"전역 범례","dvchartHeader":"머리글","dvchartLegend":"범례","dvchartPlot":"그림","dvchartPlotArea":"그림 영역","dvchartPlotCustomLabel":"게이지 라벨","dvchartPlotPointer":"게이지 포인터","dvchartShapeLegend":"범례 - 도형","dvchartSizeLegend":"범례 - 크기","dvchartTextEncoding":"텍스트 인코딩","dvchartValueAggregateEncoding":"값 집계 인코딩","dvchartXAxis":"X 축","dvchartYAxis":"Y 축","fixedPageSection":"고정형 페이지 섹션","formattedText":"서식 있는 텍스트","image":"이미지","inputField":"입력 필드","layer":"계층","line":"꺾은선형","list":"목록","listColumn":"열","listColumnsStacked":"열 2-{{columnCount}}","listRow":"행","listRowsStacked":"행 2-{{rowCount}}","overflowPlaceholder":"오버플로우 영역 표시","page":"페이지","pageFooter":"페이지 바닥글","pageHeader":"페이지 머리글","pageSection":"페이지 머리글/바닥글","partItem":"구성요소항목","report":"보고서","reportPart":"구성 요소","reportPartProperty":"속성","richtext":"서식 있는 텍스트","shape":"도형","sparkline":"스파크라인","subreport":"하위 보고서","table":"테이블","tableColumn":"열","tableDetails":"세부 정보","tableFooter":"바닥글","tableGroup":"그룹","tableHeader":"머리글","tableOfContents":"목차","tableOfContentsLevel":"수준","tableRow":"행","tablix":"테이블릭스","tablixColumn":"열","tablixMember":"테이블릭스 멤버","tablixRow":"행","textbox":"텍스트 상자","unknown":"알수없는 아이템"}},{"lng":"ko","ns":"romLabels","resources":{"chart":"차트","dvchart":"DV.Chart","matrix":"행렬","table":"테이블","tablix":"테이블릭스"}},{"lng":"ko","ns":"tablixWizard","resources":{"aggregates":{"avg":"평균","count":"개수","max":"최대값","min":"최소값","none":"없음","sum":"합계"},"btnOrganization":"조직","btnStyling":"스타일 지정","btnTotals":"합계","displayAsOptions":{"default":"기본","percentColumnGroupTotal":"% 열 그룹 합계","percentGrandTotal":"% 총합계","percentParentColumnGroupTotal":"% 상위 열 그룹 합계","percentParentRowGroupTotal":"% 상위 행 그룹 합계","percentRowGroupTotal":"행 그룹 합계 %"},"filters":{"headingGroupFilters":"그룹 필터 – {{groupLabel}}","headingTablixFilters":"테이블릭스 필터","textFilters":"필터","titleEditGroupFilters":"그룹 필터 편집...","titleEditTablixFilters":"테이블릭스 필터 편집..."},"formats":{"currency":"통화","decimal":"10진수","default":"기본값","general":"일반","number":"숫자","percent":"백분율"},"headingDataSets":"데이터 집합","headingLayoutDesign":"레이아웃 디자인","headingTablixWizard":"테이블릭스 마법사","labelCollapsedGroups":"축소된 그룹","labelExpandCollapse":"그룹 확장/축소","labelFrozenColumns":"고정된 열","labelFrozenRows":"고정된 행","labelNone":"없음","labelShowTotalsBeforeGroup":"그룹 앞에 합계 표시","labelSteppedRowGroups":"단계별 행 그룹","labelSubTotalsForColumns":"열 그룹의 소계","labelSubTotalsForRows":"행 그룹의 소계","labelTotalsForColumns":"열 그룹의 합계","labelTotalsForRows":"행 그룹의 합계","labelUserSortEnabled":"사용자 정렬 활성화","makeTablix":{"textTotal":"합계","textValues":"값"},"placeholderNoField":"필드 없음","sortings":{"Ascending":"오름차순","Descending":"내림차순","None":"없음"},"textAddDataSet":"먼저 보고서에 데이터 집합을 추가하십시오.","textAddValue":"테이블릭스를 만들려면 하나 이상의 값을 추가하십시오.","textAsRows":"행으로","textCannotEditInWizard":"테이블릭스는 구조가 복잡하며 마법사에서 편집할 수 없습니다. 대신 그룹 편집기를 사용하십시오.","textColumns":"열 그룹","textLayoutOptions":"레이아웃 옵션","textNoDataSets":"보고서에 데이터 집합이 없습니다.","textNoValues":"값을 지정하지 않았습니다.","textOpenWizard":"테이블릭스 마법사 열기...","textRowGroups":"행 그룹","textShowValuesAsRows":"값을 행으로 표시","textSwap":"바꾸기","textValues":"값","titleAggregate":"집계","titleDelete":"삭제","titleDisplayAs":"제목 표시 이름","titleFormat":"서식","titleSorting":"정렬: {{sorting}}","titleSwapRowColumnGroups":"행/열 그룹 바꾸기","warning":{"btnQuit":"종료","btnRevert":"되돌리기","headingWarning":"경고","textChangedStructure":"테이블릭스의 구조가 변경되어 테이블릭스 마법사에서 테이블릭스를 편집할 수 없습니다.","textConfirmReverting":"마법사에서 편집할 수 있는 이전 테이블릭스 상태로 되돌리십시오.","textOtherwiseCannotEdit":"그러지 않으면 테이블릭스 마법사 내에서 편집을 계속할 수 없습니다."}}},{"lng":"ko","ns":"validationErrors","resources":{"enum":{"incorrect":"\'{{enumType}}\' 형식의 열거형 값은 다음 중 하나여야 합니다. {{enumValues}}"},"errorPosition":"at line {{line}}, column {{column}}","expression":{"disabledFields":"경고 : 이 표현식 (\'{{token}}\'{{positionInfo}})에서 \'Fields\' 토큰 유형을 사용하는 것은 금지되어 있습니다.","disabledReportItems":"경고 : 이 표현식 (\'{{token}}\'{{positionInfo}})에서 \'ReportItems\' 토큰 유형을 사용하는 것은 금지되어 있습니다.","errorPosition":"{{line}} 줄, {{column}} 열에서","parseError":"구문 오류: 이 보간 구문 식을 rdl 구문으로 변환할 수 없습니다.","syntaxError":"구문 오류: 예기치 않은 토큰 \'{{token}}\' {{positionInfo}}","unknown":"알 수 없는 오류 : \'{{positionInfo}}\'","unknownField":"경고: \'{{token}}\' {{positionInfo}}의 알 수 없는 필드 이름","unknownFunction":"경고 : 알 수없는 함수 이름 \'{{token}}\'{{positionInfo}}","unknownParameter":"경고: \'{{token}}\' {{positionInfo}}의 알 수 없는 매개변수 이름","unknownReportItem":"경고: \'{{token}}\' {{positionInfo}}의 알 수 없는 보고서 항목 이름","unknownThemeImage":"경고: \'{{token}}\' {{positionInfo}}의 알 수 없는 토큰 이미지 이름","warning":"경고: 알 수 없는 토큰 \'{{token}}\' {{positionInfo}}"},"length":{"negative":"값은 0보다 커야 합니다.","tooLarge":"값은 {{max}}보다 낮아야 합니다.","tooSmall":"값은 {{min}}보다 커야 합니다.","unit":"유효한 단위는 \'cm\', \'mm\', \'in\', \'pt\' 및 \'pc\'입니다."},"mime_type":{"incorrect":"이미지 MIME 형식이 {{wildcard}}과(와) 일치해야 합니다."},"number":{"empty":"비어있는 값입니다.","nan":"값이 숫자가 아닙니다.","outOfInterval":"간격 범위 밖의 값입니다.","outOfRange":"값이 너무 크거나, 너무 작습니다.","tooLarge":"값은 {{max}}보다 낮아야 합니다.","tooSmall":"값은 {{min}}보다 커야 합니다."},"pattern":"잘못된 식입니다. 이 속성의 리터럴 식은 \'{{type}}\' 형식이어야 합니다. {{info}}","unknown":{"unknown":"알 수 없는 유형 : \'{{type}}\'"}}},{"lng":"ko","ns":"warning","resources":{"embeddedImage":{"badFile":"선택하신 \'{{name}}\' 파일은 이미지가 아닙니다.","badFileType":"선택하신 \'{{name}}({{type}})\' 파일은 지원되지 않습니다.","badImageFile":"선택하신 이미지 \'{{name}}\' 파일은 잘못되었습니다.","badImageSize":"선택하신 이미지 파일 \'{{name}}\'의 크기 제한이 {{limit}}MB를 초과합니다."},"margins":{"caption":"지정한 여백을 설정할 수 없습니다.","info":"- \'{{name}}\'은(는) {{value}}보다 클 수 없습니다.","labels":{"bottom":"아래쪽 여백","left":"왼쪽 여백","right":"오른쪽 여백","top":"위쪽 여백"}},"pageSize":{"caption":"지정된 페이지 크기를 설정할 수 없습니다.","info":"- \'{{name}}\'은(는) {{value}}보다 작을 수 없습니다.","labels":{"height":"페이지 높이","width":"페이지 너비"}}}}]')},26:function(e){e.exports=JSON.parse('[{"lng":"ko","ns":"arjswd","resources":{"about":{"applicationVersion":"어플리케이션 버전: {{applicationVersion}}","close":"닫기","coreVersion":"코어버전: {{coreVersion}}","title":"{{applicationTitle}} 정보"},"application-logo":"","application-title":"ActiveReportsJS 디자이너","collectionEditor":{"AddBtnTitle":"항목 추가","addBtnText":"추가","closeBtnTitle":"닫기","deleteBtnTitle":"삭제","showBtnTitle":"표시","textEmpty":"컬렉션이 비어 있습니다.","textItems":"항목"},"common":{"copyright":"© MESCIUS inc. All rights reserved.","untitledReportName":"제목없음"},"commonEditorProps":{"bool-false-label":"거짓","bool-true-label":"참","dataBinding":"데이터 바인딩","number-editor-decrease":"감소","number-editor-increase":"증가","placeholderEmpty":"<비어있음>"},"dataPanel":{"addCmdTitle":"추가","commonValuesLabel":"공통 값","dataSetsLabel":"데이터 집합","dataSourcesLabel":"데이터 원본","editParameter":"매개 변수 편집","noDataSets":"보고서에 데이터 집합이 없음","noDataSources":"보고서에 데이터 원본이 없음","noParameters":"지정된 매개변수가 없음","parametersLabel":"매개 변수","smartSuggestions":"Smart Suggestions"},"dataSetDialog":{"alertTextClose":"닫기","alertTextCollapse":"축소","alertTextExpand":"확장","btnBack":"부모로 돌아가기","btnExportTemplate":"내보내기...","btnValidate":"유효성 검사","confirmSaveInvalid":"변경 내용을 확인하지 못했습니다. 변경 내용을 저장하시겠습니까?","modeEditSubtitle":"데이터 집합 편집","modeNewSubtitle":"새 데이터 집합","parseQueryError":"잘못된 쿼리 문자열"},"dataSetProps":{"category":{"csv":"CSV 설정","fields":"필드","filters":"필터","name":"이름","query":"쿼리"},"columnSeparatorComma":"쉼표","columnSeparatorSemicolon":"세미콜론","columnSeparatorSpace":"스페이스","columnSeparatorTab":"탭","fieldsDataField":"데이터 필드","fieldsName":"필드 이름","fieldsValue":"값","fieldsValuePlaceholder":"식 입력","methodGet":"HTTP GET","methodPost":"HTTP POST","queryMode":{"JSON":"JSON","Text":"Text"},"title":{"calcFields":"계산된 필드","columnSeparator":"열 구분 기호","datasets":"중첩된 데이터 집합","endpoint":"Uri/경로","fields":"데이터베이스 필드","headers":"헤더","headingRow":"첫 번째 행을 헤더로 설정","jpath":"Json 경로","mergeColumnSeparators":"열 구분 기호 병합","mergeRowSeparators":"행 구분 기호 병합","method":"메서드","postBody":"본문 게시","queries":"매개 변수","queryType":"유형","rowSeparator":"행 구분 기호","startingRow":"시작 행","value":"Value"}},"dataSourceDialog":{"btnConnectionString":"연결 문자열","btnExportTemplate":"내보내기...","btnProperties":"속성","parseErrorTitle":"잘못된 연결 문자열: {{errors}}","subtitle":"데이터 소스 편집"},"dataSourceProps":{"btnLoadFromFile":"파일에서 로드","category":{"connectionString":"연결 문자열","data":"데이터","dataFormat":"데이터 유형","endpoint":"끝점","jsonData":"JSON 데이터","name":"이름","parameters":"매개 변수","sourceType":"소스 형식"},"exprMenuDialog":"식...","exprMenuReset":"다시 설정","headersKey":"머리글","headersValue":"값","providerCSV":"CSV","providerJSON":"JSON","queriesKey":"매개 변수","queriesValue":"값","sourceEmbedded":"임베디드","sourceRemote":"외부 파일 또는 URL","title":{"headers":"HTTP 헤더","queries":"쿼리 매개 변수"}},"dataTab":{"title":"데이터"},"designer":{"dateFormats":[],"defaultSettings":{"propertiesMode":"Basic","snapToGridEnabled":"false","snapToGuidesEnabled":"false","units":"in"},"fonts":[["맑은고딕","Malgun Gothic"],"Arial","Arial Black","Comic Sans MS","Courier New","Geneva","Georgia","Helvetica","Impact","Lucida Console","Meiryo","Meiryo UI","MingLiU","MingLiU-ExtB","MS Gothic","MS Mincho","MS PGothic","MS PMincho","MS Song","MS UI Gothic","NSimSun","Osaka","PMingLiU","PMingLiU-ExtB","SimSun","SimSun-ExtB","Song","Tahoma","Times New Roman","Trebuchet MS","Verdana","Yu Gothic",["굴림","Gulim"],["굴림체","GulimChe"],["궁서","Gungsuh"],["궁서체","GungsuhChe"],["돋음","Dotum"],["돋음체","DotumChe"],["바탕","Batang"],["바탕체","BatangChe"],["새굴림","New Gulim"]],"reportStyles":[],"reportTypes":["CPL","FPL","Pageless"]},"dialogs":{"cancel":"취소","confirmSavingChanges":{"dontSaveLabel":"저장하지 않기","saveLabel":"저장","wantSaveChanges":"{{reportName}}에 변경사항을 저장하시겠습니까?"},"saveChanges":"변경 내용 저장"},"error":{"api":{"createReportFailed":"보고서를 생성하지 못했습니다."},"cantLoadImages":"이미지 목록을 로드할 수 없음","cantLoadReports":"보고서 목록을 로드할 수 없음","cantLoadThemes":"테마 목록을 로드할 수 없음","customInitTemplate":{"loadFailed":"사용자의 초기 템플릿을 불러오지 못했습니다 \'{{id}}\': {{error}}"},"dataProviderNotSupported":"Data provider \\"{{provider}}\\" is not supported.","errorCode":"에러코드: {{code}}","errorSerializingReport":"내부 오류 : 보고서를 직렬화할 수 없습니다.","hasUnsavedChanges":"보고서를 불러올 수 없습니다. 디자이너에 보고서에 저장되지 않은 변경 사항이 있습니다.","internalError":"내부 오류","invalid-report-displayName":"잘못된 리포트 디스플레이이름","invalid-report-id":"잘못된 보고서 아이디","invalidReportDef":"잘못된 보고서 정의","invalidReportType":"잘못된 보고서 유형","libraries":{"importFailed":"라이브러리 \\"{{path}}\\" 가져오기 실패","loadingFailed":"라이브러리 \\"{{path}}\\" 로드 실패","unknownLibrary":{"caption":"리포트가 의존하고 있는 라이브러리를 알 수 없음","text":"이 보고서에 사용된 라이브러리 \\"{{libName}}\\"을(를) 찾을 수 없습니다."}},"noHostElement":"호스트 요소를 찾을 수 없습니다.","noReportOpened":"현재 열려 있는 보고서가 없습니다.","report-id-is-not-specified":"보고서 아이디가 지정되지 않았습니다","reportLoadFailed":"\\"{{reportName}}\\" 보고서를 열 수 없습니다.","theme":{"notFoundOrInvalid":"이 보고서에 사용된 \'{{theme}}\' 을(를) 찾을 수 없거나 컨텐츠가 잘못되었습니다."},"unableGetReport":"Unable to get a report: {{message}}","unablePerformAction":"Unable to perform the action: {{message}}","unableSetReport":"Unable to set a report: {{message}}","unsupportedDocumentType":"내부 오류: 지원되지 않는 문서 유형"},"exportTemplateDialog":{"closeBtnText":"닫기","title":"템플릿 내보내기"},"expressions":{"customCodeGroup":"커스텀 코드"},"license":{"eval":{"badge":"평가가 {days}일 후에 종료됩니다.","badge-no_days":"평가판","banner":"ActiveReportsJS 평가판"},"expired":{"badge":"평가판이 만료됨","banner":"ActiveReportsJS 평가판"},"invalid":{"badge":"잘못된 라이선스 키","banner":"라이선스가 유효하지 않습니다. \\n라이선스키를 확인해 주시기 바랍니다. \\n문의 사항은 <EMAIL> 또는 1670-0583으로 문의 바랍니다."},"no-license":{"badge":"라이선스를 찾을 수 없음","banner":"라이선스를 찾을 수 없습니다. \\nActiveReportsJS를 실행하려면 유효한 라이선스가 필요합니다. \\n문의 사항은 <EMAIL> 또는 1670-0583으로 문의 바랍니다."}},"menu":{"about":"정보"},"nameTemplates":{"dataSetCalcFieldValue":"계산필드","dataSetFieldValue":"필드","reportParameter":"매개변수"},"notificationPanel":{"collapse":"접기","dismiss":"닫기","dismissAll":"모두 닫기","expand":"펼치기","oneError":"에러","oneNotification":"알리","oneWarning":"경고","showDetails":"세부 정보 표시"},"propertiesTab":{"title":"속성"},"queryValuesDialog":{"btnSave":"저장 및 실행","title":"매개 변수 값"},"sideBar":{"collapse":"접기","expand":"펼치기"},"warning":{"unsavedChanges":"이 페이지의 보고서에는 저장되지 않은 변경사항이 있습니다."}}},{"ns":"core","lng":"ko","resources":{"errors":{"csvdataprovider":{"header-parse":"열 헤더를 구문 분석할 수 없음: \\"{{headerValue}}\\""},"dataprovider":{"commandtext-invalid":"잘못된 데이터 공급자 CommandText: {{commandText}}.","connectstring-invalid":"잘못된 데이터 소스 ConnectString: {{connectString}}.","no-data":"잘못된 데이터 공급자 설정. 소스 또는 데이터가 지정되지 않았습니다."},"jsondataprovider":{"no-data":"잘못된 연결 문자열입니다. \\"jsondata\\" 또는 \\"jsondoc\\"를 지정해야 합니다."},"fetch-failed":"\\"{{uri}}\\": {{responseStatus}} {{responseText}}에서 데이터를 로드할 수 없습니다.","data-processing":"데이터 처리 오류: {{details}}."}}}]')},44:function(e,t,a){"use strict";a.r(t);var r=a(25),o=a(26),i={wdCore:r,arjswd:o};if(window.arjsDesigner=window.arjsDesigner||{},window.arjsDesigner.addLocalization)window.arjsDesigner.addLocalization("ko",i);else{var n=window.arjsDesigner.loadLocalizations;window.arjsDesigner.loadLocalizations=function(){n&&n(),window.arjsDesigner.addLocalization("ko",i)}}}});