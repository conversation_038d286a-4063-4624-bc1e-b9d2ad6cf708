!function(e){var a={};function t(r){if(a[r])return a[r].exports;var o=a[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,t),o.l=!0,o.exports}t.m=e,t.c=a,t.d=function(e,a,r){t.o(e,a)||Object.defineProperty(e,a,{enumerable:!0,get:r})},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,a){if(1&a&&(e=t(e)),8&a)return e;if(4&a&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(t.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&a&&"string"!=typeof e)for(var o in e)t.d(r,o,function(a){return e[a]}.bind(null,o));return r},t.n=function(e){var a=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(a,"a",a),a},t.o=function(e,a){return Object.prototype.hasOwnProperty.call(e,a)},t.p="",t(t.s=43)}({10:function(e){e.exports=JSON.parse('[{"lng":"ja","ns":"adorners","resources":{"bandedList":{"detailsLabel":"<詳細>","groupsLabel":"グループ"},"bullet":{"targetValue":"目標値","value":"値"},"chart":{"categoryEncoding":{"fieldStub":"カテゴリ {{index}}"},"categoryFields":"カテゴリフィールド（x）","colorEncoding":{"fieldStub":"カラー {{index}}","title":"カラー"},"dataFields":"データフィールド（y）","detailEncoding":{"fieldStub":"詳細 {{index}}","title":"詳細"},"encodingFields":"エンコーディング","gaugeLabel":{"fieldStub":"ラベル {{index}}"},"gaugePointer":{"fieldStub":"ポインタ {{index}}"},"gaugeRanges":"ゲージ範囲","labels":"ラベル","multiValueField":"{{firstFieldValue}}, ...","shapeEncoding":{"fieldStub":"シェイプ {{index}}","title":"シェイプ"},"sizeEncoding":{"fieldStub":"サイズ {{index}}","title":"サイズ"},"textEncoding":{"title":"テキスト"},"valueEncoding":{"fieldStub":"値 {{index}}"}},"common":{"dropFieldsAndValues":"フィールドを配置してください。"},"formattedText":{"previewError":"XHTMLの解析中にエラーが発生しました。Htmlプロパティとフィールドの結合プロパティの値を検証してください。"},"shapeRoundingTip":{"multiCornerMode":"\\"Alt\\"キーで「シングルコーナー」モードに変更します","singleCornerMode":"\\"Alt\\"キーで「マルチコーナー」モードに変更します"},"sparkline":{"groupingExpressions":"グループ化の条件式","seriesValue":"系列の値"},"table":{"detailsGroupLabel":"<詳細グループ>","groupsLabel":"グループ","newGroupLabel":"<新規グループ>"},"tableOfContents":{"addItem":"項目の追加"}}},{"lng":"ja","ns":"captions","resources":{"barcodeUnsupportedSymbology":"[{{symbology}}] \'{{itemName}}\' のデザイン時プレビューはサポートされません","basicSupportReportItemInfo":"{{itemLabel}} \'{{itemName}}\' はデザイン時はサポートされません","dvchartAxis":{"category":"カテゴリ","value":"値"},"dvchartChartTitle":"タイトル","dvchartColor":"カラー [{{index}}]","dvchartDetails":"詳細 [{{index}}]","dvchartShape":"シェイプ [{{index}}]","overflowPlaceholder":"{{overflowedItemName}} オーバーフロープレースホルダ"}},{"lng":"ja","ns":"chartWizard","resources":{"buttons":{"btnBack":"戻る","btnCancel":"キャンセル","btnFinish":"完了","btnNext":"次へ"},"customization":{"labels":{"area":"チャートエリア","axisX":"X軸","axisY":"Y軸","footer":"フッタ","header":"ヘッダ","legend":"凡例","plot":"プロット"}},"labelSelectDataSet":"データセット","labelSelectPalette":"パレット","settings":{"categories":{"category":"カテゴリ","subcategory":"サブカテゴリ","values":"データ"},"labels":{"aggregate":"集計","field":"フィールド","fieldClose":"終値フィールド","fieldEnd":"終了フィールド","fieldHigh":"高値フィールド","fieldLow":"安値フィールド","fieldOpen":"始値フィールド","fieldSize":"サイズフィールド","fieldStart":"開始フィールド","fieldX":"Xフィールド","fieldY":"Yフィールド","fields":"フィールド","gaugeLabel":"ゲージラベル","gaugeRanges":"ゲージ範囲","group":"内訳","lower":"開始フィールド","pointer":"ゲージポインタ","sortDirection":"並べ替え","upper":"終了フィールド"}},"templates":{"types":{"area":"エリア","bar":"横棒","bubble":"バブル","candlestick":"ローソク足","column":"縦棒","doughnut":"ドーナツ","funnel":"ファンネル","gantt":"ガント","gauge":"ゲージ","highLowClose":"HiLoClose","highLowOpenClose":"HiLoOpenClose","line":"折れ線","pie":"円","polarBar":"ポーラー","polarColumn":"スパイラル","pyramid":"ピラミッド","radarArea":"レーダー（エリア）","radarBubble":"レーダー（バブル）","radarLine":"レーダー（折れ線）","radarScatter":"レーダー（散布図）","rangeArea":"レンジエリア","rangeBar":"レンジ横棒","rangeColumn":"レンジ縦棒","scatter":"散布図"}},"textAdvancedCustomization":"カスタマイズ","textCustom":"カスタム","textEmpty":"未設定","textPreviewStep":"プレビュー","textTypeStep":"データおよび種類","titleChartWizard":"チャートウィザード","titlePreview":"プレビュー","titleSelectType":"データおよび種類の設定","titleSettings":"設定"}},{"lng":"ja","ns":"common","resources":{"btnCancel":"キャンセル","btnOk":"OK","btnSave":"保存","textCollapse":"折りたたみ","textDelete":"削除","textExpand":"展開","textOpen":"開く...","units":{"cm":{"textFullName":"センチメートル","textShortName":"cm"},"in":{"textFullName":"インチ","textShortName":"in"}}}},{"lng":"ja","ns":"components-RPX","resources":{"appBar":{"btnScript":"スクリプト"},"dataTab":{"titleDeleteDataSource":"データソースの削除","titleEditDataSource":"データソースの編集...","titleMoveParameterDown":"下に移動","titleMoveParameterUp":"上に移動"},"menu":{"btnReportExplorer":"エクスプローラ"},"propertyGrid":{"placeholderSearchBox":"プロパティ名を入力してください","textAlphabetical":"アルファベット順","textCategorized":"カテゴリ別","textMultipleTypes":"<複数のタイプ>","textSort":"並べ替え"},"scriptEditor":{"placeholder":"{{ScriptLanguage}} でコードを書いてください"},"stylesTab":{"textBasedOn":"\\"{{parentName}}\\" を継承","textRootStyle":"継承なし","titleAddStyle":"\\"{{parentName}}\\" を継承して作成する","titleDeleteStyle":"スタイルの削除"},"toolbar":{"home":{"backColor":"背景色","fontFamily":"フォント名","fontSize":"サイズ","fontStyle":"スタイル","fontWeight":"太さ","foreColor":"文字色","textDecoration":"文字飾り","titleAlignCenter":"中央揃え","titleAlignJustify":"両端揃え","titleAlignLeft":"左揃え","titleAlignRight":"右揃え","verticalAlignBottom":"下揃え","verticalAlignMiddle":"上下中央揃え","verticalAlignTop":"上揃え"},"script":{"events":{"report":{"textDataInitialize":"DataInitialize","textFetchData":"FetchData","textNoData":"NoData","textPageEnd":"PageEnd","textPageStart":"PageStart","textReportEnd":"ReportEnd","textReportStart":"ReportStart"},"section":{"textAfterPrint":"AfterPrint","textBeforePrint":"BeforePrint","textFormat":"Format"}},"textEvent":"イベント","textObject":"オブジェクト","titleEvent":"イベント","titleObject":"オブジェクト"}}}},{"lng":"ja","ns":"components","resources":{"appBar":{"btnFile":"ファイル","btnHome":"ホーム","btnInsert":"挿入","btnParameters":"パラメータ","btnPreview":"プレビュー","btnSaveAs":"名前を付けて保存","textUnsavedChanges":"未保存の変更","titleNew":"新規作成","titleOpen":"開く","titleRedo":"やり直し","titleSave":"保存","titleUndo":"元に戻す"},"chartPaletteDropdown":{"headingExtraPalettes":"テーマのカラーパレット","headingStandardPalettes":"通常のカラーパレット"},"dataFieldPickerDropdown":{"semantic":{"noMatchingAttributesRelationsFound":"一致するアイテムがみつかりません","searchPlaceholder":"アイテム名を入力してください..."}},"dataPanel":{"commonValues":{"currentDateTime":"日付と時刻","pageNM":"ページ番号 / 総ページ","pageNMCumulative":"ページ番号 / 総ページ（丁合い）","pageNMSection":"ページ番号 / 総ページ（セクション）","pageNofMLabel":"{{pageNumber}} & \\"/\\" & {{totalPages}}","pageNumber":"ページ番号","pageNumberCumulative":"ページ番号 (丁合い)","pageNumberSection":"ページ番号 (セクション)","reportFolder":"レポートフォルダ","reportName":"レポートの名前","totalPages":"総ページ","totalPagesCumulative":"総ページ (丁合い)","totalPagesSection":"総ページ (セクション)","userContext":"ユーザーコンテキスト","userId":"ユーザーID","userLanguage":"ユーザー言語"},"dataSets":{"placeholderEnterFieldName":"フィールド名を入力してください...","semantic":{"editDataSet":"データセットの編集...","loading":"読込中...","noMatchingAttributesRelationsFound":"合致する属性またはリレーションが見つかりません","searchPlaceholder":"属性またはリレーションを入力してください..."},"textNoMatchingFieldsFound":"一致するフィールドが見つかりません"},"fieldVariations":{"Date":[{"format":"=Year({fieldExpression})","label":"Year"},{"format":"=Year({fieldExpression}) & \\" Q\\" & DatePart(\\"q\\", {fieldExpression})","label":"Year-Quarter"},{"format":"=Year({fieldExpression}) & \\" \\" & MonthName(Month({fieldExpression}))","label":"Year-Month"},{"format":"=Year({fieldExpression}) & \\" \\" & MonthName(Month({fieldExpression})) & \\" \\" & Day({fieldExpression})","label":"Year-Month-Day"},{"format":"=Year({fieldExpression}) & \\" W\\" & DatePart(\\"ww\\", {fieldExpression})","label":"Year-Week"},{"format":"=DatePart(\\"q\\", {fieldExpression})","label":"Quarter"},{"format":"=Month({fieldExpression})","label":"Month"},{"format":"=MonthName(Month({fieldExpression}))","label":"MonthName"},{"format":"=DatePart(\\"ww\\", {fieldExpression})","label":"Week"},{"format":"=Day({fieldExpression})","label":"Day"},{"format":"=WeekdayName(Weekday({fieldExpression}))","label":"DayOfWeek"}],"DateTime":[{"format":"=Year({fieldExpression})","label":"Year"},{"format":"=Year({fieldExpression}) & \\" Q\\" & DatePart(\\"q\\", {fieldExpression})","label":"Year-Quarter"},{"format":"=Year({fieldExpression}) & \\" \\" & MonthName(Month({fieldExpression}))","label":"Year-Month"},{"format":"=Year({fieldExpression}) & \\" \\" & MonthName(Month({fieldExpression})) & \\" \\" & Day({fieldExpression})","label":"Year-Month-Day"},{"format":"=Year({fieldExpression}) & \\" W\\" & DatePart(\\"ww\\", {fieldExpression})","label":"Year-Week"},{"format":"=DatePart(\\"q\\", {fieldExpression})","label":"Quarter"},{"format":"=Month({fieldExpression})","label":"Month"},{"format":"=MonthName(Month({fieldExpression}))","label":"MonthName"},{"format":"=DatePart(\\"ww\\", {fieldExpression})","label":"Week"},{"format":"=Day({fieldExpression})","label":"Day"},{"format":"=WeekdayName(Weekday({fieldExpression}))","label":"DayOfWeek"}]},"headingEditParameter":"パラメータの編集","semantic":{"textAttributesCount_0":"{{count}} 属性","textNoAttributes":"属性なし","textNoRelations":"リレーションなし","textRelationsCount_0":"{{count}} リレーション"},"textBasedOnDataSource":"{{dataSourceName}} データソース","textFieldsCount_0":"{{count}} フィールド","textModelVersion":"バージョン: {{version}}","textSharedReference":"共有参照","titleAddDataSet":"データセットの編集...","titleEditDataSet":"データセットの編集...","titleEditDataSource":"データソースの編集...","titleMore":"さらに表示...","titleMoveParameterDown":"下に移動","titleMoveParameterUp":"上に移動","titleSelectFields":"フィールドを選択..."},"fieldPicker":{"placeholderEnterFieldName":"フィールド名で検索"},"layerList":{"btnAddLayer":"追加","titleDefaultLayerCannotBeDeleted":"デフォルトのレイヤーは削除できません","titleDeleteLayer":"削除","titleEditLayer":"レイヤーの編集","titleHideLayer":"非表示","titleLockLayer":"ロック","titleShowLayer":"表示","titleUnlockLayer":"ロック解除"},"libraries":{"textEmpty":"なし","textError":"エラー","textNoLibraries":"ライブラリがありません","textNoReportParts":"ライブラリにレポートパーツが含まれていません"},"menu":{"btnBack":"戻る","btnClose":"閉じる","btnGroupEditor":"グループエディタ","btnLayerList":"レイヤー","btnLibrariesList":"ライブラリ","btnReportExplorer":"エクスプローラ","titleBack":"戻る","titlePin":"ピン留め"},"notifications":{"btnDismiss":"無視","btnDismissAll":"すべて無視","headingError_0":"{{count}} エラー","headingNotification_0":"{{count}} 個の通知","headingWarning_0":"{{count}} 警告","titleNotifications":"通知"},"propertyGrid":{"options":{"categories":"カテゴリー","collapse":"すべて折りたたむ","expand":"すべて展開する","hideAdvancedProperty":"詳細プロパティを隠す","showAdvancedProperty":"詳細プロパティを表示する"},"placeholderSearchBox":"プロパティ名を入力してください","textMultipleTypes":"<複数のタイプ>","textReportItems":"<レポートアイテム>"},"statusBar":{"btnPropertiesMode":"プロパティモード","btnShowAdvancedProperties":"詳細プロパティを表示","btnShowBasicProperties":"基本プロパティを表示","common":{"textDisabled":"無効","textEnabled":"有効"},"snapSettings":{"labelGridSize":"グリッドの幅","labelSnapToGrid":"グリッドにスナップ","labelSnapToGuides":"ガイドにスナップ","titleSnapDisabled":"スナップ無効","titleSnapEnabled":"スナップ有効"},"themePicker":{"themes":{"activeReports":"グリーン","activeReportsDark":"グリーン（ダーク）","darkOled":"有機EL（ダーク）","default":"デフォルト","defaultDark":"デフォルト（ダーク）","highContrast":"ハイコントラスト","highContrastDark":"ハイコントラスト（ダーク）","system":"システムテーマ"},"titleTheme":"テーマ"},"titleHideGrid":"グリッドを隠す","titleHideRulers":"ルーラーを隠す","titleShowGrid":"グリッドを表示する","titleShowRulers":"ルーラーを表示する","titleZoomIn":"拡大","titleZoomOut":"縮小"},"tabs":{"actions":{"textDuplicate":"コピー","textHide":"非表示","textInsert":"挿入","textMDelete":"削除","textMoveLeft":"左に移動","textMoveRight":"右に移動","textShow":"表示"},"btnAddPage":"ページの追加","btnAddSection":"エリアの追加","textPage":"ページ"},"themeEditor":{"labelNone":"なし"},"title":{"textUntitled":"新規レポート"},"toolbar":{"home":{"textExpression":"<式>","textExpressionCompact":"<式>","titleAlignCenter":"中央揃え","titleAlignJustify":"両端揃え","titleAlignLeft":"左揃え","titleAlignRight":"右揃え","titleBackgroundColor":"背景色","titleCopy":"コピー","titleCut":"切り取り","titleFontFamily":"フォント名","titleFontSize":"サイズ","titleFontStyle":"スタイル","titleFontWeight":"太さ","titlePaste":"貼り付け","titleTextColor":"文字色","titleTextDecoration":"文字飾り","titleVerticalAlignBottom":"下揃え","titleVerticalAlignMiddle":"上下中央揃え","titleVerticalAlignTop":"上揃え"},"titleExpand":"展開"}}},{"lng":"ja","ns":"contextActions-RPX","resources":{"labels":{"addGroupHeaderFooter":"グループヘッダ／フッタの追加","addPageHeaderFooter":"ページヘッダ／フッタの追加","addReportHeaderFooter":"レポートヘッダ／フッタの追加","copy":"コピー","cut":"切り取り","delete":"削除","deletePageHeaderFooter":"ページヘッダ／フッタの削除","deleteReportHeaderFooter":"レポートヘッダ／フッタの削除","layout":{"alignToBottoms":"下揃え","alignToCenters":"左右中央整列","alignToGrid":"グリッドに合わせて整列","alignToLefts":"左揃え","alignToMiddles":"上下中央整列","alignToRights":"右揃え","alignToTops":"上揃え","bringToFront":"最前面へ移動","horizontal":{"decreaseSpacing":"左右の間隔を狭くする","increaseSpacing":"左右の間隔を広くする","makeSpacingEqual":"左右の間隔を均等にする","removeSpacing":"左右の間隔を削除する"},"makeSameHeight":"高さを揃える","makeSameSize":"同じサイズに揃える","makeSameWidth":"幅を揃える","sendToBack":"最背面へ移動","separator":{"corelateControlSizing":"コントロールサイズの相関","horizontalAlignment":"水平配置","horizontalSpacing":"水平間隔","sortControls":"コントロールの並べ替え","verticalAlignment":"垂直配置","verticalSpacing":"垂直間隔"},"sizeToGrid":"サイズをグリッドに合わせる","title":"レイアウト","vertical":{"decreaseSpacing":"上下の間隔を狭くする","increaseSpacing":"上下の間隔を広くする","makeSpacingEqual":"上下の間隔を均等にする","removeSpacing":"上下の間隔を削除する"}},"pageFooter":"PageFooter","pageHeader":"PageHeader","paste":"貼り付け","report":"レポート","reportFooter":"ReportFooter","reportHeader":"ReportHeader"}}},{"lng":"ja","ns":"contextActions","resources":{"bandedList":{"addFooter":"フッタの追加","addGroupFooter":"グループフッタの追加","addGroupHeader":"グループヘッダの追加","addHeader":"ヘッダの追加","deleteGroup":"グループの削除","groupTitle":"グループ","insertGroup":"グループの挿入","removeFooter":"フッタの削除","removeGroupFooter":"グループフッタの削除","removeGroupHeader":"グループヘッダの削除","removeHeader":"ヘッダの削除","title":"BandedList"},"chart":"Chart","container":{"delete":"削除","expression":"式"},"dashboard":{"duplicateSection":"エリアのコピー","hideSection":"エリアを隠す","moveSectionLeft":"エリアを左に移動","moveSectionRight":"エリアを右に移動","removeSection":"エリアの削除","showSection":"エリアを表示する","switchTheme":"テーマの切り替え","title":"ダッシュボード"},"dvchart":{"palette":"グラフの色パレットの設定","presetGroups":{"area":"エリア","bar":"横棒","column":"縦棒","line":"折れ線","misc":"その他","pie":"円","polarBar":"ポーラー","polarColumn":"スパイラル","radar":"レーダー","range":"レンジ"},"presets":{"area":"エリア","areaPercentStacked":"積層エリア（％）","areaStacked":"積層エリア","bar":"横棒","barPercentStacked":"積層横棒（％）","barStacked":"積層横棒","bubble":"バブル","candlestick":"ローソク足","column":"縦棒","columnPercentStacked":"積層縦棒（％）","columnStacked":"積層縦棒","doughnut":"ドーナツ","funnel":"ファンネル","gantt":"ガント","gauge":"ゲージ","highLowClose":"HiLoClose","highLowOpenClose":"HiLoOpenClose","line":"折れ線","lineSmooth":"平滑線","pie":"円","polarBar":"ポーラー","polarBarPercentStacked":"積層ポーラー（％）","polarBarStacked":"積層ポーラー","polarColumn":"スパイラル","polarColumnPercentStacked":"積層スパイラル（％）","polarColumnStacked":"積層スパイラル","pyramid":"ピラミッド","radarArea":"レーダーエリア","radarBubble":"レーダーバブル","radarLine":"レーダー折れ線","radarScatter":"レーダー散布図","rangeArea":"レンジエリア","rangeBar":"レンジ横棒","rangeColumn":"レンジ縦棒","scatter":"散布図","title":"プロットのテンプレートの設定"}},"report":{"addContinuousSection":"エリアの追加","addFixedPageSection":"エリアの追加","changeMasterReport":"マスターレポートの変更","convertToMasterReport":"マスターレポートに変換","deletePage":"ページの削除","duplicatePage":"ページのコピー","duplicateSection":"エリアのコピー","hidePage":"ページを隠す","hideSection":"エリアを隠す","insertPage":"ページの挿入","insertSection":"エリアの挿入","layout":{"alignToBottoms":"下揃え","alignToCenters":"左右中央整列","alignToGrid":"グリッドに合わせて整列","alignToLefts":"左揃え","alignToMiddles":"上下中央整列","alignToRights":"右揃え","alignToTops":"上揃え","bringToFront":"最前面へ移動","horizontal":{"decreaseSpacing":"左右の間隔を狭くする","increaseSpacing":"左右の間隔を広くする","makeSpacingEqual":"左右の間隔を均等にする","removeSpacing":"左右の間隔を削除する"},"horizontalAlignment":"水平配置","makeSameHeight":"高さを揃える","makeSameSize":"同じサイズに揃える","makeSameWidth":"幅を揃える","sendToBack":"最背面へ移動","separator":{"corelateControlSizing":"コントロールサイズの相関","horizontalSpacing":"水平間隔","sortControls":"コントロールの並べ替え","verticalSpacing":"垂直間隔"},"sizeToGrid":"サイズをグリッドに合わせる","title":"レイアウト","vertical":{"decreaseSpacing":"上下の間隔を狭くする","increaseSpacing":"上下の間隔を広くする","makeSpacingEqual":"上下の間隔を均等にする","removeSpacing":"上下の間隔を削除する"},"verticalAlignment":"垂直配置"},"movePageBackward":"ページを前に移動","movePageForward":"ページを後ろに移動","moveSectionLeft":"エリアを左に移動","moveSectionRight":"エリアを右に移動","pages":"ページ","removeSection":"エリアの削除","reportParts":{"title":"レポートパーツ","titleCreateReportPart":"レポートパーツの作成"},"setMasterReport":"マスターレポートの設定","showPage":"ページを表示する","showSection":"エリアを表示する","switchTheme":"テーマの切り替え","title":"レポート"},"reportSection":{"addFooter":"フッタの追加","addHeader":"ヘッダの追加","removeFooter":"フッタの削除","removeHeader":"ヘッダの削除","title":"エリア"},"table":{"addDetails":"詳細行の追加","addFooter":"フッタの追加","addGroupFooter":"グループフッタの追加","addGroupHeader":"グループヘッダの追加","addHeader":"ヘッダの追加","cellsTitle":"セル","columnTitle":"列","deleteColumn":"列の削除","deleteGroup":"グループの削除","deleteRow":"行の削除","expression":"式","groupTitle":"グループ","insertColumn":{"left":"左","right":"右"},"insertColumnTitle":"列の挿入","insertGroup":"グループの挿入","insertRow":{"above":"上","below":"下"},"insertRowTitle":"行の挿入","mergeCells":"セルの結合","more":"さらに表示...","removeDetails":"詳細行の削除","removeFooter":"フッタの削除","removeGroupFooter":"グループフッタの削除","removeGroupHeader":"グループヘッダの削除","removeHeader":"ヘッダの削除","rowTitle":"行","splitCells":"セルの分割","title":"Table"},"tablix":{"addGroup":{"adjacentAfter":"後に隣接","adjacentBefore":"前に隣接","child":"子","parent":"親"},"addGroupTitle":"グループの追加","addTotal":{"contextMenuAfter":"合計を後に追加","contextMenuBefore":"合計を前に追加"},"cellsTitle":"セル","columnGroup":"列グループ","columnTitle":"列","delete":"削除","deleteColumn":"列の削除","deleteRow":"行の削除","disableGroup":"グループの無効化","enableGroup":"グループの有効化","expression":"式","insertColumn":{"insideGroupLeft":"内側のグループ - 左","insideGroupRight":"内側のグループ - 右","left":"左","outsideGroupLeft":"外側のグループ - 左","outsideGroupRight":"外側のグループ - 右","right":"右"},"insertColumnTitle":"列の挿入","insertRow":{"above":"上","below":"下","insideGroupAbove":"内側のグループ - 上","insideGroupBelow":"内側のグループ - 下","outsideGroupAbove":"外側のグループ - 上","outsideGroupBelow":"外側のグループ - 下"},"insertRowTitle":"行の挿入","mergeCells":"セルの結合","more":"さらに表示...","rowGroup":"行グループ","rowTitle":"行","splitCells":"セルの分割","totalTitle":"合計"}}},{"lng":"ja","ns":"defaults","resources":{"chart":{"innerRadius":0.5,"startAngle":0}}},{"lng":"ja","ns":"dialogs","resources":{"btnCancel":"キャンセル","btnInsert":"挿入","common":{"textCancel":"キャンセル"},"dataVisualizer":{"title":"データ可視化"},"expressionEditor":{"headingExpression":"式","headingFunctions":"関数","headingInfo":"情報","headingValues":"値","infoPanel":{"labelConstant":"定数:","labelDescription":"説明:","labelExample":"例:","labelName":"名前:","labelSyntax":"構文:"},"placeholderExpression":"式","search":{"placeholderSearch":"検索...","textNoResults":"\\"{{query}}\\"に一致する結果が見つかりません","textStartTyping":"検索ワードを入力してください"},"subtitle":"式エディタ"},"headingInsertColumns":"列を挿入","headingInsertRows":"行を挿入","labelCount":"カウント","labelPosition":"位置"}},{"lng":"ja","ns":"documentItems-RPX","resources":{"Barcode":"Barcode","CheckBox":"CheckBox","CrossSectionBox":"CrossSectionBox","CrossSectionLine":"CrossSectionLine","Detail":"Detail","GroupFooter":"Group Footer","GroupHeader":"Group Header","InputFieldCheckBox":"InputFieldCheckBox","InputFieldText":"InputFieldText","Label":"Label","Line":"Line","PageBreak":"PageBreak","PageFooter":"Page Footer","PageHeader":"Page Header","Picture":"Picture","Report":"Report","ReportFooter":"Report Footer","ReportHeader":"Report Header","ReportInfo":"ReportInfo","RichTextBox":"RichTextBox","Shape":"Shape","SubReport":"SubReport","TextBox":"TextBox","Unknown":"Unknown","captions":{"basicSupportReportItemInfo":"{{itemLabel}} \'{{itemName}}\' は、デザイン時の機能に制限があります。"}}},{"lng":"ja","ns":"documentsAPI","resources":{"romLabels":{"chart":"Chart","dvchart":"DV.Chart","matrix":"Matrix","table":"Table","tablix":"Tablix"},"textOpenDocumentWarnings":"ドキュメントを開く際に警告が発生しました。","textRenamedItemPrefix":"Content_{{originalName}}","textReportConversion":"レポートを開く際にエラーが発生しました。","transform":{"helpLink":"レポート項目の変換に関する情報については、{{link}}を参照してください","textBadReportItem":"✘ {{SourceType}} \\"{{Name}}\\" は、{{ResultType}} に変換できませんでした。","textError":"– [{{ErrorType}}] {{Message}}","textReport":"レポート \\"{{reportName}}\\" が変換されました。","textReportItem":"✔ {{SourceType}} \\"{{Name}}\\" は、{{ResultType}} に変換されました。"}}},{"lng":"ja","ns":"enums-RPX","resources":{"background_style":{"Gradient":"Gradient","Pattern":"Pattern","Solid":"Solid"},"barcode_caption_position":{"Above":"Above","Below":"Below","None":"None"},"barcode_rotation":{"None":"None","Rotate180Degrees":"Rotate180Degrees","Rotate270Degrees":"Rotate270Degrees","Rotate90Degrees":"Rotate90Degrees"},"barcode_style":{"Ansi39":"Ansi39","Ansi39x":"Ansi39x","Aztec":"Aztec","BC412":"BC412","Codabar":"Codabar","Code25intlv":"Code25intlv","Code25mat":"Code25mat","Code39":"Code39","Code39x":"Code39x","Code49":"Code49","Code93x":"Code93x","Code_11":"Code_11","Code_128_A":"Code_128_A","Code_128_B":"Code_128_B","Code_128_C":"Code_128_C","Code_128auto":"Code_128auto","Code_2_of_5":"Code_2_of_5","Code_93":"Code_93","DataMatrix":"DataMatrix","EAN128FNC1":"EAN128FNC1","EAN_13":"EAN_13","EAN_8":"EAN_8","GS1DataMatrix":"GS1DataMatrix","GS1QRCode":"GS1QRCode","HIBCCode128":"HIBCCode128","HIBCCode39":"HIBCCode39","IATA_2_of_5":"IATA_2_of_5","ISBN":"ISBN","ISMN":"ISMN","ISSN":"ISSN","ITF14":"ITF14","IntelligentMail":"IntelligentMail","IntelligentMailPackage":"IntelligentMailPackage","JapanesePostal":"JapanesePostal","MSI":"MSI","Matrix_2_of_5":"Matrix_2_of_5","MaxiCode":"MaxiCode","MicroPDF417":"MicroPDF417","MicroQRCode":"MicroQRCode","None":"None","PZN":"PZN","Pdf417":"Pdf417","Pharmacode":"Pharmacode","Plessey":"Plessey","PostNet":"PostNet","QRCode":"QRCode","RM4SCC":"RM4SCC","RSS14":"RSS14","RSS14Stacked":"RSS14Stacked","RSS14StackedOmnidirectional":"RSS14StackedOmnidirectional","RSS14Truncated":"RSS14Truncated","RSSExpanded":"RSSExpanded","RSSExpandedStacked":"RSSExpandedStacked","RSSLimited":"RSSLimited","SSCC_18":"SSCC_18","Telepen":"Telepen","UCCEAN128":"UCCEAN128","UPC_A":"UPC_A","UPC_E0":"UPC_E0","UPC_E1":"UPC_E1"},"border_style":{"Dash":"Dash","DashDot":"DashDot","Dot":"Dot","Double":"Double","ExtraThickSolid":"ExtraThickSolid","None":"None","Solid":"Solid","ThickDash":"ThickDash","ThickDashDot":"ThickDashDot","ThickDashDotDot":"ThickDashDotDot","ThickDot":"ThickDot","ThickDouble":"ThickDouble","ThickSolid":"ThickSolid"},"border_style_inputfield":{"Dashed":"破線","Inset":"インセット","None":"なし","Solid":"実線"},"calculated_field_type":{"Boolean":"Boolean","Date":"Date","Double":"Double","Float":"Float","Int32":"Int32","None":"None","String":"String"},"calendar":{"Gregorian":"Gregorian","GregorianArabic":"Gregorian Arabic","GregorianMiddleEastFrench":"Gregorian Middle East French","GregorianTransliteratedEnglish":"Gregorian Transliterated English","GregorianTransliteratedFrench":"Gregorian Transliterated French","GregorianUSEnglish":"Gregorian US English","Hebrew":"Hebrew","Hijri":"Hijri","Japanese":"Japanese","Korea":"Korea","Taiwan":"Taiwan","ThaiBuddhist":"Thai Buddhist"},"check_style":{"Check":"チェック","Circle":"サークル","Cross":"クロス","Diamond":"ダイアモンド","Square":"スクエア","Star":"スター"},"collate":{"Collate":"Collate","Default":"Default","DontCollate":"DontCollate"},"column_direction":{"AcrossDown":"AcrossDown","DownAcross":"DownAcross"},"compatibility_mode":{"CrossPlatform":"CrossPlatform","GDI":"GDI"},"content_alignment":{"BottomCenter":"BottomCenter","BottomLeft":"BottomLeft","BottomRight":"BottomRight","MiddleCenter":"MiddleCenter","MiddleLeft":"MiddleLeft","MiddleRight":"MiddleRight","TopCenter":"TopCenter","TopLeft":"TopLeft","TopRight":"TopRight"},"culture":{"af-ZA":"Afrikaans (South Africa)","ar-AE":"Arabic (U.A.E.)","ar-BH":"Arabic (Bahrain)","ar-DZ":"Arabic (Algeria)","ar-EG":"Arabic (Egypt)","ar-IQ":"Arabic (Iraq)","ar-JO":"Arabic (Jordan)","ar-KW":"Arabic (Kuwait)","ar-LB":"Arabic (Lebanon)","ar-LY":"Arabic (Libya)","ar-MA":"Arabic (Morocco)","ar-OM":"Arabic (Oman)","ar-QA":"Arabic (Qatar)","ar-SA":"Arabic (Saudi Arabia)","ar-SY":"Arabic (Syria)","ar-TN":"Arabic (Tunisia)","ar-YE":"Arabic (Yemen)","az-Cyrl-AZ":"Azeri (Cyrillic, Azerbaijan)","az-Latn-AZ":"Azeri (Latin, Azerbaijan)","be-BY":"Belarusian (Belarus)","bg-BG":"Bulgarian (Bulgaria)","ca-ES":"Catalan (Catalan)","cs-CZ":"Czech (Czech Republic)","da-DK":"Danish (Denmark)","de-AT":"German (Austria)","de-CH":"German (Switzerland)","de-DE":"German (Germany)","de-LI":"German (Liechtenstein)","de-LU":"German (Luxembourg)","dv-MV":"Divehi (Maldives)","el-GR":"Greek (Greece)","en-029":"English (Caribbean)","en-AU":"English (Australia)","en-BZ":"English (Belize)","en-CA":"English (Canada)","en-GB":"English (United Kingdom)","en-IE":"English (Ireland)","en-JM":"English (Jamaica)","en-NZ":"English (New Zealand)","en-PH":"English (Republic of the Philippines)","en-TT":"English (Trinidad and Tobago)","en-US":"English (United States)","en-ZA":"English (South Africa)","en-ZW":"English (Zimbabwe)","es-AR":"Spanish (Argentina)","es-BO":"Spanish (Bolivia)","es-CL":"Spanish (Chile)","es-CO":"Spanish (Colombia)","es-CR":"Spanish (Costa Rica)","es-DO":"Spanish (Dominican Republic)","es-EC":"Spanish (Ecuador)","es-ES":"Spanish (Spain)","es-GT":"Spanish (Guatemala)","es-HN":"Spanish (Honduras)","es-MX":"Spanish (Mexico)","es-NI":"Spanish (Nicaragua)","es-PA":"Spanish (Panama)","es-PE":"Spanish (Peru)","es-PR":"Spanish (Puerto Rico)","es-PY":"Spanish (Paraguay)","es-SV":"Spanish (El Salvador)","es-UY":"Spanish (Uruguay)","es-VE":"Spanish (Venezuela)","et-EE":"Estonian (Estonia)","eu-ES":"Basque (Basque)","fa-IR":"Persian (Iran)","fi-FI":"Finnish (Finland)","fo-FO":"Faroese (Faroe Islands)","fr-BE":"French (Belgium)","fr-CA":"French (Canada)","fr-CH":"French (Switzerland)","fr-FR":"French (France)","fr-LU":"French (Luxembourg)","fr-MC":"French (Principality of Monaco)","gl-ES":"Galician (Galician)","gu-IN":"Gujarati (India)","he-IL":"Hebrew (Israel)","hi-IN":"Hindi (India)","hr-BA":"Croatian (Bosnia and Herzegovina)","hr-HR":"Croatian (Croatia)","hu-HU":"Hungarian (Hungary)","hy-AM":"Armenian (Armenia)","id-ID":"Indonesian (Indonesia)","is-IS":"Icelandic (Iceland)","it-CH":"Italian (Switzerland)","it-IT":"Italian (Italy)","ja-JP":"Japanese (Japan)","ka-GE":"Georgian (Georgia)","kk-KZ":"Kazakh (Kazakhstan)","kn-IN":"Kannada (India)","ko-KR":"Korean (Korea)","kok-IN":"Konkani (India)","ky-KG":"Kyrgyz (Kyrgyzstan)","lt-LT":"Lithuanian (Lithuania)","lv-LV":"Latvian (Latvia)","mk-MK":"Macedonian (Former Yugoslav Republic of Macedonia)","mn-MN":"Mongolian (Cyrillic, Mongolia)","mr-IN":"Marathi (India)","ms-BN":"Malay (Brunei Darussalam)","ms-MY":"Malay (Malaysia)","nb-NO":"Norwegian, BokmÃ¥l (Norway)","nl-BE":"Dutch (Belgium)","nl-NL":"Dutch (Netherlands)","nn-NO":"Norwegian, Nynorsk (Norway)","pa-IN":"Punjabi (India)","pl-PL":"Polish (Poland)","pt-BR":"Portuguese (Brazil)","pt-PT":"Portuguese (Portugal)","ro-RO":"Romanian (Romania)","ru-RU":"Russian (Russia)","sa-IN":"Sanskrit (India)","sk-SK":"Slovak (Slovakia)","sl-SI":"Slovenian (Slovenia)","sq-AL":"Albanian (Albania)","sv-FI":"Swedish (Finland)","sv-SE":"Swedish (Sweden)","sw-KE":"Kiswahili (Kenya)","syr-SY":"Syriac (Syria)","ta-IN":"Tamil (India)","te-IN":"Telugu (India)","th-TH":"Thai (Thailand)","tr-TR":"Turkish (Turkey)","tt-RU":"Tatar (Russia)","uk-UA":"Ukrainian (Ukraine)","ur-PK":"Urdu (Islamic Republic of Pakistan)","uz-Cyrl-UZ":"Uzbek (Cyrillic, Uzbekistan)","uz-Latn-UZ":"Uzbek (Latin, Uzbekistan)","vi-VN":"Vietnamese (Vietnam)","zh-CN":"Chinese (People\'s Republic of China)","zh-HK":"Chinese (Hong Kong S.A.R.)","zh-MO":"Chinese (Macao S.A.R.)","zh-SG":"Chinese (Singapore)","zh-TW":"Chinese (Taiwan)"},"ddo_char_set":{"Arab":"Arab","Baltic":"Baltic","CentralEuropean":"Central European","Cyrillic":"Cyrillic","Greek":"Greek","Hebrew":"Hebrew","Turkish":"Turkish","Vietnamese":"Vietnamese","Western":"Western"},"duplex":{"Default":"Default","Horizontal":"Horizontal","Simplex":"Simplex","Vertical":"Vertical"},"field_type":{"Boolean":"Boolean","Date":"Date","Double":"Double","Float":"Float","Int32":"Int32","Integer":"Integer","None":"None","String":"String"},"font_size":{"10pt":"10pt","11pt":"11pt","12pt":"12pt","14pt":"14pt","16pt":"16pt","18pt":"18pt","20pt":"20pt","22pt":"22pt","24pt":"24pt","26pt":"26pt","28pt":"28pt","36pt":"36pt","48pt":"48pt","72pt":"72pt","8pt":"8pt","9pt":"9pt"},"font_style":{"Italic":"Italic","Normal":"Normal"},"font_weight":{"Bold":"Bold","Normal":"Normal"},"format_string":{"Page_PageNumber_of_PageCount":"","Page_PageNumber_of_PageCount_on_RunDateTime":"{PageNumber} / {PageCount} ページ ({RunDateTime:yyyy年M月d日})","RunDateTime":"{RunDateTime:}","RunDateTime_MMMM_d_yyyy":"{RunDateTime:MMMM d, yyyy}","RunDateTime_MMMM_yy":"{RunDateTime:MMMM-yy}","RunDateTime_MMMM_yyyy":"{RunDateTime:MMMM-yyyy}","RunDateTime_MMM_yy":"{RunDateTime:MMM-yy}","RunDateTime_MMM_yyyy":"{RunDateTime:MMM-yyyy}","RunDateTime_MM_dd_yy":"{RunDateTime:MM/dd/yy}","RunDateTime_MM_dd_yyyy":"{RunDateTime:MM/dd/yyyy}","RunDateTime_M_d":"{RunDateTime:M/d}","RunDateTime_M_d_yy":"{RunDateTime:M/d/yy}","RunDateTime_M_d_yy_h_mm":"{RunDateTime:M/d/yy h:mm}","RunDateTime_M_d_yy_h_mm_tt":"{RunDateTime:M/d/yy h:mm tt}","RunDateTime_M_d_yyyy":"{RunDateTime:M/d/yyyy}","RunDateTime_M_d_yyyy_h_mm":"{RunDateTime:M/d/yyyy h:mm}","RunDateTime_M_d_yyyy_h_mm_tt":"{RunDateTime:M/d/yyyy h:mm tt}","RunDateTime_M_d_yyyy}":"{RunDateTime:M/d/yyyy}","RunDateTime_d_MMM":"{RunDateTime:d-MMM}","RunDateTime_d_MMM_yy":"{RunDateTime:d-MMM-yy}","RunDateTime_d_MMM_yyyy":"{RunDateTime:d-MMM-yyyy}","RunDateTime_dd_MMM_yy":"{RunDateTime:dd-MMM-yy}","RunDateTime_dd_MMM_yyyy":"{RunDateTime:dd-MMM-yyyy}"},"gradient_style":{"DiagonalDown":"DiagonalDown","DiagonalUp":"DiagonalUp","FromCenter":"FromCenter","FromCorner":"FromCorner","Horizontal":"Horizontal","Vertical":"Vertical"},"group_keep_together":{"All":"All","FirstDetail":"FirstDetail","None":"None"},"hatch_style":{"BackwardDiagonal":"BackwardDiagonal","DarkDownwardDiagonal":"DarkDownwardDiagonal","DarkHorizontal":"DarkHorizontal","DarkUpwardDiagonal":"DarkUpwardDiagonal","DarkVertical":"DarkVertical","DashedDownwardDiagonal":"DashedDownwardDiagonal","DashedHorizontal":"DashedHorizontal","DashedUpwardDiagonal":"DashedUpwardDiagonal","DashedVertical":"DashedVertical","DiagonalBrick":"DiagonalBrick","DiagonalCross":"DiagonalCross","Divot":"Divot","DottedDiamond":"DottedDiamond","DottedGrid":"DottedGrid","ForwardDiagonal":"ForwardDiagonal","Horizontal":"Horizontal","HorizontalBrick":"HorizontalBrick","LargeCheckerBoard":"LargeCheckerBoard","LargeConfetti":"LargeConfetti","LargeGrid":"LargeGrid","LightDownwardDiagonal":"LightDownwardDiagonal","LightHorizontal":"LightHorizontal","LightUpwardDiagonal":"LightUpwardDiagonal","LightVertical":"LightVertical","NarrowHorizontal":"NarrowHorizontal","NarrowVertical":"NarrowVertical","OutlinedDiamond":"OutlinedDiamond","Percent05":"Percent05","Percent10":"Percent10","Percent20":"Percent20","Percent25":"Percent25","Percent30":"Percent30","Percent40":"Percent40","Percent50":"Percent50","Percent60":"Percent60","Percent70":"Percent70","Percent75":"Percent75","Percent80":"Percent80","Percent90":"Percent90","Plaid":"Plaid","Shingle":"Shingle","SmallCheckerBoard":"SmallCheckerBoard","SmallConfetti":"SmallConfetti","SmallGrid":"SmallGrid","SolidDiamond":"SolidDiamond","Sphere":"Sphere","Trellis":"Trellis","Vertical":"Vertical","Wave":"Wave","Weave":"Weave","WideDownwardDiagonal":"WideDownwardDiagonal","WideUpwardDiagonal":"WideUpwardDiagonal","ZigZag":"ZigZag"},"kinsoku":{"Auto":"Auto","None":"None","True":"True"},"line_style":{"Dash":"Dash","DashDot":"DashDot","DashDotDot":"DashDotDot","Dot":"Dot","Double":"Double","Solid":"Solid","Transparent":"Transparent"},"mime_type":{"image/bmp":"image/bmp","image/gif":"image/gif","image/jpeg":"image/jpeg","image/png":"image/png","image/x-emf":"image/x-emf","image/x-wmf":"image/x-wmf"},"new_column":{"After":"After","Before":"Before","BeforeAfter":"BeforeAfter","None":"None"},"new_page":{"After":"After","Before":"Before","BeforeAfter":"BeforeAfter","None":"None"},"page_orientation":{"Default":"Default","Landscape":"Landscape","Portrait":"Portrait"},"paper_kind":{"A2":"A2","A3":"A3","A3Extra":"A3Extra","A3ExtraTransverse":"A3ExtraTransverse","A3Rotated":"A3Rotated","A3Transverse":"A3Transverse","A4":"A4","A4Extra":"A4Extra","A4Plus":"A4Plus","A4Rotated":"A4Rotated","A4Small":"A4Small","A4Transverse":"A4Transverse","A5":"A5","A5Extra":"A5Extra","A5Rotated":"A5Rotated","A5Transverse":"A5Transverse","A6":"A6","A6Rotated":"A6Rotated","APlus":"APlus","B4":"B4","B4Envelope":"B4Envelope","B4JisRotated":"B4JisRotated","B5":"B5","B5Envelope":"B5Envelope","B5Extra":"B5Extra","B5JisRotated":"B5JisRotated","B5Transverse":"B5Transverse","B6Envelope":"B6Envelope","B6Jis":"B6Jis","B6JisRotated":"B6JisRotated","BPlus":"BPlus","C3Envelope":"C3Envelope","C4Envelope":"C4Envelope","C5Envelope":"C5Envelope","C65Envelope":"C65Envelope","C6Envelope":"C6Envelope","CSheet":"CSheet","Custom":"Custom","DLEnvelope":"DLEnvelope","DSheet":"DSheet","ESheet":"ESheet","Executive":"Executive","Folio":"Folio","GermanLegalFanfold":"GermanLegalFanfold","GermanStandardFanfold":"GermanStandardFanfold","InviteEnvelope":"InviteEnvelope","IsoB4":"IsoB4","ItalyEnvelope":"ItalyEnvelope","JapaneseDoublePostcard":"JapaneseDoublePostcard","JapaneseDoublePostcardRotated":"JapaneseDoublePostcardRotated","JapaneseEnvelopeChouNumber3":"JapaneseEnvelopeChouNumber3","JapaneseEnvelopeChouNumber3Rotated":"JapaneseEnvelopeChouNumber3Rotated","JapaneseEnvelopeChouNumber4":"JapaneseEnvelopeChouNumber4","JapaneseEnvelopeChouNumber4Rotated":"JapaneseEnvelopeChouNumber4Rotated","JapaneseEnvelopeKakuNumber2":"JapaneseEnvelopeKakuNumber2","JapaneseEnvelopeKakuNumber2Rotated":"JapaneseEnvelopeKakuNumber2Rotated","JapaneseEnvelopeKakuNumber3":"JapaneseEnvelopeKakuNumber3","JapaneseEnvelopeKakuNumber3Rotated":"JapaneseEnvelopeKakuNumber3Rotated","JapaneseEnvelopeYouNumber4":"JapaneseEnvelopeYouNumber4","JapaneseEnvelopeYouNumber4Rotated":"JapaneseEnvelopeYouNumber4Rotated","JapanesePostcard":"JapanesePostcard","JapanesePostcardRotated":"JapanesePostcardRotated","Ledger":"Ledger","Legal":"Legal","LegalExtra":"LegalExtra","Letter":"Letter","LetterExtra":"LetterExtra","LetterExtraTransverse":"LetterExtraTransverse","LetterPlus":"LetterPlus","LetterRotated":"LetterRotated","LetterSmall":"LetterSmall","LetterTransverse":"LetterTransverse","MonarchEnvelope":"MonarchEnvelope","Note":"Note","Number10Envelope":"Number10Envelope","Number11Envelope":"Number11Envelope","Number12Envelope":"Number12Envelope","Number14Envelope":"Number14Envelope","Number9Envelope":"Number9Envelope","PersonalEnvelope":"PersonalEnvelope","Prc16K":"Prc16K","Prc16KRotated":"Prc16KRotated","Prc32K":"Prc32K","Prc32KBig":"Prc32KBig","Prc32KBigRotated":"Prc32KBigRotated","Prc32KRotated":"Prc32KRotated","PrcEnvelopeNumber1":"PrcEnvelopeNumber1","PrcEnvelopeNumber10":"PrcEnvelopeNumber10","PrcEnvelopeNumber10Rotated":"PrcEnvelopeNumber10Rotated","PrcEnvelopeNumber1Rotated":"PrcEnvelopeNumber1Rotated","PrcEnvelopeNumber2":"PrcEnvelopeNumber2","PrcEnvelopeNumber2Rotated":"PrcEnvelopeNumber2Rotated","PrcEnvelopeNumber3":"PrcEnvelopeNumber3","PrcEnvelopeNumber3Rotated":"PrcEnvelopeNumber3Rotated","PrcEnvelopeNumber4":"PrcEnvelopeNumber4","PrcEnvelopeNumber4Rotated":"PrcEnvelopeNumber4Rotated","PrcEnvelopeNumber5":"PrcEnvelopeNumber5","PrcEnvelopeNumber5Rotated":"PrcEnvelopeNumber5Rotated","PrcEnvelopeNumber6":"PrcEnvelopeNumber6","PrcEnvelopeNumber6Rotated":"PrcEnvelopeNumber6Rotated","PrcEnvelopeNumber7":"PrcEnvelopeNumber7","PrcEnvelopeNumber7Rotated":"PrcEnvelopeNumber7Rotated","PrcEnvelopeNumber8":"PrcEnvelopeNumber8","PrcEnvelopeNumber8Rotated":"PrcEnvelopeNumber8Rotated","PrcEnvelopeNumber9":"PrcEnvelopeNumber9","PrcEnvelopeNumber9Rotated":"PrcEnvelopeNumber9Rotated","Quarto":"Quarto","Standard10x11":"Standard10x11","Standard10x14":"Standard10x14","Standard11x17":"Standard11x17","Standard12x11":"Standard12x11","Standard15x11":"Standard15x11","Standard9x11":"Standard9x11","Statement":"Statement","Tabloid":"Tabloid","TabloidExtra":"TabloidExtra","USStandardFanfold":"USStandardFanfold"},"paper_size":{"A3":"A3","A4":"A4","A5":"A5","A6":"A6","Custom":"ユーザー定義サイズ","Executive":"Executive","ISOB5":"B5 (ISO)","JISB4":"B4 (JIS)","JISB5":"B5 (JIS)","JISB6":"B6 (JIS)","Legal":"Legal","Letter":"Letter","Tabloid":"Tabloid"},"paper_source":{"AutomaticFeed":"Automatic Feed","Cassette":"Cassette","Manual":"Manual"},"paper_source_kind":{"AutomaticFeed":"AutomaticFeed","Cassette":"Cassette","Custom":"Custom","Envelope":"Envelope","FormSource":"FormSource","LargeCapacity":"LargeCapacity","LargeFormat":"LargeFormat","Lower":"Lower","Manual":"Manual","ManualFeed":"ManualFeed","Middle":"Middle","SmallFormat":"SmallFormat","TractorFeed":"TractorFeed","Upper":"Upper"},"parameter_type":{"Boolean":"Boolean","Date":"Date","String":"String"},"picture_alignment":{"BottomLeft":"BottomLeft","BottomRight":"BottomRight","Center":"Center","TopLeft":"TopLeft","TopRight":"TopRight"},"repeat_style":{"All":"All","None":"None","OnColumn":"OnColumn","OnPage":"OnPage","OnPageIncludeNoDetail":"OnPageIncludeNoDetail"},"richrext_rendering_type":{"Metafile":"Metafile","PNG":"PNG","RTF":"RTF","TransparentPNG":"TransparentPNG"},"script_language":{"C#":"C#","VBNET":"VB.NET"},"shape_style":{"Ellipse":"Ellipse","Rectangle":"Rectangle","RoundRect":"RoundRect"},"size_mode":{"Clip":"Clip","Stretch":"Stretch","Zoom":"Zoom"},"string_alignment":{"Center":"Center","Far":"Far","Near":"Near"},"string_alignment_inputfield":{"Center":"中央","Left":"左","Right":"右"},"summary_func":{"Avg":"Avg","Count":"Count","DAvg":"DAvg","DCount":"DCount","DStdDev":"DStdDev","DStdDevP":"DStdDevP","DSum":"DSum","DVar":"DVar","DVarP":"DVarP","Max":"Max","Min":"Min","StdDev":"StdDev","StdDevP":"StdDevP","Sum":"Sum","Var":"Var","VarP":"VarP"},"summary_running":{"All":"All","Group":"Group","None":"None"},"summary_type":{"GrandTotal":"GrandTotal","None":"None","PageCount":"PageCount","PageTotal":"PageTotal","SubTotal":"SubTotal"},"text_align":{"Center":"Center","Justify":"Justify","Left":"Left","Right":"Right"},"text_justify":{"Auto":"Auto","Distribute":"Distribute","Distribute_all_lines":"DistributeAllLines"},"vertical_align":{"Bottom":"Bottom","Middle":"Middle","Top":"Top"},"white_space":{"NoWrap":"NoWrap","Normal":"Normal","Pre":"Pre"},"word_wrap":{"CharWrap":"CharWrap","NoWrap":"NoWrap","WordWrap":"WordWrap"}}},{"lng":"ja","ns":"enums","resources":{"action":{"ApplyParameters":"パラメータの適用","BookmarkLink":"ブックマークに移動","Drillthrough":"レポートに移動","Hyperlink":"URLに移動","None":"なし"},"action_apply_value_cmd":{"Reset":"リセット","Set":"セット","Toggle":"トグル"},"auto_merge_mode":{"Always":"すべて","Never":"なし","Restricted":"制限"},"auto_width":{"None":"None","Proportional":"Proportional"},"axis_location":{"Left":"Left","Right":"Right"},"axis_mode":{"Cartesian":"Cartesian","Polygonal":"Polygonal","Radial":"Radial"},"barcode_caption_location":{"Above":"Above","Below":"Below","None":"None"},"barcode_ecc000_140_symbol_size":{"Auto":"Auto","Square11":"Square11","Square13":"Square13","Square15":"Square15","Square17":"Square17","Square19":"Square19","Square21":"Square21","Square23":"Square23","Square25":"Square25","Square27":"Square27","Square29":"Square29","Square31":"Square31","Square33":"Square33","Square35":"Square35","Square37":"Square37","Square39":"Square39","Square41":"Square41","Square43":"Square43","Square45":"Square45","Square47":"Square47","Square49":"Square49","Square9":"Square9"},"barcode_ecc200_encoding_mode":{"ASCII":"ASCII","Auto":"Auto","Base256":"Base256","C40":"C40","EDIFACT":"EDIFACT","Text":"Text","X12":"X12"},"barcode_ecc200_symbol_size":{"Rectangular12x26":"Rectangular12x26","Rectangular12x36":"Rectangular12x36","Rectangular16x36":"Rectangular16x36","Rectangular16x48":"Rectangular16x48","Rectangular8x18":"Rectangular8x18","Rectangular8x32":"Rectangular8x32","RectangularAuto":"RectangularAuto","Square10":"Square10","Square104":"Square104","Square12":"Square12","Square120":"Square120","Square132":"Square132","Square14":"Square14","Square144":"Square144","Square16":"Square16","Square18":"Square18","Square20":"Square20","Square22":"Square22","Square24":"Square24","Square26":"Square26","Square32":"Square32","Square36":"Square36","Square40":"Square40","Square44":"Square44","Square48":"Square48","Square52":"Square52","Square64":"Square64","Square72":"Square72","Square80":"Square80","Square88":"Square88","Square96":"Square96","SquareAuto":"SquareAuto"},"barcode_ecc_mode":{"ECC000":"ECC000","ECC050":"ECC050","ECC080":"ECC080","ECC100":"ECC100","ECC140":"ECC140","ECC200":"ECC200"},"barcode_encoding":{"37":"IBM EBCDIC (US-Canada) 37","437":"OEM United States 437","500":"IBM EBCDIC (International) 500","708":"Arabic (ASMO 708) 708","720":"Arabic (DOS) 720","737":"Greek (DOS) 737","775":"Baltic (DOS) 775","850":"Western European (DOS) 850","852":"Central European (DOS) 852","855":"OEM Cyrillic 855","857":"Turkish (DOS) 857","858":"OEM Multilingual Latin I 858","860":"Portuguese (DOS) 860","861":"Icelandic (DOS) 861","862":"Hebrew (DOS) 862","863":"French Canadian (DOS) 863","864":"Arabic (864) 864","865":"Nordic (DOS) 865","866":"Cyrillic (DOS) 866","869":"Greek, Modern (DOS) 869","870":"IBM EBCDIC (Multilingual Latin-2) 870","874":"Thai (Windows) 874","875":"IBM EBCDIC (Greek Modern) 875","932":"Japanese (Shift-JIS) 932","936":"Chinese Simplified (GB2312) 936","949":"Korean 949","950":"Chinese Traditional (Big5) 950","1026":"IBM EBCDIC (Turkish Latin-5) 1026","1047":"IBM Latin-1 1047","1140":"IBM EBCDIC (US-Canada-Euro) 1140","1141":"IBM EBCDIC (Germany-Euro) 1141","1142":"IBM EBCDIC (Denmark-Norway-Euro) 1142","1143":"IBM EBCDIC (Finland-Sweden-Euro) 1143","1144":"IBM EBCDIC (Italy-Euro) 1144","1145":"IBM EBCDIC (Spain-Euro) 1145","1146":"IBM EBCDIC (UK-Euro) 1146","1147":"IBM EBCDIC (France-Euro) 1147","1148":"IBM EBCDIC (International-Euro) 1148","1149":"IBM EBCDIC (Icelandic-Euro) 1149","1200":"Unicode 1200","1201":"Unicode (Big-Endian) 1201","1250":"Central European (Windows) 1250","1251":"Cyrillic (Windows) 1251","1252":"Western European (Windows) 1252","1253":"Greek (Windows) 1253","1254":"Turkish (Windows) 1254","1255":"Hebrew (Windows) 1255","1256":"Arabic (Windows) 1256","1257":"Baltic (Windows) 1257","1258":"Vietnamese (Windows) 1258","1361":"Korean (Johab) 1361","10000":"Western European (Mac) 10000","10001":"Japanese (Mac) 10001","10002":"Chinese Traditional (Mac) 10002","10003":"Korean (Mac) 10003","10004":"Arabic (Mac) 10004","10005":"Hebrew (Mac) 10005","10006":"Greek (Mac) 10006","10007":"Cyrillic (Mac) 10007","10008":"Chinese Simplified (Mac) 10008","10010":"Romanian (Mac) 10010","10017":"Ukrainian (Mac) 10017","10021":"Thai (Mac) 10021","10029":"Central European (Mac) 10029","10079":"Icelandic (Mac) 10079","10081":"Turkish (Mac) 10081","10082":"Croatian (Mac) 10082","12000":"Unicode (UTF-32) 12000","12001":"Unicode (UTF-32 Big-Endian) 12001","20000":"Chinese Traditional (CNS) 20000","20001":"TCA Taiwan 20001","20002":"Chinese Traditional (Eten) 20002","20003":"IBM5550 Taiwan 20003","20004":"TeleText Taiwan 20004","20005":"Wang Taiwan 20005","20105":"Western European (IA5) 20105","20106":"German (IA5) 20106","20107":"Swedish (IA5) 20107","20108":"Norwegian (IA5) 20108","20127":"US-ASCII 20127","20261":"T.61 20261","20269":"ISO-6937 20269","20273":"IBM EBCDIC (Germany) 20273","20277":"IBM EBCDIC (Denmark-Norway) 20277","20278":"IBM EBCDIC (Finland-Sweden) 20278","20280":"IBM EBCDIC (Italy) 20280","20284":"IBM EBCDIC (Spain) 20284","20285":"IBM EBCDIC (UK) 20285","20290":"IBM EBCDIC (Japanese katakana) 20290","20297":"IBM EBCDIC (France) 20297","20420":"IBM EBCDIC (Arabic) 20420","20423":"IBM EBCDIC (Greek) 20423","20424":"IBM EBCDIC (Hebrew) 20424","20833":"IBM EBCDIC (Korean Extended) 20833","20838":"IBM EBCDIC (Thai) 20838","20866":"Cyrillic (KOI8-R) 20866","20871":"IBM EBCDIC (Icelandic) 20871","20880":"IBM EBCDIC (Cyrillic Russian) 20880","20905":"IBM EBCDIC (Turkish) 20905","20924":"IBM Latin-1 20924","20932":"Japanese (JIS 0208-1990 and 0212-1990) 20932","20936":"Chinese Simplified (GB2312-80) 20936","20949":"Korean Wansung 20949","21025":"IBM EBCDIC (Cyrillic Serbian-Bulgarian) 21025","21866":"Cyrillic (KOI8-U) 21866","28591":"Western European (ISO) 28591","28592":"Central European (ISO) 28592","28593":"Latin 3 (ISO) 28593","28594":"Baltic (ISO) 28594","28595":"Cyrillic (ISO) 28595","28596":"Arabic (ISO) 28596","28597":"Greek (ISO) 28597","28598":"Hebrew (ISO-Visual) 28598","28599":"Turkish (ISO) 28599","28603":"Estonian (ISO) 28603","28605":"Latin 9 (ISO) 28605","29001":"Europa 29001","38598":"Hebrew (ISO-Logical) 38598","50220":"Japanese (JIS) 50220","50221":"Japanese (JIS-Allow 1 byte Kana) 50221","50222":"Japanese (JIS-Allow 1 byte Kana - SO/SI) 50222","50225":"Korean (ISO) 50225","50227":"Chinese Simplified (ISO-2022) 50227","51932":"Japanese (EUC) 51932","51936":"Chinese Simplified (EUC) 51936","51949":"Korean (EUC) 51949","52936":"Chinese Simplified (HZ) 52936","54936":"Chinese Simplified (GB18030) 54936","57002":"ISCII Devanagari 57002","57003":"ISCII Bengali 57003","57004":"ISCII Tamil 57004","57005":"ISCII Telugu 57005","57006":"ISCII Assamese 57006","57007":"ISCII Oriya 57007","57008":"ISCII Kannada 57008","57009":"ISCII Malayalam 57009","57010":"ISCII Gujarati 57010","57011":"ISCII Punjabi 57011","65000":"Unicode (UTF-7) 65000","65001":"Unicode (UTF-8) 65001"},"barcode_gs1_composite_type":{"CCA":"CCA","None":"None"},"barcode_maxicode_mode":{"Mode2":"Mode2","Mode3":"Mode3","Mode4":"Mode4","Mode5":"Mode5","Mode6":"Mode6"},"barcode_micro_pdf417_compaction_mode":{"Auto":"Auto","ByteCompactionMode":"ByteCompactionMode","NumericCompactionMode":"NumericCompactionMode","TextCompactionMode":"TextCompactionMode"},"barcode_micro_pdf417_version":{"ColumnPriorAuto":"ColumnPriorAuto","RowPriorAuto":"RowPriorAuto","Version1X11":"Version1X11","Version1X14":"Version1X14","Version1X17":"Version1X17","Version1X20":"Version1X20","Version1X24":"Version1X24","Version1X28":"Version1X28","Version2X11":"Version2X11","Version2X14":"Version2X14","Version2X17":"Version2X17","Version2X20":"Version2X20","Version2X23":"Version2X23","Version2X26":"Version2X26","Version2X8":"Version2X8","Version3X10":"Version3X10","Version3X12":"Version3X12","Version3X15":"Version3X15","Version3X20":"Version3X20","Version3X26":"Version3X26","Version3X32":"Version3X32","Version3X38":"Version3X38","Version3X44":"Version3X44","Version3X6":"Version3X6","Version3X8":"Version3X8","Version4X10":"Version4X10","Version4X12":"Version4X12","Version4X15":"Version4X15","Version4X20":"Version4X20","Version4X26":"Version4X26","Version4X32":"Version4X32","Version4X38":"Version4X38","Version4X4":"Version4X4","Version4X44":"Version4X44","Version4X6":"Version4X6","Version4X8":"Version4X8"},"barcode_micro_qrcode_error_level":{"L":"L","M":"M","Q":"Q"},"barcode_micro_qrcode_mask":{"Auto":"Auto","Mask00":"Mask00","Mask01":"Mask01","Mask10":"Mask10","Mask11":"Mask11"},"barcode_micro_qrcode_version":{"Auto":"Auto","M1":"M1","M2":"M2","M3":"M3","M4":"M4"},"barcode_pdf417_error_correction_level":{"Level0":"Level0","Level1":"Level1","Level2":"Level2","Level3":"Level3","Level4":"Level4","Level5":"Level5","Level6":"Level6","Level7":"Level7","Level8":"Level8"},"barcode_pdf417_type":{"Normal":"Normal","Simple":"Simple"},"barcode_qrcode_error_level":{"H":"H","L":"L","M":"M","Q":"Q"},"barcode_qrcode_mask":{"Auto":"Auto","Mask000":"Mask000","Mask001":"Mask001","Mask010":"Mask010","Mask011":"Mask011","Mask100":"Mask100","Mask101":"Mask101","Mask110":"Mask110","Mask111":"Mask111"},"barcode_qrcode_model":{"Model1":"Model1","Model2":"Model2"},"barcode_rotation":{"None":"None","Rotate180Degrees":"Rotate180Degrees","Rotate270Degrees":"Rotate270Degrees","Rotate90Degrees":"Rotate90Degrees"},"barcode_symbology":{"Ansi39":"ANSI 3 of 9","Ansi39x":"ANSI Extended 3 of 9","Aztec":"Aztec","BC412":"BC412","Codabar":"Codabar","Code25intlv":"Interleaved 2 of 5","Code39":"Code 39","Code39x":"Extended Code 39","Code49":"Code 49","Code93x":"Extended Code 93","Code_11":"Code 11","Code_128_A":"Code 128 A","Code_128_B":"Code 128 B","Code_128_C":"Code 128 C","Code_128auto":"Code 128 Auto","Code_2_of_5":"Code 2 of 5","Code_93":"Code 93","DataMatrix":"Data Matrix","EAN128FNC1":"EAN-128FNC1","EAN_13":"EAN-13","EAN_8":"EAN-8","GS1DataMatrix":"GS1 Data Matrix","GS1QRCode":"GS1 QR Code","HIBCCode128":"HIBC Code 128","HIBCCode39":"HIBC Code 39","IATA_2_of_5":"IATA 2 of 5","ISBN":"ISBN","ISMN":"ISMN","ISSN":"ISSN","ITF14":"ITF-14","IntelligentMail":"Intelligent Mail","IntelligentMailPackage":"Intelligent Mail Package","JapanesePostal":"Japanese Postal","MSI":"MSI Code","Matrix_2_of_5":"Matrix 2 of 5","MaxiCode":"MaxiCode","MicroPDF417":"Micro PDF417","MicroQRCode":"Micro QR Code","None":"None","PZN":"PZN (Pharmaceutical Central Number)","Pdf417":"PDF417","Pharmacode":"Pharmacode","Plessey":"Plessey","PostNet":"PostNet","QRCode":"QR Code","RM4SCC":"RM4SCC (Royal Mail)","RSS14":"RSS-14","RSS14Stacked":"RSS-14 Stacked","RSS14StackedOmnidirectional":"RSS-14 Stacked Omnidirectional","RSS14Truncated":"RSS-14 Truncated","RSSExpanded":"RSS Expanded","RSSExpandedStacked":"RSS Expanded Stacked","RSSLimited":"RSS Limited","SSCC_18":"SSCC-18","Telepen":"Telepen","UCCEAN128":"UCC/EAN–128","UPC_A":"UPC-A","UPC_E0":"UPC-E0","UPC_E1":"UPC-E1"},"border_style":{"DashDot":"DashDot","DashDotDot":"DashDotDot","Dashed":"Dashed","Dotted":"Dotted","Double":"Double","Groove":"Groove","Inset":"Inset","None":"None","Outset":"Outset","Ridge":"Ridge","Solid":"Solid","WindowInset":"WindowInset"},"bullet_tick_marks":{"Inside":"Inside","None":"None","Outside":"Outside"},"calendar":{"Gregorian":"Gregorian","GregorianArabic":"Gregorian Arabic","GregorianMiddleEastFrench":"Gregorian Middle East French","GregorianTransliteratedEnglish":"Gregorian Transliterated English","GregorianTransliteratedFrench":"Gregorian Transliterated French","GregorianUSEnglish":"Gregorian US English","Hebrew":"Hebrew","Hijri":"Hijri","Japanese":"Japanese","Korean":"Korean","Taiwan":"Taiwan","ThaiBuddhist":"Thai Buddhist"},"check_alignment":{"BottomCenter":"BottomCenter","BottomLeft":"BottomLeft","BottomRight":"BottomRight","MiddleCenter":"MiddleCenter","MiddleLeft":"MiddleLeft","MiddleRight":"MiddleRight","TopCenter":"TopCenter","TopLeft":"TopLeft","TopRight":"TopRight"},"check_style":{"Check":"Check","Circle":"Circle","Cross":"Cross","Diamond":"Diamond","Square":"Square","Star":"Star"},"collate_by":{"Simple":"Simple","Value":"Value","ValueIndex":"ValueIndex"},"collation":{"Albanian":"Albanian","Arabic":"Arabic","Chinese_PRC":"Chinese PRC","Chinese_PRC_Stroke":"Chinese PRC Stroke","Chinese_Taiwan_Bopomofo":"Chinese Taiwan Bopomofo","Chinese_Taiwan_Stroke":"Chinese Taiwan Stroke","Croatian":"Croatian","Cyrillic_General":"Cyrillic General","Czech":"Czech","Danish_Norwegian":"Danish Norwegian","Default":"Default","Estonian":"Estonian","FYRO_Macedonian":"Macedonian FYRO","Finnish_Swedish":"Finnish Swedish","French":"French","Georgian_Modern_Sort":"Georgian Modern Sort","German_PhoneBook":"German Phone Book","Greek":"Greek","Hebrew":"Hebrew","Hindi":"Hindi","Hungarian":"Hungarian","Hungarian_Technical":"Hungarian Technical","Icelandic":"Icelandic","Japanese":"Japanese","Japanese_Unicode":"Japanese Unicode","Korean_Wansung":"Korean Wansung","Korean_Wansung_Unicode":"Korean Wansung Unicode","Latin1_General":"Latin-1 General","Latvian":"Latvian","Lithuanian":"Lithuanian","Lithuanian_Classic":"Lithuanian Classic","Polish":"Polish","Romanian":"Romanian","Slovak":"Slovak","Slovenian":"Slovenian","Spanish_Mexican_Trad":"Spanish Mexican Traditional","Spanish_Modern":"Spanish Modern","Thai":"Thai","Turkish":"Turkish","Ukrainian":"Ukrainian","Vietnamese":"Vietnamese"},"data_element_output":{"Auto":"自動","ContentsOnly":"コンテンツのみ","NoOutput":"出力しない","Output":"出力する"},"data_element_output_aon":{"Auto":"自動","NoOutput":"出力しない","Output":"出力する"},"data_element_output_on":{"NoOutput":"出力しない","Output":"出力する"},"data_element_style":{"AttributeNormal":"属性","Auto":"自動","ElementNormal":"要素"},"data_label_position":{"Auto":"Auto","Bottom":"Bottom","BottomLeft":"BottomLeft","BottomRight":"BottomRight","Center":"Center","Left":"Left","Right":"Right","Top":"Top","TopLeft":"TopLeft","TopRight":"TopRight"},"data_visualizer_color_type":{"colorScale":"Color Scale"},"data_visualizer_gradient_type":{"DiagonalDown":"DiagonalDown","DiagonalUp":"DiagonalUp","FromCenter":"FromCenter","FromCorner":"FromCorner","Horizontal":"Horizontal","Vertical":"Vertical"},"data_visualizer_hatch_style":{"BackwardDiagonal":"BackwardDiagonal","Cross":"Cross","DarkDownwardDiagonal":"DarkDownwardDiagonal","DarkHorizontal":"DarkHorizontal","DarkUpwardDiagonal":"DarkUpwardDiagonal","DarkVertical":"DarkVertical","DashedDownwardDiagonal":"DashedDownwardDiagonal","DashedHorizontal":"DashedHorizontal","DashedUpwardDiagonal":"DashedUpwardDiagonal","DashedVertical":"DashedVertical","DiagonalBrick":"DiagonalBrick","DiagonalCross":"DiagonalCross","Divot":"Divot","DottedDiamond":"DottedDiamond","DottedGrid":"DottedGrid","ForwardDiagonal":"ForwardDiagonal","Horizontal":"Horizontal","HorizontalBrick":"HorizontalBrick","LargeCheckerBoard":"LargeCheckerBoard","LargeConfetti":"LargeConfetti","LargeGrid":"LargeGrid","LightDownwardDiagonal":"LightDownwardDiagonal","LightHorizontal":"LightHorizontal","LightUpwardDiagonal":"LightUpwardDiagonal","LightVertical":"LightVertical","NarrowHorizontal":"NarrowHorizontal","NarrowVertical":"NarrowVertical","OutlinedDiamond":"OutlinedDiamond","Percent05":"Percent05","Percent10":"Percent10","Percent20":"Percent20","Percent25":"Percent25","Percent30":"Percent30","Percent40":"Percent40","Percent50":"Percent50","Percent60":"Percent60","Percent70":"Percent70","Percent75":"Percent75","Percent80":"Percent80","Percent90":"Percent90","Plaid":"Plaid","Shingle":"Shingle","SmallCheckerBoard":"SmallCheckerBoard","SmallConfetti":"SmallConfetti","SmallGrid":"SmallGrid","SolidDiamond":"SolidDiamond","Sphere":"Sphere","Trellis":"Trellis","Vertical":"Vertical","Wave":"Wave","Weave":"Weave","WideDownwardDiagonal":"WideDownwardDiagonal","WideUpwardDiagonal":"WideUpwardDiagonal","ZigZag":"ZigZag"},"data_visualizer_icon_set_type":{"3TrafficLights":"3TrafficLights","Arrows":"Arrows","Blank":"Blank","Checkbox":"Checkbox","Flags":"Flags","GrayArrows":"GrayArrows","Quarters":"Quarters","Ratings":"Ratings","RedToBlack":"RedToBlack","Signs":"Signs","Symbols1":"Symbols1","Symbols2":"Symbols2","TrafficLights":"TrafficLights"},"data_visualizer_icon_set_value":{"false":"False","true":"True"},"data_visualizer_image_type":{"dataBar":"Data Bar","gradient":"Gradient","hatch":"Hatch","iconSet":"Icon Set","rangeBar":"Range Bar"},"dataset_option":{"Auto":"Auto","false":"False","true":"True"},"dataset_query_command_type":{"StoredProcedure":"StoredProcedure","Text":"Text"},"direction":{"LTR":"左から右","RTL":"右から左"},"display_type":{"Galley":"ゲラ","Page":"ページ"},"documentmap_numbering_style":{"CircledNumber":"①, ②, ③, ④, ⑤","Decimal":"1, 2, 3, 4, 5","DecimalLeadingZero":"01, 02, 03, 04, 05","Georgian":"ა, ბ, გ, დ, ე","Katakana":"ア, イ, ウ, エ, オ","KatakanaBrackets":"(ア), (イ), (ウ), (エ), (オ)","KatakanaIroha":"イ, ロ, ハ, ニ, ホ","KatakanaIrohaBrackets":"イ), ロ), ハ), ニ), ホ)","KatakanaIrohaLower":"ｨ, ﾛ, ﾊ, ﾆ, ﾎ","KatakanaLower":"ｱ, ｲ, ｳ, ｴ, ｵ","LowerArmenian":"ա, բ, գ, դ, ե","LowerGreek":"α, β, γ, δ, ε","LowerLatin":"a, b, c, d, e","LowerRoman":"i, ii, iii, iv, v","None":"なし","UpperArmenian":"Ա, Բ, Գ, Դ, Ե","UpperGreek":"Α, Β, Γ, Δ, Ε","UpperLatin":"A, B, C, D, E","UpperRoman":"I, II, III, IV, V"},"documentmap_source":{"All":"All","Headings":"Headings","Labels":"Labels","None":"None"},"dvchart_aggregate_type":{"Average":"Average","Count":"Count","CountDistinct":"CountDistinct","CountOfAll":"CountOfAll","List":"List","Max":"Max","Min":"Min","None":"None","PopulationStandardDeviation":"PopulationStandardDeviation","PopulationVariance":"PopulationVariance","Range":"Range","StandardDeviation":"StandardDeviation","Sum":"Sum","Variance":"Variance"},"dvchart_axis_date_mode":{"Day":"Day","Default":"Default","Hour":"Hour","Millisecond":"Millisecond","Minute":"Minute","Month":"Month","Second":"Second","Week":"Week","Year":"Year"},"dvchart_axis_overlapping_labels":{"Auto":"Auto","Show":"Show"},"dvchart_axis_position":{"Far":"Far","Near":"Near","None":"None"},"dvchart_axis_type":{"X":"X","Y":"Y"},"dvchart_clipping_mode":{"Clip":"Clip","Fit":"Fit","None":"None"},"dvchart_encoding_field_type":{"Complex":"Complex","Simple":"Simple"},"dvchart_encoding_sort":{"Ascending":"Ascending","Descending":"Descending","None":"None"},"dvchart_group_type":{"Cluster":"Cluster","None":"None","Stack":"Stack"},"dvchart_group_type_wizard":{"Cluster":"Cluster","PercentStacked":"Percentage Stack","Stacked":"Stack"},"dvchart_halign":{"Center":"Center","Left":"Left","Right":"Right"},"dvchart_legend_position":{"Bottom":"Bottom","Left":"Left","Right":"Right","Top":"Top"},"dvchart_line_aspect":{"Default":"Default","Spline":"Spline","StepCenter":"StepCenter","StepLeft":"StepLeft","StepRight":"StepRight"},"dvchart_line_position":{"Auto":"Auto","Center":"Center"},"dvchart_orientation":{"Horizontal":"Horizontal","Vertical":"Vertical"},"dvchart_overlapping_labels":{"Auto":"Auto","Hide":"Hide","Show":"Show"},"dvchart_palette":{"Aspect":"Aspect","Blue":"Blue","Blue2":"Blue2","BlueGreen":"BlueGreen","BlueWarm":"BlueWarm","Cerulan":"Cerulan","Cocoa":"Cocoa","Coral":"Coral","Custom":"カスタム","Cyborg":"Cyborg","Dark":"Dark","Darkly":"Darkly","Flatly":"Flatly","Grayscale":"Grayscale","Green":"Green","GreenYellow":"GreenYellow","HighContrast":"HighContrast","Light":"Light","Marquee":"Marquee","Median":"Median","Midnight":"Midnight","Modern":"Modern","Office":"Office","Office2010":"Office2010","Orange":"Orange","OrangeRed":"OrangeRed","Organic":"Organic","Paper":"Paper","Red":"Red","RedOrange":"RedOrange","RedViolet":"RedViolet","Slate":"Slate","Slipstream":"Slipstream","Standard":"Standard","Superhero":"Superhero","Violet":"Violet","Violet2":"Violet2","Yellow":"Yellow","YellowOrange":"YellowOrange","Zen":"Zen"},"dvchart_plot_overlay_aggregate_type":{"Average":"Average","Count":"Count","Max":"Max","Median":"Median","Min":"Min","Percentile":"Percentile","Sum":"Sum"},"dvchart_plot_overlay_axis":{"X":"X軸","Y":"Y軸"},"dvchart_plot_overlay_detail_level":{"Group":"グループ","Total":"合計"},"dvchart_plot_overlay_display":{"Back":"後","Front":"前"},"dvchart_plot_overlay_type":{"CumulativeMovingAverageTrendline":"CumulativeMovingAverageTrendline","ExponentialMovingAverageTrendline":"ExponentialMovingAverageTrendline","ExponentialTrendline":"ExponentialTrendline","FourierTrendline":"FourierTrendline","LinearTrendline":"LinearTrendline","LogarithmicTrendline":"LogarithmicTrendline","MovingAnnualTotalTrendline":"MovingAnnualTotalTrendline","MovingAverageTrendline":"MovingAverageTrendline","PolynomialTrendline":"PolynomialTrendline","PowerTrendline":"PowerTrendline","ReferenceBand":"ReferenceBand","ReferenceLine":"ReferenceLine","WeightedMovingAverageTrendline":"WeightedMovingAverageTrendline"},"dvchart_plot_rule_properties":{"BackgroundColor":"背景色","LabelTemplate":"ラベルのテンプレート","LineColor":"線の色","LineStyle":"線のスタイル","LineWidth":"線の幅","SymbolBackgroundColor":"シンボルの背景色","SymbolLineColor":"シンボルの線の色","SymbolLineStyle":"シンボルの線のスタイル","SymbolLineWidth":"シンボルの線の幅","TooltipTemplate":"ツールチップのテンプレート"},"dvchart_plot_type":{"Area":"Area","Bar":"Bar","Candlestick":"Candlestick","HighLowOpenClose":"HighLowOpenClose","Line":"Line","Scatter":"Scatter"},"dvchart_scale":{"Linear":"Linear","Logarithmic":"Logarithmic","Ordinal":"Ordinal","Percentage":"Percentage"},"dvchart_show_nulls":{"Connected":"Connected","Gaps":"Gaps","Zeros":"Zeros"},"dvchart_symbol_shape":{"Auto":"Auto","Box":"Box","Dash":"Dash","Diamond":"Diamond","Dot":"Dot","Plus":"Plus","Triangle":"Triangle","X":"X"},"dvchart_target":{"Label":"Label","ToolTip":"ToolTip"},"dvchart_template":{"PercentageCategory_p0":"Percentage In Category","PercentageDetail_p0":"Percentage In Details","categoryField":{"name":"Category Field Name","value":"Category Field Value"},"colorField":{"name":"Color Field Name","value":"Color Field Value"},"detailFields":{"name":"Details Field Name","value":"Details Field Value"},"shapeField":{"name":"Shape Field Name","value":"Shape Field Value"},"sizeField":{"name":"Size Field Name","value":"Size Field Value"},"valueField":{"name":"Value Field Name","value":"Value Field Value"}},"dvchart_text_position":{"Auto":"Auto","Center":"Center","Inside":"Inside","Outside":"Outside"},"dvchart_tick_mark":{"Cross":"Cross","Inside":"Inside","None":"None","Outside":"Outside"},"dvchart_valign":{"Bottom":"Bottom","Middle":"Middle","Top":"Top"},"end_cap":{"Arrow":"Arrow","None":"None"},"font_size":{"10pt":"10pt","11pt":"11pt","12pt":"12pt","14pt":"14pt","16pt":"16pt","18pt":"18pt","20pt":"20pt","22pt":"22pt","24pt":"24pt","26pt":"26pt","28pt":"28pt","36pt":"36pt","48pt":"48pt","72pt":"72pt","8pt":"8pt","9pt":"9pt"},"font_style":{"Italic":"斜体","Normal":"標準"},"font_weight":{"Bold":"Bold","Bolder":"Bolder","ExtraBold":"ExtraBold","ExtraLight":"ExtraLight","Heavy":"Heavy","Light":"Light","Lighter":"Lighter","Medium":"Medium","Normal":"Normal","SemiBold":"SemiBold","Thin":"Thin"},"gradient_type":{"Center":"Center","DiagonalLeft":"DiagonalLeft","DiagonalRight":"DiagonalRight","HorizontalCenter":"HorizontalCenter","LeftRight":"LeftRight","None":"None","TopBottom":"TopBottom","VerticalCenter":"VerticalCenter"},"grid_mode":{"Dots":"ドット","Lines":"ライン"},"grow_direction":{"Column":"Column","ColumnReverse":"ColumnReverse","Row":"Row","RowReverse":"RowReverse"},"horizontal_align":{"Center":"中央","Left":"左","Right":"右"},"image_repeat":{"NoRepeat":"なし","Repeat":"縦横","RepeatX":"横","RepeatY":"縦"},"image_sizing":{"AutoSize":"自動","Clip":"Clip","Fit":"Fit","FitProportional":"FitProportional"},"image_source":{"Database":"データベース","Embedded":"埋め込み","External":"共有"},"input_type":{"CheckBox":"チェックボックス","Text":"テキスト"},"keep_with_group":{"After":"After","Before":"Before","None":"None"},"label_font_style":{"Bold":"Bold","Italic":"Italic","Regular":"Regular","Strikeout":"Strikeout","Underline":"Underline"},"labels_text_orientation":{"Angled":"Angled","Auto":"Auto","Horizontal":"Horizontal","Rotated270":"Rotated270","Rotated90":"Rotated90","Stacked":"Stacked"},"language":{"Default":"デフォルト","af-ZA":"Afrikaans - South Africa","am-ET":"Amharic - Ethiopia","ar-AE":"Arabic - United Arab Emirates","ar-BH":"Arabic - Bahrain","ar-DZ":"Arabic - Algeria","ar-EG":"Arabic - Egypt","ar-IQ":"Arabic - Iraq","ar-JO":"Arabic - Jordan","ar-KW":"Arabic - Kuwait","ar-LB":"Arabic - Lebanon","ar-LY":"Arabic - Libya","ar-MA":"Arabic - Morocco","ar-OM":"Arabic - Oman","ar-QA":"Arabic - Qatar","ar-SA":"Arabic - Saudi Arabia","ar-SY":"Arabic - Syria","ar-TN":"Arabic - Tunisia","ar-YE":"Arabic - Yemen","arn-CL":"Mapudungun - Chile","as-IN":"Assamese - India","az-AZ-Cyrl":"Azeri (Cyrillic) - Azerbaijan","az-AZ-Latn":"Azeri (Latin) - Azerbaijan","ba-RU":"Bashkir - Russia","be-BY":"Belarusian - Belarus","bg-BG":"Bulgarian - Bulgaria","bn-BD":"Bengali - Bangladesh","bn-IN":"Bengali - India","bo-CN":"Tibetan - PRC","br-FR":"Breton - France","bs-Cyrl-BA":"Bosnian (Cyrillic) - Bosnia and Herzegovina","bs-Latn-BA":"Bosnian (Latin) - Bosnia and Herzegovina","ca-ES":"Catalan - Catalan","co-FR":"Corsican - France","cs-CZ":"Czech - Czech Republic","cy-GB":"Welsh - United Kingdom","da-DK":"Danish - Denmark","de-AT":"German - Austria","de-CH":"German - Switzerland","de-DE":"German - Germany","de-LI":"German - Liechtenstein","de-LU":"German - Luxembourg","div-MV":"Dhivehi - Maldives","el-GR":"Greek - Greece","en-AU":"English - Australia","en-BZ":"English - Belize","en-CA":"English - Canada","en-CB":"English - Caribbean","en-GB":"English - United Kingdom","en-IE":"English - Ireland","en-IN":"English - India","en-JM":"English - Jamaica","en-MY":"English - Malaysia","en-NZ":"English - New Zealand","en-PH":"English - Philippines","en-SG":"English - Singapore","en-TT":"English - Trinidad and Tobago","en-US":"English - United States","en-ZA":"English - South Africa","en-ZW":"English - Zimbabwe","es-AR":"Spanish - Argentina","es-BO":"Spanish - Bolivia","es-CL":"Spanish - Chile","es-CO":"Spanish - Colombia","es-CR":"Spanish - Costa Rica","es-DO":"Spanish - Dominican Republic","es-EC":"Spanish - Ecuador","es-ES":"Spanish - Spain","es-GT":"Spanish - Guatemala","es-HN":"Spanish - Honduras","es-MX":"Spanish - Mexico","es-NI":"Spanish - Nicaragua","es-PA":"Spanish - Panama","es-PE":"Spanish - Peru","es-PR":"Spanish - Puerto Rico","es-PY":"Spanish - Paraguay","es-SV":"Spanish - El Salvador","es-US":"Spanish - United States","es-UY":"Spanish - Uruguay","es-VE":"Spanish - Venezuela","et-EE":"Estonian - Estonia","eu-ES":"Basque - Basque","fa-IR":"Persian - Iran","fi-FI":"Finnish - Finland","fil-PH":"Filipino - Philippines","fo-FO":"Faroese - Faroe Islands","fr-BE":"French - Belgium","fr-CA":"French - Canada","fr-CH":"French - Switzerland","fr-FR":"French - France","fr-LU":"French - Luxembourg","fr-MC":"French - Monaco","fy-NL":"Frisian - Netherlands","ga-IE":"Irish - Ireland","gd-GB":"Scottish Gaelic - United Kingdom","gl-ES":"Galician - Galician","gsw-FR":"Alsatian - France","gu-IN":"Gujarati - India","ha-Latn-NG":"Hausa (Latin) - Nigeria","he-IL":"Hebrew - Israel","hi-IN":"Hindi - India","hr-BA":"Croatian (Latin) - Bosnia and Herzegovina","hr-HR":"Croatian - Croatia","hu-HU":"Hungarian - Hungary","hy-AM":"Armenian - Armenia","id-ID":"Indonesian - Indonesia","ig-NG":"Igbo - Nigeria","ii-CN":"Yi - PRC","is-IS":"Icelandic - Iceland","it-CH":"Italian - Switzerland","it-IT":"Italian - Italy","iu-Cans-CA":"Inuktitut (Syllabics) - Canada","iu-Latn-CA":"Inuktitut (Latin) - Canada","ja-JP":"Japanese - Japan","ka-GE":"Georgian - Georgia","kk-KZ":"Kazakh - Kazakhstan","kl-GL":"Greenlandic - Greenland","km-KH":"Khmer - Cambodia","kn-IN":"Kannada - India","ko-KR":"Korean - Korea","kok-IN":"Konkani - India","ky-KG":"Kyrgyz - Kyrgyzstan","lb-LU":"Luxembourgish - Luxembourg","lo-LA":"Lao - Lao P.D.R.","lt-LT":"Lithuanian - Lithuania","lv-LV":"Latvian - Latvia","mi-NZ":"Maori - New Zealand","mk-MK":"Macedonian - Macedonia","ml-IN":"Malayalam - India","mn-MN":"Mongolian (Cyrillic) - Mongolia","mn-Mong-CN":"Mongolian (Traditional Mongolian) - PRC","mn-Mong-MN":"Mongolian - Mongolia","moh-CA":"Mohawk - Mohawk","mr-IN":"Marathi - India","ms-BN":"Malay - Brunei","ms-MY":"Malay - Malaysia","mt-MT":"Maltese - Malta","nb-NO":"Norwegian (Bokmål) - Norway","ne-NP":"Nepali - Nepal","nl-BE":"Dutch - Belgium","nl-NL":"Dutch - The Netherlands","nn-NO":"Norwegian (Nynorsk) - Norway","nso-ZA":"Sesotho sa Leboa - South Africa","oc-FR":"Occitan - France","or-IN":"Oriya - India","pa-IN":"Punjabi - India","pl-PL":"Polish - Poland","prs-AF":"Dari - Afghanistan","ps-AF":"Pashto - Afghanistan","pt-BR":"Portuguese - Brazil","pt-PT":"Portuguese - Portugal","qut-GT":"K\'iche - Guatemala","quz-BO":"Quechua - Bolivia","quz-EC":"Quechua - Ecuador","quz-PE":"Quechua - Peru","rm-CH":"Romansh - Switzerland","ro-RO":"Romanian - Romania","ru-RU":"Russian - Russia","rw-RW":"Kinyarwanda - Rwanda","sa-IN":"Sanskrit - India","sah-RU":"Yakut - Russia","se-FI":"Sami, Northern - Finland","se-NO":"Sami, Northern - Norway","se-SE":"Sami, Northern - Sweden","si-LK":"Sinhala - Sri Lanka","sk-SK":"Slovak - Slovakia","sl-SI":"Slovenian - Slovenia","sma-NO":"Sami, Southern - Norway","sma-SE":"Sami, Southern - Sweden","smj-NO":"Sami, Lule - Norway","smj-SE":"Sami, Lule - Sweden","smn-FI":"Sami, Inari - Finland","sms-FI":"Sami, Skolt - Finland","sq-AL":"Albanian - Albania","sr-SP-Cyrl":"Serbian (Cyrillic) - Serbia","sr-SP-Latn":"Serbian (Latin) - Serbia","sv-FI":"Swedish - Finland","sv-SE":"Swedish - Sweden","sw-KE":"Swahili - Kenya","syr-SY":"Syriac - Syria","ta-IN":"Tamil - India","te-IN":"Telugu - India","tg-Cyrl-TJ":"Tajik (Cyrillic) - Tajikistan","th-TH":"Thai - Thailand","tk-TM":"Turkmen - Turkmenistan","tn-ZA":"Setswana - South Africa","tr-TR":"Turkish - Turkey","tt-RU":"Tatar - Russia","tzm-Latn-DZ":"Tamazight (Latin) - Algeria","ug-CN":"Uyghur - PRC","uk-UA":"Ukrainian - Ukraine","ur-PK":"Urdu - Pakistan","uz-UZ-Cyrl":"Uzbek (Cyrillic) - Uzbekistan","uz-UZ-Latn":"Uzbek (Latin) - Uzbekistan","vi-VN":"Vietnamese - Vietnam","wee-DE":"Lower Sorbian - Germany","wen-DE":"Upper Sorbian - Germany","wo-SN":"Wolof - Senegal","xh-ZA":"isiXhosa - South Africa","yo-NG":"Yoruba - Nigeria","zh-CN":"Chinese - People\'s Republic of China","zh-HK":"Chinese - Hong Kong SAR","zh-MO":"Chinese - Macau SAR","zh-SG":"Chinese - Singapore","zh-TW":"Chinese - Taiwan","zu-ZA":"isiZulu - South Africa"},"layout_direction":{"LTR":"Ltr","RTL":"Rtl"},"layout_order":{"NOrder":"縦方向","ZOrder":"横方向"},"legend_layout":{"Column":"Column","Row":"Row","Table":"Table"},"legend_position":{"BottomCenter":"BottomCenter","BottomLeft":"BottomLeft","BottomRight":"BottomRight","LeftBottom":"LeftBottom","LeftCenter":"LeftCenter","LeftTop":"LeftTop","RightBottom":"RightBottom","RightCenter":"RightCenter","RightTop":"RightTop","TopCenter":"TopCenter","TopLeft":"TopLeft","TopRight":"TopRight"},"line_control_style":{"DashDot":"DashDot","DashDotDot":"DashDotDot","Dashed":"Dashed","Dotted":"Dotted","Double":"Double","None":"None","Solid":"Solid"},"marker_type":{"Auto":"Auto","Circle":"Circle","Cross":"Cross","Diamond":"Diamond","None":"None","Square":"Square","Triangle":"Triangle"},"mime_type":{"image/bmp":"image/bmp","image/gif":"image/gif","image/jpeg":"image/jpeg","image/png":"image/png","image/x-emf":"image/x-emf","image/x-wmf":"image/x-wmf"},"new_page":{"Even":"偶数ページ","Next":"次ページ","Odd":"奇数ページ"},"numeral_variant":{"1":"1","2":"2","3":"3","4":"4","5":"5","6":"6","7":"7"},"operator":{"Between":"Between","BottomN":"BottomN","BottomPercent":"BottomPercent","Equal":"Equal","GreaterThan":"GreaterThan","GreaterThanOrEqual":"GreaterThanOrEqual","In":"In","LessThan":"LessThan","LessThanOrEqual":"LessThanOrEqual","Like":"Like","NotEqual":"NotEqual","TopN":"TopN","TopPercent":"TopPercent"},"order_by_condition":{"Label":"Label","None":"None","Value":"Value"},"orientation":{"Horizontal":"Horizontal","Vertical":"Vertical"},"overflow":{"Auto":"Auto","Clip":"Clip","Grow":"Grow","Scroll":"Scroll"},"page_break":{"End":"End","None":"None","Start":"Start","StartAndEnd":"StartAndEnd"},"page_break_with_between":{"Between":"Between","End":"End","None":"None","Start":"Start","StartAndEnd":"StartAndEnd"},"page_orientation":{"Landscape":"横","Portrait":"縦"},"plot_type":{"Auto":"Auto","Line":"Line","Point":"Point"},"projection_mode":{"Orthographic":"Orthographic","Perspective":"Perspective"},"pve_datetime_range_type":{"Current":"現在の","Last":"前の","LastToDate":"過去（今日まで）","Next":"次の","ToDate":"初来"},"pve_datetime_range_type_second":{"Last":"前の","Next":"次の"},"pve_datetime_range_type_time":{"Current":"現在の","Last":"前の","Next":"次の"},"pve_datetime_range_unit":{"Day":"日","Hour":"時","Minute":"分","Month":"月","Quarter":"四半期","Second":"秒","Week":"週","Year":"年"},"repeat_blank_rows":{"FillGroup":"グループ","FillPage":"ページ","None":"なし"},"reportparameter_datatype":{"Boolean":"Boolean","Date":"Date","DateTime":"DateTime","Float":"Float","Integer":"Integer","String":"String"},"reportparts_property_type":{"boolean":"Boolean","borderStyle":"BorderStyle","color":"Color","float":"Float","fontFamily":"FontFamily","fontSize":"FontSize","fontStyle":"FontStyle","fontWeight":"FontWeight","integer":"Integer","length":"Length","lineStyle":"LineStyle","lineWidth":"LineWidth","string":"String","textDecoration":"TextDecoration"},"reportparts_sizemode":{"Fixed":"Fixed","Resizable":"Resizable"},"rich_text_markup":{"HTML":"HTML"},"shading":{"None":"None","Real":"Real","Simple":"Simple"},"shape_styles":{"Ellipse":"円","Rectangle":"四角","RoundRect":"角丸"},"size_type":{"Default":"Default","FitToPage":"Fit To Page","FitToWidth":"Fit To Width"},"sparkline_type":{"Area":"Area","Columns":"Columns","Line":"Line","StackedBar":"StackedBar","Whiskers":"Whiskers"},"target_shape":{"Dot":"Dot","Line":"Line","Square":"Square"},"text_align":{"Center":"中央","General":"標準","Justify":"両端揃え","Left":"左","Right":"右"},"text_align_glcr":{"Center":"中央","General":"標準","Left":"左","Right":"右"},"text_decoration":{"DoubleUnderline":"二重下線","LineThrough":"取り消し線","None":"なし","Overline":"上線","Underline":"下線"},"text_justify":{"Auto":"自動","Distribute":"Distribute","DistributeAllLines":"DistributeAllLines"},"text_orientation":{"Auto":"Auto","Horizontal":"Horizontal","Rotated270":"Rotated270","Rotated90":"Rotated90","Stacked":"Stacked"},"tick_mark":{"Cross":"Cross","Inside":"Inside","None":"None","Outside":"Outside"},"title_position":{"Center":"Center","Far":"Far","Near":"Near"},"unicode_bidi":{"BidiOverride":"BidiOverride","Embed":"Embed","Normal":"Normal"},"upright_in_vertical_text":{"Digits":"数字","DigitsAndLatinLetters":"英数字","None":"なし"},"vertical_align":{"Bottom":"下","Middle":"中央","Top":"上"},"wrap_mode":{"CharWrap":"文字","NoWrap":"なし","WordWrap":"単語"},"writing_mode":{"lr-tb":"lr-tb","tb-rl":"tb-rl"}}},{"lng":"ja","ns":"error","resources":{"errorLabel":"エラー","expressionNode":{"argumentIsNotValid":"関数 \'{{functionName}}\' の引数 \'{{argument}}\' が有効ではありません。 {{details}}","argumentValueNotFitFunctionValueDataType":"関数 \'{{functionName}}\' の引数 \'{{argument}}\' は - {{expectedTypes}} - のいずれかの種類である必要がありますが、実際は {{actualType}} です。","argumentsShouldHaveSameDataType":"関数 \'{{functionName}}\' の引数 \'{{argument}}\' は同じデータの種類である必要があります。","typeMismatch":"値 \'{{value}}\' は {{expectedType}} に変換することができません"},"themes":{"textCannotLoad":"レポートテーマを読み込めません。","textNoThemes":"他に利用可能なテーマがないため、レポートにはテーマが設定されません。","textNotFound":"このレポートで使用されているテーマ \\"{{theme}}\\" が見つかりません。","textTotFoundOrInvalid":"このレポートで使用されているテーマ {{theme}} が見つからないか、無効なコンテンツが含まれています。","textUseDefault":"代わりに、デフォルトのテーマ \\"{{theme}}\\" が使用されます。"},"upgradeReportSemanticModel":{"entityIsAbsent":"● 最新のセマンティックモデルでは、データセット \'{{dataSetName}}\' のエンティティ \'{{entityName}}\' が存在しません。","fieldAtQueryFilterIsAbsent":"● 最新のセマンティックモデルでは、データセット \'{{dataSetName}}\' の \'{{fieldName}}\' フィールドのクエリフィルタが存在しません。","fieldInControlGroupIsAbsent":"● 最新のセマンティックモデルでは、{{controlName}} コントロールの {{groupName}} グループの \'{{propertyLabel}}\' プロパティに \'{{fieldName}}\' フィールドが存在しません。","fieldInControlIsAbsent":"● 最新のセマンティックモデルでは、{{controlName}} コントロールの \'{{propertyLabel}}\' プロパティに \'{{fieldName}}\' フィールドが存在しません。","fieldInReportIsAbsent":"● 最新のセマンティックモデルでは、レポートの \'{{propertyLabel}}\' プロパティに \'{{fieldName}}\' フィールドが存在しません。"}}},{"lng":"ja","ns":"expressionFields","resources":{"chart":{"Current":{"Category":{"description":"現在データのカテゴリフィールドの値を返します。","example":"=Chart!CurrentCategory=1; =Chart!CurrentCategory>10; =Chart!CurrentCategory=\\"Value1\\";","label":"CurrentCategory","syntax":"Chart!CurrentCategory <Comparison operator> <Value>"},"Data":{"description":"現在データのデータフィールドの値を返します。","example":"=Chart!CurrentData=1; =Chart!CurrentData>10; =Chart!CurrentData=\\"Value1\\";","label":"CurrentData","syntax":"Chart!CurrentData <Comparison operator> <Value>"},"DataClose":{"description":"財務チャートのターゲット要素の\\"Close\\"値を表します。","example":"=Chart!CurrentDataClose=1; =Chart!CurrentDataClose>10; =Chart!CurrentDataClose=\\"Value1\\"; ","label":"DataClose","syntax":"Chart!CurrentDataClose <Comparison operator> <Value>"},"DataHigh":{"description":"財務チャートのターゲット要素の\\"High\\"値を表します。","example":"=Chart!CurrentDataHigh=1; =Chart!CurrentDataHigh>10; =Chart!CurrentDataHigh=\\"Value1\\"; ","label":"DataHigh","syntax":"Chart!CurrentDataHigh <Comparison operator> <Value>"},"DataLow":{"description":"財務チャートのターゲット要素の\\"Low\\"値を表します。","example":"=Chart!CurrentDataLow=1; =Chart!CurrentDataLow>10; =Chart!CurrentDataLow=\\"Value1\\"; ","label":"DataLow","syntax":"Chart!CurrentDataLow <Comparison operator> <Value>"},"DataLower":{"description":"ガントチャートのターゲット要素の\\"Lower\\"値を表します。","example":"=Chart!CurrentDataLower=1; =Chart!CurrentDataLower>10; =Chart!CurrentDataLower=\\"Value1\\"; ","label":"DataLower","syntax":"Chart!CurrentDataLower <Comparison operator> <Value>"},"DataOpen":{"description":"財務チャートのターゲット要素の\\"Open\\"値を表します。","example":"=Chart!CurrentDataOpen=1; =Chart!CurrentDataOpen>10; =Chart!CurrentDataOpen=\\"Value1\\"; ","label":"DataOpen","syntax":"Chart!CurrentDataOpen <Comparison operator> <Value>"},"DataUpper":{"description":"ガントチャートのターゲット要素の\\"Upper\\"値を表します。","example":"=Chart!CurrentDataUpper=1; =Chart!CurrentDataUpper>10; =Chart!CurrentDataUpper=\\"Value1\\"; ","label":"DataUpper","syntax":"Chart!CurrentDataUpper <Comparison operator> <Value>"},"Detail":{"description":"現在データの詳細フィールドの値を返します。","example":"=Chart!CurrentDetail=1; =Chart!CurrentDetail>10; =Chart!CurrentDetail=\\"Value1\\";","label":"CurrentDetail","syntax":"Chart!CurrentDetail <Comparison operator> <Value>"},"label":"現在データ"},"Functions":{"CurrentValue":{"description":"指定されたプロット上のデータポイントの現在の値を表します。","example":"=CurrentValue(\\"plot1\\"); =CurrentValue(\\"plot1\\",\\"high\\"); ","label":"CurrentValue","syntax":"CurrentValue(\\"plot name\\" , \\"optional value identifier (high, low, open, close, upper, lower)\\")"},"NextValue":{"description":"指定されたプロット上のデータポイントの次の値を表します。","example":"=NextValue(\\"plot1\\"); =NextValue(\\"plot1\\",\\"high\\"); ","label":"NextValue","syntax":"NextValue(\\"plot name\\" , \\"optional value identifier (high, low, open, close, upper, lower)\\")"},"PreviousValue":{"description":"指定されたプロット上のデータポイントの前の値を表します。","example":"=PreviousValue(\\"plot1\\"); =PreviousValue(\\"plot1\\",\\"high\\"); ","label":"PreviousValue","syntax":"PreviousValue(\\"plot name\\" , \\"optional value identifier (high, low, open, close, upper, lower)\\")"},"label":"関数"},"Next":{"Category":{"description":"次データのカテゴリフィールドの値を返します。","example":"=Chart!NextCategory=1; =Chart!NextCategory>10; =Chart!NextCategory=\\"Value1\\";","label":"NextCategory","syntax":"Chart!NextCategory <Comparison operator> <Value>"},"Data":{"description":"次データのデータフィールドの値を返します。","example":"=Chart!NextData=1; =Chart!NextData>10; =Chart!NextData=\\"Value1\\";","label":"NextData","syntax":"Chart!NextData <Comparison operator> <Value>"},"DataClose":{"description":"財務チャートのターゲット要素の次の\\"Close\\"値を表します。","example":"=Chart!NextDataClose=1; =Chart!NextDataClose>10; =Chart!NextDataClose=\\"Value1\\"; ","label":"DataClose","syntax":"Chart!NextDataClose <Comparison operator> <Value>"},"DataHigh":{"description":"財務チャートのターゲット要素の次の\\"High\\"値を表します。","example":"=Chart!NextDataHigh=1; =Chart!NextDataHigh>10; =Chart!NextDataHigh=\\"Value1\\"; ","label":"DataHigh","syntax":"Chart!NextDataHigh <Comparison operator> <Value>"},"DataLow":{"description":"財務チャートのターゲット要素の次の\\"Low\\"値を表します。","example":"=Chart!NextDataLow=1; =Chart!NextDataLow>10; =Chart!NextDataLow=\\"Value1\\"; ","label":"DataLow","syntax":"Chart!NextDataLow <Comparison operator> <Value>"},"DataLower":{"description":"ガントチャートのターゲット要素の次の\\"Lower\\"値を表します。","example":"=Chart!NextDataLower=1; =Chart!NextDataLower>10; =Chart!NextDataLower=\\"Value1\\"; ","label":"DataLower","syntax":"Chart!NextDataLower <Comparison operator> <Value>"},"DataOpen":{"description":"財務チャートのターゲット要素の次の\\"Open\\"値を表します。","example":"=Chart!NextDataOpen=1; =Chart!NextDataOpen>10; =Chart!NextDataOpen=\\"Value1\\"; ","label":"DataOpen","syntax":"Chart!NextDataOpen <Comparison operator> <Value>"},"DataUpper":{"description":"ガントチャートのターゲット要素の次の\\"Upper\\"値を表します。","example":"=Chart!NextDataUpper=1; =Chart!NextDataUpper>10; =Chart!NextDataUpper=\\"Value1\\"; ","label":"DataUpper","syntax":"Chart!NextDataUpper <Comparison operator> <Value>"},"Detail":{"description":"次データの詳細フィールドの値を返します。","example":"=Chart!NextDetail=1; =Chart!NextDetail>10; =Chart!NextDetail=\\"Value1\\";","label":"NextDetail","syntax":"Chart!NextDetail <Comparison operator> <Value>"},"label":"次データ"},"Previous":{"Category":{"description":"前データのカテゴリフィールドの値を返します。","example":"=Chart!PreviousCategory=1; =Chart!PreviousCategory>10; =Chart!PreviousCategory=\\"Value1\\";","label":"PreviousCategory","syntax":"Chart!PreviousCategory <Comparison operator> <Value>"},"Data":{"description":"前データのデータフィールドの値を返します。","example":"=Chart!PreviousData=1; =Chart!PreviousData>10; =Chart!PreviousData=\\"Value1\\";","label":"PreviousData","syntax":"Chart!PreviousData <Comparison operator> <Value>"},"DataClose":{"description":"財務チャートのターゲット要素の前の\\"Close\\"値を表します。","example":"=Chart!PreviousDataClose=1; =Chart!PreviousDataClose>10; =Chart!PreviousDataClose=\\"Value1\\"; ","label":"DataClose","syntax":"Chart!PreviousDataClose <Comparison operator> <Value>"},"DataHigh":{"description":"財務チャートのターゲット要素の前の\\"High\\"値を表します。","example":"=Chart!PreviousDataHigh=1; =Chart!PreviousDataHigh>10; =Chart!PreviousDataHigh=\\"Value1\\"; ","label":"DataHigh","syntax":"Chart!PreviousDataHigh <Comparison operator> <Value>"},"DataLow":{"description":"財務チャートのターゲット要素の前の\\"Low\\"値を表します。","example":"=Chart!PreviousDataLow=1; =Chart!PreviousDataLow>10; =Chart!PreviousDataLow=\\"Value1\\"; ","label":"DataLow","syntax":"Chart!PreviousDataLow <Comparison operator> <Value>"},"DataLower":{"description":"ガントチャートのターゲット要素の前の\\"Lower\\"値を表します。","example":"=Chart!PreviousDataLower=1; =Chart!PreviousDataLower>10; =Chart!PreviousDataLower=\\"Value1\\"; ","label":"DataLower","syntax":"Chart!PreviousDataLower <Comparison operator> <Value>"},"DataOpen":{"description":"財務チャートのターゲット要素の前の\\"Open\\"値を表します。","example":"=Chart!PreviousDataOpen=1; =Chart!PreviousDataOpen>10; =Chart!PreviousDataOpen=\\"Value1\\"; ","label":"DataOpen","syntax":"Chart!PreviousDataOpen <Comparison operator> <Value>"},"DataUpper":{"description":"ガントチャートのターゲット要素の前の\\"Upper\\"値を表します。","example":"=Chart!PreviousDataUpper=1; =Chart!PreviousDataUpper>10; =Chart!PreviousDataUpper=\\"Value1\\"; ","label":"DataUpper","syntax":"Chart!PreviousDataUpper <Comparison operator> <Value>"},"Detail":{"description":"前データの詳細フィールドの値を返します。","example":"=Chart!PreviousDetail=1; =Chart!PreviousDetail>10; =Chart!PreviousDetail=\\"Value1\\";","label":"PreviousDetail","syntax":"Chart!PreviousDetail <Comparison operator> <Value>"},"label":"前データ"},"label":"Chart"},"commonValues":{"info":{"currentDateTime":{"description":"現在の日付と時刻を表示します。ページヘッダおよびページフッタで使用可能です。"},"pageNM":{"description":"現在のページ番号 (N) とページ数 (M) を \'N of M\' の書式で表示します。ページヘッダおよびページフッタで使用可能です。"},"pageNMCumulative":{"description":"丁合いのレポートにおいて、現在のページ番号と総ページ数を表示します。"},"pageNMSection":{"description":"この関数が属するセクションの現在のページ番号 (N) とページ数 (M) を \'N of M\' の書式で表示します。このセクションは、レポートまたはデータ領域である場合があります。"},"pageNumber":{"description":"現在のページ番号を表示します。ページヘッダおよびページフッタで使用可能です。"},"pageNumberCumulative":{"description":"丁合いのレポートにおいて、現在のページ番号を表示します。"},"pageNumberSection":{"description":"この関数が属するセクションの現在のページ番号を表示します。このセクションは、レポートまたはデータ領域である場合があります。"},"reportName":{"description":"レポートの名前を表示します。"},"textboxValue":{"description":"この変数は、保存時に現在のTextBoxの値に置き換えられます。","example":"=Sum($$)","example_i11n":"{Sum($$$)}","label":"現在のTextBoxの値"},"totalPages":{"description":"総ページ数を表示します。ページヘッダおよびページフッタで使用可能です。"},"totalPagesCumulative":{"description":"丁合いのレポートにおいて、総ページ数を表示します。"},"totalPagesSection":{"description":"この関数が属するセクションの総ページ数を表示します。このセクションは、レポートやデータ領域である場合があります。"},"userContext":{"description":"関数とセットで使います。例：UserContext.GetValue(\\"name\\"), UserContext.NumberToWords(123)"},"userId":{"description":"レポートをプレビューするユーザーのユーザーIDを表示します。"},"userLanguage":{"description":"レポートをプレビューするユーザーのシステムに設定されている言語を表示します。"}},"titles":{"label":"共通の値"}},"constants":{"dvchart_template":{"PercentageCategory_p0":{"description":"カテゴリ内の値フィールドのパーセンテージを表示します。"},"PercentageDetail_p0":{"description":"詳細内の値フィールドのパーセンテージを表示します。"},"categoryField":{"name":{"description":"カテゴリフィールドの名前を表示します。"},"value":{"description":"カテゴリフィールドの値を表示します。"}},"colorField":{"name":{"description":"色フィールドの名前を表示します。"},"value":{"description":"色フィールドの値を表示します。"}},"detailFields":{"name":{"description":"詳細フィールドの名前を表示します。"},"value":{"description":"詳細フィールドの値を表示します。"}},"shapeField":{"name":{"description":"形状フィールドの名前を表示します。"},"value":{"description":"形状フィールドの値を表示します。"}},"sizeField":{"name":{"description":"サイズフィールドの名前を表示します。"},"value":{"description":"サイズフィールドの値を表示します。"}},"valueField":{"name":{"description":"値フィールドの名前を表示します。"},"value":{"description":"値フィールドの値を表示します。"}}},"titles":{"label":"定数"}},"dataSets":{"titles":{"label":"データセット"}},"documentMap":{"info":{"path":{"description":"TOCレベルのパスを返します。","example":"=DocumentMap.Path & \\"This is Heading 1\\"","label":"パス"}},"titles":{"label":"見出しマップ"}},"functions":{"info":{"aggregate":{"aggregateIf":{"description":"ブール式が指定された条件を満たす場合は、指定された式から値の集計値を計算します。","example":"=AggregateIf(Fields!Discontinued.Value = True, \\"Sum\\", Fields!InStock.Value)","label":"AggregateIf","syntax":"AggregateIf(<Condition>, <AggregateFunction>, <AggregateArguments>)"},"aggregateIfWithScope":{"description":"ブール式が指定されたスコープ内で指定された条件を満たす場合、指定された式から値の集計値を計算します。","example":"=AggregateIf(Fields!Discontinued.Value = True, \\"Sum\\", Fields!InStock.Value, \\"Category\\")","label":"AggregateIf (スコープ)","syntax":"AggregateIf(<Condition>, <AggregateFunction>, <AggregateArguments>, <Scope>)"},"avg":{"description":"指定された式からすべてのnullでない数値の平均を計算します。","example":"=Avg(Fields!LifeExpentancy.Value)","label":"Avg","syntax":"Avg(<Values>)"},"avgWithScope":{"description":"指定されたスコープ内の指定された式からすべてのnullでない数値の平均を計算します。","example":"=Avg(Fields!LifeExpentancy.Value, \\"GroupByCountry\\")","label":"Avg (スコープ)","syntax":"Avg(<Values>, <Scope>)"},"count":{"description":"指定された式からNullでない値の数を計算します。","example":"=Count(Fields!EmployeeID.Value)","label":"Count","syntax":"Count(<Values>)"},"countDistinct":{"description":"指定された式から反復されない値の数を計算します。","example":"=CountDistinct(Fields!OrderID.Value)","label":"CountDistinct","syntax":"CountDistinct(<Values>)"},"countDistinctWithScope":{"description":"指定されたスコープ内の指定された式から反復されない値の数を計算します。","example":"=CountDistinct(Fields!OrderID.Value, \\"GroupByCategory\\")","label":"CountDistinct (スコープ)","syntax":"CountDistinct(<Values>, <Scope>)"},"countRows":{"description":"行数を計算します。","example":"=CountRows()","label":"CountRows","syntax":"CountRows()"},"countRowsWithScope":{"description":"指定されたスコープ内の行数を計算します。","example":"=CountRows(\\"Title\\")","label":"CountRows (スコープ)","syntax":"CountRows(<Scope>)"},"countWithScope":{"description":"指定されたスコープ内の指定された式からnullでない値の数を計算します。","example":"=Count(Fields!EmployeeID.Value, \\"Title\\")","label":"Count (スコープ)","syntax":"Count(<Values>, <Scope>)"},"crossAggregate":{"description":"指定された行グループ・列グループにて、式で指定された値を指定された集計方法で計算します。","example":"=CrossAggregate(Fields!Amount.Value, \\"Sum\\", \\"YearGroup\\", \\"CategoryGroup\\")","label":"CrossAggregate","syntax":"CrossAggregate(<Expression>, <FunctionName>, <ColumnGroupName>, <RowGroupName>)"},"cumulativeTotal":{"description":"現在のページと前のページの式によって返されたページレベルの集計値の合計を計算します。","example":"=CumulativeTotal(Fields!OrderID.Value, \\"Count\\")","label":"CumulativeTotal","syntax":"CumulativeTotal(<Expression>, <Aggregate>)"},"distinctSum":{"description":"他の式の値が繰り返されていない場合は、指定した式の値の合計を計算します。","example":"=DistinctSum(Fields!OrderID.Value, Fields!OrderFreight.Value)","label":"DistinctSum","syntax":"DistinctSum(<Values>, <Value>)"},"distinctSumWithScope":{"description":"指定されたスコープ内で他の式の値が繰り返されていない場合に、指定した式の値の合計を計算します。","example":"=DistinctSum(Fields!OrderID.Value, Fields!OrderFreight.Value, \\"Order\\")","label":"DistinctSum (スコープ)","syntax":"DistinctSum(<Values>, <Value>, <Scope>)"},"first":{"description":"指定された式から最初の値を返します。","example":"=First(Fields!ProductNumber.Value)","label":"First","syntax":"First(<Values>)"},"firstWithScope":{"description":"指定されたスコープ内の指定された式から最初の値を返します。","example":"=First(Fields!ProductNumber.Value, \\"Category\\")","label":"First (スコープ)","syntax":"First(<Values>, <Scope>)"},"last":{"description":"指定された式から最後の値を返します。","example":"=Last(Fields!ProductNumber.Value)","label":"Last","syntax":"Last(<Values>)"},"lastWithScope":{"description":"指定されたスコープ内の指定された式から最後の値を返します。","example":"=Last(Fields!ProductNumber.Value, \\"Category\\")","label":"Last (スコープ)","syntax":"Last(<Values>, <Scope>)"},"max":{"description":"指定された式からnullでない最大の値を返します。","example":"=Max(Fields!OrderTotal.Value)","label":"Max","syntax":"Max(<Values>)"},"maxWithScope":{"description":"指定されたスコープ内の指定された式からnullでない最大の値を返します。","example":"=Max(Fields!OrderTotal.Value, \\"Year\\")","label":"Max (スコープ)","syntax":"Max(<Values>, <Scope>)"},"median":{"description":"指定された式の値の中間点である値を返します。中央値は一連の値の真ん中の値です。","example":"=Median(Fields!OrderTotal.Value)","label":"Median","syntax":"Median(<Values>)"},"medianWithScope":{"description":"指定されたスコープ内で、指定した式の中で順序付けされた値の中間点である値を返します。中央値は一連の値の真ん中の値です。","example":"=Median(Fields!OrderTotal.Value, \\"Year\\")","label":"Median (スコープ)","syntax":"Median(<Values>, <Scope>)"},"min":{"description":"指定された式からnullでない最小の値を返します。","example":"=Min(Fields!OrderTotal.Value)","label":"Min","syntax":"Min(<Values>)"},"minWithScope":{"description":"指定されたスコープ内の指定された式からnullでない最小の値を返します。","example":"=Min(Fields!OrderTotal.Value, \\"Year\\")","label":"Min (スコープ)","syntax":"Min(<Values>, <Scope>)"},"mode":{"description":"指定された式から最も頻繁に発生する値を返します。","example":"=Mode(Fields!OrderTotal.Value)","label":"Mode","syntax":"Mode(<Values>)"},"modeWithScope":{"description":"指定されたスコープ内で、指定された式から最も頻繁に発生する値を返します。","example":"=Mode(Fields!OrderTotal.Value, \\"Year\\")","label":"Mode (スコープ)","syntax":"Mode(<Values>, <Scope>)"},"runningValue":{"description":"別の集計関数をパラメータとして使用して、指定された式からすべてのnullでない数値の実行中の集計値を計算します。","example":"=RunningValue(Fields!Price.Value, \\"Sum\\")","label":"RunningValue","syntax":"RunningValue(<Values>, <AggregateFunction>)"},"runningValueWithScope":{"description":"指定されたスコープ内で、別の集計関数をパラメータとして使用して、指定した式から実行中のすべてのnullでない数値の集計値を計算します。","example":"=RunningValue(Fields!Price.Value, \\"Sum\\", \\"Nwind\\")","label":"RunningValue (スコープ)","syntax":"RunningValue(<Values>, <AggregateFunction>, <Scope>)"},"stDev":{"description":"指定された式のすべてのnullでない値の標準偏差を計算します。","example":"=StDev(Fields!LineTotal.Value)","label":"StDev","syntax":"StDev(<Values>)"},"stDevP":{"description":"指定された式のすべてのnullでない値の母集団標準偏差を計算します。","example":"=StDevP(Fields!LineTotal.Value)","label":"StDevP","syntax":"StDevP(<Values>)"},"stDevPWithScope":{"description":"指定されたスコープ内の指定された式のすべてのnullでない値の母集団標準偏差を計算します。","example":"=StDevP(Fields!LineTotal.Value, \\"Order\\")","label":"StDevP (スコープ)","syntax":"StDevP(<Values>, <Scope>)"},"stDevWithScope":{"description":"指定されたスコープ内で、指定された式のすべてのnullでない値の標準偏差を計算します。","example":"=StDev(Fields!LineTotal.Value, \\"Nwind\\")","label":"StDev (スコープ)","syntax":"StDev(<Values>, <Scope>)"},"sum":{"description":"指定された式の値の合計を計算します。","example":"=Sum(Fields!Price.Value)","label":"Sum","syntax":"Sum(<Values>)"},"sumWithScope":{"description":"指定されたスコープ内の指定された式の値の合計を計算します。","example":"=Sum(Fields!Price.Value, \\"Category\\")","label":"Sum (スコープ)","syntax":"Sum(<Values>, <Scope>)"},"var":{"description":"指定された式のすべてのnullでない値の分散（標準偏差平方和）を計算します。","example":"=Var(Fields!LineTotal.Value)","label":"Var","syntax":"Var(<Values>)"},"varP":{"description":"指定された式のすべてのnullでない値の母分散（母集団標準偏差平方和）を計算します。","example":"=VarP(Fields!LineTotal.Value)","label":"VarP","syntax":"VarP(<Values>)"},"varPWithScope":{"description":"指定されたスコープ内で、指定された式のすべてのnullでない値の母集団分散（母集団標準偏差平方和）を計算します。","example":"=VarP(Fields!LineTotal.Value, \\"Order\\")","label":"VarP (スコープ)","syntax":"VarP(<Values>, <Scope>)"},"varWithScope":{"description":"指定された式のすべてのnullでない値の分散（標準偏差平方和）を計算します。","example":"=Var(Fields!LineTotal.Value, \\"Order\\")","label":"Var (スコープ)","syntax":"Var(<Values>, <Scope>)"}},"conversion":{"format":{"description":"値を指定された形式にフォーマットします。","example":"=Format(Fields!OrderDate.Value, \\"dd MMM yyyy\\")","label":"Format","syntax":"Format(<Value>, <String>)"},"toBoolean":{"description":"指定された値をBooleanに変換します。","example":"=ToBoolean(Fields!HouseOwnerFlag.Value)","label":"ToBoolean","syntax":"ToBoolean(<Value>)"},"toByte":{"description":"指定された値をByteに変換します。","example":"=ToByte(Fields!ProductNumber.Value)","label":"ToByte","syntax":"ToByte(<Value>)"},"toChar":{"description":"指定された値をCharに変換します。","example":"=ToChar(Fields!OrderStatus.Value); =ToChar(“Hello”))","label":"ToChar","syntax":"ToChar(<Value>)"},"toDateTime":{"description":"指定された値をDateおよびTime値に変換します。","example":"=ToDateTime(Fields!SaleDate.Value); =ToDateTime(\\"1 January, 2017\\")","label":"ToDateTime","syntax":"ToDateTime(<Value>)"},"toDecimal":{"description":"指定された値をDecimalに変換します。","example":"=ToDecimal(Fields!Sales.Value)","label":"ToDecimal","syntax":"ToDecimal(<Value>)"},"toDouble":{"description":"指定された値をDoubleに変換します。","example":"=ToDouble(Fields!AnnualSales.Value); =ToDouble(535.85 * .2691 * 67483)","label":"ToDouble","syntax":"ToDouble(<Value>)"},"toInt16":{"description":"指定された値を16ビット符号付き整数に変換します。","example":"=ToInt16(Fields!AnnualSales.Value); =ToInt16(535.85)","label":"ToInt16","syntax":"ToInt16(<Value>)"},"toInt32":{"description":"指定された値を32ビット符号付き整数に変換します。","example":"=ToInt32(Fields!AnnualSales.Value)","label":"ToInt32","syntax":"ToInt32(<Value>)"},"toInt64":{"description":"指定された値を64ビット符号付き整数に変換します。","example":"=ToInt64(Fields!AnnualSales.Value)","label":"ToInt64","syntax":"ToInt64(<Value>)"},"toSingle":{"description":"指定された値を単精度浮動小数点数に変換します。","example":"=ToSingle(Fields!AnnualSales.Value); =ToSingle(15.*********)","label":"ToSingle","syntax":"ToSingle(<Value>)"},"toStringDot":{"description":"値を指定された形式の文字列に変換します。","example":"=Fields!OrderDate.Value.ToString(\\"dd MMM yyyy\\")","label":".ToString","syntax":"<Value>.ToString(<String>)"},"toStringKey":{"description":"指定された値をStringに変換します。","example":"=ToString(Fields!YearlyIncome.Value); =ToString(13.50)","label":"ToString","syntax":"ToString(<Value>)"},"toUInt16":{"description":"指定された値を16ビットの符号なし整数に変換します。","example":"=ToUInt16(Fields!AnnualSales.Value)","label":"ToUInt16","syntax":"ToUInt16(<Value>)"},"toUInt32":{"description":"指定された値を32ビットの符号なし整数に変換します。","example":"=ToUInt32(Fields!AnnualSales.Value)","label":"ToUInt32","syntax":"ToUInt32(<Value>)"},"toUInt64":{"description":"指定された値を64ビットの符号なし整数に変換します。","example":"=ToUInt64(Fields!AnnualSales.Value)","label":"ToUInt64","syntax":"ToUInt64(<Value>)"}},"customFunctions":{"getValue":{"description":"指定したプロパティのユーザーコンテキストの値を表示します。例：\\"name\\", \\"email\\"","example":"=UserContext.getValue(\\"name\\")","label":"GetUserValue","syntax":"UserContext.getValue(<String>)"},"numberToWords":{"description":"指定した数値を単語に変換します。引数が1つの関数は、現在のカルチャを使用します。引数が2つの関数は、2番目の引数で指定したカルチャを使用します（サポートするカルチャ：\\"ja-jp\\", \\"en-us\\", \\"zh-cn\\"）。","example":"=UserContext.NumberToWords(123.5); =UserContext.NumberToWords(981, \\"zh-CN\\")","label":"NumberToWords","syntax":"UserContext.NumberToWords(<Number>, <String>)"}},"dateTime":{"addDays":{"description":"日付間隔を日数で追加した結果である日付と時刻の値を返します。指定した日付間隔は負の値にすることができます。","example":"=Fields!OrderDate.Value.AddDays(5)","label":"AddDays","syntax":"<DateTime>.AddDays(<Number>)"},"addHours":{"description":"時間間隔を時間単位で追加した結果である日付と時刻の値を返します。指定した時間間隔は負の値にすることができます。","example":"=Fields!OrderDate.Value.AddHours(12)","label":"AddHours","syntax":"<DateTime>.AddHours(<Number>)"},"addMilliseconds":{"description":"ミリ秒単位で時間間隔を追加した結果である日付と時刻の値を返します。指定した時間間隔は負の値にすることができます。","example":"=Fields!OrderDate.Value.AddMilliseconds(500)","label":"AddMilliseconds","syntax":"<DateTime>.AddMilliseconds(<Number>)"},"addMinutes":{"description":"時間間隔を分単位で追加した結果である日付と時刻の値を返します。指定した時間間隔は負の値にすることができます。","example":"=Fields!OrderDate.Value.AddMinutes(30)","label":"AddMinutes","syntax":"<DateTime>.AddMinutes(<Number>)"},"addMonths":{"description":"日付間隔を月単位で追加した結果の日付と時刻の値を返します。指定した日付間隔は負の値にすることができます。","example":"=Fields!OrderDate.Value.AddMonths(2)","label":"AddMonths","syntax":"<DateTime>.AddMonths(<Number>)"},"addSeconds":{"description":"秒単位で時間間隔を追加した結果である日付と時刻の値を返します。指定した時間間隔は負の値にすることができます。","example":"=Fields!OrderDate.Value.AddSeconds(30)","label":"AddSeconds","syntax":"<DateTime>.AddSeconds(<Number>)"},"addYears":{"description":"年単位の日付間隔を追加した結果である日付と時刻の値を返します。指定した日付間隔は負の値にすることができます。","example":"=Fields!OrderDate.Value.AddYears(3)","label":"AddYears","syntax":"<DateTime>.AddYears(<Number>)"},"dateAdd":{"description":"指定された単位の日付と時刻フィールドに間隔を追加した結果の日付と時刻の値を返します。","example":"=DateAdd(\\"d\\", 5, Fields!SaleDate.Value); =DateAdd(DateInterval.Day, 5, Fields!SaleDate.Value)","label":"DateAdd","syntax":"DateAdd(<DateInterval>, <Number>, <DateTime>)"},"dateDiff":{"description":"指定された単位の開始日時と終了日時の差を返します。","example":"=DateDiff(\\"yyyy\\", Fields!SaleDate.Value, \\"1/1/2015\\"); =DateDiff(DateInterval.Year, Fields!SaleDate.Value, \\"1/1/2015\\")","label":"DateDiff","syntax":"DateDiff(<DateInterval>, <DateTime1>, <DateTime2>[, <DayOfWeek>[, <WeekOfYear>]])"},"datePart":{"description":"指定された日付の指定された部分を表すInteger値を返します。","example":"=DatePart(\\"m\\", Fields!SaleDate.Value)","label":"DatePart","syntax":"DatePart(<DateInterval>, <DateTime>[, <FirstDayOfWeek>[, <FirstWeekOfYear>]])"},"dateSerial":{"description":"指定された年、月、日を表すDate値を返します。時刻情報は真夜中（00:00:00）に設定されます。","example":"=DateSerial(DatePart(\\"yyyy\\", Fields!SaleDate.Value) - 10, DatePart(\\"m\\", Fields!SaleDate.Value) + 5, DatePart(\\"d\\", Fields!SaleDate.Value) - 1)","label":"DateSerial","syntax":"DateSerial(<Year Number>, <Month Number>, <Day Number>)"},"dateString":{"description":"システムの現在の日付を表すString値を返します。","example":"=DateString(); =DatePart(\\"m\\", DateString())","label":"DateString","syntax":"DateString()"},"dateValue":{"description":"文字列で表される日付の情報を含むDate値を返します。時刻は真夜中（00:00:00）に設定されます。","example":"=DateValue(\\"December 12, 2015\\")","label":"DateValue","syntax":"DateValue(<StringDate>)"},"day":{"description":"月の「～日」を表す1から31のInteger値を返します。","example":"=Day(Fields!SaleDate.Value)","label":"Day","syntax":"Day(<DateTime>)"},"hour":{"description":"時刻の「～時」を表す0から23のInteger値を返します。","example":"=Hour(Fields!SaleDate.Value)","label":"Hour","syntax":"Hour(<DateTime>)"},"minute":{"description":"時間の「～分」を表す0から59のInteger値を返します。","example":"=Minute(Fields!SaleDate.Value)","label":"Minute","syntax":"Minute(<DateTime>)"},"month":{"description":"年の「～月」を表す1から12のInteger値を返します。","example":"=Month(Fields!SaleDate.Value)","label":"Month","syntax":"Month(<DateTime>)"},"monthName":{"description":"日付で指定された月の名前をStringとして返します。","example":"=MonthName(Fields!MonthNumber.Value)","label":"MonthName","syntax":"MonthName(<Month Number>[, <Abbreviate>])"},"now":{"description":"システムの現在の日付と時刻を返します。","example":"=Now()","label":"Now","syntax":"Now()"},"parse":{"description":"指定された文字列値を日時値に変換します。","example":"=DateTime.Parse(\\"01/01/1970\\")","label":"DateTime.Parse","syntax":"DateTime.Parse(<String>[, <String>])"},"quarter":{"description":"年の四半期を表す1〜4の整数値を返します。","example":"=Quarter(Fields!SaleDate.Value)","label":"Quarter","syntax":"Quarter(<DateTime>)"},"quarterName":{"description":"年の四半期を表す文字列値を返します。","example":"=QuarterName(Fields!SaleDate.Value)","label":"QuarterName","syntax":"QuarterName(<DateTime>)"},"second":{"description":"分の秒を表す0～59のInteger値を返します。","example":"=Second(Fields!SaleDate.Value)","label":"Second","syntax":"Second(<DateTime>)"},"timeOfDay":{"description":"システムの現在の時刻を含むDate値を返します。","example":"=TimeOfDay()","label":"TimeOfDay","syntax":"TimeOfDay()"},"timeSerial":{"description":"指定された時、分、秒を表すDate値を返します。日付情報は0001年1月1日に設定されます。","example":"=TimeSerial(DatePart(\\"h\\", Fields!SaleDate.Value), DatePart(\\"n\\", Fields!SaleDate.Value), DatePart(\\"s\\", Fields!SaleDate.Value))","label":"TimeSerial","syntax":"TimeSerial(<Hour Number>, <Minute Number>, <Second Number>)"},"timeString":{"description":"システムの現在の時刻を表すString値を返します。","example":"=TimeString()","label":"TimeString","syntax":"TimeString()"},"timeValue":{"description":"文字列で表される時刻に関する情報を含むDate値を返します。日付は0001年の1月1日に設定されます。","example":"=TimeValue(\\"15:25:45\\"); =TimeValue(Fields!SaleDate.Value)","label":"TimeValue","syntax":"TimeValue(<StringTime>)"},"timer":{"description":"真夜中から経過した秒数を表すDouble値を返します。","example":"=Timer()","label":"Timer","syntax":"Timer()"},"today":{"description":"システムの現在の日付を含むDate値を返します。","example":"=Today()","label":"Today","syntax":"Today()"},"weekday":{"description":"曜日を表す数値を含むInteger値を返します。","example":"=Weekday(Fields!SaleDate.Value, 0)","label":"Weekday","syntax":"Weekday(<DateTime>[, <DayOfWeek>])"},"weekdayName":{"description":"指定された曜日の名前を含むString値を返します。","example":"=WeekdayName(3, True, 0); =WeekDayName(DatePart(\\"w\\", Fields!SaleDate.Value), True, 0)","label":"WeekdayName","syntax":"WeekdayName(<WeekDay>[, <Abbreviate>[, <FirstDayOfWeek>]])"},"year":{"description":"年を表す1から9999までのInteger値を返します。","example":"=Year(Fields!SaleDate.Value)","label":"Year","syntax":"Year(<DateTime>)"}},"inspection":{"dbNull":{"description":"値がDBNull値かどうかを確認できます。","example":"=IIF(Fields!Organization.Value is DBNull.Value, \\"<NULL>\\", Fields!Organization.Value)","label":"DBNull.Value","syntax":"DBNull.Value"},"isArray":{"description":"式を配列として評価できる場合はTrueを返します。","example":"=IsArray(Parameters!Initials.Value)","label":"IsArray","syntax":"IsArray(<Expression>)"},"isDBNull":{"description":"式がnullと評価される場合はTrueを返します。","example":"=IsDBNull(Fields!MonthlySales.Value)","label":"IsDBNull","syntax":"IsDBNull(<Expression>)"},"isDate":{"description":"式が有効なDate値を表す場合はTrueを返します。","example":"=IsDate(Fields!BirthDate.Value); =IsDate(\\"31/12/2010\\")","label":"IsDate","syntax":"IsDate(<Expression>)"},"isError":{"description":"式がエラーと評価される場合はTrueを返します。","example":"=IsError(Fields!AnnualSales.Value = 80000)","label":"IsError","syntax":"IsError(<Expression>)"},"isNothing":{"description":"式が何も評価されない場合はTrueを返します。","example":"=IsNothing(Fields!MiddleInitial.Value)","label":"IsNothing","syntax":"IsNothing(<Expression>)"},"isNumeric":{"description":"式を数値として評価できる場合はTrueを返します。","example":"=IsNumeric(Fields!AnnualSales.Value)","label":"IsNumeric","syntax":"IsNumeric(<Expression>)"}},"math":{"abs":{"description":"単精度浮動小数点数の絶対値または正の値を返します。","example":"=Abs(-5.5); =Abs(Fields!YearlyIncome.Value - 80000)","label":"Abs","syntax":"Abs(<Number>)"},"acos":{"description":"コサイン（余弦）が指定された数となる角度を返します。","example":"=Acos(.5); =Acos(Fields!Angle.Value)","label":"Acos","syntax":"Acos(<Number>)"},"asin":{"description":"サイン（正弦）が指定された数となる角度を返します。","example":"=Asin(.5); =Asin(Fields!Angle.Value)","label":"Asin","syntax":"Asin(<Number>)"},"atan":{"description":"タンジェント（正接）が指定された数となる角度を返します。","example":"=Atan(.5); =Atan(Fields!Angle.Value)","label":"Atan","syntax":"Atan(<Number>)"},"atan2":{"description":"タンジェント（正接）が指定された2つの数値の商となる角度を返します。","example":"=Atan2(3,7); =Atan2(Fields!CoordinateY.Value, Fields!CoordinateX.Value)","label":"Atan2","syntax":"Atan2(<Number1>, <Number2>)"},"bigMul":{"description":"2つの32ビット数の乗算を返します。","example":"=BigMul(4294967295,-2147483647); =BigMul(Fields!Int32Value.Value, Fields!Int32Value.Value)","label":"BigMul","syntax":"BigMul(<Number1>, <Number2>)"},"ceiling":{"description":"指定された倍精度浮動小数点数と同じか、より大きい整数のうち、最小のものを返します。","example":"=Ceiling(98.4331); =Ceiling(Fields!AnnualSales.Value / 6)","label":"Ceiling","syntax":"Ceiling(<Number>)"},"cos":{"description":"指定された倍精度浮動小数点数と同じか、より大きい整数のうち、最小のものを返します。","example":"=Cos(60)","label":"Cos","syntax":"Cos(<Number>)"},"cosh":{"description":"指定された角度のハイパーボリックコサイン（双曲線余弦）を返します。","example":"=Cosh(60)","label":"Cosh","syntax":"Cosh(<Number>)"},"e":{"description":"Eの値2.71828182845905を返します。","example":"=E * 2","label":"E","syntax":"E"},"exp":{"description":"指定された累乗でeを返します。eはオイラー数で、これはLog関数の逆関数です。","example":"=Exp(3); =Exp(Fields!IntegerCounter.Value)","label":"Exp","syntax":"Exp(<Number>)"},"fix":{"description":"数値の整数部分を返します。","example":"=Fix(-7.15); =Fix(Fields!AnnualSales.Value / -5)","label":"Fix","syntax":"Fix(<Number>)"},"floor":{"description":"指定された倍精度浮動小数点数と同じか、より小さい整数のうち、最大のものを返します。","example":"=Floor(4.67); =Floor(Fields!AnnualSales.Value / 12)","label":"Floor","syntax":"Floor(<Number>)"},"ieeeRemainder":{"description":"IEEE規格に従って、ある数値を別の数値で除算した後の余りを返します。","example":"=IEEERemainder(9, 8)","label":"IEEERemainder","syntax":"IEEERemainder(<Number1>, <Number2>)"},"log":{"description":"指定された数値の対数を返します。","example":"=Log(20.5); =Log(Fields!NumberValue.Value)","label":"Log","syntax":"Log(<Number>)"},"log10":{"description":"指定された数値の対数を基数10に戻します。","example":"=Log10(20.5); =Log10(Fields!NumberValue.Value)","label":"Log10","syntax":"Log10(<Number>)"},"max":{"description":"指定された式からnullでない最大の値を返します。","example":"=Max(Fields!OrderTotal.Value)","label":"Max","syntax":"Max(<Values>)"},"min":{"description":"指定された式からnullでない最小の値を返します。","example":"=Min(Fields!OrderTotal.Value)","label":"Min","syntax":"Min(<Values>)"},"pi":{"description":"PIの値3.14159265358979を返します。","example":"=2 * PI * Fields!Radius.Value","label":"PI","syntax":"PI"},"pow":{"description":"ある数値を別の数値の累乗にして返します。","example":"=Pow(Fields!Quantity.Value, 2)","label":"Pow","syntax":"Pow(<Number1>, <Number2>)"},"round":{"description":"10進数を四捨五入して最も近い整数、または指定された桁までの最も近い10進数にして返します。","example":"=Round(12.456); =Round(Fields!AnnualSales.Value / 12.3)","label":"Round","syntax":"Round(<Number>)"},"sign":{"description":"8ビット符号付整数の符号を示す値を返します。","example":"=Sign(Fields!AnnualSales.Value - 60000)","label":"Sign","syntax":"Sign(<Number>)"},"sin":{"description":"指定された数値のサイン（正弦）を返します。","example":"=Sin(60)","label":"Sin","syntax":"Sin(<Number>)"},"sinh":{"description":"指定された角度のハイパーボリックサイン（双曲線正弦）を返します。","example":"=Sinh(60)","label":"Sinh","syntax":"Sinh(<Number>)"},"sqrt":{"description":"指定された数値の平方根を返します。","example":"=Sqrt(121)","label":"Sqrt","syntax":"Sqrt(<Number>)"},"tan":{"description":"指定された数値のタンジェント（正接）を返します。","example":"=Tan(60)","label":"Tan","syntax":"Tan(<Number>)"},"tanh":{"description":"指定された角度のハイパーボリックタンジェント（双曲線正接）を返します。","example":"=Tanh(60)","label":"Tanh","syntax":"Tanh(<Number>)"},"truncate":{"description":"小数点以下を四捨五入せずに削除し、整数値を返します。","example":"=Truncate(Fields!UnitPrice.Value)","label":"Truncate","syntax":"Truncate(<Number>)"}},"miscellaneous":{"getFields":{"description":"Fieldsコレクションの現在の内容を含むIDictionary<string,Field>オブジェクトを返します。データ領域内で使用される場合にのみ有効です。この関数は、複雑な条件を処理するコードを簡単に記述できます。GetFields()は、照会された各フィールド値をメソッドに渡す必要があり、多くのフィールドを扱う際には禁止される可能性があります。","example":"=GetFields(); =Code.DisplayAccountID(GetFields())","label":"GetFields","syntax":"GetFields()"},"getLength":{"description":"指定された配列内の要素の数を返します。","example":"=Parameters!MultiValueParameter.Value.GetLength(0)","label":"GetLength","syntax":"<Collection>.GetLength(<Number>)"},"groupIndex":{"description":"現在のグループ内の要素のインデックスを返します。","example":"=GroupIndex()","label":"GroupIndex","syntax":"GroupIndex()"},"groupIndexWithScope":{"description":"指定されたグループ内の要素のインデックスを返します。","example":"=GroupIndex(Group1)","label":"GroupIndex (スコープ)","syntax":"GroupIndex(<Group>)"},"inScope":{"description":"現在の値が指定されたスコープ内にあるかどうかによってtrueまたはfalseに評価されます。","example":"=InScope(\\"Order\\")","label":"InScope","syntax":"InScope(<Scope>)"},"indexOf":{"description":"配列内で特定の要素が見つかった最初のインデックスを返します。存在しない場合は、-1 を返します。","example":"=IndexOf(Parameters!pContinent.Value, Fields!ContinentName.Value) >= 0","example_i11n":"{IndexOf(@pContinent, ContinentName) >= 0}","label":"IndexOf","syntax":"IndexOf(<Source>, <SearchElement>)"},"item":{"description":"Fields / Parameters / ReportItemsから、名前でアイテムを返します。","example":"=Fields.Item(\\"Company Name\\").Name, =Parameters.Item(\\"Parameter1\\").Name, =ReportItems.Item(\\"TextBox1\\").Value","example_i11n":"{Fields.Item(\\"Company Name\\").Name}; {Parameters.Item(\\"Parameter1\\").Name}; {ReportItems.Item(\\"TextBox1\\").Value}","label":"Item","syntax":"<Object | Record>.Item(<String>)"},"join":{"description":"要素間に指定された区切り文字を使用して、配列の要素を結合した結果の文字列を返します。","example":"=Join(Parameters!MultiValueParameter.Value, \\", \\")","label":"Join","syntax":"Join(<Values>, <String>)"},"level":{"description":"現在のスコープ内の再帰的階層の現在の深さのレベルを表す0から始まる整数を返します。階層の最初のレベルは0です。","example":"=Level()","label":"Level","syntax":"Level()"},"levelWithScope":{"description":"指定されたスコープ内の再帰的階層の現在の深さのレベルを表す0から始まる整数を返します。階層の最初のレベルは0です。","example":"=Level(\\"Order\\")","label":"Level (スコープ)","syntax":"Level(<Scope>)"},"lookup":{"description":"名前と値がペアとなったデータセットから、指定された名前と最初に一致する値を返します。","example":"=Lookup(Fields!ProductID.Value, Fields!ProductID.Value, Fields!Quantity.Value, \\"DataSet2\\")","label":"Lookup","syntax":"Lookup(<Source>, <Destination>, <Result>, <DataSet>)"},"lookupSet":{"description":"名前と値がペアとなったデータセットから、指定された名前に対応する一連の値を返します。","example":"=LookupSet(Fields!ProductID.Value, Fields!ProductID.Value, Fields!Quantity.Value, \\"DataSet2\\")","label":"LookupSet","syntax":"LookupSet(<Source>, <Destination>, <Result>, <DataSet>)"},"previous":{"description":"前のデータ行の式の値を計算します。","example":"=Previous(Fields!OrderID.Value)","label":"Previous","syntax":"Previous(<Value>)"},"previousWithScope":{"description":"指定されたスコープ内の前のデータ行の式の値を計算します。","example":"=Previous(Fields!OrderID.Value, \\"Order\\")","label":"Previous (スコープ)","syntax":"Previous(<Expression>, <Scope>)"},"rowNumber":{"description":"すべての実行中の行数を返します。","example":"=RowNumber()","label":"RowNumber","syntax":"RowNumber()"},"rowNumberWithScope":{"description":"指定されたスコープ内のすべての実行中の行数を返します。","example":"=RowNumber(\\"OrderID\\")","label":"RowNumber (スコープ)","syntax":"RowNumber(<Scope>)"}},"programFlow":{"choose":{"description":"引数のリストから値を返します。","example":"=Choose(3, \\"10\\", \\"15\\", \\"20\\", \\"25\\")","label":"Choose","syntax":"Choose(<Index>, <Value1>[, <Value2>,...[, <ValueN>]])"},"iif":{"description":"式がTrueと評価された場合は最初の値を返し、Falseの場合は2番目の値を返します。","example":"=IIF(Fields!AnnualSales.Value >= 80000, \\"Above Average\\", \\"Below Average\\")","label":"IIF","syntax":"IIF(<Condition>, <TruePart>, <FalsePart>)"},"partition":{"description":"指定された数値を含む、指定された間隔に基づいて計算された範囲を表す文字列（形式 x:y）を返します。","example":"=Partition(1999, 1980, 2000, 10)","label":"Partition","syntax":"Partition(<Value>, <Start>, <End>, <Interval>)"},"switch":{"description":"式のリストの中でTrueと評価される最初の式の値を返します。","example":"=Switch(Fields!FirstName.Value = \\"Abraham\\", \\"Adria\\", Fields!FirstName.Value = \\"Charelotte\\", \\"Cherrie\\")","label":"Switch","syntax":"Switch(<Condition1>, <Value1>[, <Condition2>, <Value2>,...[, <ConditionN>, <ValueN>]])"}},"text":{"contains":{"description":"文字列に指定された部分文字列が含まれる場合、Trueを返します。","example":"=Fields!ShipAddress.Value.Contains(\\"street\\")","label":"Contains","syntax":"<String>.Contains(<String>)"},"endsWith":{"description":"文字列が指定された部分文字列で終わる場合、Trueを返します。","example":"=Fields!Description.Value.EndsWith(\\"documents\\")","label":"EndsWith","syntax":"<String>.EndsWith(<String>)"},"inStr":{"description":"文字列内で指定された部分文字列が最初に現れる開始位置を返します。","example":"=InStr(Fields!Description.Value, \\"documents\\")","label":"InStr","syntax":"InStr(<String>, <String>)"},"lastIndexOf":{"description":"文字列内で指定された部分文字列が最後に出現するインデックスを返します。","example":"=Fields!Description.Value.LastIndexOf(\\"documents\\")","label":"LastIndexOf","syntax":"<String>.LastIndexOf(<String>[, <Number>])"},"replace":{"description":"最初に指定された部分文字列のすべての出現を、文字列内の2番目に指定された部分文字列で置き換えます。","example":"=Fields!Description.Value.Replace(\\"documents\\", \\"invoices\\")","label":"Replace","syntax":"<String>.Replace(<String>, <String>)"},"startsWith":{"description":"文字列が指定された部分文字列で始まる場合、Trueを返します。","example":"=Fields!Description.Value.StartsWith(\\"Invoice\\")","label":"StartsWith","syntax":"<String>.StartsWith(<String>)"},"substring":{"description":"指定された長さの指定された位置（ゼロから始まる）の部分文字列を返します。","example":"=Fields!Description.Value.Substring(1, 10)","label":"Substring","syntax":"<String>.Substring(<Number>, <Number>)"},"toLower":{"description":"指定された文字列を小文字で返します。","example":"=Fields!ShipCountry.Value.ToLower()","label":"ToLower","syntax":"<String>.ToLower()"},"toUpper":{"description":"指定された文字列を大文字で返します。","example":"=Fields!ShipCountry.Value.ToUpper()","label":"ToUpper","syntax":"<String>.ToUpper()"},"trim":{"description":"指定された文字列の先頭と末尾の両方からすべての空白文字を削除した後、文字列を返します。","example":"=Parameters!Info.Value.Trim()","label":"Trim","syntax":"<String>.Trim()"},"trimEnd":{"description":"指定された文字列の末尾からすべての空白文字を削除した後、文字列を返します。","example":"=Parameters!Info.Value.TrimEnd()","label":"TrimEnd","syntax":"<String>.TrimEnd()"},"trimStart":{"description":"指定された文字列の先頭からすべての空白文字を削除した後、文字列を返します。","example":"=Parameters!Info.Value.TrimStart()","label":"TrimStart","syntax":"<String>.TrimStart()"}}},"titles":{"aggregate":"集計","conversion":"変換","dateTime":"日付と時刻","inspection":"検査","label":"共通の関数","math":"数学","miscellaneous":"その他","programFlow":"プログラムフロー","text":"文字列"}},"operations":{"info":{"arithmetic":{"add":{"description":"2つの数値の合計を評価するか、2つの文字列を連結します。","example":"=Fields!Quantity.Value + 2","label":"+","syntax":"<Value1> + <Value2>"},"divide":{"description":"2つの数値を除算し（分子／分母）、その商を浮動小数点数として返します。","example":"=Fields!AnnualSales.Value / 2","label":"/","syntax":"<Number1> / <Number2>"},"integerDivide":{"description":"2つの数値を除算し、整数の結果を返します。","example":"=Fields!AnnualSales.Value \\\\ 2","label":"\\\\","syntax":"<Number1> \\\\ <Number2>"},"mod":{"description":"2つの数値を除算し、余りの数を返します。","example":"=Fields!AnnualSales.Value Mod 12","label":"Mod","syntax":"<Number1> Mod <Number2>"},"multiply":{"description":"2つの数値の乗算を評価します。","example":"=Fields!Quantity.Value * 5","label":"*","syntax":"<Number1> * <Number2>"},"power":{"description":"ある数値を別の数値の累乗にします。","example":"=Fields!Quantity.Value ^ 2","label":"^","syntax":"<Number1> ^ <Number2>"},"subtract":{"description":"2つの数値の差を評価するか、数値式の値を否定します。","example":"=Fields!Quantity.Value - 2","label":"-","syntax":"<Number1> - <Number2>"}},"bitShift":{"leftShift":{"description":"ビットパターンに対して算術左シフトを実行します。","example":"=Fields!RegionID.Value << 2","label":"<<","syntax":"<Number1> << <Number2>"},"rightShift":{"description":"ビットパターンに対して算術右シフトを実行します。","example":"=Fields!RegionID.Value >> 2","label":">>","syntax":"<Number1> >> <Number2>"}},"comparison":{"equal":{"description":"左のオペランドが右のオペランドと等しい場合はTrueを返します。","example":"=Fields!AnnualSales.Value = 80000","label":"=","syntax":"<Value1> = <Value2>"},"greaterThan":{"description":"左のオペランドが右のオペランドより大きい場合はTrueを返します。","example":"=Fields!AnnualSales.Value > 80000","label":">","syntax":"<Value1> > <Value2>"},"greaterThanOrEqual":{"description":"左のオペランドが右のオペランドと等しいか、より大きい場合はTrueを返します。","example":"=Fields!AnnualSales.Value >= 80000","label":">=","syntax":"<Value1> >= <Value2>"},"is":{"description":"2つのオブジェクト参照を比較し、左のオペランドが右のオペランドと同一であればTrueを返します。","example":"=Fields!FirstName.Value Is Fields!LastName.Value","label":"Is","syntax":"<Value1> Is <Value2>"},"like":{"description":"2つの文字列を比較し、左のオペランドが右のオペランドと同じであればTrueを返します。","example":"=Fields!FirstName.Value Like \\"A*\\"","label":"Like","syntax":"<String1> Like <String2>"},"lowerThan":{"description":"左のオペランドが右のオペランドよりも小さい場合はTrueを返します。","example":"=Fields!AnnualSales.Value < 80000","label":"<","syntax":"<Value1> < <Value2>"},"lowerThanOrEqual":{"description":"左のオペランドが右のオペランドよりも小さいか等しい場合はTrueを返します。","example":"=Fields!AnnualSales.Value <= 80000","label":"<=","syntax":"<Value1> <= <Value2>"},"notEqual":{"description":"左のオペランドが右のオペランドと等しくない場合はTrueを返します。","example":"=Fields!AnnualSales.Value <> 80000","label":"<>","syntax":"<Value1> <> <Value2>"}},"concatenation":{"add":{"description":"2つの数値の合計を評価するか、2つの文字列を連結します。","example":"=Fields!FirstName.Value + \\" \\" + Fields!LastName.Value","label":"+","syntax":"<String1> + <String2>"},"concat":{"description":"個別に文字列に評価される2つの式の連結の文字列値を返します。","example":"=Fields!FirstName.Value & \\" \\" & Fields!LastName.Value","example_i11n":"{FirstName & \\" \\" & LastName}","label":"&","syntax":"<String1> & <String2>"}},"logicalBitwise":{"and":{"description":"2つのブール式の論理積、または2つの数値式のビットごとの論理積を返します。","example":"=(Fields!AnnualSales.Value > 80000) And (Fields!Quantity.Value > 5)","label":"And","syntax":"<Value1> And <Value2>"},"andAlso":{"description":"最初の式の評価によって結果が得られた場合、もう一方の式の評価をスキップして2つのブール式の論理積を返します。","example":"=(Fields!AnnualSales.Value > 80000) AndAlso (Fields!Quantity.Value > 1)","label":"AndAlso","syntax":"<Boolean1> AndAlso <Boolean2>"},"not":{"description":"ブール式の論理否定、または数値式のビットごとの否定を返します。","example":"=Not (Fields!AnnualSales.Value > 80000)","label":"Not","syntax":"Not <Value>"},"or":{"description":"2つのブール式の論理和、または2つの数値のビットごとの論理和を返します。","example":"=(Fields!AnnualSales.Value > 80000) Or (Fields!Quantity.Value > 5)","label":"Or","syntax":"<Value1> Or <Value2>"},"orElse":{"description":"片方の式の評価によって結果が得られた場合、一方の式の評価をスキップして2つのブール式の論理和を返します。","example":"=(Fields!AnnualSales.Value > 80000) OrElse (Fields!Quantity.Value > 1)","label":"OrElse","syntax":"<Boolean1> OrElse <Boolean2>"},"xor":{"description":"2つのブール式の排他的論理和演算、または2つの数値式のビットごとの除外を返します。","example":"=(Fields!AnnualSales.Value > 80000) Xor (Fields!Quantity.Value) > 5","label":"Xor","syntax":"<Value1> Xor <Value2>"}}},"titles":{"arithmetic":"算術","bitShift":"ビットシフト","comparison":"比較","concatenation":"連結","label":"演算子","logicalBitwise":"論理／ビット"}},"parameters":{"titles":{"label":"パラメータ"}},"reportItems":{"titles":{"label":"レポートアイテム"}},"reportPartProperties":{"info":{"example":"=PartProperties!<PropertyName>.Value","example_i11n":"{PartProperties.<PropertyName>.Value}, {PartProperties!<PropertyName>.Value}"},"titles":{"label":"レポートパーツのプロパティ"}},"slicers":{"titles":{"label":"スライサー"}},"textEncodingFields":{"titles":{"label":"テキストエンコードフィールド"}},"theme":{"titles":{"color":"色","constant":"定数","font":"フォント","image":"画像","label":"テーマ","majorFont":"メジャーフォント","minorFont":"マイナーフォント"}}}},{"lng":"ja","ns":"filters","resources":{"add":"追加...","addCriterion":"条件追加","addGroup":"グループの追加","addItem":"アイテムの追加","allOf":"すべて","anyOf":"いずれか","delete":"削除","edit":"編集...","expressionText":"式...","listItemsCount_0":"{{count}} 個の項目","newParameter":"新規パラメータ","operators":{"beginsWith":"Begins With","between":"Between","bottomN":"Bottom N","bottomPercent":"Bottom Percent","contains":"Contains","doesNotBeginWith":"Does Not Begin With","doesNotContain":"Does Not Contain","equalTo":"Equal To","greaterThan":"Greater Than","greaterThanOrEqualTo":"Greater Than Or Equal To","in":"In","lessThan":"Less Than","lessThanOrEqualTo":"Less Than Or Equal To","like":"Like","notEqualTo":"Not Equal To","notIn":"Not In","topN":"Top N","topPercent":"Top Percent"},"reset":"リセット"}},{"lng":"ja","ns":"glyphs-RPX","resources":{"barcode":{"textError":"エラー","unsupportedSymbology":"[{{symbology}}] \\"{{itemName}}\\" は、デザイン時の描画に制限があります。"}}},{"lng":"ja","ns":"groupEditor","resources":{"addGroup":{"btnAdjacentAfter":"後に隣接","btnAdjacentBefore":"前に隣接","btnChild":"子","btnParent":"親"},"addTotal":{"btnAfter":"後","btnBefore":"前"},"btnDelete":"削除","btnDisableGroup":"グループの無効化","btnEditExpression":"式の編集","btnEnableGroup":"グループの有効化","headingAddGroup":"グループの追加","headingAddTotal":"合計の追加","headingColumnGroups":"列グループ","headingRowGroups":"行グループ","labelAdvancedMode":"詳細モード","textHiddenStatic":"(静的)","textSelectTablix":"Tablixを選択してグループを編集"}},{"lng":"ja","ns":"labels","resources":{"body":"本文","dvchartPlotRuleNoCondition":"","dvchartXAxis":"X 軸","dvchartYAxis":"Y 軸","footer":"フッタ","header":"ヘッダ","pageFooter":"ページフッタ","pageHeader":"ページヘッダ","total":"合計"}},{"lng":"ja","ns":"marginsSizes","resources":{"values":[{"_name":"なし","cm":{"bottom":"0cm","left":"0cm","right":"0cm","top":"0cm"},"in":{"bottom":"0in","left":"0in","right":"0in","top":"0in"}},{"_name":"狭い","cm":{"bottom":"1.25cm","left":"1.25cm","right":"1.25cm","top":"1.25cm"},"in":{"bottom":"0.5in","left":"0.5in","right":"0.5in","top":"0.5in"}},{"_name":"通常","cm":{"bottom":"2.5cm","left":"2.5cm","right":"2.5cm","top":"2.5cm"},"in":{"bottom":"1in","left":"1in","right":"1in","top":"1in"}},{"_name":"広い","cm":{"bottom":"2.5cm","left":"5cm","right":"5cm","top":"2.5cm"},"in":{"bottom":"1in","left":"2in","right":"2in","top":"1in"}}]}},{"lng":"ja","ns":"nameTemplate-RPX","resources":{"Barcode":"Barcode","CheckBox":"CheckBox","CrossSectionBox":"CrossSectionBox","CrossSectionLine":"CrossSectionLine","Detail":"Detail","GroupFooter":"GroupFooter","GroupHeader":"GroupHeader","InputFieldCheckBox":"InputFieldCheckBox","InputFieldText":"InputFieldText","Label":"Label","Line":"Line","PageBreak":"PageBreak","PageFooter":"PageFooter","PageHeader":"PageHeader","Picture":"Picture","ReportFooter":"ReportFooter","ReportHeader":"ReportHeader","ReportInfo":"ReportInfo","RichTextBox":"RichTextBox","Shape":"Shape","Style":"Style","SubReport":"SubReport","TextBox":"TextBox","Unknown":"Unknown"}},{"lng":"ja","ns":"nameTemplates","resources":{"Continuous":"レポートエリア","Unknown":"アイテム","bandedList":"BandedList","bandedListGroup":"グループ","barcode":"Barcode","bullet":"Bullet","calculatedField":"計算フィールド","categoryGroup":"カテゴリグループ","chart":"Chart","checkbox":"CheckBox","columnGroup":"列グループ","container":"Container","contentplaceholder":"ContentPlaceholder","dashboardSection":"レポートエリア","dataset":"データセット","detailsGroup":"詳細グループ","dvchart":"Chart","dvchartEncodingField":"フィールド","dvchartPlot":"プロット","dvchartoverlay":"オーバーレイ","dvchartrule":"ルール","dvcharttextencoding":"Text","field":"フィールド","fixedPage":"FixedPage","formattedText":"FormattedText","group":"グループ","image":"Image","inputField":"InputField","label":"Label","layer":"レイヤー","line":"Line","list":"List","mailMergeField":"フィールド","overflowPlaceholder":"OverflowPlaceHolder","parameter":"パラメータ","parameterCollection":"パラメータ","partItem":"PartItem","pointer":"ポインタ","reportParameter":"レポートパラメータ","reportPart":"ReportPart","reportPartProperty":"Property","reportSlicer":"レポートスライサー","richtext":"MixedFormatText","rowGroup":"行グループ","seriesGroup":"系列グループ","seriesValue":"値","shape":"Shape","sparkline":"Sparkline","subreport":"SubReport","table":"Table","tableDetailsGroup":"DetailsGroup","tableGroup":"TableGroup","tableOfContents":"TableOfContents","tablix":"Tablix","textbox":"TextBox","tocLevel":"見出しレベル"}},{"lng":"ja","ns":"notifications","resources":{"addReportItem":{"caption":"種類が\'{{reportItemType}}\'のレポートアイテムは{{containerType}}に追加することができません"},"contentPlaceholderSize":{"caption":"アクションを実行することができません","text":"コンテンツがContentPlaceholderのサイズを超えています。"},"contentPlaceholderSizeWithDetails":{"text":"コンテンツがContentPlaceholderのサイズを超えています。{{contentPlaceholder}} 内の {{items}} の位置またはサイズを変更してください。"},"convertTableForClient":{"text":"✘ テーブル\'{{name}}\'は構造が無効なため、Tablixに変換されました。"},"deleteRowColumn":{"caption":"{{rowColumn}}を削除することができません","text":"{{rowColumn}}は削除できません。削除することにより{{itemType}}\'{{itemName}}\'が無効なサイズになるためです。"},"fixedPageSize":{"caption":"アクションを実行することができません","text":"レポートアイテムをページサイズよりも大きくすることはできません。"},"fplPasteFailed":{"caption":"貼り付け操作に失敗しました","text":"レポートアイテムを貼り付けるスペースが足りません。"},"innerException":{"caption":"内部的な例外が発生しました"},"invalidPageSizes":{"caption":"この操作を行うと、ページサイズが無効になります"},"libraries":{"dataSetNotFound":{"text":"必要なデータセット \\"{{dataSetName}}\\" が見つかりません。"},"dataSourceNotFound":{"text":"必要なデータ ソース \\"{{dataSourceName}}\\" が見つかりません。"},"duplicateLibrary":{"text":"{{libProp}} \\"{{libValue}}\\" を持つライブラリはすでに存在します。ライブラリ {{libProp}} は、一意である必要があります。"},"importFailed":{"caption":"\\"{{path}}\\" ライブラリのインポートに失敗しました"},"invalidLibraryName":{"text":"ライブラリ名 \\"{{libName}}\\" が無効です。英数字とアンダースコアのみ、使用できます。"},"loadingFailed":{"caption":"ライブラリ \\"{{path}}\\" のロードに失敗しました"},"noDataSetsFound":{"text":"ライブラリにデータセットが見つかりません。"},"nonMslLibrary":{"text":"ライブラリとして解析できません。"},"unknownLibrary":{"caption":"不明なライブラリに依存しています","text":"このレポートで使用されているライブラリ \\"{{libName}}\\" が見つかりません。"}},"listRowsOrColumnsCount":{"caption":"アクションを実行できません","text":"コンテンツがリストのサイズ（行／列）を超えています。"},"lockLayout":{"caption":"レポートレイアウトがロックされています。","text":"レポートアイテムのリサイズ、移動、削除、追加、およびレイアウトプロパティの変更は制限されています。"},"masterReports":{"dataSetsWereRenamed":{"caption":"データセット名の変更","text":"マスターレポート内のデータセット名と競合するため、変更しました: {{dataSetNames}}."},"dataSourcesWereRenamed":{"caption":"データソース名の変更","text":"マスターレポート内のデータソース名と競合するため、変更しました: {{dataSourceNames}}."},"embeddedImagesWereRenamed":{"caption":"埋め込み画像の名前の変更","text":"マスターレポート内の埋め込み画像の名前と競合するため、変更しました: {{embeddedImagesNames}}."},"layersWereRenamed":{"caption":"レイヤー名の変更","text":"マスターレポート内のレイヤー名と競合するため、変更しました: {{layersNames}}."},"parametersWereRenamed":{"caption":"パラメータ名の変更","text":"マスターレポート内のパラメータ名と競合するため、変更しました: {{parameterNames}}."},"reportItemsWereMovedOrResized":{"caption":"レポートアイテムの移動またはサイズ変更","details":"{{num}}. {{contentPlaceholder}} 内の {{items}}\\n","text":"更新したマスターレポートのContentPlaceholderに収まるように、レポートアイテムを移動またはサイズ変更しました。\\n{{outsideItems}}"},"reportItemsWereRenamed":{"caption":"レポートアイテム名の変更","text":"マスターレポート内のレポートアイテム名と競合するため、変更しました: {{reportItemNames}}."}},"pasteFailed":{"caption":"貼り付けるアイテムが無効です: {{elementName}}","text":"OverflowPlaceholderを貼り付けられるのは、ページレポートだけです。"},"pasteFailedDecode":{"caption":"貼り付け中のデコードエラー: {{reason}}"},"pasteWarningDecode":{"caption":"貼り付け中のデコード警告","text":"{{warning}}"},"removeDefaultLayer":{"caption":"デフォルトのレイヤーは削除することはできません"},"removeLastValidValue":{"caption":"バインドされたパラメータのうち最後の有効な値は削除できません"},"transaction":{"caption":"トランザクションに失敗しました: {{innerException}}"}}},{"lng":"ja","ns":"pageSizes","resources":{"values":[{"_name":"レター","cm":{"height":"27.9cm","width":"21.6cm"},"in":{"height":"11in","width":"8.5in"}},{"_name":"タブロイド","cm":{"height":"43.2cm","width":"27.9cm"},"in":{"height":"17in","width":"11in"}},{"_name":"リーガル","cm":{"height":"35.6cm","width":"21.6cm"},"in":{"height":"14in","width":"8.5in"}},{"_name":"エグゼクティブ","cm":{"height":"26.7cm","width":"18.4cm"},"in":{"height":"10.5in","width":"7.25in"}},{"_name":"A3","cm":{"height":"42cm","width":"29.7cm"},"in":{"height":"16.54in","width":"11.69in"}},{"_name":"A4","cm":{"height":"29.7cm","width":"21cm"},"in":{"height":"11.69in","width":"8.27in"}},{"_name":"A5","cm":{"height":"21cm","width":"14.8cm"},"in":{"height":"8.27in","width":"5.83in"}},{"_name":"A6","cm":{"height":"14.8cm","width":"10.5cm"},"in":{"height":"5.83in","width":"4.13in"}},{"_name":"B4 (JIS)","cm":{"height":"36.4cm","width":"25.7cm"},"in":{"height":"14.33in","width":"10.12in"}},{"_name":"B5 (JIS)","cm":{"height":"25.7cm","width":"18.2cm"},"in":{"height":"10.12in","width":"7.17in"}},{"_name":"B6 (JIS)","cm":{"height":"18.2cm","width":"12.8cm"},"in":{"height":"7.17in","width":"5.04in"}},{"_name":"B5 (ISO)","cm":{"height":"25cm","width":"17.6cm"},"in":{"height":"9.84in","width":"6.93in"}}]}},{"lng":"ja","ns":"parametersViewEditor","resources":{"alignmentEnum":{"bottom":"Bottom","center":"Center","horizontal":"Horizontal","justify":"Justify","left":"Left","none":"None","right":"Right","top":"Top","vertical":"Vertical"},"bindingAdorner":{"headingBinding":"バインド","textUnspecified":"未設定"},"bindingProperties":{"any":"Any","boolean":"Boolean","date":"Date","dateDateTime":"Date, DateTime","integerFloat":"Integer, Float"},"booleanProperties":{"checkbox":"チェックボックス","falseText":"いいえ","radio":"ラジオボタン","toggle":"トグル","trueText":"はい","undefinedText":"未定義"},"boundParameter":{"allowBlank":"空白の値を許可する","defaultValue":"既定値","multiValue":"複数の値を許可する","multiline":"複数行表示","nullable":"Null値を許可する","parameter":"パラメータ {{name}}","text":"バウンドパラメータ"},"buttonProperties":{"clear":"クリア","preview":"プレビュー","reset":"リセット"},"canvas":"キャンバス","dataType":{"boolean":"Boolean","date":"Date","dateTime":"DateTime","float":"Float","integer":"Integer","string":"String"},"dateRangeParameterLabels":{"monthYearOrder":"Y-M","placeholderDateEnd":"終了日","placeholderDateStart":"開始日","shortcuts":{"labelLastMonth":"先月","labelLastWeek":"先週","labelLastYear":"昨年","labelMonthToDate":"今月","labelWeekToDate":"今週","labelYearToDate":"今年"},"tabLabelAnnually":"毎年","tabLabelDaily":"毎日","tabLabelMonthly":"毎月","textBack":"カレンダーに戻る","textShortcutsList":"共通範囲"},"dateTimeParameterLabels":{"monthYearOrder":"Y-M","textBack":"カレンダーに戻る","textClear":"クリア","textToday":"今日"},"dateTimeRange":{"rangeTypes":{"Current_Day":"今日","Current_Hour":"現在の時","Current_Minute":"現在の分","Current_Month":"今月","Current_Quarter":"今四半期","Current_Week":"今週","Current_Year":"今年","LastSimple_Day":"前日","LastSimple_Hour":"前の時","LastSimple_Minute":"前の分","LastSimple_Month":"前月","LastSimple_Quarter":"前四半期","LastSimple_Second":"前の秒","LastSimple_Week":"前週","LastSimple_Year":"前年","LastToDateSimple_Day":"過去 1 日（今日まで）","LastToDateSimple_Month":"過去 1 月（今日まで）","LastToDateSimple_Quarter":"過去 1 四半期（今日まで）","LastToDateSimple_Week":"過去 1 週（今日まで）","LastToDateSimple_Year":"過去 1 年（今日まで）","LastToDate_Day_0":"過去 {{count}} 日（今日まで）","LastToDate_Month_0":"過去 {{count}} 月（今日まで）","LastToDate_Quarter_0":"過去 {{count}} 四半期（今日まで）","LastToDate_Week_0":"過去 {{count}} 週（今日まで）","LastToDate_Year_0":"過去 {{count}} 年（今日まで）","Last_Day_0":"過去 {{count}} 日","Last_Hour_0":"過去  {{count}} 時","Last_Minute_0":"過去 {{count}} 分","Last_Month_0":"過去 {{count}} 月","Last_Quarter_0":"過去 {{count}} 四半期","Last_Second_0":"過去 {{count}} 秒","Last_Week_0":"過去 {{count}} 週","Last_Year_0":"過去 {{count}} 年","NextSimple_Day":"次の日","NextSimple_Hour":"次の時","NextSimple_Minute":"次の分","NextSimple_Month":"次月","NextSimple_Quarter":"次四半期","NextSimple_Second":"次の秒","NextSimple_Week":"次週","NextSimple_Year":"次年","Next_Day_0":"次の {{count}} 日","Next_Hour_0":"次の {{count}} 時","Next_Minute_0":"次の {{count}} 分","Next_Month_0":"次の {{count}} 月","Next_Quarter_0":"次の {{count}} 四半期","Next_Second_0":"次の {{count}} 秒","Next_Week_0":"次の {{count}} 週","Next_Year_0":"次の {{count}} 年","ToDate_Day":"日初来","ToDate_Month":"月初来","ToDate_Quarter":"四半期初来","ToDate_Week":"週初来","ToDate_Year":"年初来"}},"dateViewProperties":{"accent":"アクセント","days":"日","default":"既定値","error":"エラー","months":"月","none":"None","warning":"警告","years":"年"},"editors":{"nameBoolean":"ブールパラメータ","nameButton":"ボタン","nameDateRange":"日付範囲パラメータ","nameDateTime":"日付時刻パラメータ","nameDateTimeRange":"日付時刻範囲パラメータ","nameDropdown":"ドロップダウンパラメータ","nameHeading":"ヘッダ","nameList":"リストパラメータ","nameMultivalue":"複数値パラメータ","nameNumber":"数値パラメータ","nameNumberRange":"数値範囲パラメータ","namePlainText":"テキスト","nameText":"テキストパラメータ","nameTreeView":"階層パラメータ","textCheckRangeType":"両方のパラメータは同じデータ型でなければなりません。","textHeading":"ヘッダ","textNull":"null"},"fieldsProperties":{"addBtnText":"追加","addBtnTitle":"項目を追加","closeBtnTitle":"閉じる","collectionIsEmpty":"コレクションが空です","items":"個の項目","showItem":"項目を表示する"},"labels":{"removeDefaultValue":"デフォルト値を削除"},"msgString":{"controlNotSupportParameters":"このコントロールは、指定した値をもつパラメータをサポートしません。","controlNotSupportSingle":"このコントロールは、単一値パラメーターをサポートしていません。","controlRequires":"このコントロールには、使用可能な値のリストが必要です。","notSupControl":"は、このコントロールではサポートされていません"},"parameters":{"labelBoolean":"ブールパラメータ","labelDateRange":"日付範囲パラメータ","labelDateTime":"日付時刻パラメータ","labelDateTimeRange":"日付時刻範囲パラメータ","labelDropdown":"ドロップダウンパラメータ","labelHierarchical":"階層パラメータ","labelList":"リストパラメータ","labelMultivalue":"複数値パラメータ","labelNumber":"数値パラメータ","labelNumberRange":"数値範囲パラメータ","labelSingleLineParameter":"テキストパラメータ"},"parametersPanel":{"headingEditParameter":"パラメータの編集","headingEditRange":"範囲の編集","headingEditable":"表示","headingHidden":"非表示","titleEditParameter":"パラメータの編集..."},"parametersText":"パラメータ","placeholderEmpty":"","placeholderMultipleValues":"<複数の値>","properties":{"categories":{"advanced":"その他","appearance":"外観","binding":"バインド","common":"共通","details":"詳細","label":"ラベル","locationAndSize":"位置／サイズ","preview":"プレビュー"},"labels":{"action":"アクション","alignment":"水平方向の整列","amount":"量","anchorDate":"起点の日時","background":"背景","binding":"バインド","color":"色","columns":"列","daysHeaderFormat":"日付書式","display":"表示","dropdown":"ドロップダウン","endpoint":"「まで」の扱い","endpointStates":{"textFalse":"含まない","textTrue":"含む"},"falseText":"\\"False\\"のテキスト","fields":"フィールド","from":"から","groupBy":"グループ化","height":"高さ","label":"ラベル","layout":"レイアウト","left":"左","list":"リスト","max":"最大","maxRange":"最大範囲","min":"最小","mode":"モード","multiline":"複数行","offset":"オフセット","pathString":"パス文字列","placeholder":"プレースホルダ","placeholderFrom":"\\"から\\"のプレースホルダ","placeholderTo":"\\"まで\\"のプレースホルダ","range":"範囲","ranges":"範囲","recursive":"再帰的","roundInputToStep":"ステップ間隔で入力する","showDefaultRanges":{"label":"デフォルトの範囲","textFalse":"非表示","textTrue":"表示"},"slider":"スライダー","step":"ステップ間隔","strikeThrough":"取り消し線","text":"テキスト","to":"まで","top":"上","trueText":"\\"True\\"のテキスト","type":"種類","unit":"単位","upDownEditor":"アップダウン","value":"値","viewMode":"表示モード","width":"幅"}},"propertiesText":"プロパティ","propertyGrid":{"btnCloseSearch":"閉じる","placeholderSearch":"プロパティ名を入力してください...","textEmptyList":"プロパティを表示するアイテムを選択してください","textNoCommonProperties":"共通のプロパティはありません","textUnknownProperty":"不明なプロパティ:"},"sideBar":{"collapse":"折りたたみ","expand":"展開"},"surface":{"btnAutoGenerate":"自動生成","btnGenerate":"生成","emptySurfaceBlock":{"textDescriptionButtonAfter":"できます。","textDescriptionButtonBefore":"ツールボックスのコントロールを使って作成するか、使用可能なパラメーターリストから","textDescriptionOne":"このレポートにはカスタムパラメータビューがありません。","textDescriptionTwo":"ツールボックスのコントロールを使って作成できます。"},"messageBlock":{"textDescriptionButtonAfter":"できます。","textDescriptionButtonBefore":"エディタを","textDescriptionOne":"すべてのレポートパラメータがバインドされているわけではありません。"},"scrollBar":{"textMessagesOne":"パラメータは、このようにビューワのサイドバーに表示されます。","textMessagesTwo":"コンポーネントをドラッグして順序を変更できます。"}},"toolbar":{"btnDelete":"削除","btnDuplicate":"複製","btnGenerateView":"ビューを生成","btnHighlightRequired":"必須項目をハイライト","btnLayoutFreeForm":"フリーレイアウト","btnLayoutStack":"縦方向レイアウト","btnRemoveView":"ビューを削除","btnResetView":"ビューをリセット"}}},{"lng":"ja","ns":"properties-RPX","resources":{"categories":{"appearance":"表示","behavior":"動作","border":"罫線","common":"共通","data":"データ","design":"デザイン","format":"フォーマット","layout":"レイアウト","misc":"その他","page":"ページ","pdf":"PDFフォーム","printing":"印刷","summary":"集計","text":"テキスト","watermark":"ウォーターマーク"},"labels":{"MultiLine":"複数行表示","alignment":"キャプション位置（水平方向）","anchorBottom":"次のセクションまで引く","angle":"角度","autoReplaceFields":"データソースで置換","autoSize":"サイズの自動調整","backColor":"背景色","backColor2":"背景色2","backgroundPattern":"背景パターン","backgroundStyle":"背景スタイル","barCodeStyle":"種類","barHeight":"高さ","barWidth":"細いバーの幅","border":{"color":"色（罫線）","style":"スタイル（罫線）","width":"太さ（罫線）"},"calcFieldDefaultValue":"既定値","calcFieldFormula":"式","calcFieldName":"名前","calcFieldType":"タイプ","calendar":"カレンダー","canGrow":"内容に応じて拡大","canShrink":"内容に応じて縮小","captionGrouping":"キャプションのグループ化","captionPosition":"キャプション位置（垂直方向）","characterSpacing":"文字ピッチ","checkAlignment":"テキストの位置","checkSize":"チェックのサイズ","checkStyle":"チェックの種類","checkSumEnabled":"チェックサム","checked":"チェックする","colKeepTogether":"グループを同じカラムに収める","collate":"丁合い","columnCount":"カラム数","columnDirection":"カラムの出力方向","columnLayout":"カラムレイアウト","columnSpacing":"カラムの間隔","control":{"bottom":"下","dataField":"データフィールド","height":"高さ","left":"左","name":"名前","right":"右","tag":"タグ","top":"上","visible":"表示する","width":"幅"},"countNullValues":"Null値を集計する","crossSectionBoxRadius":"角丸の半径","culture":"カルチャ","dataField":"データフィールド","description":"説明","distinctField":"重複の除外","duplex":"両面印刷","enabled":"有効","expressionErrorMessage":"構文解析エラーのメッセージ","fieldName":"フィールド名","fontFamily":"Font Family","fontLinethrough":"Text Strikethrough","fontSize":"Font Size","fontStyle":"Font Style","fontUnderline":"Text Underline","fontWeight":"Font Weight","foreColor":"色","formatString":"書式","gradientStyle":"グラデーションスタイル","groupKeepTogether":"ページとフッタを同じページに収める","gutterMargin":"とじしろ","height":"高さ","htmlValue":"HTML値","hyperLink":"ハイパーリンク","image":"画像","keepTogether":"セクションを1ページに収める","lineColor":"色","lineSpacing":"行間","lineStyle":"スタイル","lineWeight":"太さ","margin":"余白","maxLength":"最大文字数","maxPages":"最大ページ数","minCondenseRate":"最小縮小率","mirrorMargins":"見開きページ","multiLine":"複数行","name":"名前","newColumn":"新しいカラムの挿入","newPage":"新しいページの挿入","nwratio":"NW比率","orientation":"印刷の方向","outputFormat":"表示形式","padding":"パディング","paperHeight":"高さ","paperSize":"用紙サイズ","paperWidth":"幅","paramUI":"パラメータダイアログを表示する","parameterDefaultValue":"デフォルト値","parameterFormat":"Format","parameterName":"名前","parameterParameterType":"タイプ","parameterPrompt":"表示するメッセージ","parameterPromptUser":"ダイアログを表示する","password":"パスワード","pictureAlignment":"画像の位置","printAtBottom":"ページ下部に表示","printWidth":"印刷可能な幅","quietZoneBottom":"クワイエットゾーン（下）","quietZoneLeft":"クワイエットゾーン（左）","quietZoneRight":"クワイエットゾーン（右）","quietZoneTop":"クワイエットゾーン（上）","radius":{"default":"既定値"},"readonly":"読み取り専用","repeatStyle":"繰り返し出力","repeatToFill":"空行出力","reportName":"サブレポートの名前","required":"必須項目","rightToLeft":"右から左に表示","rotation":"回転","roundingRadius":"角丸の半径","script":"スクリプト","scriptLanguage":"スクリプト言語","shape":"図形の種類","sizeMode":"サイズ設定","spellCheck":"スペルチェック","style":{"backColor":"背景色","className":"スタイル名","ddoCharSet":"文字セット","fontFamily":"フォント名","fontLinethrough":"取り消し線","fontSize":"サイズ","fontStyle":"斜体","fontUnderline":"下線","fontWeight":"太字","foreColor":"色","kinsoku":"禁則","shrinkToFit":"縮小して全体表示","styleName":"名前","textAlign":"水平方向の整列","textJustify":"均等割付","verticalAlign":"垂直方向の整列","verticalText":"縦書き","wrapMode":"折り返し"},"styles":"スタイル","summaryFunc":"集計の計算式","summaryGroup":"集計グループ","summaryRunning":"集計方法","summaryType":"集計レベル","supplementBarHeight":"補助バーコードのバーの高さ","supplementCaptionPosition":"補助バーコードのキャプション位置","supplementDataField":"補助バーコードのデータフィールド","supplementSpacing":"メインと補助バーコードの間隔","supplementText":"補助バーコードの値","tabIndex":"タブ順","tag":"タグ","text":"テキスト","title":"タイトル","underlayNext":"次のセクションの下に隠す","userData":"ユーザーデータ","version":"バージョン","visible":"表示する","watermarkAlignment":"配置","watermarkImage":"画像","watermarkPrintOnPages":"表示対象のページ","watermarkSizeMode":"サイズ設定","x1":"X1","x2":"X2","y1":"Y1","y2":"Y2"}}},{"lng":"ja","ns":"propertyDescriptors","resources":{"categories":{"action":"アクション","appearance":"外観","availableValues":"使用できる値","background":"背景","backgroundAndBorders":"背景と罫線","bar":"横棒の設定","border":"罫線","borderColor":"色","borderRoundingRadius":"罫線の角丸の半径","borderStyle":"スタイル","borderWidth":"太さ","borders":"罫線","column":"列","common":"全般","configurations":"構成","content":"コンテンツ","data":"データ","dataLabel":"データラベル","dataLabelText":"テキスト","defaultValue":"既定値","dimensions":"位置とサイズ","documentMap":"見出しマップ","dvchartLabelBorder":"ラベルの境界線","dvchartLabelLine":"ラベルの罫線","dvchartLabelText":"ラベルのテキスト","dvchartLegend":"凡例","dvchartLegendBackground":"凡例の背景","dvchartLegendBorder":"凡例の境界線","dvchartLegendText":"凡例のテキスト","dvchartLegendTitle":"凡例のタイトル","dvchartPlotColorEncodings":"エンコード - 色","dvchartPlotConfig":"設定","dvchartPlotEncodings":"エンコーディング","dvchartPlotShapeEncodings":"エンコード - 形","dvchartPlotSizeEncodings":"エンコード - サイズ","dvchartPlotStyle":"スタイル","dvchartPlotSymbols":"シンボル","fields":"フィールド","fillStyle":"塗りつぶし","font":"フォント","general":"その他","grid":"グリッド線","group":"グループ","input":"入力フィールド","international":"インターナショナル","labelStyle":"スタイル","labels":"ラベル","layout":"レイアウト","line":"線","majorGrid":"主グリッド線","majorTicks":"主目盛","margins":"余白","marker":"マーカー","minorGrid":"副グリッド線","minorTicks":"副目盛","misc":"その他","noData":"データなし","options":"オプション","pageSize":"サイズ","preview":"プレビュー","range":"レンジ","referenceLine":"参照線","scale":"スケール","seriesLineStyle":"区分線のスタイル","settings":"設定","staticMembers":"静的メンバ","symbology":"シンボル","tableFooter":"フッタ","tableHeader":"ヘッダ","targetDevice":"出力デバイス","targetStyle":"目標値のスタイル","text":"テキスト","threeDProperties":"3Dのプロパティ","tickStyle":"目盛線のスタイル","title":"タイトル","userSort":"ユーザー操作の並べ替え","valueStyle":"スタイル","visibility":"表示"},"labels":{"action":{"applyParameters":"パラメータの適用","jumpToBookmark":"ブックマークに移動","jumpToReport":"レポートに移動","jumpToUrl":"URLに移動","parameters":"パラメータ","slice":"スライサー"},"bandedList":{"canGrow":"内容に応じて拡大","canShrink":"内容に応じて縮小","consumeWhiteSpace":"空白を埋める","preventOrphanedFooter":"フッタ行のみのページの出力を防止","preventOrphanedHeader":"ヘッダ行のみのページの出力を防止","printAtBottom":"ページ下部に表示","repeatOnNewPage":"すべてのページに表示"},"barcode":{"aztecOptions":{"encoding":"エンコード","errorCorrection":"エラー訂正レベル","layers":"レイヤー"},"barHeight":"バーの高さ","captionGrouping":"キャプションのグループ化","captionLocation":"キャプションの表示位置","checksum":"チェックサム","code49Options":{"groupNumber":"グループ番号","grouping":"グループ化"},"dataMatrixOptions":{"ecc000_140SymbolSize":"シンボルサイズ（ECC200以外）","ecc200EncodingMode":"エンコード（ECC200）","ecc200SymbolSize":"シンボルサイズ（ECC200）","eccMode":"ECCモード","encoding":"エンコード","encodingMode":"エンコード","fileIdentifier":"ファイル識別子","structureNumber":"連結番号","structuredAppend":"連結","symbolSize":"シンボルサイズ"},"ean128Fnc1Options":{"barAdjust":"バーの調整","moduleSize":"モジュールサイズ","resolution":"解像度"},"gs1CompositeOptions":{"type":"複合型","value":"複合型の値"},"gs1QrCodeOptions":{"encoding":"エンコード","errorLevel":"エラー訂正レベル","mask":"マスク","version":"バージョン"},"invalidBarcodeText":"無効な場合のメッセージ","maxiCodeOptions":{"mode":"モード"},"microPdf417Options":{"compactionMode":"圧縮モード","fileId":"ファイルID","segmentCount":"セグメント数","segmentIndex":"セグメントインデックス","version":"バージョン"},"microQrCodeOptions":{"encoding":"エンコード","errorLevel":"エラー訂正レベル","mask":"マスク","version":"バージョン"},"narrowBarWidth":"細いバーの幅","nwRatio":"NW比率","nwRatio_help":{"text":"2種類の幅のみを使用するシンボルにおいて、細いバーと太いバーの幅の比率を定義する値です。"},"pdf417Options":{"columns":"列","errorCorrectionLevel":"エラー訂正レベル","rows":"行","type":"PDF417タイプ"},"qrCodeOptions":{"connection":"連結","connectionNumber":"連結番号","encoding":"エンコード","errorLevel":"エラー訂正レベル","mask":"マスク","model":"モデル","version":"バージョン"},"quietZone":"クワイエットゾーン","rotation":"回転","rssExpandedStacked":{"rowCount":"行数"},"supplementOptions":{"barHeight":"補助バーコードのバーの高さ","captionLocation":"補助バーコードのキャプション位置","spacing":"メインと補助バーコードとの間隔","value":"補助バーコードの値"},"symbology":"種類","value":"値"},"border":{"bottom":"下罫線","color":"色","default":"デフォルト","left":"左罫線","right":"右罫線","style":"スタイル","top":"上罫線","width":"太さ"},"bullet":{"bestValue":"最大値","interval":"目盛の間隔","labelFontColor":"色（ラベル）","labelFontFamily":"フォント名（ラベル）","labelFontSize":"サイズ（ラベル）","labelFontStyle":"スタイル（ラベル）","labelFormat":"表示形式","orientation":"表示方向","range1Boundary":"範囲１","range2Boundary":"範囲２","showLabels":"ラベルの表示","targetLineColor":"色","targetLineWidth":"太さ","targetShape":"形状","targetValue":"目標値","tickMarks":"位置","ticksLineColor":"色","ticksLineWidth":"太さ","value":"値","valueColor":"色（値）","worstValue":"最小値"},"checkBox":{"checkAlignment":"テキストの位置","checked":"チェックする","text":"テキスト"},"container":{"canGrow":"内容に応じて拡大","consumeWhiteSpace":"空白を埋める","gridMode":"グリッドモード","linkToChild":"子アイテムの参照","newPage":"新ページ","overflow":"オーバーフロー","pageBreak":"改ページ"},"contentPlaceHolder":{"consumeWhiteSpace":"空白を埋める","text":"メッセージ"},"dashboardSection":{"displayName":"表示名"},"data":{"dataElementName":"要素名","dataElementOutput":"要素出力","dataElementStyle":"要素のスタイル"},"dataRegion":{"dataSetName":"データセット","dataSetParameters":"データセットパラメータ","dataSetParameters_help":{"text":"データセットパラメータプロパティでパラメータを設定することで、異なるデータセットに連結されたデータ領域に、データを表示することができます。"},"filters":"フィルタ","newPage":"新ページ","newSection":"データ領域毎にページ番号を設定","noRowsMessage":"データがない場合のメッセージ","overflowName":"オーバーフローデータの連結先","pageBreak":"改ページ","repeatToFill":"空行出力","sortExpressions":"並べ替えの式","throwIfPlaceHoldersEmpty":"空のプレースホルダを表示しない"},"dataSet":{"accentSensitivity":"アクセントを区別する","boundFields":"バウンドフィールド","calculatedFields":"計算フィールド","caseSensitivity":"大文字、小文字を区別する","collation":"照合順序","commandType":"コマンドの種類","fields":"フィールド","filters":"フィルター","kanatypeSensitivity":"ひらがな、カタカナを区別する","name":"名前","parameters":"パラメータ","query":"クエリ","widthSensitivity":"文字幅を区別する"},"dataSetParameter":{"name":"名前","value":"値"},"dataVisualizer":{"colorScale":{"maximum":"最大値","maximumColor":"最大値の色","middle":"中間値","middleColor":"中間値の色","minimum":"最小値","minimumColor":"最小値の色","useMiddleColor":"中間値の色の使用","value":"値"},"dataBar":{"alternateColor":"代替色","color":"色","maximum":"最大値","minimum":"最小値","useAlternateColor":"代替色の使用","useAlternateColor_help":{"text":"有効にされている場合、\'値\' が \'ゼロ値\' よりも小さいと \'代替色\' が使用されます。"},"value":"値","zeroValue":"ゼロ値"},"gradient":{"color1":"色 1","color2":"色 2","gradientType":"グラデーションの種類"},"hatch":{"color1":"色 1","color2":"色 2","hatchStyle":"網掛けのスタイル"},"iconSet":{"icon1Value":"アイコン1の値","icon2Value":"アイコン2の値","icon3Value":"アイコン3の値","icon4Value":"アイコン4の値","icon5Value":"アイコン5の値","iconSet":"アイコンセット"},"rangeBar":{"color":"色","displayProgressIndicator":"プログレスバーを表示","length":"長さ","maximum":"最大値","minimum":"最小値","progressIndicatorColor":"プログレスバーの色","progressIndicatorLength":"プログレスバーの長さ","startingValue":"開始値"},"visualizerType":"データ可視化の種類"},"dimensions":{"bottom":"下","endPointX":"終点 X","endPointY":"終点 Y","fixedHeight":"固定の高さ","fixedSize":"固定のサイズ","fixedWidth":"固定の幅","height":"高さ","left":"左","location":"位置","right":"右","size":"サイズ","startPointX":"始点 X","startPointY":"始点 Y","top":"上","width":"幅"},"dvChartPlotCustomLabels":{"offsetX":"オフセット X","offsetY":"オフセット Y","text":"テキスト"},"dvChartPlotPointers":{"end":"ニードルの先端","needlePinWidth":"ピンの幅","needleWidth":"ニードルの幅"},"dvPlotOverlays":{"aggregateType":"集計の種類","axis":"軸","backgroundColor":"背景色","backwardForecastPeriod":"後方予測期間","detailLevel":"詳細レベル","display":"表示","end":"終了","field":"フィールド","forwardForecastPeriod":"前方予測期間","intercept":"インターセプト","legendLabel":"凡例ラベル","lineColor":"線の色","lineStyle":"線のスタイル","lineWidth":"線の幅","name":"名前","order":"次数","period":"期間","start":"開始","type":"種類","value":"値"},"dvchart":{"bar":{"bottomWidth":"下部の幅","neckHeight":"ネックの高さ","overlap":"重複","topWidth":"上部の幅","width":"幅"},"customPaletteColors":"パレットのカスタム色","legendHidden":"非表示","legendOrientation":"向き","legendPosition":"位置","legendWrapping":"折り返し","palette":"パレット","plotTemplate":"プロットテンプレート","plotTemplateDropdown":"選択...","plots":"プロット"},"dvchartAxis":{"axisType":"軸の種類","dateMode":"日付モード","gridLinesStyle":"グリッド線のスタイル","height":"高さ","labelAngle":"ラベルの角度","labelField":"ラベルフィールド","lineStyle":"線のスタイル","logBase":"対数軸の底","majorInterval":"主グリッド線の間隔","max":"最大値","maxHeight":"最大の高さ","maxWidth":"最大幅","min":"最小値","minorInterval":"副グリッド線の間隔","origin":"始点","overlappingLabels":"ラベルの重複","plots":"プロット","position":"位置","reversed":"反転","scale":"スケールの種類","showGridLines":"グリッド線を表示","showMajorGridLines":"主グリッド線","showMinorGridLines":"副グリッド線","tickMarks":"目盛","tickSize":"目盛のサイズ","tickStyle":"目盛のスタイル","title":"タイトル","visible":"表示","width":"幅"},"dvchartEncoding":{"aggregateType":"集計","excludeNulls":"null値を除外","fieldType":"フィールドの種類","fieldValue":"値","group":"グループ","sort":"並べ替えの順序","sortingAggregate":"並べ替えの集計","sortingField":"並べ替えの式","target":"ターゲット","templateKey":"テンプレートキー","value":"値"},"dvchartHeaderFooter":{"caption":"キャプション","height":"高さ"},"dvchartLegend":{"hidden":"非表示","iconColor":"アイコンの色","maxHeight":"最大の高さ","maxWidth":"最大幅","orientation":"向き","position":"位置","ranges":"範囲","title":"タイトル"},"dvchartPlot":{"action":"アクションタイプ","axisMode":"軸モード","bar":"バーの線","category":"カテゴリ","categorySort":"カテゴリの並べ替えの順序","categorySortingAggregate":"カテゴリの並べ替えの集計","categorySortingField":"カテゴリの並べ替えの式","clippingMode":"クリッピングモード","color":"カラー","colorAggregate":"カラーの集計","customLabels":"ゲージラベル","details":"詳細","gaugeRanges":"ゲージ範囲","innerRadius":"内半径","lineAspect":"線の側面","lineColor":"線の色","lineStyle":"線のスタイル","lineWidth":"線の幅","offset":"オフセット","opacity":"不透明度","overlays":"オーバーレイ","plotStyle":"プロットのスタイル","pointers":"ゲージポインタ","radial":"半径","rules":"ルール","shape":"シェイプ","shapeAggregate":"シェイプの集計","showNulls":"null値の表示","showValuesNamesInLegend":"カラーに値の名前を表示","size":"サイズ","sizeAggregate":"サイズの集計","startAngle":"開始角度","swapAxes":"X軸／Y軸を入れ替える","sweep":"スイープ","symbolBackgroundColor":"背景色","symbolOpacity":"シンボルの不透明度","symbolShape":"シンボルの形状","symbolStyle":"シンボルのスタイル","symbols":"シンボルの表示","text":"テキスト","textBackgroundColorStyle":"背景色","textConnectingLine":"連結線","textLinePosition":"位置","textOffset":"オフセット","textOverlappingLabels":"ラベルの重複","textPosition":"位置","textTemplate":"テンプレート","texts":"テキストエンコーディング","tooltip":"ツールチップ","tooltipTemplate":"ツールチップのテンプレート","type":"プロットの種類","unpivotData":"ピボット解除","values":"値"},"dvchartPlotArea":{"axes":"軸"},"dvchartPlotRules":{"condition":"条件","name":"名前","ruleProperties":"ルールのプロパティ","targetProperty":"対象のプロパティ","valueRuleProperties":"値"},"dvchartValueEncoding":{"field":{"caption":"キャプション","close":"Close","high":"High","key":"キー","low":"Low","lower":"開始フィールド","open":"Open","upper":"終了フィールド","value":"値"}},"empty":"","filter":{"filterExpression":"式","filterValues":"フィルタする値","operator":"演算子","value":"値"},"font":{"fontFamily":"フォント名","fontSize":"サイズ","fontStyle":"スタイル","fontWeight":"太さ"},"formattedText":{"encodeMailMergeFields":"マージフィールドのエンコード","html":"HTMLコンテンツ","mailMergeFields":"マージフィールド"},"fplPage":{"orientation":"印刷の方向","size":"サイズ"},"fplReport":{"fixedElementName":"ページの要素名","fixedElementOutput":"ページの要素の出力形式"},"group":{"dataCollectionName":"コレクション名","dataElementName":"要素名","dataElementOutput":"要素出力","documentMapLabel":"ラベル","filters":"フィルタ","groupExpressions":"式","name":"名前","newPage":"新ページ","newSection":"グループ毎にページ番号を設定","pageBreak":"改ページ","pageBreakDisabled":"改ページの無効化","parent":"親グループ","printFooterAtBottom":"フッタを一番下に表示する"},"image":{"backgroundRepeat":"背景画像の繰り返し表示","horizontalAlignment":"水平方向の整列","imageLabel":"画像","mimeType":"MIMEの種類","sizing":"サイズ設定","source":"ソース","value":"値","verticalAlignment":"垂直方向の整列"},"inputField":{"checkSize":"チェックのサイズ","checkStyle":"チェックの種類","checked":"チェックを付ける","fieldName":"入力フィールド名","inputType":"種類","maxLength":"文字数","multiline":"複数行","password":"パスワード","readonly":"読み取り専用","required":"必須","spellCheck":"スペルチェック","tabIndex":"タブ順","value":"値"},"layer":{"designerDataFieldVisible":"フィールドセレクタの表示","designerLock":"ロック","designerTransparency":"透過","designerVisible":"表示","name":"名前","targetDevice":{"all":"すべて","export":"エクスポート","paper":"印刷","screen":"画面に表示"}},"line":{"endPoint":"線端","lineColor":"色","lineStyle":"スタイル","lineWidth":"太さ","startPoint":"始点"},"list":{"consumeWhiteSpace":"空白を埋める","dataInstanceElementOutput":"インスタンスの要素出力","dataInstanceName":"インスタンスの要素名","gridMode":"グリッドモード","growDirection":"レイアウト方向","rowsOrColumnsCount":"行または列のカウント"},"margins":{"bottom":"下","left":"左","right":"右","top":"上"},"multipleValues":"<複数の値>","overflowPlaceHolder":{"overflowName":"オーバーフローデータの連結先"},"padding":{"bottom":"下","left":"左","right":"右","top":"上"},"pageSection":{"printOnFirstPage":"1ページ目に印刷","printOnLastPage":"最終ページに印刷"},"parameter":{"omit":"省略","parametername":"名前","parameters":"パラメータ","value":"値"},"partItem":{"library":"ライブラリ","reportPart":"レポートパーツ"},"report":{"author":"作成者","collapseWhiteSpace":"連続する空白を省略","collateBy":"丁合い","columnSpacing":"列の間隔","columns":"列","consumeContainerWhitespace":"空白を埋める","description":"レポートの説明","displayType":"表示形式","embeddedImages":"埋め込み画像","language":"言語","layers":"レイヤー","layoutOrder":"レイアウト順","level":"レベル","levels":"レベル","marginsSizes":"サイズ","marginsStyle":"スタイル","nameMasterReport":"マスターレポート","numberingStyle":"ナンバリング形式","pageHeight":"高さ","pageOrientation":"印刷の方向","pageSize":"用紙サイズ","pageWidth":"幅","reportPart":{"description":"説明","displayName":"表示名","partProperties":"プロパティ","properties":{"category":"カテゴリ","defaultValue":"デフォルト値","description":"説明","displayName":"表示名","type":"種類"},"reportItemName":"レポートアイテム名","sizeMode":"サイズ設定"},"reportParts":"レポートパーツ","sizeType":"サイズ","source":"ソース","startPageNumber":"ページの開始番号","theme":"テーマ","themes":"テーマ"},"reportItem":{"accessibleDescription":"アクセシビリティ","actionType":"種類","bookmark":"ブックマークID","keepTogether":"1ページ内に収める","label":"ラベル","layerName":"レイヤー名","name":"名前","pageName":"ページ名","style":"スタイル","toolTip":"ツールチップ","visibility":"表示","zIndex":"Zインデックス"},"reportParameter":{"allowBlankValue":"空白の値を許可する","allowNullValue":"Null値を許可する","dataSetName":"データセットの名前","dataType":"データタイプ","hidden":"非表示","label":"ラベル","labelField":"ラベルフィールド","multiline":"複数行表示","multivalue":"複数の値を許可する","name":"名前","orderBy":"並べ替え","parameterFormat":"表示形式","parameterValues":"パラメータ値","prompt":"ダイアログの表示文字列","selectAllValue":"すべての値を選択","selectAllValue_help":{"text":"この値を指定した場合、複数値パラメータに「すべて選択」フラグが設定されている場合にパラメータ値に渡す値を決定します。"},"value":"値","valueField":"値フィールド","values":"値"},"reportSlicer":{"allowBlankValue":"空白の値を許可する","allowNullValue":"Null値を許可する","dataSetName":"データセットの名前","multivalue":"複数の値を許可する","name":"名前"},"richtext":{"canGrow":"テキストに合わせて拡張","markup":"種類","value":"値"},"roundingRadius":{"bottomLeft":"左下","bottomRight":"右下","default":"既定値","label":"角丸の半径","topLeft":"左上","topRight":"右上"},"sparkline":{"fillColor":"色","gradientEndColor":"グラデーションの終了色","gradientStyle":"グラデーションの種類","lineColor":"色","lineWidth":"太さ","markerColor":"マーカーの色","markerVisibility":"マーカーの表示","maximumColumnWidth":"最大幅","rangeFillColor":"色","rangeGradientEndColor":"グラデーションの終了色","rangeGradientStyle":"グラデーションの種類","rangeLowerBound":"下限","rangeUpperBound":"上限","rangeVisibility":"表示","seriesValue":"値","sparklineType":"種類"},"style":{"angle":"角度","backgroundAndBorders":"背景＆罫線","backgroundColor":"背景色","backgroundGradientEndColor":"グラデーションの終了色","backgroundGradientType":"グラデーションの種類","backgroundImage":"画像","border":"罫線","calendar":"カレンダー","characterSpacing":"文字ピッチ","color":"色","corner":"隅","direction":"方向","font":"フォント","format":"表示形式","headingLevel":"目次レベル","language":"言語","lineHeight":"行の高さ","lineSpacing":"行間","maxLevel":"最大レベル","minCondenseRate":"最小縮小率","minCondenseRate_help":{"text":"テキストの水平方向の最小縮小率をパーセントで指定します。10から100の間である必要があります。"},"numeralLanguage":"桁形式","numeralVariant":"桁形式の言語変種","padding":"パディング","shapeStyle":"形状","shrinkToFit":"縮小して全体表示","textAlign":"水平方向の整列","textDecoration":"文字飾り","textIndent":"インデント","textJustify":"均等割付","unicodeBiDi":"Unicodeの文字表記の方向","uprightInVerticalText":"縦中横","verticalAlign":"垂直方向の整列","wrapMode":"文字列の折り返し","writingMode":"文字表記の方向"},"subreport":{"inheritStyleSheet":"スタイルシートの継承","mergeTransactions":"トランザクションのマージ","reportName":"サブレポート","reportParameters":"レポートパラメータ","substituteThemeOnSubreport":"テーマの継承"},"table":{"autoWidth":"幅の自動調整","detailsDataCollectionName":"詳細データのコレクション名","detailsDataElementName":"詳細データの要素名","detailsDataElementOutput":"詳細データの要素出力","keepTogether":"1ページ内に収める","preventOrphanedFooter":"フッタ行のみのページの出力を防止","printAtBottom":"ページ下部に表示","repeatBlankRows":"空行出力","repeatOnNewPage":"すべてのページに表示"},"tablix":{"frozenColumns":"列の固定表示","frozenRows":"行の固定表示","groupsBeforeRowHeaders":"行ヘッダの前でグループ化","layoutDirection":"レイアウトの方向","repeatColumnHeaders":"列ヘッダを繰り返す","repeatRowHeaders":"行ヘッダを繰り返す"},"tablixBodyCell":{"autoMergeMode":"自動でマージ","autoMergeMode_help":{"text":"ひとつの列内で同じ内容を含んでいる2つ以上の連続したセルを結合するかどうかを示します。TextBoxセルに対してのみ機能します。"}},"tablixMember":{"groupEnabled":"有効","keepWithGroup":"グループとして維持","repeatOnNewPage":"全ページに繰り返し表示"},"textbox":{"canGrow":"内容に応じて拡大","canShrink":"内容に応じて縮小","initialToggleState":"トグルの初期状態","value":"値"},"tocLevel":{"displayFillCharacters":"充填文字の表示","displayPageNumber":"ページ番号の表示","fillCharacter":"充填文字","label":"ラベル"},"userSort":{"sortExpression":"並べ替えの式","sortExpressionScope":"並べ替えの式のスコープ","sortTarget":"並べ替えの対象"},"visibility":{"hidden":"非表示","toggleItem":"トグルスイッチにする項目"}}}},{"lng":"ja","ns":"propertyEditors-RPX","resources":{"image":{"textChange":"変更...","textPick":"開く..."},"statusWrapper":{"btnReset":"リセット","titleDefault":"","titleError":"無効です","titleInherited":"継承しています","titleModified":"変更されました"},"style":{"drillCaptionStyle":"スタイル","textEdit":"編集"}}},{"lng":"ja","ns":"propertyEditors","resources":{"boolean":{"textFalse":"いいえ","textTrue":"はい","textUndefined":"未定義"},"chartComplexEncodingFieldCollectionEditor":{"prefix":"Field"},"chartSimpleEncodingFieldCollectionEditor":{"captionHeader":"キャプション","keyHeader":"キー","valueHeader":"値"},"collection":{"btnAdd":"項目の追加","textEmpty":"コレクションが空です","textItemsCount_0":"{{count}} 個の項目","titleAdd":"項目の追加","titleClose":"閉じる","titleDelete":"削除","titleShowItems":"項目の表示"},"colorDropdown":{"btnColorPicker":"その他の色","btnPalettes":"パレット","btnWebColors":"Webカラー","headingStandard":"標準色","headingTheme":"テーマの色","labelHex":"カラーコード","labelHue":"色相","labelLightness":"明度","labelSaturation":"彩度","themeColors":{"titleBase":"{{colorKey}}","titleDarker25":"{{colorKey}} - 25% 暗い","titleDarker50":"{{colorKey}} - 50% 暗い","titleLighter25":"{{colorKey}} - 25% 明るい","titleLighter50":"{{colorKey}} - 50% 明るい","titleLighter75":"{{colorKey}} - 75% 明るい"}},"common":{"textEmpty":"","textExpression":"<式>","textMultipleValues":"<複数の値>","textNone":"<なし>","titleCollapse":"折りたたみ","titleExpand":"展開"},"dataSetFieldCollection":{"dataFieldHeader":"データフィールド","dataFieldPlaceholder":"<データフィールド>","fieldHeader":"フィールド名","fieldPlaceholder":"<名前>","valueHeader":"値","valuePlaceholder":"<値>"},"dataSetParameterCollection":{"nameHeader":"パラメータ名","namePlaceholder":"<名前>","valueHeader":"値","valuePlaceholder":"<値>"},"dataSetQuery":{"placeholder":"クエリをここに入力"},"dvcartLegendRangeOptionsCollection":{"titleHeader":"タイトル","titlePlaceholder":"<タイトル>","toHeader":"上限値"},"dvchartEncodingCollection":{"aggregate":{"prefix":"集計","propertiesTitle":"集計プロパティ"},"color":{"valuesName":"カラーに値の名前を表示"},"details":{"prefix":"詳細","propertiesTitle":"詳細プロパティ"},"fieldPlaceholder":"<キー>","text":{"prefix":"テキスト","propertiesTitle":"テキストプロパティ"},"value":{"prefix":"値","propertiesTitle":"値プロパティ"},"valuePlaceholder":"<値>"},"dvchartPlotTemplate":{"textSelect":"選択..."},"dvchartRuleProperties":{"headingTargetProperty":"対象のプロパティ","headingValueProperty":"値"},"dvchartTemplate":{"custom":"<カスタム>"},"format":{"$locale":"ja-JP","$localeCurrency":"JPY","currency":"通貨","custom":"(カスタム)","customFormatMask":"(###) ###-####","decimal":"10進数","default":"<デフォルト>","digitsLabel":"桁数:","fixedPoint":"固定小数点","fullDateShortTime":"完全な日付/短い形式の時刻","general":"全般","generalDateLongTime":"一般の日付/長い形式の時刻","generalDateShortTime":"一般の日付/短い形式の時刻","hexadecimal":"16進数","longDate":"長い形式の日付","longTime":"長い形式の時刻","monthDay":"m月d日","number":"数値","percent":"パーセント","scientific":"指数","shortDate":"短い形式の日付","shortTime":"短い形式の時刻","yearMonth":"y年m月"},"image":{"btnDatabase":"データベース","btnEmbedded":"埋め込み","btnShared":"共有","textLoad":"読み込む...","textNoDataFieldsFound":"データフィールドが見つかりません","textNoImagesFound":"画像が見つかりません","titleRemove":"\'{{name}}\'を削除する..."},"layerCollection":{"propertiesTitle":"レイヤーのプロパティ: {{layerName}}"},"mailMergeFieldsCollection":{"nameHeader":"フィールド名","namePlaceholder":"<名前>","valueHeader":"値","valuePlaceholder":"<値>"},"marginsSizes":{"custom":"(カスタム)"},"pageSize":{"custom":"(カスタム)"},"palette":{"customPaletteLabel":"<カスタム>","extraPalettesHeader":"テーマのカラーパレット","standardPalettesHeader":"通常のカラーパレット"},"parameterCollection":{"titleProperties":"パラメータのプロパティ"},"parameterValuesOrder":{"ascending":"昇順","descending":"降順"},"picker":{"btnDataVisualizer":"データの可視化...","btnExpression":"式","btnPickData":"データ...","btnReset":"リセット","headingParameters":"パラメータ","titleDataBinding":"データ設定","warnings":{"groupingByAggregate":"集計属性によるグループ化は行わないでください。","groupingIsDiscouraged":"この属性によるグループ化は行わないでください。","masterReportAttributes":"only attributes used in the master report are available for content reports"}},"reportParameter":{"labelFromQuery":"クエリから値を取得","labelNonQueried":"値を直接入力","labelSource":"ソース","placeholderEmpty":"","placeholderLabel":"ラベル","placeholderValue":"値"},"reportPartPropertiesCollection":{"propertiesTitle":"プロパティ: {{reportPartName}}"},"reportPartsCollection":{"propertiesTitle":"レポートパーツ: {{reportPartName}}"},"reports":{"textLoading":"読み込み中...","textLoadingError":"エラー {{status}}: {{statusText}}"},"simple":{"backgroundColor":{"label":"背景色","title":"背景色"},"borders":{"borderColor":{"label":"色","title":"色"},"borderStyle":{"label":"スタイル","title":"スタイル"},"borderWidth":{"label":"太さ","title":"太さ"},"borders":"罫線","sides":{"all":"すべて","bottom":"下","left":"左","reset":"リセット","right":"右","top":"上"}},"common":{"textExpressionCompact":"<式>"},"font":{"fontFamily":{"label":"フォント名","title":"フォント名"},"fontSize":{"label":"サイズ","title":"サイズ"},"fontStyle":{"label":"スタイル","title":"スタイル"},"fontWeight":{"label":"太さ","title":"太さ"},"textColor":{"label":"色","title":"色"},"textDecoration":{"label":"文字飾り","title":"文字飾り"}}},"subreport":{"parameter":"パラメータ","parameterNameHeader":"パラメータ名","parameterNamePlaceholder":"<名前>","parameterValueHeader":"値","parameterValuePlaceholder":"<値>"},"toggleState":{"textCollapsed":"折りたたみ","textExpanded":"展開"},"validationErrors":{"expression":{"disabledFields":"警告: この式で\'Fields\'トークンは使用できません (\'{{token}}\' {{positionInfo}})","disabledReportItems":"警告: この式で\'ReportItems\'トークンは使用できません (\'{{token}}\' {{positionInfo}})","errorPosition":"行：{{line}}, 列：{{column}}","parseError":"構文エラー：式をrdl構文に変換することはできません。","syntaxError":"構文エラー：予期しないトークンです。\'{{token}}\' {{positionInfo}}","unknown":"不明なエラー \'{{positionInfo}}\'","unknownField":"警告: 不明なフィールド名です。\'{{token}}\' {{positionInfo}}","unknownFunction":"警告: 不明な関数名です。\'{{token}}\' {{positionInfo}}","unknownParameter":"警告: 不明なパラメータ名です。\'{{token}}\' {{positionInfo}}","unknownReportItem":"警告: 不明なレポートアイテム名です。\'{{token}}\' {{positionInfo}}","unknownThemeImage":"警告: 不明なテーマ名です。\'\'{{token}}\'\' {{positionInfo}}","warning":"警告: 不明なトークンです。\'{{token}}\' {{positionInfo}}"}}}},{"lng":"ja","ns":"reportItems","resources":{"Page":"Page","Report":"レポート","bandedList":"BandedList","bandedListDetails":"Details","bandedListFooter":"Footer","bandedListGroup":"Group","bandedListHeader":"Header","barcode":"Barcode","body":"本文","bullet":"Bullet","checkbox":"CheckBox","container":"Container","contentPlaceholderText":"Textプロパティを設定して、レポート作成者に、ここに追加するコンテンツを説明します","contentplaceholder":"ContentPlaceholder","continuousSection":"レポートエリア","dashboard":"ダッシュボード","dashboardPageFooter":"フッタ","dashboardPageHeader":"ヘッダ","dashboardSection":"レポートエリア","dvchart":"Chart","dvchartAggregateEncoding":"集計のエンコーディング","dvchartAxis":"軸","dvchartCategoryEncoding":"カテゴリのエンコーディング","dvchartColorLegend":"凡例 - 凡例","dvchartDetailsEncoding":"詳細のエンコーディング","dvchartEncodingValue":"エンコード値","dvchartFooter":"フッタ","dvchartGlobalLegend":"グローバル凡例","dvchartHeader":"ヘッダ","dvchartLegend":"凡例","dvchartPlot":"プロット","dvchartPlotArea":"プロットエリア","dvchartPlotCustomLabel":"ゲージラベル","dvchartPlotPointer":"ゲージポインタ","dvchartShapeLegend":"凡例 - 形状","dvchartSizeLegend":"凡例 - サイズ","dvchartTextEncoding":"テキストエンコーディング","dvchartValueAggregateEncoding":"値の集計のエンコーディング","dvchartXAxis":"X 軸","dvchartYAxis":"Y 軸","fixedPageSection":"レポートエリア","formattedText":"FormattedText","image":"Image","inputField":"InputField","layer":"レイヤー","line":"Line","list":"List","listColumn":"列","listColumnsStacked":"列 2-{{columnCount}}","listRow":"行","listRowsStacked":"行 2-{{rowCount}}","overflowPlaceholder":"OverflowPlaceHolder","page":"ページ","pageFooter":"ページフッタ","pageHeader":"ページヘッダ","pageSection":"ページヘッダ／ページフッタ","partItem":"パーツアイテム","report":"レポート","reportPart":"レポートパーツ","reportPartProperty":"プロパティ","richtext":"MixedFormatText","shape":"Shape","sparkline":"Sparkline","subreport":"SubReport","table":"Table","tableColumn":"列","tableDetails":"詳細","tableFooter":"フッタ","tableGroup":"グループ","tableHeader":"ヘッダ","tableOfContents":"TableOfContents","tableOfContentsLevel":"レベル","tableRow":"行","tablix":"Tablix","tablixColumn":"列","tablixMember":"Tablixメンバ","tablixRow":"行","textbox":"TextBox","unknown":"不明なアイテム"}},{"lng":"ja","ns":"romLabels","resources":{"chart":"Chart","dvchart":"DV.Chart","matrix":"Matrix","table":"Table","tablix":"Tablix"}},{"lng":"ja","ns":"tablixWizard","resources":{"aggregates":{"avg":"Avg","count":"Count","max":"Max","min":"Min","none":"None","sum":"Sum"},"btnOrganization":"構成","btnStyling":"スタイル","btnTotals":"合計","displayAsOptions":{"default":"既定値","percentColumnGroupTotal":"列グループ比率(%)","percentGrandTotal":"総計比率(%)","percentParentColumnGroupTotal":"親列グループ比率(%)","percentParentRowGroupTotal":"親行グループ比率(%)","percentRowGroupTotal":"行グループ比率(%)"},"filters":{"headingGroupFilters":"グループフィルタ – {{groupLabel}}","headingTablixFilters":"Tablixフィルタ","textFilters":"フィルタ","titleEditGroupFilters":"グループフィルタの編集...","titleEditTablixFilters":"Tablixフィルタの編集..."},"formats":{"currency":"Currency","decimal":"Decimal","default":"Default","general":"General","number":"Number","percent":"Percent"},"headingDataSets":"データセット","headingLayoutDesign":"レイアウトデザイン","headingTablixWizard":"Tablixウィザード","labelCollapsedGroups":"グループの折りたたみ","labelExpandCollapse":"グループの展開／折りたたみ","labelFrozenColumns":"列の固定","labelFrozenRows":"行の固定","labelNone":"なし","labelShowTotalsBeforeGroup":"グループの前に合計を表示","labelSteppedRowGroups":"行グループ設定","labelSubTotalsForColumns":"列グループの小計","labelSubTotalsForRows":"行グループの小計","labelTotalsForColumns":"列グループの合計","labelTotalsForRows":"行グループの合計","labelUserSortEnabled":"並べ替え","makeTablix":{"textTotal":"合計","textValues":"値"},"placeholderNoField":"フィールドがありません","sortings":{"Ascending":"昇順","Descending":"降順","None":"なし"},"textAddDataSet":"はじめにデータセットを追加してください。","textAddValue":"Tablixを作成するには、少なくとも1つの値を追加してください。","textAsRows":"行として表示","textCannotEditInWizard":"Tablixの構造が複雑なため、ウィザードでは編集できません。 グループエディタを使用してください。","textColumns":"列グループ","textLayoutOptions":"レイアウトオプション","textNoDataSets":"レポートにデータセットがありません。","textNoValues":"指定された値がありません。","textOpenWizard":"Tablixウィザードを開く...","textRowGroups":"行グループ","textShowValuesAsRows":"行に値を表示","textSwap":"入替","textValues":"値","titleAggregate":"集計","titleDelete":"削除","titleDisplayAs":"表示","titleFormat":"書式","titleSorting":"並べ替え: {{sorting}}","titleSwapRowColumnGroups":"行／列グループの入れ替え","warning":{"btnQuit":"閉じる","btnRevert":"元に戻す","headingWarning":"警告","textChangedStructure":"Tablixの構造が変更されているためTablixウィザードで編集できません。","textConfirmReverting":"Tablixウィザードで編集可能な状態に戻してください。","textOtherwiseCannotEdit":"そうでない場合、Tablixウィザードで編集することはできません。"}}},{"lng":"ja","ns":"validationErrors","resources":{"enum":{"incorrect":"\'{{enumType}}\'型のEnum値は次のいずれかである必要があります: {{enumValues}}"},"errorPosition":"行 {{line}}, 列 {{column}}","expression":{"disabledFields":"警告: この式で\'Fields\'トークンは使用できません (\'{{token}}\' {{positionInfo}})","disabledReportItems":"警告: この式で\'ReportItems\'トークンは使用できません (\'{{token}}\' {{positionInfo}})","errorPosition":"行：{{line}}, 列：{{column}}","parseError":"構文エラー：式をrdl構文に変換することはできません。","syntaxError":"構文エラー：予期しないトークンです。\'{{token}}\' {{positionInfo}}","unknown":"不明なエラー \'{{positionInfo}}\'","unknownField":"警告: 不明なフィールド名です。\'{{token}}\' {{positionInfo}}","unknownFunction":"警告: 不明な関数名です。\'{{token}}\' {{positionInfo}}","unknownParameter":"警告: 不明なパラメータ名です。\'{{token}}\' {{positionInfo}}","unknownReportItem":"警告: 不明なレポートアイテム名です。\'{{token}}\' {{positionInfo}}","unknownThemeImage":"警告: 不明なテーマ名です。\'\'{{token}}\'\' {{positionInfo}}","warning":"警告: 不明なトークンです。\'{{token}}\' {{positionInfo}}"},"length":{"negative":"0以上の値を設定してください","tooLarge":"値は{{max}}以下である必要があります。","tooSmall":"値は{{min}}以上である必要があります。","unit":"有効な単位は\'cm\', \'mm\', \'in\', \'pt\', \'pc\'です"},"mime_type":{"incorrect":"画像のMIMEタイプは、{{wildcard}} と一致する必要があります。"},"number":{"empty":"値が空です","nan":"値は数字ではありません","outOfInterval":"値がインターバルの範囲外です","outOfRange":"値が範囲外です","tooLarge":"値は {{max}} より小さなものである必要があります","tooSmall":"値は {{min}} より大きなものである必要があります"},"pattern":"式が無効です。このプロパティのリテラル式の種類は、\'{{type}}\'. {{info}} である必要があります","unknown":{"unknown":"不明なタイプ \'{{type}}\'"}}},{"lng":"ja","ns":"warning","resources":{"embeddedImage":{"badFile":"選択されたファイル \'{{name}}\' は画像ではありません。","badFileType":"選択されたファイル \'{{name}}\' ({{type}}) はサポートされていません。","badImageFile":"選択された画像ファイル \'{{name}}\' は無効です。","badImageSize":"選択された画像ファイル \'{{name}}\' は制限値 {{limit}} MBを超えています。"},"margins":{"caption":"指定されたマージンは設定できません","info":"- \'{{name}}\' は {{value}} を超えることはできません","labels":{"bottom":"下","left":"左","right":"右","top":"上"}},"pageSize":{"caption":"指定されたページサイズは設定できません","info":"- \'{{name}}\' は {{value}} 未満にすることはできません","labels":{"height":"高さ","width":"幅"}}}}]')},11:function(e){e.exports=JSON.parse('[{"lng":"ja","ns":"arjswd","resources":{"about":{"applicationVersion":"製品バージョン: {{applicationVersion}}","close":"閉じる","coreVersion":"バージョン（Core）: {{coreVersion}}","title":"{{applicationTitle}}の製品情報"},"application-logo":"<path mask=\'url(#mask1)\' fill=\'#FFFFFF\' d=\'M0.861,12.454a11.637,11.637 0 1,0 23.274,0a11.637,11.637 0 1,0 -23.274,0M6.463,6.463c0.321-0.321,0.519-0.905,0.519-2.972c0-2.067-0.198-2.651-0.519-2.972l0,0h0V0.519l0,0C6.142,0.198,5.559,0,3.491,0C1.424,0,0.840,0.198,0.519,0.519l0,0v0.000l0,0l0,0C0.198,0.840,0,1.424,0,3.491c0,2.068,0.198,2.651,0.519,2.972l0,0l0,0l0,0l0,0c0.321,0.321,0.905,0.519,2.972,0.519C5.559,6.982,6.142,6.784,6.463,6.463L6.463,6.463L6.463,6.463L6.463,6.463L6.463,6.463z\'/><defs><mask id=\'mask1\'><path fill=\'#FFFFFF\' d=\'M0.000 0.000 H24.000 V24.000 H0.000 Z M18.422,10.923C16.362,7.077,14.339,5.207,12.237,5.207c-2.006,0-4.298,2.217-6.454,6.243c-1.618,3.020-1.189,4.642-0.544,5.471c0.572,0.735,1.589,1.174,2.718,1.174c2.001,0,3.029-0.805,4.314-1.847c0.265-0.215,0.772-0.588,0.959-0.703c0.793,1.649,1.991,2.567,3.830,2.567c1.822,0,3.273-0.931,3.273-2.368C20.334,14.819,20.000,13.868,18.422,10.923z M11.894,15.462c-1.532,1.243-2.579,1.654-3.738,1.654c-0.412,0-1.300-0.090-1.918-0.883c-0.521-0.668-0.972-2.008,0.412-4.592c2.017-3.765,3.790-5.448,5.551-5.448c1.863,0,3.350,1.318,5.305,4.967c0.864,1.612,0.205,2.073-0.791,2.159C14.738,13.490,13.353,14.277,11.894,15.462z M14.151,15.053c0,0,1.063-0.840,2.851-0.840c1.666,0,2.091,0.881,2.101,1.560c0.015,1.006-1.111,1.659-2.124,1.659C14.838,17.432,14.151,15.053,14.151,15.053zM12.201,6.192c1.137,0,2.020,1.140,3.482,3.624s1.329,3.301,0.181,3.621l1.490,0.349l0.998-1.416l-0.812-2.461L15.103,7.030l-2.182-1.161M5.826,3.973h-1.747c-0.010,0-0.018-0.001-0.023-0.001c0.000-0.001,0.001-0.003,0.001-0.005v-1.151l0.001-0.064c0.014-0.419,0.229-0.632,0.655-0.652l0.003-0.000c0.032-0.002,0.050-0.003,0.080-0.003h1.200c0.067,0,0.118-0.051,0.118-0.118V1.884c0-0.069-0.048-0.118-0.118-0.118h-1.216l-0.116,0.004c-0.617,0.016-1.026,0.392-1.044,0.958c-0.001,0.022-0.002,0.072-0.002,0.094v1.137c0,0.120,0,0.343,0.282,0.343h1.771c0.022,0.000,0.030,0.004,0.023,0.011v0.454c-0.002,0.007-0.003,0.014-0.003,0.020c-0.012,0.285-0.076,0.342-0.378,0.342h-1.584c-0.067,0-0.118,0.050-0.118,0.118v0.096c0,0.067,0.051,0.118,0.118,0.118h0.168c0.244,0,1.464-0.001,1.493-0.002c0.508-0.014,0.722-0.223,0.739-0.720c0.001-0.024,0.001-0.054,0.001-0.080v-0.342C6.131,4.232,6.131,3.973,5.826,3.973zM2.926,1.770h-0.210c-0.068,0-0.118,0.050-0.118,0.118v2.571c0.003,0.206-0.052,0.366-0.162,0.479c-0.118,0.120-0.300,0.184-0.529,0.186H1.533c-0.378-0.009-0.460-0.079-0.460-0.399l-0.000-0.559c0-0.068-0.050-0.118-0.118-0.118h-0.203c-0.069,0-0.118,0.050-0.118,0.123c0.000,0.001,0.002,0.036,0.002,0.068v0.061c-0.000,0.142-0.001,0.474,0,0.492c0.019,0.396,0.166,0.661,0.823,0.668c0.016,0.000,0.508,0.000,0.508,0.000c0.694,0,1.076-0.364,1.076-1.024L3.043,1.885C3.043,1.830,2.981,1.770,2.926,1.770z\'/></mask></defs>","application-title":"ActiveReportsJSデザイナ","collectionEditor":{"AddBtnTitle":"項目追加","addBtnText":"追加","closeBtnTitle":"閉じる","deleteBtnTitle":"削除","showBtnTitle":"表示","textEmpty":"コレクションが空です","textItems":"項目"},"common":{"copyright":"© MESCIUS inc. All rights reserved.","untitledReportName":"新規レポート"},"commonEditorProps":{"bool-false-label":"いいえ","bool-true-label":"はい","dataBinding":"データ設定","number-editor-decrease":"減少","number-editor-increase":"増加","placeholderEmpty":"<空>"},"dataPanel":{"addCmdTitle":"追加","commonValuesLabel":"共通の値","dataSetsLabel":"データセット","dataSourcesLabel":"データソース","editParameter":"パラメータの編集","noDataSets":"レポートのデータセットが未設定です","noDataSources":"レポートのデータソースが未設定です","noParameters":"レポートのパラメータが未設定です","parametersLabel":"パラメータ","smartSuggestions":"Smart Suggestions"},"dataSetDialog":{"alertTextClose":"閉じる","alertTextCollapse":"縮小","alertTextExpand":"展開","btnBack":"親データに戻る","btnExportTemplate":"出力...","btnValidate":"検証","confirmSaveInvalid":"変更内容は検証されていません。変更内容を保存しますか？","modeEditSubtitle":"データセットの編集","modeNewSubtitle":"新規データセット","parseQueryError":"不正なクエリ文字列"},"dataSetProps":{"category":{"csv":"CSV設定","fields":"フィールド","filters":"フィルタ","name":"名前","query":"クエリ"},"columnSeparatorComma":"カンマ","columnSeparatorSemicolon":"セミコロン","columnSeparatorSpace":" スペース","columnSeparatorTab":"タブ","fieldsDataField":"データフィールド","fieldsName":"フィールド名","fieldsValue":"値","fieldsValuePlaceholder":"式を入力してください","methodGet":"HTTP GET","methodPost":"HTTP POST","queryMode":{"JSON":"JSON","Text":"Text"},"title":{"calcFields":"計算フィールド","columnSeparator":"区切り文字","datasets":"ネストデータセット","endpoint":"URI/パス","fields":"データベースフィールド","headers":"ヘッダ","headingRow":"先頭行をヘッダとする","jpath":"JSONパス","mergeColumnSeparators":"重ねて表記でエスケープ（区切り文字）","mergeRowSeparators":"重ねて表記でエスケープ（改行コード）","method":"メソッド","postBody":"POST内容","queries":"パラメータ","queryType":"クエリ型","rowSeparator":"改行コード","startingRow":"開始行","value":"Value"}},"dataSourceDialog":{"btnConnectionString":"接続文字列","btnExportTemplate":"出力...","btnProperties":"プロパティ","parseErrorTitle":"接続文字列が不正です: {{errors}}","subtitle":"データソースの編集"},"dataSourceProps":{"btnLoadFromFile":"ファイルを選択","category":{"connectionString":"接続文字列","data":"データ","dataFormat":"種類","endpoint":"エンドポイント","jsonData":"JSONデータ","name":"名前","parameters":"パラメータ","sourceType":"形式"},"exprMenuDialog":"式...","exprMenuReset":"リセット","headersKey":"ヘッダ","headersValue":"値","providerCSV":"CSV","providerJSON":"JSON","queriesKey":"パラメータ","queriesValue":"値","sourceEmbedded":"埋め込み","sourceRemote":"外部ファイルまたはURL","title":{"headers":"HTTPヘッダ","queries":"クエリパラメータ"}},"dataTab":{"title":"データ"},"designer":{"dateFormats":["yyyy年M月d日","yyyy/MM/dd HH:mm:ss","yyyy/MM/dd","HH:mm:ss","tt hh:mm:ss","yyyy/MM/dd dddd","tt hh:mm","yyyyMMddHHmmss","MM/dd/yyyy h:mm:ss tt","M/d/yyyy H:mm:ss","M/d/yyyy","M/d/yy H:mm","M/d/yy","H:mm:ss","h:mm:ss tt","M/d/yyyy dddd","M/d/yy dddd","h:mm tt","dd/MM/yyyy HH:mm:ss","d/MM/yyyy H:mm:ss","dd/MM/yyyy","d/MM/yy H:mm","d/MM/yy","H\\" h \\"mm:ss","H\\" h \\"mm"],"defaultSettings":{"propertiesMode":"Advanced","snapToGridEnabled":"true","snapToGuidesEnabled":"true","units":"cm"},"fonts":["Arial","Arial Black","Comic Sans MS","Courier New","Geneva","Georgia","Helvetica","Impact","Lucida Console",["メイリオ","Meiryo"],"Meiryo UI","MingLiU","MingLiU-ExtB",["ＭＳ ゴシック","MS Gothic"],["ＭＳ 明朝","MS Mincho"],["ＭＳ Ｐゴシック","MS PGothic"],["ＭＳ Ｐ明朝","MS PMincho"],"MS Song","MS UI Gothic","NSimSun","Osaka","PMingLiU","PMingLiU-ExtB","SimSun","SimSun-ExtB","Song","Tahoma","Times New Roman","Trebuchet MS","Verdana",["游明朝","Yu Mincho"],["游ゴシック","Yu Gothic"]],"reportStyles":[],"reportTypes":["FPL","CPL","Pageless"]},"dialogs":{"cancel":"キャンセル","confirmSavingChanges":{"dontSaveLabel":"保存しない","saveLabel":"保存","wantSaveChanges":"{{reportName}}に変更を保存しますか?"},"saveChanges":"変更を保存"},"error":{"api":{"createReportFailed":"レポートの作成に失敗しました"},"cantLoadImages":"画像リストの読込に失敗しました","cantLoadReports":"レポートリストの読込に失敗しました","cantLoadThemes":"テーマリストの読込に失敗しました","customInitTemplate":{"loadFailed":"カスタムテンプレート \'{{id}}\' の読込に失敗しました: {{error}}"},"dataProviderNotSupported":"Data provider \\"{{provider}}\\" is not supported.","errorCode":"エラーコード: {{code}}","errorSerializingReport":"内部エラー: レポートのシリアライズに失敗しました","hasUnsavedChanges":"レポートを読み込めません デザイナに未保存の変更が存在します","internalError":"内部エラーが発生しました","invalid-report-displayName":"レポートの表示名が無効です","invalid-report-id":"無効なレポートIDです","invalidReportDef":"無効なレポート定義です","invalidReportType":"無効なレポート形式です","libraries":{"importFailed":"\\"{{path}}\\" ライブラリのインポートに失敗しました","loadingFailed":"ライブラリ \\"{{path}}\\" のロードに失敗しました","unknownLibrary":{"caption":"不明なライブラリに依存しています","text":"このレポートで使用されているライブラリ \\"{{libName}}\\" が見つかりません。"}},"noHostElement":"hostする要素が見つかりません。","noReportOpened":"現在開いているレポートはありません","report-id-is-not-specified":"レポートIDが指定されていません","reportLoadFailed":"レポート \\"{{reportName}}\\"は開くことができませんでした","theme":{"notFoundOrInvalid":"このレポートで使用されているテーマ \'{{theme}}\' が見つからないか、無効なコンテンツが含まれています。"},"unableGetReport":"Unable to get a report: {{message}}","unablePerformAction":"Unable to perform the action: {{message}}","unableSetReport":"Unable to set a report: {{message}}","unsupportedDocumentType":"内部エラー: サポートされていないドキュメント形式です"},"exportTemplateDialog":{"closeBtnText":"閉じる","title":"テンプレート出力"},"expressions":{"customCodeGroup":"カスタム関数"},"license":{"eval":{"badge":"トライアル期限 残り{days}日","badge-no_days":"トライアル版","banner":"メシウス株式会社 ActiveReportsJSデザイナ"},"expired":{"badge":"トライアル期限切れ","banner":"メシウス株式会社 ActiveReportsJSデザイナ"},"invalid":{"badge":"無効なライセンスキー","banner":"無効なライセンスキーです。\\nライセンスキーを再確認し、有効なライセンスを設定してください。"},"no-license":{"badge":"ライセンスキーが見つかりません","banner":"ライセンスキーが見つかりません。\\nActiveReportsJSデザイナを使用するには、有効なライセンスを設定してください。"}},"menu":{"about":"製品情報"},"nameTemplates":{"dataSetCalcFieldValue":"計算フィールド","dataSetFieldValue":"フィールド","reportParameter":"レポートパラメータ"},"notificationPanel":{"collapse":"折りたたみ","dismiss":"無視","dismissAll":"すべて無視","expand":"展開","oneError":"エラー","oneNotification":"個の通知","oneWarning":"警告","showDetails":"詳細表示"},"propertiesTab":{"title":"プロパティ"},"queryValuesDialog":{"btnSave":"保存して実行","title":"パラメータ値"},"sideBar":{"collapse":"折りたたみ","expand":"展開"},"warning":{"unsavedChanges":"このページのレポートには保存されていない変更があります"}}},{"ns":"core","lng":"ja","resources":{"errors":{"csvdataprovider":{"header-parse":"列ヘッダを解析できませんでした: \\"{{headerValue}}\\""},"dataprovider":{"commandtext-invalid":"無効なデータプロバイダコマンドです: {{commandText}}","connectstring-invalid":"接続文字列が無効です: {{connectString}}","no-data":"データプロバイダ設定が無効です ソースまたはデータが指定されていません"},"jsondataprovider":{"no-data":"接続文字列が無効です \\"jsondata\\" または \\"jsondoc\\" を指定してください"},"fetch-failed":"データを読み込むことができませんでした \\"{{uri}}\\": {{responseStatus}} {{responseText}}.","data-processing":"データプロセスでエラーが発生しました: {{details}}."}}}]')},43:function(e,a,t){"use strict";t.r(a);var r=t(10),o=t(11),i={wdCore:r,arjswd:o};if(window.arjsDesigner=window.arjsDesigner||{},window.arjsDesigner.addLocalization)window.arjsDesigner.addLocalization("ja",i);else{var n=window.arjsDesigner.loadLocalizations;window.arjsDesigner.loadLocalizations=function(){n&&n(),window.arjsDesigner.addLocalization("ja",i)}}}});