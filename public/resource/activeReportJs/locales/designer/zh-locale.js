!function(e){var o={};function t(r){if(o[r])return o[r].exports;var l=o[r]={i:r,l:!1,exports:{}};return e[r].call(l.exports,l,l.exports,t),l.l=!0,l.exports}t.m=e,t.c=o,t.d=function(e,o,r){t.o(e,o)||Object.defineProperty(e,o,{enumerable:!0,get:r})},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,o){if(1&o&&(e=t(e)),8&o)return e;if(4&o&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(t.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&o&&"string"!=typeof e)for(var l in e)t.d(r,l,function(o){return e[o]}.bind(null,l));return r},t.n=function(e){var o=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(o,"a",o),o},t.o=function(e,o){return Object.prototype.hasOwnProperty.call(e,o)},t.p="",t(t.s=46)}([,,,,,,,,function(e){e.exports=JSON.parse('[{"lng":"zh","ns":"arjswd","resources":{"about":{"applicationVersion":"应用程序版本: {{applicationVersion}}","close":"关闭","coreVersion":"核心版本: {{coreVersion}}","title":"关于{{applicationTitle}}"},"application-logo":"<path mask=\'url(#mask1)\' fill=\'#FFFFFF\' d=\'M0.861,12.454a11.637,11.637 0 1,0 23.274,0a11.637,11.637 0 1,0 -23.274,0M6.463,6.463c0.321-0.321,0.519-0.905,0.519-2.972c0-2.067-0.198-2.651-0.519-2.972l0,0h0V0.519l0,0C6.142,0.198,5.559,0,3.491,0C1.424,0,0.840,0.198,0.519,0.519l0,0v0.000l0,0l0,0C0.198,0.840,0,1.424,0,3.491c0,2.068,0.198,2.651,0.519,2.972l0,0l0,0l0,0l0,0c0.321,0.321,0.905,0.519,2.972,0.519C5.559,6.982,6.142,6.784,6.463,6.463L6.463,6.463L6.463,6.463L6.463,6.463L6.463,6.463z\'/><defs><mask id=\'mask1\'><path fill=\'#FFFFFF\' d=\'M0.000 0.000 H24.000 V24.000 H0.000 Z M18.422,10.923C16.362,7.077,14.339,5.207,12.237,5.207c-2.006,0-4.298,2.217-6.454,6.243c-1.618,3.020-1.189,4.642-0.544,5.471c0.572,0.735,1.589,1.174,2.718,1.174c2.001,0,3.029-0.805,4.314-1.847c0.265-0.215,0.772-0.588,0.959-0.703c0.793,1.649,1.991,2.567,3.830,2.567c1.822,0,3.273-0.931,3.273-2.368C20.334,14.819,20.000,13.868,18.422,10.923z M11.894,15.462c-1.532,1.243-2.579,1.654-3.738,1.654c-0.412,0-1.300-0.090-1.918-0.883c-0.521-0.668-0.972-2.008,0.412-4.592c2.017-3.765,3.790-5.448,5.551-5.448c1.863,0,3.350,1.318,5.305,4.967c0.864,1.612,0.205,2.073-0.791,2.159C14.738,13.490,13.353,14.277,11.894,15.462z M14.151,15.053c0,0,1.063-0.840,2.851-0.840c1.666,0,2.091,0.881,2.101,1.560c0.015,1.006-1.111,1.659-2.124,1.659C14.838,17.432,14.151,15.053,14.151,15.053zM12.201,6.192c1.137,0,2.020,1.140,3.482,3.624s1.329,3.301,0.181,3.621l1.490,0.349l0.998-1.416l-0.812-2.461L15.103,7.030l-2.182-1.161M5.826,3.973h-1.747c-0.010,0-0.018-0.001-0.023-0.001c0.000-0.001,0.001-0.003,0.001-0.005v-1.151l0.001-0.064c0.014-0.419,0.229-0.632,0.655-0.652l0.003-0.000c0.032-0.002,0.050-0.003,0.080-0.003h1.200c0.067,0,0.118-0.051,0.118-0.118V1.884c0-0.069-0.048-0.118-0.118-0.118h-1.216l-0.116,0.004c-0.617,0.016-1.026,0.392-1.044,0.958c-0.001,0.022-0.002,0.072-0.002,0.094v1.137c0,0.120,0,0.343,0.282,0.343h1.771c0.022,0.000,0.030,0.004,0.023,0.011v0.454c-0.002,0.007-0.003,0.014-0.003,0.020c-0.012,0.285-0.076,0.342-0.378,0.342h-1.584c-0.067,0-0.118,0.050-0.118,0.118v0.096c0,0.067,0.051,0.118,0.118,0.118h0.168c0.244,0,1.464-0.001,1.493-0.002c0.508-0.014,0.722-0.223,0.739-0.720c0.001-0.024,0.001-0.054,0.001-0.080v-0.342C6.131,4.232,6.131,3.973,5.826,3.973zM2.926,1.770h-0.210c-0.068,0-0.118,0.050-0.118,0.118v2.571c0.003,0.206-0.052,0.366-0.162,0.479c-0.118,0.120-0.300,0.184-0.529,0.186H1.533c-0.378-0.009-0.460-0.079-0.460-0.399l-0.000-0.559c0-0.068-0.050-0.118-0.118-0.118h-0.203c-0.069,0-0.118,0.050-0.118,0.123c0.000,0.001,0.002,0.036,0.002,0.068v0.061c-0.000,0.142-0.001,0.474,0,0.492c0.019,0.396,0.166,0.661,0.823,0.668c0.016,0.000,0.508,0.000,0.508,0.000c0.694,0,1.076-0.364,1.076-1.024L3.043,1.885C3.043,1.830,2.981,1.770,2.926,1.770z\'/></mask></defs>","application-title":"ActiveReportsJS 设计器","collectionEditor":{"AddBtnTitle":"添加新项","addBtnText":"添加","closeBtnTitle":"关闭","deleteBtnTitle":"删除","showBtnTitle":"分享","textEmpty":"集合为空","textItems":"数据项"},"common":{"copyright":"©️ Grapecity China, Inc. All rights reserved.","untitledReportName":"未命名"},"commonEditorProps":{"bool-false-label":"否","bool-true-label":"是","dataBinding":"数据绑定","number-editor-decrease":"减少","number-editor-increase":"增加","placeholderEmpty":"<空>"},"dataPanel":{"addCmdTitle":"添加","commonValuesLabel":"内置数据","dataSetsLabel":"数据集","dataSourcesLabel":"数据源","editParameter":"编辑参数","noDataSets":"报表未添加数据集","noDataSources":"报表未添加数据源","noParameters":"报表未添加参数","parametersLabel":"报表参数","smartSuggestions":"Smart Suggestions"},"dataSetDialog":{"alertTextClose":"关闭","alertTextCollapse":"折叠","alertTextExpand":"展开","btnBack":"返回","btnExportTemplate":"导出...","btnValidate":"验证","confirmSaveInvalid":"修改未验证，是否要保存修改？","modeEditSubtitle":"编辑数据集","modeNewSubtitle":"新建数据集","parseQueryError":"无效的查询语句"},"dataSetProps":{"category":{"csv":"CSV设置","fields":"字段","filters":"过滤器","name":"名称","query":"查询"},"columnSeparatorComma":"逗号","columnSeparatorSemicolon":"分号","columnSeparatorSpace":"空格","columnSeparatorTab":"制表符","fieldsDataField":"数据字段","fieldsName":"字段名称","fieldsValue":"字段值","fieldsValuePlaceholder":"请输入表达式","methodGet":"HTTP GET","methodPost":"HTTP POST","queryMode":{"JSON":"JSON","Text":"Text"},"title":{"calcFields":"计算字段","columnSeparator":"列分离","datasets":"嵌套的数据集","endpoint":"Uri/路径","fields":"查询字段","headers":"请求头","headingRow":"标题行","jpath":"Json 查询","mergeColumnSeparators":"合并列分隔符","mergeRowSeparators":"合并行分隔符","method":"方法","postBody":"Post body","queries":"查询参数","queryType":"类型","rowSeparator":"行分隔符","startingRow":"起始行","value":"Value"}},"dataSourceDialog":{"btnConnectionString":"连接字符串","btnExportTemplate":"导出...","btnProperties":"属性","parseErrorTitle":"无效的连接字符串: {{errors}}","subtitle":"编辑数据源"},"dataSourceProps":{"btnLoadFromFile":"打开文件","category":{"connectionString":"连接字符串","data":"数据","dataFormat":"数据格式","endpoint":"JSON URL","jsonData":"JSON 数据","name":"名称","parameters":"查询参数","sourceType":"类型"},"exprMenuDialog":"表达式...","exprMenuReset":"重置","headersKey":"请求头","headersValue":"值","providerCSV":"CSV","providerJSON":"JSON","queriesKey":"参数","queriesValue":"值","sourceEmbedded":"内嵌","sourceRemote":"远程","title":{"headers":"HTTP请求头","queries":"查询参数"}},"dataTab":{"title":"数据绑定"},"designer":{"dateFormats":[],"defaultSettings":{"propertiesMode":"Basic","snapToGridEnabled":"true","snapToGuidesEnabled":"true","units":"cm"},"fonts":[["微软雅黑","MicrosoftYaHei"],"微软雅黑 Light","等线","等线 Light","宋体","仿宋","新宋体","幼圆","楷体","隶书","黑体","Arial","Arial Black","Comic Sans MS","Courier New","Geneva","Georgia","Helvetica","Impact","Lucida Console","Meiryo","Meiryo UI","MingLiU","MingLiU-ExtB","MS Gothic","MS Mincho","MS PGothic","MS PMincho","MS Song","MS UI Gothic","NSimSun","Osaka","PMingLiU","PMingLiU-ExtB","SimSun","SimSun-ExtB","Song","Tahoma","Times New Roman","Trebuchet MS","Verdana","Yu Gothic"],"reportStyles":[],"reportTypes":["CPL","FPL","Pageless"]},"dialogs":{"cancel":"取消","confirmSavingChanges":{"dontSaveLabel":"不保存","saveLabel":"保存","wantSaveChanges":"是否保存你对报表 [{{reportName}}] 所做的修改?"},"saveChanges":"保存"},"error":{"api":{"createReportFailed":"创建报表失败"},"cantLoadImages":"无法加载图像列表","cantLoadReports":"无法加载报表列表","cantLoadThemes":"无法加载主题列表","customInitTemplate":{"loadFailed":"加载指定模板出错\'{{id}}\': {{error}}"},"dataProviderNotSupported":"Data provider \\"{{provider}}\\" is not supported.","errorCode":"错误代码: {{code}}","errorSerializingReport":"内部错误：无法序列化报告","hasUnsavedChanges":"无法加载报告。 设计器在报告中存在未保存的更改。","internalError":"内部错误","invalid-report-displayName":"无效的报表显示名称","invalid-report-id":"无效的报表ID","invalidReportDef":"无效的报表定义","invalidReportType":"无效的报表类型","libraries":{"importFailed":"模版\\"{{path}}\\"导入失败","loadingFailed":"库\\"{{path}}\\"加载失败","unknownLibrary":{"caption":"报表依赖于未知的库","text":"找不到此报告中使用的库“{{libName}}”"}},"noHostElement":"无法找到宿主元素","noReportOpened":"目前没有打开报告","report-id-is-not-specified":"未指定报表ID","reportLoadFailed":"无法打开报告 \\"{{reportName}}\\"。","theme":{"notFoundOrInvalid":"报表所设置的主题 \'{{theme}}\' 未能找到，或者是主题样式内部格式不正确。"},"unableGetReport":"Unable to get a report: {{message}}","unablePerformAction":"Unable to perform the action: {{message}}","unableSetReport":"Unable to set a report: {{message}}","unsupportedDocumentType":"内部错误：不支持的文档类型"},"exportTemplateDialog":{"closeBtnText":"关闭","title":"导出模板"},"expressions":{"customCodeGroup":"自定义函数"},"license":{"eval":{"badge":"剩余评估时间为 {days} 天","badge-no_days":"试用版本","banner":"本版本为\\n 西安葡萄城 ActiveReportsJS 报表控件试用版"},"expired":{"badge":"试用版已过期","banner":"本版本为\\n 西安葡萄城 ActiveReportsJS 报表控件试用版"},"invalid":{"badge":"无效的授权码信息","banner":"无效的授权码信息.\\n请仔细检查项目中的授权码信息，并添加有效的授权码。\\n如果需要协助，请致电：400-657-6008"},"no-license":{"badge":"未发现授权码信息","banner":"未找到授权码信息.\\n您需要有效的授权码才能运行 ActiveReportsJS\\n可申请试用授权码，帮助您评估\\n如果您已购买正式授权,请检查您的邮箱 .\\n如果需要协助，请致电：400-657-6008"}},"menu":{"about":"关于我们"},"nameTemplates":{"dataSetCalcFieldValue":"计算字段","dataSetFieldValue":"查询字段","reportParameter":"报表参数"},"notificationPanel":{"collapse":"折叠","dismiss":"忽略","dismissAll":"全部忽略","expand":"展开","oneError":"错误","oneNotification":"单条提示","oneWarning":"警告","showDetails":"显示详情"},"propertiesTab":{"title":"属性设置"},"queryValuesDialog":{"btnSave":"保存并运行","title":"参数值"},"sideBar":{"collapse":"折叠","expand":"展开"},"warning":{"unsavedChanges":"该报表有尚未保存的修改，请确定是否关闭画面！"}}},{"ns":"core","lng":"zh","resources":{"errors":{"csvdataprovider":{"header-parse":"无法解析列标题 \\"{{headerValue}}\\""},"dataprovider":{"commandtext-invalid":"数据提供程序 CommandText 无效：{{commandText}}。","connectstring-invalid":"数据源连接字符串无效：{{connectString}}。","no-data":"数据来源设置无效，未指定数据源或数据"},"jsondataprovider":{"no-data":"无效的连接字符串。 必须指定 \\"jsondata\\" 或 \\"jsondoc\\" "},"fetch-failed":"无法从指定 \\"{{uri}}\\": {{responseStatus}} {{responseText}}加载数据","data-processing":"数据处理错误{{details}}."}}}]')},function(e){e.exports=JSON.parse('[{"id":"a63e5c5c-a5bb-418f-8407-d9277ef4f5c8","name":"默认样式","content":{}},{"id":"18f5c8cf-9e91-4bb3-92fc-fa5258d4b989","name":"深色","content":{"BackgroundColor":"=Theme.Colors!Accent1","Border":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"}}},{"id":"cce02d12-70a7-418b-94d1-51a21d5337cb","name":"浅色","content":{"BackgroundColor":"=Theme.Colors!Light1","Border":{"Color":"=Theme.Colors!Dark1","Width":"1pt","Style":"Solid"}}}]')},,,function(e){e.exports=JSON.parse('[{"lng":"zh","ns":"adorners","resources":{"bandedList":{"detailsLabel":"<明细>","groupsLabel":"分组"},"bullet":{"targetValue":"目标数据","value":"实际数据"},"chart":{"categoryEncoding":{"fieldStub":"分类 {{index}}"},"categoryFields":"分类","colorEncoding":{"fieldStub":"颜色 {{index}}","title":"颜色"},"dataFields":"数据字段","detailEncoding":{"fieldStub":"明细 {{index}}","title":"明细"},"encodingFields":"详细设置","gaugeLabel":{"fieldStub":"标签 {{index}}"},"gaugePointer":{"fieldStub":"指针 {{index}}"},"gaugeRanges":"仪表盘","labels":"标签","multiValueField":"{{firstFieldValue}}, ...","shapeEncoding":{"fieldStub":"形状 {{index}}","title":"形状"},"sizeEncoding":{"fieldStub":"大小 {{index}}","title":"大小"},"textEncoding":{"title":"文本"},"valueEncoding":{"fieldStub":"数值 {{index}}"}},"common":{"dropFieldsAndValues":"拖放字段进行绑定"},"formattedText":{"previewError":"你设置的 Html 属性在进行转化时失败，请检查该设置是否正确"},"shapeRoundingTip":{"multiCornerMode":"按“Alt”键切换到“单角”模式","singleCornerMode":"按“Alt”键切换到“多角”模式"},"sparkline":{"groupingExpressions":"分组条件","seriesValue":"系列数据"},"table":{"detailsGroupLabel":"<明细分组>","groupsLabel":"表格分组","newGroupLabel":"<新建分组>"},"tableOfContents":{"addItem":"添加项目"}}},{"lng":"zh","ns":"captions","resources":{"barcodeUnsupportedSymbology":"[{{symbology}}] \'{{itemName}}\' 无法在设计器中显示，仅支持在预览模式下显示","basicSupportReportItemInfo":"{{itemLabel}} \'{{itemName}}\' 在设计器中支持有限制","dvchartAxis":{"category":"分类","value":"数值"},"dvchartChartTitle":"图表标题","dvchartColor":"颜色 [{{index}}]","dvchartDetails":"明细 [{{index}}]","dvchartShape":"形状 [{{index}}]","overflowPlaceholder":"{{overflowedItemName}} 内容溢出占位符"}},{"lng":"zh","ns":"chartWizard","resources":{"buttons":{"btnBack":"上一步","btnCancel":"取消","btnFinish":"完成","btnNext":"下一步"},"customization":{"labels":{"area":"绘图区","axisX":"横轴","axisY":"纵轴","footer":"底部标题","header":"图表标题","legend":"图例","plot":"绘图区"}},"labelSelectDataSet":"选择数据","labelSelectPalette":"选择调色板","settings":{"categories":{"category":"分类字段","subcategory":"子类字段","values":"数值字段"},"labels":{"aggregate":"聚合方式","field":"字段","fieldClose":"收盘字段","fieldEnd":"结束字段","fieldHigh":"盘高字段","fieldLow":"盘低字段","fieldOpen":"开盘字段","fieldSize":"大小字段","fieldStart":"开始字段","fieldX":"分类字段","fieldY":"数据字段","fields":"字段","gaugeLabel":"仪表盘标签","gaugeRanges":"仪表盘","group":"分组方式","lower":"低值","pointer":"仪表盘 指针","sortDirection":"排序方向","upper":"高值"}},"templates":{"types":{"area":"面积图","bar":"条形图","bubble":"气泡图","candlestick":"开盘-盘高-盘低-收盘图","column":"柱状图","doughnut":"圆环图","funnel":"漏斗图","gantt":"甘特图","gauge":"仪表盘","highLowClose":"盘高-盘低-收盘图","highLowOpenClose":"盘高-盘低-开盘-收盘图","line":"折线图","pie":"饼图","polarBar":"极坐标条形图","polarColumn":"极坐标柱状图","pyramid":"锥形图","radarArea":"雷达面积图","radarBubble":"雷达气泡图","radarLine":"雷达折线图","radarScatter":"雷达散点图","rangeArea":"区间面积图","rangeBar":"区间条形图","rangeColumn":"区间柱状图","scatter":"散点图"}},"textAdvancedCustomization":"高级设置","textCustom":"自定义","textEmpty":"空","textPreviewStep":"预览","textTypeStep":"数据和类型","titleChartWizard":"图表创建向导","titlePreview":"预览","titleSelectType":"选择数据和图表类型","titleSettings":"设置"}},{"lng":"zh","ns":"common","resources":{"btnCancel":"取消","btnOk":"确定","btnSave":"保存","textCollapse":"折疊","textDelete":"删除","textExpand":"展開","textOpen":"打开...","units":{"cm":{"textFullName":"厘米","textShortName":"cm"},"in":{"textFullName":"英寸","textShortName":"in"}}}},{"lng":"zh","ns":"components-RPX","resources":{"appBar":{"btnScript":"脚本"},"dataTab":{"titleDeleteDataSource":"删除数据源","titleEditDataSource":"编辑数据源...","titleMoveParameterDown":"下移","titleMoveParameterUp":"上移"},"menu":{"btnReportExplorer":"元素管理"},"propertyGrid":{"placeholderSearchBox":"请输入属性名称...","textAlphabetical":" 按字母顺序","textCategorized":"分类","textMultipleTypes":"<多种类型>","textSort":"类别"},"scriptEditor":{"placeholder":"输入 {{ScriptLanguage}} 编码"},"stylesTab":{"textBasedOn":"基于 \\"{{parentName}}\\"","textRootStyle":"没有父样式","titleAddStyle":"添加样式基于 \\"{{parentName}}\\"","titleDeleteStyle":"删除样式"},"toolbar":{"home":{"backColor":"背景色","fontFamily":"字体系列","fontSize":"字体大小","fontStyle":"字体样式斜体","fontWeight":"字体粗细","foreColor":"前景色","textDecoration":"文字修饰下划线","titleAlignCenter":"文本居中","titleAlignJustify":"文本对齐","titleAlignLeft":"文本左对齐","titleAlignRight":"文本右对齐","verticalAlignBottom":"垂直底部对齐","verticalAlignMiddle":"垂直居中","verticalAlignTop":"垂直顶部对齐"},"script":{"events":{"report":{"textDataInitialize":"DataInitialize","textFetchData":"FetchData","textNoData":"NoData","textPageEnd":"PageEnd","textPageStart":"PageStart","textReportEnd":"ReportEnd","textReportStart":"ReportStart"},"section":{"textAfterPrint":"AfterPrint","textBeforePrint":"BeforePrint","textFormat":"Format"}},"textEvent":"Event","textObject":"Object","titleEvent":"Event","titleObject":"Object"}}}},{"lng":"zh","ns":"components","resources":{"appBar":{"btnFile":"文件","btnHome":"开始","btnInsert":"插入","btnParameters":"查询面板","btnPreview":"预览","btnSaveAs":"另存为","textUnsavedChanges":"未保存的修改","titleNew":"新建","titleOpen":"打开","titleRedo":"恢复","titleSave":"保存","titleUndo":"撤消"},"chartPaletteDropdown":{"headingExtraPalettes":"主题色","headingStandardPalettes":"标准色"},"dataFieldPickerDropdown":{"semantic":{"noMatchingAttributesRelationsFound":"未找到匹配的内容","searchPlaceholder":"输入名称进行查找..."}},"dataPanel":{"commonValues":{"currentDateTime":"当前日期和时间","pageNM":"第 N 页、共 M 页","pageNMCumulative":"第 N 页、共 M 页 (全局范围)","pageNMSection":"第 N 页、共 M 页 (局部范围)","pageNofMLabel":"\\"第 \\" & {{pageNumber}} & \\" 页、共 \\" & {{totalPages}} & \\" 页\\"","pageNumber":"页号","pageNumberCumulative":"页号 (全局范围)","pageNumberSection":"页号 (局部范围)","reportFolder":"报表文件夹","reportName":"报表名称","totalPages":"总页数","totalPagesCumulative":"总页数 (全局范围)","totalPagesSection":"总页数 (局部范围)","userContext":"用户上下文","userId":"用户编号","userLanguage":"用户语言"},"dataSets":{"placeholderEnterFieldName":"输入字段名称进行查找...","semantic":{"editDataSet":"编辑数据集...","loading":"加载中...","noMatchingAttributesRelationsFound":"未找到匹配的属性和关联关系","searchPlaceholder":"输入属性或关系名称进行查找..."},"textNoMatchingFieldsFound":"未找到匹配的字段"},"fieldVariations":{"Date":[{"format":"=Year({fieldExpression}) & \\"年\\"","label":"年"},{"format":"={fieldExpression}.ToString(\\"yyyy-MM\\")","label":"年-月(yyyy-MM)"},{"format":"={fieldExpression}.ToString(\\"yyyy年MM月\\")","label":"年-月(yyyy年MM月)"},{"format":"={fieldExpression}.ToString(\\"yyyy-MM-dd\\")","label":"年-月-日(yyyy-MM-dd)"},{"format":"={fieldExpression}.ToString(\\"yyyy年MM月dd日\\")","label":"年-月-日(yyyy年MM月dd日)"},{"format":"=Year({fieldExpression}) & \\"-Q\\" & DatePart(\\"q\\", {fieldExpression})","label":"年-季度"},{"format":"=Year({fieldExpression}) & \\"年\\" & DatePart(\\"ww\\", {fieldExpression}) & \\"周\\"","label":"年-周"},{"format":"=Choose(DatePart(\\"q\\", {fieldExpression}), \\"一\\",  \\"二\\", \\"三\\", \\"四\\") & \\"季度\\"","label":"季度"},{"format":"=Month({fieldExpression}) & \\"月\\"","label":"月(8月)"},{"format":"=MonthName(Month({fieldExpression}))","label":"月(八月)"},{"format":"=\\"第\\" & DatePart(\\"ww\\", {fieldExpression}) & \\"周\\"","label":"周"},{"format":"=Day({fieldExpression})","label":"天"},{"format":"={fieldExpression}.ToString(\\"dddd\\")","label":"星期"}],"DateTime":[{"format":"=Year({fieldExpression}) & \\"年\\"","label":"年"},{"format":"={fieldExpression}.ToString(\\"yyyy-MM\\")","label":"年-月(yyyy-MM)"},{"format":"={fieldExpression}.ToString(\\"yyyy年MM月\\")","label":"年-月(yyyy年MM月)"},{"format":"={fieldExpression}.ToString(\\"yyyy-MM-dd\\")","label":"年-月-日(yyyy-MM-dd)"},{"format":"={fieldExpression}.ToString(\\"yyyy年MM月dd日\\")","label":"年-月-日(yyyy年MM月dd日)"},{"format":"=Year({fieldExpression}) & \\"-Q\\" & DatePart(\\"q\\", {fieldExpression})","label":"年-季度"},{"format":"=Year({fieldExpression}) & \\"年\\" & DatePart(\\"ww\\", {fieldExpression}) & \\"周\\"","label":"年-周"},{"format":"=Choose(DatePart(\\"q\\", {fieldExpression}), \\"一\\",  \\"二\\", \\"三\\", \\"四\\") & \\"季度\\"","label":"季度"},{"format":"=Month({fieldExpression}) & \\"月\\"","label":"月(8月)"},{"format":"=MonthName(Month({fieldExpression}))","label":"月(八月)"},{"format":"=\\"第\\" & DatePart(\\"ww\\", {fieldExpression}) & \\"周\\"","label":"周"},{"format":"=Day({fieldExpression})","label":"天"},{"format":"={fieldExpression}.ToString(\\"dddd\\")","label":"星期"}]},"headingEditParameter":"编辑参数","semantic":{"textAttributesCount_0":"{{count}} 个属性","textNoAttributes":"没有属性","textNoRelations":"没有关系","textRelationsCount_0":"{{count}} 个关系"},"textBasedOnDataSource":"数据源：{{dataSourceName}}","textFieldsCount_0":"{{count}} 个字段","textModelVersion":"版本: {{version}}","textSharedReference":"共享参考","titleAddDataSet":"请添加数据集...","titleEditDataSet":"编辑数据集...","titleEditDataSource":"编辑数据源...","titleMore":"更多...","titleMoveParameterDown":"下移","titleMoveParameterUp":"上移","titleSelectFields":"请选择字段"},"fieldPicker":{"placeholderEnterFieldName":"输入字段名称进行查找..."},"layerList":{"btnAddLayer":"添加层","titleDefaultLayerCannotBeDeleted":"不能删除默认层","titleDeleteLayer":"删除层","titleEditLayer":"编辑图层","titleHideLayer":"隐藏层","titleLockLayer":"锁定层","titleShowLayer":"显示层","titleUnlockLayer":"解锁层"},"libraries":{"textEmpty":"空","textError":"错误","textNoLibraries":"没有可用的库","textNoReportParts":"没有组件资源在资源库"},"menu":{"btnBack":"上一步","btnClose":"关闭","btnGroupEditor":"分组管理","btnLayerList":"报表层","btnLibrariesList":"报表组件库","btnReportExplorer":"元素管理","titleBack":"上一步","titlePin":"固定"},"notifications":{"btnDismiss":"忽略","btnDismissAll":"全部忽略","headingError_0":"{{count}} 错误","headingNotification_0":"{{count}} 单条提示","headingWarning_0":"{{count}} 警告","titleNotifications":"通知"},"propertyGrid":{"options":{"categories":"分类","collapse":"折叠所有","expand":"展开所有","hideAdvancedProperty":"隐藏高级属性","showAdvancedProperty":"显示高级属性"},"placeholderSearchBox":"输入属性名称进行查找...","textMultipleTypes":"<多种类型>","textReportItems":"<报表元素>"},"statusBar":{"btnPropertiesMode":"属性设置模式","btnShowAdvancedProperties":"高级属性设置模式","btnShowBasicProperties":"基础属性设置模式","common":{"textDisabled":"禁用","textEnabled":"启用"},"snapSettings":{"labelGridSize":"网格大小","labelSnapToGrid":"网格对齐","labelSnapToGuides":"自动对齐","titleSnapDisabled":"禁用自动对齐","titleSnapEnabled":"启用自动对齐"},"themePicker":{"themes":{"activeReports":"ActiveReports","activeReportsDark":"ActiveReports Dark","darkOled":"OLED深色","default":"默认","defaultDark":"默认深色","highContrast":"高对比度","highContrastDark":"高对比度深色","system":"系统主题"},"titleTheme":"主题"},"titleHideGrid":"隐藏网格线","titleHideRulers":"隐藏标尺","titleShowGrid":"显示网格线","titleShowRulers":"显示标尺","titleZoomIn":"放大","titleZoomOut":"缩小"},"tabs":{"actions":{"textDuplicate":"复制","textHide":"隐藏","textInsert":"插入","textMDelete":"删除","textMoveLeft":"向左移","textMoveRight":"向右移","textShow":"显示"},"btnAddPage":"添加页","btnAddSection":"添加区域","textPage":"页"},"themeEditor":{"labelNone":"<无>"},"title":{"textUntitled":"未命名"},"toolbar":{"home":{"textExpression":"<表达式>","textExpressionCompact":"<表达式>","titleAlignCenter":"居中对齐","titleAlignJustify":"两端对齐","titleAlignLeft":"左对齐","titleAlignRight":"右对齐","titleBackgroundColor":"背景色","titleCopy":"复制","titleCut":"剪切","titleFontFamily":"字体","titleFontSize":"字号","titleFontStyle":"字形","titleFontWeight":"字重","titlePaste":"粘贴","titleTextColor":"颜色","titleTextDecoration":"下划线","titleVerticalAlignBottom":"底端对齐","titleVerticalAlignMiddle":"垂直居中","titleVerticalAlignTop":"顶端对齐"},"titleExpand":"展开"}}},{"lng":"zh","ns":"contextActions-RPX","resources":{"labels":{"addGroupHeaderFooter":"添加组页眉/页脚","addPageHeaderFooter":"添加页面页眉/页脚","addReportHeaderFooter":"添加报表页眉/页脚","copy":"复制","cut":"剪切","delete":"删除","deletePageHeaderFooter":"删除页面页眉/页脚","deleteReportHeaderFooter":"删除报表页眉/页脚","layout":{"alignToBottoms":"底部对齐","alignToCenters":"居中对齐","alignToGrid":"与网格线对齐","alignToLefts":"左对齐","alignToMiddles":"居中对齐","alignToRights":"右对齐","alignToTops":"顶部对齐","bringToFront":"置于顶层","horizontal":{"decreaseSpacing":"减少水平间距","increaseSpacing":"增加水平间距","makeSpacingEqual":"等水平间距","removeSpacing":"移除水平间距"},"makeSameHeight":"等高","makeSameSize":"等大小","makeSameWidth":"等宽","sendToBack":"置于底层","separator":{"corelateControlSizing":"同步组件大小","horizontalAlignment":"水平对齐","horizontalSpacing":"水平间距","sortControls":"排序控件","verticalAlignment":"垂直对齐","verticalSpacing":"垂直间距"},"sizeToGrid":"按网格调整大小","title":"布局","vertical":{"decreaseSpacing":"减少垂直间距","increaseSpacing":"增加垂直间距","makeSpacingEqual":"等垂直间距","removeSpacing":"移除垂直间距"}},"pageFooter":"页面页脚","pageHeader":"页面页眉","paste":"粘贴","report":"报表","reportFooter":"报表页脚","reportHeader":"报表页眉"}}},{"lng":"zh","ns":"contextActions","resources":{"bandedList":{"addFooter":"添加尾部区域","addGroupFooter":"添加分组尾","addGroupHeader":"添加分组头","addHeader":"添加头部区域","deleteGroup":"删除分组","groupTitle":"分组","insertGroup":"插入分组","removeFooter":"删除尾部区域","removeGroupFooter":"删除分组尾","removeGroupHeader":"删除分组头","removeHeader":"删除头部区域","title":"带状列表"},"chart":"图表","container":{"delete":"删除","expression":"表达式"},"dashboard":{"duplicateSection":"复制区域","hideSection":"隐藏区域","moveSectionLeft":"向左移动区域","moveSectionRight":"向右移动区域","removeSection":"删除区域","showSection":"显示区域","switchTheme":"切换主题","title":"交互式报表"},"dvchart":{"palette":"图表配色","presetGroups":{"area":"面积图","bar":"条形图","column":"柱形图","line":"折线图","misc":"更多类型","pie":"饼图","polarBar":"极坐标条形图","polarColumn":"极坐标柱状图","radar":"雷达图","range":"区间图表"},"presets":{"area":"面积图","areaPercentStacked":"百分比堆积面积图","areaStacked":"堆积面积图","bar":"条形图","barPercentStacked":"百分比堆积条形图","barStacked":"堆积条形图","bubble":"气泡图","candlestick":"开盘-盘高-盘底-收盘图","column":"柱状图","columnPercentStacked":"百分比堆积柱形图","columnStacked":"堆积柱形图","doughnut":"圆环图","funnel":"漏斗图","gantt":"甘特图","gauge":"仪表盘","highLowClose":"盘高-盘低-收盘图","highLowOpenClose":"盘高-盘低-开盘-收盘图","line":"折线图","lineSmooth":"曲线图","pie":"饼图","polarBar":"极坐标条形图","polarBarPercentStacked":"极坐标百分比堆叠条形图","polarBarStacked":"极坐标堆叠条形图","polarColumn":"极坐标柱状图","polarColumnPercentStacked":"极坐标百分比堆叠柱状图","polarColumnStacked":"极坐标堆叠柱状图","pyramid":"锥形图","radarArea":"雷达面积图","radarBubble":"雷达气泡图","radarLine":"雷达折线图","radarScatter":"雷达散点图","rangeArea":"区间面积图","rangeBar":"区间条形图","rangeColumn":"区间柱状图","scatter":"散点图","title":"图表类型"}},"report":{"addContinuousSection":"添加报表区域 ","addFixedPageSection":"Add Fixed Page Section","changeMasterReport":"更换母版报表","convertToMasterReport":"转换成母版报表","deletePage":"删除页面","duplicatePage":"复制页面","duplicateSection":"复制区域","hidePage":"隐藏页面","hideSection":"隐藏区域","insertPage":"插入页面","insertSection":"插入区域","layout":{"alignToBottoms":"底部对齐","alignToCenters":"居中对齐","alignToGrid":"与网格线对齐","alignToLefts":"左对齐","alignToMiddles":"居中对齐","alignToRights":"右对齐","alignToTops":"顶部对齐","bringToFront":"置于顶层","horizontal":{"decreaseSpacing":"减少水平间距","increaseSpacing":"增加水平间距","makeSpacingEqual":"等水平间距","removeSpacing":"移除水平间距"},"horizontalAlignment":"水平对齐","makeSameHeight":"等高","makeSameSize":"等大小","makeSameWidth":"等宽","sendToBack":"置于底层","separator":{"corelateControlSizing":"同步组件大小","horizontalSpacing":"水平间距","sortControls":"排序控件","verticalSpacing":"垂直间距"},"sizeToGrid":"按网格调整大小","title":"布局","vertical":{"decreaseSpacing":"减少垂直间距","increaseSpacing":"增加垂直间距","makeSpacingEqual":"等垂直间距","removeSpacing":"移除垂直间距"},"verticalAlignment":"垂直对齐"},"movePageBackward":"向左移动页面","movePageForward":"向右移动页面","moveSectionLeft":"向左移动区域","moveSectionRight":"向右移动区域","pages":"页面","removeSection":"删除区域","reportParts":{"title":"报表组件","titleCreateReportPart":"创建报表组件"},"setMasterReport":"设置母版报表","showPage":"显示页面","showSection":"显示区域","switchTheme":"切换主题","title":"报表"},"reportSection":{"addFooter":"添加页脚","addHeader":"添加页眉","removeFooter":"删除页脚","removeHeader":"删除页眉","title":"区域"},"table":{"addDetails":"添加数据行","addFooter":"添加汇总行","addGroupFooter":"添加分组尾","addGroupHeader":"添加分组头","addHeader":"添加标题行","cellsTitle":"单元格","columnTitle":"列操作","deleteColumn":"删除列","deleteGroup":"删除分组","deleteRow":"删除行","expression":"表达式","groupTitle":"分组操作","insertColumn":{"left":"在左侧插入列","right":"在右侧插入列"},"insertColumnTitle":"插入列","insertGroup":"插入分组","insertRow":{"above":"在上方插入行","below":"在下方插入行"},"insertRowTitle":"插入行","mergeCells":"合并单元格","more":"更多...","removeDetails":"删除明细行","removeFooter":"删除汇总行","removeGroupFooter":"删除分组尾","removeGroupHeader":"删除分组头","removeHeader":"删除标题行","rowTitle":"行操作","splitCells":"拆分单元格","title":"表格"},"tablix":{"addGroup":{"adjacentAfter":"同级分组-在当前分组后面","adjacentBefore":"同级分组-在当前分组前面","child":"子级分组","parent":"父级分组"},"addGroupTitle":"插入分组","addTotal":{"contextMenuAfter":"在分组后显示汇总","contextMenuBefore":"在分组前显示汇总"},"cellsTitle":"单元格","columnGroup":"列分组","columnTitle":"列操作","delete":"删除","deleteColumn":"删除列","deleteRow":"删除行","disableGroup":"禁用分组","enableGroup":"启用分组","expression":"表达式","insertColumn":{"insideGroupLeft":"在分组内 - 左侧","insideGroupRight":"在分组内 - 右侧","left":"左侧","outsideGroupLeft":"在分组外 - 左侧","outsideGroupRight":"在分组外 - 右侧","right":"右侧"},"insertColumnTitle":"插入列","insertRow":{"above":"上方","below":"下方","insideGroupAbove":"在分组内 - 上方","insideGroupBelow":"在分组内 - 下方","outsideGroupAbove":"在分组外 - 上方","outsideGroupBelow":"在分组外 - 下方"},"insertRowTitle":"插入行","mergeCells":"合并单元格","more":"更多...","rowGroup":"行分组","rowTitle":"行操作","splitCells":"拆分单元格","totalTitle":"汇总操作"}}},{"lng":"zh","ns":"defaults","resources":{"chart":{"innerRadius":0.7,"startAngle":90}}},{"lng":"zh","ns":"dialogs","resources":{"btnCancel":"取消","btnInsert":"插入","common":{"textCancel":"取消"},"dataVisualizer":{"title":"数据可视化器"},"expressionEditor":{"headingExpression":"表达式","headingFunctions":"函数","headingInfo":"使用说明","headingValues":"数据","infoPanel":{"labelConstant":"常量:","labelDescription":"说明:","labelExample":"示例:","labelName":"名称:","labelSyntax":"语法:"},"placeholderExpression":"表达式","search":{"placeholderSearch":"查找...","textNoResults":"未找到 \\"{{query}}\\" 内容","textStartTyping":"请输入关键字进行查找"},"subtitle":"表达式编辑器"},"headingInsertColumns":"插入列","headingInsertRows":"插入行","labelCount":"数量","labelPosition":"位置"}},{"lng":"zh","ns":"documentItems-RPX","resources":{"Barcode":"条形码","CheckBox":"复选框","CrossSectionBox":"截面框","CrossSectionLine":"截面线","Detail":"详情","GroupFooter":"组页脚","GroupHeader":"组页眉","InputFieldCheckBox":"输入字段复选框","InputFieldText":"输入字段","Label":"标签","Line":"线条","PageBreak":"分页符","PageFooter":"页脚","PageHeader":"页眉","Picture":"图片","Report":"报表","ReportFooter":"报表页脚","ReportHeader":"报表页眉","ReportInfo":"报表信息","RichTextBox":"富文本","Shape":"形状","SubReport":"子报表","TextBox":"文本框","Unknown":"未知","captions":{"basicSupportReportItemInfo":"{{itemLabel}} \'{{itemName}}\' 在设计时支持有限."}}},{"lng":"zh","ns":"documentsAPI","resources":{"romLabels":{"chart":"Chart","dvchart":"DV.Chart","matrix":"Matrix","table":"Table","tablix":"Tablix"},"textOpenDocumentWarnings":"Document opened with warnings","textRenamedItemPrefix":"内容_{{originalName}}","textReportConversion":"There have been issues while opening this report","transform":{"helpLink":"Please find more information on report items transformation at {{link}}.","textBadReportItem":"✘ {{SourceType}} \\"{{Name}}\\" couldn\'t have been transformed to {{ResultType}} due to internal issues:","textError":"– [{{ErrorType}}] {{Message}}","textReport":"The report \\"{{reportName}}\\" has been transformed.","textReportItem":"✔ {{SourceType}} \\"{{Name}}\\" has been transformed to {{ResultType}}."}}},{"lng":"zh","ns":"enums-RPX","resources":{"background_style":{"Gradient":"渐变","Pattern":"模式","Solid":"实线"},"barcode_caption_position":{"Above":"以上","Below":"以下","None":"无"},"barcode_rotation":{"None":"无","Rotate180Degrees":"旋转180度","Rotate270Degrees":"旋转270度","Rotate90Degrees":"旋转90度"},"barcode_style":{"Ansi39":"Ansi39","Ansi39x":"Ansi39x","Aztec":"Aztec","BC412":"BC412","Codabar":"Codabar","Code25intlv":"Code25intlv","Code25mat":"Code25mat","Code39":"Code39","Code39x":"Code39x","Code49":"Code49","Code93x":"Code93x","Code_11":"Code_11","Code_128_A":"Code_128_A","Code_128_B":"Code_128_B","Code_128_C":"Code_128_C","Code_128auto":"Code_128auto","Code_2_of_5":"Code_2_of_5","Code_93":"Code_93","DataMatrix":"数据矩阵","EAN128FNC1":"EAN128FNC1","EAN_13":"EAN_13","EAN_8":"EAN_8","GS1DataMatrix":"GS1数据矩阵","GS1QRCode":"GS1QRCode","HIBCCode128":"HIBCCode128","HIBCCode39":"HIBCCode39","IATA_2_of_5":"IATA_2_of_5","ISBN":"ISBN","ISMN":"ISMN","ISSN":"ISSN","ITF14":"ITF14","IntelligentMail":"智能邮箱","IntelligentMailPackage":"智能邮箱包","JapanesePostal":"日本邮政","MSI":"MSI","Matrix_2_of_5":"Matrix_2_of_5","MaxiCode":"MaxiCode","MicroPDF417":"MicroPDF417","MicroQRCode":"MicroQRCode","None":"无","PZN":"PZN","Pdf417":"Pdf417","Pharmacode":"药物代码","Plessey":"Plessey","PostNet":"PostNet","QRCode":"QRCode","RM4SCC":"RM4SCC","RSS14":"RSS14","RSS14Stacked":"RSS14堆栈","RSS14StackedOmnidirectional":"RSS14全向堆栈","RSS14Truncated":"RSS14节录","RSSExpanded":"RSS扩展","RSSExpandedStacked":"RSS 扩展堆栈","RSSLimited":"RSS限制","SSCC_18":"SSCC_18","Telepen":"Telepen","UCCEAN128":"UCCEAN128","UPC_A":"UPC_A","UPC_E0":"UPC_E0","UPC_E1":"UPC_E1"},"border_style":{"Dash":"破折线","DashDot":"破绽号","Dot":"点","Double":"双线","ExtraThickSolid":"超粗实线","None":"无","Solid":"实线","ThickDash":"粗破折号","ThickDashDot":"粗破折号","ThickDashDotDot":"粗破折号","ThickDot":"粗圆点","ThickDouble":"粗双线","ThickSolid":"粗实线"},"border_style_inputfield":{"Dashed":"虚线","Inset":"插图","None":"无","Solid":"实线"},"calculated_field_type":{"Boolean":"布尔","Date":"日期","Double":"双精度型","Float":"浮点型","Int32":"整型32","None":"无","String":"字符串"},"calendar":{"Gregorian":"格里高利语","GregorianArabic":"公历阿拉伯语","GregorianMiddleEastFrench":"格里高利中东法语","GregorianTransliteratedEnglish":"公历音译英语","GregorianTransliteratedFrench":"公历音译法语","GregorianUSEnglish":"公历美式英语","Hebrew":"希伯来语","Hijri":"回历","Japanese":"日语","Korea":"韩国","Taiwan":"台湾","ThaiBuddhist":"泰国佛教徒"},"check_style":{"Check":"格纹","Circle":"圆形","Cross":"十字","Diamond":"菱形","Square":"方形","Star":"星型"},"collate":{"Collate":"校勘","Default":"默认","DontCollate":"不用校勘"},"column_direction":{"AcrossDown":"横向向下","DownAcross":"向下穿过"},"compatibility_mode":{"CrossPlatform":"跨平台","GDI":"GDI"},"content_alignment":{"BottomCenter":"中下","BottomLeft":"左下","BottomRight":"右下","MiddleCenter":"中间","MiddleLeft":"左中","MiddleRight":"右中","TopCenter":"顶部居中","TopLeft":"左上","TopRight":"右上"},"culture":{"af-ZA":"南非荷兰语 (南非)","ar-AE":"阿拉伯语 (阿联酋)","ar-BH":"阿拉伯语 (巴林)","ar-DZ":"阿拉伯语 (阿尔及利亚)","ar-EG":"阿拉伯语 (埃及)","ar-IQ":"阿拉伯语 (伊拉克)","ar-JO":"阿拉伯语 (约旦)","ar-KW":"阿拉伯语 (科威特)","ar-LB":"阿拉伯语 (黎巴嫩)","ar-LY":"阿拉伯语 (利比亚)","ar-MA":"阿拉伯语 (摩洛哥)","ar-OM":"阿拉伯语 (阿曼)","ar-QA":"阿拉伯语 (卡塔尔)","ar-SA":"阿拉伯语 (沙特阿拉伯)","ar-SY":"阿拉伯语 (叙利亚)","ar-TN":"阿拉伯语 (突尼斯)","ar-YE":"阿拉伯语 (也门)","az-Cyrl-AZ":"阿塞里语 (西里尔语， 阿塞拜疆)","az-Latn-AZ":"阿塞里语 (拉丁语， 阿塞拜疆)","be-BY":"比利时语 (白俄罗斯)","bg-BG":"保加利亚 (保加利亚)","ca-ES":"加泰罗尼亚 (加泰罗尼亚)","cs-CZ":"捷克 (捷克共和国)","da-DK":"丹麦语 (丹麦)","de-AT":"德语 (奥地利)","de-CH":"德语 (瑞士)","de-DE":"德语 (德国)","de-LI":"德语 (列支敦士登)","de-LU":"德语 (卢森堡)","dv-MV":"迪维希语 (马尔代夫)","el-GR":"希腊语 (希腊)","en-029":"英语 (加勒比)","en-AU":"英语 (澳大利亚)","en-BZ":"英语 (伯利兹)","en-CA":"英语 (加拿大)","en-GB":"英语 (英国)","en-IE":"英语 (爱尔兰)","en-JM":"英语 (牙买加)","en-NZ":"英语 (新西兰)","en-PH":"英语 (菲律宾共和国)","en-TT":"英语 (特立尼达和多巴哥)","en-US":"英语 (美国)","en-ZA":"英语 (南非)","en-ZW":"英语 (津巴布韦)","es-AR":"西班牙语 (阿根廷)","es-BO":"西班牙语 (玻利维亚)","es-CL":"西班牙语 (智利)","es-CO":"西班牙语 (哥伦比亚)","es-CR":"西班牙语 (哥斯达黎加)","es-DO":"西班牙语 (多米尼加共和国)","es-EC":"西班牙语 (厄瓜多尔)","es-ES":"西班牙语 (西班牙)","es-GT":"西班牙语 (危地马拉)","es-HN":"西班牙语 (洪都拉斯)","es-MX":"西班牙语 (墨西哥)","es-NI":"西班牙语 (尼加拉瓜)","es-PA":"西班牙语 (巴拿马)","es-PE":"西班牙语 (秘鲁)","es-PR":"西班牙语 (波多黎各)","es-PY":"西班牙语 (巴拉圭)","es-SV":"西班牙语 (萨尔瓦多)","es-UY":"西班牙语 (乌拉圭)","es-VE":"西班牙语 (委内瑞拉)","et-EE":"爱沙尼亚语 (爱沙尼亚)","eu-ES":"巴士克语 (巴士克语)","fa-IR":"波斯语 (伊朗)","fi-FI":"芬兰语 (芬兰)","fo-FO":"法罗语 (法罗群岛)","fr-BE":"法语 (比利时)","fr-CA":"法语 (加拿大)","fr-CH":"法语 (瑞士)","fr-FR":"法语 (法国)","fr-LU":"法语 (卢森堡)","fr-MC":"法语 (摩纳哥公国)","gl-ES":"加利西亚语 (加利西亚)","gu-IN":"古吉拉特语 (印度)","he-IL":"希伯来语 (以色列)","hi-IN":"印地语 (印度)","hr-BA":"克罗地亚语 (波斯尼亚-黑塞哥维那)","hr-HR":"克罗地亚语 (克罗地亚)","hu-HU":"匈牙利语 (匈牙利)","hy-AM":"亚美尼亚 (亚美尼亚)","id-ID":"印度尼西亚语 (印度尼西亚)","is-IS":"冰岛语 (冰岛)","it-CH":"意大利语 (瑞士)","it-IT":"意大利语 (意大利)","ja-JP":"日语 (日本)","ka-GE":"格鲁吉亚语 (格鲁吉亚)","kk-KZ":"哈萨克语 (哈萨克斯坦)","kn-IN":"卡纳拉语 (印度)","ko-KR":"朝鲜语 (韩国)","kok-IN":"孔卡尼语 (印度)","ky-KG":"吉尔吉斯语 (吉尔吉斯斯坦)","lt-LT":"立陶宛语 (立陶宛)","lv-LV":"拉脱维亚语 (拉脱维亚)","mk-MK":"马其顿语 (马其顿共和国)","mn-MN":"蒙古语 (西里尔文， 蒙古)","mr-IN":"马拉地语 (印度)","ms-BN":"马来语 (文莱达鲁萨兰国)","ms-MY":"马来语 (马来西亚)","nb-NO":"挪威语， 博克姆语 (挪威)","nl-BE":"荷兰语 (比利时)","nl-NL":"荷兰语 (荷兰)","nn-NO":"新挪威语 (挪威)","pa-IN":"旁遮普语 (印度)","pl-PL":"波兰语 (波兰)","pt-BR":"葡萄牙语 (巴西)","pt-PT":"葡萄牙语 (葡萄牙)","ro-RO":"罗马尼亚语 (罗马尼亚)","ru-RU":"俄语 (俄罗斯)","sa-IN":"梵文 (印度)","sk-SK":"斯洛伐克语 (斯洛伐克)","sl-SI":"斯洛文尼亚语 (斯洛文尼亚)","sq-AL":"阿尔巴尼亚语 (阿尔巴尼亚)","sv-FI":"瑞典语 (芬兰)","sv-SE":"瑞典语 (瑞典)","sw-KE":"斯瓦希里语 (肯尼亚)","syr-SY":"叙利亚语 (叙利亚)","ta-IN":"泰米尔语 (印度)","te-IN":"泰卢固语 (印度)","th-TH":"泰语 (泰国)","tr-TR":"土耳其语 (土耳其)","tt-RU":"鞑靼语 (俄罗斯)","uk-UA":"乌克兰语 (乌克兰)","ur-PK":"乌尔都语 (巴基斯坦伊斯兰共和国)","uz-Cyrl-UZ":"乌兹别克语 (西里尔语，乌兹别克斯坦)","uz-Latn-UZ":"乌兹别克语 (拉丁语，乌兹别克斯坦)","vi-VN":"越南语 (越南)","zh-CN":"中文 (中华人民共和国)","zh-HK":"中文 (香港特别行政区)","zh-MO":"中文 (澳门)","zh-SG":"中文 (新加坡)","zh-TW":"中文 (台湾)"},"ddo_char_set":{"Arab":"阿拉伯语","Baltic":"波罗","CentralEuropean":"中瓯","Cyrillic":"西里尔","Greek":"希腊语","Hebrew":"希伯来语","Turkish":"土耳其语","Vietnamese":"越南语","Western":"西瓯"},"duplex":{"Default":"默认","Horizontal":"横向","Simplex":"简单","Vertical":"纵向"},"field_type":{"Boolean":"布尔","Date":"日期","Double":"双精度型","Float":"浮点型","Int32":"整型32","Integer":"整型","None":"无","String":"字符串"},"font_size":{"10pt":"10pt","11pt":"11pt","12pt":"12pt","14pt":"14pt","16pt":"16pt","18pt":"18pt","20pt":"20pt","22pt":"22pt","24pt":"24pt","26pt":"26pt","28pt":"28pt","36pt":"36pt","48pt":"48pt","72pt":"72pt","8pt":"8pt","9pt":"9pt"},"font_style":{"Italic":"斜体","Normal":"正常"},"font_weight":{"Bold":"加粗","Normal":"正常"},"format_string":{"Page_PageNumber_of_PageCount":"","Page_PageNumber_of_PageCount_on_RunDateTime":"页面 {PageNumber} 总页数 {PageCount} 在 {RunDateTime}","RunDateTime":"{RunDateTime:}","RunDateTime_MMMM_d_yyyy":"{RunDateTime:MMMM d, yyyy}","RunDateTime_MMMM_yy":"{RunDateTime:MMMM-yy}","RunDateTime_MMMM_yyyy":"{RunDateTime:MMMM-yyyy}","RunDateTime_MMM_yy":"{RunDateTime:MMM-yy}","RunDateTime_MMM_yyyy":"{RunDateTime:MMM-yyyy}","RunDateTime_MM_dd_yy":"{RunDateTime:MM/dd/yy}","RunDateTime_MM_dd_yyyy":"{RunDateTime:MM/dd/yyyy}","RunDateTime_M_d":"{RunDateTime:M/d}","RunDateTime_M_d_yy":"{RunDateTime:M/d/yy}","RunDateTime_M_d_yy_h_mm":"{RunDateTime:M/d/yy h:mm}","RunDateTime_M_d_yy_h_mm_tt":"{RunDateTime:M/d/yy h:mm tt}","RunDateTime_M_d_yyyy":"{RunDateTime:M/d/yyyy}","RunDateTime_M_d_yyyy_h_mm":"{RunDateTime:M/d/yyyy h:mm}","RunDateTime_M_d_yyyy_h_mm_tt":"{RunDateTime:M/d/yyyy h:mm tt}","RunDateTime_M_d_yyyy}":"{RunDateTime:M/d/yyyy}","RunDateTime_d_MMM":"{RunDateTime:d-MMM}","RunDateTime_d_MMM_yy":"{RunDateTime:d-MMM-yy}","RunDateTime_d_MMM_yyyy":"{RunDateTime:d-MMM-yyyy}","RunDateTime_dd_MMM_yy":"{RunDateTime:dd-MMM-yy}","RunDateTime_dd_MMM_yyyy":"{RunDateTime:dd-MMM-yyyy}"},"gradient_style":{"DiagonalDown":"对角线向下","DiagonalUp":"对角线向上","FromCenter":"从中心","FromCorner":"从角落","Horizontal":"横向","Vertical":"纵向"},"group_keep_together":{"All":"所有","FirstDetail":"第一详情","None":"无"},"hatch_style":{"BackwardDiagonal":"向后对角线","DarkDownwardDiagonal":"深色向下对角线","DarkHorizontal":"深横向","DarkUpwardDiagonal":"深色向上对角线","DarkVertical":"深纵向","DashedDownwardDiagonal":"虚线向下对角线","DashedHorizontal":"虚线横线","DashedUpwardDiagonal":"虚线向上对角线","DashedVertical":"虚线竖线","DiagonalBrick":"DiagonalBrick","DiagonalCross":"对角线交叉","Divot":"Divot","DottedDiamond":"DottedDiamond","DottedGrid":"DottedGrid","ForwardDiagonal":"向前对角线","Horizontal":"横向","HorizontalBrick":"HorizontalBrick","LargeCheckerBoard":"LargeCheckerBoard","LargeConfetti":"LargeConfetti","LargeGrid":"大网格","LightDownwardDiagonal":"浅色向下对角线","LightHorizontal":"浅横向","LightUpwardDiagonal":"浅色向上对角线","LightVertical":"浅纵向","NarrowHorizontal":"窄横向","NarrowVertical":"窄纵向","OutlinedDiamond":"OutlinedDiamond","Percent05":"5%","Percent10":"10%","Percent20":"20%","Percent25":"25%","Percent30":"30%","Percent40":"40%","Percent50":"50%","Percent60":"60%","Percent70":"70%","Percent75":"75%","Percent80":"80%","Percent90":"90%","Plaid":"Plaid","Shingle":"Shingle","SmallCheckerBoard":"SmallCheckerBoard","SmallConfetti":"SmallConfetti","SmallGrid":"SmallGrid","SolidDiamond":"SolidDiamond","Sphere":"Sphere","Trellis":"Trellis","Vertical":"纵向","Wave":"波浪","Weave":"Weave","WideDownwardDiagonal":"宽向下对角线","WideUpwardDiagonal":"宽向上对角线","ZigZag":"ZigZag"},"kinsoku":{"Auto":"自动","None":"无","True":"真"},"line_style":{"Dash":"破折号","DashDot":"破折号","DashDotDot":"双向破折号","Dot":"点","Double":"双横线","Solid":"实线","Transparent":"透明度"},"mime_type":{"image/bmp":"图片/bmp","image/gif":"图片/gif","image/jpeg":"图片/jpeg","image/png":"图片/png","image/x-emf":"图片/x-emf","image/x-wmf":"图片/x-wmf"},"new_column":{"After":"后面","Before":"前面","BeforeAfter":"前后","None":"无"},"new_page":{"After":"后面","Before":"前面","BeforeAfter":"前后","None":"无"},"page_orientation":{"Default":"默认","Landscape":"美化","Portrait":"纵向"},"paper_kind":{"A2":"A2","A3":"A3","A3Extra":"A3Extra","A3ExtraTransverse":"A3ExtraTransverse","A3Rotated":"A3Rotated","A3Transverse":"A3Transverse","A4":"A4","A4Extra":"A4Extra","A4Plus":"A4Plus","A4Rotated":"A4Rotated","A4Small":"小A4","A4Transverse":"A4截面","A5":"A5","A5Extra":"A5Extra","A5Rotated":"A5Rotated","A5Transverse":"A5Transverse","A6":"A6","A6Rotated":"A6Rotated","APlus":"APlus","B4":"B4","B4Envelope":"B4信封","B4JisRotated":"B4JisRotated","B5":"B5","B5Envelope":"B5信封","B5Extra":"B5Extra","B5JisRotated":"B5JisRotated","B5Transverse":"B5Transverse","B6Envelope":"B6信封","B6Jis":"B6Jis","B6JisRotated":"B6JisRotated","BPlus":"BPlus","C3Envelope":"C3信封","C4Envelope":"C4信封","C5Envelope":"C5信封","C65Envelope":"C65信封","C6Envelope":"C6信封","CSheet":"CSheet","Custom":"自定义","DLEnvelope":"DL信封","DSheet":"DSheet","ESheet":"ESheet","Executive":"Executive","Folio":"Folio","GermanLegalFanfold":"德国法律复写簿","GermanStandardFanfold":"德国标准复写簿","InviteEnvelope":"请帖","IsoB4":"IsoB4","ItalyEnvelope":"意大利信封","JapaneseDoublePostcard":"JapaneseDoublePostcard","JapaneseDoublePostcardRotated":"日本双面明信片Rotated","JapaneseEnvelopeChouNumber3":"日本3号Chou信封","JapaneseEnvelopeChouNumber3Rotated":"日本3号Chou信封Rotated","JapaneseEnvelopeChouNumber4":"日本4号Chou信封","JapaneseEnvelopeChouNumber4Rotated":"日本4号Chou信封Rotated","JapaneseEnvelopeKakuNumber2":"日本2号Kaku信封","JapaneseEnvelopeKakuNumber2Rotated":"本2号Kaku信封Rotated","JapaneseEnvelopeKakuNumber3":"日本3号Kaku信封","JapaneseEnvelopeKakuNumber3Rotated":"日本3号KakuRotated","JapaneseEnvelopeYouNumber4":"日本4号You信封","JapaneseEnvelopeYouNumber4Rotated":"日本4号You信封Rotated","JapanesePostcard":"日本明信片","JapanesePostcardRotated":"日本明信片Rotated","Ledger":"分类账","Legal":"法律专用纸","LegalExtra":"LegalExtra","Letter":"信纸","LetterExtra":"LetterExtra","LetterExtraTransverse":"LetterExtraTransverse","LetterPlus":"LetterPlus","LetterRotated":"Rotated信封","LetterSmall":"小信纸","LetterTransverse":"信封截面","MonarchEnvelope":"王室信封","Note":"便条","Number10Envelope":"10号信封","Number11Envelope":"10号信封","Number12Envelope":"12号信封","Number14Envelope":"14号信封","Number9Envelope":"9号信封","PersonalEnvelope":"个人信纸","Prc16K":"Prc16K","Prc16KRotated":"Prc16KRotated","Prc32K":"Prc32K","Prc32KBig":"Prc32KBig","Prc32KBigRotated":"Prc32KBigRotated","Prc32KRotated":"Prc32KRotated","PrcEnvelopeNumber1":"PrcEnvelopeNumber1","PrcEnvelopeNumber10":"PrcEnvelopeNumber10","PrcEnvelopeNumber10Rotated":"PrcEnvelopeNumber10Rotated","PrcEnvelopeNumber1Rotated":"PrcEnvelopeNumber1Rotated","PrcEnvelopeNumber2":"PrcEnvelopeNumber2","PrcEnvelopeNumber2Rotated":"PrcEnvelopeNumber2Rotated","PrcEnvelopeNumber3":"PrcEnvelopeNumber3","PrcEnvelopeNumber3Rotated":"PrcEnvelopeNumber3Rotated","PrcEnvelopeNumber4":"PrcEnvelopeNumber4","PrcEnvelopeNumber4Rotated":"PrcEnvelopeNumber4Rotated","PrcEnvelopeNumber5":"PrcEnvelopeNumber5","PrcEnvelopeNumber5Rotated":"PrcEnvelopeNumber5Rotated","PrcEnvelopeNumber6":"PrcEnvelopeNumber6","PrcEnvelopeNumber6Rotated":"PrcEnvelopeNumber6Rotated","PrcEnvelopeNumber7":"PrcEnvelopeNumber7","PrcEnvelopeNumber7Rotated":"PrcEnvelopeNumber7Rotated","PrcEnvelopeNumber8":"PrcEnvelopeNumber8","PrcEnvelopeNumber8Rotated":"PrcEnvelopeNumber8Rotated","PrcEnvelopeNumber9":"PrcEnvelopeNumber9","PrcEnvelopeNumber9Rotated":"PrcEnvelopeNumber9Rotated","Quarto":"Quarto","Standard10x11":"标准10x11","Standard10x14":"Standard10x14","Standard11x17":"Standard11x17","Standard12x11":"标准12x11","Standard15x11":"标准15x11","Standard9x11":"标准9x11","Statement":"Statement","Tabloid":"Tabloid","TabloidExtra":"TabloidExtra","USStandardFanfold":"美国标准复写簿"},"paper_size":{"A3":"A3","A4":"A4","A5":"A5","A6":"A6","Custom":"自定义","Executive":"Executive","ISOB5":"B5 (ISO)","JISB4":"B4 (JIS)","JISB5":"B5 (JIS)","JISB6":"B6 (JIS)","Legal":"Legal","Letter":"Letter","Tabloid":"Tabloid"},"paper_source":{"AutomaticFeed":"自动送纸","Cassette":"暗盒","Manual":"手动"},"paper_source_kind":{"AutomaticFeed":"自动续纸","Cassette":"暗盒","Custom":"自定义","Envelope":"信封","FormSource":"源文本","LargeCapacity":"大容量","LargeFormat":"大格式","Lower":"下","Manual":"手册","ManualFeed":"手工续纸","Middle":"中间","SmallFormat":"小格式","TractorFeed":"牵引送纸","Upper":"上"},"parameter_type":{"Boolean":"布尔","Date":"日期","String":"字符串"},"picture_alignment":{"BottomLeft":"左下","BottomRight":"右下","Center":"居中","TopLeft":"左上","TopRight":"右上"},"repeat_style":{"All":" 所有","None":"无","OnColumn":"在列上","OnPage":"在页面","OnPageIncludeNoDetail":"页面上不包括细节"},"richrext_rendering_type":{"Metafile":"Meta文件","PNG":"PNG","RTF":"RTF","TransparentPNG":"透明PNG"},"script_language":{"C#":"C#","VBNET":"VB.NET"},"shape_style":{"Ellipse":"椭圆","Rectangle":"矩形","RoundRect":"带圆角的矩形"},"size_mode":{"Clip":"固定","Stretch":"拉伸","Zoom":"缩放"},"string_alignment":{"Center":"居中","Far":"远","Near":"近"},"string_alignment_inputfield":{"Center":"居中","Left":"左","Right":"右"},"summary_func":{"Avg":"Avg","Count":"Count","DAvg":"DAvg","DCount":"DCount","DStdDev":"DStdDev","DStdDevP":"DStdDevP","DSum":"DSum","DVar":"DVar","DVarP":"DVarP","Max":"Max","Min":"Min","StdDev":"StdDev","StdDevP":"StdDevP","Sum":"Sum","Var":"Var","VarP":"VarP"},"summary_running":{"All":"所有","Group":"组","None":"无"},"summary_type":{"GrandTotal":"总计","None":"None","PageCount":"页面计数","PageTotal":"页面总计","SubTotal":"小计"},"text_align":{"Center":"居中","Justify":"对齐","Left":"左","Right":"右"},"text_justify":{"Auto":"自动","Distribute":"分布","Distribute_all_lines":"分配所有线"},"vertical_align":{"Bottom":"底部","Middle":"中间","Top":"顶部"},"white_space":{"NoWrap":"不换行","Normal":"正常","Pre":"Pre"},"word_wrap":{"CharWrap":"根据字符换行","NoWrap":"不自动换行","WordWrap":"自动换行"}}},{"lng":"zh","ns":"enums","resources":{"action":{"ApplyParameters":"应用到报表参数","BookmarkLink":"跳转至书签","Drillthrough":"跳转至报表","Hyperlink":"跳转至URL","None":"无"},"action_apply_value_cmd":{"Reset":"重置","Set":"设置","Toggle":"切换"},"auto_merge_mode":{"Always":"跨分组合并","Never":"永不合并","Restricted":"分组内合并"},"auto_width":{"None":"无","Proportional":"按比例划分"},"axis_location":{"Left":"左","Right":"右"},"axis_mode":{"Cartesian":"笛卡尔坐标","Polygonal":"极坐标多边形","Radial":"极坐标环状"},"barcode_caption_location":{"Above":"置于顶部","Below":"置于底部","None":"无标题"},"barcode_ecc000_140_symbol_size":{"Auto":"Auto","Square11":"Square11","Square13":"Square13","Square15":"Square15","Square17":"Square17","Square19":"Square19","Square21":"Square21","Square23":"Square23","Square25":"Square25","Square27":"Square27","Square29":"Square29","Square31":"Square31","Square33":"Square33","Square35":"Square35","Square37":"Square37","Square39":"Square39","Square41":"Square41","Square43":"Square43","Square45":"Square45","Square47":"Square47","Square49":"Square49","Square9":"Square9"},"barcode_ecc200_encoding_mode":{"ASCII":"ASCII","Auto":"Auto","Base256":"Base256","C40":"C40","EDIFACT":"EDIFACT","Text":"Text","X12":"X12"},"barcode_ecc200_symbol_size":{"Rectangular12x26":"Rectangular12x26","Rectangular12x36":"Rectangular12x36","Rectangular16x36":"Rectangular16x36","Rectangular16x48":"Rectangular16x48","Rectangular8x18":"Rectangular8x18","Rectangular8x32":"Rectangular8x32","RectangularAuto":"RectangularAuto","Square10":"Square10","Square104":"Square104","Square12":"Square12","Square120":"Square120","Square132":"Square132","Square14":"Square14","Square144":"Square144","Square16":"Square16","Square18":"Square18","Square20":"Square20","Square22":"Square22","Square24":"Square24","Square26":"Square26","Square32":"Square32","Square36":"Square36","Square40":"Square40","Square44":"Square44","Square48":"Square48","Square52":"Square52","Square64":"Square64","Square72":"Square72","Square80":"Square80","Square88":"Square88","Square96":"Square96","SquareAuto":"SquareAuto"},"barcode_ecc_mode":{"ECC000":"ECC000","ECC050":"ECC050","ECC080":"ECC080","ECC100":"ECC100","ECC140":"ECC140","ECC200":"ECC200"},"barcode_encoding":{"37":"IBM EBCDIC (US-Canada) 37","437":"OEM United States 437","500":"IBM EBCDIC (International) 500","708":"Arabic (ASMO 708) 708","720":"Arabic (DOS) 720","737":"Greek (DOS) 737","775":"Baltic (DOS) 775","850":"Western European (DOS) 850","852":"Central European (DOS) 852","855":"OEM Cyrillic 855","857":"Turkish (DOS) 857","858":"OEM Multilingual Latin I 858","860":"Portuguese (DOS) 860","861":"Icelandic (DOS) 861","862":"Hebrew (DOS) 862","863":"French Canadian (DOS) 863","864":"Arabic (864) 864","865":"Nordic (DOS) 865","866":"Cyrillic (DOS) 866","869":"Greek, Modern (DOS) 869","870":"IBM EBCDIC (Multilingual Latin-2) 870","874":"Thai (Windows) 874","875":"IBM EBCDIC (Greek Modern) 875","932":"Japanese (Shift-JIS) 932","936":"Chinese Simplified (GB2312) 936","949":"Korean 949","950":"Chinese Traditional (Big5) 950","1026":"IBM EBCDIC (Turkish Latin-5) 1026","1047":"IBM Latin-1 1047","1140":"IBM EBCDIC (US-Canada-Euro) 1140","1141":"IBM EBCDIC (Germany-Euro) 1141","1142":"IBM EBCDIC (Denmark-Norway-Euro) 1142","1143":"IBM EBCDIC (Finland-Sweden-Euro) 1143","1144":"IBM EBCDIC (Italy-Euro) 1144","1145":"IBM EBCDIC (Spain-Euro) 1145","1146":"IBM EBCDIC (UK-Euro) 1146","1147":"IBM EBCDIC (France-Euro) 1147","1148":"IBM EBCDIC (International-Euro) 1148","1149":"IBM EBCDIC (Icelandic-Euro) 1149","1200":"Unicode 1200","1201":"Unicode (Big-Endian) 1201","1250":"Central European (Windows) 1250","1251":"Cyrillic (Windows) 1251","1252":"Western European (Windows) 1252","1253":"Greek (Windows) 1253","1254":"Turkish (Windows) 1254","1255":"Hebrew (Windows) 1255","1256":"Arabic (Windows) 1256","1257":"Baltic (Windows) 1257","1258":"Vietnamese (Windows) 1258","1361":"Korean (Johab) 1361","10000":"Western European (Mac) 10000","10001":"Japanese (Mac) 10001","10002":"Chinese Traditional (Mac) 10002","10003":"Korean (Mac) 10003","10004":"Arabic (Mac) 10004","10005":"Hebrew (Mac) 10005","10006":"Greek (Mac) 10006","10007":"Cyrillic (Mac) 10007","10008":"Chinese Simplified (Mac) 10008","10010":"Romanian (Mac) 10010","10017":"Ukrainian (Mac) 10017","10021":"Thai (Mac) 10021","10029":"Central European (Mac) 10029","10079":"Icelandic (Mac) 10079","10081":"Turkish (Mac) 10081","10082":"Croatian (Mac) 10082","12000":"Unicode (UTF-32) 12000","12001":"Unicode (UTF-32 Big-Endian) 12001","20000":"Chinese Traditional (CNS) 20000","20001":"TCA Taiwan 20001","20002":"Chinese Traditional (Eten) 20002","20003":"IBM5550 Taiwan 20003","20004":"TeleText Taiwan 20004","20005":"Wang Taiwan 20005","20105":"Western European (IA5) 20105","20106":"German (IA5) 20106","20107":"Swedish (IA5) 20107","20108":"Norwegian (IA5) 20108","20127":"US-ASCII 20127","20261":"T.61 20261","20269":"ISO-6937 20269","20273":"IBM EBCDIC (Germany) 20273","20277":"IBM EBCDIC (Denmark-Norway) 20277","20278":"IBM EBCDIC (Finland-Sweden) 20278","20280":"IBM EBCDIC (Italy) 20280","20284":"IBM EBCDIC (Spain) 20284","20285":"IBM EBCDIC (UK) 20285","20290":"IBM EBCDIC (Japanese katakana) 20290","20297":"IBM EBCDIC (France) 20297","20420":"IBM EBCDIC (Arabic) 20420","20423":"IBM EBCDIC (Greek) 20423","20424":"IBM EBCDIC (Hebrew) 20424","20833":"IBM EBCDIC (Korean Extended) 20833","20838":"IBM EBCDIC (Thai) 20838","20866":"Cyrillic (KOI8-R) 20866","20871":"IBM EBCDIC (Icelandic) 20871","20880":"IBM EBCDIC (Cyrillic Russian) 20880","20905":"IBM EBCDIC (Turkish) 20905","20924":"IBM Latin-1 20924","20932":"Japanese (JIS 0208-1990 and 0212-1990) 20932","20936":"Chinese Simplified (GB2312-80) 20936","20949":"Korean Wansung 20949","21025":"IBM EBCDIC (Cyrillic Serbian-Bulgarian) 21025","21866":"Cyrillic (KOI8-U) 21866","28591":"Western European (ISO) 28591","28592":"Central European (ISO) 28592","28593":"Latin 3 (ISO) 28593","28594":"Baltic (ISO) 28594","28595":"Cyrillic (ISO) 28595","28596":"Arabic (ISO) 28596","28597":"Greek (ISO) 28597","28598":"Hebrew (ISO-Visual) 28598","28599":"Turkish (ISO) 28599","28603":"Estonian (ISO) 28603","28605":"Latin 9 (ISO) 28605","29001":"Europa 29001","38598":"Hebrew (ISO-Logical) 38598","50220":"Japanese (JIS) 50220","50221":"Japanese (JIS-Allow 1 byte Kana) 50221","50222":"Japanese (JIS-Allow 1 byte Kana - SO/SI) 50222","50225":"Korean (ISO) 50225","50227":"Chinese Simplified (ISO-2022) 50227","51932":"Japanese (EUC) 51932","51936":"Chinese Simplified (EUC) 51936","51949":"Korean (EUC) 51949","52936":"Chinese Simplified (HZ) 52936","54936":"Chinese Simplified (GB18030) 54936","57002":"ISCII Devanagari 57002","57003":"ISCII Bengali 57003","57004":"ISCII Tamil 57004","57005":"ISCII Telugu 57005","57006":"ISCII Assamese 57006","57007":"ISCII Oriya 57007","57008":"ISCII Kannada 57008","57009":"ISCII Malayalam 57009","57010":"ISCII Gujarati 57010","57011":"ISCII Punjabi 57011","65000":"Unicode (UTF-7) 65000","65001":"Unicode (UTF-8) 65001"},"barcode_gs1_composite_type":{"CCA":"CCA","None":"None"},"barcode_maxicode_mode":{"Mode2":"Mode2","Mode3":"Mode3","Mode4":"Mode4","Mode5":"Mode5","Mode6":"Mode6"},"barcode_micro_pdf417_compaction_mode":{"Auto":"Auto","ByteCompactionMode":"ByteCompactionMode","NumericCompactionMode":"NumericCompactionMode","TextCompactionMode":"TextCompactionMode"},"barcode_micro_pdf417_version":{"ColumnPriorAuto":"ColumnPriorAuto","RowPriorAuto":"RowPriorAuto","Version1X11":"Version1X11","Version1X14":"Version1X14","Version1X17":"Version1X17","Version1X20":"Version1X20","Version1X24":"Version1X24","Version1X28":"Version1X28","Version2X11":"Version2X11","Version2X14":"Version2X14","Version2X17":"Version2X17","Version2X20":"Version2X20","Version2X23":"Version2X23","Version2X26":"Version2X26","Version2X8":"Version2X8","Version3X10":"Version3X10","Version3X12":"Version3X12","Version3X15":"Version3X15","Version3X20":"Version3X20","Version3X26":"Version3X26","Version3X32":"Version3X32","Version3X38":"Version3X38","Version3X44":"Version3X44","Version3X6":"Version3X6","Version3X8":"Version3X8","Version4X10":"Version4X10","Version4X12":"Version4X12","Version4X15":"Version4X15","Version4X20":"Version4X20","Version4X26":"Version4X26","Version4X32":"Version4X32","Version4X38":"Version4X38","Version4X4":"Version4X4","Version4X44":"Version4X44","Version4X6":"Version4X6","Version4X8":"Version4X8"},"barcode_micro_qrcode_error_level":{"L":"L","M":"M","Q":"Q"},"barcode_micro_qrcode_mask":{"Auto":"Auto","Mask00":"Mask00","Mask01":"Mask01","Mask10":"Mask10","Mask11":"Mask11"},"barcode_micro_qrcode_version":{"Auto":"Auto","M1":"M1","M2":"M2","M3":"M3","M4":"M4"},"barcode_pdf417_error_correction_level":{"Level0":"Level0","Level1":"Level1","Level2":"Level2","Level3":"Level3","Level4":"Level4","Level5":"Level5","Level6":"Level6","Level7":"Level7","Level8":"Level8"},"barcode_pdf417_type":{"Normal":"Normal","Simple":"Simple"},"barcode_qrcode_error_level":{"H":"H","L":"L","M":"M","Q":"Q"},"barcode_qrcode_mask":{"Auto":"Auto","Mask000":"Mask000","Mask001":"Mask001","Mask010":"Mask010","Mask011":"Mask011","Mask100":"Mask100","Mask101":"Mask101","Mask110":"Mask110","Mask111":"Mask111"},"barcode_qrcode_model":{"Model1":"Model1","Model2":"Model2"},"barcode_rotation":{"None":"不旋转","Rotate180Degrees":"旋转180度","Rotate270Degrees":"旋转270度","Rotate90Degrees":"旋转90度"},"barcode_symbology":{"Ansi39":"ANSI 3 of 9","Ansi39x":"ANSI Extended 3 of 9","Aztec":"Aztec","BC412":"BC412","Codabar":"Codabar","Code25intlv":"Interleaved 2 of 5","Code39":"Code 39","Code39x":"Extended Code 39","Code49":"Code 49","Code93x":"Extended Code 93","Code_11":"Code 11","Code_128_A":"Code 128 A","Code_128_B":"Code 128 B","Code_128_C":"Code 128 C","Code_128auto":"Code 128 Auto","Code_2_of_5":"Code 2 of 5","Code_93":"Code 93","DataMatrix":"Data Matrix","EAN128FNC1":"EAN-128FNC1","EAN_13":"EAN-13","EAN_8":"EAN-8","GS1DataMatrix":"GS1 Data Matrix","GS1QRCode":"GS1 QR Code","HIBCCode128":"HIBC Code 128","HIBCCode39":"HIBC Code 39","IATA_2_of_5":"IATA 2 of 5","ISBN":"ISBN (International Standard Book Number)","ISMN":"ISMN (Internationally Standard Music Number)","ISSN":"ISSN (International Standard Serial Number)","ITF14":"ITF-14","IntelligentMail":"Intelligent Mail","IntelligentMailPackage":"Intelligent Mail Package","JapanesePostal":"Japanese Postal","MSI":"MSI Code","Matrix_2_of_5":"Matrix 2 of 5","MaxiCode":"MaxiCode","MicroPDF417":"Micro PDF417","MicroQRCode":"Micro QR Code","None":"无","PZN":"PZN (Pharmaceutical Central Number)","Pdf417":"PDF417","Pharmacode":"Pharmacode","Plessey":"Plessey","PostNet":"PostNet","QRCode":"QR Code","RM4SCC":"RM4SCC (Royal Mail)","RSS14":"RSS-14","RSS14Stacked":"RSS-14 Stacked","RSS14StackedOmnidirectional":"RSS-14 Stacked Omnidirectional","RSS14Truncated":"RSS-14 Truncated","RSSExpanded":"RSS Expanded","RSSExpandedStacked":"RSS Expanded Stacked","RSSLimited":"RSS Limited","SSCC_18":"SSCC-18","Telepen":"Telepen","UCCEAN128":"UCC/EAN–128","UPC_A":"UPC-A","UPC_E0":"UPC-E0","UPC_E1":"UPC-E1"},"border_style":{"DashDot":"点划相间线","DashDotDot":"划线后跟两点线","Dashed":"虚线","Dotted":"点虚线","Double":"双实线","Groove":"凹槽线","Inset":"内凹线","None":"无","Outset":"外凸线","Ridge":"菱形线","Solid":"实线","WindowInset":"带外框内凹线"},"bullet_tick_marks":{"Inside":"内部","None":"无","Outside":"外部"},"calendar":{"Gregorian":"公历","GregorianArabic":"阿拉伯日历","GregorianMiddleEastFrench":"中东法国日历","GregorianTransliteratedEnglish":"英语","GregorianTransliteratedFrench":"法语","GregorianUSEnglish":"美国英语","Hebrew":"希伯来语","Hijri":"回历","Japanese":"日语","Korean":"韩语","Taiwan":"台湾","ThaiBuddhist":"泰国"},"check_alignment":{"BottomCenter":"底部居中","BottomLeft":"底部靠左","BottomRight":"底部靠右","MiddleCenter":"中部居中","MiddleLeft":"中部靠左","MiddleRight":"中部靠右","TopCenter":"顶部居中","TopLeft":"顶部靠左","TopRight":"顶部靠右"},"check_style":{"Check":"选择","Circle":"圆形","Cross":"交叉","Diamond":"钻石","Square":"方形","Star":"星形"},"collate_by":{"Simple":"简单模式","Value":"12","ValueIndex":"11"},"collation":{"Albanian":"阿尔巴尼亚语","Arabic":"阿拉伯语","Chinese_PRC":"中文简体","Chinese_PRC_Stroke":"中文简体笔划","Chinese_Taiwan_Bopomofo":"中文繁体拼音","Chinese_Taiwan_Stroke":"中文简体笔划","Croatian":"克罗地亚语","Cyrillic_General":"西里尔语","Czech":"捷克语","Danish_Norwegian":"丹麦挪威语","Default":"默认","Estonian":"爱沙尼亚语","FYRO_Macedonian":"马其顿语（Macedonian FYRO）","Finnish_Swedish":"芬兰瑞典语","French":"法语","Georgian_Modern_Sort":"格鲁吉亚现代排序","German_PhoneBook":"德语","Greek":"希腊语","Hebrew":"希伯来语","Hindi":"印地语","Hungarian":"匈牙利语","Hungarian_Technical":"匈牙利语（Hungarian Technical）","Icelandic":"冰岛语","Japanese":"日语","Japanese_Unicode":"日语Unicode","Korean_Wansung":"朝鲜语（Korean Wansung）","Korean_Wansung_Unicode":"朝鲜语（Korean Wansung Unicode）","Latin1_General":"拉丁语（Latin-1 General）","Latvian":"拉脱维亚语","Lithuanian":"立陶宛语","Lithuanian_Classic":"立陶宛语（Lithuanian Classic）","Polish":"波兰语","Romanian":"罗马尼亚语","Slovak":"斯洛伐克语","Slovenian":"斯洛文尼亚语","Spanish_Mexican_Trad":"西班牙语（墨西哥）","Spanish_Modern":"西班牙语（现代）","Thai":"泰语","Turkish":"土耳其语","Ukrainian":"乌克兰语","Vietnamese":"越南语"},"data_element_output":{"Auto":"自动","ContentsOnly":"仅输出内容","NoOutput":"不输出","Output":"输出"},"data_element_output_aon":{"Auto":"自动","NoOutput":"不输出","Output":"输出"},"data_element_output_on":{"NoOutput":"不输出","Output":"输出"},"data_element_style":{"AttributeNormal":"普通属性（AttributeNormal）","Auto":"自动","ElementNormal":"普通元素（ElementNormal）"},"data_label_position":{"Auto":"自动","Bottom":"底部居中","BottomLeft":"底部靠左","BottomRight":"底部靠右","Center":"中部居中","Left":"中部靠左","Right":"中部靠右","Top":"顶部居中","TopLeft":"顶部靠左","TopRight":"顶部靠右"},"data_visualizer_color_type":{"colorScale":"色阶"},"data_visualizer_gradient_type":{"DiagonalDown":"对角线向下","DiagonalUp":"对角线向上","FromCenter":"从中心开始","FromCorner":"从边角开始","Horizontal":"水平渐变","Vertical":"垂直渐变"},"data_visualizer_hatch_style":{"BackwardDiagonal":"后对角线","Cross":"交叉线","DarkDownwardDiagonal":"对角线向下","DarkHorizontal":"深色水平线","DarkUpwardDiagonal":"对角线向上","DarkVertical":"暗角垂直","DashedDownwardDiagonal":"对角线向下","DashedHorizontal":"水平虚线","DashedUpwardDiagonal":"向上对角线虚线","DashedVertical":"垂直虚线","DiagonalBrick":"对角斜带","DiagonalCross":"对角线十字","Divot":"草图","DottedDiamond":"虚线菱形","DottedGrid":"虚线网格","ForwardDiagonal":"正向对角线","Horizontal":"水平对角线","HorizontalBrick":"水平带","LargeCheckerBoard":"棋盘格","LargeConfetti":"颗粒","LargeGrid":"大网格","LightDownwardDiagonal":"向下对角线","LightHorizontal":"亮水平线","LightUpwardDiagonal":"向上对角线","LightVertical":"垂直","NarrowHorizontal":"窄水平线","NarrowVertical":"窄垂直线","OutlinedDiamond":"轮廓菱形","Percent05":"百分比5","Percent10":"百分比10","Percent20":"百分比20","Percent25":"百分比25","Percent30":"百分比30","Percent40":"百分比40","Percent50":"百分比50","Percent60":"百分比60","Percent70":"百分比70","Percent75":"百分比75","Percent80":"百分比80","Percent90":"百分比90","Plaid":"格子形状","Shingle":"方片形状","SmallCheckerBoard":"小型棋盘格","SmallConfetti":"小方片形状","SmallGrid":"小网格","SolidDiamond":"实心菱形","Sphere":"球形","Trellis":"格子","Vertical":"垂直线","Wave":"波浪线","Weave":"编织线","WideDownwardDiagonal":"宽下对角线","WideUpwardDiagonal":"宽向上对角线","ZigZag":"Z字形"},"data_visualizer_icon_set_type":{"3TrafficLights":"三态交通灯","Arrows":"彩色箭头","Blank":"空白","Checkbox":"选择状态","Flags":"三色旗","GrayArrows":"灰色箭头","Quarters":"五象限图","Ratings":"评分","RedToBlack":"红黑渐变","Signs":"三标符号","Symbols1":"符号-有圆圈","Symbols2":"符号-无圆圈","TrafficLights":"四态交通灯"},"data_visualizer_icon_set_value":{"false":"否","true":"是"},"data_visualizer_image_type":{"dataBar":"数据条","gradient":"渐变填充","hatch":"图案填充","iconSet":"图标集","rangeBar":"进度条"},"dataset_option":{"Auto":"自动","false":"否","true":"是"},"dataset_query_command_type":{"StoredProcedure":"存储过程","Text":"查询语句"},"direction":{"LTR":"从左到右（LTR）","RTL":"从右到左（RTL）"},"display_type":{"Galley":"不分页","Page":"单页视图"},"documentmap_numbering_style":{"CircledNumber":"①, ②, ③, ④, ⑤","Decimal":"1, 2, 3, 4, 5","DecimalLeadingZero":"01, 02, 03, 04, 05","Georgian":"ა, ბ, გ, დ, ე","Katakana":"ア, イ, ウ, エ, オ","KatakanaBrackets":"(ア), (イ), (ウ), (エ), (オ)","KatakanaIroha":"イ, ロ, ハ, ニ, ホ","KatakanaIrohaBrackets":"イ), ロ), ハ), ニ), ホ)","KatakanaIrohaLower":"ｨ, ﾛ, ﾊ, ﾆ, ﾎ","KatakanaLower":"ｱ, ｲ, ｳ, ｴ, ｵ","LowerArmenian":"ա, բ, գ, դ, ե","LowerGreek":"α, β, γ, δ, ε","LowerLatin":"a, b, c, d, e","LowerRoman":"i, ii, iii, iv, v","None":"无","UpperArmenian":"Ա, Բ, Գ, Դ, Ե","UpperGreek":"Α, Β, Γ, Δ, Ε","UpperLatin":"A, B, C, D, E","UpperRoman":"I, II, III, IV, V"},"documentmap_source":{"All":"全部","Headings":"标题","Labels":"标签","None":"无"},"dvchart_aggregate_type":{"Average":"平均值","Count":"计数","CountDistinct":"计数不重复","CountOfAll":"计数（全部）","List":"列表","Max":"最大值","Min":"最小值","None":"无","PopulationStandardDeviation":"总标准偏差","PopulationVariance":"总方差","Range":"范围","StandardDeviation":"标准方差","Sum":"求和","Variance":"方差"},"dvchart_axis_date_mode":{"Day":"天","Default":"默认","Hour":"时","Millisecond":"毫秒","Minute":"分","Month":"月","Second":"秒","Week":"周","Year":"年"},"dvchart_axis_overlapping_labels":{"Auto":"按空间尽量显示","Show":"始终全部显示"},"dvchart_axis_position":{"Far":"较远","Near":"较近","None":"无"},"dvchart_axis_type":{"X":"X","Y":"Y"},"dvchart_clipping_mode":{"Clip":"裁剪显示","Fit":"尽量显示","None":"无"},"dvchart_encoding_field_type":{"Complex":"高级模式","Simple":"简单模式"},"dvchart_encoding_sort":{"Ascending":"升序","Descending":"降序","None":"无"},"dvchart_group_type":{"Cluster":"聚簇","None":"无","Stack":"堆积"},"dvchart_group_type_wizard":{"Cluster":"聚簇","PercentStacked":"百分比堆栈","Stacked":"堆积"},"dvchart_halign":{"Center":"居中","Left":"左对齐","Right":"右对齐"},"dvchart_legend_position":{"Bottom":"底部","Left":"左侧","Right":"右侧","Top":"顶部"},"dvchart_line_aspect":{"Default":"折线","Spline":"平滑曲线","StepCenter":"阶梯居中","StepLeft":"阶梯靠左","StepRight":"阶梯靠右"},"dvchart_line_position":{"Auto":"自动","Center":"居中"},"dvchart_orientation":{"Horizontal":"水平","Vertical":"垂直"},"dvchart_overlapping_labels":{"Auto":"按空间尽量显示","Hide":"隐藏","Show":"始终全部显示"},"dvchart_palette":{"Aspect":"暗色系","Blue":"蓝色系","Blue2":"蓝色系2","BlueGreen":"蓝绿色系","BlueWarm":"暖蓝色系","Cerulan":"蔚蓝色系","Cocoa":"冷色系","Coral":"亮色系","Custom":"自定义","Cyborg":"玫红色希","Dark":"灰色希","Darkly":"深色系","Flatly":"扁平化色系","Grayscale":"深灰色系","Green":"绿色系","GreenYellow":"黄绿色调系","HighContrast":"高对比度色系","Light":"浅色系","Marquee":"聚焦色系","Median":"通用色系","Midnight":"亮色系1","Modern":"时尚色系","Office":"Office风","Office2010":"Office2010","Orange":"桔色系","OrangeRed":"桔红色调","Organic":"经典色系","Paper":"冷色系1","Red":"红色调","RedOrange":"红桔色调","RedViolet":"枚红色调","Slate":"冷色系2","Slipstream":"跳跃色系","Standard":"标准色系","Superhero":"活跃色","Violet":"紫色系","Violet2":"紫色系2","Yellow":"黄色系","YellowOrange":"黄橙色系","Zen":"冷色调"},"dvchart_plot_overlay_aggregate_type":{"Average":"平均值","Count":"计数","Max":"最大值","Median":"中位数","Min":"最小值","Percentile":"百分比","Sum":"求和"},"dvchart_plot_overlay_axis":{"X":"X轴","Y":"Y轴"},"dvchart_plot_overlay_detail_level":{"Group":"分组","Total":"总计"},"dvchart_plot_overlay_display":{"Back":"背景色","Front":"前景色"},"dvchart_plot_overlay_type":{"CumulativeMovingAverageTrendline":"累积移动平均趋势线","ExponentialMovingAverageTrendline":"指数移动平均趋势线","ExponentialTrendline":"指数趋势线","FourierTrendline":"傅立叶趋势线","LinearTrendline":"线性趋势线","LogarithmicTrendline":"对数趋势线","MovingAnnualTotalTrendline":"移动年度总趋势线","MovingAverageTrendline":"移动平均趋势线","PolynomialTrendline":"多项式趋势线","PowerTrendline":"电力趋势线","ReferenceBand":"参考波段","ReferenceLine":"参考线","WeightedMovingAverageTrendline":"加权移动平均趋势线"},"dvchart_plot_rule_properties":{"BackgroundColor":"填充颜色","LabelTemplate":"标签内容","LineColor":"折线颜色","LineStyle":"折线样式","LineWidth":"折线粗细","SymbolBackgroundColor":"标记背景色","SymbolLineColor":"标记连接线颜色","SymbolLineStyle":"标记连接线样式","SymbolLineWidth":"标记连接线粗细","TooltipTemplate":"鼠标提示内容"},"dvchart_plot_type":{"Area":"面积图","Bar":"柱形图","Candlestick":"开盘-盘高-盘底-收盘图","HighLowOpenClose":"盘高-盘低-开盘-收盘图","Line":"折线图","Scatter":"散点图"},"dvchart_scale":{"Linear":"线性","Logarithmic":"对数","Ordinal":"序数","Percentage":"百分比"},"dvchart_show_nulls":{"Connected":"连接","Gaps":"显示为空白","Zeros":"显示为零"},"dvchart_symbol_shape":{"Auto":"Auto","Box":"方形","Dash":"虚线","Diamond":"菱形","Dot":"圆形","Plus":"加号","Triangle":"三角形","X":"X型"},"dvchart_target":{"Label":"数据标签","ToolTip":"鼠标提示"},"dvchart_template":{"PercentageCategory_p0":"分类百分比","PercentageDetail_p0":"明细百分比","categoryField":{"name":"分类字段名称","value":"分类字段数据"},"colorField":{"name":"颜色字段名称","value":"颜色字段数据"},"detailFields":{"name":"明细字段名称","value":"明细字段数据"},"shapeField":{"name":"形状字段名称","value":"形状字段数据"},"sizeField":{"name":"大小字段名称","value":"大小字段数据"},"valueField":{"name":"数值字段名称","value":"数值字段数据"}},"dvchart_text_position":{"Auto":"自动","Center":"居中","Inside":"内部","Outside":"外部"},"dvchart_tick_mark":{"Cross":"相交","Inside":"内部","None":"无","Outside":"外部"},"dvchart_valign":{"Bottom":"底部","Middle":"居中","Top":"顶部"},"end_cap":{"Arrow":"箭头","None":"无"},"font_size":{"10pt":"10pt","11pt":"11pt","12pt":"12pt","14pt":"14pt","16pt":"16pt","18pt":"18pt","20pt":"20pt","22pt":"22pt","24pt":"24pt","26pt":"26pt","28pt":"28pt","36pt":"36pt","48pt":"48pt","72pt":"72pt","8pt":"8pt","9pt":"9pt"},"font_style":{"Italic":"斜体","Normal":"普通"},"font_weight":{"Bold":"粗体","Bolder":"较粗","ExtraBold":"超粗","ExtraLight":"超细","Heavy":"胖型","Light":"细体","Lighter":"较细","Medium":"中等","Normal":"普通","SemiBold":"半粗","Thin":"瘦型"},"gradient_type":{"Center":"从内到外","DiagonalLeft":"左对角线","DiagonalRight":"右对角线","HorizontalCenter":"水平方向从内到外","LeftRight":"从左至右","None":"无","TopBottom":"从上至下","VerticalCenter":"垂直方向从内到外"},"grid_mode":{"Dots":"点","Lines":"线"},"grow_direction":{"Column":"垂直排列","ColumnReverse":"垂直方向倒序排列","Row":"水平排列","RowReverse":"水平方向倒序排列"},"horizontal_align":{"Center":"居中","Left":"居左","Right":"居右"},"image_repeat":{"NoRepeat":"不重复","Repeat":"重复","RepeatX":"横向重复","RepeatY":"纵向重复"},"image_sizing":{"AutoSize":"自动","Clip":"剪切","Fit":"缩放","FitProportional":"等比例缩放"},"image_source":{"Database":"数据库","Embedded":"内嵌","External":"外部"},"input_type":{"CheckBox":"复选框","Text":"文本框"},"keep_with_group":{"After":"与之后分组一同显示","Before":"与之前分组一同显示","None":"无"},"label_font_style":{"Bold":"粗体","Italic":"斜体","Regular":"正常","Strikeout":"删除线","Underline":"下划线"},"labels_text_orientation":{"Angled":"标签旋转","Auto":"自动","Horizontal":"水平","Rotated270":"旋转270度","Rotated90":"旋转90度","Stacked":"堆叠（Stacked）"},"language":{"Default":"默认","af-ZA":"阿非利堪斯语 - 南非","am-ET":"阿姆哈拉语 - 埃塞俄比亚","ar-AE":"阿拉伯语 - 阿联酋","ar-BH":"阿拉伯语 - 巴林","ar-DZ":"阿拉伯语 - 阿尔及利亚","ar-EG":"阿拉伯语 - 埃及","ar-IQ":"阿拉伯语 - 伊拉克","ar-JO":"阿拉伯语 - 约旦","ar-KW":"阿拉伯语 - 科威特","ar-LB":"阿拉伯语 - 黎巴嫩","ar-LY":"阿拉伯语 - 利比亚","ar-MA":"阿拉伯语 - 摩洛哥","ar-OM":"阿拉伯语 - 阿曼","ar-QA":"阿拉伯语 - 卡塔尔","ar-SA":"阿拉伯语 - 沙特阿拉伯","ar-SY":"阿拉伯语 - 叙利亚","ar-TN":"阿拉伯语 - 突尼斯","ar-YE":"阿拉伯语 - 也门","arn-CL":"马普切语 - 智利","as-IN":"阿萨姆语 - 印度","az-AZ-Cyrl":"阿塞拜疆语 (西里尔文) - 阿塞拜疆","az-AZ-Latn":"阿塞拜疆语 (拉丁文) - 阿塞拜疆","ba-RU":"巴什基尔语 - 俄罗斯","be-BY":"白俄罗斯语 - 白俄罗斯","bg-BG":"保加利亚语 - 保加利亚","bn-BD":"孟加拉语 - 孟加拉国","bn-IN":"孟加拉语 - 印度","bo-CN":"藏语 - 中国","br-FR":"布列塔尼语 - 法国","bs-Cyrl-BA":"波斯尼亚语 (西里尔文) - 波斯尼亚和黑塞哥维那","bs-Latn-BA":"波斯尼亚语 (拉丁文) - 波斯尼亚和黑塞哥维那","ca-ES":"加泰罗尼亚语 - 加泰罗尼亚","co-FR":"科西嘉语 - 法国","cs-CZ":"捷克语 - 捷克","cy-GB":"威尔士语 - 英国","da-DK":"丹麦语 - 丹麦","de-AT":"德语 - 奥地利","de-CH":"德语 - 瑞士","de-DE":"德语 - 德国","de-LI":"德语 - 列支敦士登","de-LU":"德语 - 卢森堡","div-MV":"迪维希语 - 马尔代夫","el-GR":"希腊语 - 希腊","en-AU":"英语 - 澳大利亚","en-BZ":"英语 - 伯利兹","en-CA":"英语 - 加拿大","en-CB":"英语 - 加勒比","en-GB":"英语 - 英国","en-IE":"英语 - 爱尔兰","en-IN":"英语 - 印度","en-JM":"英语 - 牙买加","en-MY":"英语 - 马来西亚","en-NZ":"英语 - 新西兰","en-PH":"英语 - 菲律宾","en-SG":"英语 - 新加坡","en-TT":"英语 - 特立尼达和多巴哥","en-US":"英语 - 美国","en-ZA":"英语 - 南非","en-ZW":"英语 - 津巴布韦","es-AR":"西班牙语 - 阿根廷","es-BO":"西班牙语 - 玻利维亚","es-CL":"西班牙语 - 智利","es-CO":"西班牙语 - 哥伦比亚","es-CR":"西班牙语 - 哥斯达黎加","es-DO":"西班牙语 - 多明尼加共和国","es-EC":"西班牙语 - 厄瓜多尔","es-ES":"西班牙语 - 西班牙","es-GT":"西班牙语 - 危地马拉","es-HN":"西班牙语 - 洪都拉斯","es-MX":"西班牙语 - 墨西哥","es-NI":"西班牙语 - 尼加拉瓜","es-PA":"西班牙语 - 巴拿马","es-PE":"西班牙语 - 秘鲁","es-PR":"西班牙语 - 波多黎各","es-PY":"西班牙语 - 巴拉圭","es-SV":"西班牙语 - 萨尔瓦多","es-US":"西班牙语 - 美国","es-UY":"西班牙语 - 乌拉圭","es-VE":"西班牙语 - 委内瑞拉","et-EE":"爱沙尼亚语 - 爱沙尼亚","eu-ES":"巴斯克语 - 巴斯克","fa-IR":"波斯语 - 伊朗","fi-FI":"芬兰语 - 芬兰","fil-PH":"菲律宾语 - 菲律宾","fo-FO":"法罗语 - 法罗群岛","fr-BE":"法语 - 比利时","fr-CA":"法语 - 加拿大","fr-CH":"法语 - 瑞士","fr-FR":"法语 - 法国","fr-LU":"法语 - 卢森堡","fr-MC":"法语 - 摩纳哥","fy-NL":"弗里西亚语 - 荷兰","ga-IE":"爱尔兰语 - 爱尔兰","gd-GB":"苏格兰高地凯尔特语 - 英国","gl-ES":"加利西亚语 - 加利西亚","gsw-FR":"阿尔萨斯语 - 法国","gu-IN":"古吉拉特语 - 印度","ha-Latn-NG":"豪萨语 (拉丁文) - 尼日利亚","he-IL":"希伯来语 - 以色列","hi-IN":"印地语 - 印度","hr-BA":"克罗地亚语 (拉丁文) - 波斯尼亚和黑塞哥维那","hr-HR":"克罗地亚语 - 克罗地亚","hu-HU":"匈牙利语 - 匈牙利","hy-AM":"亚美尼亚语 - 亚美尼亚","id-ID":"印度尼西亚语 - 印度尼西亚","ig-NG":"伊博语 - 尼日利亚","ii-CN":"彝语 - 中国","is-IS":"冰岛语 - 冰岛","it-CH":"意大利语 - 瑞士","it-IT":"意大利语 - 意大利","iu-Cans-CA":"因纽特语 (拼音) - 加拿大","iu-Latn-CA":"因纽特语 (拉丁文) - 加拿大","ja-JP":"日语 - 日本","ka-GE":"格鲁吉亚语 - 格鲁吉亚","kk-KZ":"哈萨克语 - 哈萨克斯坦","kl-GL":"格陵兰语 - 格陵兰","km-KH":"高棉语 - 柬埔寨","kn-IN":"卡纳拉语 - 印度","ko-KR":"朝鲜语 - 韩国","kok-IN":"贡根语 - 印度","ky-KG":"吉尔吉斯语 - 吉尔吉斯斯坦","lb-LU":"卢森堡语 - 卢森堡","lo-LA":"老挝语 - 老挝","lt-LT":"立陶宛语 - 立陶宛","lv-LV":"拉脱维亚语 - 拉脱维亚","mi-NZ":"毛利语 - 新西兰","mk-MK":"马其顿语 - 马其顿","ml-IN":"马拉雅拉姆语 - 印度","mn-MN":"蒙古语 (西里尔文) - 蒙古","mn-Mong-CN":"蒙古语 (旧蒙文) - 中国","mn-Mong-MN":"蒙古语 - 蒙古","moh-CA":"莫霍克语 - 莫霍克","mr-IN":"马拉塔语 - 印度","ms-BN":"马来语 - 文莱","ms-MY":"马来语 - 马来西亚","mt-MT":"马耳他语 - 马耳他","nb-NO":"挪威语 (书面挪威语) - 挪威","ne-NP":"Nepali - 尼泊尔","nl-BE":"荷兰语 - 比利时","nl-NL":"荷兰语 - 荷兰","nn-NO":"挪威语 (新挪威语) - 挪威","nso-ZA":"北梭托语 - 南非","oc-FR":"奥克西坦语 - 法国","or-IN":"奥里雅语 - 印度","pa-IN":"旁遮普语 - 印度","pl-PL":"波兰语 - 波兰","prs-AF":"达利语 - 阿富汗","ps-AF":"普什图语 - 阿富汗","pt-BR":"葡萄牙语 - 巴西","pt-PT":"葡萄牙语 - 葡萄牙","qut-GT":"K\'iche - 危地马拉","quz-BO":"克丘亚语 - 玻利维亚","quz-EC":"克丘亚语 - 厄瓜多尔","quz-PE":"克丘亚语 - 秘鲁","rm-CH":"罗曼什语 - 瑞士","ro-RO":"罗马尼亚语 - 罗马尼亚","ru-RU":"俄语 - 俄罗斯","rw-RW":"卢旺达语 - 卢旺达","sa-IN":"梵语 - 印度","sah-RU":"雅库特语 - 俄罗斯","se-FI":"北萨米语 - 芬兰","se-NO":"北萨米语 - 挪威","se-SE":"北萨米语 - 瑞典","si-LK":"僧伽罗语 - 斯里兰卡","sk-SK":"斯洛伐克语 - 斯洛伐克","sl-SI":"斯洛文尼亚语 - 斯洛文尼亚","sma-NO":"南萨米语 - 挪威","sma-SE":"南萨米语 - 瑞典","smj-NO":"吕勒萨米语 - 挪威","smj-SE":"吕勒萨米语 - 瑞典","smn-FI":"伊纳里萨米语 - 芬兰","sms-FI":"斯科尔特萨米 - 芬兰","sq-AL":"阿尔巴尼亚语 - 阿尔巴尼亚","sr-SP-Cyrl":"塞尔维亚语 (西里尔文) - 塞尔维亚","sr-SP-Latn":"塞尔维亚语 (拉丁文) - 塞尔维亚","sv-FI":"瑞典语 - 芬兰","sv-SE":"瑞典语 - 瑞典","sw-KE":"斯瓦希里语 - 肯尼亚","syr-SY":"叙利亚语 - 叙利亚","ta-IN":"泰米尔语 - 印度","te-IN":"泰卢固语 - 印度","tg-Cyrl-TJ":"塔吉克斯坦语 (西里尔文) - 塔吉克斯坦","th-TH":"泰语 - 泰国","tk-TM":"土库曼斯坦语 - 土库曼斯坦","tn-ZA":"茨瓦纳语 - 南非","tr-TR":"土耳其语 - 土耳其","tt-RU":"鞑靼语 - 俄罗斯","tzm-Latn-DZ":"塔马塞特语 (拉丁文) - 阿尔及利亚","ug-CN":"维吾尔语 - 中国","uk-UA":"乌克兰语 - 乌克兰","ur-PK":"乌尔都语 - 巴基斯坦","uz-UZ-Cyrl":"西里尔语 - 乌兹别克斯坦","uz-UZ-Latn":"拉丁语 - 乌兹别克斯坦","vi-VN":"越南语 - 越南","wee-DE":"下索布语 - 德国","wen-DE":"上索布语 - 德国","wo-SN":"沃洛夫语 - 塞内加尔","xh-ZA":"科萨语 - 南非","yo-NG":"约鲁巴语 - 尼日利亚","zh-CN":"中文 - 中国","zh-HK":"中文 - 香港特别行政区","zh-MO":"中文 - 澳门特别行政区","zh-SG":"中文 - 新加坡","zh-TW":"中文 - 台湾","zu-ZA":"祖鲁语 - 南非"},"layout_direction":{"LTR":"从左至右","RTL":"从右至左"},"layout_order":{"NOrder":"N-先行后列","ZOrder":"Z-先列后行"},"legend_layout":{"Column":"列式","Row":"行式","Table":"表格"},"legend_position":{"BottomCenter":"底部居中","BottomLeft":"底部靠左","BottomRight":"底部靠右","LeftBottom":"左下","LeftCenter":"左中","LeftTop":"左上","RightBottom":"右下","RightCenter":"右中","RightTop":"右上","TopCenter":"顶部居中","TopLeft":"顶部靠左","TopRight":"顶部靠右"},"line_control_style":{"DashDot":"点划相间线","DashDotDot":"划线后跟两点线","Dashed":"虚线","Dotted":"点虚线","Double":"双实线","None":"透明","Solid":"实线"},"marker_type":{"Auto":"自动","Circle":"圆形","Cross":"十字形","Diamond":"菱形","None":"无","Square":"方形","Triangle":"三角形"},"mime_type":{"image/bmp":"image/bmp","image/gif":"image/gif","image/jpeg":"image/jpeg","image/png":"image/png","image/x-emf":"image/x-emf","image/x-wmf":"image/x-wmf"},"new_page":{"Even":"偶数项换页","Next":"下一项换页","Odd":"奇数项换页"},"numeral_variant":{"1":"1","2":"2","3":"3","4":"4","5":"5","6":"6","7":"7"},"operator":{"Between":"介于（Between）","BottomN":"最后 N 项","BottomPercent":"最后 N% 项","Equal":"等于","GreaterThan":"大于","GreaterThanOrEqual":"大于或等于","In":"包含于","LessThan":"小于","LessThanOrEqual":"小于或等于","Like":"形如（Like）","NotEqual":"不等于","TopN":"最前 N 项","TopPercent":"最前 N% 项"},"order_by_condition":{"Label":"标签字段","None":"无","Value":"数据字段"},"orientation":{"Horizontal":"水平","Vertical":"垂直"},"overflow":{"Auto":"自动","Clip":"缩减","Grow":"增加","Scroll":"滚动"},"page_break":{"End":"结束位置","None":"无","Start":"开始位置","StartAndEnd":"开始与结束位置"},"page_break_with_between":{"Between":"介于（Between）","End":"结束位置","None":"无","Start":"开始位置","StartAndEnd":"开始与结束位置"},"page_orientation":{"Landscape":"横向","Portrait":"纵向"},"plot_type":{"Auto":"自动","Line":"线","Point":"点"},"projection_mode":{"Orthographic":"正射投影","Perspective":"透视"},"pve_datetime_range_type":{"Current":"当前","Last":"上一个","LastToDate":"上一个至今","Next":"下一个","ToDate":"至今"},"pve_datetime_range_type_second":{"Last":"上一秒","Next":"下一秒"},"pve_datetime_range_type_time":{"Current":"当前时间","Last":"上一个","Next":"下一个"},"pve_datetime_range_unit":{"Day":"天","Hour":"时","Minute":"分","Month":"月","Quarter":"季度","Second":"秒","Week":"周","Year":"年"},"repeat_blank_rows":{"FillGroup":"填充当前分组","FillPage":"填充当前页","None":"无"},"reportparameter_datatype":{"Boolean":"布尔型","Date":"日期型","DateTime":"日期时间型","Float":"浮点型","Integer":"整型","String":"字符串型"},"reportparts_property_type":{"boolean":"布尔","borderStyle":"边框样式","color":"颜色","float":"浮点","fontFamily":"字体库","fontSize":"字体大小","fontStyle":"字体样式","fontWeight":"字体粗细","integer":"整型","length":"长度","lineStyle":"线条样式","lineWidth":"行宽","string":"字符","textDecoration":"文字装饰"},"reportparts_sizemode":{"Fixed":"固定的","Resizable":"可调整大小"},"rich_text_markup":{"HTML":"HTML"},"shading":{"None":"无","Real":"真实","Simple":"简单"},"shape_styles":{"Ellipse":"椭圆形","Rectangle":"矩形","RoundRect":"圆角矩形"},"size_type":{"Default":"原始大小","FitToPage":"符合页面","FitToWidth":"符合宽度"},"sparkline_type":{"Area":"面积图","Columns":"柱形","Line":"折线","StackedBar":"堆积图","Whiskers":"盈亏"},"target_shape":{"Dot":"点","Line":"线","Square":"面"},"text_align":{"Center":"居中","General":"常规","Justify":"两端对齐","Left":"靠左","Right":"靠右"},"text_align_glcr":{"Center":"居中","General":"常规","Left":"靠左","Right":"靠右"},"text_decoration":{"DoubleUnderline":"双下划线","LineThrough":"删除线","None":"无","Overline":"上划线","Underline":"下划线"},"text_justify":{"Auto":"自动","Distribute":"两端对齐","DistributeAllLines":"两端对齐所有行"},"text_orientation":{"Auto":"自动","Horizontal":"水平","Rotated270":"旋转270度","Rotated90":"旋转90度","Stacked":"堆叠（Stacked）"},"tick_mark":{"Cross":"交叉","Inside":"内部","None":"无","Outside":"外部"},"title_position":{"Center":"居中","Far":"靠右","Near":"靠左"},"unicode_bidi":{"BidiOverride":"双向覆盖（BidiOverride）","Embed":"内嵌（Embed）","Normal":"普通"},"upright_in_vertical_text":{"Digits":"数字","DigitsAndLatinLetters":"数字和拉丁字母","None":"无"},"vertical_align":{"Bottom":"底端对齐","Middle":"垂直居中","Top":"顶端对齐"},"wrap_mode":{"CharWrap":"按字符换行","NoWrap":"不换行","WordWrap":"按单词换行"},"writing_mode":{"lr-tb":"左右上下","tb-rl":"上下右左"}}},{"lng":"zh","ns":"error","resources":{"errorLabel":"错误","expressionNode":{"argumentIsNotValid":"函数 \'{{functionName}}\' 的参数 \'{{argument}}\' 无效，详细说明： {{details}}","argumentValueNotFitFunctionValueDataType":"函数 \'{{functionName}}\' 的参数 \'{{argument}}\' 应该是以下类型 {{expectedTypes}} ，但现在设置的是 {{actualType}}。","argumentsShouldHaveSameDataType":"函数 \'{{functionName}}\' 的参数 {{arguments}} 应该有相同的数据类型。","typeMismatch":"数值 \'{{value}}\' 不能被转换为 {{expectedType}}."},"themes":{"textCannotLoad":"无法加载报表主题文件","textNoThemes":"当前报表中没有可用的主题样式，所以，我们无法为报表设置一种主题风格。","textNotFound":"无法找到报表中所使用的主题样式，主题名称为 \\"{{theme}}\\"。","textTotFoundOrInvalid":"报表所设置的主题 {{theme}} 未能找到，或者是主题样式内部格式不正确。","textUseDefault":"我们将使用默认的主题样式 \\"{{theme}}\\"。"},"upgradeReportSemanticModel":{"entityIsAbsent":"●  数据模型\'{{dataSetName}}\' 的最新版本中缺少实体 \'{{entityName}}\' 的定义","fieldAtQueryFilterIsAbsent":"● 数据模型 \'{{dataSetName}}\' 的最新版本中缺少字段 \'{{fieldName}}\' 的定义","fieldInControlGroupIsAbsent":"● 数据字段 \'{{fieldName}}\' 在 {{controlName}} 分组 {{groupName}} \'{{propertyLabel}}\' 属性缺失。","fieldInControlIsAbsent":"● 数据字段 \'{{fieldName}}\' 在 {{controlName}} \'{{propertyLabel}}\' 属性缺失。","fieldInReportIsAbsent":"● 数据字段 \'{{fieldName}}\' 在报表 \'{{propertyLabel}}\' 属性缺失。"}}},{"lng":"zh","ns":"expressionFields","resources":{"chart":{"Current":{"Category":{"description":"表示目标元素的“分类字段”值","example":"=Chart!CurrentCategory=1; =Chart!CurrentCategory>10; =Chart!CurrentCategory=\\"Value1\\";","label":"分类","syntax":"Chart!CurrentCategory <比较运算符> <比较数值>"},"Data":{"description":"表示目标元素的“数据字段”值","example":"=Chart!CurrentData=1; =Chart!CurrentData>10; =Chart!CurrentData=\\"Value1\\";","label":"数据","syntax":"Chart!CurrentData <比较运算符> <比较数值>"},"DataClose":{"description":"代表金融图表中的\'收盘\'值。","example":"=Chart!CurrentDataClose=1; =Chart!CurrentDataClose>10; =Chart!CurrentDataClose=\\"Value1\\"; ","label":"收盘","syntax":"Chart!CurrentDataClose <比较运算符> <比较数值>"},"DataHigh":{"description":"代表金融图表中的\'高\'值。","example":"=Chart!CurrentDataHigh=1; =Chart!CurrentDataHigh>10; =Chart!CurrentDataHigh=\\"Value1\\"; ","label":"盘高","syntax":"Chart!CurrentDataHigh <比较运算符> <比较数值>"},"DataLow":{"description":"代表金融图表中的\'低\'值。","example":"=Chart!CurrentDataLow=1; =Chart!CurrentDataLow>10; =Chart!CurrentDataLow=\\"Value1\\"; ","label":"盘低","syntax":"Chart!CurrentDataLow <比较运算符> <比较数值>"},"DataLower":{"description":"代表甘特图中的\'较小\'值。","example":"=Chart!CurrentDataLower=1; =Chart!CurrentDataLower>10; =Chart!CurrentDataLower=\\"Value1\\"; ","label":"较小值","syntax":"Chart!CurrentDataLower <比较运算符> <比较数值>"},"DataOpen":{"description":"代表金融图表中的\'开盘\'值。","example":"=Chart!CurrentDataOpen=1; =Chart!CurrentDataOpen>10; =Chart!CurrentDataOpen=\\"Value1\\"; ","label":"开盘","syntax":"Chart!CurrentDataOpen <比较运算符> <比较数值>"},"DataUpper":{"description":"代表甘特图中的\'较大\'值。","example":"=Chart!CurrentDataUpper=1; =Chart!CurrentDataUpper>10; =Chart!CurrentDataUpper=\\"Value1\\"; ","label":"较大值","syntax":"Chart!CurrentDataUpper <比较运算符> <比较数值>"},"Detail":{"description":"表示目标元素的“详细字段”值","example":"=Chart!CurrentDetail=1; =Chart!CurrentDetail>10; =Chart!CurrentDetail=\\"Value1\\";","label":"详细","syntax":"Chart!CurrentDetail <比较运算符> <比较数值>"},"label":"当前元素"},"Functions":{"CurrentValue":{"description":"代表制定绘图区上当前元素的值","example":"=CurrentValue(\\"plot1\\"); =CurrentValue(\\"plot1\\",\\"high\\"); ","label":"当前元素","syntax":"CurrentValue(\\"绘图区名称\\" , \\"可选的标识符(用于：盘高、盘底、开盘、收盘、较大值、较小值）\\")"},"NextValue":{"description":"代表制定绘图区上后一个元素的值","example":"=NextValue(\\"plot1\\"); =NextValue(\\"plot1\\",\\"high\\"); ","label":"后一个元素","syntax":"NextValue(\\"绘图区名称\\" , \\"可选的标识符(用于：盘高、盘底、开盘、收盘、较大值、较小值）\\")"},"PreviousValue":{"description":"代表制定绘图区上前一个元素的值","example":"=PreviousValue(\\"plot1\\"); =PreviousValue(\\"plot1\\",\\"high\\"); ","label":"前一个元素","syntax":"PreviousValue(\\"绘图区名称\\" , \\"可选的标识符(用于：盘高、盘底、开盘、收盘、较大值、较小值）\\")"},"label":"函数"},"Next":{"Category":{"description":"表示当前元素的后一个元素的“类别字段”值","example":"=Chart!NextCategory=1; =Chart!NextCategory>10; =Chart!NextCategory=\\"Value1\\";","label":"分类","syntax":"Chart!NextCategory <比较运算符> <比较数值>"},"Data":{"description":"表示当前元素的后一个元素的“数据字段”值","example":"=Chart!NextData=1; =Chart!NextData>10; =Chart!NextData=\\"Value1\\";","label":"数据","syntax":"Chart!NextData <比较运算符> <比较数值>"},"DataClose":{"description":"代表金融图表中的\'收盘\'值。","example":"=Chart!NextDataClose=1; =Chart!NextDataClose>10; =Chart!NextDataClose=\\"Value1\\"; ","label":"收盘","syntax":"Chart!NextDataClose <比较运算符> <比较数值>"},"DataHigh":{"description":"代表金融图表中的\'高\'值。","example":"=Chart!NextDataHigh=1; =Chart!NextDataHigh>10; =Chart!NextDataHigh=\\"Value1\\"; ","label":"盘高","syntax":"Chart!NextDataHigh <比较运算符> <比较数值>"},"DataLow":{"description":"代表金融图表中的\'低\'值。","example":"=Chart!NextDataLow=1; =Chart!NextDataLow>10; =Chart!NextDataLow=\\"Value1\\"; ","label":"盘低","syntax":"Chart!NextDataLow <比较运算符> <比较数值>"},"DataLower":{"description":"代表金融图表中的\'较小\'值。","example":"=Chart!NextDataLower=1; =Chart!NextDataLower>10; =Chart!NextDataLower=\\"Value1\\"; ","label":"较小值","syntax":"Chart!NextDataLower <比较运算符> <比较数值>"},"DataOpen":{"description":"代表金融图表中的\'开盘\'值。","example":"=Chart!NextDataOpen=1; =Chart!NextDataOpen>10; =Chart!NextDataOpen=\\"Value1\\"; ","label":"开盘","syntax":"Chart!NextDataOpen <比较运算符> <比较数值>"},"DataUpper":{"description":"代表金融图表中的\'较大\'值。","example":"=Chart!NextDataUpper=1; =Chart!NextDataUpper>10; =Chart!NextDataUpper=\\"Value1\\"; ","label":"较大值","syntax":"Chart!NextDataUpper <比较运算符> <比较数值>"},"Detail":{"description":"表示当前元素的后一个元素的“详细字段”值","example":"=Chart!NextDetail=1; =Chart!NextDetail>10; =Chart!NextDetail=\\"Value1\\";","label":"详细","syntax":"Chart!NextDetail <比较运算符> <比较数值>"},"label":"后一个元素"},"Previous":{"Category":{"description":"表示当前元素的前一个元素的“类别字段”值","example":"=Chart!PreviousCategory=1; =Chart!PreviousCategory>10; =Chart!PreviousCategory=\\"Value1\\";","label":"分类","syntax":"Chart!PreviousCategory <比较运算符> <比较数值>"},"Data":{"description":"表示当前元素的前一个元素的“数据字段”值","example":"=Chart!PreviousData=1; =Chart!PreviousData>10; =Chart!PreviousData=\\"Value1\\";","label":"数据","syntax":"Chart!PreviousData <比较运算符> <比较数值>"},"DataClose":{"description":"代表金融图表中的\'收盘\'值。","example":"=Chart!PreviousDataClose=1; =Chart!PreviousDataClose>10; =Chart!PreviousDataClose=\\"Value1\\"; ","label":"收盘","syntax":"Chart!PreviousDataClose <比较运算符> <比较数值>"},"DataHigh":{"description":"代表金融图表中的\'高\'值。","example":"=Chart!PreviousDataHigh=1; =Chart!PreviousDataHigh>10; =Chart!PreviousDataHigh=\\"Value1\\"; ","label":"盘高","syntax":"Chart!PreviousDataHigh <比较运算符> <比较数值>"},"DataLow":{"description":"代表金融图表中的\'低\'值。","example":"=Chart!PreviousDataLow=1; =Chart!PreviousDataLow>10; =Chart!PreviousDataLow=\\"Value1\\"; ","label":"盘低","syntax":"Chart!PreviousDataLow <比较运算符> <比较数值>"},"DataLower":{"description":"代表金融图表中的\'较小\'值。","example":"=Chart!PreviousDataLower=1; =Chart!PreviousDataLower>10; =Chart!PreviousDataLower=\\"Value1\\"; ","label":"较小值","syntax":"Chart!PreviousDataLower <比较运算符> <比较数值>"},"DataOpen":{"description":"代表金融图表中的\'开盘\'值。","example":"=Chart!PreviousDataOpen=1; =Chart!PreviousDataOpen>10; =Chart!PreviousDataOpen=\\"Value1\\"; ","label":"开盘","syntax":"Chart!PreviousDataOpen <比较运算符> <比较数值>"},"DataUpper":{"description":"代表金融图表中的\'较大\'值。","example":"=Chart!PreviousDataUpper=1; =Chart!PreviousDataUpper>10; =Chart!PreviousDataUpper=\\"Value1\\"; ","label":"较大值","syntax":"Chart!PreviousDataUpper <比较运算符> <比较数值>"},"Detail":{"description":"表示当前元素的前一个元素的“详细字段”值","example":"=Chart!PreviousDetail=1; =Chart!PreviousDetail>10; =Chart!PreviousDetail=\\"Value1\\";","label":"详细","syntax":"Chart!PreviousDetail <比较运算符> <比较数值>"},"label":"前一个元素"},"label":"图表"},"commonValues":{"info":{"currentDateTime":{"description":"显示当前日期和时间，可用于页眉和页脚。"},"pageNM":{"description":"显示当前页码（N）和总页数（M），可用于页眉和页脚。"},"pageNMCumulative":{"description":"显示当前的累计页码（N）和总页数（M），该常量适用于你设置了页面排序规则时的页码统计。"},"pageNMSection":{"description":"显示本函数所属区域的当前页码（N）和总页数（M），区域可能是一个报表，也可能是一个数据区域。"},"pageNumber":{"description":"显示当前页码，可用于页眉和页脚。"},"pageNumberCumulative":{"description":"显示当前累计页码，该常量适用于你设置了页面排序规则时的页码统计。"},"pageNumberSection":{"description":"显示本函数所属区域的当前页码，区域可能是一个报表，也可能是一个数据区域。"},"reportName":{"description":"显示该报表的名称。"},"textboxValue":{"description":"保存时该变量将替换为当前文本框值","example":"=Sum($$)","example_i11n":"{Sum($$$)}","label":"当前文本框值"},"totalPages":{"description":"显示总页数，可用于页眉和页脚。"},"totalPagesCumulative":{"description":"显示累计总页数，该常量适用于你设置了页面排序规则时的页码统计。"},"totalPagesSection":{"description":"显示本函数所属区域的总页数，区域可能是一个报表，也可能是一个数据区域。"},"userContext":{"description":"用于获取用户相关的上下文信息，比如：UserContext.GetValue(\\"name\\"), UserContext.NumberToWords(123)"},"userId":{"description":"显示查看本报表的用户ID。"},"userLanguage":{"description":"显示系统设定的用户界面语言名称。"}},"titles":{"label":"内置数据"}},"constants":{"dvchart_template":{"PercentageCategory_p0":{"description":"显示该数值字段在分类上的百分比"},"PercentageDetail_p0":{"description":"显示该数值字段在明细上的百分比"},"categoryField":{"name":{"description":"显示分类字段的字段名称"},"value":{"description":"显示分类字段的实际数值"}},"colorField":{"name":{"description":"显示颜色字段的字段名称"},"value":{"description":"显示颜色字段的实际数值"}},"detailFields":{"name":{"description":"显示明细字段的字段名称"},"value":{"description":"显示明细字段的实际数值"}},"shapeField":{"name":{"description":"显示形状字段的字段名称"},"value":{"description":"显示形状字段的实际数值"}},"sizeField":{"name":{"description":"显示大小字段的字段名称"},"value":{"description":"显示大小字段的实际数值"}},"valueField":{"name":{"description":"显示数据字段的字段名称"},"value":{"description":"显示数据字段的实际数值"}}},"titles":{"label":"常量数值"}},"dataSets":{"titles":{"label":"数据集"}},"documentMap":{"info":{"path":{"description":"返回文档目录级别的路径。","example":"=DocumentMap.Path & \\"标题1\\"","label":"路径"}},"titles":{"label":"文档结构"}},"functions":{"info":{"aggregate":{"aggregateIf":{"description":"如果布尔表达式符合给定条件，则计算指定表达式的集合函数值。","example":"=AggregateIf(Fields!Discontinued.Value = True, \\"Sum\\", Fields!InStock.Value)","label":"AggregateIf","syntax":"AggregateIf(<条件表达式>, <集合函数>, <集合函数参数>)"},"aggregateIfWithScope":{"description":"在指定作用域内，如果布尔表达式符合给定条件，则计算指定表达式的集合函数值。","example":"=AggregateIf(Fields!Discontinued.Value = True, \\"Sum\\", Fields!InStock.Value, \\"Category\\")","label":"AggregateIf (with scope)","syntax":"AggregateIf(<条件表达式>, <集合函数>, <集合函数参数>, <作用域>)"},"avg":{"description":"计算指定表达式中所有非空数值的平均数。","example":"=Avg(Fields!LifeExpentancy.Value)","label":"Avg","syntax":"Avg(<数值表达式>)"},"avgWithScope":{"description":"在指定作用域内，计算表达式中所有非空数值的平均数。","example":"=Avg(Fields!LifeExpentancy.Value, \\"GroupByCountry\\")","label":"Avg (with scope)","syntax":"Avg(<数值表达式>, <作用域>)"},"count":{"description":"计算指定表达式中所有非空数值的总个数。","example":"=Count(Fields!EmployeeID.Value)","label":"Count","syntax":"Count(<数值表达式>)"},"countDistinct":{"description":"计算指定表达式中所有非重复值的个数。","example":"=CountDistinct(Fields!OrderID.Value)","label":"CountDistinct","syntax":"CountDistinct(<数值表达式>)"},"countDistinctWithScope":{"description":"在指定作用域内，计算指定表达式中所有非重复值的个数。","example":"=CountDistinct(Fields!OrderID.Value, \\"GroupByCategory\\")","label":"CountDistinct (with scope)","syntax":"CountDistinct(<数值表达式>, <作用域>)"},"countRows":{"description":"计算记录行数。","example":"=CountRows()","label":"CountRows","syntax":"CountRows()"},"countRowsWithScope":{"description":"计算指定范围内的记录行数。","example":"=CountRows(\\"Title\\")","label":"CountRows (with scope)","syntax":"CountRows(<作用域>)"},"countWithScope":{"description":"在指定作用域内，计算指定表达式中所有非空数值的总个数。","example":"=Count(Fields!EmployeeID.Value, \\"Title\\")","label":"Count (with scope)","syntax":"Count(<数值表达式>, <作用域>)"},"crossAggregate":{"description":"计算指定行和列分组对应单元格的数值，计算公式由给定的聚合函数名称决定，比如：Sum、Count等","example":"=CrossAggregate(Fields!Amount.Value, \\"Sum\\", \\"YearGroup\\", \\"CategoryGroup\\")","label":"CrossAggregate","syntax":"CrossAggregate(<表达式>, <聚合函数>, <列分组名称>, <行分组名称>)"},"cumulativeTotal":{"description":"计算当前页面级别的数据统计结果。","example":"=CumulativeTotal(Fields!OrderID.Value, \\"Count\\")","label":"CumulativeTotal","syntax":"CumulativeTotal(<数值表达式>, <聚合函数>)"},"distinctSum":{"description":"当一个表达式的数值不重复时，计算另一表达式的总和。","example":"=DistinctSum(Fields!OrderID.Value, Fields!OrderFreight.Value)","label":"DistinctSum","syntax":"DistinctSum(<非重复数值表达式>, <待汇总数值表达式>)"},"distinctSumWithScope":{"description":"在指定作用域内，当一个表达式的数值不重复时，计算另一表达式的总和。","example":"=DistinctSum(Fields!OrderID.Value, Fields!OrderFreight.Value, \\"Order\\")","label":"DistinctSum (with scope)","syntax":"DistinctSum(<非重复数值表达式>, <待汇总数值表达式>, <作用域>)"},"first":{"description":"返回指定表达式的第一个值。","example":"=First(Fields!ProductNumber.Value)","label":"First","syntax":"First(<数值表达式>)"},"firstWithScope":{"description":"在指定作用域内，返回指定表达式的第一个值。","example":"=First(Fields!ProductNumber.Value, \\"Category\\")","label":"First (with scope)","syntax":"First(<数值表达式>, <作用域>)"},"last":{"description":"返回指定表达式的最后一个值。","example":"=Last(Fields!ProductNumber.Value)","label":"Last","syntax":"Last(<数值表达式>)"},"lastWithScope":{"description":"在指定作用域内，返回指定表达式的最后一个值。","example":"=Last(Fields!ProductNumber.Value, \\"Category\\")","label":"Last (with scope)","syntax":"Last(<数值表达式>, <作用域>)"},"max":{"description":"返回指定表达式中所有非空数值的最大值。","example":"=Max(Fields!OrderTotal.Value)","label":"Max","syntax":"Max(<数值表达式>)"},"maxWithScope":{"description":"在指定作用域内，返回指定表达式中所有非空数值的最大值。","example":"=Max(Fields!OrderTotal.Value, \\"Year\\")","label":"Max (with scope)","syntax":"Max(<数值表达式>, <作用域>)"},"median":{"description":"返回指定表达式中所有数值的中位数。","example":"=Median(Fields!OrderTotal.Value)","label":"Median","syntax":"Median(<数值表达式>)"},"medianWithScope":{"description":"在指定作用域内，返回指定表达式中所有数值的中位数。","example":"=Median(Fields!OrderTotal.Value, \\"Year\\")","label":"Median (with scope)","syntax":"Median(<数值表达式>, <作用域>)"},"min":{"description":"返回指定表达式中所有非空数值的最小值。","example":"=Min(Fields!OrderTotal.Value)","label":"Min","syntax":"Min(<数值表达式>)"},"minWithScope":{"description":"在指定作用域内，返回指定表达式中所有非空数值的最小值。","example":"=Min(Fields!OrderTotal.Value, \\"Year\\")","label":"Min (with scope)","syntax":"Min(<数值表达式>, <作用域>)"},"mode":{"description":"返回指定表达式中重复次数最多的数值。","example":"=Mode(Fields!OrderTotal.Value)","label":"Mode","syntax":"Mode(<数值表达式>)"},"modeWithScope":{"description":"在指定作用域内，返回指定表达式中重复次数最多的数值。","example":"=Mode(Fields!OrderTotal.Value, \\"Year\\")","label":"Mode (with scope)","syntax":"Mode(<数值表达式>, <作用域>)"},"runningValue":{"description":"以另一集合函数为参数，计算指定表达式的累进集合函数值。","example":"=RunningValue(Fields!Price.Value, \\"Sum\\")","label":"RunningValue","syntax":"RunningValue(<数值表达式>, <集合函数>)"},"runningValueWithScope":{"description":"在指定作用域内，以另一集合函数为参数，计算指定表达式的累进集合函数值。","example":"=RunningValue(Fields!Price.Value, \\"Sum\\", \\"Nwind\\")","label":"RunningValue (with scope)","syntax":"RunningValue(<数值表达式>, <集合函数>, <作用域>)"},"stDev":{"description":"计算指定表达式所有非空数值的标准差。","example":"=StDev(Fields!LineTotal.Value)","label":"StDev","syntax":"StDev(<数值表达式>)"},"stDevP":{"description":"计算指定表达式所有非空数值的总体标准差。","example":"=StDevP(Fields!LineTotal.Value)","label":"StDevP","syntax":"StDevP(<数值表达式>)"},"stDevPWithScope":{"description":"在指定作用域内，计算指定表达式所有非空数值的总体标准差。","example":"=StDevP(Fields!LineTotal.Value, \\"Order\\")","label":"StDevP (with scope)","syntax":"StDevP(<数值表达式>, <作用域>)"},"stDevWithScope":{"description":"在指定作用域内，计算指定表达式所有非空数值的标准差。","example":"=StDev(Fields!LineTotal.Value, \\"Nwind\\")","label":"StDev (with scope)","syntax":"StDev(<数值表达式>, <作用域>)"},"sum":{"description":"计算指定表达式所有数值的总和。","example":"=Sum(Fields!Price.Value)","label":"Sum","syntax":"Sum(<数值表达式>)"},"sumWithScope":{"description":"在指定作用域内，计算指定表达式所有数值的总和。","example":"=Sum(Fields!Price.Value, \\"Category\\")","label":"Sum (with scope)","syntax":"Sum(<数值表达式>, <作用域>)"},"var":{"description":"计算指定表达式所有非空数值的方差（标准差的平方）。","example":"=Var(Fields!LineTotal.Value)","label":"Var","syntax":"Var(<数值表达式>)"},"varP":{"description":"计算指定表达式所有非空数值的总体方差（总体标准差的平方）。","example":"=VarP(Fields!LineTotal.Value)","label":"VarP","syntax":"VarP(<数值表达式>)"},"varPWithScope":{"description":"在指定作用域内，计算指定表达式所有非空数值的总体方差（总体标准差的平方）。","example":"=VarP(Fields!LineTotal.Value, \\"Order\\")","label":"VarP (with scope)","syntax":"VarP(<数值表达式>, <作用域>)"},"varWithScope":{"description":"在指定作用域内，计算指定表达式所有非空数值的方差（标准差的平方）。","example":"=Var(Fields!LineTotal.Value, \\"Order\\")","label":"Var (with scope)","syntax":"Var(<数值表达式>, <作用域>)"}},"conversion":{"format":{"description":"将数值按照格式化字符串的设置进行转换，最终返回相应的格式化之后的字符串。比如：Format(0.23645, \\"0.00%\\")将返回23.65%。","example":"=Format(Fields!OrderDate.Value, \\"dd MMM yyyy\\")","label":"Format","syntax":"Format(<数值表达式>, <格式字符串>)"},"toBoolean":{"description":"将一个数值或字符串变成布尔值（真/假）。所有非0数值，无论正负，都会转换成真。","example":"=ToBoolean(Fields!HouseOwnerFlag.Value)","label":"ToBoolean","syntax":"ToBoolean(<表达式>)"},"toByte":{"description":"将参数转换为一个0-255的字节数值。","example":"=ToByte(Fields!ProductNumber.Value)","label":"ToByte","syntax":"ToByte(<表达式>)"},"toChar":{"description":"将一个数值转换成对应ASCII值的字符，例如ToChar(65)返回A。","example":"=ToChar(Fields!OrderStatus.Value); =ToChar(“Hello”))","label":"ToChar","syntax":"ToChar(<表达式>)"},"toDateTime":{"description":"将一个字符串（如\\"2017-1-1 23:59:59\\"）转换成一个日期时间型数据。","example":"=ToDateTime(Fields!SaleDate.Value); =ToDateTime(\\"1 January, 2017\\")","label":"ToDateTime","syntax":"ToDateTime(<表达式>)"},"toDecimal":{"description":"将一个字符串或数值转换成带小数位的数值。","example":"=ToDecimal(Fields!Sales.Value)","label":"ToDecimal","syntax":"ToDecimal(<表达式>)"},"toDouble":{"description":"将一个字符串或数值转换成双精度的数值。","example":"=ToDouble(Fields!AnnualSales.Value); =ToDouble(535.85 * .2691 * 67483)","label":"ToDouble","syntax":"ToDouble(<表达式>)"},"toInt16":{"description":"将一个字符串或数值转换成2字节的带符号整数值。","example":"=ToInt16(Fields!AnnualSales.Value); =ToInt16(535.85)","label":"ToInt16","syntax":"ToInt16(<表达式>)"},"toInt32":{"description":"将一个字符串或数值转换成4字节的带符号整数值。","example":"=ToInt32(Fields!AnnualSales.Value)","label":"ToInt32","syntax":"ToInt32(<表达式>)"},"toInt64":{"description":"将一个字符串或数值转换成8字节的带符号整数值。","example":"=ToInt64(Fields!AnnualSales.Value)","label":"ToInt64","syntax":"ToInt64(<表达式>)"},"toSingle":{"description":"将一个字符串或数值转换成单精度浮点数。","example":"=ToSingle(Fields!AnnualSales.Value); =ToSingle(15.*********)","label":"ToSingle","syntax":"ToSingle(<表达式>)"},"toStringDot":{"description":"将一个数值转换为格式化之后的字符串，比如：\\"0.85642\\".ToString(\\"0.00%\\")将返回 85.64%。","example":"=Fields!OrderDate.Value.ToString(\\"dd MMM yyyy\\")","label":".ToString","syntax":"<数值表达式>.ToString(<格式字符串>)"},"toStringKey":{"description":"将任意类型数据转换为一个字符串。对象型参数的转换结果为\\"Object\\"。","example":"=ToString(Fields!YearlyIncome.Value); =ToString(13.50)","label":"ToString","syntax":"ToString(<表达式>)"},"toUInt16":{"description":"将一个字符串或数值转换成2字节的无符号整数值。","example":"=ToUInt16(Fields!AnnualSales.Value)","label":"ToUInt16","syntax":"ToUInt16(<表达式>)"},"toUInt32":{"description":"将一个字符串或数值转换成4字节的无符号整数值。","example":"=ToUInt32(Fields!AnnualSales.Value)","label":"ToUInt32","syntax":"ToUInt32(<表达式>)"},"toUInt64":{"description":"将一个字符串或数值转换成8字节的无符号整数值。","example":"=ToUInt64(Fields!AnnualSales.Value)","label":"ToUInt64","syntax":"ToUInt64(<表达式>)"}},"customFunctions":{"getValue":{"description":"获取报表访问者的上下文信息，比如：通过\\"name\\"获取用户名,\\"email\\"获取邮箱","example":"=UserContext.getValue(\\"name\\")","label":"GetUserValue","syntax":"UserContext.getValue(<上下文关键字>)"},"numberToWords":{"description":"将阿拉伯数字转换为中/英/日文对于的大写文字，比如：\\"123\\"将转为\\"壹佰贰拾叁\\"。该函数的第二个参数是可选的，不填写时自动判断操作系统的语言进行转换，也可直接指定需要的语言，包括：\\"zh-cn\\", \\"en-us\\" 和 \\"ja-jp\\"。 ","example":"=UserContext.NumberToWords(123.5); =UserContext.NumberToWords(981, \\"zh-CN\\")","label":"NumberToWords","syntax":"UserContext.NumberToWords(<数字>, <语言字符串>)"}},"dateTime":{"addDays":{"description":"在指定的日期时间基础上，再加/减指定的天数。比如，\\"2019/08/06\\".AddDays(2)将返回2019/08/08；\\"2019/08/06\\".AddDays(-3)将返回2019/08/03。","example":"=Fields!OrderDate.Value.AddDays(5)","label":"AddDays","syntax":"<DateTime>.AddDays(<增减天数>)"},"addHours":{"description":"在指定的日期时间基础上，再加/减指定的小时数。比如，\\"2019/08/06 15:28:02\\".AddHours(2)将返回2019/08/06 17:28:02；\\"2019/08/06 15:28:02\\".AddHours(-3)将返回2019/08/06 12:28:02。","example":"=Fields!OrderDate.Value.AddHours(12)","label":"AddHours","syntax":"<DateTime>.AddHours(<增减小时数>)"},"addMilliseconds":{"description":"在指定的日期时间基础上，再加/减指定的毫秒数。","example":"=Fields!OrderDate.Value.AddMilliseconds(500)","label":"AddMilliseconds","syntax":"<DateTime>.AddMilliseconds(<增减毫秒数>)"},"addMinutes":{"description":"在指定的日期时间基础上，再加/减指定的分钟数。","example":"=Fields!OrderDate.Value.AddMinutes(30)","label":"AddMinutes","syntax":"<DateTime>.AddMinutes(<增减分钟数>)"},"addMonths":{"description":"在指定的日期时间基础上，再加/减指定的月数。比如，\\"2019/08/06\\".AddMonths(2)将返回2019/10/06。","example":"=Fields!OrderDate.Value.AddMonths(2)","label":"AddMonths","syntax":"<DateTime>.AddMonths(<增减月数>)"},"addSeconds":{"description":"在指定的日期时间基础上，再加/减指定的秒数。比如，\\"2019/08/06 15:28:02\\".AddSeconds(2)将返回2019/08/06 15:28:04。","example":"=Fields!OrderDate.Value.AddSeconds(30)","label":"AddSeconds","syntax":"<DateTime>.AddSeconds(<增减秒数>)"},"addYears":{"description":"在指定的日期时间基础上，再加/减指定的年数。比如，\\"2019/08/06\\".AddYears(2)将返回2021/08/06。","example":"=Fields!OrderDate.Value.AddYears(3)","label":"AddYears","syntax":"<DateTime>.AddYears(<增减年数>)"},"dateAdd":{"description":"返回一个日期值增减一个指定单位时间间隔的结果。如DataAdd(\\"d\\",-1,\\"2017-1-1\\")表示在2017-1-1基础上增加-1天，返回值为\\"2016-12-31\\"。","example":"=DateAdd(\\"d\\", 5, Fields!SaleDate.Value); =DateAdd(DateInterval.Day, 5, Fields!SaleDate.Value)","label":"DateAdd","syntax":"DateAdd(<时间单位>, <增减数值>, <日期时间表达式>)"},"dateDiff":{"description":"返回一个长整型（Long）值，该值为两个日期值之间的时间间隔数。如DateDiff(\\"yyyy\\",\\"2016-12-31\\",\\"2017-1-1\\")返回值为1，表示两个日期值之间的年份差值为1。","example":"=DateDiff(\\"yyyy\\", Fields!SaleDate.Value, \\"1/1/2015\\"); =DateDiff(DateInterval.Year, Fields!SaleDate.Value, \\"1/1/2015\\")","label":"DateDiff","syntax":"DateDiff(<时间单位>, <日期时间值1>, <日期时间值2>[, <周内某天0-6>[, <年中某周1-53>]])"},"datePart":{"description":"返回一个整型（Integer）值，其中包含给定 Date 值的指定部分（年，月，日，时，分，秒，毫秒，星期几等）。","example":"=DatePart(\\"m\\", Fields!SaleDate.Value)","label":"DatePart","syntax":"DatePart(<时间单位>, <日期时间值>[, <一周的第一天>[, <一年的第一周>]])"},"dateSerial":{"description":"返回表示指定年月日的日期时间值，其时间信息被设置为午夜 (00:00:00)。","example":"=DateSerial(DatePart(\\"yyyy\\", Fields!SaleDate.Value) - 10, DatePart(\\"m\\", Fields!SaleDate.Value) + 5, DatePart(\\"d\\", Fields!SaleDate.Value) - 1)","label":"DateSerial","syntax":"DateSerial(<年度数字>, <月份数字>, <日期数字>)"},"dateString":{"description":"返回当前日期按当前区域设定中的日期格式显示的字符串。","example":"=DateString(); =DatePart(\\"m\\", DateString())","label":"DateString","syntax":"DateString()"},"dateValue":{"description":"返回一个 Date 值，该值包含用字符串表示的日期信息，其时间信息设置为午夜 (00:00:00)。例如：DateValue(\\"2012-12-31\\")。","example":"=DateValue(\\"December 12, 2015\\")","label":"DateValue","syntax":"DateValue(<日期形式的字符串>)"},"day":{"description":"返回日期值的\\"日\\"（1-31）。","example":"=Day(Fields!SaleDate.Value)","label":"Day","syntax":"Day(<日期时间>)"},"hour":{"description":"返回时间值的小时部分（0-23）。","example":"=Hour(Fields!SaleDate.Value)","label":"Hour","syntax":"Hour(<日期时间>)"},"minute":{"description":"返回时间值的分钟部分（0-59）。","example":"=Minute(Fields!SaleDate.Value)","label":"Minute","syntax":"Minute(<日期时间>)"},"month":{"description":"返回日期值的月份部分（1-12）。","example":"=Month(Fields!SaleDate.Value)","label":"Month","syntax":"Month(<日期时间>)"},"monthName":{"description":"返回月份数的月份名称，例如：MonthName(3)返回\\"三月\\"。","example":"=MonthName(Fields!MonthNumber.Value)","label":"MonthName","syntax":"MonthName(<月份数字>[, <是否简写>])"},"now":{"description":"返回当前的系统日期时间值。","example":"=Now()","label":"Now","syntax":"Now()"},"parse":{"description":"将一个日期时间值，返回为指定的格式。","example":"=DateTime.Parse(\\"01/01/1970\\")","label":"DateTime.Parse","syntax":"DateTime.Parse(<格式字符串>[, <String>])"},"quarter":{"description":"返回日期值的季度部分（1-4）。","example":"=Quarter(Fields!SaleDate.Value)","label":"Quarter","syntax":"Quarter(<日期时间>)"},"quarterName":{"description":"返回季度数的季度名称，例如：QuarterName(2020/01/05)返回\\"一季度\\"。","example":"=QuarterName(Fields!SaleDate.Value)","label":"QuarterName","syntax":"QuarterName(<日期时间>)"},"second":{"description":"返回日期时间值的秒（0-59）。","example":"=Second(Fields!SaleDate.Value)","label":"Second","syntax":"Second(<日期时间>)"},"timeOfDay":{"description":"返回当前系统时间的时间部分，返回值不带日期部分，显示为字符串形式为\\"0001/1/1 9:55:36\\"。","example":"=TimeOfDay()","label":"TimeOfDay","syntax":"TimeOfDay()"},"timeSerial":{"description":"返回一个日期时间值，该值表示指定的小时、分钟和秒，其日期信息设置基点为公元元年 1 月 1 日，如TimeSerial(23,45,58)。","example":"=TimeSerial(DatePart(\\"h\\", Fields!SaleDate.Value), DatePart(\\"n\\", Fields!SaleDate.Value), DatePart(\\"s\\", Fields!SaleDate.Value))","label":"TimeSerial","syntax":"TimeSerial(<小时数字>, <分钟数字>, <秒数字>)"},"timeString":{"description":"返回当前时间的字符串形式，如10:17:43。","example":"=TimeString()","label":"TimeString","syntax":"TimeString()"},"timeValue":{"description":"返回一个日期型数据的时间值。如TimeValue(Now)返回\\"0001/1/1 10:19:00\\"。","example":"=TimeValue(\\"15:25:45\\"); =TimeValue(Fields!SaleDate.Value)","label":"TimeValue","syntax":"TimeValue(<日期时间>)"},"timer":{"description":"返回一个双精度数值，表示从0时到现在的时间，单位是秒，如 36472.149871。","example":"=Timer()","label":"Timer","syntax":"Timer()"},"today":{"description":"返回当前系统日期。","example":"=Today()","label":"Today","syntax":"Today()"},"weekday":{"description":"返回代表一星期中某天的整数，如Weekday(Today)返回7，表示星期六，如果返回1表示星期日。","example":"=Weekday(Fields!SaleDate.Value, 0)","label":"Weekday","syntax":"Weekday(<日期时间>[, <一周第一天>])"},"weekdayName":{"description":"返回代表一星期中某天的星期几名称。","example":"=WeekdayName(3, True, 0); =WeekDayName(DatePart(\\"w\\", Fields!SaleDate.Value), True, 0)","label":"WeekdayName","syntax":"WeekdayName(<星期几的数字>[, <是否简写>[, <一周第一天>]])"},"year":{"description":"返回一个日期型数据的年度值（1-9999）。","example":"=Year(Fields!SaleDate.Value)","label":"Year","syntax":"Year(<日期时间>)"}},"inspection":{"dbNull":{"description":"检查当前数值是否为 DBNull.","example":"=IIF(Fields!Organization.Value is DBNull.Value, \\"<NULL>\\", Fields!Organization.Value)","label":"DBNull.Value","syntax":"DBNull.Value"},"isArray":{"description":"如果表达式是数组，返回True。","example":"=IsArray(Parameters!Initials.Value)","label":"IsArray","syntax":"IsArray(<表达式>)"},"isDBNull":{"description":"如果表达式是数据库NULL值，返回True。","example":"=IsDBNull(Fields!MonthlySales.Value)","label":"IsDBNull","syntax":"IsDBNull(<表达式>)"},"isDate":{"description":"如果表达式是日期时间值，返回True。","example":"=IsDate(Fields!BirthDate.Value); =IsDate(\\"31/12/2010\\")","label":"IsDate","syntax":"IsDate(<表达式>)"},"isError":{"description":"如果表达式是一个错误对象，返回True。","example":"=IsError(Fields!AnnualSales.Value = 80000)","label":"IsError","syntax":"IsError(<表达式>)"},"isNothing":{"description":"如果表达式是一个空引用指针对象，返回True。","example":"=IsNothing(Fields!MiddleInitial.Value)","label":"IsNothing","syntax":"IsNothing(<表达式>)"},"isNumeric":{"description":"如果表达式是一个数值型数据，返回True。","example":"=IsNumeric(Fields!AnnualSales.Value)","label":"IsNumeric","syntax":"IsNumeric(<表达式>)"}},"math":{"abs":{"description":"返回一个数值的绝对值。","example":"=Abs(-5.5); =Abs(Fields!YearlyIncome.Value - 80000)","label":"Abs","syntax":"Abs(<数值表达式>)"},"acos":{"description":"返回一个数值的反余弦函数值。","example":"=Acos(.5); =Acos(Fields!Angle.Value)","label":"Acos","syntax":"Acos(<数值表达式>)"},"asin":{"description":"返回一个数值的反正弦函数值。","example":"=Asin(.5); =Asin(Fields!Angle.Value)","label":"Asin","syntax":"Asin(<Number>)"},"atan":{"description":"返回一个数值的反正切函数值。","example":"=Atan(.5); =Atan(Fields!Angle.Value)","label":"Atan","syntax":"Atan(<数值表达式>)"},"atan2":{"description":"返回两个数值之商的反正切函数值。","example":"=Atan2(3,7); =Atan2(Fields!CoordinateY.Value, Fields!CoordinateX.Value)","label":"Atan2","syntax":"Atan2(<数值表达式1>, <数值表达式2>)"},"bigMul":{"description":"返回两个4字节整数的乘积。","example":"=BigMul(4294967295,-2147483647); =BigMul(Fields!Int32Value.Value, Fields!Int32Value.Value)","label":"BigMul","syntax":"BigMul(<数值表达式1>, <数值表达式2>)"},"ceiling":{"description":"返回不小于指定浮点数的最小整数。","example":"=Ceiling(98.4331); =Ceiling(Fields!AnnualSales.Value / 6)","label":"Ceiling","syntax":"Ceiling(<数值表达式>)"},"cos":{"description":"返回一个数值的余弦函数值。","example":"=Cos(60)","label":"Cos","syntax":"Cos(<数值表达式>)"},"cosh":{"description":"返回一个数值的双曲余弦函数值。","example":"=Cosh(60)","label":"Cosh","syntax":"Cosh(<数值表达式>)"},"e":{"description":"返回自然对数的底（欧拉数E）的数值，即2.71828182845905。","example":"=E * 2","label":"E","syntax":"E"},"exp":{"description":"返回欧拉数E的幂次方。本函数是Log函数的反函数。","example":"=Exp(3); =Exp(Fields!IntegerCounter.Value)","label":"Exp","syntax":"Exp(<数值表达式>)"},"fix":{"description":"返回将浮点数值直接舍弃小数部分的整数部分。","example":"=Fix(-7.15); =Fix(Fields!AnnualSales.Value / -5)","label":"Fix","syntax":"Fix(<数值表达式>)"},"floor":{"description":"返回不大于浮点数值的最大整数。","example":"=Floor(4.67); =Floor(Fields!AnnualSales.Value / 12)","label":"Floor","syntax":"Floor(<数值表达式>)"},"ieeeRemainder":{"description":"返回一指定数字被另一指定数字相除的余数，取余操作遵循IEEE标准。","example":"=IEEERemainder(9, 8)","label":"IEEERemainder","syntax":"IEEERemainder(<数值表达式1>, <数值表达式2>)"},"log":{"description":"返回一个数值的自然对数值。","example":"=Log(20.5); =Log(Fields!NumberValue.Value)","label":"Log","syntax":"Log(<数值表达式>)"},"log10":{"description":"返回一个数值的常用对数值（以10为底）。","example":"=Log10(20.5); =Log10(Fields!NumberValue.Value)","label":"Log10","syntax":"Log10(<数值表达式>)"},"max":{"description":"返回指定表达式中所有非空数值的最大值。","example":"=Max(Fields!OrderTotal.Value)","label":"Max","syntax":"Max(<数值表达式>)"},"min":{"description":"返回指定表达式中所有非空数值的最小值。","example":"=Min(Fields!OrderTotal.Value)","label":"Min","syntax":"Min(<数值表达式>)"},"pi":{"description":"返回圆周率PI的值，即3.14159265358979。","example":"=2 * PI * Fields!Radius.Value","label":"PI","syntax":"PI"},"pow":{"description":"返回数值的幂次方。","example":"=Pow(Fields!Quantity.Value, 2)","label":"Pow","syntax":"Pow(<数值表达式1>, <数值表达式2>)"},"round":{"description":"返回浮点参数值四舍五入的最邻近整数。","example":"=Round(12.456); =Round(Fields!AnnualSales.Value / 12.3)","label":"Round","syntax":"Round(<数值表达式>)"},"sign":{"description":"返回一个6位数字的正负号（-1或+1）。","example":"=Sign(Fields!AnnualSales.Value - 60000)","label":"Sign","syntax":"Sign(<数值表达式>)"},"sin":{"description":"返回一个数值的正弦函数值。","example":"=Sin(60)","label":"Sin","syntax":"Sin(<数值表达式>)"},"sinh":{"description":"返回一个数值的双曲正弦函数值。","example":"=Sinh(60)","label":"Sinh","syntax":"Sinh(<数值表达式>)"},"sqrt":{"description":"返回一个数值的平方根。","example":"=Sqrt(121)","label":"Sqrt","syntax":"Sqrt(<数值表达式>)"},"tan":{"description":"返回一个数值的正切函数值。","example":"=Tan(60)","label":"Tan","syntax":"Tan(<数值表达式>)"},"tanh":{"description":"返回一个数值的正切函数值。","example":"=Tanh(60)","label":"Tanh","syntax":"Tanh(<数值表达式>)"},"truncate":{"description":"删除小数点后的数字而不四舍五入，并返回整数值。","example":"=Truncate(Fields!UnitPrice.Value)","label":"Truncate","syntax":"Truncate(<Number>)"}},"miscellaneous":{"getFields":{"description":"返回一个IDictionary<string,Field>对象，包含当前字段列表的集合。仅在一个数据区域内有效。此函数可令复杂条件的表达式更易于编写。","example":"=GetFields(); =Code.DisplayAccountID(GetFields())","label":"GetFields","syntax":"GetFields()"},"getLength":{"description":"返回指定数组中的元素个数。","example":"=Parameters!MultiValueParameter.Value.GetLength(0)","label":"GetLength","syntax":"<Collection>.GetLength(<Number>)"},"groupIndex":{"description":"返回当前组中元素的索引。","example":"=GroupIndex()","label":"GroupIndex","syntax":"GroupIndex()"},"groupIndexWithScope":{"description":"返回指定组中元素的索引。","example":"=GroupIndex(Group1)","label":"GroupIndex (with scope)","syntax":"GroupIndex(<分组>)"},"inScope":{"description":"如果当前值在某个作用域内，返回True。","example":"=InScope(\\"Order\\")","label":"InScope","syntax":"InScope(<作用域>)"},"indexOf":{"description":"返回给定元素的第一个索引，如果不存在，则返回 -1。","example":"=IndexOf(Parameters!pContinent.Value, Fields!ContinentName.Value) >= 0","example_i11n":"{IndexOf(@pContinent, ContinentName) >= 0}","label":"IndexOf","syntax":"IndexOf(<Source>, <SearchElement>)"},"item":{"description":"通过名称，返回一个字段、参数或者报表元素","example":"=Fields.Item(\\"Company Name\\").Name, =Parameters.Item(\\"Parameter1\\").Name, =ReportItems.Item(\\"TextBox1\\").Value","example_i11n":"{Fields.Item(\\"Company Name\\").Name}; {Parameters.Item(\\"Parameter1\\").Name}; {ReportItems.Item(\\"TextBox1\\").Value}","label":"Item","syntax":"<Object | Record>.Item(<String>)"},"join":{"description":"连接指定数组的元素或集合的成员，在每个元素或成员之间使用指定的分隔符。","example":"=Join(Parameters!MultiValueParameter.Value, \\", \\")","label":"Join","syntax":"Join(<数组>, <分隔符>)"},"level":{"description":"返回当前值在层次结构中的级别。层次结构的第一级是0。","example":"=Level()","label":"Level","syntax":"Level()"},"levelWithScope":{"description":"在指定的作用域内，返回当前值在层次结构中的级别。层次结构的第一级是0。","example":"=Level(\\"Order\\")","label":"Level (with scope)","syntax":"Level(<作用域>)"},"lookup":{"description":"返回外键关联的另一数据集的指定字段值。","example":"=Lookup(Fields!ProductID.Value, Fields!ProductID.Value, Fields!Quantity.Value, \\"DataSet2\\")","label":"Lookup","syntax":"Lookup(<主数据集字段>, <关联数据集匹配字段>, <关联数据集数据字段>, <关联数据集名称>)"},"lookupSet":{"description":"返回外键关联的另一数据集的指定字段的一组匹配值。","example":"=LookupSet(Fields!ProductID.Value, Fields!ProductID.Value, Fields!Quantity.Value, \\"DataSet2\\")","label":"LookupSet","syntax":"LookupSet(<主数据集字段>, <关联数据集匹配字段>, <关联数据集数据字段>, <关联数据集名称>)"},"previous":{"description":"返回指定作用域中的前一个值，例如上一行记录的某一字段的值。","example":"=Previous(Fields!OrderID.Value)","label":"Previous","syntax":"Previous(<表达式>)"},"previousWithScope":{"description":"计算指定范围内上一行数据的表达式数值。","example":"=Previous(Fields!OrderID.Value, \\"Order\\")","label":"Previous (with scope)","syntax":"Previous(<表达式>, <作用域>)"},"rowNumber":{"description":"返回记录行号。","example":"=RowNumber()","label":"RowNumber","syntax":"RowNumber()"},"rowNumberWithScope":{"description":"返回指定作用域内的记录行号。","example":"=RowNumber(\\"OrderID\\")","label":"RowNumber (with scope)","syntax":"RowNumber(<作用域>)"}},"programFlow":{"choose":{"description":"以第一个参数为索引，返回后续参数中的一个。例如Choose(1,\\"a\\",\\"b\\")返回字符串\\"a\\"。","example":"=Choose(3, \\"10\\", \\"15\\", \\"20\\", \\"25\\")","label":"Choose","syntax":"Choose(<索引>, <表达式1>[, <表达式2>,...[, <表达式N>]])"},"iif":{"description":"根据第一个参数的结果逻辑值（真/假），返回第二个或第三个参数的值。","example":"=IIF(Fields!AnnualSales.Value >= 80000, \\"Above Average\\", \\"Below Average\\")","label":"IIF","syntax":"IIF(<条件表达式>, <TruePart>, <FalsePart>)"},"partition":{"description":"返回一个字符串，表示一个指定数值，出现在一个系列值中的哪一个区段。比如Partition(230,1,1000,100) 返回字符串“201:300”。其含义是：从1开始至1000为止，每隔100为一个区间，230落在201-300这个区间内。","example":"=Partition(1999, 1980, 2000, 10)","label":"Partition","syntax":"Partition(<整数表达式>, <区段起始值>, <区段结束值>, <区段间隔步长>)"},"switch":{"description":"计算一组表达式列表的值，然后返回与表达式列表中最先为真的表达式所相关的数值或表达式。","example":"=Switch(Fields!FirstName.Value = \\"Abraham\\", \\"Adria\\", Fields!FirstName.Value = \\"Charelotte\\", \\"Cherrie\\")","label":"Switch","syntax":"Switch(<条件表达式1>, <表达式1>[, <条件表达式2>, <表达式2>,...[, <条件表达式N>, <表达式N>]])"}},"text":{"contains":{"description":"判断主字符串中是否包含子字符串，包含返回true，不包含则返回false。","example":"=Fields!ShipAddress.Value.Contains(\\"street\\")","label":"Contains","syntax":"<主字符串>.Contains(<子字符串>)"},"endsWith":{"description":"判断主字符串的末尾是否以子字符串结束，是返回true，不是则返回false。","example":"=Fields!Description.Value.EndsWith(\\"documents\\")","label":"EndsWith","syntax":"<主字符串>.EndsWith(<子字符串>)"},"inStr":{"description":"返回指定子字符串在字符串中首次出现的起始位置","example":"=InStr(Fields!Description.Value, \\"documents\\")","label":"InStr","syntax":"InStr(<String>, <String>)"},"lastIndexOf":{"description":"判断子字符串在主字符串中最后一次出现的索引位置，从0位置开始查找，找到返回对应的索引位置，未找到则返回-1。","example":"=Fields!Description.Value.LastIndexOf(\\"documents\\")","label":"LastIndexOf","syntax":"<主字符串>.LastIndexOf(<子字符串>[, <搜索起始位置>])"},"replace":{"description":"在主字符串中，查找所有旧字符串，如果找到就使用新字符串进行替换，未找到就不做任何更改。","example":"=Fields!Description.Value.Replace(\\"documents\\", \\"invoices\\")","label":"Replace","syntax":"<主字符串>.Replace(<旧字符串>, <新字符串>)"},"startsWith":{"description":"判断主字符串的开始是否与子字符串一致，一致返回true，不一致则返回false。","example":"=Fields!Description.Value.StartsWith(\\"Invoice\\")","label":"StartsWith","syntax":"<主字符串>.StartsWith(<子字符串>)"},"substring":{"description":"从主字符串中返回一个子字符串，子字符串从指定的字符位置开始且具有指定的长度。","example":"=Fields!Description.Value.Substring(1, 10)","label":"Substring","syntax":"<主字符串>.Substring(<起始字符位置>, <字符长度>)"},"toLower":{"description":"将主字符串全部字符转换为小写形式。","example":"=Fields!ShipCountry.Value.ToLower()","label":"ToLower","syntax":"<主字符串>.ToLower()"},"toUpper":{"description":"将主字符串全部字符转换为大写形式。","example":"=Fields!ShipCountry.Value.ToUpper()","label":"ToUpper","syntax":"<主字符串>.ToUpper()"},"trim":{"description":"从主字符串的开头和结尾删除所有空白字符后剩余的字符串。","example":"=Parameters!Info.Value.Trim()","label":"Trim","syntax":"<主字符串>.Trim()"},"trimEnd":{"description":"从主字符串的结尾删除所有空白字符后剩余的字符串。","example":"=Parameters!Info.Value.TrimEnd()","label":"TrimEnd","syntax":"<主字符串>.TrimEnd()"},"trimStart":{"description":"从主字符串的开头删除所有空白字符后剩余的字符串。","example":"=Parameters!Info.Value.TrimStart()","label":"TrimStart","syntax":"<主字符串>.TrimStart()"}}},"titles":{"aggregate":"集合","conversion":"转换","dateTime":"日期","inspection":"检测","label":"常用函数","math":"数学","miscellaneous":"其他","programFlow":"流程","text":"文本"}},"operations":{"info":{"arithmetic":{"add":{"description":"计算两个数值的和或者串接两个字符串。","example":"=Fields!Quantity.Value + 2","label":"+","syntax":"<表达式1> + <表达式2>"},"divide":{"description":"执行除法，返回浮点数商。","example":"=Fields!AnnualSales.Value / 2","label":"/","syntax":"<表达式1> / <表达式2>"},"integerDivide":{"description":"执行除法，返回整数商。","example":"=Fields!AnnualSales.Value \\\\ 2","label":"\\\\","syntax":"<表达式1> \\\\ <表达式2>"},"mod":{"description":"返回两数相除的余数。","example":"=Fields!AnnualSales.Value Mod 12","label":"Mod","syntax":"<表达式1> Mod <表达式2>"},"multiply":{"description":"执行乘法，返回积。","example":"=Fields!Quantity.Value * 5","label":"*","syntax":"<表达式1> * <表达式2>"},"power":{"description":"求幂次方。","example":"=Fields!Quantity.Value ^ 2","label":"^","syntax":"<表达式1> ^ <表达式2>"},"subtract":{"description":"执行减法，返回差。","example":"=Fields!Quantity.Value - 2","label":"-","syntax":"<表达式1> - <表达式2>"}},"bitShift":{"leftShift":{"description":"执行左移，例如：4<<2 表示将数字4的二进制数形式（100）左移2位，成为二进制的10000，即十进制数的16。","example":"=Fields!RegionID.Value << 2","label":"<<","syntax":"<表达式1> << <表达式2>"},"rightShift":{"description":"执行右移，例如：4>>1表示将数字4的二进制数形式（100）右移1位，成为二进制的10，即十进制2。","example":"=Fields!RegionID.Value >> 2","label":">>","syntax":"<表达式1> >> <表达式2>"}},"comparison":{"equal":{"description":"如果左操作数等于右操作数，返回True。","example":"=Fields!AnnualSales.Value = 80000","label":"=","syntax":"<表达式1> = <表达式2>"},"greaterThan":{"description":"如果左操作数大于右操作数，返回True。","example":"=Fields!AnnualSales.Value > 80000","label":">","syntax":"<表达式1> > <表达式2>"},"greaterThanOrEqual":{"description":"如果左操作数大于或等于右操作数，返回True。","example":"=Fields!AnnualSales.Value >= 80000","label":">=","syntax":"<表达式1> >= <表达式2>"},"is":{"description":"比较两个对象引用，如果做操作数与右操作数是同一个对象，返回True。","example":"=Fields!FirstName.Value Is Fields!LastName.Value","label":"Is","syntax":"<表达式1> Is <表达式2>"},"like":{"description":"比较两个字符串，如果左操作数给定的字符串与右操作数指定的模式相匹配，返回True。","example":"=Fields!FirstName.Value Like \\"A*\\"","label":"Like","syntax":"<表达式1> Like <表达式2>"},"lowerThan":{"description":"如果左操作数小于右操作数，返回True。","example":"=Fields!AnnualSales.Value < 80000","label":"<","syntax":"<表达式1> < <表达式2>"},"lowerThanOrEqual":{"description":"如果左操作数小于或等于右操作数，返回True。","example":"=Fields!AnnualSales.Value <= 80000","label":"<=","syntax":"<表达式1> <= <表达式2>"},"notEqual":{"description":"如果左操作数不等于右操作数，返回True。","example":"=Fields!AnnualSales.Value <> 80000","label":"<>","syntax":"<表达式1> <> <表达式2>"}},"concatenation":{"add":{"description":"计算两个数值的和或者串接两个字符串。","example":"=Fields!FirstName.Value + \\" \\" + Fields!LastName.Value","label":"+","syntax":"<表达式1> + <表达式2>"},"concat":{"description":"串接两个字符串。","example":"=Fields!FirstName.Value & \\" \\" & Fields!LastName.Value","example_i11n":"{FirstName & \\" \\" & LastName}","label":"&","syntax":"<表达式1> & <表达式2>"}},"logicalBitwise":{"and":{"description":"返回两个布尔表达式的逻辑与操作结果，或者两个数值表达式的按位与操作结果。","example":"=(Fields!AnnualSales.Value > 80000) And (Fields!Quantity.Value > 5)","label":"And","syntax":"<表达式1> And <表达式2>"},"andAlso":{"description":"返回两个布尔表达式的逻辑与操作结果，如果第一个表达式为False则跳过第二个表达式的判断。","example":"=(Fields!AnnualSales.Value > 80000) AndAlso (Fields!Quantity.Value > 1)","label":"AndAlso","syntax":"<布尔表达式1> AndAlso <布尔表达式2>"},"not":{"description":"返回布尔表达式的逻辑取反结果，或者是数值表达式的按位取反结果。","example":"=Not (Fields!AnnualSales.Value > 80000)","label":"Not","syntax":"Not <表达式>"},"or":{"description":"返回两个布尔表达式的逻辑或操作结果，或者两个数值表达式的按位或操作结果。","example":"=(Fields!AnnualSales.Value > 80000) Or (Fields!Quantity.Value > 5)","label":"Or","syntax":"<表达式1> Or <表达式2>"},"orElse":{"description":"返回两个布尔表达式的逻辑或操作结果，如果第一个表达式为True则跳过第二个表达式的判断。","example":"=(Fields!AnnualSales.Value > 80000) OrElse (Fields!Quantity.Value > 1)","label":"OrElse","syntax":"<布尔表达式1> OrElse <布尔表达式2>"},"xor":{"description":"返回两个布尔表达式的逻辑异或操作结果，或者两个数值表达式的按位异或操作结果。","example":"=(Fields!AnnualSales.Value > 80000) Xor (Fields!Quantity.Value) > 5","label":"Xor","syntax":"<表达式1> Xor <表达式2>"}}},"titles":{"arithmetic":"算术","bitShift":"位移","comparison":"比较","concatenation":"连接","label":"位运算","logicalBitwise":"逻辑/位运算"}},"parameters":{"titles":{"label":"报表参数"}},"reportItems":{"titles":{"label":"报表元素"}},"reportPartProperties":{"info":{"example":"=PartProperties!<PropertyName>.Value","example_i11n":"{PartProperties.<PropertyName>.Value}, {PartProperties!<PropertyName>.Value}"},"titles":{"label":"报表组件属性"}},"slicers":{"titles":{"label":"Slicers"}},"textEncodingFields":{"titles":{"label":"文本字段"}},"theme":{"titles":{"color":"颜色","constant":"文本","font":"字体","image":"图片","label":"报表主题","majorFont":"标题字体","minorFont":"正文字体"}}}},{"lng":"zh","ns":"filters","resources":{"add":"添加过滤...","addCriterion":"添加规则","addGroup":"添加分组","addItem":"添加项目","allOf":"与","anyOf":"或","delete":"删除","edit":"编辑...","expressionText":"表达式...","listItemsCount_0":"{{count}} 个项目","newParameter":"新建参数","operators":{"beginsWith":"开头是","between":"介于","bottomN":"最后 N 项","bottomPercent":"最后 N% 项","contains":"包含","doesNotBeginWith":"开头不是","doesNotContain":"不包含","equalTo":"等于","greaterThan":"大于","greaterThanOrEqualTo":"大于等于","in":"在","lessThan":"小于","lessThanOrEqualTo":"小于等于","like":"形如","notEqualTo":"不等于","notIn":"不在","topN":"前 N 项","topPercent":"前 N% 项"},"reset":"重置"}},{"lng":"zh","ns":"glyphs-RPX","resources":{"barcode":{"textError":"错误","unsupportedSymbology":"[{{symbology}}] \\"{{itemName}}\\" 在设计时预览受限."}}},{"lng":"zh","ns":"groupEditor","resources":{"addGroup":{"btnAdjacentAfter":"同级分组-在当前分组后面","btnAdjacentBefore":"同级分组-在当前分组前面","btnChild":"子级分组","btnParent":"父级分组"},"addTotal":{"btnAfter":"在分组后显示汇总","btnBefore":"在分组前显示汇总"},"btnDelete":"删除","btnDisableGroup":"禁用分组","btnEditExpression":"分组条件表达式","btnEnableGroup":"启用分组","headingAddGroup":"添加分组","headingAddTotal":"添加汇总","headingColumnGroups":"列分组","headingRowGroups":"行分组","labelAdvancedMode":"高级模式","textHiddenStatic":"(固定单元格)","textSelectTablix":"请选中一个矩表组件，编辑相应的分组设置"}},{"lng":"zh","ns":"labels","resources":{"body":"报表主体","dvchartPlotRuleNoCondition":"未设置条件","dvchartXAxis":"横轴","dvchartYAxis":"纵轴","footer":"底部标题","header":"图表标题","pageFooter":"页脚","pageHeader":"页眉","total":"汇总"}},{"lng":"zh","ns":"marginsSizes","resources":{"values":[{"_name":"无边距(0cm * 0cm)","cm":{"bottom":"0cm","left":"0cm","right":"0cm","top":"0cm"},"in":{"bottom":"0in","left":"0in","right":"0in","top":"0in"}},{"_name":"很窄(0.25cm * 0.25cm)","cm":{"bottom":"0.25cm","left":"0.25cm","right":"0.25cm","top":"0.25cm"},"in":{"bottom":"0.1in","left":"0.1in","right":"0.1in","top":"0.1in"}},{"_name":"较窄(0.5cm * 0.5cm)","cm":{"bottom":"0.5cm","left":"0.5cm","right":"0.5cm","top":"0.5cm"},"in":{"bottom":"0.2in","left":"0.2in","right":"0.2in","top":"0.2in"}},{"_name":"正常(1cm * 1cm)","cm":{"bottom":"1cm","left":"1cm","right":"1cm","top":"1cm"},"in":{"bottom":"0.5in","left":"0.5in","right":"0.5in","top":"0.5in"}},{"_name":"较宽(2.5cm * 2.5cm)","cm":{"bottom":"2.5cm","left":"2.5cm","right":"2.5cm","top":"2.5cm"},"in":{"bottom":"1in","left":"1in","right":"1in","top":"1in"}},{"_name":"很宽(5cm * 2.5cm)","cm":{"bottom":"2.5cm","left":"5cm","right":"5cm","top":"2.5cm"},"in":{"bottom":"1in","left":"2in","right":"2in","top":"1in"}}]}},{"lng":"zh","ns":"nameTemplate-RPX","resources":{"Barcode":"条形码","CheckBox":"复选框","CrossSectionBox":"截面框","CrossSectionLine":"截面线","Detail":"详情","GroupFooter":"组页脚","GroupHeader":"组页眉","InputFieldCheckBox":"输入字段复选框","InputFieldText":"输入字段","Label":"标签","Line":"线条","PageBreak":"分页符","PageFooter":"页脚","PageHeader":"页眉","Picture":"图片","ReportFooter":"报表页脚","ReportHeader":"报表页眉","ReportInfo":"报表信息","RichTextBox":"富文本","Shape":"形状","Style":"样式","SubReport":"子报表","TextBox":"文本框","Unknown":"未知"}},{"lng":"zh","ns":"nameTemplates","resources":{"Continuous":"报表区域","Unknown":"条目","bandedList":"带状列表","bandedListGroup":"分组","barcode":"条形码","bullet":"数据条","calculatedField":"计算字段","categoryGroup":"分类","chart":"图表","checkbox":"复选框","columnGroup":"列分组","container":"容器","contentplaceholder":"内容占位符","dashboardSection":"区域","dataset":"数据集","detailsGroup":"明细分组","dvchart":"图表","dvchartEncodingField":"字段","dvchartPlot":"绘图区域","dvchartoverlay":"配置","dvchartrule":"规则","dvcharttextencoding":"文本","field":"数据字段","fixedPage":"固定页面","formattedText":"富文本","group":"分组","image":"图片","inputField":"输入字段","label":"标签","layer":"报表层","line":"线条","list":"列表","mailMergeField":"邮件合并字段","overflowPlaceholder":"内容占位符","parameter":"查询参数","parameterCollection":"查询参数","partItem":"组件条目","pointer":"指针","reportParameter":"报表参数","reportPart":" 报表组件","reportPartProperty":"属性","reportSlicer":"ReportSlicer","richtext":"富文本","rowGroup":"行分组","seriesGroup":"系列分组","seriesValue":"数值","shape":"图形","sparkline":"迷你图","subreport":"子报表","table":"表格","tableDetailsGroup":"明细分组","tableGroup":"表格分组","tableOfContents":"目录","tablix":"矩表","textbox":"文本框","tocLevel":"目录层级"}},{"lng":"zh","ns":"notifications","resources":{"addReportItem":{"caption":"\'{{reportItemType}}\' 类型的报表元素不能添加到 {{containerType}}"},"contentPlaceholderSize":{"caption":"无法执行操作","text":"内容超出内容占位符大小"},"contentPlaceholderSizeWithDetails":{"text":"内容超出内容占位符大小，请尝试手动减小 {{contentPlaceholder}} 中 {{items}} 的大小或位置。"},"convertTableForClient":{"text":"✘ 将表格 \'{{name}}\' 转为矩表时，出现结构异常"},"deleteRowColumn":{"caption":"不能删除 {{rowColumn}}","text":"{{rowColumn}} 行不能被删除，因为会导致 {{itemType}} \'{{itemName}}\' 的大小无法确定"},"fixedPageSize":{"caption":"无法执行该操作","text":"报表元素大小不能超过页面大小"},"fplPasteFailed":{"caption":"粘贴操作失败","text":"没有足够空间粘贴报表元素"},"innerException":{"caption":"内部错误"},"invalidPageSizes":{"caption":"无效的纸张大小设置"},"libraries":{"dataSetNotFound":{"text":"未找到所需的数据集“{{dataSetName}}”"},"dataSourceNotFound":{"text":"未找到所需的数据源“{{dataSourceName}}”"},"duplicateLibrary":{"text":"Library with {{libProp}} \\"{{libValue}}\\" already exists. Library {{libProp}} must be unique"},"importFailed":{"caption":"模版\\"{{path}}\\"导入失败"},"invalidLibraryName":{"text":"Invalid library name \\"{{libName}}\\". Use alphanumeric characters and underscore only"},"loadingFailed":{"caption":" 库\\"{{path}}\\"加载失败"},"noDataSetsFound":{"text":"在库中找不到数据集"},"nonMslLibrary":{"text":"无法将非 MSL 的报表解析为模版"},"unknownLibrary":{"caption":"报表依赖于未知的库","text":"找不到此报告中使用的库“{{libName}}”"}},"listRowsOrColumnsCount":{"caption":"无法执行该操作","text":"内容超出列表列/行大小"},"lockLayout":{"caption":"报表布局已被锁定","text":"报表布局被锁定之后，无法进行影响布局的操作，比如：移动、调整大小、删除、添加和修改。"},"masterReports":{"dataSetsWereRenamed":{"caption":"数据集已重命名","text":"与母版报表中的数据集冲突的数据集已重命名为: {{dataSetNames}}."},"dataSourcesWereRenamed":{"caption":"数据源已重命名","text":"与母版报表中的数据源冲突的数据源已重命名为: {{dataSourceNames}}."},"embeddedImagesWereRenamed":{"caption":"内嵌图片已经重命名","text":"与母版报表中的内嵌图片冲突的内嵌图片已经重命名为: {{embeddedImagesNames}}."},"layersWereRenamed":{"caption":"图层已重命名","text":"与母版报表中的图层冲突的图层已经重命名为:{{layersNames}}."},"parametersWereRenamed":{"caption":"报表参数已经重命名","text":"与母版报表中的参数冲突的参数已重命名：{{parameterNames}}."},"reportItemsWereMovedOrResized":{"caption":"报表条目已移动或调整大小","details":"{{num}}。{{contentPlaceholder}} 中的 {{items}}\\n","text":"内容占位符中的条目已移动或调整大小以适应母版报表的更新内容占位符：\\n{{outsideItems}}"},"reportItemsWereRenamed":{"caption":"报表条目已经重命名","text":"与母版报表中的报告项目冲突的报表条目已重命名：{{reportItemNames}}."}},"pasteFailed":{"caption":"要复制的项目无效: {{elementName}}","text":"报表中包含不可用元素"},"pasteFailedDecode":{"caption":"复制时解码错误: {{reason}}"},"pasteWarningDecode":{"caption":"复制时解码警告","text":"{{warning}}"},"removeDefaultLayer":{"caption":"不能删除默认报表层"},"removeLastValidValue":{"caption":"无法删除参数所绑定的最后一个有效值 "},"transaction":{"caption":"操作失败: {{innerExceprion}}"}}},{"lng":"zh","ns":"pageSizes","resources":{"values":[{"_name":"A4","cm":{"height":"29.7cm","width":"21cm"},"in":{"height":"11.69in","width":"8.27in"}},{"_name":"移动页面","cm":{"height":"18cm","width":"10cm"},"in":{"height":"7.2in","width":"4in"}},{"_name":"信纸","cm":{"height":"27.9cm","width":"21.6cm"},"in":{"height":"11in","width":"8.5in"}},{"_name":"A3","cm":{"height":"42cm","width":"29.7cm"},"in":{"height":"16.54in","width":"11.69in"}},{"_name":"A5","cm":{"height":"21cm","width":"14.8cm"},"in":{"height":"8.27in","width":"5.83in"}},{"_name":"A6","cm":{"height":"14.8cm","width":"10.5cm"},"in":{"height":"5.83in","width":"4.13in"}},{"_name":"B4 (JIS)","cm":{"height":"36.4cm","width":"25.7cm"},"in":{"height":"14.33in","width":"10.12in"}},{"_name":"B5 (JIS)","cm":{"height":"25.7cm","width":"18.2cm"},"in":{"height":"10.12in","width":"7.17in"}},{"_name":"B6 (JIS)","cm":{"height":"18.2cm","width":"12.8cm"},"in":{"height":"7.17in","width":"5.04in"}},{"_name":"B5 (ISO)","cm":{"height":"25cm","width":"17.6cm"},"in":{"height":"9.84in","width":"6.93in"}},{"_name":"Tabloid","cm":{"height":"43.2cm","width":"27.9cm"},"in":{"height":"17in","width":"11in"}},{"_name":"法律专用纸","cm":{"height":"35.6cm","width":"21.6cm"},"in":{"height":"14in","width":"8.5in"}},{"_name":"Executive","cm":{"height":"26.7cm","width":"18.4cm"},"in":{"height":"10.5in","width":"7.25in"}}]}},{"lng":"zh","ns":"parametersViewEditor","resources":{"alignmentEnum":{"bottom":"底部","center":"居中","horizontal":"横向显示","justify":"两端对齐","left":"左侧","none":"不显示","right":"右侧","top":"顶部","vertical":"纵向显示"},"bindingAdorner":{"headingBinding":"绑定参数","textUnspecified":"还未指定"},"bindingProperties":{"any":"任意类型","boolean":"布尔型","date":"日期","dateDateTime":"日期, 日期时间","integerFloat":"整型, 浮点型"},"booleanProperties":{"checkbox":"复选框","falseText":"否","radio":"单选框","toggle":"开关按钮","trueText":"是","undefinedText":"未指定"},"boundParameter":{"allowBlank":"允许空白值(Empty)","defaultValue":"默认值","multiValue":"多选","multiline":"多行文本","nullable":"允许为空值(Null)","parameter":"{{name}} 参数","text":"绑定参数"},"buttonProperties":{"clear":"清空","preview":"预览","reset":"重置"},"canvas":"查询面板","dataType":{"boolean":"布尔","date":"日期","dateTime":"日期时间","float":"浮点型","integer":"整型","string":"字符串"},"dateRangeParameterLabels":{"monthYearOrder":"Y-M","placeholderDateEnd":"结束","placeholderDateStart":"开始","shortcuts":{"labelLastMonth":"上月","labelLastWeek":"上周","labelLastYear":"去年","labelMonthToDate":"本月至今","labelWeekToDate":"本周至今","labelYearToDate":"本年至今"},"tabLabelAnnually":"年","tabLabelDaily":"天","tabLabelMonthly":"月","textBack":"返回","textShortcutsList":"常用日期范围"},"dateTimeParameterLabels":{"monthYearOrder":"Y-M","textBack":"返回","textClear":"清空","textToday":"今天"},"dateTimeRange":{"rangeTypes":{"Current_Day":"当天","Current_Hour":"当前时间","Current_Minute":"当前分钟","Current_Month":"当月","Current_Quarter":"本季度","Current_Week":"本周","Current_Year":"当年","LastSimple_Day":"昨天","LastSimple_Hour":"上一个小时","LastSimple_Minute":"最后一分钟","LastSimple_Month":"上个月","LastSimple_Quarter":"上个季度","LastSimple_Second":"最后一秒","LastSimple_Week":"上周","LastSimple_Year":"去年","LastToDateSimple_Day":"昨天至今","LastToDateSimple_Month":"上个月至今","LastToDateSimple_Quarter":"上个季度至今","LastToDateSimple_Week":"上周至今","LastToDateSimple_Year":"去年至今","LastToDate_Day_0":"迄今为止过去 {{count}} 天","LastToDate_Month_0":"迄今为止过去 {{count}} 月","LastToDate_Quarter_0":"迄今为止过去 {{count}} 季度","LastToDate_Week_0":"迄今为止过去 {{count}} 周","LastToDate_Year_0":"迄今为止过去 {{count}} 年","Last_Day_0":"过去 {{count}} 天","Last_Hour_0":"过去 {{count}} 小时","Last_Minute_0":"过去 {{count}} 分钟","Last_Month_0":"过去 {{count}} 月","Last_Quarter_0":"过去 {{count}} 季度","Last_Second_0":"过去 {{count}} 秒","Last_Week_0":"过去 {{count}} 周","Last_Year_0":"过去 {{count}} 年","NextSimple_Day":"明天","NextSimple_Hour":"下一小时","NextSimple_Minute":"下一分钟","NextSimple_Month":"下个月","NextSimple_Quarter":"下个季度","NextSimple_Second":"下一秒","NextSimple_Week":"下周","NextSimple_Year":"明年","Next_Day_0":"接下来 {{count}} 天","Next_Hour_0":"接下来 {{count}} 小时","Next_Minute_0":"接下来 {{count}} 分钟","Next_Month_0":"接下来 {{count}} 年","Next_Quarter_0":"接下来 {{count}} 季度","Next_Second_0":"接下来 {{count}} 秒","Next_Week_0":"接下来 {{count}} 周","Next_Year_0":"接下来 {{count}} 年","ToDate_Day":"当天至今","ToDate_Month":"当月至今","ToDate_Quarter":"本季度至今","ToDate_Week":"本周至今","ToDate_Year":"今年迄今为止"}},"dateViewProperties":{"accent":"主题颜色","days":"天","default":"默认","error":"错误颜色","months":"月","none":"无","warning":"警告颜色","years":"年"},"editors":{"nameBoolean":"布尔型","nameButton":"按钮","nameDateRange":"日期范围","nameDateTime":"日期时间","nameDateTimeRange":"时间范围输入框","nameDropdown":"下拉框","nameHeading":"标题","nameList":"列表框","nameMultivalue":"多值文本框","nameNumber":"数字","nameNumberRange":"数字范围","namePlainText":"标签","nameText":"文本框","nameTreeView":"树形列表","textCheckRangeType":"两个参数应该具有相同类型","textHeading":"标题","textNull":"空值"},"fieldsProperties":{"addBtnText":"添加","addBtnTitle":"添加项目","closeBtnTitle":"关闭","collectionIsEmpty":"集合中没有任何项目","items":"个项目","showItem":"显示项目"},"labels":{"removeDefaultValue":"删除默认值"},"msgString":{"controlNotSupportParameters":"该控件不支持绑定到设置了【可用数据】属性的参数","controlNotSupportSingle":"该控件不支持单值参数","controlRequires":"该控件需要绑定到设置了【可用数据】属性的参数","notSupControl":"未被该控件支持"},"parameters":{"labelBoolean":"布尔型","labelDateRange":"日期范围","labelDateTime":"日期时间","labelDateTimeRange":"时间范围参数","labelDropdown":"下拉框","labelHierarchical":"树形列表","labelList":"列表框","labelMultivalue":"多值文本框","labelNumber":"数字","labelNumberRange":"数字范围","labelSingleLineParameter":"文本框"},"parametersPanel":{"headingEditParameter":"编辑参数","headingEditRange":"编辑范围","headingEditable":"可见参数","headingHidden":"隐藏参数","titleEditParameter":"编辑参数..."},"parametersText":"报表参数","placeholderEmpty":"","placeholderMultipleValues":"<--\x3e","properties":{"categories":{"advanced":"高级","appearance":"外观","binding":"绑定参数","common":"常规设置","details":"详细设置","label":"标签","locationAndSize":"位置 & 大小","preview":"预览"},"labels":{"action":"按钮动作","alignment":"对齐方式","amount":"数量","anchorDate":"基准日期","background":"背景色","binding":"绑定","color":"字体颜色","columns":"分栏数量","daysHeaderFormat":"日期显示格式","display":"显示位置","dropdown":"下拉框模式","endpoint":"端点","endpointStates":{"textFalse":"排除","textTrue":"包含"},"falseText":"\\"False\\"显示为","fields":"绑定字段","from":"起始值","groupBy":"多个字段分组","height":"高度","label":"标签","layout":"显示方向","left":"左","list":"列表模式","max":"最大值","maxRange":"最大日期跨度","min":"最小值","mode":"构建方式","multiline":"多行文本","offset":"偏移量","pathString":"单字段自动构建","placeholder":"占位符","placeholderFrom":"““从”占位符”开始","placeholderTo":"“至”占位符”","range":"范围","ranges":"范围","recursive":"两个字段递归","roundInputToStep":"四舍五入","showDefaultRanges":{"label":"默认范围","textFalse":"显示","textTrue":"隐藏"},"slider":"滑块","step":"步进大小","strikeThrough":"显示横线","text":"标题文本","to":"截止值","top":"上","trueText":"\\"True\\"显示为","type":"显示方式","unit":"单位","upDownEditor":"加减按钮","value":"数值","viewMode":"选择方式","width":"宽度"}},"propertiesText":"属性设置","propertyGrid":{"btnCloseSearch":"关闭","placeholderSearch":"请输入属性名称","textEmptyList":"选中一个元素，查看其属性设置","textNoCommonProperties":"没有可用的属性","textUnknownProperty":"未知属性:"},"sideBar":{"collapse":"折叠","expand":"展开"},"surface":{"btnAutoGenerate":"点击自动生成","btnGenerate":"点击自动添加","emptySurfaceBlock":{"textDescriptionButtonAfter":"快速创建查询面板，也可以从左侧工具箱中拖拽控件，来创建自定义的查询面板。","textDescriptionButtonBefore":"你可以","textDescriptionOne":"该报表还没有创建自定义的查询面板！","textDescriptionTwo":"你可以从左侧工具箱中添加控件，创建自定义的查询面板。"},"messageBlock":{"textDescriptionButtonAfter":"快速添加其它参数到查询面板中。","textDescriptionButtonBefore":"你可以","textDescriptionOne":"并不是所有报表参数都已经添加到查询面板，"},"scrollBar":{"textMessagesOne":"报表参数将按照以下布局进行显示","textMessagesTwo":"拖动控件改变显示顺序"}},"toolbar":{"btnDelete":"删除","btnDuplicate":"复制","btnGenerateView":"生成面板","btnHighlightRequired":"高亮必输项","btnLayoutFreeForm":"自由表单","btnLayoutStack":"堆叠","btnRemoveView":"删除面板","btnResetView":"重置面板"}}},{"lng":"zh","ns":"properties-RPX","resources":{"categories":{"appearance":"外观","behavior":"行为","border":"边框","common":"通用","data":"数据","design":"设计","format":"格式","layout":"布局","misc":"杂项","page":"页面","pdf":"PDF","printing":"打印","summary":"汇总","text":"文本","watermark":"水印"},"labels":{"MultiLine":"多行","alignment":"对齐","anchorBottom":"底部固定","angle":"角度","autoReplaceFields":"自动替换字段","autoSize":"自动大小","backColor":"背景颜色","backColor2":"背景色2","backgroundPattern":"背景图案","backgroundStyle":"背景样式","barCodeStyle":"样式","barHeight":"栏高","barWidth":"栏宽","border":{"color":"边框颜色","style":"边框样式","width":"边框宽度"},"calcFieldDefaultValue":"默认值","calcFieldFormula":"公式","calcFieldName":"名称","calcFieldType":"类型","calendar":"日历","canGrow":"可增长","canShrink":"可收缩","captionGrouping":"说明分组","captionPosition":"说明位置","characterSpacing":"字符串间距","checkAlignment":"对齐检查","checkSize":"选中尺寸","checkStyle":"选中样式","checkSumEnabled":"启用校验","checked":"检查","colKeepTogether":"列组保持一致","collate":"校勘","columnCount":"列数统计","columnDirection":"列方向","columnLayout":"列布局","columnSpacing":"列间距","control":{"bottom":"底部","dataField":"数据字段","height":"高度","left":"左","name":"名称","right":"右","tag":"标签","top":"上","visible":"可见性","width":"宽度"},"countNullValues":"统计空值","crossSectionBoxRadius":"半径","culture":"文化","dataField":"数据字段","description":"描述","distinctField":"不同的字段","duplex":"双面打印","enabled":"启用","expressionErrorMessage":"表达式错误信息","fieldName":"字段名称","fontFamily":"字体系列","fontLinethrough":"文本删除线","fontSize":"字体大小","fontStyle":"字体样式","fontUnderline":"文本下划线","fontWeight":"字体粗细","foreColor":"前景色","formatString":"格式化字符串","gradientStyle":"渐变样式","groupKeepTogether":"组保持一致","gutterMargin":"装订线","height":"高度","htmlValue":"HTML取值","hyperLink":"超链接","image":"图片","keepTogether":"保持一致","lineColor":"线条颜色","lineSpacing":"行间距","lineStyle":"线条样式","lineWeight":"线条粗细","margin":"间距","maxLength":"最大长度","maxPages":"最大页面","minCondenseRate":"最小压缩比例","mirrorMargins":"页边距","multiLine":"多行","name":"名称","newColumn":"新增列","newPage":"新增页","nwratio":"窄宽比例","orientation":"方向","outputFormat":"输出格式","padding":"填充","paperHeight":"纸张高度","paperSize":"纸张尺寸","paperWidth":"纸张宽度","paramUI":"显示参数查询面板","parameterDefaultValue":"默认值","parameterFormat":"格式","parameterName":"名称","parameterParameterType":"参数类型","parameterPrompt":"提示","parameterPromptUser":"提示用户","password":"密码","pictureAlignment":"图片对齐","printAtBottom":"在底部打印","printWidth":"打印宽度","quietZoneBottom":"底部空白区","quietZoneLeft":"左侧空白区","quietZoneRight":"右侧空白区","quietZoneTop":"顶部空白区","radius":{"default":"默认"},"readonly":"只读","repeatStyle":"重复样式","repeatToFill":"重复填充","reportName":"报表名称","required":"必须的","rightToLeft":"从右到左","rotation":"旋转","roundingRadius":"圆角","script":"脚本","scriptLanguage":"脚本语言","shape":"形状","sizeMode":"尺寸模式","spellCheck":"拼写检查","style":{"backColor":"背景色","className":"类型名称","ddoCharSet":"脚本","fontFamily":"字体系列","fontLinethrough":"文本删除线","fontSize":"字体大小","fontStyle":"字体样式","fontUnderline":"文本下划线","fontWeight":"字体粗细","foreColor":"颜色","kinsoku":"避头避尾","shrinkToFit":"缩小以适应","styleName":"名称","textAlign":"文本对齐","textJustify":"文本对齐","verticalAlign":"垂直对齐","verticalText":"纵向文本","wrapMode":"换行模式"},"styles":"样式","summaryFunc":"汇总函数","summaryGroup":"汇总组","summaryRunning":"运行汇总","summaryType":"汇总类型","supplementBarHeight":"补充栏高度","supplementCaptionPosition":"补充说明位置","supplementDataField":"补充数据字段","supplementSpacing":"补充间距","supplementText":"补充文本","tabIndex":"标签索引","tag":"标签","text":"文本","title":"标题","underlayNext":"下一步","userData":"用户数据","version":"版本","visible":"可见性","watermarkAlignment":"对齐","watermarkImage":"图片","watermarkPrintOnPages":"在页面上打印","watermarkSizeMode":"尺寸模式","x1":"X1","x2":"X2","y1":"Y1","y2":"Y2"}}},{"lng":"zh","ns":"propertyDescriptors","resources":{"categories":{"action":"钻取操作","appearance":"外观","availableValues":"可用数据","background":"背景选项","backgroundAndBorders":"背景 & 边框","bar":"条形设置","border":"边框选项","borderColor":"边框颜色","borderRoundingRadius":"圆角半径","borderStyle":"边框样式","borderWidth":"边框宽度","borders":"边框选项","column":"分栏数量","common":"常规选项","configurations":"重叠","content":"内容","data":"数据选项","dataLabel":"数据标签","dataLabelText":"数据标签字体","defaultValue":"默认数据","dimensions":"位置 & 大小","documentMap":"文档结构","dvchartLabelBorder":"标签边框","dvchartLabelLine":"标签连接线","dvchartLabelText":"标签文本","dvchartLegend":"图例","dvchartLegendBackground":"图例背景","dvchartLegendBorder":"图例边框","dvchartLegendText":"图例文本","dvchartLegendTitle":"图例标题","dvchartPlotColorEncodings":"绑定- 颜色","dvchartPlotConfig":"绘图区选项","dvchartPlotEncodings":"绑定设置","dvchartPlotShapeEncodings":"绑定 - 形状","dvchartPlotSizeEncodings":"绑定 - 尺寸","dvchartPlotStyle":"样式","dvchartPlotSymbols":"符号","fields":"数据字段","fillStyle":"填充样式","font":"字体","general":"常规选项","grid":"网格线","group":"分组选项","input":"输入","international":"国际化","labelStyle":"标签样式","labels":"坐标轴标签","layout":"页面布局","line":"坐标轴样式","majorGrid":"主网格线","majorTicks":"主刻度线","margins":"页边距","marker":"标记","minorGrid":"次网格线","minorTicks":"次刻度线","misc":"其他选项","noData":"无数据提示","options":"其他选项","pageSize":"纸张选项","preview":"预览报表","range":"范围选项","referenceLine":"数据预警线","scale":"刻度线","seriesLineStyle":"系列线样式","settings":"数据筛选","staticMembers":"静态成员","symbology":"条码类型","tableFooter":"表尾选项","tableHeader":"表头选项","targetDevice":"输出设备","targetStyle":"目标样式","text":"文本字体","threeDProperties":"3D 设置","tickStyle":"刻度样式","title":"标题","userSort":"排序选项","valueStyle":"数据样式","visibility":"显示选项"},"labels":{"action":{"applyParameters":"应用到报表参数","jumpToBookmark":"跳转到书签","jumpToReport":"跳转到报表","jumpToUrl":"跳转到URL","parameters":"参数","slice":"Slicers"},"bandedList":{"canGrow":"高度自动变高","canShrink":"高度自动变低","consumeWhiteSpace":"删除空白区域","preventOrphanedFooter":"禁止单独显示尾部","preventOrphanedHeader":"禁止单独显示头部","printAtBottom":"打印到底部","repeatOnNewPage":"每页重复显示"},"barcode":{"aztecOptions":{"encoding":"编码","errorCorrection":"纠错","layers":"层"},"barHeight":"条码高度","captionGrouping":"标题分组","captionLocation":"标题位置","checksum":"校验符号","code49Options":{"groupNumber":"分组编号","grouping":"进行分组"},"dataMatrixOptions":{"ecc000_140SymbolSize":"Ecc000_140 显示大小","ecc200EncodingMode":"Ecc200 编码模式","ecc200SymbolSize":"Ecc200 显示大小","eccMode":"Ecc 模式","encoding":"编码","encodingMode":"编码模式","fileIdentifier":"标识符","structureNumber":"结构编号","structuredAppend":"结构化追加","symbolSize":"显示大小"},"ean128Fnc1Options":{"barAdjust":"条码调整","moduleSize":"模型大小","resolution":"Dpi"},"gs1CompositeOptions":{"type":"类型","value":"数值"},"gs1QrCodeOptions":{"encoding":"编码规则","errorLevel":"错误级别","mask":"掩模","version":"版本"},"invalidBarcodeText":"无效的条码文本","maxiCodeOptions":{"mode":"模式"},"microPdf417Options":{"compactionMode":"压缩模式","fileId":"文件编号","segmentCount":"分段数量","segmentIndex":"分段索引","version":"版本"},"microQrCodeOptions":{"encoding":"编码方式","errorLevel":"错误级别","mask":"掩模","version":"版本"},"narrowBarWidth":"窄条宽度","nwRatio":"窄宽比例","nwRatio_help":{"text":"也被称为N维，它是一个值，定义了仅包含两种宽度的符号中窄条和宽条比率的倍数。"},"pdf417Options":{"columns":"列数","errorCorrectionLevel":"纠错级别","rows":"行数","type":"类型"},"qrCodeOptions":{"connection":"连接","connectionNumber":"连接数量","encoding":"编码","errorLevel":"错误级别","mask":"掩模","model":"模式","version":"版本"},"quietZone":"边界宽度","rotation":"旋转角度","rssExpandedStacked":{"rowCount":"行数"},"supplementOptions":{"barHeight":"条形高度","captionLocation":"标题位置","spacing":"间距","value":"数值"},"symbology":"条码类型","value":"数值"},"border":{"bottom":"下","color":"颜色","default":"默认","left":"左","right":"右","style":"线型","top":"上","width":"粗细"},"bullet":{"bestValue":"最大数据","interval":"数据间隔","labelFontColor":"颜色","labelFontFamily":"字体","labelFontSize":"字号","labelFontStyle":"字形","labelFormat":"标签格式","orientation":"显示方向","range1Boundary":"第一参考区间","range2Boundary":"第二参考区间","showLabels":"显示标签","targetLineColor":"颜色","targetLineWidth":"线宽","targetShape":"形状","targetValue":"目标数据","tickMarks":"标记","ticksLineColor":"颜色","ticksLineWidth":"线宽","value":"实际数据","valueColor":"数据颜色","worstValue":"最小数据"},"checkBox":{"checkAlignment":"对齐方式","checked":"选中","text":"文本"},"container":{"canGrow":"是否增长","consumeWhiteSpace":"不显示空白区域","gridMode":"网格模式","linkToChild":"链接到子容器","newPage":"换页方式","overflow":" 溢出","pageBreak":"换页方式"},"contentPlaceHolder":{"consumeWhiteSpace":"空白","text":"文本"},"dashboardSection":{"displayName":"显示名称"},"data":{"dataElementName":"元素输出名称","dataElementOutput":"元素是否输出","dataElementStyle":"元素输出样式"},"dataRegion":{"dataSetName":"数据集名称","dataSetParameters":"组件嵌套参数","dataSetParameters_help":{"text":"在数据集参数属性上添加参数，该参数将用于建立绑定了不同数据集的嵌套数据区域组件之间的数据关系"},"filters":"数据过滤条件","newPage":"换页方式","newSection":"分节","noRowsMessage":"没有数据提示信息","overflowName":"内容占位符名称","pageBreak":"换页方式","repeatToFill":"填充空行","sortExpressions":"数据排序规则","throwIfPlaceHoldersEmpty":"占位符为空"},"dataSet":{"accentSensitivity":"方言敏感","boundFields":"查询字段","calculatedFields":"计算字段","caseSensitivity":"大小写敏感","collation":"排序规则","commandType":"查询类型","fields":"数据字段","filters":"数据过滤","kanatypeSensitivity":"日文假名敏感","name":"数据集名称","parameters":"查询参数","query":"查询语句","widthSensitivity":"全半角敏感"},"dataSetParameter":{"name":"参数名称","value":"参数数据"},"dataVisualizer":{"colorScale":{"maximum":"最大值","maximumColor":"最大值颜色","middle":"中间值","middleColor":"中间值颜色","minimum":"最小值","minimumColor":"最小值颜色","useMiddleColor":"使用中间值颜色","value":"数值"},"dataBar":{"alternateColor":"负值替代色","color":"颜色","maximum":"最大值","minimum":"最小值","useAlternateColor":"使用负值替代色","useAlternateColor_help":{"text":"启用负值替代色之后，将会使用改颜色显示小于零的数据"},"value":"数值","zeroValue":"零数值"},"gradient":{"color1":"颜色 1","color2":"颜色 2","gradientType":"渐变样式"},"hatch":{"color1":"颜色 1","color2":"颜色 2","hatchStyle":"图案样式"},"iconSet":{"icon1Value":"图标 1 数值","icon2Value":"图标 2 数值","icon3Value":"图标 3 数值","icon4Value":"图标 4 数值","icon5Value":"图标 5 数值","iconSet":"图标集"},"rangeBar":{"color":"颜色","displayProgressIndicator":"显示进度指示器","length":"长度","maximum":"最大值","minimum":"最小值","progressIndicatorColor":"进度指示器颜色","progressIndicatorLength":"进度指示器长度","startingValue":"开始数值"},"visualizerType":"可视化类型"},"dimensions":{"bottom":"下","endPointX":"终点-横坐标","endPointY":"终点-纵坐标","fixedHeight":"固定高度","fixedSize":"固定大小","fixedWidth":"固定宽度","height":"高度","left":"左","location":"位置","right":"右","size":"大小","startPointX":"起点-横坐标","startPointY":"起点-纵坐标","top":"上","width":"宽度"},"dvChartPlotCustomLabels":{"offsetX":"X 偏移量","offsetY":"Y 偏移量","text":"文本"},"dvChartPlotPointers":{"end":"结束","needlePinWidth":"指针针脚宽度","needleWidth":"指针宽度"},"dvPlotOverlays":{"aggregateType":"聚合类型","axis":"轴","backgroundColor":"背景颜色","backwardForecastPeriod":"向后预测期","detailLevel":"明细级别","display":"展示","end":"结束","field":"字段名称","forwardForecastPeriod":"未来预测期","intercept":"拦截","legendLabel":"图例标签","lineColor":"线条颜色","lineStyle":"线型","lineWidth":"线宽","name":"名称","order":"顺序","period":"周期","start":"开始","type":"类型","value":"值"},"dvchart":{"bar":{"bottomWidth":"底端宽度","neckHeight":"漏斗高度","overlap":"重叠效果","topWidth":"顶端宽度","width":"条形宽度"},"customPaletteColors":"自定义颜色","legendHidden":"隐藏图例","legendOrientation":"图例方向","legendPosition":"图例位置","legendWrapping":"折行显示","palette":"调色板","plotTemplate":"图表类型","plotTemplateDropdown":"请选择...","plots":"绘图区域"},"dvchartAxis":{"axisType":"坐标轴类型","dateMode":"日期模式","gridLinesStyle":"网格线样式","height":"高度","labelAngle":"标签显示角度","labelField":"标签字段","lineStyle":"连接线样式","logBase":"对数坐标","majorInterval":"主要间隔","max":"最大值","maxHeight":"最大高度","maxWidth":"最大宽度","min":"最小值","minorInterval":"次要间隔","origin":"原点坐标","overlappingLabels":"标签重叠","plots":"绘图区域","position":"位置","reversed":"逆序显示","scale":"缩放类型","showGridLines":"显示网格线","showMajorGridLines":"主网格线","showMinorGridLines":"次网格线","tickMarks":"刻度线标记","tickSize":"刻度线大小","tickStyle":"刻度线样式","title":"标题","visible":"是否显示","width":"宽度"},"dvchartEncoding":{"aggregateType":"聚合方式","excludeNulls":"排除空值","fieldType":"字段类型","fieldValue":"字段数值","group":"分组","sort":"排序方向","sortingAggregate":"排序聚合方式","sortingField":"排序字段","target":"应用区域","templateKey":"模板标识","value":"数值"},"dvchartHeaderFooter":{"caption":"标题","height":"高度"},"dvchartLegend":{"hidden":"隐藏图例","iconColor":"图标颜色","maxHeight":"最大高度","maxWidth":"最大宽度","orientation":"图例方向","position":"图例位置","ranges":"区间范围","title":"图例标题"},"dvchartPlot":{"action":"钻取类型","axisMode":"坐标轴模式","bar":"条形连接线","category":"分类","categorySort":"分类排序方向","categorySortingAggregate":"分类排序聚合方式","categorySortingField":"分类排序字段","clippingMode":"删减方式","color":"颜色","colorAggregate":"颜色聚合方式","customLabels":"仪表盘标签","details":"明细","gaugeRanges":"仪表盘","innerRadius":"内径大小","lineAspect":"曲线类型","lineColor":"曲线颜色","lineStyle":"曲线样式","lineWidth":"曲线宽度","offset":"分离距离","opacity":"不透明度","overlays":"重叠设置","plotStyle":"绘图区域样式","pointers":"仪表盘 指针","radial":"使用极坐标","rules":"条件格式化","shape":"形状","shapeAggregate":"形状聚合方式","showNulls":"显示空值","showValuesNamesInLegend":"显示数值名称","size":"大小","sizeAggregate":"大小聚合方式","startAngle":"开始角度","swapAxes":"切换坐标轴","sweep":"扇形角度","symbolBackgroundColor":"背景颜色","symbolOpacity":"符号透明度","symbolShape":"符号形状","symbolStyle":"符号样式","symbols":"显示标记符号","text":"文字","textBackgroundColorStyle":"背景颜色","textConnectingLine":"连接线","textLinePosition":"位置","textOffset":"分离显示","textOverlappingLabels":"标签重叠","textPosition":"文字位置","textTemplate":"文本内容","texts":"文本","tooltip":"鼠标提示","tooltipTemplate":"鼠标提示内容","type":"绘图区域类型","unpivotData":"逆透视数据","values":"数值"},"dvchartPlotArea":{"axes":"坐标轴"},"dvchartPlotRules":{"condition":"条件","name":"名称","ruleProperties":"规则属性","targetProperty":"目标属性名称","valueRuleProperties":"属性值"},"dvchartValueEncoding":{"field":{"caption":"标题","close":"收盘","high":"盘高","key":"键值","low":"盘低","lower":"最低","open":"开盘","upper":"最高","value":"数值"}},"empty":"<空>","filter":{"filterExpression":"过滤条件","filterValues":"过滤值","operator":"运算符","value":"数据"},"font":{"fontFamily":"字体","fontSize":"字号","fontStyle":"字形","fontWeight":"粗细"},"formattedText":{"encodeMailMergeFields":"编码邮件合并字段","html":"Html","mailMergeFields":"邮件合并字段"},"fplPage":{"orientation":"显示方向","size":"大小"},"fplReport":{"fixedElementName":"固定页面元素名称","fixedElementOutput":"固定页面元素输出"},"group":{"dataCollectionName":"数据集合名称","dataElementName":"数据元素名称","dataElementOutput":"数据元素输出","documentMapLabel":"文档标签","filters":"过滤条件","groupExpressions":"分组条件","name":"分组名称","newPage":"换页方式","newSection":"插入分节","pageBreak":"换页方式","pageBreakDisabled":"禁用换页设置","parent":"上级分组","printFooterAtBottom":"分组尾显示在底部"},"image":{"backgroundRepeat":"重复","horizontalAlignment":"水平对齐","imageLabel":"图片","mimeType":"格式","sizing":"大小","source":"来源","value":"数据","verticalAlignment":"垂直对齐"},"inputField":{"checkSize":"检测大小","checkStyle":"检测样式","checked":"选中","fieldName":"字段名称","inputType":"类型","maxLength":"最大长度","multiline":"多行文本","password":"密码","readonly":"字段","required":"必输项","spellCheck":"拼写检查","tabIndex":"标签索引","value":"数值"},"layer":{"designerDataFieldVisible":"启用字段选择器","designerLock":"锁定层","designerTransparency":"设计器透明度","designerVisible":"是否显示","name":"层名称","targetDevice":{"all":"全部","export":"导出","paper":"打印","screen":"预览"}},"line":{"endPoint":"终点","lineColor":"颜色","lineStyle":"线形","lineWidth":"线宽","startPoint":"起点"},"list":{"consumeWhiteSpace":"不显示空白区域","dataInstanceElementOutput":"数据实例元素输出","dataInstanceName":"数据实例名称","gridMode":"网格模式","growDirection":"增长方向","rowsOrColumnsCount":"行/列计数"},"margins":{"bottom":"下","left":"左","right":"右","top":"上"},"multipleValues":"<多个值>","overflowPlaceHolder":{"overflowName":"内容占位符名称"},"padding":{"bottom":"下","left":"左","right":"右","top":"上"},"pageSection":{"printOnFirstPage":"打印在第一页","printOnLastPage":"打印在最后一页"},"parameter":{"omit":"忽略","parametername":"参数名称","parameters":"参数","value":"数据"},"partItem":{"library":"报表组件库","reportPart":"报表组件"},"report":{"author":"报表作者","collapseWhiteSpace":"折叠白色空间","collateBy":"排列方式","columnSpacing":"分栏间距","columns":"分栏数量","consumeContainerWhitespace":"不显示空白区域","description":"报表描述","displayType":"显示方式","embeddedImages":"内嵌图片","language":"语言","layers":"报表分层","layoutOrder":"矩表换页方式","level":"当前嵌套层级数","levels":"文档嵌套总层级数","marginsSizes":"页边距大小","marginsStyle":"页边距样式","nameMasterReport":"母版报表","numberingStyle":"编号样式","pageHeight":"纸张高度","pageOrientation":"纸张方向","pageSize":"纸张大小","pageWidth":"纸张宽度","reportPart":{"description":"报表描述","displayName":"显示名称","partProperties":"属性设置","properties":{"category":"分类","defaultValue":"默认数据","description":"报表描述","displayName":"显示名称","type":"类型"},"reportItemName":"组件条目名称","sizeMode":"尺寸模式"},"reportParts":"报表组件","sizeType":"大小自适应","source":"目录来源","startPageNumber":"开始页号","theme":"报表主题","themes":"主题"},"reportItem":{"accessibleDescription":"无障碍描述","actionType":"钻取类型","bookmark":"书签","keepTogether":"显示在一起","label":"标签","layerName":"报表层名称","name":"名称","pageName":"页面名称","style":"外观样式","toolTip":"鼠标提示","visibility":"显示","zIndex":"层叠顺序"},"reportParameter":{"allowBlankValue":"允许空白（Blank）","allowNullValue":"允许空值（Null）","dataSetName":"数据集名称","dataType":"数据类型","hidden":"隐藏","label":"标签","labelField":"标签字段","multiline":"多行数据","multivalue":"多值数据","name":"参数名称","orderBy":"排序规则","parameterFormat":"格式","parameterValues":"参数值","prompt":"提示文本","selectAllValue":"全选","selectAllValue_help":{"text":"该设置会作为用户选择全选时返回的数据"},"value":"数值","valueField":"数据字段","values":"数据"},"reportSlicer":{"allowBlankValue":"Allow Blank Value","allowNullValue":"Allow Null Value","dataSetName":"Data Set Name","multivalue":"Multivalue","name":"Name"},"richtext":{"canGrow":"自动变高","markup":"标记类型","value":"数值"},"roundingRadius":{"bottomLeft":"下 左","bottomRight":"下 右","default":"默认","label":"圆角半径","topLeft":"上 左","topRight":"上 又"},"sparkline":{"fillColor":"填充色","gradientEndColor":"渐变终点颜色","gradientStyle":"渐变类型","lineColor":"线条颜色","lineWidth":"线条宽度","markerColor":"标记颜色","markerVisibility":"显示标记","maximumColumnWidth":"最大宽度","rangeFillColor":"填充色","rangeGradientEndColor":"渐变终点颜色","rangeGradientStyle":"渐变类型","rangeLowerBound":"下限","rangeUpperBound":"上限","rangeVisibility":"显示","seriesValue":"系列值","sparklineType":"类型"},"style":{"angle":"旋转角度","backgroundAndBorders":"填充 & 边框","backgroundColor":"颜色","backgroundGradientEndColor":"渐变终点颜色","backgroundGradientType":"渐变类型","backgroundImage":"图片","border":"边框","calendar":"日历","characterSpacing":"字间距","color":"颜色","corner":"表角","direction":"方向","font":"字体","format":"数据格式","headingLevel":"标题层级","language":"语言","lineHeight":"行高","lineSpacing":"行间距","maxLevel":"最大层级","minCondenseRate":"字宽比例","minCondenseRate_help":{"text":"文字的最小宽度压缩比例，该值介于10到100之间"},"numeralLanguage":"数字语言","numeralVariant":"数字格式","padding":"内部间距","shapeStyle":"图形样式","shrinkToFit":"字体自适应","textAlign":"文本对齐","textDecoration":"特殊效果","textIndent":"文本缩进","textJustify":"两端对齐","unicodeBiDi":"双向嵌入级别","uprightInVerticalText":"竖排文本","verticalAlign":"垂直对齐","wrapMode":"自动换行","writingMode":"文字方向"},"subreport":{"inheritStyleSheet":"继承样式表","mergeTransactions":"合并事务","reportName":"报表名称","reportParameters":"参数","substituteThemeOnSubreport":"使用子报表样式表"},"table":{"autoWidth":"自动列宽","detailsDataCollectionName":"明细数据集合名称","detailsDataElementName":"明细数据元素名称","detailsDataElementOutput":"明细数据元素输出","keepTogether":"显示在一起","preventOrphanedFooter":"禁止单独显示表尾","printAtBottom":"显示在页面底端","repeatBlankRows":"填充空白行","repeatOnNewPage":"每页重复显示"},"tablix":{"frozenColumns":"冻结列数","frozenRows":"冻结行数","groupsBeforeRowHeaders":"分组显示在行头之前","layoutDirection":"文字方向","repeatColumnHeaders":"重复显示列头","repeatRowHeaders":"重复显示行头"},"tablixBodyCell":{"autoMergeMode":"自动合并","autoMergeMode_help":{"text":"是否将数据相同的相邻单元格就行合并，该设置仅对文本框类型单元格有效"}},"tablixMember":{"groupEnabled":"启用分组","keepWithGroup":"与分组显示在一起","repeatOnNewPage":"每页都显示"},"textbox":{"canGrow":"自动变大","canShrink":"自动缩小","initialToggleState":"初始切换状态","value":"数据"},"tocLevel":{"displayFillCharacters":"显示填充字符","displayPageNumber":"显示页号","fillCharacter":"填充字符","label":"标签"},"userSort":{"sortExpression":"排序规则","sortExpressionScope":"排序范围","sortTarget":"排序目标"},"visibility":{"hidden":"隐藏","toggleItem":"切换元素"}}}},{"lng":"zh","ns":"propertyEditors-RPX","resources":{"image":{"textChange":"变更...","textPick":"选择..."},"statusWrapper":{"btnReset":"重置","titleDefault":"","titleError":"无效","titleInherited":"继承自父样式","titleModified":"修改"},"style":{"drillCaptionStyle":"样式","textEdit":"编辑"}}},{"lng":"zh","ns":"propertyEditors","resources":{"boolean":{"textFalse":"否","textTrue":"是","textUndefined":"未指定"},"chartComplexEncodingFieldCollectionEditor":{"prefix":"字段"},"chartSimpleEncodingFieldCollectionEditor":{"captionHeader":"标题","keyHeader":"字段","valueHeader":"值"},"collection":{"btnAdd":"添加项目","textEmpty":"集合中没有任何项目","textItemsCount_0":"{{count}} 个项目","titleAdd":"添加项目","titleClose":"关闭","titleDelete":"删除","titleShowItems":"显示项目"},"colorDropdown":{"btnColorPicker":"更多颜色","btnPalettes":"调色板","btnWebColors":"Web 颜色","headingStandard":"标准颜色","headingTheme":"主题颜色","labelHex":"十六进制","labelHue":"色调","labelLightness":"亮度","labelSaturation":"饱和度","themeColors":{"titleBase":"{{colorKey}}","titleDarker25":"{{colorKey}} - 25% 深色","titleDarker50":"{{colorKey}} - 50% 深色","titleLighter25":"{{colorKey}} - 25% 浅色","titleLighter50":"{{colorKey}} - 50% 浅色","titleLighter75":"{{colorKey}} - 75% 浅色"}},"common":{"textEmpty":"<空>","textExpression":"<表达式>","textMultipleValues":"<--\x3e","textNone":"<无>","titleCollapse":"折叠","titleExpand":"展开"},"dataSetFieldCollection":{"dataFieldHeader":"数据字段","dataFieldPlaceholder":"<数据字段>","fieldHeader":"字段名称","fieldPlaceholder":"<名称>","valueHeader":"数据","valuePlaceholder":"<数据>"},"dataSetParameterCollection":{"nameHeader":"参数名称","namePlaceholder":"<名称>","valueHeader":"数据","valuePlaceholder":"<数据>"},"dataSetQuery":{"placeholder":"请输入查询语句，例如：select * from 产品"},"dvcartLegendRangeOptionsCollection":{"titleHeader":"图例标题","titlePlaceholder":"<图例标题>","toHeader":"范围"},"dvchartEncodingCollection":{"aggregate":{"prefix":"聚合","propertiesTitle":"聚合选项"},"color":{"valuesName":"显示数值名称"},"details":{"prefix":"明细","propertiesTitle":"明细选项"},"fieldPlaceholder":"<字段>","text":{"prefix":"文本","propertiesTitle":"文本属性"},"value":{"prefix":"数值","propertiesTitle":"数值选项"},"valuePlaceholder":"<数值>"},"dvchartPlotTemplate":{"textSelect":"请选择..."},"dvchartRuleProperties":{"headingTargetProperty":"目标属性名称","headingValueProperty":"属性值"},"dvchartTemplate":{"custom":"<自定义>"},"format":{"$locale":"zh-CN","$localeCurrency":"CNY","currency":"货币","custom":"(自定义)","customFormatMask":"(###) ###-####","decimal":"数值","default":"<默认>","digitsLabel":"数字:","fixedPoint":"固定小数点","fullDateShortTime":"完整日期/短时间","general":"常规","generalDateLongTime":"普通日期/长时间","generalDateShortTime":"普通日期/短时间","hexadecimal":"十六进制","longDate":"长日期","longTime":"长时间","monthDay":"月日","number":"数字","percent":"百分比","scientific":"科学计数法","shortDate":"短日期","shortTime":"短时间","yearMonth":"年月"},"image":{"btnDatabase":"数据库图片","btnEmbedded":"内嵌图片","btnShared":"共享图片","textLoad":"加载...","textNoDataFieldsFound":"未找到数据字段","textNoImagesFound":"未找到图片","titleRemove":"删除 \'{{name}}\'..."},"layerCollection":{"propertiesTitle":"分层属性: {{layerName}}"},"mailMergeFieldsCollection":{"nameHeader":"字段名称","namePlaceholder":"<名称>","valueHeader":"数值","valuePlaceholder":"<数值>"},"marginsSizes":{"custom":"(自定义)"},"pageSize":{"custom":"(自定义)"},"palette":{"customPaletteLabel":"<自定义>","extraPalettesHeader":"主题色","standardPalettesHeader":"标准色"},"parameterCollection":{"titleProperties":"参数选项"},"parameterValuesOrder":{"ascending":"升序","descending":"降序"},"picker":{"btnDataVisualizer":"数据可视化器...","btnExpression":"表达式...","btnPickData":"选择数据...","btnReset":"重置","headingParameters":"参数","titleDataBinding":"数据绑定","warnings":{"groupingByAggregate":"不建议使用聚合函数作为分组条件","groupingIsDiscouraged":"不建议使用该属性作为分组条件","masterReportAttributes":"仅母版报表中使用的字段可用于内容报表"}},"reportParameter":{"labelFromQuery":"查询结果","labelNonQueried":"手动添加","labelSource":"数据来源","placeholderEmpty":"<空>","placeholderLabel":"标签","placeholderValue":"数据"},"reportPartPropertiesCollection":{"propertiesTitle":"属性: {{reportPartName}}"},"reportPartsCollection":{"propertiesTitle":"报表组件: {{reportPartName}}"},"reports":{"textLoading":"加载中...","textLoadingError":"错误 {{status}}: {{statusText}}"},"simple":{"backgroundColor":{"label":"背景色","title":"背景色"},"borders":{"borderColor":{"label":"颜色","title":"边框颜色"},"borderStyle":{"label":"样式","title":"边框样式"},"borderWidth":{"label":"粗细","title":"边框粗细"},"borders":"边框","sides":{"all":"全部","bottom":"底部","left":"左边","reset":"重置","right":"右边","top":"顶部"}},"common":{"textExpressionCompact":"<表达式>"},"font":{"fontFamily":{"label":"字体","title":"字体"},"fontSize":{"label":"字号","title":"字号"},"fontStyle":{"label":"字形","title":"字形"},"fontWeight":{"label":"粗细","title":"粗细"},"textColor":{"label":"颜色","title":"前景色"},"textDecoration":{"label":"下划线","title":"下划线"}}},"subreport":{"parameter":"参数","parameterNameHeader":"名称","parameterNamePlaceholder":"<名称>","parameterValueHeader":"数据","parameterValuePlaceholder":"<数据>"},"toggleState":{"textCollapsed":"折叠","textExpanded":"展开"},"validationErrors":{"expression":{"disabledFields":"警告：禁止在该表达式(例如 \'{{token}}\' {{positionInfo}})中使用 \'Fields\' 类型","disabledReportItems":"警告：禁止在该表达式(例如 \'{{token}}\' {{positionInfo}})中使用 \'ReportItems\' 类型","errorPosition":"在第{{line}}行, 第{{column}}列","parseError":"语法错误: 该表达式存在语法错误，请检查","syntaxError":"语法错误: 未知的关键字 \'{{token}}\' {{positionInfo}}","unknown":"未知错误 \'{{positionInfo}}\'","unknownField":"警告: 未知字段名称出现在 \'{{token}}\' {{positionInfo}}","unknownFunction":"警告: 未知的函数名称 \'{{token}}\' {{positionInfo}}","unknownParameter":"警告: 未知参数名称出现在 \'{{token}}\' {{positionInfo}}","unknownReportItem":"警告: 未知报表元素名称出现在 \'{{token}}\' {{positionInfo}}","unknownThemeImage":"警告: 未知的主题图片名称 \'{{token}}\' {{positionInfo}}","warning":"警告: 未知关键字 \'{{token}}\' {{positionInfo}}"}}}},{"lng":"zh","ns":"reportItems","resources":{"Page":"页","Report":"报表","bandedList":"带状列表","bandedListDetails":"明细","bandedListFooter":"尾部","bandedListGroup":"分组","bandedListHeader":"头部","barcode":"条形码","body":"报表主体","bullet":"数据条","checkbox":"复选框","container":"容器","contentPlaceholderText":"设置文本属性以提醒报表创建者要在此处添加哪些内容","contentplaceholder":"内容占位符","continuousSection":"报表区域","dashboard":"交互式报表","dashboardPageFooter":"底部标题","dashboardPageHeader":"图表标题","dashboardSection":"区域","dvchart":"图表","dvchartAggregateEncoding":"聚合设置","dvchartAxis":"坐标轴","dvchartCategoryEncoding":"分类设置","dvchartColorLegend":"图例 - 颜色","dvchartDetailsEncoding":"明细设置","dvchartEncodingValue":"数值绑定","dvchartFooter":"图表底部标题","dvchartGlobalLegend":"全局图例","dvchartHeader":"图表顶部标题","dvchartLegend":"图例","dvchartPlot":"绘图区域","dvchartPlotArea":"绘图区域","dvchartPlotCustomLabel":"仪表盘标签","dvchartPlotPointer":"仪表盘 指针","dvchartShapeLegend":"图例 - 形状","dvchartSizeLegend":"图例 - 大小","dvchartTextEncoding":"文本","dvchartValueAggregateEncoding":"数值聚合","dvchartXAxis":"横轴","dvchartYAxis":"纵轴","fixedPageSection":"Fixed Page Section","formattedText":"富文本","image":"图片","inputField":"输入字段","layer":"报表层","line":"线条","list":"列表","listColumn":"列","listColumnsStacked":"列 2-{{columnCount}}","listRow":"行","listRowsStacked":"行 2-{{rowCount}}","overflowPlaceholder":"内容溢出占位符","page":"页","pageFooter":"页脚","pageHeader":"页眉","pageSection":"页眉/页脚","partItem":"组件条目","report":"报表","reportPart":"组件","reportPartProperty":"属性","richtext":"富文本","shape":"图形","sparkline":"迷你图","subreport":"子报表","table":"表格","tableColumn":"表格列","tableDetails":"明细行","tableFooter":"表尾","tableGroup":"表格分组","tableHeader":"表头","tableOfContents":"目录","tableOfContentsLevel":"层级","tableRow":"表格行","tablix":"矩表","tablixColumn":"列","tablixMember":"分组","tablixRow":"行","textbox":"文本框","unknown":"未知条目"}},{"lng":"zh","ns":"romLabels","resources":{"chart":"图表","dvchart":"新图表","matrix":"矩阵","table":"表格","tablix":"矩表"}},{"lng":"zh","ns":"tablixWizard","resources":{"aggregates":{"avg":"平均值","count":"计数","max":"最大值","min":"最小值","none":"无运算","sum":"求和"},"btnOrganization":"结构","btnStyling":"样式","btnTotals":"汇总","displayAsOptions":{"default":"无计算","percentColumnGroupTotal":"列分组汇总的百分比","percentGrandTotal":"总计的百分比","percentParentColumnGroupTotal":"父级列分组汇总的百分比","percentParentRowGroupTotal":"父级行分组汇总的百分比","percentRowGroupTotal":"行分组汇总的百分比"},"filters":{"headingGroupFilters":"分组过滤 – {{groupLabel}}","headingTablixFilters":"过滤","textFilters":"过滤","titleEditGroupFilters":"编辑过滤...","titleEditTablixFilters":"编辑过滤..."},"formats":{"currency":"货币","decimal":"小数","default":"默认","general":"常规","number":"数字","percent":"百分比"},"headingDataSets":"数据集","headingLayoutDesign":"布局设置","headingTablixWizard":"矩表设计向导","labelCollapsedGroups":"默认折叠显示分组","labelExpandCollapse":"开启展开/折叠分组功能","labelFrozenColumns":"冻结列数","labelFrozenRows":"冻结行数","labelNone":"无样式","labelShowTotalsBeforeGroup":"在分组前显示合计","labelSteppedRowGroups":"开启树形表格布局","labelSubTotalsForColumns":"显示列分组小计","labelSubTotalsForRows":"显示行分组小计","labelTotalsForColumns":"显示列分组总计","labelTotalsForRows":"显示行分组总计","labelUserSortEnabled":"开启列头排序","makeTablix":{"textTotal":"合计","textValues":"数值"},"placeholderNoField":"没有可用数据字段","sortings":{"Ascending":"升序","Descending":"降序","None":"无"},"textAddDataSet":"请返回报表设计界面，先添加报表数据集","textAddValue":"请至少添加一个数值字段","textAsRows":"显示为行","textCannotEditInWizard":"该矩表结构过于复杂，无法在向导中编辑，请通过矩表分组管理器进行配置","textColumns":"列分组","textLayoutOptions":"选项","textNoDataSets":"没有可用数据集","textNoValues":"还未添加任何数值字段","textOpenWizard":"打开矩表向导...","textRowGroups":"行分组","textShowValuesAsRows":"将数值按照行方向显示","textSwap":"行列转置","textValues":"数值","titleAggregate":"计算公式","titleDelete":"删除","titleDisplayAs":"数值显示方式","titleFormat":"数据格式","titleSorting":"排序: {{sorting}}","titleSwapRowColumnGroups":"交换行/列分组位置","warning":{"btnQuit":"不回退","btnRevert":"回退","headingWarning":"警告","textChangedStructure":"无法打开设计向导来编辑矩表，因为矩表的结构已经进行了自定义设置","textConfirmReverting":"请确认是否将矩表回退到可在设计向导中编辑的状态，","textOtherwiseCannotEdit":"否则，该矩表将不能继续使用设计向导功能，但仍然可以通过手动的方式进行设计。"}}},{"lng":"zh","ns":"validationErrors","resources":{"enum":{"incorrect":"类型 \'{{enumType}}\' 的枚举值应为以下值之一: {{enumValues}}"},"errorPosition":"at line {{line}}, column {{column}}","expression":{"disabledFields":"警告：禁止在该表达式(例如 \'{{token}}\' {{positionInfo}})中使用 \'Fields\' 类型","disabledReportItems":"警告：禁止在该表达式(例如 \'{{token}}\' {{positionInfo}})中使用 \'ReportItems\' 类型","errorPosition":"在第{{line}}行, 第{{column}}列","parseError":"语法错误: 该表达式存在语法错误，请检查","syntaxError":"语法错误: 未知的关键字 \'{{token}}\' {{positionInfo}}","unknown":"未知错误 \'{{positionInfo}}\'","unknownField":"警告: 未知字段名称出现在 \'{{token}}\' {{positionInfo}}","unknownFunction":"警告: 未知的函数名称 \'{{token}}\' {{positionInfo}}","unknownParameter":"警告: 未知参数名称出现在 \'{{token}}\' {{positionInfo}}","unknownReportItem":"警告: 未知报表元素名称出现在 \'{{token}}\' {{positionInfo}}","unknownThemeImage":"警告: 未知的主题图片名称 \'{{token}}\' {{positionInfo}}","warning":"警告: 未知关键字 \'{{token}}\' {{positionInfo}}"},"length":{"negative":"数据需要大于零","tooLarge":"数据应该小于 {{max}}","tooSmall":"数据应该大于 {{min}}","unit":"支持的单位包括 \'cm\', \'mm\', \'in\', \'pt\' 和 \'pc\'"},"mime_type":{"incorrect":"图片的MIME类型应匹配 {{wildcard}}"},"number":{"empty":"值为空","nan":"不是一个有效数据","outOfInterval":"值超出区间","outOfRange":"值太大或太小","tooLarge":"数据应该小于 {{max}}","tooSmall":"数据应该大于 {{min}}"},"pattern":"无效表达式，此选项的数据类型应该为 \'{{type}}\'. {{info}}","unknown":{"unknown":"未知类型 \'{{type}}\'"}}},{"lng":"zh","ns":"warning","resources":{"embeddedImage":{"badFile":"您选择的 \'{{name}}\' 并不是一个图片文件","badFileType":"不支持这种格式的文件 \'{{name}}\' ({{type}})","badImageFile":"您选择的 \'{{name}}\' 是一个无效文件","badImageSize":"您选择的图片文件 \'{{name}}\' 超过了 {{limit}} MB 的大小限制"},"margins":{"caption":"无法使用该页边距设置","info":"- \'{{name}}\' 不能大于 {{value}}.","labels":{"bottom":"下边距","left":"左边距","right":"右边距","top":"上边距"}},"pageSize":{"caption":"无法使用该纸张大小","info":"- \'{{name}}\' 不能小于 {{value}}.","labels":{"height":"纸张高度","width":"纸张宽度"}}}}]')},function(e){e.exports=JSON.parse('[{"id":"c8aa4403-83ef-402b-a7da-032063cf629a","name":"主题色1","content":{"ValueColor":"=Theme.Colors!Accent1","TargetLineColor":"=Theme.Colors(5,4)","LabelFontColor":"=Theme.Colors!Dark1","LabelFontFamily":"=Theme.Fonts!MinorFont.Family","LabelFontSize":"=Theme.Fonts!MinorFont.Size","LabelFontStyle":"=Theme.Fonts!MinorFont.Style","TicksLineColor":"=Theme.Colors(1,0)"}},{"id":"558f9b04-1c88-4e41-91c6-64d669c7b6e1","name":"主题色2","content":{"ValueColor":"=Theme.Colors!Accent2","TargetLineColor":"=Theme.Colors(5,5)","LabelFontColor":"=Theme.Colors!Dark1","LabelFontFamily":"=Theme.Fonts!MinorFont.Family","LabelFontSize":"=Theme.Fonts!MinorFont.Size","LabelFontStyle":"=Theme.Fonts!MinorFont.Style","TicksLineColor":"=Theme.Colors(1,0)"}}]')},function(e){e.exports=JSON.parse('[{"id":"448b5aa2-0046-4a3a-b229-e204ce209c82","name":"空白样式","content":{}},{"id":"ceb3638c-a8b2-4f12-ac67-a5ac5f388795","name":"常规样式","content":{"BackgroundColor":"=Theme.Colors!Light1","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MinorFont.Family","FontSize":"=Theme.Fonts!MinorFont.Size","FontStyle":"=Theme.Fonts!MinorFont.Style","FontWeight":"=Theme.Fonts!MinorFont.Weight"}},{"id":"ac6772ab-58d3-4086-b540-08e52eee4143","name":"深色背景","content":{"BackgroundColor":"=Theme.Colors!Accent1","Color":"=Theme.Colors!Light1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}}]')},function(e){e.exports=JSON.parse('[{"id":"63b3da02-daec-415c-806c-a23427a1df63","name":"默认样式","content":{}},{"id":"8a405f0b-8035-4e2b-97eb-7af716ce15ad","name":"深色","content":{"BackgroundColor":"=Theme.Colors!Accent1","Border":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"}}},{"id":"82d551bf-87ea-4cf0-b381-6cb013c563a2","name":"浅色","content":{"BackgroundColor":"=Theme.Colors!Light1","Border":{"Color":"=Theme.Colors!Dark1","Width":"1pt","Style":"Solid"}}}]')},function(e){e.exports=JSON.parse('[{"id":"f405e93c-3a8e-4249-8b39-902d31a45c8f","name":"空白样式","content":{}},{"id":"62f42f38-813d-4d01-abb8-3ad2053db05e","name":"常规样式","content":{"BackgroundColor":"=Theme.Colors!Light1","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MinorFont.Family","FontSize":"=Theme.Fonts!MinorFont.Size","FontStyle":"=Theme.Fonts!MinorFont.Style","FontWeight":"=Theme.Fonts!MinorFont.Weight"}},{"id":"745867b8-4165-4781-90c1-450942e46a70","name":"深色背景","content":{"BackgroundColor":"=Theme.Colors!Accent1","Color":"=Theme.Colors!Light1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}}]')},function(e){e.exports=JSON.parse('[{"id":"3d2c3781-4eea-4ac3-8d50-636edd9328d5","name":"默认样式","content":{}},{"id":"5b7b4e73-22e5-42ed-99c4-62840bdde79d","name":"深色","content":{"BackgroundColor":"=Theme.Colors!Accent1","Border":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"}}},{"id":"3a0e3dcf-397c-4040-aca7-e9caa7d18a0c","name":"浅色","content":{"BackgroundColor":"=Theme.Colors!Light1","Border":{"Color":"=Theme.Colors!Dark1","Width":"1pt","Style":"Solid"}}}]')},function(e){e.exports=JSON.parse('[{"id":"061a12a9-fbb0-4057-9e58-eb8abb1dd9a6","name":"默认样式","content":{}},{"id":"b19a8843-5476-448c-abd3-bf41a84fa9f7","name":"深色","content":{"BackgroundColor":"=Theme.Colors!Accent1","Border":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"}}},{"id":"5ffeb5dc-71fd-4fef-9bb2-5743f950b4bd","name":"浅色","content":{"BackgroundColor":"=Theme.Colors!Light1","Border":{"Color":"=Theme.Colors!Dark1","Width":"1pt","Style":"Solid"}}}]')},function(e){e.exports=JSON.parse('[{"id":"e547a197-1c0a-4f94-98fa-9c478a18c550","name":"主题色1","content":{"LineColor":"=Theme.Colors!Accent1","MarkerColor":"=Theme.Colors(5,4)","FillColor":"=Theme.Colors!Accent1","GradientEndColor":"=Theme.Colors(5,4)","RangeFillColor":"=Theme.Colors!Light1","RangeGradientEndColor":"=Theme.Colors(4,1)"}},{"id":"d88bffbb-d585-49e8-8153-13b5f36706a6","name":"主题色2","content":{"LineColor":"=Theme.Colors!Accent2","MarkerColor":"=Theme.Colors(5,5)","FillColor":"=Theme.Colors!Accent2","GradientEndColor":"=Theme.Colors(5,5)","RangeFillColor":"=Theme.Colors!Light1","RangeGradientEndColor":"=Theme.Colors(4,1)"}}]')},function(e){e.exports=JSON.parse('[{"id":"6bf0b2cc-95b7-437e-b26b-8b07d2f4b0cc","name":"主题色1-交替行颜色","content":{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,4)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Details":{"AlternatingExpression":"=IIF(RowNumber(Nothing) Mod 2, {0}, {1})","Rows":[{"BackgroundColor":"=Theme.Colors(1,4)","Color":"Black","LeftBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"},{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,4)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"TableGroups":{"Styles":[{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,4)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,4)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,4)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,4)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,4)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,4)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}}]},"Border":{"Style":"None"}}},{"id":"c728fbbc-6ae5-47bd-8e20-d710772a5858","name":"主题色1-浅色","content":{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors!Light1","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight","TopBorder":{"Color":"=Theme.Colors!Accent1","Width":"2pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors!Accent1","Width":"2pt","Style":"Solid"}}]},"Details":{"AlternatingExpression":"=IIF(RowNumber(Nothing) Mod 2, {0}, {1})","Rows":[{"BackgroundColor":"=Theme.Colors(1,4)","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MinorFont.Family","FontSize":"=Theme.Fonts!MinorFont.Size","FontStyle":"=Theme.Fonts!MinorFont.Style","FontWeight":"=Theme.Fonts!MinorFont.Weight"},{"BackgroundColor":"=Theme.Colors!Light1","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MinorFont.Family","FontSize":"=Theme.Fonts!MinorFont.Size","FontStyle":"=Theme.Fonts!MinorFont.Style","FontWeight":"=Theme.Fonts!MinorFont.Weight"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors!Light1","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]},"TableGroups":{"Styles":[{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,4)","Color":"=Theme.Colors!Light1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,4)","Color":"=Theme.Colors!Light1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,4)","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,4)","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,4)","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,4)","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]}}]},"Border":{"Style":"None"}}},{"id":"a777e6ce-5b59-4e01-87c1-a7e636295697","name":"主题色1","content":{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,4)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Details":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,4)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"TableGroups":{"Styles":[{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,4)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,4)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,4)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,4)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,4)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,4)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,4)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}}]},"Border":{"Style":"None"}}},{"id":"6fd72c6e-d722-4baf-8bd9-d2a973b3fe25","name":"主题色2-交替行颜色","content":{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,5)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Details":{"AlternatingExpression":"=IIF(RowNumber(Nothing) Mod 2, {0}, {1})","Rows":[{"BackgroundColor":"=Theme.Colors(1,5)","Color":"Black","LeftBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"},{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,5)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"TableGroups":{"Styles":[{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,5)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,5)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,5)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,5)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,5)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,5)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}}]},"Border":{"Style":"None"}}},{"id":"93f6f8bc-4851-48ac-a71d-a283ab4e906a","name":"主题色2-浅色","content":{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors!Light1","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight","TopBorder":{"Color":"=Theme.Colors!Accent2","Width":"2pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors!Accent2","Width":"2pt","Style":"Solid"}}]},"Details":{"AlternatingExpression":"=IIF(RowNumber(Nothing) Mod 2, {0}, {1})","Rows":[{"BackgroundColor":"=Theme.Colors(1,5)","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MinorFont.Family","FontSize":"=Theme.Fonts!MinorFont.Size","FontStyle":"=Theme.Fonts!MinorFont.Style","FontWeight":"=Theme.Fonts!MinorFont.Weight"},{"BackgroundColor":"=Theme.Colors!Light1","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MinorFont.Family","FontSize":"=Theme.Fonts!MinorFont.Size","FontStyle":"=Theme.Fonts!MinorFont.Style","FontWeight":"=Theme.Fonts!MinorFont.Weight"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors!Light1","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]},"TableGroups":{"Styles":[{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,5)","Color":"=Theme.Colors!Light1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,5)","Color":"=Theme.Colors!Light1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,5)","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,5)","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,5)","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,5)","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]}}]},"Border":{"Style":"None"}}},{"id":"4dacfbc1-2a3a-4478-9b5d-9675fe2ac93a","name":"主题色2","content":{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,5)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Details":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,5)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"TableGroups":{"Styles":[{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,5)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,5)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,5)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,5)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,5)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,5)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,5)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}}]},"Border":{"Style":"None"}}},{"id":"79e51b00-cc9d-4e55-9b8b-66572701df5d","name":"主题色3-交替行颜色","content":{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,6)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Details":{"AlternatingExpression":"=IIF(RowNumber(Nothing) Mod 2, {0}, {1})","Rows":[{"BackgroundColor":"=Theme.Colors(1,6)","Color":"Black","LeftBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"},{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,6)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"TableGroups":{"Styles":[{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,6)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,6)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,6)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,6)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,6)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,6)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}}]},"Border":{"Style":"None"}}},{"id":"42848bce-3a6b-450b-a487-660ddc2fb75e","name":"主题色3-浅色","content":{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors!Light1","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight","TopBorder":{"Color":"=Theme.Colors!Accent3","Width":"2pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors!Accent3","Width":"2pt","Style":"Solid"}}]},"Details":{"AlternatingExpression":"=IIF(RowNumber(Nothing) Mod 2, {0}, {1})","Rows":[{"BackgroundColor":"=Theme.Colors(1,6)","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MinorFont.Family","FontSize":"=Theme.Fonts!MinorFont.Size","FontStyle":"=Theme.Fonts!MinorFont.Style","FontWeight":"=Theme.Fonts!MinorFont.Weight"},{"BackgroundColor":"=Theme.Colors!Light1","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MinorFont.Family","FontSize":"=Theme.Fonts!MinorFont.Size","FontStyle":"=Theme.Fonts!MinorFont.Style","FontWeight":"=Theme.Fonts!MinorFont.Weight"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors!Light1","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]},"TableGroups":{"Styles":[{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,6)","Color":"=Theme.Colors!Light1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,6)","Color":"=Theme.Colors!Light1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,6)","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,6)","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,6)","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,6)","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]}}]},"Border":{"Style":"None"}}},{"id":"f84cde16-7505-415d-929b-8c555a035f21","name":"主题色3","content":{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,6)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Details":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,6)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"TableGroups":{"Styles":[{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,6)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,6)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,6)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,6)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,6)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,6)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,6)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}}]},"Border":{"Style":"None"}}},{"id":"a34d1237-aecf-4fb2-91b3-8d670117a213","name":"主题色4-交替行颜色","content":{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,7)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Details":{"AlternatingExpression":"=IIF(RowNumber(Nothing) Mod 2, {0}, {1})","Rows":[{"BackgroundColor":"=Theme.Colors(1,7)","Color":"Black","LeftBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"},{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,7)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"TableGroups":{"Styles":[{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,7)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,7)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,7)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,7)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,7)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,7)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}}]},"Border":{"Style":"None"}}},{"id":"3a929446-2da5-499c-9f71-fcc4181b78ba","name":"主题色4-浅色","content":{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors!Light1","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight","TopBorder":{"Color":"=Theme.Colors!Accent4","Width":"2pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors!Accent4","Width":"2pt","Style":"Solid"}}]},"Details":{"AlternatingExpression":"=IIF(RowNumber(Nothing) Mod 2, {0}, {1})","Rows":[{"BackgroundColor":"=Theme.Colors(1,7)","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MinorFont.Family","FontSize":"=Theme.Fonts!MinorFont.Size","FontStyle":"=Theme.Fonts!MinorFont.Style","FontWeight":"=Theme.Fonts!MinorFont.Weight"},{"BackgroundColor":"=Theme.Colors!Light1","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MinorFont.Family","FontSize":"=Theme.Fonts!MinorFont.Size","FontStyle":"=Theme.Fonts!MinorFont.Style","FontWeight":"=Theme.Fonts!MinorFont.Weight"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors!Light1","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]},"TableGroups":{"Styles":[{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,7)","Color":"=Theme.Colors!Light1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,7)","Color":"=Theme.Colors!Light1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,7)","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,7)","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,7)","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,7)","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]}}]},"Border":{"Style":"None"}}},{"id":"fd3a9591-b335-4f62-95c5-b9607fed8892","name":"主题色4","content":{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,7)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Details":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,7)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"TableGroups":{"Styles":[{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,7)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,7)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,7)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,7)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,7)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,7)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,7)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}}]},"Border":{"Style":"None"}}},{"id":"f7a2c85f-b5ea-46cb-86ab-14eaf153892b","name":"主题色5-交替行颜色","content":{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,8)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Details":{"AlternatingExpression":"=IIF(RowNumber(Nothing) Mod 2, {0}, {1})","Rows":[{"BackgroundColor":"=Theme.Colors(1,8)","Color":"Black","LeftBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"},{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,8)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"TableGroups":{"Styles":[{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,8)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,8)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,8)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,8)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,8)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,8)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}}]},"Border":{"Style":"None"}}},{"id":"bbc01d43-23ef-46d2-9a08-1a8e6a9c3de8","name":"主题色5-浅色","content":{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors!Light1","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight","TopBorder":{"Color":"=Theme.Colors!Accent5","Width":"2pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors!Accent5","Width":"2pt","Style":"Solid"}}]},"Details":{"AlternatingExpression":"=IIF(RowNumber(Nothing) Mod 2, {0}, {1})","Rows":[{"BackgroundColor":"=Theme.Colors(1,8)","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MinorFont.Family","FontSize":"=Theme.Fonts!MinorFont.Size","FontStyle":"=Theme.Fonts!MinorFont.Style","FontWeight":"=Theme.Fonts!MinorFont.Weight"},{"BackgroundColor":"=Theme.Colors!Light1","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MinorFont.Family","FontSize":"=Theme.Fonts!MinorFont.Size","FontStyle":"=Theme.Fonts!MinorFont.Style","FontWeight":"=Theme.Fonts!MinorFont.Weight"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors!Light1","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]},"TableGroups":{"Styles":[{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,8)","Color":"=Theme.Colors!Light1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,8)","Color":"=Theme.Colors!Light1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,8)","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,8)","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,8)","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,8)","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}]}}]},"Border":{"Style":"None"}}},{"id":"e15218c1-55c9-4295-982e-b06e7396ed00","name":"主题色5","content":{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,8)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Details":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,8)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"TableGroups":{"Styles":[{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,8)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,8)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,8)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,8)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,8)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,8)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,8)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}}]},"Border":{"Style":"None"}}},{"id":"867d5b1d-a2ae-437b-ab1b-94c10fd84816","name":"主题色6-交替行颜色","content":{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,9)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Details":{"AlternatingExpression":"=IIF(RowNumber(Nothing) Mod 2, {0}, {1})","Rows":[{"BackgroundColor":"=Theme.Colors(1,9)","Color":"Black","LeftBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"},{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,9)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"TableGroups":{"Styles":[{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,9)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,9)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,9)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,9)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,9)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,9)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}}]},"Border":{"Style":"None"}}},{"id":"48101fed-e8f7-44ec-bbe5-1b7fa2048177","name":"主题色6","content":{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,9)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Details":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,9)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"TableGroups":{"Styles":[{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,9)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,9)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,9)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,9)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,9)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,9)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(5,9)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}}]},"Border":{"Style":"None"}}},{"id":"62efee5a-8a40-477d-8c76-e2c2d03c4da3","name":"空白样式","content":{"Header":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Details":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"TableGroups":{"Styles":[{"Header":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}}]},"Border":{"Style":"None"}}}]')},function(e){e.exports=JSON.parse('[{"id":"b3342e8e-9c1f-4221-a0f1-fd54ce6ac9c5","name":"空白样式","content":{}},{"id":"16531ae3-2130-4e65-88d5-c638f2d19dbf","name":"常规样式","content":{"BackgroundColor":"=Theme.Colors!Light1","Border":{"Color":"Black","Width":"1pt","Style":"None"},"Levels":[{"FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight","Color":"=Theme.Colors!Dark1","PaddingLeft":"0pt"},{"FontFamily":"=Theme.Fonts!MinorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight","Color":"=Theme.Colors!Dark1","PaddingLeft":"5pt"},{"FontFamily":"=Theme.Fonts!MinorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MinorFont.Weight","Color":"=Theme.Colors(3,0)","PaddingLeft":"10pt"},{"FontFamily":"=Theme.Fonts!MinorFont.Family","FontSize":"=Theme.Fonts!MinorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MinorFont.Weight","Color":"=Theme.Colors(3,0)","PaddingLeft":"15pt"},{"FontFamily":"=Theme.Fonts!MinorFont.Family","FontSize":"=Theme.Fonts!MinorFont.Size","FontStyle":"=Theme.Fonts!MinorFont.Style","FontWeight":"=Theme.Fonts!MinorFont.Weight","Color":"=Theme.Colors(3,0)","PaddingLeft":"20pt"}]}},{"id":"0f071010-587c-4b94-98bb-78bba394a012","name":"深色背景","content":{"BackgroundColor":"=Theme.Colors!Accent1","Border":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"Levels":[{"FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight","Color":"=Theme.Colors!Light1","PaddingLeft":"0pt"},{"FontFamily":"=Theme.Fonts!MinorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight","Color":"=Theme.Colors!Light1","PaddingLeft":"5pt"},{"FontFamily":"=Theme.Fonts!MinorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MinorFont.Weight","Color":"=Theme.Colors(3,1)","PaddingLeft":"10pt"},{"FontFamily":"=Theme.Fonts!MinorFont.Family","FontSize":"=Theme.Fonts!MinorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MinorFont.Weight","Color":"=Theme.Colors(3,1)","PaddingLeft":"15pt"},{"FontFamily":"=Theme.Fonts!MinorFont.Family","FontSize":"=Theme.Fonts!MinorFont.Size","FontStyle":"=Theme.Fonts!MinorFont.Style","FontWeight":"=Theme.Fonts!MinorFont.Weight","Color":"=Theme.Colors(3,1)","PaddingLeft":"20pt"}]}}]')},function(e){e.exports=JSON.parse('[{"id":"47d72c3d-1d68-4dad-b654-fa6ff464ee22","name":"主题色1","content":{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,4)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Details":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"}}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,4)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"TableGroups":{"Styles":[{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,4)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,4)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,4)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,4)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,4)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,4)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,4)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}}]},"Border":{"Style":"None"}}},{"id":"a37685cd-1f85-4885-a84e-7df5dbde116c","name":"主题色2","content":{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,5)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Details":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,5)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"TableGroups":{"Styles":[{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,5)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,5)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,5)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,5)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,5)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,5)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,5)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}}]},"Border":{"Style":"None"}}},{"id":"de2338ea-35cb-4a3f-af36-050de0141b28","name":"主题色3","content":{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,6)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Details":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,6)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"TableGroups":{"Styles":[{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,6)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,6)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,6)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,6)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,6)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,6)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,6)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}}]},"Border":{"Style":"None"}}},{"id":"6bc5344e-a8c4-43cd-a2c1-81ee8ba66e6a","name":"主题色4","content":{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,7)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Details":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,7)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"TableGroups":{"Styles":[{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,7)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,7)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,7)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,7)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,7)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,7)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,7)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}}]},"Border":{"Style":"None"}}},{"id":"382a5e38-2f01-496e-a663-cb2426cc9be2","name":"主题色5","content":{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,8)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Details":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,8)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"TableGroups":{"Styles":[{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,8)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,8)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,8)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,8)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,8)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,8)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,8)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}}]},"Border":{"Style":"None"}}},{"id":"28f64df1-7a2f-48fd-bf23-894e925a9b1a","name":"主题色6","content":{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,9)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Details":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(4,9)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"TableGroups":{"Styles":[{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,9)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(0,9)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,9)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(3,9)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,9)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"=Theme.Colors(2,9)","Color":"White","LeftBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"=Theme.Colors(4,9)","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}}]},"Border":{"Style":"None"}}},{"id":"e3093459-cbb4-4c26-babe-f9bbea46eb8f","name":"空白样式","content":{"Header":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Details":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"TableGroups":{"Styles":[{"Header":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}},{"Header":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]},"Footer":{"Rows":[{"BackgroundColor":"Transparent","Color":"Black","LeftBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"RightBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TopBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"BottomBorder":{"Color":"DimGray","Width":"1pt","Style":"Solid"},"TextAlign":"Center","VerticalAlign":"Middle"}]}}]},"Border":{"Style":"None"}}}]')},function(e){e.exports=JSON.parse('[{"id":"b89954dd-a360-4909-926a-34ae179f314f","name":"空白样式","content":{}},{"id":"0464aff7-5f0b-424a-933a-910ec3e12830","name":"常规样式","content":{"BackgroundColor":"=Theme.Colors!Light1","Color":"=Theme.Colors!Dark1","FontFamily":"=Theme.Fonts!MinorFont.Family","FontSize":"=Theme.Fonts!MinorFont.Size","FontStyle":"=Theme.Fonts!MinorFont.Style","FontWeight":"=Theme.Fonts!MinorFont.Weight"}},{"id":"1d8ed9a5-315d-47fc-9b7f-cdf70dc269a4","name":"深色背景","content":{"BackgroundColor":"=Theme.Colors!Accent1","Color":"=Theme.Colors!Light1","FontFamily":"=Theme.Fonts!MajorFont.Family","FontSize":"=Theme.Fonts!MajorFont.Size","FontStyle":"=Theme.Fonts!MajorFont.Style","FontWeight":"=Theme.Fonts!MajorFont.Weight"}}]')},function(e){e.exports=JSON.parse('[{"id":"1ee18575-8aed-4793-94f2-90794cd3d7bf","name":"彩色系-1","content":["=Theme.Colors!Accent1","=Theme.Colors!Accent2","=Theme.Colors!Accent3","=Theme.Colors!Accent4","=Theme.Colors!Accent5","=Theme.Colors!Accent6","=Theme.Colors(3,4)","=Theme.Colors(3,5)","=Theme.Colors(3,6)","=Theme.Colors(3,7)","=Theme.Colors(3,8)","=Theme.Colors(3,9)"]},{"id":"87b63c0e-011f-48fc-b229-a4036bdb9b4d","name":"彩色系-2","content":["=Theme.Colors!Accent1","=Theme.Colors!Accent3","=Theme.Colors!Accent5","=Theme.Colors(3,4)","=Theme.Colors(3,6)","=Theme.Colors(3,8)","=Theme.Colors(2,4)","=Theme.Colors(2,6)","=Theme.Colors(2,8)","=Theme.Colors(1,4)","=Theme.Colors(1,6)","=Theme.Colors(1,8)"]},{"id":"10a51252-5559-4345-9796-2599f4dce958","name":"彩色系-3","content":["=Theme.Colors!Accent2","=Theme.Colors!Accent4","=Theme.Colors!Accent6","=Theme.Colors(3,5)","=Theme.Colors(3,7)","=Theme.Colors(3,9)","=Theme.Colors(2,5)","=Theme.Colors(2,7)","=Theme.Colors(2,9)","=Theme.Colors(1,5)","=Theme.Colors(1,7)","=Theme.Colors(1,9)"]},{"id":"4cd5403c-c6df-4714-a2ef-e7aab6f3b72e","name":"彩色系-4","content":["=Theme.Colors!Accent6","=Theme.Colors!Accent5","=Theme.Colors!Accent4","=Theme.Colors(3,9)","=Theme.Colors(3,8)","=Theme.Colors(3,7)","=Theme.Colors(2,9)","=Theme.Colors(2,8)","=Theme.Colors(2,7)","=Theme.Colors(1,9)","=Theme.Colors(1,8)","=Theme.Colors(1,7)"]},{"id":"b61a982b-b2a2-4b5e-8bec-fd03790d42b9","name":"主题色1-由深到浅","content":["=Theme.Colors(5,4)","=Theme.Colors(4,4)","=Theme.Colors!Accent1","=Theme.Colors(3,4)","=Theme.Colors(2,4)","=Theme.Colors(1,4)"]},{"id":"2d3d33cb-01f8-466c-b222-d6959b9fbc64","name":"主题色1-由浅到深","content":["=Theme.Colors(1,4)","=Theme.Colors(2,4)","=Theme.Colors(3,4)","=Theme.Colors!Accent1","=Theme.Colors(4,4)","=Theme.Colors(5,4)"]},{"id":"db331810-935a-45b0-b953-f2aaec00b6cf","name":"主题色2-由深到浅","content":["=Theme.Colors(5,5)","=Theme.Colors(4,5)","=Theme.Colors!Accent2","=Theme.Colors(3,5)","=Theme.Colors(2,5)","=Theme.Colors(1,5)"]},{"id":"c61a72cb-e0ef-451c-ba96-6087fa811509","name":"主题色2-由浅到深","content":["=Theme.Colors(1,5)","=Theme.Colors(2,5)","=Theme.Colors(3,5)","=Theme.Colors!Accent2","=Theme.Colors(4,5)","=Theme.Colors(5,5)"]},{"id":"937a3e22-af78-4880-8cbe-6227cb44f7e2","name":"主题色3-由深到浅","content":["=Theme.Colors(5,6)","=Theme.Colors(4,6)","=Theme.Colors!Accent3","=Theme.Colors(3,6)","=Theme.Colors(2,6)","=Theme.Colors(1,6)"]},{"id":"b48ee0bb-2fb7-4e62-81ac-ac954473800f","name":"主题色3-由浅到深","content":["=Theme.Colors(1,6)","=Theme.Colors(2,6)","=Theme.Colors(3,6)","=Theme.Colors!Accent3","=Theme.Colors(4,6)","=Theme.Colors(5,6)"]},{"id":"19b3fcb6-e6c5-47c1-9b09-8a7aa7fb815c","name":"主题色4-由深到浅","content":["=Theme.Colors(5,7)","=Theme.Colors(4,7)","=Theme.Colors!Accent4","=Theme.Colors(3,7)","=Theme.Colors(2,7)","=Theme.Colors(1,7)"]},{"id":"44d1d5c0-aa77-482e-b320-c9e7fa4d571d","name":"主题色4-由浅到深","content":["=Theme.Colors(1,7)","=Theme.Colors(2,7)","=Theme.Colors(3,7)","=Theme.Colors!Accent4","=Theme.Colors(4,7)","=Theme.Colors(5,7)"]},{"id":"7b78ac09-2c09-4d7b-8cd7-09103fc1340e","name":"主题色5-由深到浅","content":["=Theme.Colors(5,8)","=Theme.Colors(4,8)","=Theme.Colors!Accent5","=Theme.Colors(3,8)","=Theme.Colors(2,8)","=Theme.Colors(1,8)"]},{"id":"eaf3ab0a-a1cc-4cb7-a72a-a5e71a69c496","name":"主题色5-由浅到深","content":["=Theme.Colors(1,8)","=Theme.Colors(2,8)","=Theme.Colors(3,8)","=Theme.Colors!Accent5","=Theme.Colors(4,8)","=Theme.Colors(5,8)"]},{"id":"67420441-37f2-4f25-b5b0-d3adec8f24b8","name":"主题色6-由深到浅","content":["=Theme.Colors(5,9)","=Theme.Colors(4,9)","=Theme.Colors!Accent6","=Theme.Colors(3,9)","=Theme.Colors(2,9)","=Theme.Colors(1,9)"]},{"id":"8ff5627c-4e0f-4ab7-a41e-18f5af09209e","name":"主题色6-由浅到深","content":["=Theme.Colors(1,9)","=Theme.Colors(2,9)","=Theme.Colors(3,9)","=Theme.Colors!Accent6","=Theme.Colors(4,9)","=Theme.Colors(5,9)"]}]')},,,,,,,,,,,,,,,,,,,,,,function(e,o,t){"use strict";t.r(o);var r=t(12),l=t(8),i=t(13),a=t(14),n=t(15),d=t(16),s=t(17),h=t(9);const m={Bullet:i,CheckBox:a,FormattedText:n,InputField:d,List:s,Rectangle:h,Container:h,Shape:t(18),Sparkline:t(19),Table:t(20),TableOfContents:t(21),Tablix:t(22),TextBox:t(23),ChartPalette:t(24)};const C=Object.keys(m).reduce((function(e,o){return m[o].map(function(e){return function(o,t){var r=Object.assign({},o.content);return r.$targetType=e,{id:o.id,name:o.name,content:r,isDefault:0===t,targetTypes:[e]}}}(o)).forEach((function(o){e.push(o)})),e}),[]);l.find((function(e){return"arjswd"===e.ns})).resources.designer.reportStyles=C;var p={wdCore:r,arjswd:l};if(window.arjsDesigner=window.arjsDesigner||{},window.arjsDesigner.addLocalization)window.arjsDesigner.addLocalization("zh",p);else{var c=window.arjsDesigner.loadLocalizations;window.arjsDesigner.loadLocalizations=function(){c&&c(),window.arjsDesigner.addLocalization("zh",p)}}}]);