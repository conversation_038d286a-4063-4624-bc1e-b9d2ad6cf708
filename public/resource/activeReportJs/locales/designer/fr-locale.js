!function(e){var a={};function t(r){if(a[r])return a[r].exports;var n=a[r]={i:r,l:!1,exports:{}};return e[r].call(n.exports,n,n.exports,t),n.l=!0,n.exports}t.m=e,t.c=a,t.d=function(e,a,r){t.o(e,a)||Object.defineProperty(e,a,{enumerable:!0,get:r})},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,a){if(1&a&&(e=t(e)),8&a)return e;if(4&a&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(t.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&a&&"string"!=typeof e)for(var n in e)t.d(r,n,function(a){return e[a]}.bind(null,n));return r},t.n=function(e){var a=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(a,"a",a),a},t.o=function(e,a){return Object.prototype.hasOwnProperty.call(e,a)},t.p="",t(t.s=45)}({27:function(e){e.exports=JSON.parse('[{"lng":"fr","ns":"adorners","resources":{"bandedList":{"detailsLabel":"<Détails>","groupsLabel":"Groupes"},"bullet":{"targetValue":"Valeur cible","value":"Valeur"},"chart":{"categoryEncoding":{"fieldStub":"Catégorie {{index}}"},"categoryFields":"Champs de catégorie","colorEncoding":{"fieldStub":"Couleur {{index}}","title":"Couleur"},"dataFields":"Champs de donnée","detailEncoding":{"fieldStub":"Détails {{index}}","title":"Détails"},"encodingFields":"Encodages","gaugeLabel":{"fieldStub":"Libellé {{index}}"},"gaugePointer":{"fieldStub":"Pointeur {{index}}"},"gaugeRanges":"Gauge Ranges","labels":"Libellés","multiValueField":"{{firstFieldValue}}, ...","shapeEncoding":{"fieldStub":"Forme {{index}}","title":"Forme"},"sizeEncoding":{"fieldStub":"Taille {{index}}","title":"Taille"},"textEncoding":{"title":"Texte"},"valueEncoding":{"fieldStub":"Valeur {{index}}"}},"common":{"dropFieldsAndValues":"Déposez les champs et les valeurs ici"},"formattedText":{"previewError":"Une erreur d\'analyse XHTML s\'est produite. Veuillez vérifier la valeur de la propriété Html et les valeurs des champs de fusion."},"shapeRoundingTip":{"multiCornerMode":"Press \\"Alt\\" key to switch to \\"Single Corner\\" mode","singleCornerMode":"Press \\"Alt\\" key to switch to \\"Multi-Corner\\" mode"},"sparkline":{"groupingExpressions":"Expressions de regroupement","seriesValue":"Valeur des séries"},"table":{"detailsGroupLabel":"<Détails groupe>","groupsLabel":"Groupes","newGroupLabel":"<Nouveau groupe>"},"tableOfContents":{"addItem":"Ajouter item"}}},{"lng":"fr","ns":"captions","resources":{"barcodeUnsupportedSymbology":"[{{symbology}}] l\'aperçu de \'{{itemName}}\' est limité au moment de la conception.","basicSupportReportItemInfo":"{{itemLabel}} \'{{itemName}}\' a une prise en charge limité lors de la conception.","dvchartAxis":{"category":"Catégorie","value":"Valeur"},"dvchartChartTitle":"Titre du graphique","dvchartColor":"Couleur [{{index}}]","dvchartDetails":"Détails [{{index}}]","dvchartShape":"Forme [{{index}}]","overflowPlaceholder":"{{overflowedItemName}} Espace de débordement"}},{"lng":"fr","ns":"chartWizard","resources":{"buttons":{"btnBack":"Précédent","btnCancel":"Annuler","btnFinish":"Terminer","btnNext":"Suivant"},"customization":{"labels":{"area":"Zone","axisX":"Axe X","axisY":"Axe Y","footer":"Pied de page","header":"En-tête de page","legend":"Légende","plot":"Paramètres du tracé"}},"labelSelectDataSet":"Sélectionnez le Dataset","labelSelectPalette":"Sélectionnez la palette du graphique","settings":{"categories":{"category":"Catégories de donnée","subcategory":"Sous-catégories de donnée","values":"Valeurs de donnée"},"labels":{"aggregate":"Agrégat","field":"Champ","fieldClose":"Fermer le champ","fieldEnd":"Champ de fin","fieldHigh":"Champ élevé","fieldLow":"Champ bas","fieldOpen":"Champ ouvert","fieldSize":"Champ taille","fieldStart":"Champ de départ","fieldX":"Champ X","fieldY":"Champ Y","fields":"Champs","gaugeLabel":"Liobellé de jauge","gaugeRanges":"Gauge Ranges","group":"Méthode de décomposition","lower":"Inférieur","pointer":"Pointeur de jauge","sortDirection":"Direction tri","upper":"Supérieur"}},"templates":{"types":{"area":"Zone","bar":"Barre","bubble":"Bulle","candlestick":"Chandelier","column":"Colonne","doughnut":"Doughnut","funnel":"Entonnoir","gantt":"Gantt","gauge":"Jauge","highLowClose":"Haut Bas Fermer","highLowOpenClose":"Haut Bas Ouvrir Fermer","line":"Ligne","pie":"Pie","polarBar":"Polaire","polarColumn":"Spirale","pyramid":"Pyramide","radarArea":"Radar zone","radarBubble":"Radar bulle","radarLine":"Radar Ligne","radarScatter":"Radar dispersion","rangeArea":"Zone de plage","rangeBar":"Barre de plage","rangeColumn":"Colonne de plage","scatter":"Dispersion"}},"textAdvancedCustomization":"Personnalisation avancée","textCustom":"Personnalisé","textEmpty":"Vide","textPreviewStep":"Aperçu","textTypeStep":"Donnée et type","titleChartWizard":"Assistant Graphiques","titlePreview":"Aperçu","titleSelectType":"Sélectionnez le type de données et de graphique","titleSettings":"Paramètres"}},{"lng":"fr","ns":"common","resources":{"btnCancel":"Annuler","btnOk":"OK","btnSave":"Enregistrer","textCollapse":"Réduire","textDelete":"Supprimer","textExpand":"Développer","textOpen":"Ouvrir...","units":{"cm":{"textFullName":"Centimètres","textShortName":"cm"},"in":{"textFullName":"Pouces","textShortName":"in"}}}},{"lng":"fr","ns":"components-RPX","resources":{"appBar":{"btnScript":"Script"},"dataTab":{"titleDeleteDataSource":"Delete Data Source","titleEditDataSource":"Edit Data Source...","titleMoveParameterDown":"Move Down","titleMoveParameterUp":"Move Up"},"menu":{"btnReportExplorer":"Explorer"},"propertyGrid":{"placeholderSearchBox":"enter property name here...","textAlphabetical":"Alphabetical","textCategorized":"Categorized","textMultipleTypes":"<Multiple Types>","textSort":"Sort"},"scriptEditor":{"placeholder":"Enter {{ScriptLanguage}} code"},"stylesTab":{"textBasedOn":"Based on \\"{{parentName}}\\"","textRootStyle":"No Parent Style","titleAddStyle":"Add Style Based on \\"{{parentName}}\\"","titleDeleteStyle":"Delete Style"},"toolbar":{"home":{"backColor":"Back Color","fontFamily":"Font Family","fontSize":"Font Size","fontStyle":"Font Style Italic","fontWeight":"Font Weight Bold","foreColor":"Fore Color","textDecoration":"Text Decoration Underline","titleAlignCenter":"Text Align Center","titleAlignJustify":"Text Align Justify","titleAlignLeft":"Text Align Left","titleAlignRight":"Text Align Right","verticalAlignBottom":"Vertical Align Bottom","verticalAlignMiddle":"Vertical Align Middle","verticalAlignTop":"Vertical Align Top"},"script":{"events":{"report":{"textDataInitialize":"DataInitialize","textFetchData":"FetchData","textNoData":"NoData","textPageEnd":"PageEnd","textPageStart":"PageStart","textReportEnd":"ReportEnd","textReportStart":"ReportStart"},"section":{"textAfterPrint":"AfterPrint","textBeforePrint":"BeforePrint","textFormat":"Format"}},"textEvent":"Event","textObject":"Object","titleEvent":"Event","titleObject":"Object"}}}},{"lng":"fr","ns":"components","resources":{"appBar":{"btnFile":"Fichier","btnHome":"Accueil","btnInsert":"Insérer","btnParameters":"Paramètres","btnPreview":"Aperçu","btnSaveAs":"Enregistrer sous","textUnsavedChanges":"Modifications non enregistrées","titleNew":"Nouveau","titleOpen":"Ouvrir","titleRedo":"Refaire","titleSave":"Enregistrer","titleUndo":"Annuler"},"chartPaletteDropdown":{"headingExtraPalettes":"Palettes Theme","headingStandardPalettes":"Palettes Standard"},"dataFieldPickerDropdown":{"semantic":{"noMatchingAttributesRelationsFound":"Aucun élément correspondant trouvé","searchPlaceholder":"entrez le nom de l\'élément ici..."}},"dataPanel":{"commonValues":{"currentDateTime":"Date et heure courantes","pageNM":"Page N sur M","pageNMCumulative":"Page N sur M (Cumulative)","pageNMSection":"Page N sur M (Section)","pageNofMLabel":"\\"Page \\" & {{pageNumber}} & \\" sur \\" & {{totalPages}}","pageNumber":"Numero de page","pageNumberCumulative":"Numéro de page (Cumulative)","pageNumberSection":"Numéro de page (Section)","reportFolder":"Dossier de rapport","reportName":"Nom de rapport","totalPages":"Total Pages","totalPagesCumulative":"Total Pages (Cumulative)","totalPagesSection":"Total Pages (Section)","userContext":"Contexte utilisateur","userId":"ID Utilisateur","userLanguage":"Language utilisateur"},"dataSets":{"placeholderEnterFieldName":"entrez le nom du champ ici...","semantic":{"editDataSet":"Modifier le Dataset...","loading":"Chargement...","noMatchingAttributesRelationsFound":"Aucun attribut ou relation correspondant n\'a été trouvé","searchPlaceholder":"entrez ici le nom de l\'attribut ou de la relation..."},"textNoMatchingFieldsFound":"Aucun champ correspondant trouvé"},"fieldVariations":{"Date":[{"format":"=Year({fieldExpression})","label":"Année"},{"format":"=Year({fieldExpression}) & \\" Q\\" & DatePart(\\"q\\", {fieldExpression})","label":"Année-Trimestre"},{"format":"=Year({fieldExpression}) & \\" \\" & MonthName(Month({fieldExpression}))","label":"Année-Mois"},{"format":"=Year({fieldExpression}) & \\" \\" & MonthName(Month({fieldExpression})) & \\" \\" & Day({fieldExpression})","label":"Année-Mois-Jour"},{"format":"=Year({fieldExpression}) & \\" W\\" & DatePart(\\"ww\\", {fieldExpression})","label":"Année-Semaine"},{"format":"=DatePart(\\"q\\", {fieldExpression})","label":"Trimestre"},{"format":"=Month({fieldExpression})","label":"Mois"},{"format":"=MonthName(Month({fieldExpression}))","label":"Nom Mois"},{"format":"=DatePart(\\"ww\\", {fieldExpression})","label":"Semaine"},{"format":"=Day({fieldExpression})","label":"Jour"},{"format":"=WeekdayName(Weekday({fieldExpression}))","label":"Jour de la semaine"}],"DateTime":[{"format":"=Year({fieldExpression})","label":"Année"},{"format":"=Year({fieldExpression}) & \\" Q\\" & DatePart(\\"q\\", {fieldExpression})","label":"Année-Trimestre"},{"format":"=Year({fieldExpression}) & \\" \\" & MonthName(Month({fieldExpression}))","label":"Année-Mois"},{"format":"=Year({fieldExpression}) & \\" \\" & MonthName(Month({fieldExpression})) & \\" \\" & Day({fieldExpression})","label":"Année-Mois-Jour"},{"format":"=Year({fieldExpression}) & \\" W\\" & DatePart(\\"ww\\", {fieldExpression})","label":"Année-Semaine"},{"format":"=DatePart(\\"q\\", {fieldExpression})","label":"Trimestre"},{"format":"=Month({fieldExpression})","label":"Mois"},{"format":"=MonthName(Month({fieldExpression}))","label":"Nom Mois"},{"format":"=DatePart(\\"ww\\", {fieldExpression})","label":"Semaine"},{"format":"=Day({fieldExpression})","label":"Jour"},{"format":"=WeekdayName(Weekday({fieldExpression}))","label":"Jour de la semaine"}]},"headingEditParameter":"Modifier le paramètre","semantic":{"textAttributesCount":"{{count}} attribut","textAttributesCount_plural":"{{count}} attributs","textNoAttributes":"auncun attribute","textNoRelations":"aucune relation","textRelationsCount":"{{count}} relation","textRelationsCount_plural":"{{count}} relations"},"textBasedOnDataSource":"basé sur {{dataSourceName}}","textFieldsCount":"{{count}} champ","textFieldsCount_plural":"{{count}} champs","textModelVersion":"Version: {{version}}","textSharedReference":"Shared Reference","titleAddDataSet":"Ajouter un Dataset...","titleEditDataSet":"Modifier le Dataset...","titleEditDataSource":"Modifier la source de données...","titleMore":"Plus...","titleMoveParameterDown":"Descendre","titleMoveParameterUp":"Monter","titleSelectFields":"Sélectionnez les champs..."},"fieldPicker":{"placeholderEnterFieldName":"entrez le nom du champ ici..."},"layerList":{"btnAddLayer":"Ajouter calque","titleDefaultLayerCannotBeDeleted":"Le calque par défaut ne peut pas être supprimé","titleDeleteLayer":"Supprimer calque","titleEditLayer":"Edit Layer","titleHideLayer":"Masquer le calque","titleLockLayer":"Vérrouiller le calque","titleShowLayer":"Afficher le calque","titleUnlockLayer":"Déverrouiller le calque"},"libraries":{"textEmpty":"Empty","textError":"Error","textNoLibraries":"No available libraries","textNoReportParts":"Library contains no report parts"},"menu":{"btnBack":"Arrière","btnClose":"Fermer","btnGroupEditor":"Éditeur de groupe","btnLayerList":"Calques","btnLibrariesList":"Libraries","btnReportExplorer":"Explorer","titleBack":"Arrière","titlePin":"Épingler"},"notifications":{"btnDismiss":"Rejeter","btnDismissAll":"Rejeter tout","headingError":"{{count}} Erreur","headingError_plural":"{{count}} Erreurs","headingNotification":"{{count}} Notification","headingNotification_plural":"{{count}} Notifications","headingWarning":"{{count}} Avertissement","headingWarning_plural":"{{count}} Avertissements","titleNotifications":"Notifications"},"propertyGrid":{"options":{"categories":"Categories","collapse":"Collapse All","expand":"Expand All","hideAdvancedProperty":"Hide Advanced Properties","showAdvancedProperty":"Show Advanced Properties"},"placeholderSearchBox":"entrez le nom de la propriété ici...","textMultipleTypes":"<Multiple types>","textReportItems":"<Éléments de rapport>"},"statusBar":{"btnPropertiesMode":"Mode Propriétés","btnShowAdvancedProperties":"Afficher les propriétés avancées","btnShowBasicProperties":"Afficher les propriétés de base","common":{"textDisabled":"Désactivé","textEnabled":"Activé"},"snapSettings":{"labelGridSize":"Taille de la grille","labelSnapToGrid":"Accrocher à la grille","labelSnapToGuides":"Accrocher aux repères","titleSnapDisabled":"Snap Disabled","titleSnapEnabled":"Snap Enabled"},"themePicker":{"themes":{"activeReports":"ActiveReports","activeReportsDark":"ActiveReports Dark","darkOled":"OLED Dark","default":"Default","defaultDark":"Default Dark","highContrast":"High Contrast","highContrastDark":"High Contrast Dark","system":"System Theme"},"titleTheme":"Theme"},"titleHideGrid":"Hide Grid","titleHideRulers":"Hide Rulers","titleShowGrid":"Show Grid","titleShowRulers":"Show Rulers","titleZoomIn":"Zoom In","titleZoomOut":"Zoom Out"},"tabs":{"actions":{"textDuplicate":"Duplicate","textHide":"Hide","textInsert":"Insert","textMDelete":"Delete","textMoveLeft":"Move Left","textMoveRight":"Move Right","textShow":"Show"},"btnAddPage":"Ajouter Page","btnAddSection":"Ajouter Section","textPage":"Page"},"themeEditor":{"labelNone":"<Aucun>"},"title":{"textUntitled":"Sans titre"},"toolbar":{"home":{"textExpression":"<Expression>","textExpressionCompact":"<𝑓>","titleAlignCenter":"Centrer","titleAlignJustify":"Justifier","titleAlignLeft":"Aligner à gauche","titleAlignRight":"Aligner à droite","titleBackgroundColor":"Couleur arrière-plan","titleCopy":"Copier","titleCut":"Couper","titleFontFamily":"Police","titleFontSize":"Taille police","titleFontStyle":"Style","titleFontWeight":"Gras","titlePaste":"Coller","titleTextColor":"Couleur texte","titleTextDecoration":"Souligner","titleVerticalAlignBottom":"Aligner verticalement en bas","titleVerticalAlignMiddle":"Centrer verticalement","titleVerticalAlignTop":"Aligner verticalement en haut"},"titleExpand":"Expand"}}},{"lng":"fr","ns":"contextActions-RPX","resources":{"labels":{"addGroupHeaderFooter":"Add Group Header/Footer","addPageHeaderFooter":"Add Page Header/Footer","addReportHeaderFooter":"Add Report Header/Footer","copy":"Copier","cut":"Couper","delete":"Supprimer","deletePageHeaderFooter":"Delete Page Header/Footer","deleteReportHeaderFooter":"Delete Report Header/Footer","layout":{"alignToBottoms":"Align to Bottoms","alignToCenters":"Align to Centers","alignToGrid":"Align to Grid","alignToLefts":"Align to Lefts","alignToMiddles":"Align to Middles","alignToRights":"Align to Rights","alignToTops":"Align to Tops","bringToFront":"Bring to Front","horizontal":{"decreaseSpacing":"Decrease Horizontal Spacing","increaseSpacing":"Increase Horizontal Spacing","makeSpacingEqual":"Make Horizontal Spacing Equal","removeSpacing":"Remove Horizontal Spacing"},"makeSameHeight":"Make Same Height","makeSameSize":"Make Same Size","makeSameWidth":"Make Same Width","sendToBack":"Send to Back","separator":{"corelateControlSizing":"Correlate Control Sizes","horizontalAlignment":"Horizontal Alignment","horizontalSpacing":"Horizontal Spacing","sortControls":"Sort Controls","verticalAlignment":"Vertical Alignment","verticalSpacing":"Vertical Spacing"},"sizeToGrid":"Size to Grid","title":"Layout","vertical":{"decreaseSpacing":"Decrease Vertical Spacing","increaseSpacing":"Increase Vertical Spacing","makeSpacingEqual":"Make Vertical Spacing Equal","removeSpacing":"Remove Vertical Spacing"}},"pageFooter":"PageFooter","pageHeader":"PageHeader","paste":"Coller","report":"Rapport","reportFooter":"ReportFooter","reportHeader":"ReportHeader"}}},{"lng":"fr","ns":"contextActions","resources":{"bandedList":{"addFooter":"Ajouter pied de page","addGroupFooter":"Ajouter pied de groupe","addGroupHeader":"Ajouter en-tête de groupe","addHeader":"Ajouter en-tête","deleteGroup":"Supprimer groupe","groupTitle":"Groupe","insertGroup":"Insérer groupe","removeFooter":"Supprimer pied de page","removeGroupFooter":"Supprimer pied de page du groupe","removeGroupHeader":"Supprimer en-tête de groupe","removeHeader":"Supprimer en-tête","title":"Liste groupée"},"chart":"Graphique","container":{"delete":"Supprimer","expression":"Expression"},"dashboard":{"duplicateSection":"Duplicate Section","hideSection":"Hide Section","moveSectionLeft":"Move Section Left","moveSectionRight":"Move Section Right","removeSection":"Supprimer section","showSection":"Show Section","switchTheme":"Changer de thème","title":"Dashboard"},"dvchart":{"palette":"Définir la palette du graphique","presetGroups":{"area":"Zone","bar":"Barre","column":"Colonne","line":"Ligne","misc":"Divers","pie":"Pie","polarBar":"Polaire","polarColumn":"Spirale","radar":"Radar","range":"Plage"},"presets":{"area":"Zone","areaPercentStacked":"Pourcentage de surface empilé","areaStacked":"Superficie empilée","bar":"Barre","barPercentStacked":"Pourcentage de barre empilé","barStacked":"Barre empilée","bubble":"Bulle","candlestick":"chandelier","column":"Colonne","columnPercentStacked":"Colonne Pourcentage empilé","columnStacked":"Colonne empilée","doughnut":"Doughnut","funnel":"Entonnoir","gantt":"Gantt","gauge":"Jauge","highLowClose":"Haut Bas Fermer","highLowOpenClose":"Haut Bas Ouvrir Fermer","line":"Ligne","lineSmooth":"Ligne lisse","pie":"Pie","polarBar":"Polaire","polarBarPercentStacked":"Pourcentage polaire empilé","polarBarStacked":"Polaire empilé","polarColumn":"Spirale","polarColumnPercentStacked":"Pourcentage spirale empilé","polarColumnStacked":"Spirale empilée","pyramid":"Pyramide","radarArea":"Radar Zone","radarBubble":"Radar bulle","radarLine":"Radar Ligne","radarScatter":"Radar Dispersion","rangeArea":"Plage Zone","rangeBar":"Plage Barre","rangeColumn":"Plage Colonne","scatter":"Dispersion","title":"Définir le modèle de tracé"}},"report":{"addContinuousSection":"Ajouter section continue","addFixedPageSection":"Ajouter section de page fixe","changeMasterReport":"Change Master Report","convertToMasterReport":"Convert to Master Report","deletePage":"Supprimer page","duplicatePage":"Dupliquer page","duplicateSection":"Duplicate Section","hidePage":"Hide Page","hideSection":"Hide Section","insertPage":"Insérer page","insertSection":"Insert Section","layout":{"alignToBottoms":"Align to Bottoms","alignToCenters":"Align to Centers","alignToGrid":"Align to Grid","alignToLefts":"Align to Lefts","alignToMiddles":"Align to Middles","alignToRights":"Align to Rights","alignToTops":"Align to Tops","bringToFront":"Bring to Front","horizontal":{"decreaseSpacing":"Decrease Horizontal Spacing","increaseSpacing":"Increase Horizontal Spacing","makeSpacingEqual":"Make Horizontal Spacing Equal","removeSpacing":"Remove Horizontal Spacing"},"horizontalAlignment":"Horizontal alignment","makeSameHeight":"Make Same Height","makeSameSize":"Make Same Size","makeSameWidth":"Make Same Width","sendToBack":"Send to Back","separator":{"corelateControlSizing":"Correlate Control Sizes","horizontalSpacing":"Horizontal Spacing","sortControls":"Sort Controls","verticalSpacing":"Vertical Spacing"},"sizeToGrid":"Size to Grid","title":"Layout","vertical":{"decreaseSpacing":"Decrease Vertical Spacing","increaseSpacing":"Increase Vertical Spacing","makeSpacingEqual":"Make Vertical Spacing Equal","removeSpacing":"Remove Vertical Spacing"},"verticalAlignment":"Vertical alignment"},"movePageBackward":"Déplacer la page vers l\'arrière","movePageForward":"Avancer la page","moveSectionLeft":"Move Section Left","moveSectionRight":"Move Section Right","pages":"Pages","removeSection":"Supprimer section","reportParts":{"title":"Report Parts","titleCreateReportPart":"Create Report Part"},"setMasterReport":"Set Master Report","showPage":"Show Page","showSection":"Show Section","switchTheme":"Changer de thème","title":"Rapport"},"reportSection":{"addFooter":"Ajouter pied de page","addHeader":"Ajouter en-tête","removeFooter":"Supprimer pied de page","removeHeader":"Supprimer en-tête","title":"Section"},"table":{"addDetails":"Ajouter détails","addFooter":"Ajouter pied de tableau","addGroupFooter":"Ajouter pied de groupe","addGroupHeader":"Ajouter en-tête de groupe","addHeader":"Ajouter en-tête de tableau","cellsTitle":"Cellules","columnTitle":"Colonne","deleteColumn":"Supprimer colonne","deleteGroup":"Supprimer groupe","deleteRow":"Supprimer ligne","expression":"Expression cellules","groupTitle":"Groupe","insertColumn":{"left":"Gauche","right":"Droite"},"insertColumnTitle":"Insérer colonne","insertGroup":"Insérer groupe","insertRow":{"above":"Au dessus","below":"En dessous"},"insertRowTitle":"Insérer lignes","mergeCells":"Fusionner cellules","more":"Plus...","removeDetails":"Supprimer détails","removeFooter":"Supprimer pied de tableau","removeGroupFooter":"Supprimer pied de page du groupe","removeGroupHeader":"Supprimer en-tête de groupe","removeHeader":"Supprimer en-tête de tableau","rowTitle":"Ligne","splitCells":"Diviser cellules","title":"Tableau"},"tablix":{"addGroup":{"adjacentAfter":"Adjacent après","adjacentBefore":"Adjacent Avant","child":"Enfant","parent":"Parent"},"addGroupTitle":"Ajouter groupe","addTotal":{"contextMenuAfter":"Ajouter total après","contextMenuBefore":"Ajouter total avant"},"cellsTitle":"Cellules","columnGroup":"Groupe colonne","columnTitle":"Colonne","delete":"Supprimer","deleteColumn":"Supprimer colonne","deleteRow":"Supprimer ligne","disableGroup":"Désactiver groupe","enableGroup":"Activer groupe","expression":"Expression cellule","insertColumn":{"insideGroupLeft":"A l\'intérieur du groupe - Gauche","insideGroupRight":"A l\'intérieur du groupe - Droite","left":"Gauche","outsideGroupLeft":"A l\'extérieur du groupe - Gauche","outsideGroupRight":"A l\'extérieur du groupe - Droite","right":"Droite"},"insertColumnTitle":"Insérer colonne","insertRow":{"above":"Au dessus","below":"En dessous","insideGroupAbove":"A l\'intérieur du groupe - Au dessus","insideGroupBelow":"A l\'intérieur du groupe - En dessous","outsideGroupAbove":"A l\'extérieur du groupe - Au dessus","outsideGroupBelow":"A l\'extérieur du groupe - En dessous"},"insertRowTitle":"Insérer ligne","mergeCells":"Fusionner cellules","more":"Plus...","rowGroup":"Groupe de ligne","rowTitle":"Ligne","splitCells":"Diviser cellules","totalTitle":"Total"}}},{"lng":"fr","ns":"defaults","resources":{"Graphique":{"anglededepart":0,"rayoninterieur":0.5}}},{"lng":"fr","ns":"dialogs","resources":{"btnCancel":"Annuler","btnInsert":"Insérer","common":{"textCancel":"Annuler"},"dataVisualizer":{"title":"Visualiseur de données"},"expressionEditor":{"headingExpression":"Expression","headingFunctions":"Fonctions","headingInfo":"Info","headingValues":"Valeurs","infoPanel":{"labelConstant":"Constante:","labelDescription":"Description:","labelExample":"Exemple:","labelName":"Nom:","labelSyntax":"Syntaxe:"},"placeholderExpression":"expression","search":{"placeholderSearch":"recherche...","textNoResults":"aucun résultat pour \\"{{query}}\\"","textStartTyping":"commencez à taper pour voir les résultats"},"subtitle":"Editeur d\'expression"},"headingInsertColumns":"Insérer colonnes","headingInsertRows":"Insérer lignes","labelCount":"Compte","labelPosition":"Position"}},{"lng":"fr","ns":"documentItems-RPX","resources":{"Barcode":"Barcode","CheckBox":"CheckBox","CrossSectionBox":"CrossSectionBox","CrossSectionLine":"CrossSectionLine","Detail":"Detail","GroupFooter":"Group Footer","GroupHeader":"Group Header","InputFieldCheckBox":"InputFieldCheckBox","InputFieldText":"InputFieldText","Label":"Label","Line":"Line","PageBreak":"PageBreak","PageFooter":"Page Footer","PageHeader":"Page Header","Picture":"Picture","Report":"Report","ReportFooter":"Report Footer","ReportHeader":"Report Header","ReportInfo":"ReportInfo","RichTextBox":"RichTextBox","Shape":"Shape","SubReport":"SubReport","TextBox":"TextBox","Unknown":"Unknown","captions":{"basicSupportReportItemInfo":"{{itemLabel}} \'{{itemName}}\' has limited support in design-time."}}},{"lng":"fr","ns":"documentsAPI","resources":{"romLabels":{"chart":"Graphique","dvchart":"DV.Graphique","matrix":"Matrice","table":"Tableau","tablix":"Tableau matriciel"},"textOpenDocumentWarnings":"Document ouvert avec des avertissements","textRenamedItemPrefix":"Content_{{originalName}}","textReportConversion":"Des problèmes sont survenus lors de l\'ouverture de ce rapport","transform":{"helpLink":"Please find more information on report items transformation at {{link}}.","textBadReportItem":"✘ {{SourceType}} \\"{{Name}}\\" n\'a pas pu être transformé en {{ResultType}} dû à une erreur interne:","textError":"– [{{ErrorType}}] {{Message}}","textReport":"The report \\"{{reportName}}\\" a été transformé.","textReportItem":"✔ {{SourceType}} \\"{{Name}}\\" a été transformé en {{ResultType}}."}}},{"lng":"fr","ns":"enums-RPX","resources":{"background_style":{"Gradient":"Gradient","Pattern":"Pattern","Solid":"Solid"},"barcode_caption_position":{"Above":"Above","Below":"Below","None":"None"},"barcode_rotation":{"None":"None","Rotate180Degrees":"Rotate180Degrees","Rotate270Degrees":"Rotate270Degrees","Rotate90Degrees":"Rotate90Degrees"},"barcode_style":{"Ansi39":"Ansi39","Ansi39x":"Ansi39x","Aztec":"Aztec","BC412":"BC412","Codabar":"Codabar","Code25intlv":"Code25intlv","Code25mat":"Code25mat","Code39":"Code39","Code39x":"Code39x","Code49":"Code49","Code93x":"Code93x","Code_11":"Code_11","Code_128_A":"Code_128_A","Code_128_B":"Code_128_B","Code_128_C":"Code_128_C","Code_128auto":"Code_128auto","Code_2_of_5":"Code_2_of_5","Code_93":"Code_93","DataMatrix":"DataMatrix","EAN128FNC1":"EAN128FNC1","EAN_13":"EAN_13","EAN_8":"EAN_8","GS1DataMatrix":"GS1DataMatrix","GS1QRCode":"GS1QRCode","HIBCCode128":"HIBCCode128","HIBCCode39":"HIBCCode39","IATA_2_of_5":"IATA_2_of_5","ISBN":"ISBN","ISMN":"ISMN","ISSN":"ISSN","ITF14":"ITF14","IntelligentMail":"IntelligentMail","IntelligentMailPackage":"IntelligentMailPackage","JapanesePostal":"JapanesePostal","MSI":"MSI","Matrix_2_of_5":"Matrix_2_of_5","MaxiCode":"MaxiCode","MicroPDF417":"MicroPDF417","MicroQRCode":"MicroQRCode","None":"None","PZN":"PZN","Pdf417":"Pdf417","Pharmacode":"Pharmacode","Plessey":"Plessey","PostNet":"PostNet","QRCode":"QRCode","RM4SCC":"RM4SCC","RSS14":"RSS14","RSS14Stacked":"RSS14Stacked","RSS14StackedOmnidirectional":"RSS14StackedOmnidirectional","RSS14Truncated":"RSS14Truncated","RSSExpanded":"RSSExpanded","RSSExpandedStacked":"RSSExpandedStacked","RSSLimited":"RSSLimited","SSCC_18":"SSCC_18","Telepen":"Telepen","UCCEAN128":"UCCEAN128","UPC_A":"UPC_A","UPC_E0":"UPC_E0","UPC_E1":"UPC_E1"},"border_style":{"Dash":"Dash","DashDot":"DashDot","Dot":"Dot","Double":"Double","ExtraThickSolid":"ExtraThickSolid","None":"None","Solid":"Solid","ThickDash":"ThickDash","ThickDashDot":"ThickDashDot","ThickDashDotDot":"ThickDashDotDot","ThickDot":"ThickDot","ThickDouble":"ThickDouble","ThickSolid":"ThickSolid"},"border_style_inputfield":{"Dashed":"Dashed","Inset":"Inset","None":"None","Solid":"Solid"},"calculated_field_type":{"Boolean":"Boolean","Date":"Date","Double":"Double","Float":"Float","Int32":"Int32","None":"None","String":"String"},"calendar":{"Gregorian":"Gregorian","GregorianArabic":"Gregorian Arabic","GregorianMiddleEastFrench":"Gregorian Middle East French","GregorianTransliteratedEnglish":"Gregorian Transliterated English","GregorianTransliteratedFrench":"Gregorian Transliterated French","GregorianUSEnglish":"Gregorian US English","Hebrew":"Hebrew","Hijri":"Hijri","Japanese":"Japanese","Korea":"Korea","Taiwan":"Taiwan","ThaiBuddhist":"Thai Buddhist"},"check_style":{"Check":"Check","Circle":"Circle","Cross":"Cross","Diamond":"Diamond","Square":"Square","Star":"Star"},"collate":{"Collate":"Collate","Default":"Default","DontCollate":"DontCollate"},"column_direction":{"AcrossDown":"AcrossDown","DownAcross":"DownAcross"},"compatibility_mode":{"CrossPlatform":"CrossPlatform","GDI":"GDI"},"content_alignment":{"BottomCenter":"BottomCenter","BottomLeft":"BottomLeft","BottomRight":"BottomRight","MiddleCenter":"MiddleCenter","MiddleLeft":"MiddleLeft","MiddleRight":"MiddleRight","TopCenter":"TopCenter","TopLeft":"TopLeft","TopRight":"TopRight"},"culture":{"af-ZA":"Afrikaans (South Africa)","ar-AE":"Arabic (U.A.E.)","ar-BH":"Arabic (Bahrain)","ar-DZ":"Arabic (Algeria)","ar-EG":"Arabic (Egypt)","ar-IQ":"Arabic (Iraq)","ar-JO":"Arabic (Jordan)","ar-KW":"Arabic (Kuwait)","ar-LB":"Arabic (Lebanon)","ar-LY":"Arabic (Libya)","ar-MA":"Arabic (Morocco)","ar-OM":"Arabic (Oman)","ar-QA":"Arabic (Qatar)","ar-SA":"Arabic (Saudi Arabia)","ar-SY":"Arabic (Syria)","ar-TN":"Arabic (Tunisia)","ar-YE":"Arabic (Yemen)","az-Cyrl-AZ":"Azeri (Cyrillic, Azerbaijan)","az-Latn-AZ":"Azeri (Latin, Azerbaijan)","be-BY":"Belarusian (Belarus)","bg-BG":"Bulgarian (Bulgaria)","ca-ES":"Catalan (Catalan)","cs-CZ":"Czech (Czech Republic)","da-DK":"Danish (Denmark)","de-AT":"German (Austria)","de-CH":"German (Switzerland)","de-DE":"German (Germany)","de-LI":"German (Liechtenstein)","de-LU":"German (Luxembourg)","dv-MV":"Divehi (Maldives)","el-GR":"Greek (Greece)","en-029":"English (Caribbean)","en-AU":"English (Australia)","en-BZ":"English (Belize)","en-CA":"English (Canada)","en-GB":"English (United Kingdom)","en-IE":"English (Ireland)","en-JM":"English (Jamaica)","en-NZ":"English (New Zealand)","en-PH":"English (Republic of the Philippines)","en-TT":"English (Trinidad and Tobago)","en-US":"English (United States)","en-ZA":"English (South Africa)","en-ZW":"English (Zimbabwe)","es-AR":"Spanish (Argentina)","es-BO":"Spanish (Bolivia)","es-CL":"Spanish (Chile)","es-CO":"Spanish (Colombia)","es-CR":"Spanish (Costa Rica)","es-DO":"Spanish (Dominican Republic)","es-EC":"Spanish (Ecuador)","es-ES":"Spanish (Spain)","es-GT":"Spanish (Guatemala)","es-HN":"Spanish (Honduras)","es-MX":"Spanish (Mexico)","es-NI":"Spanish (Nicaragua)","es-PA":"Spanish (Panama)","es-PE":"Spanish (Peru)","es-PR":"Spanish (Puerto Rico)","es-PY":"Spanish (Paraguay)","es-SV":"Spanish (El Salvador)","es-UY":"Spanish (Uruguay)","es-VE":"Spanish (Venezuela)","et-EE":"Estonian (Estonia)","eu-ES":"Basque (Basque)","fa-IR":"Persian (Iran)","fi-FI":"Finnish (Finland)","fo-FO":"Faroese (Faroe Islands)","fr-BE":"French (Belgium)","fr-CA":"French (Canada)","fr-CH":"French (Switzerland)","fr-FR":"French (France)","fr-LU":"French (Luxembourg)","fr-MC":"French (Principality of Monaco)","gl-ES":"Galician (Galician)","gu-IN":"Gujarati (India)","he-IL":"Hebrew (Israel)","hi-IN":"Hindi (India)","hr-BA":"Croatian (Bosnia and Herzegovina)","hr-HR":"Croatian (Croatia)","hu-HU":"Hungarian (Hungary)","hy-AM":"Armenian (Armenia)","id-ID":"Indonesian (Indonesia)","is-IS":"Icelandic (Iceland)","it-CH":"Italian (Switzerland)","it-IT":"Italian (Italy)","ja-JP":"Japanese (Japan)","ka-GE":"Georgian (Georgia)","kk-KZ":"Kazakh (Kazakhstan)","kn-IN":"Kannada (India)","ko-KR":"Korean (Korea)","kok-IN":"Konkani (India)","ky-KG":"Kyrgyz (Kyrgyzstan)","lt-LT":"Lithuanian (Lithuania)","lv-LV":"Latvian (Latvia)","mk-MK":"Macedonian (Former Yugoslav Republic of Macedonia)","mn-MN":"Mongolian (Cyrillic, Mongolia)","mr-IN":"Marathi (India)","ms-BN":"Malay (Brunei Darussalam)","ms-MY":"Malay (Malaysia)","nb-NO":"Norwegian, BokmÃ¥l (Norway)","nl-BE":"Dutch (Belgium)","nl-NL":"Dutch (Netherlands)","nn-NO":"Norwegian, Nynorsk (Norway)","pa-IN":"Punjabi (India)","pl-PL":"Polish (Poland)","pt-BR":"Portuguese (Brazil)","pt-PT":"Portuguese (Portugal)","ro-RO":"Romanian (Romania)","ru-RU":"Russian (Russia)","sa-IN":"Sanskrit (India)","sk-SK":"Slovak (Slovakia)","sl-SI":"Slovenian (Slovenia)","sq-AL":"Albanian (Albania)","sv-FI":"Swedish (Finland)","sv-SE":"Swedish (Sweden)","sw-KE":"Kiswahili (Kenya)","syr-SY":"Syriac (Syria)","ta-IN":"Tamil (India)","te-IN":"Telugu (India)","th-TH":"Thai (Thailand)","tr-TR":"Turkish (Turkey)","tt-RU":"Tatar (Russia)","uk-UA":"Ukrainian (Ukraine)","ur-PK":"Urdu (Islamic Republic of Pakistan)","uz-Cyrl-UZ":"Uzbek (Cyrillic, Uzbekistan)","uz-Latn-UZ":"Uzbek (Latin, Uzbekistan)","vi-VN":"Vietnamese (Vietnam)","zh-CN":"Chinese (People\'s Republic of China)","zh-HK":"Chinese (Hong Kong S.A.R.)","zh-MO":"Chinese (Macao S.A.R.)","zh-SG":"Chinese (Singapore)","zh-TW":"Chinese (Taiwan)"},"ddo_char_set":{"Arab":"Arab","Baltic":"Baltic","CentralEuropean":"Central European","Cyrillic":"Cyrillic","Greek":"Greek","Hebrew":"Hebrew","Turkish":"Turkish","Vietnamese":"Vietnamese","Western":"Western"},"duplex":{"Default":"Default","Horizontal":"Horizontal","Simplex":"Simplex","Vertical":"Vertical"},"field_type":{"Boolean":"Boolean","Date":"Date","Double":"Double","Float":"Float","Int32":"Int32","Integer":"Integer","None":"None","String":"String"},"font_size":{"10pt":"10pt","11pt":"11pt","12pt":"12pt","14pt":"14pt","16pt":"16pt","18pt":"18pt","20pt":"20pt","22pt":"22pt","24pt":"24pt","26pt":"26pt","28pt":"28pt","36pt":"36pt","48pt":"48pt","72pt":"72pt","8pt":"8pt","9pt":"9pt"},"font_style":{"Italic":"Italic","Normal":"Normal"},"font_weight":{"Bold":"Bold","Normal":"Normal"},"format_string":{"Page_PageNumber_of_PageCount":"Page {PageNumber} of {PageCount}","Page_PageNumber_of_PageCount_on_RunDateTime":"Page {PageNumber} of {PageCount} on {RunDateTime}","RunDateTime":"{RunDateTime:}","RunDateTime_MMMM_d_yyyy":"{RunDateTime:MMMM d, yyyy}","RunDateTime_MMMM_yy":"{RunDateTime:MMMM-yy}","RunDateTime_MMMM_yyyy":"{RunDateTime:MMMM-yyyy}","RunDateTime_MMM_yy":"{RunDateTime:MMM-yy}","RunDateTime_MMM_yyyy":"{RunDateTime:MMM-yyyy}","RunDateTime_MM_dd_yy":"{RunDateTime:MM/dd/yy}","RunDateTime_MM_dd_yyyy":"{RunDateTime:MM/dd/yyyy}","RunDateTime_M_d":"{RunDateTime:M/d}","RunDateTime_M_d_yy":"{RunDateTime:M/d/yy}","RunDateTime_M_d_yy_h_mm":"{RunDateTime:M/d/yy h:mm}","RunDateTime_M_d_yy_h_mm_tt":"{RunDateTime:M/d/yy h:mm tt}","RunDateTime_M_d_yyyy_h_mm":"{RunDateTime:M/d/yyyy h:mm}","RunDateTime_M_d_yyyy_h_mm_tt":"{RunDateTime:M/d/yyyy h:mm tt}","RunDateTime_M_d_yyyy}":"{RunDateTime:M/d/yyyy}","RunDateTime_d_MMM":"{RunDateTime:d-MMM}","RunDateTime_d_MMM_yy":"{RunDateTime:d-MMM-yy}","RunDateTime_d_MMM_yyyy":"{RunDateTime:d-MMM-yyyy}","RunDateTime_dd_MMM_yy":"{RunDateTime:dd-MMM-yy}","RunDateTime_dd_MMM_yyyy":"{RunDateTime:dd-MMM-yyyy}"},"gradient_style":{"DiagonalDown":"DiagonalDown","DiagonalUp":"DiagonalUp","FromCenter":"FromCenter","FromCorner":"FromCorner","Horizontal":"Horizontal","Vertical":"Vertical"},"group_keep_together":{"All":"All","FirstDetail":"FirstDetail","None":"None"},"hatch_style":{"BackwardDiagonal":"BackwardDiagonal","DarkDownwardDiagonal":"DarkDownwardDiagonal","DarkHorizontal":"DarkHorizontal","DarkUpwardDiagonal":"DarkUpwardDiagonal","DarkVertical":"DarkVertical","DashedDownwardDiagonal":"DashedDownwardDiagonal","DashedHorizontal":"DashedHorizontal","DashedUpwardDiagonal":"DashedUpwardDiagonal","DashedVertical":"DashedVertical","DiagonalBrick":"DiagonalBrick","DiagonalCross":"DiagonalCross","Divot":"Divot","DottedDiamond":"DottedDiamond","DottedGrid":"DottedGrid","ForwardDiagonal":"ForwardDiagonal","Horizontal":"Horizontal","HorizontalBrick":"HorizontalBrick","LargeCheckerBoard":"LargeCheckerBoard","LargeConfetti":"LargeConfetti","LargeGrid":"LargeGrid","LightDownwardDiagonal":"LightDownwardDiagonal","LightHorizontal":"LightHorizontal","LightUpwardDiagonal":"LightUpwardDiagonal","LightVertical":"LightVertical","NarrowHorizontal":"NarrowHorizontal","NarrowVertical":"NarrowVertical","OutlinedDiamond":"OutlinedDiamond","Percent05":"Percent05","Percent10":"Percent10","Percent20":"Percent20","Percent25":"Percent25","Percent30":"Percent30","Percent40":"Percent40","Percent50":"Percent50","Percent60":"Percent60","Percent70":"Percent70","Percent75":"Percent75","Percent80":"Percent80","Percent90":"Percent90","Plaid":"Plaid","Shingle":"Shingle","SmallCheckerBoard":"SmallCheckerBoard","SmallConfetti":"SmallConfetti","SmallGrid":"SmallGrid","SolidDiamond":"SolidDiamond","Sphere":"Sphere","Trellis":"Trellis","Vertical":"Vertical","Wave":"Wave","Weave":"Weave","WideDownwardDiagonal":"WideDownwardDiagonal","WideUpwardDiagonal":"WideUpwardDiagonal","ZigZag":"ZigZag"},"kinsoku":{"Auto":"Auto","None":"None","True":"True"},"line_style":{"Dash":"Dash","DashDot":"DashDot","DashDotDot":"DashDotDot","Dot":"Dot","Double":"Double","Solid":"Solid","Transparent":"Transparent"},"mime_type":{"image/bmp":"image/bmp","image/gif":"image/gif","image/jpeg":"image/jpeg","image/png":"image/png","image/x-emf":"image/x-emf","image/x-wmf":"image/x-wmf"},"new_column":{"After":"After","Before":"Before","BeforeAfter":"BeforeAfter","None":"None"},"new_page":{"After":"After","Before":"Before","BeforeAfter":"BeforeAfter","None":"None"},"page_orientation":{"Default":"Default","Landscape":"Landscape","Portrait":"Portrait"},"paper_kind":{"A2":"A2","A3":"A3","A3Extra":"A3Extra","A3ExtraTransverse":"A3ExtraTransverse","A3Rotated":"A3Rotated","A3Transverse":"A3Transverse","A4":"A4","A4Extra":"A4Extra","A4Plus":"A4Plus","A4Rotated":"A4Rotated","A4Small":"A4Small","A4Transverse":"A4Transverse","A5":"A5","A5Extra":"A5Extra","A5Rotated":"A5Rotated","A5Transverse":"A5Transverse","A6":"A6","A6Rotated":"A6Rotated","APlus":"APlus","B4":"B4","B4Envelope":"B4Envelope","B4JisRotated":"B4JisRotated","B5":"B5","B5Envelope":"B5Envelope","B5Extra":"B5Extra","B5JisRotated":"B5JisRotated","B5Transverse":"B5Transverse","B6Envelope":"B6Envelope","B6Jis":"B6Jis","B6JisRotated":"B6JisRotated","BPlus":"BPlus","C3Envelope":"C3Envelope","C4Envelope":"C4Envelope","C5Envelope":"C5Envelope","C65Envelope":"C65Envelope","C6Envelope":"C6Envelope","CSheet":"CSheet","Custom":"Custom","DLEnvelope":"DLEnvelope","DSheet":"DSheet","ESheet":"ESheet","Executive":"Executive","Folio":"Folio","GermanLegalFanfold":"GermanLegalFanfold","GermanStandardFanfold":"GermanStandardFanfold","InviteEnvelope":"InviteEnvelope","IsoB4":"IsoB4","ItalyEnvelope":"ItalyEnvelope","JapaneseDoublePostcard":"JapaneseDoublePostcard","JapaneseDoublePostcardRotated":"JapaneseDoublePostcardRotated","JapaneseEnvelopeChouNumber3":"JapaneseEnvelopeChouNumber3","JapaneseEnvelopeChouNumber3Rotated":"JapaneseEnvelopeChouNumber3Rotated","JapaneseEnvelopeChouNumber4":"JapaneseEnvelopeChouNumber4","JapaneseEnvelopeChouNumber4Rotated":"JapaneseEnvelopeChouNumber4Rotated","JapaneseEnvelopeKakuNumber2":"JapaneseEnvelopeKakuNumber2","JapaneseEnvelopeKakuNumber2Rotated":"JapaneseEnvelopeKakuNumber2Rotated","JapaneseEnvelopeKakuNumber3":"JapaneseEnvelopeKakuNumber3","JapaneseEnvelopeKakuNumber3Rotated":"JapaneseEnvelopeKakuNumber3Rotated","JapaneseEnvelopeYouNumber4":"JapaneseEnvelopeYouNumber4","JapaneseEnvelopeYouNumber4Rotated":"JapaneseEnvelopeYouNumber4Rotated","JapanesePostcard":"JapanesePostcard","JapanesePostcardRotated":"JapanesePostcardRotated","Ledger":"Ledger","Legal":"Legal","LegalExtra":"LegalExtra","Letter":"Letter","LetterExtra":"LetterExtra","LetterExtraTransverse":"LetterExtraTransverse","LetterPlus":"LetterPlus","LetterRotated":"LetterRotated","LetterSmall":"LetterSmall","LetterTransverse":"LetterTransverse","MonarchEnvelope":"MonarchEnvelope","Note":"Note","Number10Envelope":"Number10Envelope","Number11Envelope":"Number11Envelope","Number12Envelope":"Number12Envelope","Number14Envelope":"Number14Envelope","Number9Envelope":"Number9Envelope","PersonalEnvelope":"PersonalEnvelope","Prc16K":"Prc16K","Prc16KRotated":"Prc16KRotated","Prc32K":"Prc32K","Prc32KBig":"Prc32KBig","Prc32KBigRotated":"Prc32KBigRotated","Prc32KRotated":"Prc32KRotated","PrcEnvelopeNumber1":"PrcEnvelopeNumber1","PrcEnvelopeNumber10":"PrcEnvelopeNumber10","PrcEnvelopeNumber10Rotated":"PrcEnvelopeNumber10Rotated","PrcEnvelopeNumber1Rotated":"PrcEnvelopeNumber1Rotated","PrcEnvelopeNumber2":"PrcEnvelopeNumber2","PrcEnvelopeNumber2Rotated":"PrcEnvelopeNumber2Rotated","PrcEnvelopeNumber3":"PrcEnvelopeNumber3","PrcEnvelopeNumber3Rotated":"PrcEnvelopeNumber3Rotated","PrcEnvelopeNumber4":"PrcEnvelopeNumber4","PrcEnvelopeNumber4Rotated":"PrcEnvelopeNumber4Rotated","PrcEnvelopeNumber5":"PrcEnvelopeNumber5","PrcEnvelopeNumber5Rotated":"PrcEnvelopeNumber5Rotated","PrcEnvelopeNumber6":"PrcEnvelopeNumber6","PrcEnvelopeNumber6Rotated":"PrcEnvelopeNumber6Rotated","PrcEnvelopeNumber7":"PrcEnvelopeNumber7","PrcEnvelopeNumber7Rotated":"PrcEnvelopeNumber7Rotated","PrcEnvelopeNumber8":"PrcEnvelopeNumber8","PrcEnvelopeNumber8Rotated":"PrcEnvelopeNumber8Rotated","PrcEnvelopeNumber9":"PrcEnvelopeNumber9","PrcEnvelopeNumber9Rotated":"PrcEnvelopeNumber9Rotated","Quarto":"Quarto","Standard10x11":"Standard10x11","Standard10x14":"Standard10x14","Standard11x17":"Standard11x17","Standard12x11":"Standard12x11","Standard15x11":"Standard15x11","Standard9x11":"Standard9x11","Statement":"Statement","Tabloid":"Tabloid","TabloidExtra":"TabloidExtra","USStandardFanfold":"USStandardFanfold"},"paper_size":{"A3":"A3","A4":"A4","A5":"A5","A6":"A6","Custom":"Custom","Executive":"Executive","ISOB5":"B5 (ISO)","JISB4":"B4 (JIS)","JISB5":"B5 (JIS)","JISB6":"B6 (JIS)","Legal":"Legal","Letter":"Letter","Tabloid":"Tabloid"},"paper_source":{"AutomaticFeed":"Automatic Feed","Cassette":"Cassette","Manual":"Manual"},"paper_source_kind":{"AutomaticFeed":"AutomaticFeed","Cassette":"Cassette","Custom":"Custom","Envelope":"Envelope","FormSource":"FormSource","LargeCapacity":"LargeCapacity","LargeFormat":"LargeFormat","Lower":"Lower","Manual":"Manual","ManualFeed":"ManualFeed","Middle":"Middle","SmallFormat":"SmallFormat","TractorFeed":"TractorFeed","Upper":"Upper"},"parameter_type":{"Boolean":"Boolean","Date":"Date","String":"String"},"picture_alignment":{"BottomLeft":"BottomLeft","BottomRight":"BottomRight","Center":"Center","TopLeft":"TopLeft","TopRight":"TopRight"},"repeat_style":{"All":"All","None":"None","OnColumn":"OnColumn","OnPage":"OnPage","OnPageIncludeNoDetail":"OnPageIncludeNoDetail"},"richrext_rendering_type":{"Metafile":"Metafile","PNG":"PNG","RTF":"RTF","TransparentPNG":"TransparentPNG"},"script_language":{"C#":"C#","VBNET":"VB.NET"},"shape_style":{"Ellipse":"Ellipse","Rectangle":"Rectangle","RoundRect":"RoundRect"},"size_mode":{"Clip":"Clip","Stretch":"Stretch","Zoom":"Zoom"},"string_alignment":{"Center":"Center","Far":"Far","Near":"Near"},"string_alignment_inputfield":{"Center":"Center","Left":"Left","Right":"Right"},"summary_func":{"Avg":"Avg","Count":"Count","DAvg":"DAvg","DCount":"DCount","DStdDev":"DStdDev","DStdDevP":"DStdDevP","DSum":"DSum","DVar":"DVar","DVarP":"DVarP","Max":"Max","Min":"Min","StdDev":"StdDev","StdDevP":"StdDevP","Sum":"Sum","Var":"Var","VarP":"VarP"},"summary_running":{"All":"All","Group":"Group","None":"None"},"summary_type":{"GrandTotal":"GrandTotal","None":"None","PageCount":"PageCount","PageTotal":"PageTotal","SubTotal":"SubTotal"},"text_align":{"Center":"Center","Justify":"Justify","Left":"Left","Right":"Right"},"text_justify":{"Auto":"Auto","Distribute":"Distribute","Distribute_all_lines":"DistributeAllLines"},"vertical_align":{"Bottom":"Bottom","Middle":"Middle","Top":"Top"},"white_space":{"NoWrap":"NoWrap","Normal":"Normal","Pre":"Pre"},"word_wrap":{"CharWrap":"CharWrap","NoWrap":"NoWrap","WordWrap":"WordWrap"}}},{"lng":"fr","ns":"enums","resources":{"action":{"ApplyParameters":"Appliquer paramètres","BookmarkLink":"Aller au signet","Drillthrough":"Aller au rapport","Hyperlink":"Aller à l\'URL","None":"Aucun"},"action_apply_value_cmd":{"Reset":"Reset","Set":"Set","Toggle":"Toggle"},"auto_merge_mode":{"Always":"Toujours","Never":"Jamais","Restricted":"Restreint"},"auto_width":{"None":"Aucun","Proportional":"Proportionnel"},"axis_location":{"Left":"Gauche","Right":"Droite"},"axis_mode":{"Cartesian":"Cartésien","Polygonal":"Polygonal","Radial":"Radial"},"barcode_caption_location":{"Above":"Au dessus","Below":"En dessous","None":"Aucun"},"barcode_ecc000_140_symbol_size":{"Auto":"Auto","Square11":"Carré11","Square13":"Carré13","Square15":"Carré15","Square17":"Carré17","Square19":"Carré19","Square21":"Carré21","Square23":"Carré23","Square25":"Carré25","Square27":"Carré27","Square29":"Carré29","Square31":"Carré31","Square33":"Carré33","Square35":"Carré35","Square37":"Carré37","Square39":"Carré39","Square41":"Carré41","Square43":"Carré43","Square45":"Carré45","Square47":"Carré47","Square49":"Carré49","Square9":"Carré9"},"barcode_ecc200_encoding_mode":{"ASCII":"ASCII","Auto":"Auto","Base256":"Base256","C40":"C40","EDIFACT":"EDIFACT","Text":"Texte","X12":"X12"},"barcode_ecc200_symbol_size":{"Rectangular12x26":"Rectangulaire12x26","Rectangular12x36":"Rectangulaire12x36","Rectangular16x36":"Rectangulaire16x36","Rectangular16x48":"Rectangulaire16x48","Rectangular8x18":"Rectangulaire8x18","Rectangular8x32":"Rectangulaire8x32","RectangularAuto":"Rectangulaire Auto","Square10":"Carré10","Square104":"Carré104","Square12":"Carré12","Square120":"Carré120","Square132":"Carré132","Square14":"Carré14","Square144":"Carré144","Square16":"Carré16","Square18":"Carré18","Square20":"Carré20","Square22":"Carré22","Square24":"Carré24","Square26":"Carré26","Square32":"Carré32","Square36":"Carré36","Square40":"Carré40","Square44":"Carré44","Square48":"Carré48","Square52":"Carré52","Square64":"Carré64","Square72":"Carré72","Square80":"Carré80","Square88":"Carré88","Square96":"Carré96","SquareAuto":"Carré Auto"},"barcode_ecc_mode":{"ECC000":"ECC000","ECC050":"ECC050","ECC080":"ECC080","ECC100":"ECC100","ECC140":"ECC140","ECC200":"ECC200"},"barcode_encoding":{"37":"IBM EBCDIC (US-Canada) 37","437":"OEM USA 437","500":"IBM EBCDIC (International) 500","708":"Arabe (ASMO 708) 708","720":"Arabe (DOS) 720","737":"Grec (DOS) 737","775":"Baltique (DOS) 775","850":"Europe de l\'ouest (DOS) 850","852":"Europe centrale (DOS) 852","855":"OEM Cyrillique 855","857":"Turc (DOS) 857","858":"OEM Latin multilangue I 858","860":"Portuguais(DOS) 860","861":"Islandais (DOS) 861","862":"Hébreu (DOS) 862","863":"Français canadien (DOS) 863","864":"Arabe (864) 864","865":"Nordique (DOS) 865","866":"Cyrillique (DOS) 866","869":"Grec moderne(DOS) 869","870":"IBM EBCDIC (Latin multilangue-2) 870","874":"Thaïlandais (Windows) 874","875":"IBM EBCDIC (Grec moderne) 875","932":"Japonais (Shift-JIS) 932","936":"Chinois simplifié (GB2312) 936","949":"Coréen 949","950":"Chinois traditionnel (Big5) 950","1026":"IBM EBCDIC (Turc Latin-5) 1026","1047":"IBM Latin-1 1047","1140":"IBM EBCDIC (US-Canada-Euro) 1140","1141":"IBM EBCDIC (Allemagne-Euro) 1141","1142":"IBM EBCDIC (Danemark-Norvège-Euro) 1142","1143":"IBM EBCDIC (Finlande-Suède-Euro) 1143","1144":"IBM EBCDIC (Italie-Euro) 1144","1145":"IBM EBCDIC (Espagne-Euro) 1145","1146":"IBM EBCDIC (Royaume-Uni-Euro) 1146","1147":"IBM EBCDIC (France-Euro) 1147","1148":"IBM EBCDIC (International-Euro) 1148","1149":"IBM EBCDIC (Islande-Euro) 1149","1200":"Unicode 1200","1201":"Unicode (Big-Endian) 1201","1250":"Europe centrale (Windows) 1250","1251":"Cyrillique (Windows) 1251","1252":"Europe de l\'ouest (Windows) 1252","1253":"Grec (Windows) 1253","1254":"Turc (Windows) 1254","1255":"Hébreu (Windows) 1255","1256":"Arabe (Windows) 1256","1257":"Baltique (Windows) 1257","1258":"Vietnamien (Windows) 1258","1361":"Coréen (Johab) 1361","10000":"Europe de l\'ouest (Mac) 10000","10001":"Japonais (Mac) 10001","10002":"Chinois traditionnel (Mac) 10002","10003":"Coréen (Mac) 10003","10004":"Arabe (Mac) 10004","10005":"Hébreu (Mac) 10005","10006":"Grec (Mac) 10006","10007":"Cyrillique (Mac) 10007","10008":"Chinois simplifié (Mac) 10008","10010":"Roumain (Mac) 10010","10017":"Ukrainien (Mac) 10017","10021":"Thaïlandais (Mac) 10021","10029":"Europe centrale (Mac) 10029","10079":"Islandais (Mac) 10079","10081":"Turc (Mac) 10081","10082":"Croate (Mac) 10082","12000":"Unicode (UTF-32) 12000","12001":"Unicode (UTF-32 Big-Endian) 12001","20000":"Chinois traditionnel (CNS) 20000","20001":"TCA Taiwan 20001","20002":"Chinois traditionnel (Eten) 20002","20003":"IBM5550 Taiwan 20003","20004":"TeleText Taiwan 20004","20005":"Wang Taiwan 20005","20105":"Europe de l\'ouest (IA5) 20105","20106":"Allemand (IA5) 20106","20107":"Suédois (IA5) 20107","20108":"Norvégien (IA5) 20108","20127":"US-ASCII 20127","20261":"T.61 20261","20269":"ISO-6937 20269","20273":"IBM EBCDIC (Allemagne) 20273","20277":"IBM EBCDIC (Danemark-Norvège) 20277","20278":"IBM EBCDIC (Finlande-Suède) 20278","20280":"IBM EBCDIC (Italie) 20280","20284":"IBM EBCDIC (Espagne) 20284","20285":"IBM EBCDIC (Royaume-Uni) 20285","20290":"IBM EBCDIC (Japonais katakana) 20290","20297":"IBM EBCDIC (France) 20297","20420":"IBM EBCDIC (Arabe) 20420","20423":"IBM EBCDIC (Grec) 20423","20424":"IBM EBCDIC (Hébreu) 20424","20833":"IBM EBCDIC (Coréen étendu) 20833","20838":"IBM EBCDIC (Thaïlandais) 20838","20866":"Cyrillique (KOI8-R) 20866","20871":"IBM EBCDIC (Islandais) 20871","20880":"IBM EBCDIC (Cyrillique Russe) 20880","20905":"IBM EBCDIC (Turc) 20905","20924":"IBM Latin-1 20924","20932":"Japonais (JIS 0208-1990 and 0212-1990) 20932","20936":"Chinois simplifié (GB2312-80) 20936","20949":"Coréen Wansung 20949","21025":"IBM EBCDIC (Cyrillique Serbe-Bulgare) 21025","21866":"Cyrillique (KOI8-U) 21866","28591":"Europe de l\'ouest (ISO) 28591","28592":"Europe centrale (ISO) 28592","28593":"Latin 3 (ISO) 28593","28594":"Baltique (ISO) 28594","28595":"Cyrillique (ISO) 28595","28596":"Arabe (ISO) 28596","28597":"Grec (ISO) 28597","28598":"Hébreu (ISO-Visual) 28598","28599":"Turc (ISO) 28599","28603":"Estonien (ISO) 28603","28605":"Latin 9 (ISO) 28605","29001":"Europe 29001","38598":"Hébreu (ISO-Logical) 38598","50220":"Japonais (JIS) 50220","50221":"Japonais (JIS-Allow 1 byte Kana) 50221","50222":"Japonais (JIS-Allow 1 byte Kana SO/SI) 50222","50225":"Coréen (ISO) 50225","50227":"Chinois simplifié (ISO-2022) 50227","51932":"Japonais (EUC) 51932","51936":"Chinois simplifié (EUC) 51936","51949":"Coréen (EUC) 51949","52936":"Chinois simplifié (HZ) 52936","54936":"Chinois simplifié (GB18030) 54936","57002":"ISCII Devanagari 57002","57003":"ISCII Bengali 57003","57004":"ISCII Tamil 57004","57005":"ISCII Telugu 57005","57006":"ISCII Assamese 57006","57007":"ISCII Oriya 57007","57008":"ISCII Kannada 57008","57009":"ISCII Malayalam 57009","57010":"ISCII Gujarati 57010","57011":"ISCII Punjabi 57011","65000":"Unicode (UTF-7) 65000","65001":"Unicode (UTF-8) 65001"},"barcode_gs1_composite_type":{"CCA":"CCA","None":"Aucun"},"barcode_maxicode_mode":{"Mode2":"Mode2","Mode3":"Mode3","Mode4":"Mode4","Mode5":"Mode5","Mode6":"Mode6"},"barcode_micro_pdf417_compaction_mode":{"Auto":"Auto","ByteCompactionMode":"Mode compactage byte","NumericCompactionMode":"Mode compactage numérique","TextCompactionMode":"Mode compactage de texte"},"barcode_micro_pdf417_version":{"ColumnPriorAuto":"ColonnePriorAuto","RowPriorAuto":"LignePriorAuto","Version1X11":"Version1X11","Version1X14":"Version1X14","Version1X17":"Version1X17","Version1X20":"Version1X20","Version1X24":"Version1X24","Version1X28":"Version1X28","Version2X11":"Version2X11","Version2X14":"Version2X14","Version2X17":"Version2X17","Version2X20":"Version2X20","Version2X23":"Version2X23","Version2X26":"Version2X26","Version2X8":"Version2X8","Version3X10":"Version3X10","Version3X12":"Version3X12","Version3X15":"Version3X15","Version3X20":"Version3X20","Version3X26":"Version3X26","Version3X32":"Version3X32","Version3X38":"Version3X38","Version3X44":"Version3X44","Version3X6":"Version3X6","Version3X8":"Version3X8","Version4X10":"Version4X10","Version4X12":"Version4X12","Version4X15":"Version4X15","Version4X20":"Version4X20","Version4X26":"Version4X26","Version4X32":"Version4X32","Version4X38":"Version4X38","Version4X4":"Version4X4","Version4X44":"Version4X44","Version4X6":"Version4X6","Version4X8":"Version4X8"},"barcode_micro_qrcode_error_level":{"L":"L","M":"M","Q":"Q"},"barcode_micro_qrcode_mask":{"Auto":"Auto","Mask00":"Masque00","Mask01":"Masque01","Mask10":"Masque10","Mask11":"Masque11"},"barcode_micro_qrcode_version":{"Auto":"Auto","M1":"M1","M2":"M2","M3":"M3","M4":"M4"},"barcode_pdf417_error_correction_level":{"Level0":"Niveau0","Level1":"Niveau1","Level2":"Niveau2","Level3":"Niveau3","Level4":"Niveau4","Level5":"Niveau5","Level6":"Niveau6","Level7":"Niveau7","Level8":"Niveau8"},"barcode_pdf417_type":{"Normal":"Normal","Simple":"Simple"},"barcode_qrcode_error_level":{"H":"H","L":"L","M":"M","Q":"Q"},"barcode_qrcode_mask":{"Auto":"Auto","Mask000":"Masque000","Mask001":"Masque001","Mask010":"Masque010","Mask011":"Masque011","Mask100":"Masque100","Mask101":"Masque101","Mask110":"Masque110","Mask111":"Masque111"},"barcode_qrcode_model":{"Model1":"Modèle1","Model2":"Modèle2"},"barcode_rotation":{"None":"Aucun","Rotate180Degrees":"Rotation180Degrés","Rotate270Degrees":"Rotation270Degrés","Rotate90Degrees":"Rotation90Degrés"},"barcode_symbology":{"Ansi39":"ANSI 3 sur 9","Ansi39x":"ANSI Etendu 3 sur 9","Aztec":"Aztec","BC412":"BC412","Codabar":"Codabar","Code25intlv":"Entrelaçé 2 sur 5","Code39":"Code 39","Code39x":"Code étendu 39","Code49":"Code 49","Code93x":"Code étendu 93","Code_11":"Code 11","Code_128_A":"Code 128 A","Code_128_B":"Code 128 B","Code_128_C":"Code 128 C","Code_128auto":"Code 128 Auto","Code_2_of_5":"Code 2 sur 5","Code_93":"Code 93","DataMatrix":"Matrice données","EAN128FNC1":"EAN-128FNC1","EAN_13":"EAN-13","EAN_8":"EAN-8","GS1DataMatrix":"GS1 Matrice données","GS1QRCode":"GS1 QR Code","HIBCCode128":"HIBC Code 128","HIBCCode39":"HIBC Code 39","IATA_2_of_5":"IATA 2 sur 5","ISBN":"ISBN (International Standard Book Number)","ISMN":"ISMN (Internationally Standard Music Number)","ISSN":"ISSN (International Standard Serial Number)","ITF14":"ITF-14","IntelligentMail":"Mail Intelligent","IntelligentMailPackage":"Package mail Intelligent","JapanesePostal":"Poste japonaise","MSI":"MSI Code","Matrix_2_of_5":"Matrice 2 sur 5","MaxiCode":"MaxiCode","MicroPDF417":"Micro PDF417","MicroQRCode":"Micro QR Code","None":"Aucun","PZN":"PZN (Pharmaceutical Central Number)","Pdf417":"PDF417","Pharmacode":"Pharmacode","Plessey":"Plessey","PostNet":"PostNet","QRCode":"QR Code","RM4SCC":"RM4SCC (Royal Mail)","RSS14":"RSS-14","RSS14Stacked":"RSS-14 Empilé","RSS14StackedOmnidirectional":"RSS-14 Empilé Omnidirectionel","RSS14Truncated":"RSS-14 Tronqué","RSSExpanded":"RSS Étendu","RSSExpandedStacked":"RSS Étendu empilé","RSSLimited":"RSS Limité","SSCC_18":"SSCC-18","Telepen":"Telepen","UCCEAN128":"UCC/EAN–128","UPC_A":"UPC-A","UPC_E0":"UPC-E0","UPC_E1":"UPC-E1"},"border_style":{"DashDot":"TiretPoint","DashDotDot":"TiretPointPoint","Dashed":"Tiret","Dotted":"Pointillé","Double":"Double","Groove":"Ombre","Inset":"Demi-ombre","None":"Aucun","Outset":"Demi-ombre2","Ridge":"Ombre2","Solid":"Pleine","WindowInset":"WindowInset"},"bullet_tick_marks":{"Inside":"Intérieur","None":"Aucun","Outside":"Extérieur"},"calendar":{"Gregorian":"Gregorien","GregorianArabic":"Gregorien Arabe","GregorianMiddleEastFrench":"Gregorien Français Europe centrale","GregorianTransliteratedEnglish":"Gregorien Translittéré Anglais","GregorianTransliteratedFrench":"Gregorien Translittéré Français","GregorianUSEnglish":"Gregorien US Anglais","Hebrew":"Hébreu","Hijri":"Hijri","Japanese":"Japonais","Korean":"Coréen","Taiwan":"Taïwan","ThaiBuddhist":"Thaï Bouddhiste"},"check_alignment":{"BottomCenter":"BasCentre","BottomLeft":"BasGauche","BottomRight":"BasDroite","MiddleCenter":"MilieuCentre","MiddleLeft":"MilieuGauche","MiddleRight":"MilieuDroit","TopCenter":"HautCentre","TopLeft":"HautGauche","TopRight":"HautDroite"},"check_style":{"Check":"Coche","Circle":"Cercle","Cross":"Croix","Diamond":"Diamant","Square":"Carré","Star":"Étoile"},"collate_by":{"Simple":"Simple","Value":"Veleur","ValueIndex":"ValeurIndex"},"collation":{"Albanian":"Albanais","Arabic":"Arabe","Chinese_PRC":"RPC Chinois","Chinese_PRC_Stroke":"RPC Stroke Chinois","Chinese_Taiwan_Bopomofo":"Chinois Taiwan Bopomofo","Chinese_Taiwan_Stroke":"Chinois Taiwan Stroke","Croatian":"Croate","Cyrillic_General":"Cyrillique Général","Czech":"Tchèque","Danish_Norwegian":"Danois Norvégien","Default":"Defaut","Estonian":"Estonien","FYRO_Macedonian":"Macedonien FYRO","Finnish_Swedish":"Finnois Suédois","French":"Français","Georgian_Modern_Sort":"Georgien Moderne","German_PhoneBook":"Annuaire téléphonique allemand","Greek":"Grc","Hebrew":"Hébreu","Hindi":"Hindi","Hungarian":"Hongrois","Hungarian_Technical":"Hongrois Technique","Icelandic":"Islandais","Japanese":"Japonais","Japanese_Unicode":"Japonais Unicode","Korean_Wansung":"Coréen Wansung","Korean_Wansung_Unicode":"Coréen Wansung Unicode","Latin1_General":"Latin-1 Général","Latvian":"Letton","Lithuanian":"Lituanien","Lithuanian_Classic":"Lituanien Classique","Polish":"Polonais","Romanian":"Roumain","Slovak":"Slovaque","Slovenian":"Slovène","Spanish_Mexican_Trad":"Espagnol Mexicain Traditionnel","Spanish_Modern":"Espagnol Moderne","Thai":"Thaïlandais","Turkish":"Turc","Ukrainian":"Ukrainien","Vietnamese":"Vietnamien"},"data_element_output":{"Auto":"Auto","ContentsOnly":"ContenuSeulement","NoOutput":"AucuneSortie","Output":"Sortie"},"data_element_output_aon":{"Auto":"Auto","NoOutput":"AucuneSortie","Output":"Sortie"},"data_element_output_on":{"NoOutput":"AucuneSortie","Output":"Sortie"},"data_element_style":{"AttributeNormal":"AttributNormal","Auto":"Auto","ElementNormal":"ElementNormal"},"data_label_position":{"Auto":"Auto","Bottom":"Bas","BottomLeft":"BasGauche","BottomRight":"BasDroite","Center":"Centre","Left":"Gauche","Right":"Droite","Top":"Haut","TopLeft":"HautGauche","TopRight":"HautDroite"},"data_visualizer_color_type":{"colorScale":"Echelle de couleurs"},"data_visualizer_gradient_type":{"DiagonalDown":"DiagonaleBas","DiagonalUp":"DiagonaleHaut","FromCenter":"APartirDuCentre","FromCorner":"APartirDuCoin","Horizontal":"Horizontal","Vertical":"Vertical"},"data_visualizer_hatch_style":{"BackwardDiagonal":"DiagonaleArrière","Cross":"Cross","DarkDownwardDiagonal":"VersLeBasDiagonaleSombre","DarkHorizontal":"HorizontalSombre","DarkUpwardDiagonal":"VerLeHautDiagonaleSombre","DarkVertical":"VerticalSombre","DashedDownwardDiagonal":"TiretVersLeBasDiagonale","DashedHorizontal":"TiretHorizontal","DashedUpwardDiagonal":"TiretVersLeHautDiagonale","DashedVertical":"TiretVertical","DiagonalBrick":"BriqueDiagonale","DiagonalCross":"CroixDiagonale","Divot":"Divot","DottedDiamond":"PointDiamant","DottedGrid":"PointGrille","ForwardDiagonal":"DiagonaleAvant","Horizontal":"Horizontal","HorizontalBrick":"BriqueHorizontal","LargeCheckerBoard":"GrandDamier","LargeConfetti":"GrandConfetti","LargeGrid":"GrilleLarge","LightDownwardDiagonal":"VersLeBasDiagonaleClaire","LightHorizontal":"HorizontalClair","LightUpwardDiagonal":"VersLeHautDiagonaleClaire","LightVertical":"VerticalClair","NarrowHorizontal":"ÉtroitHorizontal","NarrowVertical":"ÉtroitVertical","OutlinedDiamond":"DiamantÉsquissé","Percent05":"Pourcentage05","Percent10":"Pourcentage10","Percent20":"Pourcentage20","Percent25":"Pourcentage25","Percent30":"Pourcentage30","Percent40":"Pourcentage40","Percent50":"Pourcentage50","Percent60":"Pourcentage60","Percent70":"Pourcentage70","Percent75":"Pourcentage75","Percent80":"Pourcentage80","Percent90":"Pourcentage90","Plaid":"Plaid","Shingle":"Galets","SmallCheckerBoard":"PetitDamier","SmallConfetti":"PetitConfetti","SmallGrid":"PetiteGrille","SolidDiamond":"DiamantPlein","Sphere":"Sphere","Trellis":"Treillis","Vertical":"Vertical","Wave":"Vague","Weave":"Tissage","WideDownwardDiagonal":"LargeDiagonaleVersLeBas","WideUpwardDiagonal":"LargeDiagonaleVersLeHaut","ZigZag":"ZigZag"},"data_visualizer_icon_set_type":{"3TrafficLights":"FeuTricolore","Arrows":"flèches","Blank":"Vierge","Checkbox":"Case à cocher","Flags":"Drapeaux","GrayArrows":"FlèchesGrises","Quarters":"Quarts","Ratings":"Notes","RedToBlack":"RougeVersNoir","Signs":"Panneaux","Symbols1":"Symbole1","Symbols2":"Symbole2","TrafficLights":"FeuxCirculation"},"data_visualizer_icon_set_value":{"false":"Faux","true":"Vrai"},"data_visualizer_image_type":{"dataBar":"Barre données","gradient":"Gradiant","hatch":"Hachure","iconSet":"Heu d\'icônes","rangeBar":"Barre plage"},"dataset_option":{"Auto":"Auto","false":"Faux","true":"Vrai"},"dataset_query_command_type":{"StoredProcedure":"ProcédureStockée","Text":"Texte"},"direction":{"LTR":"GvD","RTL":"DvG"},"display_type":{"Galley":"Galerie","Page":"Page"},"documentmap_numbering_style":{"CircledNumber":"①, ②, ③, ④, ⑤","Decimal":"1, 2, 3, 4, 5","DecimalLeadingZero":"01, 02, 03, 04, 05","Georgian":"ა, ბ, გ, დ, ე","Katakana":"ア, イ, ウ, エ, オ","KatakanaBrackets":"(ア), (イ), (ウ), (エ), (オ)","KatakanaIroha":"イ, ロ, ハ, ニ, ホ","KatakanaIrohaBrackets":"イ), ロ), ハ), ニ), ホ)","KatakanaIrohaLower":"ｨ, ﾛ, ﾊ, ﾆ, ﾎ","KatakanaLower":"ｱ, ｲ, ｳ, ｴ, ｵ","LowerArmenian":"ա, բ, գ, դ, ե","LowerGreek":"α, β, γ, δ, ε","LowerLatin":"a, b, c, d, e","LowerRoman":"i, ii, iii, iv, v","None":"aucun","UpperArmenian":"Ա, Բ, Գ, Դ, Ե","UpperGreek":"Α, Β, Γ, Δ, Ε","UpperLatin":"A, B, C, D, E","UpperRoman":"I, II, III, IV, V"},"documentmap_source":{"All":"Tout","Headings":"Rubriques","Labels":"Libellés","None":"Aucun"},"dvchart_aggregate_type":{"Average":"Moyenne","Count":"Compte","CountDistinct":"CountDistinct","CountOfAll":"CompteDeTout","List":"Liste","Max":"Max","Min":"Min","None":"Aucun","PopulationStandardDeviation":"Écart-typePopulation","PopulationVariance":"ÉcartDémographique","Range":"Plage","StandardDeviation":"DeviationStandard","Sum":"Somme","Variance":"Variation"},"dvchart_axis_date_mode":{"Day":"Jour","Default":"Defaut","Hour":"Heure","Millisecond":"Milliseconde","Minute":"Minute","Month":"Mois","Second":"Seconde","Week":"Semaine","Year":"Année"},"dvchart_axis_overlapping_labels":{"Auto":"Auto","Show":"Afficher"},"dvchart_axis_position":{"Far":"Lointain","Near":"Proche","None":"Aucun"},"dvchart_axis_type":{"X":"X","Y":"Y"},"dvchart_clipping_mode":{"Clip":"Couper","Fit":"Ajuster","None":"Aucun"},"dvchart_encoding_field_type":{"Complex":"Complexe","Simple":"Simple"},"dvchart_encoding_sort":{"Ascending":"Ascendant","Descending":"Descendant","None":"Aucun"},"dvchart_group_type":{"Cluster":"Grappe","None":"Aucun","Stack":"Empilement"},"dvchart_group_type_wizard":{"Cluster":"Grappe","PercentStacked":"Pourcentage Empilement","Stacked":"Empilement"},"dvchart_halign":{"Center":"Centre","Left":"Gauche","Right":"Droite"},"dvchart_legend_position":{"Bottom":"Bas","Left":"Gauche","Right":"Droite","Top":"Haut"},"dvchart_line_aspect":{"Default":"Defaut","Spline":"Courbe","StepCenter":"PasVersCentre","StepLeft":"PasVersGauche","StepRight":"PAsVersDroite"},"dvchart_line_position":{"Auto":"Auto","Center":"Centre"},"dvchart_orientation":{"Horizontal":"Horizontal","Vertical":"Vertical"},"dvchart_overlapping_labels":{"Auto":"Auto","Hide":"Masquer","Show":"Afficher"},"dvchart_palette":{"Aspect":"Aspect","Blue":"Bleu","Blue2":"Bleu2","BlueGreen":"BleuVert","BlueWarm":"BleuChaud","Cerulan":"Azuré","Cocoa":"Cacao","Coral":"Corail","Custom":"Personnalisé","Cyborg":"Cyborg","Dark":"Sombre","Darkly":"Obscure","Flatly":"Flatly","Grayscale":"EchelleGris","Green":"Vert","GreenYellow":"VertJaune","HighContrast":"ContrasteÉlevé","Light":"Clair","Marquee":"Défilement","Median":"Médian","Midnight":"Minuit","Modern":"Moderne","Office":"Office","Office2010":"Office2010","Orange":"Orange","OrangeRed":"OrangeRouge","Organic":"Organique","Paper":"Papier","Red":"Rouge","RedOrange":"RougeOrange","RedViolet":"RougeViolet","Slate":"Ardoise","Slipstream":"Slipstream","Standard":"Standard","Superhero":"Superheros","Violet":"Violet","Violet2":"Violet2","Yellow":"Jaune","YellowOrange":"JauneOrange","Zen":"Zen"},"dvchart_plot_overlay_aggregate_type":{"Average":"Moyenne","Count":"Compte","Max":"Max","Median":"Médian","Min":"Min","Percentile":"Centile","Sum":"Somme"},"dvchart_plot_overlay_axis":{"X":"X","Y":"Y"},"dvchart_plot_overlay_detail_level":{"Group":"Groupe","Total":"Total"},"dvchart_plot_overlay_display":{"Back":"Arrière","Front":"Avant"},"dvchart_plot_overlay_type":{"CumulativeMovingAverageTrendline":"TendanceMoyenneMobileCumulative","ExponentialMovingAverageTrendline":"TendanceMoyenneMobileExponentielle","ExponentialTrendline":"TendanceExponentielle","FourierTrendline":"TendanceFourier","LinearTrendline":"TendanceLinéaire","LogarithmicTrendline":"TendanceLogarithmique","MovingAnnualTotalTrendline":"TendanceAnnuelleTotaleMobile","MovingAverageTrendline":"TendanceMoyenneMobile","PolynomialTrendline":"TendancePolynôme","PowerTrendline":"TendancePuissance","ReferenceBand":"BandeRéférence","ReferenceLine":"LigneRéférence","WeightedMovingAverageTrendline":"TendanceMoyenneMobilePondérée"},"dvchart_plot_rule_properties":{"BackgroundColor":"CouleurFond","LabelTemplate":"ModèleLibellé","LineColor":"CouleurLigne","LineStyle":"StyleLigne","LineWidth":"LargeurLigne","SymbolBackgroundColor":"CouleurArrière-planSymbole","SymbolLineColor":"CouleurLigneSymbole","SymbolLineStyle":"StyleLigneSymbole","SymbolLineWidth":"LargeurLigneSymbole","TooltipTemplate":"ModèleInfo-bulle"},"dvchart_plot_type":{"Area":"Zone","Bar":"Barre","Candlestick":"Chandelier","HighLowOpenClose":"HautBasOuvrirFermer","Line":"Ligne","Scatter":"Dispersion"},"dvchart_scale":{"Linear":"Linéaire","Logarithmic":"Logarithmique","Ordinal":"Ordinal","Percentage":"Pourcentage"},"dvchart_show_nulls":{"Connected":"Connecté","Gaps":"Gaps","Zeros":"Zéros"},"dvchart_symbol_shape":{"Auto":"Auto","Box":"Boite","Dash":"Tiret","Diamond":"Diamant","Dot":"Point","Plus":"Plus","Triangle":"Triangle","X":"X"},"dvchart_target":{"Label":"Libellé","ToolTip":"Info-bulle"},"dvchart_template":{"PercentageCategory_p0":"Pourcentage en catégorie","PercentageDetail_p0":"Pourcentage en détail","categoryField":{"name":"Nom du champ Catégorie","value":"Valeur du champ Catégorie"},"colorField":{"name":"Nom du champ Couleur","value":"Valeur du champ Couleur"},"detailFields":{"name":"Nom du champ Détails","value":"Valeur du champ Détails"},"shapeField":{"name":"Nom du champ Forme","value":"Valeur du champ Forme"},"sizeField":{"name":"Nom du champ Taille","value":"Valeur du champ Taille"},"valueField":{"name":"Nom du champ Valeur","value":"Valeur du champ Valeur"}},"dvchart_text_position":{"Auto":"Auto","Center":"Centre","Inside":"Intérieur","Outside":"Extérieur"},"dvchart_tick_mark":{"Cross":"Croix","Inside":"Intérieur","None":"Aucun","Outside":"Extérieur"},"dvchart_valign":{"Bottom":"Bas","Middle":"Milieu","Top":"Haut"},"end_cap":{"Arrow":"Flèche","None":"Aucun"},"font_size":{"10pt":"10pt","11pt":"11pt","12pt":"12pt","14pt":"14pt","16pt":"16pt","18pt":"18pt","20pt":"20pt","22pt":"22pt","24pt":"24pt","26pt":"26pt","28pt":"28pt","36pt":"36pt","48pt":"48pt","72pt":"72pt","8pt":"8pt","9pt":"9pt"},"font_style":{"Italic":"Italique","Normal":"Normal"},"font_weight":{"Bold":"Gras","Bolder":"PlusGras","ExtraBold":"ExtraGras","ExtraLight":"Extrafin","Heavy":"TrèsGras","Light":"Fin","Lighter":"PlusFin","Medium":"Medium","Normal":"Normal","SemiBold":"SemiGras","Thin":"TrèsFin"},"gradient_type":{"Center":"Centre","DiagonalLeft":"DiagonalaGauche","DiagonalRight":"DiagonaleDroite","HorizontalCenter":"HorizontalCentre","LeftRight":"GaucheDroite","None":"Aucun","TopBottom":"HautBas","VerticalCenter":"VerticalCentre"},"grid_mode":{"Dots":"Points","Lines":"Lignes"},"grow_direction":{"Column":"Colonne","ColumnReverse":"ColonneInversée","Row":"Ligne","RowReverse":"LigneInversée"},"horizontal_align":{"Center":"Centre","Left":"Gauche","Right":"Droite"},"image_repeat":{"NoRepeat":"AucuneRépétition","Repeat":"Répéter","RepeatX":"RépéterX","RepeatY":"RépéterY"},"image_sizing":{"AutoSize":"TailleAuto","Clip":"Couper","Fit":"Ajuster","FitProportional":"AjusterProportionnellement"},"image_source":{"Database":"BaseDeDonnées","Embedded":"Intégré","External":"Externe"},"input_type":{"CheckBox":"Case à cocher","Text":"Texte"},"keep_with_group":{"After":"Après","Before":"Avant","None":"Aucun"},"label_font_style":{"Bold":"Gras","Italic":"Italique","Regular":"Regulier","Strikeout":"Barré","Underline":"Souligné"},"labels_text_orientation":{"Angled":"Incliné","Auto":"Auto","Horizontal":"Horizontal","Rotated270":"TournéA270","Rotated90":"TournéA90","Stacked":"Empilé"},"language":{"Default":"Default","af-ZA":"Afrikaans - Afrique du Sud","am-ET":"Amharic - Ethiopie","ar-AE":"Arabe - Emirats Arabes Unis","ar-BH":"Arabe - Bahrein","ar-DZ":"Arabe - Algerie","ar-EG":"Arabe - Egypte","ar-IQ":"Arabe - Iraq","ar-JO":"Arabe - Jordanie","ar-KW":"Arabe - Kuweit","ar-LB":"Arabe - Liban","ar-LY":"Arabe - Libye","ar-MA":"Arabe - Maroc","ar-OM":"Arabe - Oman","ar-QA":"Arabe - Qatar","ar-SA":"Arabe - Arabie Saoudite","ar-SY":"Arabe - Syrie","ar-TN":"Arabe - Tunisie","ar-YE":"Arabe - Yemen","arn-CL":"Mapudungun - Chili","as-IN":"Assamese - Inde","az-AZ-Cyrl":"Azeri (Cyrillique) - Azerbaïdjan","az-AZ-Latn":"Azeri (Latin) - Azerbaïdjan","ba-RU":"Bashkir - Russie","be-BY":"Belarusse - Belarusse","bg-BG":"Bulgare - Bulgarie","bn-BD":"Bengali - Bangladesh","bn-IN":"Bengali - Indie","bo-CN":"Tibetan - RPC","br-FR":"Breton - France","bs-Cyrl-BA":"Bosniaque (Cyrillique) - Bosnie Herzegovine","bs-Latn-BA":"Bosniaque (Latin) - Bosnie Herzegovine","ca-ES":"Catalan - Catalogne","co-FR":"Corse - France","cs-CZ":"Tchèque - République Tchèque","cy-GB":"Gallois - Royaume Uni","da-DK":"Danois - Danemark","de-AT":"Allemand - Autriche","de-CH":"Allemand - Suisse","de-DE":"Allemand - Allemagne","de-LI":"Allemand - Liechtenstein","de-LU":"Allemand - Luxembourg","div-MV":"Dhivehi - Maldives","el-GR":"Grec - Grèce","en-AU":"English - Australie","en-BZ":"English - Belize","en-CA":"English - Canada","en-CB":"English - Caraïbes","en-GB":"English - Royaume Uni","en-IE":"English - Irlande","en-IN":"English - Indie","en-JM":"English - Jamaïque","en-MY":"English - Malaisie","en-NZ":"English - Nouvelle Zealand","en-PH":"English - Philippines","en-SG":"English - Singapour","en-TT":"English - Trinidad et Tobago","en-US":"English - USA","en-ZA":"English - Afrique du Sud","en-ZW":"English - Zimbabwe","es-AR":"Espagnol - Argentine","es-BO":"Espagnol - Bolivie","es-CL":"Espagnol - Chili","es-CO":"Espagnol - Colombie","es-CR":"Espagnol - Costa Rica","es-DO":"Espagnol - République Dominicaine","es-EC":"Espagnol - Equateur","es-ES":"Espagnol - Espagne","es-GT":"Espagnol - Guatemala","es-HN":"Espagnol - Honduras","es-MX":"Espagnol - Mexique","es-NI":"Espagnol - Nicaragua","es-PA":"Espagnol - Panama","es-PE":"Espagnol - Perou","es-PR":"Espagnol - Puerto Rico","es-PY":"Espagnol - Paraguay","es-SV":"Espagnol - Salvador","es-US":"Espagnol - USA","es-UY":"Espagnol - Uruguay","es-VE":"Espagnol - Venezuela","et-EE":"Estonien - Estonie","eu-ES":"Basque - Basque","fa-IR":"Perse - Iran","fi-FI":"Finois - Finlande","fil-PH":"Philippin - Philippines","fo-FO":"Faroese - Iles Feroe","fr-BE":"Français - Belgique","fr-CA":"Français - Canada","fr-CH":"Français - Suisse","fr-FR":"Français - France","fr-LU":"Français - Luxembourg","fr-MC":"Français - Monaco","fy-NL":"Frisian - Pays-Bas","ga-IE":"Irish - Irlande","gd-GB":"Gaélique écossais - Royaume Uni","gl-ES":"Galicien - Galice","gsw-FR":"Alsacien - France","gu-IN":"Gujarati - Inde","ha-Latn-NG":"Hausa (Latin) - Nigeria","he-IL":"Hébreu - Israël","hi-IN":"Hindi - Inde","hr-BA":"Croate (Latin) - Bosnie Herzegovine","hr-HR":"Croate - Croatie","hu-HU":"Hongrois - Hongrie","hy-AM":"Armenien - Armenie","id-ID":"Indonesien - Indonesie","ig-NG":"Igbo - Nigeria","ii-CN":"Yi - RPC","is-IS":"Isandais - Islande","it-CH":"Italian - Suisse","it-IT":"Italian - Italie","iu-Cans-CA":"Inuktitut (Syllabique) - Canada","iu-Latn-CA":"Inuktitut (Latin) - Canada","ja-JP":"Japonais - Japon","ka-GE":"Georgien - Georgie","kk-KZ":"Kazakh - Kazakhstan","kl-GL":"Groenlandais - Groenland","km-KH":"Khmer - Cambodge","kn-IN":"Kannada - Inde","ko-KR":"Coréen - Corée","kok-IN":"Konkani - Inde","ky-KG":"Kyrgyz - Kyrgyzstan","lb-LU":"Luxembourgeois - Luxembourg","lo-LA":"Lao - Lao P.D.R.","lt-LT":"Lituanien - Lituanie","lv-LV":"Letton - Lettonie","mi-NZ":"Maori - Nouvelle Zealand","mk-MK":"Macédoinen - Macédoine","ml-IN":"Malayalam - Inde","mn-MN":"Mongole (Cyrillique) - Mongole","mn-Mong-CN":"Mongole (Traditionel) - RPC","mn-Mong-MN":"Mongole - Mongolie","moh-CA":"Mohawk - Mohawk","mr-IN":"Marathi - Inde","ms-BN":"Malay - Brunei","ms-MY":"Malay - Malaisie","mt-MT":"Maltais - Malte","nb-NO":"Norvégien (Bokmål) - Norvège","ne-NP":"Nepalais - Nepal","nl-BE":"Hollandais - Belgique","nl-NL":"Hollandais - Pays-Bas","nn-NO":"Norvégien (Nynorsk) - Norvège","nso-ZA":"Sesotho sa Leboa - Afrique du Sud","oc-FR":"Occitan - France","or-IN":"Oriya - Inde","pa-IN":"Punjabi - Inde","pl-PL":"Polonais - Pologne","prs-AF":"Dari - Afghanistan","ps-AF":"Pashto - Afghanistan","pt-BR":"Portuguais - Brésil","pt-PT":"Portuguais - Portugal","qut-GT":"K\'iche - Guatemala","quz-BO":"Quechua - Bolivie","quz-EC":"Quechua - Equateur","quz-PE":"Quechua - Perou","rm-CH":"Romanche - Suisse","ro-RO":"Roumain - Roumanie","ru-RU":"Russe - Russie","rw-RW":"Kinyarwanda - Rwanda","sa-IN":"Sanskrit - Inde","sah-RU":"Yakut - Russie","se-FI":"Sami, Northern - Finlande","se-NO":"Sami, Northern - Norvège","se-SE":"Sami, Northern - Suède","si-LK":"Sinhala - Sri Lanka","sk-SK":"Slovaque - Slovaquie","sl-SI":"Slovène - Slovénie","sma-NO":"Sami, Southern - Norvège","sma-SE":"Sami, Southern - Suède","smj-NO":"Sami, Lule - Norvège","smj-SE":"Sami, Lule - Suède","smn-FI":"Sami, Inari - Finlande","sms-FI":"Sami, Skolt - Finlande","sq-AL":"Albanais - Albanie","sr-SP-Cyrl":"Serbe (Cyrillique) - Serbie","sr-SP-Latn":"Serbe (Latin) - Serbie","sv-FI":"Suédois - Finlande","sv-SE":"Suédois - Suède","sw-KE":"Swahili - Kenya","syr-SY":"Syriac - Syrie","ta-IN":"Tamil - Inde","te-IN":"Telugu - Inde","tg-Cyrl-TJ":"Tajik (Cyrillique) - Tajikistan","th-TH":"Thai - Thailande","tk-TM":"Turkmen - Turkmenistan","tn-ZA":"Setswana - Afrique du Sud","tr-TR":"Turc - Turquie","tt-RU":"Tatar - Russie","tzm-Latn-DZ":"Tamazight (Latin) - Algerie","ug-CN":"Uyghur - RPC","uk-UA":"Ukrainien - Ukraine","ur-PK":"Urdu - Pakistan","uz-UZ-Cyrl":"Ouzbek (Cyrillique) - Ouzbekistan","uz-UZ-Latn":"Ouzbek (Latin) - Ouzbekistan","vi-VN":"Vietnamien - Vietnam","wee-DE":"Bas Sorabe - Allemagne","wen-DE":"Haut sorabe - Allemagne","wo-SN":"Wolof - Senegal","xh-ZA":"isiXhosa - Afrique du Sud","yo-NG":"Yoruba - Nigeria","zh-CN":"Chinois - RPC","zh-HK":"Chinois - Hong Kong SAR","zh-MO":"Chinois - Macao SAR","zh-SG":"Chinois - Singapour","zh-TW":"Chinois - Taiwan","zu-ZA":"isiZulu - Afrique du Sud"},"layout_direction":{"LTR":"GvD","RTL":"DvG"},"layout_order":{"NOrder":"N-ordre","ZOrder":"Z-ordre"},"legend_layout":{"Column":"Colonne","Row":"Ligne","Table":"Tableau"},"legend_position":{"BottomCenter":"BasCentre","BottomLeft":"BasGauche","BottomRight":"BasDroite","LeftBottom":"GaucheBas","LeftCenter":"GaucheCentre","LeftTop":"GaucheHaut","RightBottom":"DroiteBas","RightCenter":"DroiteCentre","RightTop":"DroiteHaut","TopCenter":"HautCentre","TopLeft":"HautGauche","TopRight":"HautDroit"},"line_control_style":{"DashDot":"TiretPoint","DashDotDot":"TiretPointPoint","Dashed":"Tiret","Dotted":"Point","Double":"Double","None":"Aucun","Solid":"Plein"},"marker_type":{"Auto":"Auto","Circle":"Cercle","Cross":"Croix","Diamond":"Diamant","None":"Aucun","Square":"Carré","Triangle":"Triangle"},"mime_type":{"image/bmp":"image/bmp","image/gif":"image/gif","image/jpeg":"image/jpeg","image/png":"image/png","image/x-emf":"image/x-emf","image/x-wmf":"image/x-wmf"},"new_page":{"Even":"Pair","Next":"Suivant","Odd":"Impair"},"numeral_variant":{"1":"1","2":"2","3":"3","4":"4","5":"5","6":"6","7":"7"},"operator":{"Between":"Entre","BottomN":"BasN","BottomPercent":"BasPourcentage","Equal":"Égal","GreaterThan":"PlusGrandQue","GreaterThanOrEqual":"PlusGrandQueOuÉgal","In":"Dans","LessThan":"PlusPetitQue","LessThanOrEqual":"PLusPetitQueOuÉgal","Like":"Comme","NotEqual":"NonÉgal","TopN":"HautN","TopPercent":"HautPourcentage"},"order_by_condition":{"Label":"Libellé","None":"Aucun","Value":"Valeur"},"orientation":{"Horizontal":"Horizontal","Vertical":"Vertical"},"overflow":{"Auto":"Auto","Clip":"Clip","Grow":"Grow","Scroll":"Scroll"},"page_break":{"End":"Fin","None":"Aucun","Start":"Début","StartAndEnd":"DébutEtFin"},"page_break_with_between":{"Between":"Entre","End":"Fin","None":"Aucun","Start":"Début","StartAndEnd":"DébutEtFin"},"page_orientation":{"Landscape":"Paysage","Portrait":"Portrait"},"plot_type":{"Auto":"Auto","Line":"Ligne","Point":"Point"},"projection_mode":{"Orthographic":"Orthographique","Perspective":"Perspective"},"pve_datetime_range_type":{"Current":"Current","Last":"Last","LastToDate":"LastToDate","Next":"Next","ToDate":"ToDate"},"pve_datetime_range_type_second":{"Last":"Last","Next":"Next"},"pve_datetime_range_type_time":{"Current":"Current","Last":"Last","Next":"Next"},"pve_datetime_range_unit":{"Day":"Day","Hour":"Hour","Minute":"Minute","Month":"Month","Quarter":"Quarter","Second":"Second","Week":"Week","Year":"Year"},"repeat_blank_rows":{"FillGroup":"RemplirGroupe","FillPage":"RemplirPage","None":"Aucun"},"reportparameter_datatype":{"Boolean":"Booléen","Date":"Date","DateTime":"DateHeure","Float":"Flottant","Integer":"Entier","String":"ChaineCaractères"},"reportparts_property_type":{"boolean":"Boolean","borderStyle":"BorderStyle","color":"Color","float":"Float","fontFamily":"FontFamily","fontSize":"FontSize","fontStyle":"FontStyle","fontWeight":"FontWeight","integer":"Integer","length":"Length","lineStyle":"LineStyle","lineWidth":"LineWidth","string":"String","textDecoration":"TextDecoration"},"reportparts_sizemode":{"Fixed":"Fixed","Resizable":"Resizable"},"rich_text_markup":{"HTML":"HTML"},"shading":{"None":"Aucun","Real":"Réel","Simple":"Simple"},"shape_styles":{"Ellipse":"Ellipse","Rectangle":"Rectangle","RoundRect":"RectangleArrondi"},"size_type":{"Default":"Defaut","FitToPage":"Ajuster à la page","FitToWidth":"Ajuster à la largeur"},"sparkline_type":{"Area":"Zone","Columns":"Colonnes","Line":"Ligne","StackedBar":"BarreEmpilée","Whiskers":"Pertes"},"target_shape":{"Dot":"Point","Line":"Ligne","Square":"Carré"},"text_align":{"Center":"Centre","General":"Général","Justify":"Justifié","Left":"Gauche","Right":"Droite"},"text_align_glcr":{"Center":"Centre","General":"Général","Left":"Gauche","Right":"Droite"},"text_decoration":{"DoubleUnderline":"DoubleSouligné","LineThrough":"Ligneàtravers","None":"Aucun","Overline":"Surligné","Underline":"Souligné"},"text_justify":{"Auto":"Auto","Distribute":"Distribuer","DistributeAllLines":"DistribuerToutesLesLignes"},"text_orientation":{"Auto":"Auto","Horizontal":"Horizontal","Rotated270":"Rotation270","Rotated90":"Rotation90","Stacked":"Empilé"},"tick_mark":{"Cross":"Croix","Inside":"Intérieur","None":"Aucun","Outside":"Extérieur"},"title_position":{"Center":"Center","Far":"Lointain","Near":"Proche"},"unicode_bidi":{"BidiOverride":"OutrepasserBidi","Embed":"Intégrer","Normal":"Normal"},"upright_in_vertical_text":{"Digits":"Chiffres","DigitsAndLatinLetters":"ChiffreEtLettres","None":"Aucun"},"vertical_align":{"Bottom":"Bas","Middle":"Milieu","Top":"Haut"},"wrap_mode":{"CharWrap":"Coupure entre caractères","NoWrap":"Pas de coupure","WordWrap":"Coupure entre mots"},"writing_mode":{"lr-tb":"gd-hb","tb-rl":"hb-dg"}}},{"lng":"fr","ns":"error","resources":{"errorLabel":"Erreur","expressionNode":{"argumentIsNotValid":"L\'argument \'{{argument}}\' de la fonction \'{{functionName}}\' n\'est pas valide. {{details}}","argumentValueNotFitFunctionValueDataType":"L\'argument \'{{argument}}\' de la fonction \'{{functionName}}\' doit avoir l\'un des types suivants - {{expectedTypes}} - mais a le type {{actualType}}.","argumentsShouldHaveSameDataType":"L\'arguments {{arguments}} de la fonction \'{{functionName}}\' doit avoir le même type de données.","typeMismatch":"La valeur \'{{value}}\' ne peut pas être converti en {{expectedType}}."},"themes":{"textCannotLoad":"Impossible de charger le thème du rapport","textNoThemes":"Veuillez noter qu\'il n\'y a pas d\'autres thèmes disponibles, le rapport n\'a donc pas de thème spécifié.","textNotFound":"Le thème \\"{{theme}}\\" utilisé dans ce rapport est introuvable.","textTotFoundOrInvalid":"Le thème {{theme}} utilisé dans ce rapport n\'est pas trouvé ou a un contenu non valide.","textUseDefault":"Le thème par défaut \\"{{theme}}\\" sera utilisé à la place."},"upgradeReportSemanticModel":{"entityIsAbsent":"● L\'entité \'{{entityName}}\' du Dataset \'{{dataSetName}}\' est absent du dernier modèle sémantique.","fieldAtQueryFilterIsAbsent":"● Le champ \'{{fieldName}}\' de la requête du filtre du Dataset \'{{dataSetName}}\' est absent du dernier modèle sémantique.","fieldInControlGroupIsAbsent":"● Le champ \'{{fieldName}}\' de la propriété \'{{propertyLabel}}\' du contrôle {{controlName}} du groupe {{groupName}} est absent du dernier modèle sémantique.","fieldInControlIsAbsent":"● Le champ \'{{fieldName}}\' de la propriété \'{{propertyLabel}}\' du contrôle {{controlName}} est absent du dernier modèle sémantique.","fieldInReportIsAbsent":"● Le champ \'{{fieldName}}\' de la propriété \'{{propertyLabel}}\' dans le rapport est absent du dernier modèle sémantique."}}},{"lng":"fr","ns":"expressionFields","resources":{"chart":{"Current":{"Category":{"description":"Représente la valeur de l\'élément cible du champ Catégorie.","example":"=Chart!CurrentCategory=1; =Chart!CurrentCategory>10; =Chart!CurrentCategory=\\"Value1\\"; ","label":"Category","syntax":"Chart!CurrentCategory <Opérateur de comparaison> <Valeur>"},"Data":{"description":"Représente la valeur de l\'élément cible du champ Donnée.","example":"=Chart!CurrentData=1; =Chart!CurrentData>10; =Chart!CurrentData=\\"Value1\\"; ","label":"Data","syntax":"Chart!CurrentData <Opérateur de comparaison> <Valeur>"},"DataClose":{"description":"Représente la valeur \'Fermante\' de l\'élément cible dans les graphiques financiers.","example":"=Chart!CurrentDataClose=1; =Chart!CurrentDataClose>10; =Chart!CurrentDataClose=\\"Value1\\"; ","label":"DataClose","syntax":"Chart!CurrentDataClose <Opérateur de comparaison> <Valeur>"},"DataHigh":{"description":"Représente la valeur \'Elevée\' de l\'élément cible dans les graphiques financiers.","example":"=Chart!CurrentDataHigh=1; =Chart!CurrentDataHigh>10; =Chart!CurrentDataHigh=\\"Value1\\"; ","label":"DataHigh","syntax":"Chart!CurrentDataHigh <Opérateur de comparaison> <Valeur>"},"DataLow":{"description":"Représente la valeur \'Basse\' de l\'élément cible dans les graphiques financiers.","example":"=Chart!CurrentDataLow=1; =Chart!CurrentDataLow>10; =Chart!CurrentDataLow=\\"Value1\\"; ","label":"DataLow","syntax":"Chart!CurrentDataLow <Opérateur de comparaison> <Valeur>"},"DataLower":{"description":"Représente la valeur \'Inférieure\' de l\'élément cible dans les diagrammes de Gantt et les diagrammes en boîte.","example":"=Chart!CurrentDataLower=1; =Chart!CurrentDataLower>10; =Chart!CurrentDataLower=\\"Value1\\"; ","label":"DataLower","syntax":"Chart!CurrentDataLower <Opérateur de comparaison> <Valeur>"},"DataOpen":{"description":"Représente la valeur \'Ouvrante\' de l\'élément cible dans les graphiques financiers.","example":"=Chart!CurrentDataOpen=1; =Chart!CurrentDataOpen>10; =Chart!CurrentDataOpen=\\"Value1\\"; ","label":"DataOpen","syntax":"Chart!CurrentDataOpen <Opérateur de comparaison> <Valeur>"},"DataUpper":{"description":"Représente la valeur \'Supérieure\' de l\'élément cible dans les diagrammes de Gantt et les diagrammes en boîte.","example":"=Chart!CurrentDataUpper=1; =Chart!CurrentDataUpper>10; =Chart!CurrentDataUpper=\\"Value1\\"; ","label":"DataUpper","syntax":"Chart!CurrentDataUpper <Opérateur de comparaison> <Valeur>"},"Detail":{"description":"Représente la valeur de l\'élément cible Encodage du champ Détail.","example":"=Chart!CurrentDetail=1; =Chart!CurrentDetail>10; =Chart!CurrentDetail=\\"Value1\\"; ","label":"Detail","syntax":"Chart!CurrentDetail <Opérateur de comparaison> <Valeur>"},"label":"Elément Courant"},"Functions":{"CurrentValue":{"description":"Représente la valeur courante d\'un marqueur sur une représentation graphique spécifique.","example":"=CurrentValue(\\"plot1\\"); =CurrentValue(\\"plot1\\",\\"high\\"); ","label":"CurrentValue","syntax":"CurrentValue(\\"nom du plot\\" , \\"identifiant optionnel de valeur (élevée, basse, ouvrante, fermante, supérieure, inférieure)\\")"},"NextValue":{"description":"Représente la valeur du marqueur suivant la valeur courante d\'une représentation graphique spécifique.","example":"=NextValue(\\"plot1\\"); =NextValue(\\"plot1\\",\\"high\\"); ","label":"NextValue","syntax":"NextValue(\\"nom du plot\\" , \\"identifiant optionnel de valeur (élevée, basse, ouvrante, fermante, supérieure, inférieure)\\")"},"PreviousValue":{"description":"Représente la valeur du marqueur précédant la valeur courante d\'une représentation graphique spécifique.","example":"=PreviousValue(\\"plot1\\"); =PreviousValue(\\"plot1\\",\\"high\\"); ","label":"PreviousValue","syntax":"PreviousValue(\\"nom du plot\\" , \\"identifiant optionnel de valeur (élevée, basse, ouvrante, fermante, supérieure, inférieure)\\")"},"label":"Fonctions"},"Next":{"Category":{"description":"Représente la valeur du champ Catégorie suivant l\'élément cible.","example":"=Chart!NextCategory=1; =Chart!NextCategory>10; =Chart!NextCategory=\\"Value1\\"; ","label":"Category","syntax":"Chart!NextCategory <Opérateur de comparaison> <Valeur>"},"Data":{"description":"Représente la valeur du champ Donnée suivant l\'élément cible.","example":"=Chart!NextData=1; =Chart!NextData>10; =Chart!NextData=\\"Value1\\"; ","label":"Data","syntax":"Chart!NextData <Opérateur de comparaison> <Valeur>"},"DataClose":{"description":"Représente la valeur \'Fermante\' suivant l\'élément cible dans les graphiques financiers.","example":"=Chart!NextDataClose=1; =Chart!NextDataClose>10; =Chart!NextDataClose=\\"Value1\\"; ","label":"DataClose","syntax":"Chart!NextDataClose  <Opérateur de comparaison> <Valeur>"},"DataHigh":{"description":"Représente la valeur \'Elevée\' suivant l\'élément cible dans les graphiques financiers.","example":"=Chart!NextDataHigh=1; =Chart!NextDataHigh>10; =Chart!NextDataHigh=\\"Value1\\"; ","label":"DataHigh","syntax":"Chart!NextDataHigh <Opérateur de comparaison> <Valeur>"},"DataLow":{"description":"Représente la valeur \'Basse\' suivant l\'élément cible dans les graphiques financiers.","example":"=Chart!NextDataLow=1; =Chart!NextDataLow>10; =Chart!NextDataLow=\\"Value1\\"; ","label":"DataLow","syntax":"Chart!NextDataLow <Opérateur de comparaison> <Valeur>"},"DataLower":{"description":"Représente la valeur \'Inférieure\' suivant l\'élément cible dans les diagrammes de Gantt et les diagrammes en boîte.","example":"=Chart!NextDataLower=1; =Chart!NextDataLower>10; =Chart!NextDataLower=\\"Value1\\"; ","label":"DataLower","syntax":"Chart!NextDataLower <Opérateur de comparaison> <Valeur>"},"DataOpen":{"description":"Représente la valeur \'Ouvrante\' suivant l\'élément cible dans les graphiques financiers.","example":"=Chart!NextDataOpen=1; =Chart!NextDataOpen>10; =Chart!NextDataOpen=\\"Value1\\"; ","label":"DataOpen","syntax":"Chart!NextDataOpen <Opérateur de comparaison> <Valeur>"},"DataUpper":{"description":"Représente la valeur \'Supérieure\' suivant l\'élément cible dans les diagrammes de Gantt et les diagrammes en boîte.","example":"=Chart!NextDataUpper=1; =Chart!NextDataUpper>10; =Chart!NextDataUpper=\\"Value1\\"; ","label":"DataUpper","syntax":"Chart!NextDataUpper <Opérateur de comparaison> <Valeur>"},"Detail":{"description":"Représente la valeur de l\'Encodage du champ Détail suivant l\'élément cible.","example":"=Chart!NextDetail=1; =Chart!NextDetail>10; =Chart!NextDetail=\\"Value1\\"; ","label":"Detail","syntax":"Chart!NextDetail <Opérateur de comparaison> <Valeur>"},"label":"Element Suivant"},"Previous":{"Category":{"description":"Représente la valeur du champ Cétégorie précédant l\'élément cible.","example":"=Chart!PreviousCategory=1; =Chart!PreviousCategory>10; =Chart!PreviousCategory=\\"Value1\\"; ","label":"Category","syntax":"Chart!PreviousCategory <Opérateur de comparaison> <Valeur>"},"Data":{"description":"Représente la valeur du champ Donnée précédant l\'élément cible.","example":"=Chart!PreviousData=1; =Chart!PreviousData>10; =Chart!PreviousData=\\"Value1\\"; ","label":"Data","syntax":"Chart!PreviousData <Opérateur de comparaison> <Valeur>"},"DataClose":{"description":"Représente la valeur \'Fermante\' précédant l\'élément cible dans les graphiques financiers.","example":"=Chart!PreviousDataClose=1; =Chart!PreviousDataClose>10; =Chart!PreviousDataClose=\\"Value1\\"; ","label":"DataClose","syntax":"Chart!PreviousDataClose <Opérateur de comparaison> <Valeur>"},"DataHigh":{"description":"Représente la valeur \'Elevée\' précédant l\'élément cible dans les graphiques financiers.","example":"=Chart!PreviousDataHigh=1; =Chart!PreviousDataHigh>10; =Chart!PreviousDataHigh=\\"Value1\\"; ","label":"DataHigh","syntax":"Chart!PreviousDataHigh <Opérateur de comparaison> <Valeur>"},"DataLow":{"description":"Représente la valeur \'Basse\' précédant l\'élément cible dans les graphiques financiers.","example":"=Chart!PreviousDataLow=1; =Chart!PreviousDataLow>10; =Chart!PreviousDataLow=\\"Value1\\"; ","label":"DataLow","syntax":"Chart!PreviousDataLow <Opérateur de comparaison> <Valeur>"},"DataLower":{"description":"Représente la valeur \'Inférieure\' précédant l\'élément cible dans les diagrammes de Gantt et les diagrammes en boîte.","example":"=Chart!PreviousDataLower=1; =Chart!PreviousDataLower>10; =Chart!PreviousDataLower=\\"Value1\\"; ","label":"DataLower","syntax":"Chart!PreviousDataLower <Opérateur de comparaison> <Valeur>"},"DataOpen":{"description":"Représente la valeur \'Ouvrante\' précédant l\'élément cible dans les graphiques financiers.","example":"=Chart!PreviousDataOpen=1; =Chart!PreviousDataOpen>10; =Chart!PreviousDataOpen=\\"Value1\\"; ","label":"DataOpen","syntax":"Chart!PreviousDataOpen <Opérateur de comparaison> <Valeur>"},"DataUpper":{"description":"Représente la valeur \'Supérieure\' precédant l\'élément cible dans les diagrammes de Gantt et les diagrammes en boîte.","example":"=Chart!PreviousDataUpper=1; =Chart!PreviousDataUpper>10; =Chart!PreviousDataUpper=\\"Value1\\"; ","label":"DataUpper","syntax":"Chart!PreviousDataUpper <Opérateur de comparaison> <Valeur>"},"Detail":{"description":"Représente la valeur de l\'Encodage du champ Détail précédant l\'élément cible.","example":"=Chart!PreviousDetail=1; =Chart!PreviousDetail>10; =Chart!PreviousDetail=\\"Value1\\"; ","label":"Detail","syntax":"Chart!PreviousDetail <Opérateur de comparaison> <Valeur>"},"label":"Elément Précédent"},"label":"Graphique"},"commonValues":{"info":{"currentDateTime":{"description":"Affiche la date et l\'heure du jour. Peut être urilisé dans l\'En-tête ou le Pied de Page."},"pageNM":{"description":"Affiche le numéro de la page courant (N) et le nombre total de page (M) au format \'N sur M\'. Peut être utilisé dans l\'En-tête ou le Pied de Page."},"pageNMCumulative":{"description":"Affiche à la fois la page courante et le nombre total de pages cumulatives dans le rapport. Page N sur M (Cumulées) est appliqué à la numérotation des page à l\'utilisation de collation dans le rapport."},"pageNMSection":{"description":"Affiche le numéro de la page courante (N) et le nombre total de pages (M) au format \'N sur M,\' de la section à laquelle la fonction appartient. Cette section peut être un rapport ou une data region."},"pageNumber":{"description":"Affiche le numéro de la page courante. Peut être utilisé dans l\'En-tête ou le Pied de Page."},"pageNumberCumulative":{"description":"Affiche le numéro de la page cumulative courante. Numéro de Page (Cumulative) est appliquée à la numérotation de la page à l\'utilisation de collation dans le rapport."},"pageNumberSection":{"description":"Affiche le numéro de la page courante de la section à laquelle la fonction appartient. La section peut être un rapport ou une data region."},"reportName":{"description":"Affiche le nom du rapport."},"textboxValue":{"description":"This variable will be replaced with the current textbox(es) value(s) when saved.","example":"=Sum($$)","example_i11n":"{Sum($$$)}","label":"Current Textbox Value"},"totalPages":{"description":"Affiche le nombre total de pages. Peut être utilisé dans l\'En-tête et le Pied de Page."},"totalPagesCumulative":{"description":"Affiche le nombre total de pages cumulatives dans le rapport. Total des Pages (Cumulatives) est appliqué à la numérotation des pages à l\'utilisation de collation dans le rapport."},"totalPagesSection":{"description":"Affiche le nombre total de pages de la section à laquelle la fonction appartient. La section peut être un rapport ou une data region."},"userContext":{"description":"A utiliser uniquement avec une fonction, e.g. ContexteUtilisateur.GetValue(\\"nom\\"), ContexteUtilisateur.NumberToWords(123)."},"userId":{"description":"Affiche l\'ID Utilisateur de l\'utilisateur visualisant le rapport."},"userLanguage":{"description":"Affiche la langue de l\'utilisateur visualisant le rapport en fonction des paramètres système."}},"titles":{"label":"Valeurs Communes"}},"constants":{"dvchart_template":{"PercentageCategory_p0":{"description":"Affiche le pourcentage de la Valeur du champ dans la Catégorie."},"PercentageDetail_p0":{"description":"Affiche le pourcentage de la Valeur du champ dans les Détails."},"categoryField":{"name":{"description":"Affiche le nom du champ Catégorie."},"value":{"description":"Affiche la valeur du champ Catégorie."}},"colorField":{"name":{"description":"Affiche le nom du champ Couleur."},"value":{"description":"Affiche la valeur du champ Couleur."}},"detailFields":{"name":{"description":"Affiche le nom du champ Détails."},"value":{"description":"Affiche la valeur du champ Détails."}},"shapeField":{"name":{"description":"Affiche le nom du champ Forme."},"value":{"description":"Affiche la valeur du champ Forme."}},"sizeField":{"name":{"description":"Affiche la valeur du champ Taille."},"value":{"description":"Affiche la valeur du champ Taille."}},"valueField":{"name":{"description":"Affiche la valeur du champ Valeur."},"value":{"description":"Affiche la valeur du champ Valeur."}}},"titles":{"label":"Constantes"}},"dataSets":{"titles":{"label":"Data Sets"}},"documentMap":{"info":{"path":{"description":"Retourne le chemin du niveau de TOC.","example":"=DocumentMap.Path & \\"This is Heading 1\\"","label":"Chemin"}},"titles":{"label":"Structure du Document"}},"functions":{"info":{"aggregate":{"aggregateIf":{"description":"Calcule l\'agrégat des valeurs d\'une expression donnée si l\'expression Booléenne respecte la condition donnée.","example":"=AggregateIf(Fields!Discontinued.Value = True, \\"Sum\\", Fields!InStock.Value)","label":"AggregateIf","syntax":"AggregateIf(<Condition>, <FonctionAgregat>, <EnsembleDesArguments>)"},"aggregateIfWithScope":{"description":"Calcule la somme des valeurs d\'une expression donnée si l\'expression Booléenne respecte la condition donnée, dans une portée donnée.","example":"=AggregateIf(Fields!Discontinued.Value = True, \\"Sum\\", Fields!InStock.Value, \\"Category\\")","label":"AggregateIf (with scope)","syntax":"AggregateIf(<Condition>, <FonctionAgregat>, <EnsembleDesArguments>, <Portée>)"},"avg":{"description":"Calcule la moyenne de toutes les valeurs numériques non-nulles d\'une expression donnée.","example":"=Avg(Fields!LifeExpentancy.Value)","label":"Avg","syntax":"Avg(<Valeurs>)"},"avgWithScope":{"description":"Calcule la moyenne de toutes les valeurs numériques non-nulles d\'une expression donnée dans une portée donnée.","example":"=Avg(Fields!LifeExpentancy.Value, \\"GroupByCountry\\")","label":"Avg (with scope)","syntax":"Avg(<Valeurs>, <Portée>)"},"count":{"description":"Calcule le nombre de valeurs non-nulles d\'une expression donnée.","example":"=Count(Fields!EmployeeID.Value)","label":"Count","syntax":"Count(<Valeurs>)"},"countDistinct":{"description":"Calcule le nombre de valeurs uniques d\'une expression donnée.","example":"=CountDistinct(Fields!OrderID.Value)","label":"CountDistinct","syntax":"CountDistinct(<Valeurs>)"},"countDistinctWithScope":{"description":"Calcule le nombre de valeurs uniques d\'une expression donnée dans un portée donnée.","example":"=CountDistinct(Fields!OrderID.Value, \\"GroupByCategory\\")","label":"CountDistinct (with scope)","syntax":"CountDistinct(<Valeurs>, <Portée>)"},"countRows":{"description":"Calcule le nombre de lignes .","example":"=CountRows()","label":"CountRows","syntax":"CountRows()"},"countRowsWithScope":{"description":"Calcule le nombre de lignes dans une portée donnée.","example":"=CountRows(\\"Title\\")","label":"CountRows (with scope)","syntax":"CountRows(<Portée>)"},"countWithScope":{"description":"Calcule le nombre de valeurs non-nulles d\'une expression donnée dans une portée donnée.","example":"=Count(Fields!EmployeeID.Value, \\"Title\\")","label":"Count (with scope)","syntax":"Count(<Valeurs>, <Portée>)"},"crossAggregate":{"description":"Calcule la fonction donnée ayant pour argument une expression donnée dans l\'alignement d\'une ligne et d\'une colonne données.","example":"=CrossAggregate(Fields!Amount.Value, \\"Sum\\", \\"YearGroup\\", \\"CategoryGroup\\")","label":"CrossAggregate","syntax":"CrossAggregate(<Expression>, <FunctionName>, <ColumnGroupName>, <RowGroupName>)"},"cumulativeTotal":{"description":"Calcule la somme des cumules des niveaux de page retournés par l\'expression pour la page courante et les précédentes.","example":"=CumulativeTotal(Fields!OrderID.Value, \\"Count\\")","label":"CumulativeTotal","syntax":"CumulativeTotal(<Expression>, <Aggregate>)"},"distinctSum":{"description":"Calcule la somme des valeurs de l\'expression donnée quand la valeur de l\'autre expression n\'est pas en doublon.","example":"=DistinctSum(Fields!OrderID.Value, Fields!OrderFreight.Value)","label":"DistinctSum","syntax":"DistinctSum(<Valeurs>, <Valeur>)"},"distinctSumWithScope":{"description":"Calcule la somme des valeurs de l\'expression donnée quand la valeur de l\'autre expression n\'est pas en doublon, dans une portée donnée.","example":"=DistinctSum(Fields!OrderID.Value, Fields!OrderFreight.Value, \\"Order\\")","label":"DistinctSum (with scope)","syntax":"DistinctSum(<Valeurs>, <Valeur>, <Portée>)"},"first":{"description":"Retourne la première valeur d\'une expression donnée.","example":"=First(Fields!ProductNumber.Value)","label":"First","syntax":"First(<Valeurs>)"},"firstWithScope":{"description":"Retourne la première valeur d\'une expression donnée dans une portée donnée.","example":"=First(Fields!ProductNumber.Value, \\"Category\\")","label":"First (with scope)","syntax":"First(<Valeurs>, <Portée>)"},"last":{"description":"Retourne la dernière valeur de l\'expression spécifiée.","example":"=Last(Fields!ProductNumber.Value)","label":"Last","syntax":"Last(<Valeurs>)"},"lastWithScope":{"description":"Retourne la dernière valeur de l\'expression donnée dans la portée donnée.","example":"=Last(Fields!ProductNumber.Value, \\"Category\\")","label":"Last (with scope)","syntax":"Last(<Valeurs>, <Portée>)"},"max":{"description":"Retourne la valeur non-nulle maximum de l\'expression spécifiée.","example":"=Max(Fields!OrderTotal.Value)","label":"Max","syntax":"Max(<Valeurs>)"},"maxWithScope":{"description":"Retourne la valeur non-nulle maximum de l\'expression spécifiée dans la portée donnée.","example":"=Max(Fields!OrderTotal.Value, \\"Year\\")","label":"Max (with scope)","syntax":"Max(<Valeurs>, <Portée>)"},"median":{"description":"Retourne la valeur qui est le point médian des valeurs dans l\'expression donnée. La Médiane est la valeur centrale dans une séquence de valeurs.","example":"=Median(Fields!OrderTotal.Value)","label":"Median","syntax":"Median(<Valeurs>)"},"medianWithScope":{"description":"Retourne la valeur qui est le point médian des valeurs dans l\'expression donnée, dans la portée spécifiée. La Médiane est la valeur centrale dans une séquence de valeurs.","example":"=Median(Fields!OrderTotal.Value, \\"Year\\")","label":"Median (with scope)","syntax":"Median(<Valeurs>, <Portée>)"},"min":{"description":"Retourne la valeur non-nulle minimale de l\'expression donnée. ","example":"=Min(Fields!OrderTotal.Value)","label":"Min","syntax":"Min(<Valeurs>)"},"minWithScope":{"description":"Retourne la valeur non-nulle minimale de l\'expression donnée dans une portée donnée.","example":"=Min(Fields!OrderTotal.Value, \\"Year\\")","label":"Min (with scope)","syntax":"Min(<Valeurs>, <Portée>)"},"mode":{"description":"Retourne la valeur apparaissant le plus souvent dans l\'expression donnée.","example":"=Mode(Fields!OrderTotal.Value)","label":"Mode","syntax":"Mode(<Valeurs>)"},"modeWithScope":{"description":"Retourne la valeur apparaissant le plus souvent dans l\'expression donnée, dans une portée donnée.","example":"=Mode(Fields!OrderTotal.Value, \\"Year\\")","label":"Mode (with scope)","syntax":"Mode(<Valeurs>, <Portée>)"},"runningValue":{"description":"Calcule un agrégat courant de toutes les valeurs numériques non-nulles de l\'expression donnée, en utilisant une autre fonction d\'agrégat en paramètre.","example":"=RunningValue(Fields!Price.Value, \\"Sum\\")","label":"RunningValue","syntax":"RunningValue(<Valeurs>, <AggregateFunction>)"},"runningValueWithScope":{"description":"Calcule un agrégat courant de toutes les valeurs numériques non-nulles de l\'expression donnée, en utilisant une autre fonction d\'agrégat en paramètre, dans une portée donnée.","example":"=RunningValue(Fields!Price.Value, \\"Sum\\", \\"Nwind\\")","label":"RunningValue (with scope)","syntax":"RunningValue(<Valeurs>, <AggregateFunction>, <Portée>)"},"stDev":{"description":"Calcule l\'écart type de toutes les valeurs non-nulles de l\'expression donnée.","example":"=StDev(Fields!LineTotal.Value)","label":"StDev","syntax":"StDev(<Valeurs>)"},"stDevP":{"description":"Calcule la population de l\'écart type de toutes les valeurs non-nulles de l\'expression donnée.","example":"=StDevP(Fields!LineTotal.Value)","label":"StDevP","syntax":"StDevP(<Valeurs>)"},"stDevPWithScope":{"description":"Calcule la population de l\'écart type de toutes les valeurs non-nulles de l\'expression donnée dans une portée donnée.","example":"=StDevP(Fields!LineTotal.Value, \\"Order\\")","label":"StDevP (with scope)","syntax":"StDevP(<Valeurs>, <Portée>)"},"stDevWithScope":{"description":"Calcule l\'écart type de toutes les valeurs non-nulles de l\'expression donnée, dans une portée donnée.","example":"=StDev(Fields!LineTotal.Value, \\"Nwind\\")","label":"StDev (with scope)","syntax":"StDev(<Valeurs>, <Portée>)"},"sum":{"description":"Calcule la somme des valeurs de l\'expression donnée.","example":"=Sum(Fields!Price.Value)","label":"Sum","syntax":"Sum(<Valeurs>)"},"sumWithScope":{"description":"Calcule la somme des valeurs de l\'expression donnée dans une portée donnée.","example":"=Sum(Fields!Price.Value, \\"Category\\")","label":"Sum (with scope)","syntax":"Sum(<Valeurs>, <Portée>)"},"var":{"description":"Calcule la variance (écart type au carré) de toutes les valeurs non-nulles de l\'expression donnée.","example":"=Var(Fields!LineTotal.Value)","label":"Var","syntax":"Var(<Valeurs>)"},"varP":{"description":"Calcule la variance de la population (la population de l\'écart type au carré) de toutes les valeurs non-nulles de l\'expression donnée.","example":"=VarP(Fields!LineTotal.Value)","label":"VarP","syntax":"VarP(<Valeurs>)"},"varPWithScope":{"description":"Calcule la population de la variance (population de l\'écart type au carré) de toutes les valeurs non-nulles de l\'expression donnée, dans une portée donnée.","example":"=VarP(Fields!LineTotal.Value, \\"Order\\")","label":"VarP (with scope)","syntax":"VarP(<Valeurs>, <Portée>)"},"varWithScope":{"description":"Calcule la variance (écart type au carré) de toutes les valeurs non-nulles de l\'expression donnée.","example":"=Var(Fields!LineTotal.Value, \\"Order\\")","label":"Var (with scope)","syntax":"Var(<Valeurs>, <Portée>)"}},"conversion":{"format":{"description":"Formate la valeur au format spécifié.","example":"=Format(Fields!OrderDate.Value, \\"dd MMM yyyy\\")","label":"Format","syntax":"Format(<Valeur>, <Chaîne>)"},"toBoolean":{"description":"Convertit la valeur spécifiée en Boolean.","example":"=ToBoolean(Fields!HouseOwnerFlag.Value)","label":"ToBoolean","syntax":"ToBoolean(<Valeur>)"},"toByte":{"description":"Convertit la valeur spécifiée en Byte.","example":"=ToByte(Fields!ProductNumber.Value)","label":"ToByte","syntax":"ToByte(<Valeur>)"},"toChar":{"description":"Convertit la valeur spécifiée en Char.","example":"=ToChar(Fields!OrderStatus.Value); =ToChar(“Hello”)","label":"ToChar","syntax":"ToChar(<Valeur>)"},"toDateTime":{"description":"Convertit la valeur spécifiée en valeur Date et Time.","example":"=ToDateTime(Fields!SaleDate.Value); =ToDateTime(\\"1 January, 2017\\")","label":"ToDateTime","syntax":"ToDateTime(<Valeur>)"},"toDecimal":{"description":"Convertir la valeur spécifiée en Decimal.","example":"=ToDecimal(Fields!Sales.Value)","label":"ToDecimal","syntax":"ToDecimal(<Valeur>)"},"toDouble":{"description":"Convertit la valeur spécifiée en Double.","example":"=ToDouble(Fields!AnnualSales.Value); =ToDouble(535.85 * .2691 * 67483)","label":"ToDouble","syntax":"ToDouble(<Valeur>)"},"toInt16":{"description":"Convertit la valeur spécifiée en Integer sur 16 bit signé.","example":"=ToInt16(Fields!AnnualSales.Value); =ToInt16(535.85)","label":"ToInt16","syntax":"ToInt16(<Valeur>)"},"toInt32":{"description":"Convertit la valeur spécifiée en Integer sur 32 bit signé.","example":"=ToInt32(Fields!AnnualSales.Value)","label":"ToInt32","syntax":"ToInt32(<Valeur>)"},"toInt64":{"description":"Convertit la valeur spécifiée en Integer sur 64 bit signé.","example":"=ToInt64(Fields!AnnualSales.Value)","label":"ToInt64","syntax":"ToInt64(<Valeur>)"},"toSingle":{"description":"Convertit la valeur spécifiée en un nombre à une seule décimale.","example":"=ToSingle(Fields!AnnualSales.Value); =ToSingle(15.*********)","label":"ToSingle","syntax":"ToSingle(<Valeur>)"},"toStringDot":{"description":"Convertit la valeur en String dans le format spécifié.","example":"=Fields!OrderDate.Value.ToString(\\"dd MMM yyyy\\")","label":".ToString","syntax":"<Valeur>.ToString(<Chaîne>)"},"toStringKey":{"description":"Convertit la valeur spécifiée en String.","example":"=ToString(Fields!YearlyIncome.Value); =ToString(13.50)","label":"ToString","syntax":"ToString(<Valeur>)"},"toUInt16":{"description":"Convertit la valeur spécifiée en Integer sur 16 bit non signé.","example":"=ToUInt16(Fields!AnnualSales.Value)","label":"ToUInt16","syntax":"ToUInt16(<Valeur>)"},"toUInt32":{"description":"Convertit la valeur spécifiée en Integer sur 32 bit non signé.","example":"=ToUInt32(Fields!AnnualSales.Value)","label":"ToUInt32","syntax":"ToUInt32(<Valeur>)"},"toUInt64":{"description":"Convertit la valeur spécifiée en Integer sur 64 bit non signé.","example":"=ToUInt64(Fields!AnnualSales.Value)","label":"ToUInt64","syntax":"ToUInt64(<Valeur>)"}},"customFunctions":{"getValue":{"description":"Affiche la valeur du contexte utilisateur pour une propriété donnée, e.g. \\"nom\\",\\"email\\".","example":"=UserContext.getValue(\\"name\\")","label":"GetUserValue","syntax":"UserContext.getValue(<Chaîne>)"},"numberToWords":{"description":"Convertit la valeur spécifiée en mots. Dans une fonction à un seul argument, la langue utilisée est celle du portail. Dans une fonction à deux arguments, la langue utilisée est celle passée en second argument (Cultures compatibles: \\"zh-cn\\", \\"en-us\\", \\"ja-jp\\", \\"fr-fr\\").","example":"=UserContext.NumberToWords(123.5); =UserContext.NumberToWords(981, \\"zh-CN\\")","label":"NumberToWords","syntax":"UserContext.NumberToWords(<Nombre>, <Chaîne>)"}},"dateTime":{"addDays":{"description":"Retourne le résultat de l\'addition d\'un intervalle entre deux dates en jours sous forme d\'une valeur en date et heure. l\'intervalle spécifié peut être négatif.","example":"=Fields!OrderDate.Value.AddDays(5)","label":"AddDays","syntax":"<DateHeure>.AddDays(<Nombre>)"},"addHours":{"description":"Retourne le résultat de l\'addition d\'un intervalle entre deux dates en heures sous forme d\'une valeur en date et heure. L\'intervalle spécifié peut être négatif.","example":"=Fields!OrderDate.Value.AddHours(12)","label":"AddHours","syntax":"<DateHeure>.AddHours(<Nombre>)"},"addMilliseconds":{"description":"Retourne le résultat de l\'addition d\'un intervalle entre deux dates en millisecondes sous forme d\'une valeur en date et heure. L\'intervalle spécifié peut être négatif.","example":"=Fields!OrderDate.Value.AddMilliseconds(500)","label":"AddMilliseconds","syntax":"<DateHeure>.AddMilliseconds(<Nombre>)"},"addMinutes":{"description":"Retourne le résultat de l\'addition d\'un intervalle entre deux dates en minutes sous forme d\'une valeur en date et heure. L\'intervalle spécifié peut être négatif.","example":"=Fields!OrderDate.Value.AddMinutes(30)","label":"AddMinutes","syntax":"<DateHeure>.AddMinutes(<Nombre>)"},"addMonths":{"description":"Retourne le résultat de l\'addition d\'un intervalle entre deux dates en mois sous forme d\'une valeur en date et heure. L\'intervalle spécifié peut être négatif.","example":"=Fields!OrderDate.Value.AddMonths(2)","label":"AddMonths","syntax":"<DateHeure>.AddMonths(<Nombre>)"},"addSeconds":{"description":"Retourne le résultat de l\'addition d\'un intervalle entre deux dates en secondes sous forme d\'une valeur en date et heure. L\'intervalle spécifié peut être négatif.","example":"=Fields!OrderDate.Value.AddSeconds(30)","label":"AddSeconds","syntax":"<DateHeure>.AddSeconds(<Nombre>)"},"addYears":{"description":"Retourne le résultat de l\'addition d\'un intervalle entre deux dates en années sous forme d\'une valeur en date et heure. L\'intervalle spécifié peut être négatif.","example":"=Fields!OrderDate.Value.AddYears(3)","label":"AddYears","syntax":"<DateHeure>.AddYears(<Nombre>)"},"dateAdd":{"description":"Retourne le résultat de l\'addition du champ date et heure d\'une unité donnée sous forme d\'une valeur en date et heure.","example":"=DateAdd(\\"d\\", 5, Fields!SaleDate.Value); =DateAdd(DateInterval.Day, 5, Fields!SaleDate.Value)","label":"DateAdd","syntax":"DateAdd(<IntervalDate>, <Nombre>, <DateHeure>)"},"dateDiff":{"description":"Retourne la différence entre la date et heure de début et la date et heure de fin d\'une unité donnée.","example":"=DateDiff(\\"yyyy\\", Fields!SaleDate.Value, \\"1/1/2015\\"); =DateDiff(DateInterval.Year, Fields!SaleDate.Value, \\"1/1/2015\\")","label":"DateDiff","syntax":"DateDiff(<IntervalDate>, <DateHeure1>, <DateHeure2>[, <DayOfWeek>[, <WeekOfYear>]])"},"datePart":{"description":"Retourne la valeur en Integer qui représente une partie spécifique de la date donnée.","example":"=DatePart(\\"m\\", Fields!SaleDate.Value)","label":"DatePart","syntax":"DatePart(<IntervalDate>, <DateHeure>[, <PremierJourDeLaSemaine>[, <PremiereSemaineDeLannee>]])"},"dateSerial":{"description":"Retourne une valeur au format Date représentant une année, un mois et un jour donnés dont l\'heure est réglée sur minuit (00:00:00).","example":"=DateSerial(DatePart(\\"yyyy\\", Fields!SaleDate.Value) - 10, DatePart(\\"m\\", Fields!SaleDate.Value) + 5, DatePart(\\"d\\", Fields!SaleDate.Value) - 1)","label":"DateSerial","syntax":"DateSerial(<Nombre Année>, <Nombre Mois>, <Nombre Jour>)"},"dateString":{"description":"Retourne une valeur au format String représentant la date du jour de votre système.","example":"=DateString(); =DatePart(\\"m\\", DateString())","label":"DateString","syntax":"DateString()"},"dateValue":{"description":"Retourne une valeur au format Date qui contient l\'information sur la date représentée par une String, dont l\'heure est réglée sur minuit (00:00:00).","example":"=DateValue(\\"December 12, 2015\\")","label":"DateValue","syntax":"DateValue(<ChaîneDate>)"},"day":{"description":"Retourne une valeur au format Integer allant de 1 à 31 représentant le jour du mois.","example":"=Day(Fields!SaleDate.Value)","label":"Day","syntax":"Day(<DateHeure>)"},"hour":{"description":"Retourne une valeur au format Integer allant de 0 à 23 représentant l\'heure du jour.","example":"=Hour(Fields!SaleDate.Value)","label":"Hour","syntax":"Hour(<DateHeure>)"},"minute":{"description":"Retourne une valeur au format Integer allant de 0 à 59 représentant la minute de l\'heure.","example":"=Minute(Fields!SaleDate.Value)","label":"Minute","syntax":"Minute(<DateHeure>)"},"month":{"description":"Retourne une valeur au format Integer allant de 1 à 12 représentant le mois de l\'année.","example":"=Month(Fields!SaleDate.Value)","label":"Month","syntax":"Month(<DateHeure>)"},"monthName":{"description":"Retourne le nom du mois spécifié dans la date sous forme de String.","example":"=MonthName(Fields!MonthNumber.Value)","label":"MonthName","syntax":"MonthName(<Month Number>[, <Abbreviate>])"},"now":{"description":"Retourne la date et l\'heure actuels de votre système.","example":"=Now()","label":"Now","syntax":"Now()"},"parse":{"description":"Convertit la valeur de la chaîne de caractère spécifiée sous fourme du valeur date et heure.","example":"=DateTime.Parse(\\"01/01/1970\\")","label":"DateTime.Parse","syntax":"DateTime.Parse(<Chaîne>[, <Chaîne>])"},"quarter":{"description":"Retourne une valeur au format Integer allant de 1 à 4 représenant le quart d\'une année.","example":"=Quarter(Fields!SaleDate.Value)","label":"Quarter","syntax":"Quarter(<DateHeure>)"},"quarterName":{"description":"Retourne une valeur au format String représentant le quart d\'une année.","example":"=QuarterName(Fields!SaleDate.Value)","label":"QuarterName","syntax":"QuarterName(<DateHeure>)"},"second":{"description":"Retourne une valeur au format Integer allant de 0 à 59 représentant les secondes dans une minute.","example":"=Second(Fields!SaleDate.Value)","label":"Second","syntax":"Second(<DateHeure>)"},"timeOfDay":{"description":"Retourne une valeur au format Date contenant l\'heure actuelle dans votre système.","example":"=TimeOfDay()","label":"TimeOfDay","syntax":"TimeOfDay()"},"timeSerial":{"description":"Retourne une valeur au format Date représentant une heure, minute, seconde spécifiées, dont les informations sur la date sont définies par rapport au 1er janvier de l\'année 0001.","example":"=TimeSerial(DatePart(\\"h\\", Fields!SaleDate.Value), DatePart(\\"n\\", Fields!SaleDate.Value), DatePart(\\"s\\", Fields!SaleDate.Value))","label":"TimeSerial","syntax":"TimeSerial(<Nombre Heure>, <Nombre Minute>, <Nombre Seconde>)"},"timeString":{"description":"Retourne une valeur au format String représentant l\'heure actuelle de votre système.","example":"=TimeString()","label":"TimeString","syntax":"TimeString()"},"timeValue":{"description":"Retourne une valeur au format Date contenant les informations sur l\'heure représentées par une String, dont la date est définie au 1er Janvier de l\'année 0001.","example":"=TimeValue(\\"15:25:45\\"); =TimeValue(Fields!SaleDate.Value)","label":"TimeValue","syntax":"TimeValue(<ChaîneHeure>)"},"timer":{"description":"Retourne une valeur au format Double représentant le nombre de secondes écoulées depuis minuit.","example":"=Timer()","label":"Timer","syntax":"Timer()"},"today":{"description":"Retourne une valeur au format Date contenant la date du jour de votre système.","example":"=Today()","label":"Today","syntax":"Today()"},"weekday":{"description":"Retourne une valeur au format Integer contenant un nombre représentant le jour de la semaine.","example":"=Weekday(Fields!SaleDate.Value, 0)","label":"Weekday","syntax":"Weekday(<DateHeure>[, <JourDeLaSemaine>])"},"weekdayName":{"description":"Retourne une valeur au format String contenant le nom du jour de la semaine spécifié.","example":"=WeekdayName(3, True, 0); =WeekDayName(DatePart(\\"w\\", Fields!SaleDate.Value), True, 0)","label":"WeekdayName","syntax":"WeekdayName(<JourDeLaSemaine>[, <Abréviation>[, <PremierJourDeLaSemaine>]])"},"year":{"description":"Retourne une valeur au format Integer allant de 1 à 9999 représentant l\'année.","example":"=Year(Fields!SaleDate.Value)","label":"Year","syntax":"Year(<DateHeure>)"}},"inspection":{"dbNull":{"description":"Permet de vérifier si une valeur est DBNull.","example":"=IIF(Fields!Organization.Value is DBNull.Value, \\"<NULL>\\", Fields!Organization.Value)","label":"DBNull.Value","syntax":"DBNull.Valeur"},"isArray":{"description":"Retourne True si l\'expression peut être évaluée dans un tableau.","example":"=IsArray(Parameters!Initials.Value)","label":"IsArray","syntax":"IsArray(<Expression>)"},"isDBNull":{"description":"Retourne True si l\'expression vaut null.","example":"=IsDBNull(Fields!MonthlySales.Value)","label":"IsDBNull","syntax":"IsDBNull(<Expression>)"},"isDate":{"description":"Retourne True si l\'expression représente une valeur au format Date valide.","example":"=IsDate(Fields!BirthDate.Value); =IsDate(\\"31/12/2010\\")","label":"IsDate","syntax":"IsDate(<Expression>)"},"isError":{"description":"Retourne True si l\'expression est une erreur.","example":"=IsError(Fields!AnnualSales.Value = 80000)","label":"IsError","syntax":"IsError(<Expression>)"},"isNothing":{"description":"Retourne True si l\'expression est vide.","example":"=IsNothing(Fields!MiddleInitial.Value)","label":"IsNothing","syntax":"IsNothing(<Expression>)"},"isNumeric":{"description":"Retourne True si l\'expression est un nombre.","example":"=IsNumeric(Fields!AnnualSales.Value)","label":"IsNumeric","syntax":"IsNumeric(<Expression>)"}},"math":{"abs":{"description":"Retourne la valeur absolue ou positive d\'un nombre à une décimale.","example":"=Abs(-5.5); =Abs(Fields!YearlyIncome.Value - 80000)","label":"Abs","syntax":"Abs(<Nombre>)"},"acos":{"description":"Retourne un angle dont le cosinus est le nombre spécifié.","example":"=Acos(.5); =Acos(Fields!Angle.Value)","label":"Acos","syntax":"Acos(<Nombre>)"},"asin":{"description":"Retourne un angle dont le sinus est le nombre spécifié.","example":"=Asin(.5); =Asin(Fields!Angle.Value)","label":"Asin","syntax":"Asin(<Nombre>)"},"atan":{"description":"Retourne un angle dont la tangente est le nombre spécifié.","example":"=Atan(.5); =Atan(Fields!Angle.Value)","label":"Atan","syntax":"Atan(<Nombre>)"},"atan2":{"description":"Retourne l\'angle dont la tangente est le quotient de deux nombres spécifiques.","example":"=Atan2(3,7); =Atan2(Fields!CoordinateY.Value, Fields!CoordinateX.Value)","label":"Atan2","syntax":"Atan2(<Nombre1>, <Nombre2>)"},"bigMul":{"description":"Retourne la multiplication de deux nombres de 32 bit.","example":"=BigMul(4294967295,-2147483647); =BigMul(Fields!Int32Value.Value, Fields!Int32Value.Value)","label":"BigMul","syntax":"BigMul(<Nombre1>, <Nombre2>)"},"ceiling":{"description":"Retourne le plus petit entier supérieur ou égal au nombre à deux décimales.","example":"=Ceiling(98.4331); =Ceiling(Fields!AnnualSales.Value / 6)","label":"Ceiling","syntax":"Ceiling(<Nombre>)"},"cos":{"description":"Retourne le plus petit entier supérieur ou égal au nombre à deux décimales.","example":"=Cos(60)","label":"Cos","syntax":"Cos(<Nombre>)"},"cosh":{"description":"Retourne le cosinus hyperbolique de l\'angle spécifié.","example":"=Cosh(60)","label":"Cosh","syntax":"Cosh(<Nombre>)"},"e":{"description":"Retourne la valeur de E qui est de 2.71828182845905.","example":"=E * 2","label":"E","syntax":"E"},"exp":{"description":"Retourne e élevé à la puissance spécifiée, où e est le nombre d\'Euler. C\'est l\'inverse de la fonction log.","example":"=Exp(3); =Exp(Fields!IntegerCounter.Value)","label":"Exp","syntax":"Exp(<Nombre>)"},"fix":{"description":"Retourne la partie entière d\'un nombre.","example":"=Fix(-7.15); =Fix(Fields!AnnualSales.Value / -5)","label":"Fix","syntax":"Fix(<Nombre>)"},"floor":{"description":"Retourne l\'entier le plus grand plus petit ou égal au nombre à deux décimales spécifié.","example":"=Floor(4.67); =Floor(Fields!AnnualSales.Value / 12)","label":"Floor","syntax":"Floor(<Nombre>)"},"ieeeRemainder":{"description":"Retourne le rest après la division d\'un  nombre par un autre conformément aux normes IEEE.","example":"=IEEERemainder(9, 8)","label":"IEEERemainder","syntax":"IEEERemainder(<Nombre1>, <Nombre2>)"},"log":{"description":"Retourne le logarithme d\'une nombre spécifique.","example":"=Log(20.5); =Log(Fields!NumberValue.Value)","label":"Log","syntax":"Log(<Nombre>)"},"log10":{"description":"Renvoie le logarithme du nombre spécifié en base 10.","example":"=Log10(20.5); =Log10(Fields!NumberValue.Value)","label":"Log10","syntax":"Log10(<Nombre>)"},"max":{"description":"Retourne la valeur non-nulle maximum de l\'expresison spécifiée.","example":"=Max(Fields!OrderTotal.Value)","label":"Max","syntax":"Max(<Valeurs>)"},"min":{"description":"Retourne la valeur non-nulle minimum de l\'expression spécifiée.","example":"=Min(Fields!OrderTotal.Value)","label":"Min","syntax":"Min(<Valeurs>)"},"pi":{"description":"Retourne la valeur de Pi qui est 3.14159265358979.","example":"=2 * PI * Fields!Radius.Value","label":"PI","syntax":"PI"},"pow":{"description":"Retourne un nombre élevé à la puissance d\'un autre nombre.","example":"=Pow(Fields!Quantity.Value, 2)","label":"Pow","syntax":"Pow(<Nombre1>, <Nombre2>)"},"round":{"description":"Retourne l\'arrondi d\'un nombre décimal à l\'entier le plus proche ou au nombre décimal le plus proche jusqu\'aux chiffres spécifiés.","example":"=Round(12.456); =Round(Fields!AnnualSales.Value / 12.3)","label":"Round","syntax":"Round(<Nombre>)"},"sign":{"description":"Retourne une valeur indiquant le signe d\'un nombre entier sur 8 bit signé.","example":"=Sign(Fields!AnnualSales.Value - 60000)","label":"Sign","syntax":"Sign(<Nombre>)"},"sin":{"description":"Retourne le sinus du nombre spécifié.","example":"=Sin(60)","label":"Sin","syntax":"Sin(<Nombre>)"},"sinh":{"description":"Retourne le sinus hyperbolique de l\'angle spécifié.","example":"=Sinh(60)","label":"Sinh","syntax":"Sinh(<Nombre>)"},"sqrt":{"description":"Retourne la racine carrée du nombre spécifié.","example":"=Sqrt(121)","label":"Sqrt","syntax":"Sqrt(<Nombre>)"},"tan":{"description":"Retourne la tangente du nombre spécifié.","example":"=Tan(60)","label":"Tan","syntax":"Tan(<Nombre>)"},"tanh":{"description":"Retourne la tangente hyperbolique de l\'angle spécifié.","example":"=Tanh(60)","label":"Tanh","syntax":"Tanh(<Nombre>)"},"truncate":{"description":"Supprime les chiffres après la virgule sans arrondir, et retourne la valeur de l\'entier.","example":"=Truncate(Fields!UnitPrice.Value)","label":"Truncate","syntax":"Truncate(<Nombre>)"}},"miscellaneous":{"getFields":{"description":"Retourne un object de type IDictionary<string, Champ> contenant les contenus courants de la collection Champs. Uniquement valide à l\'utilisation dans une data region. Cette fonction facilite l\'écriture du code traitant des conditionnelles complexes. L\'écriture d\'une fonction équivalente sans GetFields() nécessiterait de passer chaque valeur des champs de la requête dans la méthode ce qui serait rédhibitoire dans le traitement de nombreux champ.","example":"=GetFields(); =Code.DisplayAccountID(GetFields())","label":"GetFields","syntax":"GetFields()"},"getLength":{"description":"Retourne le nombre d\'éléments dans le tableau spécifié.","example":"=Parameters!MultiValueParameter.Value.GetLength(0)","label":"GetLength","syntax":"<Collection>.GetLength(<Nombre>)"},"groupIndex":{"description":"Retourne l\'index de l\'élément dans le groupe courant.","example":"=GroupIndex()","label":"GroupIndex","syntax":"GroupIndex()"},"groupIndexWithScope":{"description":"Retourne l\'index de l\'élément dans le groupe spécifié.","example":"=GroupIndex(Group1)","label":"GroupIndex (with scope)","syntax":"GroupIndex(<Groupe>)"},"inScope":{"description":"Evalue à true ou false en fonction de si la valeur courante est incluse dans la portée spécifique.","example":"=InScope(\\"Order\\")","label":"InScope","syntax":"InScope(<Portée>)"},"indexOf":{"description":"Returns the first index at which a given element can be found in the array or -1 if it is not present.","example":"=IndexOf(Parameters!pContinent.Value, Fields!ContinentName.Value) >= 0","example_i11n":"{IndexOf(@pContinent, ContinentName) >= 0}","label":"IndexOf","syntax":"IndexOf(<Source>, <SearchElement>)"},"item":{"description":"Retourne un élément par son nom de Champs/Paramètres/ElémentsDeRapport.","example":"=Fields.Item(\\"Company Name\\").Name; =Parameters.Item(\\"Parameter1\\").Name; =ReportItems.Item(\\"TextBox1\\").Value","example_i11n":"{Fields.Item(\\"Company Name\\").Name}; {Parameters.Item(\\"Parameter1\\").Name}; {ReportItems.Item(\\"TextBox1\\").Value}","label":"Item","syntax":"<Objet | Enregistrement>.Item(<Chaîne>)"},"join":{"description":"Retourne une chaîne de caractère qui est le résultat du regroupement des éléments d\'un tableau en utilisant le délimiteur spécifié entre les éléments.","example":"=Join(Parameters!MultiValueParameter.Value, \\", \\")","label":"Join","syntax":"Join(<Valeurs>, <Chaîne>)"},"level":{"description":"Retourne un entier basé sur zéro représenant le niveau de profondeur actuel dans une hiérarchie récursive dans la portée courante. Le premier niveau dans la hiérarchie est 0.","example":"=Level()","label":"Level","syntax":"Level()"},"levelWithScope":{"description":"Retourne un entier basé sur zéro représenant le niveau de profondeur actuel dans une hiérarchie récursive dans la portée spécifiée. Le premier niveau dans la hiérarchie est 0.","example":"=Level(\\"Order\\")","label":"Level (with scope)","syntax":"Level(<Portée>)"},"lookup":{"description":"Retourne la première valeur correspondant au nom spécifié du dataset ayant des paires de nom et valeur.","example":"=Lookup(Fields!ProductID.Value, Fields!ProductID.Value, Fields!Quantity.Value, \\"DataSet2\\")","label":"Lookup","syntax":"Lookup(<Source>, <Destination>, <Résultat>, <DataSet>)"},"lookupSet":{"description":"Retourne l\'ensemble des valeurs correspondant au nom spécifié du dataset contenant des paires nom/valeur.","example":"=LookupSet(Fields!ProductID.Value, Fields!ProductID.Value, Fields!Quantity.Value, \\"DataSet2\\")","label":"LookupSet","syntax":"LookupSet(<Source>, <Destination>, <Résultat>, <DataSet>)"},"previous":{"description":"Calcule la valeur de l\'expression pour la ligne de données précédente.","example":"=Previous(Fields!OrderID.Value)","label":"Previous","syntax":"Previous(<Expression>)"},"previousWithScope":{"description":"Calcule la valeur de l\'expression pour la ligne de données précédente au sein de la portée spécifiée.","example":"=Previous(Fields!OrderID.Value, \\"Order\\")","label":"Previous (with scope)","syntax":"Previous(<Expression>, <Portée>)"},"rowNumber":{"description":"Retourne le compte courant de toutes les lignes.","example":"=RowNumber()","label":"RowNumber","syntax":"RowNumber()"},"rowNumberWithScope":{"description":"Retourne le compte courant de toutes les lignes au sein de la portée spécifiée.","example":"=RowNumber(\\"OrderID\\")","label":"RowNumber (with scope)","syntax":"RowNumber(<Portée>)"}},"programFlow":{"choose":{"description":"Retourne une valeur d\'une liste d\'arguments.","example":"=Choose(3, \\"10\\", \\"15\\", \\"20\\", \\"25\\")","label":"Choose","syntax":"Choose(<Index>, <Valeur1>[, <Valeur2>,...[, <ValeurN>]])"},"iif":{"description":"Retourne la première valeur si l\'expression est True, et la seconde valeur si l\'expression est False.","example":"=IIF(Fields!AnnualSales.Value >= 80000, \\"Above Average\\", \\"Below Average\\")","label":"IIF","syntax":"IIF(<Condition>, <PartieTrue>, <PartieFalse>)"},"partition":{"description":"Retourne une chaîne de caractère (sous la forme x : y) représentant la plage calculée basée sur l\'intervalle spécifié contenant le nombre spécifié.","example":"=Partition(1999, 1980, 2000, 10)","label":"Partition","syntax":"Partition(<Valeur>, <Début>, <Fin>, <Intervalle>)"},"switch":{"description":"Retourne la valeur de la première expression évaluée à True parmi une liste d\'expressions.","example":"=Switch(Fields!FirstName.Value = \\"Abraham\\", \\"Adria\\", Fields!FirstName.Value = \\"Charelotte\\", \\"Cherrie\\")","label":"Switch","syntax":"Switch(<Condition1>, <Valeur1>[, <Condition2>, <Valeur2>,...[, <ConditionN>, <ValeurN>]])"}},"text":{"contains":{"description":"Retourne True si la chaîne de caractère contient la sous-chaîne spécifiée.","example":"=Fields!ShipAddress.Value.Contains(\\"street\\")","label":"Contains","syntax":"<Chaîne>.Contains(<Chaîne>)"},"endsWith":{"description":"Retourne vrai si la chaîne de caractère se termine par la sous-chaîne spécifiée.","example":"=Fields!Description.Value.EndsWith(\\"documents\\")","label":"EndsWith","syntax":"<Chaîne>.EndsWith(<Chaîne>)"},"inStr":{"description":"Retourne la position de départ de la première occurrence de la sous-chaîne spécifiée dans la chaîne de caractère.","example":"=InStr(Fields!Description.Value, \\"documents\\")","label":"InStr","syntax":"InStr(<Chaîne>, <Chaîne>)"},"lastIndexOf":{"description":"Retourne l\'index de la dernière occurrence de la sous-chaîne spécifiée dans la chaîne de caractère.","example":"=Fields!Description.Value.LastIndexOf(\\"documents\\")","label":"LastIndexOf","syntax":"<Chaîne>.LastIndexOf(<Chaîne>[, <Nombre>])"},"replace":{"description":"Remplace toutes les occurrences de la première sous-chaîne spécifiée avec la seconde sous-chaîne spécifiée dans la chaîne de caractère.","example":"=Fields!Description.Value.Replace(\\"documents\\", \\"invoices\\")","label":"Replace","syntax":"<Chaîne>.Replace(<Chaîne>, <Chaîne>)"},"startsWith":{"description":"Retourne True si la chaîne de caractère commmence par la sous-chaîne spécifiée.","example":"=Fields!Description.Value.StartsWith(\\"Invoice\\")","label":"StartsWith","syntax":"<Chaîne>.StartsWith(<Chaîne>)"},"substring":{"description":"Retourne la sous-chaîne à la position spécifiée (basée sur zéro) de la longueur spécifiée.","example":"=Fields!Description.Value.Substring(1, 10)","label":"Substring","syntax":"<Chaîne>.Substring(<Nombre>, <Nombre>)"},"toLower":{"description":"Retourne la chaîne de caractère spécifiée en minuscule.","example":"=Fields!ShipCountry.Value.ToLower()","label":"ToLower","syntax":"<Chaîne>.ToLower()"},"toUpper":{"description":"Retourne la chaîne de caractère spécifiée en majuscule.","example":"=Fields!ShipCountry.Value.ToUpper()","label":"ToUpper","syntax":"<Chaîne>.ToUpper()"},"trim":{"description":"Retourne la chaîne de caractère après avoir supprimé tous les espaces au début et à la fin de la chaîne de caractère spécifiée.","example":"=Parameters!Info.Value.Trim()","label":"Trim","syntax":"<Chaîne>.Trim()"},"trimEnd":{"description":"Retourne la chaîne de caractère après avoir supprimé tous les espaces à la fin de la chaîne de caractère spécifiée.","example":"=Parameters!Info.Value.TrimEnd()","label":"TrimEnd","syntax":"<Chaîne>.TrimEnd()"},"trimStart":{"description":"Retourne la chaîne de caractère après avoir supprimé tous les espaces du début de la chaîne de caractère spécifiée.","example":"=Parameters!Info.Value.TrimStart()","label":"TrimStart","syntax":"<Chaîne>.TrimStart()"}}},"titles":{"aggregate":"Aggrégat","conversion":"Conversion","dateTime":"Date & Heure","inspection":"Inspection","label":"Fonctions Communes","math":"Math","miscellaneous":"Divers","programFlow":"Structures Conditionnelles","text":"Texte"}},"operations":{"info":{"arithmetic":{"add":{"description":"Evalue la somme de deux nombres ou concatène deux chaînes de caractère.","example":"=Fields!Quantity.Value + 2","label":"+","syntax":"<Valeur1> + <Valeur2>"},"divide":{"description":"Divise deux nombres (numérateur par dénominateur) et retourne le quotient sous la forme d\'un nombre décimal.","example":"=Fields!AnnualSales.Value / 2","label":"/","syntax":"<Nombre1> / <Nombre2>"},"integerDivide":{"description":"Divise deux nombres et retourne le résultat sous la forme d\'un entier.","example":"=Fields!AnnualSales.Value \\\\ 2","label":"\\\\","syntax":"<Nombre1> \\\\ <Nombre2>"},"mod":{"description":"Divise deux nombres et retourne le reste.","example":"=Fields!AnnualSales.Value Mod 12","label":"Mod","syntax":"<Nombre1> Mod <Nombre2>"},"multiply":{"description":"Evalue la multiplication de deux nombres.","example":"=Fields!Quantity.Value * 5","label":"*","syntax":"<Nombre1> * <Nombre2>"},"power":{"description":"Elève un nombre à la puissance d\'un autre nombre.","example":"=Fields!Quantity.Value ^ 2","label":"^","syntax":"<Nombre1> ^ <Nombre2>"},"subtract":{"description":"Evalue la différence entre deux nombres ou déduit la valeur d\'une expression numérique.","example":"=Fields!Quantity.Value - 2","label":"-","syntax":"<Nombre1> - <Nombre2>"}},"bitShift":{"leftShift":{"description":"Effectue un décalage arithmétique vers la gauche sur un motif de bits.","example":"=Fields!RegionID.Value << 2","label":"<<","syntax":"<Nombre1> << <Nombre2>"},"rightShift":{"description":"Effectue un décalage arithmétique vers la droite sur un motif de bits.","example":"=Fields!RegionID.Value >> 2","label":">>","syntax":"<Nombre1> >> <Nombre2>"}},"comparison":{"equal":{"description":"Retourne True si l\'opérande de gauche est égal à l\'opérande de droite.","example":"=Fields!AnnualSales.Value = 80000","label":"=","syntax":"<Valeur1> = <Valeur2>"},"greaterThan":{"description":"Retourne True si l\'opérande de gauche est plus grand que l\'opérande de droite.","example":"=Fields!AnnualSales.Value > 80000","label":">","syntax":"<Valeur1> > <Valeur2>"},"greaterThanOrEqual":{"description":"Retourne True si l\'opérande de gauche est plus grand ou égal à l\'opérande de droite.","example":"=Fields!AnnualSales.Value >= 80000","label":">=","syntax":"<Valeur1> >= <Valeur2>"},"is":{"description":"Compare deux références d\'un objet et retourne True si l\'opérande de gauche est identique à l\'opérande de droite.","example":"=Fields!FirstName.Value Is Fields!LastName.Value","label":"Is","syntax":"<Valeur1> Is <Valeur2>"},"like":{"description":"Compare deux chaînes de caractère et retourne True si l\'opérande de gache est le même que l\'opérande de droite.","example":"=Fields!FirstName.Value Like \\"A*\\"","label":"Like","syntax":"<Chaîne1> Like <Chaîne2>"},"lowerThan":{"description":"Retourne True si l\'opérande de gauche est plus petit que l\'opérande de droite.","example":"=Fields!AnnualSales.Value < 80000","label":"<","syntax":"<Valeur1> < <Valeur2>"},"lowerThanOrEqual":{"description":"Retourne True si l\'opérande de gauche est plus petit ou égal à l\'opérande de droite.","example":"=Fields!AnnualSales.Value <= 80000","label":"<=","syntax":"<Valeur1> <= <Valeur2>"},"notEqual":{"description":"Retourne True si l\'opérande de gauche n\'est pas égal l\'opérande de droite.","example":"=Fields!AnnualSales.Value <> 80000","label":"<>","syntax":"<Valeur1> <> <Valeur2>"}},"concatenation":{"add":{"description":"Evalue la somme de deux nombres ou concatène deux chaînes de caractère.","example":"=Fields!FirstName.Value + \\" \\" + Fields!LastName.Value","label":"+","syntax":"<Chaîne1> + <Chaîne2>"},"concat":{"description":"Retourne la valeur en chaîne de caractère de la concaténation de deux chaînes de caractère individuelles.","example":"=Fields!FirstName.Value & \\" \\" & Fields!LastName.Value","example_i11n":"{FirstName & \\" \\" & LastName}","label":"&","syntax":"<Chaîne1> & <Chaîne2>"}},"logicalBitwise":{"and":{"description":"Retourne la conjonction logique de deux expressions Booléennes, ou la conjonction binaire de deux expressions numériques.","example":"=(Fields!AnnualSales.Value > 80000) And (Fields!Quantity.Value > 5)","label":"And","syntax":"<Valeur1> And <Valeur2>"},"andAlso":{"description":"Retourne la conjonction logique de deux expressions Booléennes en omettant l\'évaluation de l\'autre expression si celle-ci fournit le résultat.","example":"=(Fields!AnnualSales.Value > 80000) AndAlso (Fields!Quantity.Value > 1)","label":"AndAlso","syntax":"<Booléen1> AndAlso <Booléen2>"},"not":{"description":"Retourne la négation logique d\'une expression Booléenne, ou la négation binaire d\'une expression numérique.","example":"=Not (Fields!AnnualSales.Value > 80000)","label":"Not","syntax":"Not <Valeur>"},"or":{"description":"Retourne la disjonction logique de deux expressions Booléennes, ou la disjonction binaire de deux valeurs numériques.","example":"=(Fields!AnnualSales.Value > 80000) Or (Fields!Quantity.Value > 5)","label":"Or","syntax":"<Valeur1> Or <Valeur2>"},"orElse":{"description":"Retourne la disjonction logique de deux expressions Booléennes en omettant l\'évaluation de la seconde expression si celle-ci fournit le résultat.","example":"=(Fields!AnnualSales.Value > 80000) OrElse (Fields!Quantity.Value > 1)","label":"OrElse","syntax":"<Booléen1> OrElse <Booléen2>"},"xor":{"description":"Retourne une opération d\'exclusion logique de deux expressions Booléennes, ou une exclusion binaire de deux expressions numériques .","example":"=(Fields!AnnualSales.Value > 80000) Xor (Fields!Quantity.Value) > 5","label":"Xor","syntax":"<Valeur1> Xor <Valeur2>"}}},"titles":{"arithmetic":"Arithmetique","bitShift":"Bit Shift","comparison":"Comparaison","concatenation":"Concaténation","label":"Opérations","logicalBitwise":"Logique/Bitwise"}},"parameters":{"titles":{"label":"Paramètres"}},"reportItems":{"titles":{"label":"Eléments de rapport"}},"reportPartProperties":{"info":{"example":"=PartProperties!<PropertyName>.Value","example_i11n":"{PartProperties.<PropertyName>.Value}, {PartProperties!<PropertyName>.Value}"},"titles":{"label":"Report Part Properties"}},"slicers":{"titles":{"label":"Filtres"}},"textEncodingFields":{"titles":{"label":"Champs d\'Encodage du Texte"}},"theme":{"titles":{"color":"Couleurs","constant":"Constantes","font":"Polices de caractère","image":"Images","label":"Thème","majorFont":"Police Principale","minorFont":"Police secondaire"}}}},{"lng":"fr","ns":"filters","resources":{"add":"Ajouter...","addCriterion":"Ajouter critère","addGroup":"Ajouter groupe","addItem":"Ajouter élément","allOf":"Tous","anyOf":"N\'importe quel","delete":"Supprimer","edit":"Modifier...","expressionText":"Expression...","listItemsCount":"{{count}} élément","listItemsCount_plural":"{{count}} élements","newParameter":"Nouveau paramètre","operators":{"beginsWith":"Commence par","between":"Entre","bottomN":"Bas N","bottomPercent":"Pourcentage inférieur","contains":"Contient","doesNotBeginWith":"Ne commence pas par","doesNotContain":"Ne contient pas","equalTo":"Égal à","greaterThan":"Plus grand que","greaterThanOrEqualTo":"Plus grand que ou égal à","in":"Dans","lessThan":"Plus petit que","lessThanOrEqualTo":"Plus pétit que ou égal à","like":"Comme","notEqualTo":"Pas égal à","notIn":"Pas dans","topN":"Haut N","topPercent":"Pourcentage supérieur"},"reset":"Réinitialiser"}},{"lng":"fr","ns":"glyphs-RPX","resources":{"barcode":{"textError":"Error","unsupportedSymbology":"[{{symbology}}] \\"{{itemName}}\\" preview is limited in design-time."}}},{"lng":"fr","ns":"groupEditor","resources":{"addGroup":{"btnAdjacentAfter":"Adjacent Après","btnAdjacentBefore":"Adjacent Avant","btnChild":"Enfant","btnParent":"Parent"},"addTotal":{"btnAfter":"Après","btnBefore":"Avant"},"btnDelete":"Supprimer","btnDisableGroup":"Désactiver groupe","btnEditExpression":"Modifier expression","btnEnableGroup":"Activer groupe","headingAddGroup":"Ajouter groupe","headingAddTotal":"Ajputer Total","headingColumnGroups":"Groupes de colonnes","headingRowGroups":"Groupes de lignes","labelAdvancedMode":"Mode avancé","textHiddenStatic":"(Statique)","textSelectTablix":"Sélectionnez un tableau matriciel pour modifier ses groupes"}},{"lng":"fr","ns":"labels","resources":{"body":"Corps","dvchartPlotRuleNoCondition":"État vide","dvchartXAxis":"Axe X","dvchartYAxis":"Axe Y","footer":"Pied","header":"En-tête","pageFooter":"Pied de page","pageHeader":"En-tête de page","total":"Total"}},{"lng":"fr","ns":"marginsSizes","resources":{"values":[{"_name":"Aucun","cm":{"bottom":"0cm","left":"0cm","right":"0cm","top":"0cm"},"in":{"bottom":"0in","left":"0in","right":"0in","top":"0in"}},{"_name":"Étroit","cm":{"bottom":"1.25cm","left":"1.25cm","right":"1.25cm","top":"1.25cm"},"in":{"bottom":"0.5in","left":"0.5in","right":"0.5in","top":"0.5in"}},{"_name":"Normal","cm":{"bottom":"2.5cm","left":"2.5cm","right":"2.5cm","top":"2.5cm"},"in":{"bottom":"1in","left":"1in","right":"1in","top":"1in"}},{"_name":"Large","cm":{"bottom":"2.5cm","left":"5cm","right":"5cm","top":"2.5cm"},"in":{"bottom":"1in","left":"2in","right":"2in","top":"1in"}}]}},{"lng":"fr","ns":"nameTemplate-RPX","resources":{"Barcode":"Barcode","CheckBox":"CheckBox","CrossSectionBox":"CrossSectionBox","CrossSectionLine":"CrossSectionLine","Detail":"Detail","GroupFooter":"GroupFooter","GroupHeader":"GroupHeader","InputFieldCheckBox":"InputFieldCheckBox","InputFieldText":"InputFieldText","Label":"Label","Line":"Line","PageBreak":"PageBreak","PageFooter":"PageFooter","PageHeader":"PageHeader","Picture":"Picture","ReportFooter":"ReportFooter","ReportHeader":"ReportHeader","ReportInfo":"ReportInfo","RichTextBox":"RichTextBox","Shape":"Shape","Style":"Style","SubReport":"SubReport","TextBox":"TextBox","Unknown":"Unknown"}},{"lng":"fr","ns":"nameTemplates","resources":{"Continuous":"SectionContinue","Unknown":"Élement","bandedList":"Liste bandeau","bandedListGroup":"Groupe","barcode":"Code barre","bullet":"Puce","calculatedField":"ChampCalculé","categoryGroup":"GroupeCatégorie","chart":"Grpahique","checkbox":"Case à cocher","columnGroup":"GroupeColonne","container":"Conteneur","contentplaceholder":"ContentPlaceholder","dashboardSection":"Section","dataset":"DataSet","detailsGroup":"GroupeDétails","dvchart":"Graphique","dvchartEncodingField":"Champ","dvchartPlot":"Lot","dvchartoverlay":"Surcouche","dvchartrule":"Règle","dvcharttextencoding":"Texte","field":"Champ","fixedPage":"PageFixe","formattedText":"TexteFormaté","group":"Groupe","image":"Image","inputField":"ChampDeSaisie","label":"Libellé","layer":"Calque","line":"Ligne","list":"Liste","mailMergeField":"Champ","overflowPlaceholder":"EspaceDeDébordement","parameter":"Paramètre","parameterCollection":"Paramètres","partItem":"PartItem","pointer":"Pointeur","reportParameter":"Paramètre","reportPart":"ReportPart","reportPartProperty":"Property","reportSlicer":"TrancheRapport","richtext":"TexteEnrichi","rowGroup":"GroupeLigne","seriesGroup":"GroupeSéries","seriesValue":"Valeur","shape":"Forme","sparkline":"Sparkline","subreport":"Sous-rapport","table":"Tableau","tableDetailsGroup":"GRoupeDétailsTableau","tableGroup":"GroupeTableau","tableOfContents":"TableDesMatières","tablix":"TableauMatriciel","textbox":"ZoneDeTexte","tocLevel":"Niveau"}},{"lng":"fr","ns":"notifications","resources":{"addReportItem":{"caption":"Un élément de rapport de type \'{{reportItemType}}\' ne peut pas être ajouté à {{containerType}}"},"contentPlaceholderSize":{"caption":"Action cannot be performed","text":"Content exceeds Content placeholder size"},"contentPlaceholderSizeWithDetails":{"text":"Content exceeds Content placeholder size. Please try manually reducing the size or position of {{items}} in the {{contentPlaceholder}}."},"convertTableForClient":{"text":"✘ Le tableau \'{{name}}\' a une structure invalide et a été converti en tableau matriciel"},"deleteRowColumn":{"caption":"Impossible de supprimer {{rowColumn}}","text":"{{rowColumn}} ne peut être supprimé car {{itemType}} \'{{itemName}}\' aurait alors une taille invalide."},"fixedPageSize":{"caption":"L\'action ne peut pas être effectuée","text":"L\'élément de rapport ne peut pas dépasser la taille de la page"},"fplPasteFailed":{"caption":"L\'opération de collage a échoué","text":"Il n\'y a pas assez d\'espace pour coller les éléments du rapport"},"innerException":{"caption":"Une exception interne s\'est produite"},"invalidPageSizes":{"caption":"Cette action entraînera des tailles de page non valides"},"libraries":{"dataSetNotFound":{"text":"Required data set \\"{{dataSetName}}\\" not found"},"dataSourceNotFound":{"text":"Required data source \\"{{dataSourceName}}\\" not found"},"duplicateLibrary":{"text":"Library with {{libProp}} \\"{{libValue}}\\" already exists. Library {{libProp}} must be unique"},"importFailed":{"caption":"Library \\"{{path}}\\" importing failed"},"invalidLibraryName":{"text":"Invalid library name \\"{{libName}}\\". Use alphanumeric characters and underscore only"},"loadingFailed":{"caption":"Library \\"{{path}}\\" loading failed"},"noDataSetsFound":{"text":"No data sets found in library"},"nonMslLibrary":{"text":"Unable to parse non-MSL report as library"},"unknownLibrary":{"caption":"Report depends on unknown library","text":"The library \\"{{libName}}\\" used in this report is not found"}},"listRowsOrColumnsCount":{"caption":"L\'action ne peut pas être effectuée","text":"Le contenu dépasse la taille de la colonne/ligne de la liste"},"lockLayout":{"caption":"La mise en page du rapport est verrouillée","text":"Le redimensionnement, le déplacement, la suppression, l\'ajout d\'éléments de rapport et la modification des propriétés de mise en page sont limités."},"masterReports":{"dataSetsWereRenamed":{"caption":"Data sets were renamed","text":"Data sets which conflict with those in the master report were renamed: {{dataSetNames}}."},"dataSourcesWereRenamed":{"caption":"Data sources were renamed","text":"Data sources which conflict with those in the master report were renamed: {{dataSourceNames}}."},"embeddedImagesWereRenamed":{"caption":"Embedded Images were renamed","text":"Embedded Images which conflict with those in the master report were renamed: {{embeddedImagesNames}}."},"layersWereRenamed":{"caption":"Layers were renamed","text":"Layers which conflict with those in the master report were renamed: {{layersNames}}."},"parametersWereRenamed":{"caption":"Report parameters were renamed","text":"Parameters which conflict with those in the master report were renamed: {{parameterNames}}."},"reportItemsWereMovedOrResized":{"caption":"Report items were moved or resized","details":"{{num}}. {{items}} in the {{contentPlaceholder}}\\n","text":"The items in the content placeholders moved or resized to fit into the updated content placeholders of master report:\\n{{outsideItems}}"},"reportItemsWereRenamed":{"caption":"Report items were renamed","text":"Report items which conflict with those in the master report were renamed: {{reportItemNames}}."}},"pasteFailed":{"caption":"Élément non valide à coller: {{elementName}}","text":"Vous pouvez coller l\'espace de débordement uniquement dans le rapport à page fixe"},"pasteFailedDecode":{"caption":"Erreur de décodage lors du collage: {{reason}}"},"pasteWarningDecode":{"caption":"Avertissement de décodage lors du collage","text":"{{warning}}"},"removeDefaultLayer":{"caption":"Le calque par défaut ne peut pas être supprimé"},"removeLastValidValue":{"caption":"La dernière valeur valide pour le paramètre lié ne peut pas être supprimée"},"transaction":{"caption":"Echec de la transaction: {{innerException}}"}}},{"lng":"fr","ns":"pageSizes","resources":{"values":[{"_name":"Lettre","cm":{"height":"27.9cm","width":"21.6cm"},"in":{"height":"11in","width":"8.5in"}},{"_name":"Tabloïde","cm":{"height":"43.2cm","width":"27.9cm"},"in":{"height":"17in","width":"11in"}},{"_name":"Legal","cm":{"height":"35.6cm","width":"21.6cm"},"in":{"height":"14in","width":"8.5in"}},{"_name":"Executive","cm":{"height":"26.7cm","width":"18.4cm"},"in":{"height":"10.5in","width":"7.25in"}},{"_name":"A3","cm":{"height":"42cm","width":"29.7cm"},"in":{"height":"16.54in","width":"11.69in"}},{"_name":"A4","cm":{"height":"29.7cm","width":"21cm"},"in":{"height":"11.69in","width":"8.27in"}},{"_name":"A5","cm":{"height":"21cm","width":"14.8cm"},"in":{"height":"8.27in","width":"5.83in"}},{"_name":"A6","cm":{"height":"14.8cm","width":"10.5cm"},"in":{"height":"5.83in","width":"4.13in"}},{"_name":"B4 (JIS)","cm":{"height":"36.4cm","width":"25.7cm"},"in":{"height":"14.33in","width":"10.12in"}},{"_name":"B5 (JIS)","cm":{"height":"25.7cm","width":"18.2cm"},"in":{"height":"10.12in","width":"7.17in"}},{"_name":"B6 (JIS)","cm":{"height":"18.2cm","width":"12.8cm"},"in":{"height":"7.17in","width":"5.04in"}},{"_name":"B5 (ISO)","cm":{"height":"25cm","width":"17.6cm"},"in":{"height":"9.84in","width":"6.93in"}}]}},{"lng":"fr","ns":"parametersViewEditor","resources":{"alignmentEnum":{"bottom":"Bas","center":"Centre","horizontal":"Horizontal","justify":"Justifié","left":"Gauche","none":"Aucun","right":"Droite","top":"Haut","vertical":"Vertical"},"bindingAdorner":{"headingBinding":"Liaison","textUnspecified":"Non spécifié"},"bindingProperties":{"any":"Tout","boolean":"Booléen","date":"Date","dateDateTime":"Date, DateHeure","integerFloat":"Entier, Flottant"},"booleanProperties":{"checkbox":"Case à cocher","falseText":"Faux","radio":"Radio","toggle":"Basculer","trueText":"Vrai","undefinedText":"Non défini"},"boundParameter":{"allowBlank":"Autoriser vide","defaultValue":"Valeur par défaut","multiValue":"Valeur multiple","multiline":"Multiligne","nullable":"Nul autorisé","parameter":"{{name}} Paramètre","text":"Paramètre lié"},"buttonProperties":{"clear":"Effacer","preview":"Aperçu","reset":"Réinitialiser"},"canvas":"Canevas","dataType":{"boolean":"Booléen","date":"Date","dateTime":"DateHeure","float":"Flottant","integer":"entier","string":"Chaîne de caractères"},"dateRangeParameterLabels":{"monthYearOrder":"M-A","placeholderDateEnd":"Fin","placeholderDateStart":"Début","shortcuts":{"labelLastMonth":"Mois précédent","labelLastWeek":"Semaine précédente","labelLastYear":"Année précédente","labelMonthToDate":"Mois en cours","labelWeekToDate":"Semaine en cours","labelYearToDate":"Année en cours"},"tabLabelAnnually":"Annuel","tabLabelDaily":"Quotidien","tabLabelMonthly":"Mensuel","textBack":"Retour au calendrier","textShortcutsList":"Plages communnes"},"dateTimeParameterLabels":{"monthYearOrder":"M-A","textBack":"Retour au calendrier","textClear":"Effacer","textToday":"Aujourd\'hui"},"dateTimeRange":{"rangeTypes":{"Current_Day":"Current Day","Current_Hour":"Current Hour","Current_Minute":"Current Minute","Current_Month":"Current Month","Current_Quarter":"Current Quarter","Current_Week":"Current Week","Current_Year":"Current Year","LastSimple_Day":"Last Day","LastSimple_Hour":"Last Hour","LastSimple_Minute":"Last Minute","LastSimple_Month":"Last Month","LastSimple_Quarter":"Last Quarter","LastSimple_Second":"Last Second","LastSimple_Week":"Last Week","LastSimple_Year":"Last Year","LastToDateSimple_Day":"Last Day To Date","LastToDateSimple_Month":"Last Month To Date","LastToDateSimple_Quarter":"Last Quarter To Date","LastToDateSimple_Week":"Last Week To Date","LastToDateSimple_Year":"Last Year To Date","LastToDate_Day":"Last {{count}} Days To Date","LastToDate_Day_plural":"Last {{count}} Days To Date","LastToDate_Month":"Last {{count}} Months To Date","LastToDate_Month_plural":"Last {{count}} Months To Date","LastToDate_Quarter":"Last {{count}} Quarters To Date","LastToDate_Quarter_plural":"Last {{count}} Quarters To Date","LastToDate_Week":"Last {{count}} Weeks To Date","LastToDate_Week_plural":"Last {{count}} Weeks To Date","LastToDate_Year":"Last {{count}} Years To Date","LastToDate_Year_plural":"Last {{count}} Years To Date","Last_Day":"Last {{count}} Days","Last_Day_plural":"Last {{count}} Days","Last_Hour":"Last {{count}} Hours","Last_Hour_plural":"Last {{count}} Hours","Last_Minute":"Last {{count}} Minutes","Last_Minute_plural":"Last {{count}} Minutes","Last_Month":"Last {{count}} Months","Last_Month_plural":"Last {{count}} Months","Last_Quarter":"Last {{count}} Quarters","Last_Quarter_plural":"Last {{count}} Quarters","Last_Second":"Last {{count}} Seconds","Last_Second_plural":"Last {{count}} Seconds","Last_Week":"Last {{count}} Weeks","Last_Week_plural":"Last {{count}} Weeks","Last_Year":"Last {{count}} Years","Last_Year_plural":"Last {{count}} Years","NextSimple_Day":"Next Day","NextSimple_Hour":"Next Hour","NextSimple_Minute":"Next Minute","NextSimple_Month":"Next Month","NextSimple_Quarter":"Next Quarter","NextSimple_Second":"Next Second","NextSimple_Week":"Next Week","NextSimple_Year":"Next Year","Next_Day":"Next {{count}} Days","Next_Day_plural":"Next {{count}} Days","Next_Hour":"Next {{count}} Hours","Next_Hour_plural":"Next {{count}} Hours","Next_Minute":"Next {{count}} Minutes","Next_Minute_plural":"Next {{count}} Minutes","Next_Month":"Next {{count}} Months","Next_Month_plural":"Next {{count}} Months","Next_Quarter":"Next {{count}} Quarters","Next_Quarter_plural":"Next {{count}} Quarters","Next_Second":"Next {{count}} Seconds","Next_Second_plural":"Next {{count}} Seconds","Next_Week":"Next {{count}} Weeks","Next_Week_plural":"Next {{count}} Weeks","Next_Year":"Next {{count}} Years","Next_Year_plural":"Next {{count}} Years","ToDate_Day":"Day-To-Date","ToDate_Month":"Month-To-Date","ToDate_Quarter":"Quarter-To-Date","ToDate_Week":"Week-To-Date","ToDate_Year":"Year-To-Date"}},"dateViewProperties":{"accent":"Accent","days":"Jours","default":"Defaut","error":"Erreur","months":"Mois","none":"Aucun","warning":"Avertissement","years":"Années"},"editors":{"nameBoolean":"Éditeur booléen","nameButton":"Bouton","nameDateRange":"Éditeur de plage de dates","nameDateTime":"Éditeur DateHeure","nameDateTimeRange":"DateTime Range Editor","nameDropdown":"Éditeur déroulant","nameHeading":"En-tête","nameList":"Éditeur de liste","nameMultivalue":"Éditeur multivaleur","nameNumber":"Éditeur de nombres","nameNumberRange":"Éditeur de plages de numéros","namePlainText":"Texte brut","nameText":"Éditeur de texte","nameTreeView":"Arborescence","textCheckRangeType":"Les deux paramètres doivent avoir le même type de données","textHeading":"Texte d\'en-tête","textNull":"Nul"},"fieldsProperties":{"addBtnText":"Ajouter","addBtnTitle":"Ajouter élément","closeBtnTitle":"Fermer","collectionIsEmpty":"La collection est vide","items":"éléments","showItem":"Afficher éléments"},"labels":{"removeDefaultValue":"Supprimer la valeur par défaut"},"msgString":{"controlNotSupportParameters":"Ce contrôle ne prend pas en charge les paramètres avec des valeurs disponibles définies","controlNotSupportSingle":"Ce contrôle ne prend pas en charge les paramètres à valeur unique","controlRequires":"Ce contrôle nécessite une liste de valeurs disponibles","notSupControl":"n\'est pa ssupporté par ce contrôle"},"parameters":{"labelBoolean":"Paramètre booléen","labelDateRange":"Paramètre de plage de dates","labelDateTime":"Paramètre DateHeure","labelDateTimeRange":"DateTimeRange Parameter","labelDropdown":"Paramètre déroulant","labelHierarchical":"Paramètre hiérarchique","labelList":"Paramètre de liste","labelMultivalue":"Paramètre multivaleur","labelNumber":"Paramètre de nombre","labelNumberRange":"Paramètre de plage de nombres","labelSingleLineParameter":"Paramètre une seule ligne"},"parametersPanel":{"headingEditParameter":"Modifier paramètre","headingEditRange":"Edit Range","headingEditable":"Visible","headingHidden":"Caché","titleEditParameter":"Modifier paramètres..."},"parametersText":"Paramètres","placeholderEmpty":"<Vide>","placeholderMultipleValues":"<Valeurs multiples>","properties":{"categories":{"advanced":"Advanced","appearance":"Appearance","binding":"Liaison","common":"Commun","details":"Détails","label":"Libellé","locationAndSize":"Emplacement & taille","preview":"Preview"},"labels":{"action":"Action","alignment":"Alignement","amount":"Amount","anchorDate":"Anchor Date","background":"Arrière-plan","binding":"Liaison","color":"Couleur","columns":"Colonnes","daysHeaderFormat":"Format d\'en-tête des jours","display":"Affichage","dropdown":"Menu déroulant","endpoint":"Endpoint","endpointStates":{"textFalse":"Exclude","textTrue":"Include"},"falseText":"\\"Faux\\" texte","fields":"Champs","from":"De","groupBy":"Grouper Par","height":"Hauteur","label":"Label","layout":"Mise en page","left":"Gauche","list":"Liste","max":"Max","maxRange":"Plage max","min":"Min","mode":"Mode","multiline":"Multiligne","offset":"Offset","pathString":"Chaîne de chemin","placeholder":"Espace réservé","placeholderFrom":"\\"From\\" Placeholder","placeholderTo":"\\"To\\" Placeholder","range":"Plage","ranges":"Ranges","recursive":"Recursif","roundInputToStep":"Arrondir l\'entrée au pas","showDefaultRanges":{"label":"Default Ranges","textFalse":"Hidden","textTrue":"Visible"},"slider":"Curseur","step":"Pas","strikeThrough":"Barré","text":"Texte","to":"A","top":"Haut","trueText":"\\"Vrai\\" texte","type":"Type","unit":"Unité","upDownEditor":"Éditeur Haut-Bas","value":"Valeur","viewMode":"Mode Vue","width":"Largeur"}},"propertiesText":"Propriétés","propertyGrid":{"btnCloseSearch":"Fermer","placeholderSearch":"entrez le nom de la propriété ici...","textEmptyList":"Sélectionnez un élément pour voir les propriétés","textNoCommonProperties":"Il n\'y a pas de propriétés communes","textUnknownProperty":"Propriété inconnue:"},"sideBar":{"collapse":"Réduire","expand":"Étendre"},"surface":{"btnAutoGenerate":"Génération automatique","btnGenerate":"Générer","emptySurfaceBlock":{"textDescriptionButtonAfter":"à partir des paramètres disponibles, ou redémarrez avec les commandes de la boîte à outils.","textDescriptionButtonBefore":"Vous pouvez","textDescriptionOne":"Ce rapport n\'a pas de vue des paramètres personnalisés.","textDescriptionTwo":"Vous pouvez le créer à l\'aide des commandes de la boîte à outils."},"messageBlock":{"textDescriptionButtonAfter":"editeurs pour eux.","textDescriptionButtonBefore":"Vous pouvez","textDescriptionOne":"Tous les paramètres de rapport n\'ont pas été liés."},"scrollBar":{"textMessagesOne":"Les paramètres auront cet aspect lorsqu\'ils seront affichés dans la barre latérale du visualiseur.","textMessagesTwo":"Vous pouvez modifier l\'ordre en faisant glisser les composants."}},"toolbar":{"btnDelete":"Supprimer","btnDuplicate":"Dupliquer","btnGenerateView":"Générer la vue","btnHighlightRequired":"Surbrillance requise","btnLayoutFreeForm":"Free Form","btnLayoutStack":"Stack","btnRemoveView":"Supprimer la vue","btnResetView":"Réinitialiser la vue"}}},{"lng":"fr","ns":"properties-RPX","resources":{"categories":{"appearance":"Appearance","behavior":"Behavior","border":"Border","common":"Common","data":"Data","design":"Design","format":"Format","layout":"Layout","misc":"Misc","page":"Page","pdf":"PDF","printing":"Printing","summary":"Summary","text":"Text","watermark":"Watermark"},"labels":{"MultiLine":"Multiline","alignment":"Alignment","anchorBottom":"Anchor Bottom","angle":"Angle","autoReplaceFields":"Auto Replace Fields","autoSize":"Auto Size","backColor":"Background Color","backColor2":"Background Color 2","backgroundPattern":"Background Pattern","backgroundStyle":"Background Style","barCodeStyle":"Style","barHeight":"Bar Height","barWidth":"Bar Width","border":{"color":"Border Color","style":"Border Style","width":"Border Width"},"calcFieldDefaultValue":"Default Value","calcFieldFormula":"Formula","calcFieldName":"Name","calcFieldType":"Type","calendar":"Calendar","canGrow":"Can Grow","canShrink":"Can Shrink","captionGrouping":"Caption Grouping","captionPosition":"Caption Position","characterSpacing":"Character Spacing","checkAlignment":"Check Alignment","checkSize":"Check Size","checkStyle":"Check Style","checkSumEnabled":"Checksum Enabled","checked":"Checked","colKeepTogether":"Column Group Keep Together","collate":"Collate","columnCount":"Column Count","columnDirection":"Column Direction","columnLayout":"Column Layout","columnSpacing":"Column Spacing","control":{"bottom":"Bottom","dataField":"Data Field","height":"Height","left":"Left","name":"Name","right":"Right","tag":"Tag","top":"Top","visible":"Visible","width":"Width"},"countNullValues":"Count Null Values","crossSectionBoxRadius":"Radius","culture":"Culture","dataField":"Data Field","description":"Description","distinctField":"Distinct Field","duplex":"Duplex","enabled":"Enabled","expressionErrorMessage":"Expression Error Message","fieldName":"Field Name","fontFamily":"Font Family","fontLinethrough":"Text Strikethrough","fontSize":"Font Size","fontStyle":"Font Style","fontUnderline":"Text Underline","fontWeight":"Font Weight","foreColor":"Fore Color","formatString":"Format String","gradientStyle":"Gradient Style","groupKeepTogether":"Group Keep Together","gutterMargin":"Gutter","height":"Height","htmlValue":"HTML Value","hyperLink":"Hyperlink","image":"Image","keepTogether":"Keep Together","lineColor":"Line Color","lineSpacing":"Line Spacing","lineStyle":"Line Style","lineWeight":"Line Weight","margin":"Margin","maxLength":"Max Length","maxPages":"Max Pages","minCondenseRate":"Min Condense Rate","mirrorMargins":"Mirror Margins","multiLine":"Multiline","name":"Name","newColumn":"New Column","newPage":"New Page","nwratio":"NW Ratio","orientation":"Orientation","outputFormat":"Output Format","padding":"Padding","paperHeight":"Paper Height","paperSize":"Paper Size","paperWidth":"Paper Width","paramUI":"Show Parameters UI","parameterDefaultValue":"Default Value","parameterFormat":"Format","parameterName":"Name","parameterParameterType":"Parameter Type","parameterPrompt":"Prompt","parameterPromptUser":"Prompt User","password":"Password","pictureAlignment":"Picture Alignment","printAtBottom":"Print At Bottom","printWidth":"Print Width","quietZoneBottom":"Quiet Zone Bottom","quietZoneLeft":"Quiet Zone Left","quietZoneRight":"Quiet Zone Right","quietZoneTop":"Quiet Zone Top","radius":{"default":"Default"},"readonly":"Readonly","repeatStyle":"Repeat Style","repeatToFill":"Repeat To Fill","reportName":"Report Name","required":"Required","rightToLeft":"Right to Left","rotation":"Rotation","roundingRadius":"Rounding Radius","script":"Script","scriptLanguage":"Script Language","shape":"Shape","sizeMode":"Size Mode","spellCheck":"Spellcheck","style":{"backColor":"Background Color","className":"Class Name","ddoCharSet":"Script","fontFamily":"Font Family","fontLinethrough":"Text Strikethrough","fontSize":"Font Size","fontStyle":"Font Style","fontUnderline":"Text Underline","fontWeight":"Font Weight","foreColor":"Color","kinsoku":"Kinsoku","shrinkToFit":"Shrink To Fit","styleName":"Name","textAlign":"Text Alignment","textJustify":"Text Justify","verticalAlign":"Vertical Alignment","verticalText":"Vertical Text","wrapMode":"Wrap Mode"},"styles":"Styles","summaryFunc":"Summary Func","summaryGroup":"Summary Group","summaryRunning":"Summary Running","summaryType":"Summary Type","supplementBarHeight":"Supplement Bar Height","supplementCaptionPosition":"Supplement Caption Position","supplementDataField":"Supplement Data Field","supplementSpacing":"Supplement Spacing","supplementText":"Supplement Text","tabIndex":"Tab Index","tag":"Tag","text":"Text","title":"Title","underlayNext":"Underlay Next","userData":"User Data","version":"Version","visible":"Visible","watermarkAlignment":"Alignment","watermarkImage":"Image","watermarkPrintOnPages":"Print On Pages","watermarkSizeMode":"Size Mode","x1":"X1","x2":"X2","y1":"Y1","y2":"Y2"}}},{"lng":"fr","ns":"propertyDescriptors","resources":{"categories":{"action":"Action","appearance":"Apparence","availableValues":"Valeurs disponibles","background":"Arrière-plan","backgroundAndBorders":"Arrière-plan et bordures","bar":"Paramètres de la barre","border":"Bordure","borderColor":"Couleur bordure","borderRoundingRadius":"Rayon d\'arrondi bordure","borderStyle":"Style bordure","borderWidth":"Largeur de bordure","borders":"Bordures","column":"Colonne","common":"Commun","configurations":"Configurations","content":"Contenu","data":"Données","dataLabel":"Liobellé données","dataLabelText":"Texte libellé de données","defaultValue":"Valeur par défaut","dimensions":"Dimensions","documentMap":"Carte document","dvchartLabelBorder":"Bordure libellé","dvchartLabelLine":"Ligne libellé","dvchartLabelText":"Texte libellé","dvchartLegend":"Légende","dvchartLegendBackground":"Arrière-plan légende","dvchartLegendBorder":"Bordure légende","dvchartLegendText":"Texte légende","dvchartLegendTitle":"Titre légende","dvchartPlotColorEncodings":"Encodage - couleur","dvchartPlotConfig":"Configuration","dvchartPlotEncodings":"Encodages - communs","dvchartPlotShapeEncodings":"Encodage - forme","dvchartPlotSizeEncodings":"Encodage - taille","dvchartPlotStyle":"Style","dvchartPlotSymbols":"Symboles","fields":"Champs","fillStyle":"Style remplissage","font":"Police","general":"Général","grid":"Lignes grille","group":"Groupe","input":"Entrée","international":"International","labelStyle":"Style libellé","labels":"Libellés","layout":"Mise en page","line":"Ligne","majorGrid":"Grille majeure","majorTicks":"Coches majeures","margins":"Marges","marker":"Marqueur","minorGrid":"Grille mineure","minorTicks":"coches mineures","misc":"Divers","noData":"Aucune donnée","options":"Options","pageSize":"Taille page","preview":"Aperçu","range":"Plage","referenceLine":"Ligne référence","scale":"Échelle","seriesLineStyle":"Style ligne de série","settings":"Paramètres","staticMembers":"Membres statiques","symbology":"Symbologie","tableFooter":"Base de page","tableHeader":"En-tête","targetDevice":"Appareil cible","targetStyle":"Style cible","text":"Texte","threeDProperties":"Propriétés 3D","tickStyle":"Style coche","title":"Titre","userSort":"Tri utilisateur","valueStyle":"Style valeur","visibility":"Visibilité"},"labels":{"action":{"applyParameters":"Appliquer paramètres","jumpToBookmark":"Aller au signet","jumpToReport":"Aller au rapport","jumpToUrl":"Aller à l\'URL","parameters":"Paramètres","slice":"Tranches"},"bandedList":{"canGrow":"Peut croître","canShrink":"Peut rétrécir","consumeWhiteSpace":"Utiliser les espaces blancs","preventOrphanedFooter":"Empêcher pied de page orphelin","preventOrphanedHeader":"Empêcher en-tête orphelin","printAtBottom":"Imprimer en bas","repeatOnNewPage":"Répéter sur nouvelle page"},"barcode":{"aztecOptions":{"encoding":"Encodage","errorCorrection":"Correction erreur","layers":"Calques"},"barHeight":"Hauteur barre","captionGrouping":"Regroupement libellés","captionLocation":"Emplacement libellé","checksum":"Somme de contrôle","code49Options":{"groupNumber":"Numéro de groupe","grouping":"Regroupement"},"dataMatrixOptions":{"ecc000_140SymbolSize":"Taille symbole Ecc000_140","ecc200EncodingMode":"Mode encodage Ecc200","ecc200SymbolSize":"Taille symbole Ecc200","eccMode":"Mode Ecc","encoding":"Encodage","encodingMode":"Mode encodage","fileIdentifier":"Identificateur de fichier","structureNumber":"Numéro de structure","structuredAppend":"Ajout structuré","symbolSize":"Taille symbole"},"ean128Fnc1Options":{"barAdjust":"Ajustement barre","moduleSize":"Taille module","resolution":"Dpi"},"gs1CompositeOptions":{"type":"Type composite","value":"Valeur composite"},"gs1QrCodeOptions":{"encoding":"Encodage","errorLevel":"Niveau d\'erreur","mask":"Masque","version":"Version"},"invalidBarcodeText":"Texte code-barre incorrect","maxiCodeOptions":{"mode":"Mode"},"microPdf417Options":{"compactionMode":"Mode compactage","fileId":"Id Fichier","segmentCount":"Nombre de segments","segmentIndex":"Index segment","version":"Version"},"microQrCodeOptions":{"encoding":"Encodage","errorLevel":"Niveau d\'erreur","mask":"Masque","version":"Version"},"narrowBarWidth":"Largeur barre étroite","nwRatio":"Ratio NW","nwRatio_help":{"text":"Également connue sous le nom de dimension N, il s\'agit d\'une valeur définissant le multiple du rapport entre les barres étroites et larges dans les symbologies qui contiennent des barres de seulement deux largeurs."},"pdf417Options":{"columns":"Colonnes","errorCorrectionLevel":"Niveau de correction d\'erreur","rows":"Lignes","type":"Type PDF417"},"qrCodeOptions":{"connection":"Connexion","connectionNumber":"Numéro de connexion","encoding":"Encodage","errorLevel":"Niveau d\'erreur","mask":"Masque","model":"Modèle","version":"Version"},"quietZone":"Zone de silence","rotation":"Rotation","rssExpandedStacked":{"rowCount":"Nombre de ligne"},"supplementOptions":{"barHeight":"Hauteur de la barre de supplément","captionLocation":"Emplacement du libellé du supplément","spacing":"Espacement du supplément","value":"Valeur du supplément"},"symbology":"Type","value":"Valeur"},"border":{"bottom":"Bas","color":"Couleur","default":"Defaut","left":"Gauche","right":"Droite","style":"Style","top":"Haut","width":"Largeur"},"bullet":{"bestValue":"Meilleure valeur","interval":"Intervalle","labelFontColor":"Couleur police","labelFontFamily":"Police","labelFontSize":"Taille police","labelFontStyle":"Style","labelFormat":"Format libellé","orientation":"Orientation","range1Boundary":"Limite de plage1","range2Boundary":"Limite de plage2","showLabels":"Afficher libellés","targetLineColor":"Couleur","targetLineWidth":"Largeur ligne","targetShape":"Forme","targetValue":"Valeur cible","tickMarks":"Marques","ticksLineColor":"Couleur","ticksLineWidth":"Largeur ligne","value":"Valeur","valueColor":"Couleur valeur","worstValue":"Pire valeur"},"checkBox":{"checkAlignment":"Vérifier alignement","checked":"Vérifié","text":"Texte"},"container":{"canGrow":"Peut croître","consumeWhiteSpace":"Utiliser les espaces blancs","gridMode":"Mode grille","linkToChild":"Lien vers l\'enfant","newPage":"Nouvelle page","overflow":"Débordement","pageBreak":"Saut de page"},"contentPlaceHolder":{"consumeWhiteSpace":"Consume White Space","text":"Text"},"dashboardSection":{"displayName":"Display Name"},"data":{"dataElementName":"Nom de l\'élément","dataElementOutput":"Sortie de l\'élément","dataElementStyle":"Style de l\'élément"},"dataRegion":{"dataSetName":"Nom Dataset","dataSetParameters":"Paramètres Dataset","dataSetParameters_help":{"text":"La définition d\'un paramètre dans la propriété Paramètres DataSet permet d\'ajouter des relations pour afficher des données dans des régions de données imbriquées liées à différents jeux de données."},"filters":"Filtres","newPage":"Nouvelle page","newSection":"Nouvelle section","noRowsMessage":"Message","overflowName":"Nom débordement","pageBreak":"Saut de page","repeatToFill":"Répéter pour remplir","sortExpressions":"Expressions de tri","throwIfPlaceHoldersEmpty":"Lancer si les espaces sont vides"},"dataSet":{"accentSensitivity":"Sensible aux accents","boundFields":"Champs liés","calculatedFields":"Champs calculés","caseSensitivity":"Sensible à la casse","collation":"Examen","commandType":"Type Command","fields":"Champs","filters":"Filtres","kanatypeSensitivity":"Sensible au Kanatype","name":"Nom","parameters":"Paramètres","query":"Requête","widthSensitivity":"Sensible à la largeur"},"dataSetParameter":{"name":"Nom","value":"Valeur"},"dataVisualizer":{"colorScale":{"maximum":"Maximum","maximumColor":"Couleur maximum","middle":"Milieu","middleColor":"Couleur milieu","minimum":"Minimum","minimumColor":"Couleur minimum","useMiddleColor":"Utiliser couleur milieu","value":"Valeur"},"dataBar":{"alternateColor":"Alterner couleur","color":"Coleur","maximum":"Maximum","minimum":"Minimum","useAlternateColor":"Utiliser alterner couleur","useAlternateColor_help":{"text":"Si activé, \'Alterner couleur\' sera utilisé lorsque \'Valeur\' est inférieure à \'Valeur zéro\'"},"value":"Valeur","zeroValue":"Valeur zéro"},"gradient":{"color1":"Couleur 1","color2":"Couleur 2","gradientType":"Type dégradé"},"hatch":{"color1":"Couleur 1","color2":"Couleur 2","hatchStyle":"Style hachurage"},"iconSet":{"icon1Value":"Valeur icône 1","icon2Value":"Valeur icône 2","icon3Value":"Valeur icône 3","icon4Value":"Valeur icône 4","icon5Value":"Valeur icône 5","iconSet":"Jeu d\'icônes"},"rangeBar":{"color":"Couleur","displayProgressIndicator":"Afficher indicateur de progression","length":"Longueur","maximum":"Maximum","minimum":"Minimum","progressIndicatorColor":"Couleur indicateur de progression","progressIndicatorLength":"Longueur indicateur de progression","startingValue":"Valeur de départ"},"visualizerType":"Type de visualiseur"},"dimensions":{"bottom":"Bas","endPointX":"Point final X","endPointY":"Point final Y","fixedHeight":"Hauteur fixe","fixedSize":"Taille fixe","fixedWidth":"Largeur fixe","height":"Hauteur","left":"Gauche","location":"Emplacement","right":"Droite","size":"Taille","startPointX":"Point de départ X","startPointY":"Point de départ Y","top":"Haut","width":"Largeur"},"dvChartPlotCustomLabels":{"offsetX":"Décalage X","offsetY":"Décalage Y","text":"Texte"},"dvChartPlotPointers":{"end":"Fin","needlePinWidth":"Largeur de la broche d\'aiguille","needleWidth":"Largeur d\'aiguille"},"dvPlotOverlays":{"aggregateType":"Type d\'agrégat","axis":"Axe","backgroundColor":"Couleur de fond","backwardForecastPeriod":"Période de prévision rétrospective","detailLevel":"Niveau détail","display":"Afficher","end":"Fin","field":"Champ","forwardForecastPeriod":"Période de prévision à terme","intercept":"Intercepter","legendLabel":"Loibellé légende","lineColor":"Couleur ligne","lineStyle":"Style ligne","lineWidth":"Largeur ligne","name":"Nom","order":"Tri","period":"Période","start":"Début","type":"Type","value":"Valeur"},"dvchart":{"bar":{"bottomWidth":"Largeur Bas","neckHeight":"Hauteur cou","overlap":"Overlap","topWidth":"Largeur Haut","width":"Largeur"},"customPaletteColors":"Palette de couleurs personnalisée","legendHidden":"Caché","legendOrientation":"Orientation","legendPosition":"Position","legendWrapping":"Entourage","palette":"Palette","plotTemplate":"Modèle de jauge","plotTemplateDropdown":"Selectionner...","plots":"Jauges"},"dvchartAxis":{"axisType":"Type axe","dateMode":"Mode date","gridLinesStyle":"Style ligne de grille","height":"Hauteur","labelAngle":"Angle libellé","labelField":"Champ libellé","lineStyle":"Style ligne","logBase":"Base journal","majorInterval":"Intervalle majeur","max":"Max","maxHeight":"Hauteur maximum","maxWidth":"Largeur maximum","min":"Min","minorInterval":"Intervalle mineur","origin":"Origine","overlappingLabels":"Étiquettes superposées","plots":"Jauges","position":"Position","reversed":"Inversé","scale":"Type d\'échelle","showGridLines":"Affiche lignes grille","showMajorGridLines":"Lignes grille majeures","showMinorGridLines":"Lignes grills mineures","tickMarks":"Coches","tickSize":"Taille coche","tickStyle":"Style coche","title":"Titre","visible":"Visible","width":"Largeur"},"dvchartEncoding":{"aggregateType":"Agrégat","excludeNulls":"Exclure valeurs nulles","fieldType":"Type champ","fieldValue":"Valeur","group":"Groupe","sort":"Sens tri","sortingAggregate":"Agrégat de tri","sortingField":"Expression tri","target":"Cible","templateKey":"Clef de modèle","value":"Valeurs"},"dvchartHeaderFooter":{"caption":"Libellé","height":"Auteur"},"dvchartLegend":{"hidden":"Caché","iconColor":"Couleur icône","maxHeight":"Hauteur max","maxWidth":"Largeur max","orientation":"Orientation","position":"Position","ranges":"Plages","title":"Titre"},"dvchartPlot":{"action":"Atype d\'action","axisMode":"Mode Axe","bar":"Lignes barre","category":"Categorie","categorySort":"Sens tri catégories","categorySortingAggregate":"Agrégat de tri par catégorie","categorySortingField":"Expression de tri de catégorie","clippingMode":"Mode d\'écrêtage","color":"Couleur","colorAggregate":"Agrégat couleur","customLabels":"Libellés jauges","details":"Détails","gaugeRanges":"Gauge Ranges","innerRadius":"Rayon intérieur","lineAspect":"Aspect de la ligne","lineColor":"Couleur ligne","lineStyle":"Style ligne","lineWidth":"Largeur ligne","offset":"Décalage","opacity":"Opacité","overlays":"Surcouches","plotStyle":"Style jauge","pointers":"Pointeur jauge","radial":"Radial","rules":"Règles","shape":"Forme","shapeAggregate":"Agrégat forme","showNulls":"Afficher valeurs nulles","showValuesNamesInLegend":"Afficher noms des valeurs dans la légende","size":"Taille","sizeAggregate":"Agrégat taille","startAngle":"Angle de début","swapAxes":"Échanger axes","sweep":"Balayer","symbolBackgroundColor":"Couleur fond","symbolOpacity":"Opacité symbole","symbolShape":"Forme symbole","symbolStyle":"Style symbole","symbols":"Afficher symboles","text":"Texte","textBackgroundColorStyle":"Couleur de fond","textConnectingLine":"Ligne connectée","textLinePosition":"Position ligne texte","textOffset":"Décalage","textOverlappingLabels":"Étiquettes superposées","textPosition":"Position texte","textTemplate":"Modèle","texts":"Encodage texte","tooltip":"Info-bulle","tooltipTemplate":"Modèle info-bulle","type":"Type jauge","unpivotData":"Ne pas pivoter les données","values":"Valeurs"},"dvchartPlotArea":{"axes":"Axes"},"dvchartPlotRules":{"condition":"Condition","name":"Nom","ruleProperties":"Propriétés de la règle","targetProperty":"Propriété cible","valueRuleProperties":"Valeur"},"dvchartValueEncoding":{"field":{"caption":"Libellé","close":"Fermé","high":"Haut","key":"Clef","low":"Bas","lower":"Inférieur","open":"Ouvert","upper":"supérieur","value":"Valeur"}},"empty":"<Vide>","filter":{"filterExpression":"Expression de filtre","filterValues":"Valeur de filtre","operator":"Operateur","value":"Valeur"},"font":{"fontFamily":"Police","fontSize":"Taille police","fontStyle":"Style","fontWeight":"Gras"},"formattedText":{"encodeMailMergeFields":"Encoder champs de fusion","html":"Html","mailMergeFields":"fusionner champs"},"fplPage":{"orientation":"Orientation","size":"Taille"},"fplReport":{"fixedElementName":"Nom délément de page fixe","fixedElementOutput":"Sortie d\'élément de page fixe"},"group":{"dataCollectionName":"Nom collection de données","dataElementName":"Nom élément de données","dataElementOutput":"Sortie élément de données","documentMapLabel":"Libellé","filters":"Filtres","groupExpressions":"Expressions de groupe","name":"Nom","newPage":"Nouvelle page","newSection":"Nouvelle section","pageBreak":"Saut de page","pageBreakDisabled":"Saut de page désactivé","parent":"Parent","printFooterAtBottom":"Imprimer le pied de page en bas"},"image":{"backgroundRepeat":"Répéter fond","horizontalAlignment":"Alignement horizontal","imageLabel":"Image","mimeType":"Type MIME","sizing":"Dimensionnement image","source":"Source","value":"Valeur","verticalAlignment":"Alignement vertical"},"inputField":{"checkSize":"Vérifier taille","checkStyle":"Vérifier style","checked":"Vérifié","fieldName":"Nom champ","inputType":"Type","maxLength":"Longueur maximum","multiline":"Multiligne","password":"Mot de passe","readonly":"Lecture seule","required":"Requis","spellCheck":"Vérification orthographique","tabIndex":"Tab Index","value":"Valeur"},"layer":{"designerDataFieldVisible":"Disponibilité du sélecteur de champ","designerLock":"Vérrouillage du Designer","designerTransparency":"Transparence du Designer","designerVisible":"Visibilité du Designer","name":"Nom","targetDevice":{"all":"Tout","export":"Export","paper":"Papier","screen":"Écran"}},"line":{"endPoint":"Point final","lineColor":"Couleur ligne","lineStyle":"Style ligne","lineWidth":"Largeur ligne","startPoint":"Point de départ"},"list":{"consumeWhiteSpace":"Utiliser les espaces blancs","dataInstanceElementOutput":"Sortie d\'élément d\'instance de données","dataInstanceName":"Nom de l\'instance de données","gridMode":"Mode grille","growDirection":"Sens de croissance","rowsOrColumnsCount":"Nombre de lignes ou de colonnes"},"margins":{"bottom":"Bas","left":"Gauche","right":"Droite","top":"Haut"},"multipleValues":"<Valeurs multiples>","overflowPlaceHolder":{"overflowName":"Nom du débordement"},"padding":{"bottom":"Bas","left":"Gauche","right":"Droite","top":"Haut"},"pageSection":{"printOnFirstPage":"Imprimer sur la première page","printOnLastPage":"Imprimer sur la dernière page"},"parameter":{"omit":"Omettre","parametername":"Nom du paramètre","parameters":"Paramètres","value":"Valeur"},"partItem":{"library":"Library","reportPart":"Report Part"},"report":{"author":"Auteur","collapseWhiteSpace":"Réduire l\'Espace Blanc","collateBy":"Assembler par","columnSpacing":"Espacement colonnes","columns":"Colonnes","consumeContainerWhitespace":"Utiliser les espaces blancs","description":"Description","displayType":"Type d\'affichage","embeddedImages":"IMages intégrées","language":"Lange","layers":"Calques","layoutOrder":"Ordre de mise en page","level":"Niveau","levels":"Niveaux","marginsSizes":"Marges","marginsStyle":"Style","nameMasterReport":"Master Report","numberingStyle":"Style numérotation","pageHeight":"Hauteur de page","pageOrientation":"Orientation de page","pageSize":"Taille de page","pageWidth":"Largeur de page","reportPart":{"description":"Description","displayName":"Display Name","partProperties":"Properties","properties":{"category":"Category","defaultValue":"Default Value","description":"Description","displayName":"Display Name","type":"Type"},"reportItemName":"Report Item Name","sizeMode":"Size Mode"},"reportParts":"Report Parts","sizeType":"Type taille","source":"Source","startPageNumber":"N° page de démarrage","theme":"Thème","themes":"Thèmes"},"reportItem":{"accessibleDescription":"Accessible Description","actionType":"Type","bookmark":"Signet","keepTogether":"Garder ensemble","label":"Libellé","layerName":"Nom calque","name":"Nom","pageName":"Nom de page","style":"Style","toolTip":"Info-bulle","visibility":"Visibilité","zIndex":"Index Z"},"reportParameter":{"allowBlankValue":"Autoriser valeur vide","allowNullValue":"Autoriser valeur nulle","dataSetName":"Nom Dataset","dataType":"Type données","hidden":"Caché","label":"Libellé","labelField":"Champ libellé","multiline":"Multiligne","multivalue":"Multivaleur","name":"Nom","orderBy":"Trier par","parameterFormat":"Format","parameterValues":"Valeurs paramètre","prompt":"Prompt","selectAllValue":"Sélectionner toutes les valeurs","selectAllValue_help":{"text":"Si spécifié, détermine la valeur à transmettre à la valeur du paramètre au cas où l\'indicateur \\"Sélectionner tout\\" est défini pour le paramètre multivaleur."},"value":"Valeur","valueField":"Champ valeur","values":"Valeurs"},"reportSlicer":{"allowBlankValue":"Autoriser valeur vide","allowNullValue":"Autoriser valeur nulle","dataSetName":"Nom Dataset","multivalue":"Multivaleur","name":"Nom"},"richtext":{"canGrow":"Peut croître","markup":"Type balisage","value":"Valeur"},"roundingRadius":{"bottomLeft":"Bas Gauche","bottomRight":"Bas Droite","default":"Defaut","label":"Rayon arrondi","topLeft":"Haut Gauche","topRight":"Haut Droite"},"sparkline":{"fillColor":"Couleur de remplissage","gradientEndColor":"Couleur fin de dégradé","gradientStyle":"Type dégradé","lineColor":"Couleur ligne","lineWidth":"Largeur ligne","markerColor":"Couleur marqueur","markerVisibility":"Visibilité marqueur","maximumColumnWidth":"Largeur maximum","rangeFillColor":"Couleur de remplissage","rangeGradientEndColor":"Couleur fin de dégradé","rangeGradientStyle":"Type dégradé","rangeLowerBound":"Limite inférieure","rangeUpperBound":"Limite supérieure","rangeVisibility":"Visibilité","seriesValue":"Valeur séries","sparklineType":"Type Sparkline"},"style":{"angle":"Angle","backgroundAndBorders":"Bordures et arrière-plan","backgroundColor":"Couleur","backgroundGradientEndColor":"Couleur fin de dégradé","backgroundGradientType":"Type dégradé","backgroundImage":"Image","border":"Bordure","calendar":"Calendrier","characterSpacing":"Espacement caractères","color":"Couleur","corner":"Coin","direction":"Direction","font":"Police","format":"Format","headingLevel":"Niveau de titre","language":"Langue","lineHeight":"Hauteur ligne","lineSpacing":"Espacement ligne","maxLevel":"Niveau maximum","minCondenseRate":"Condenssation caractères","minCondenseRate_help":{"text":"Spécifie le taux minimal de réduction horizontale du texte en pourcentages. Doit être compris entre 10 et 100."},"numeralLanguage":"Langage numérique","numeralVariant":"Variante numérique","padding":"Marge interieure","shapeStyle":"Style forme","shrinkToFit":"Réduire pour s\'adapter","textAlign":"Alignement horizontal","textDecoration":"Souligné","textIndent":"Indentation texte","textJustify":"Justification","unicodeBiDi":"Unicode BiDi","uprightInVerticalText":"Orientation texte vertical","verticalAlign":"Alignement vertical","wrapMode":"Coupure texte","writingMode":"Mode écriture"},"subreport":{"inheritStyleSheet":"Feuille de style héritée","mergeTransactions":"Fusionner transactions","reportName":"Nom rapport","reportParameters":"Paramètres rapport","substituteThemeOnSubreport":"Remplacer thème du sous-rapport"},"table":{"autoWidth":"Largeur auto","detailsDataCollectionName":"Nom collection détails données","detailsDataElementName":"Nom élément détails données","detailsDataElementOutput":"Sortie élément détails sortie","keepTogether":"Garder ensemble","preventOrphanedFooter":"Empêcher pied de page orphelin","printAtBottom":"Imprimer en base","repeatBlankRows":"Répéter lignes vide","repeatOnNewPage":"Répéter sur nouvelle page"},"tablix":{"frozenColumns":"Colonnes gelées","frozenRows":"Lignes gelées","groupsBeforeRowHeaders":"Groupes avant en-têtes de ligne","layoutDirection":"Sens mise en page","repeatColumnHeaders":"Répéter en-têtes de colonne","repeatRowHeaders":"Répéter en-têtes de ligne"},"tablixBodyCell":{"autoMergeMode":"Mode fusion automatique","autoMergeMode_help":{"text":"Indique si deux ou plusieurs cellules continues d\'une colonne ayant le même contenu doivent être fusionnées. Cela ne fonctionnera que pour la cellule ZoneDeTexte."}},"tablixMember":{"groupEnabled":"Activé","keepWithGroup":"Garder avec le groupe","repeatOnNewPage":"Répéter sur nouvelle page"},"textbox":{"canGrow":"Peut croître","canShrink":"Peut rétrécir","initialToggleState":"Développement initial","value":"Valeur"},"tocLevel":{"displayFillCharacters":"Afficher caractère de remplissage","displayPageNumber":"Afficher numéro de page","fillCharacter":"Caractère de remplissage","label":"Libellé"},"userSort":{"sortExpression":"Expression de tri","sortExpressionScope":"Appliquer dans","sortTarget":"Cible de tri"},"visibility":{"hidden":"Caché","toggleItem":"Ouv. / Ferm. élément"}}}},{"lng":"fr","ns":"propertyEditors-RPX","resources":{"image":{"textChange":"Change...","textPick":"Pick..."},"statusWrapper":{"btnReset":"Reset","titleDefault":"","titleError":"Invalid","titleInherited":"Inherited from Parent Style","titleModified":"Modified"},"style":{"drillCaptionStyle":"Style","textEdit":"Edit"}}},{"lng":"fr","ns":"propertyEditors","resources":{"boolean":{"textFalse":"Faux","textTrue":"Vrai","textUndefined":"Non défini"},"chartComplexEncodingFieldCollectionEditor":{"prefix":"Champ"},"chartSimpleEncodingFieldCollectionEditor":{"captionHeader":"Libellé","keyHeader":"Clef","valueHeader":"Valeur"},"collection":{"btnAdd":"Ajouter","textEmpty":"La collection est vide","textItemsCount":"{{count}} élément","textItemsCount_plural":"{{count}} élements","titleAdd":"Ajouter élément","titleClose":"Fermer","titleDelete":"Supprimer","titleShowItems":"Afficher éléments"},"colorDropdown":{"btnColorPicker":"Autres couleurs","btnPalettes":"Palettes","btnWebColors":"Couleurs web","headingStandard":"Couleurs standard","headingTheme":"Couleurs du thème","labelHex":"Hex","labelHue":"Teinte","labelLightness":"Luminosité","labelSaturation":"Saturation","themeColors":{"titleBase":"{{colorKey}}","titleDarker25":"{{colorKey}} - 25% plus sombre","titleDarker50":"{{colorKey}} - 50% plus sombre","titleLighter25":"{{colorKey}} - 25% plus clair","titleLighter50":"{{colorKey}} - 50% plus clair","titleLighter75":"{{colorKey}} - 75% plus clair"}},"common":{"textEmpty":"<Vide>","textExpression":"<Expression>","textMultipleValues":"<Valeurs multiples>","textNone":"<Aucun>","titleCollapse":"Réduire","titleExpand":"Développer"},"dataSetFieldCollection":{"dataFieldHeader":"Champ donnée","dataFieldPlaceholder":"<Champ donnée>","fieldHeader":"Nom champ","fieldPlaceholder":"<Nom>","valueHeader":"Valeur","valuePlaceholder":"<Valeur>"},"dataSetParameterCollection":{"nameHeader":"Nom paramètre","namePlaceholder":"<Nom>","valueHeader":"Valeur","valuePlaceholder":"<Valeur>"},"dataSetQuery":{"placeholder":"entrez la requête ici..."},"dvcartLegendRangeOptionsCollection":{"titleHeader":"Titre","titlePlaceholder":"<Titre>","toHeader":"Vers"},"dvchartEncodingCollection":{"aggregate":{"prefix":"Agrégat","propertiesTitle":"Propriétés d\'agrégat"},"color":{"valuesName":"Afficher les noms des valeurs dans la légende"},"details":{"prefix":"Détails","propertiesTitle":"Propriétés de détail"},"fieldPlaceholder":"<Clef>","text":{"prefix":"Texte","propertiesTitle":"Propriétés de texte"},"value":{"prefix":"Valeur","propertiesTitle":"Propriétés de valeur"},"valuePlaceholder":"<Valeur>"},"dvchartPlotTemplate":{"textSelect":"Choisir..."},"dvchartRuleProperties":{"headingTargetProperty":"Propriété cible","headingValueProperty":"Valeur"},"dvchartTemplate":{"custom":"<Personnalisé>"},"format":{"$locale":"fr-FR","$localeCurrency":"EUR","currency":"Devise","custom":"(Personnalisé)","customFormatMask":"(###) ###-####","decimal":"Décimal","default":"<Defaut>","digitsLabel":"Chiffres","fixedPoint":"Point fixe","fullDateShortTime":"Date complète/heure courte","general":"Général","generalDateLongTime":"Date générale/heure longue","generalDateShortTime":"Date générale/heure courte","hexadecimal":"Hexadecimal","longDate":"Date longue","longTime":"Heure longue","monthDay":"Jour du mois","number":"Nombre","percent":"Pourcentage","scientific":"Scientifique","shortDate":"Date courte","shortTime":"Heure courte","yearMonth":"Mois de l\'année"},"image":{"btnDatabase":"Base de données","btnEmbedded":"Intégré","btnShared":"Partagé","textLoad":"Chargement...","textNoDataFieldsFound":"aucun champ de données trouvé","textNoImagesFound":"Aucune image trouvée","titleRemove":"Supprimer \'{{name}}\'..."},"layerCollection":{"propertiesTitle":"Propriétés de calque: {{layerName}}"},"mailMergeFieldsCollection":{"nameHeader":"Nom champ","namePlaceholder":"<Nom>","valueHeader":"Valeur","valuePlaceholder":"<Valeur>"},"marginsSizes":{"custom":"(Personnalisé)"},"pageSize":{"custom":"(Personnalisé)"},"palette":{"customPaletteLabel":"<Personnalisé>","extraPalettesHeader":"Palettes Theme","standardPalettesHeader":"Palettes Standard"},"parameterCollection":{"titleProperties":"Propriétés de paramètre"},"parameterValuesOrder":{"ascending":"Ascendant","descending":"Descendant"},"picker":{"btnDataVisualizer":"Visualiseur de données...","btnExpression":"Expression...","btnPickData":"Choisir des données...","btnReset":"Réinitialiser","headingParameters":"Paramètres","titleDataBinding":"Liaison de données","warnings":{"groupingByAggregate":"le regroupement par attributs agrégés est déconseillé","groupingIsDiscouraged":"le regroupement par cet attribut est déconseillé","masterReportAttributes":"only attributes used in the master report are available for content reports"}},"reportParameter":{"labelFromQuery":"A partir de la requête","labelNonQueried":"Non requêté","labelSource":"Source","placeholderEmpty":"<Vide>","placeholderLabel":"Libellé","placeholderValue":"Valeur"},"reportPartPropertiesCollection":{"propertiesTitle":"Property: {{reportPartName}}"},"reportPartsCollection":{"propertiesTitle":"Report Part: {{reportPartName}}"},"reports":{"textLoading":"Chargement...","textLoadingError":"Erreur {{status}}: {{statusText}}"},"simple":{"backgroundColor":{"label":"Couleur","title":"Couleur arrière plan"},"borders":{"borderColor":{"label":"Couleur","title":"Couleur bordure"},"borderStyle":{"label":"Style","title":"Style bordure"},"borderWidth":{"label":"Largeur","title":"Largeur bordure"},"borders":"Bordures","sides":{"all":"Tous","bottom":"Bas","left":"Gauche","reset":"Réinitialiser","right":"Droit","top":"Haut"}},"common":{"textExpressionCompact":"<𝑓>"},"font":{"fontFamily":{"label":"Police","title":"Police"},"fontSize":{"label":"Taille","title":"Taille police"},"fontStyle":{"label":"Style","title":"Style"},"fontWeight":{"label":"Gras","title":"Gras"},"textColor":{"label":"Couleur","title":"Couleur texte"},"textDecoration":{"label":"Décoration","title":"Souligné"}}},"subreport":{"parameter":"Paramètre","parameterNameHeader":"Nom","parameterNamePlaceholder":"<Nom>","parameterValueHeader":"Valeur","parameterValuePlaceholder":"<Valeur>"},"toggleState":{"textCollapsed":"Réduit","textExpanded":"Développé"},"validationErrors":{"expression":{"disabledFields":"Attention : Il est interdit d\'utiliser le type de token \'Champs\' dans cette expression (dans \'{{token}}\' {{positionInfo}})","disabledReportItems":"Attention : Il est interdit d\'utiliser le type de jeton \'Élements de rapport\' dans cette expression (dans \'{{token}}\' {{positionInfo}})","errorPosition":"à la ligne {{line}}, colonne {{column}}","parseError":"Erreur de syntaxe: il n\'est pas possible de convertir cette expression de syntaxe d\'interpolation en syntaxe rdl","syntaxError":"Erreur de syntaxe: jeton inattendu \'{{token}}\' {{positionInfo}}","unknown":"Erreur inconnue \'{{positionInfo}}\'","unknownField":"Attention: nom de champ inconnu dans \'{{token}}\' {{positionInfo}}","unknownFunction":"Attention : nom de fonction inconnu \'{{token}}\' {{positionInfo}}","unknownParameter":"Attention: nom de paramètre inconnu dans \'{{token}}\' {{positionInfo}}","unknownReportItem":"Avertissement: nom d\'élément de rapport inconnu dans \'{{token}}\' {{positionInfo}}","unknownThemeImage":"Avertissement: nom d\'image de thème inconnu dans \'{{token}}\' {{positionInfo}}","warning":"Attention: jeton inconnu \'{{token}}\' {{positionInfo}}"}}}},{"lng":"fr","ns":"reportItems","resources":{"Page":"Page","Report":"Rapport","bandedList":"Liste groupée","bandedListDetails":"Détails","bandedListFooter":"Pied","bandedListGroup":"Groupe","bandedListHeader":"En-tête","barcode":"Code barre","body":"Corps","bullet":"Puce","checkbox":"Case à cocher","container":"Conteneur","contentPlaceholderText":"Set the Text property to tell Content Report authors what to add here","contentplaceholder":"Content Placeholder","continuousSection":"Section continue","dashboard":"Dashboard","dashboardPageFooter":"Pied","dashboardPageHeader":"En-tête","dashboardSection":"Section","dvchart":"Graphique","dvchartAggregateEncoding":"Encodage agrégat","dvchartAxis":"Axe","dvchartCategoryEncoding":"encodage catégorie","dvchartColorLegend":"Légende - Couleur","dvchartDetailsEncoding":"Encodage détails","dvchartEncodingValue":"Valeur encodage","dvchartFooter":"Pied","dvchartGlobalLegend":"Légende globale","dvchartHeader":"En-tête","dvchartLegend":"Légende","dvchartPlot":"Parcelle","dvchartPlotArea":"Zone de parcelle","dvchartPlotCustomLabel":"Libellé jauge","dvchartPlotPointer":"Pointeur jauge","dvchartShapeLegend":"Légende - Forme","dvchartSizeLegend":"Légende - Taille","dvchartTextEncoding":"Encodage texte","dvchartValueAggregateEncoding":"Encodage agrégat de valeur","dvchartXAxis":"Axe X","dvchartYAxis":"Axe Y","fixedPageSection":"Section page fixe","formattedText":"Texte formaté","image":"Image","inputField":"Champ de saisie","layer":"Calque","line":"Ligne","list":"Liste","listColumn":"Colonne","listColumnsStacked":"Colonnes 2-{{columnCount}}","listRow":"Lignes liste","listRowsStacked":"Lignes 2-{{rowCount}}","overflowPlaceholder":"Espace de débordement","page":"Page","pageFooter":"Pied de page","pageHeader":"En-tête de page","pageSection":"En-tête/Pied de page","partItem":"PartItem","report":"Rapport","reportPart":"Report Part","reportPartProperty":"Property","richtext":"Texte enrichi","shape":"Forme","sparkline":"Sparkline","subreport":"Sous-rapport","table":"Tableau","tableColumn":"Colonne","tableDetails":"Détails","tableFooter":"Pied","tableGroup":"Groupe","tableHeader":"En-tête","tableOfContents":"Table des matières","tableOfContentsLevel":"Niveau","tableRow":"Ligne","tablix":"Tableau matriciel","tablixColumn":"Colonne","tablixMember":"Membre tableau matriciel","tablixRow":"Ligne","textbox":"Zone de texte","unknown":"Élement inconnu"}},{"lng":"fr","ns":"romLabels","resources":{"chart":"Graphique","dvchart":"DV.Graphique","matrix":"Matrice","table":"Tableau","tablix":"Tableau matriciel"}},{"lng":"fr","ns":"tablixWizard","resources":{"aggregates":{"avg":"Moy","count":"Compte","max":"Max","min":"Min","none":"Aucun","sum":"Somme"},"btnOrganization":"Organisation","btnStyling":"Habillage","btnTotals":"Totaux","displayAsOptions":{"default":"Défaut","percentColumnGroupTotal":"% Total du groupe de colonnes","percentGrandTotal":"% Grand Total","percentParentColumnGroupTotal":"% Total du groupe de colonnes parent","percentParentRowGroupTotal":"% Total du groupe de lignes parent","percentRowGroupTotal":"% Total du groupe de lignes"},"filters":{"headingGroupFilters":"Filtres de groupe – {{groupLabel}}","headingTablixFilters":"Filtres de tableau matriciel","textFilters":"Filtres","titleEditGroupFilters":"Modifier les filtres de groupe...","titleEditTablixFilters":"Modifier les filtres de tableau matriciel..."},"formats":{"currency":"Devise","decimal":"Décimale","default":"Défaut","general":"Général","number":"Nombre","percent":"Pourcentage"},"headingDataSets":"DataSets","headingLayoutDesign":"Conception mise en page","headingTablixWizard":"Assistant tableau matriciel","labelCollapsedGroups":"Groupes réduits","labelExpandCollapse":"Développer/Réduire groupes","labelFrozenColumns":"Colonnes gelées","labelFrozenRows":"Lignes gelées","labelNone":"Aucun","labelShowTotalsBeforeGroup":"Afficher les totaux avant les groupes","labelSteppedRowGroups":"Groupes de lignes échelonnées","labelSubTotalsForColumns":"Sous-totaux pour les groupes de colonnes","labelSubTotalsForRows":"Sous-totaux pour les groupes de lignes","labelTotalsForColumns":"Totaux pour les groupes de colonnes","labelTotalsForRows":"Totaux pour les groupes de lignes","labelUserSortEnabled":"Activer tri utilisateur","makeTablix":{"textTotal":"Total","textValues":"Valeurs"},"placeholderNoField":"pas de champ","sortings":{"Ascending":"Ascendant","Descending":"Descendant","None":"Aucun"},"textAddDataSet":"Veuillez d\'abord ajouter un Dataset à votre rapport.","textAddValue":"Veuillez ajouter au moins une valeur unique pour créer un tableau matriciel.","textAsRows":"En tant que lignes","textCannotEditInWizard":"Le tableau matriciel a une structure complexe et ne peut pas être modifié dans l\'assistant. Veuillez utiliser l\'éditeur de groupe à la place.","textColumns":"Groupes de colonnes","textLayoutOptions":"Options de mise en page","textNoDataSets":"Le rapport n\'a pas de Dataset.","textNoValues":"Aucune valeur n\'est spécifiée.","textOpenWizard":"Ouvrir tableau matriciel...","textRowGroups":"Groupes de lignes","textShowValuesAsRows":"Afficher les valeurs sous forme de lignes","textSwap":"Échanger","textValues":"Valeurs","titleAggregate":"Agrégat","titleDelete":"Supprimer","titleDisplayAs":"Afficher comme","titleFormat":"Format","titleSorting":"Tri: {{sorting}}","titleSwapRowColumnGroups":"Permuter les groupes de lignes/colonnes","warning":{"btnQuit":"Quitter","btnRevert":"Revenir","headingWarning":"Avertissement","textChangedStructure":"L\'assistant de tableau matriciel ne peut pas modifier le tableau matriciel car sa structure a été modifiée.","textConfirmReverting":"Veuillez confirmer le retour à l\'état précédent du tableau matriciel modifiable par l\'assistant.","textOtherwiseCannotEdit":"Sinon, il ne sera pas possible de continuer l\'édition de l\'assistant tableau matriciel"}}},{"lng":"fr","ns":"validationErrors","resources":{"enum":{"incorrect":"Une valeur d\'enumération de type \'{{enumType}}\' doit être une des suivantes : {{enumValues}}"},"errorPosition":"à la ligne {{line}}, colonne {{column}}","expression":{"disabledFields":"Attention : Il est interdit d\'utiliser le type de token \'Champs\' dans cette expression (in \'{{token}}\' {{positionInfo}})","disabledReportItems":"Attention : Il est interdit d\'utiliser le type de jeton \'Élémént de rapport\' dans cette expression (dans \'{{token}}\' {{positionInfo}})","errorPosition":"à la ligne {{line}}, colonne {{column}}","parseError":"Erreur de syntaxe: il n\'est pas possible de convertir cette expression de syntaxe d\'interpolation en syntaxe rdl","syntaxError":"Erreur de syntaxe: jeton inattendu \'{{token}}\' {{positionInfo}}","unknown":"Erreur inconnue \'{{positionInfo}}\'","unknownField":"Attention: nom de champ inconnu dans \'{{token}}\' {{positionInfo}}","unknownFunction":"Attention : nom de fonction inconnu \'{{token}}\' {{positionInfo}}","unknownParameter":"Attention: nom de paramètre inconnu dans \'{{token}}\' {{positionInfo}}","unknownReportItem":"Avertissement: nom d\'élément de rapport inconnu dans \'{{token}}\' {{positionInfo}}","unknownThemeImage":"Avertissement: nom d\'image de thème inconnu dans \'{{token}}\' {{positionInfo}}","warning":"Attention: jeton inconnu \'{{token}}\' {{positionInfo}}"},"length":{"negative":"La valeur doit être supérieure à zéro","tooLarge":"La valeur doit être inférieure à {{max}}","tooSmall":"La valeur doit être supérieure à {{min}}","unit":"Les unité valides sont \'cm\', \'mm\', \'in\', \'pt\', and \'pc\'"},"mime_type":{"incorrect":"Le type MIME de l\'image doit correspondre {{wildcard}}"},"number":{"empty":"La valeur est vide","nan":"La valeur n\'est pas un nombre","outOfInterval":"La valeur est hors intervalle","outOfRange":"La valeur est trop grande ou trop petite","tooLarge":"La valeur doit être inférieure à {{max}}","tooSmall":"La valeur doit être supérieure à {{min}}"},"pattern":"Expression invalide. Les expressions littérales de cette propriété doivent être de type \'{{type}}\'. {{info}}","unknown":{"unknown":"Type inconnu \'{{type}}\'"}}},{"lng":"fr","ns":"warning","resources":{"embeddedImage":{"badFile":"Le fichier sélectionné \'{{name}}\' n\'est pas une image.","badFileType":"Le fichier sélectionné \'{{name}}\' ({{type}}) n\'est pas supporté.","badImageFile":"Le fichier image sélectionné \'{{name}}\' est invalide.","badImageSize":"Le fichier image sélectionné \'{{name}}\' dépasse la limite de {{limit}} MB."},"margins":{"caption":"Les marges spécifiées ne peuvent pas être définies","info":"- \'{{name}}\' ne peut être supérieur à {{value}}.","labels":{"bottom":"Marge inférieure","left":"Marge gauche","right":"Marge droite","top":"Marge supérieure"}},"pageSize":{"caption":"La taille de page spécifiée ne peut pas être définie","info":"- \'{{name}}\' ne peut pas être inférieur à {{value}}.","labels":{"height":"Hauteur page","width":"Largeur page"}}}}]')},28:function(e){e.exports=JSON.parse('[{"lng":"fr","ns":"arjswd","resources":{"about":{"applicationVersion":"Version de l\'application: {{applicationVersion}}","close":"Fermer","coreVersion":"Version du noyau: {{coreVersion}}","title":"A propos {{applicationTitle}}"},"application-logo":"","application-title":"ActiveReportsJS Designer","collectionEditor":{"AddBtnTitle":"Ajouter un élément","addBtnText":"Ajouter","closeBtnTitle":"Fermer","deleteBtnTitle":"Supprimer","showBtnTitle":"Afficher","textEmpty":"La collection est vide","textItems":"éléments"},"common":{"copyright":"© MESCIUS inc. All rights reserved.","untitledReportName":"Sans titre"},"commonEditorProps":{"bool-false-label":"Faux","bool-true-label":"Vrai","dataBinding":"Liaison de données","number-editor-decrease":"Dimminuer","number-editor-increase":"Augmenter","placeholderEmpty":"<Vide>"},"dataPanel":{"addCmdTitle":"Ajouter","commonValuesLabel":"Valeurs communes","dataSetsLabel":"DataSets","dataSourcesLabel":"Sources de données","editParameter":"Modifier les paramètres","noDataSets":"Le rapport n\'a pas de Datasets","noDataSources":"Le rapport n\' pas de source de données","noParameters":"Le rapport n\'a pas de paramètres","parametersLabel":"Paramètres","smartSuggestions":"Smart Suggestions"},"dataSetDialog":{"alertTextClose":"Fermer","alertTextCollapse":"Réduire","alertTextExpand":"Étendre","btnBack":"Retour au parent","btnExportTemplate":"Export...","btnValidate":"Valider","confirmSaveInvalid":"Les modifications n\'ont pas été validées. Souhaitez-vous enregistrer les modifications ?","modeEditSubtitle":"Modifier le Dataset","modeNewSubtitle":"Nouveau Dataset","parseQueryError":"Chaîne de requête incorrecte"},"dataSetProps":{"category":{"csv":"CSV Settings","fields":"Champs","filters":"Filtres","name":"Nom","query":"Requête"},"columnSeparatorComma":"Comma","columnSeparatorSemicolon":"Semicolon","columnSeparatorSpace":"Space","columnSeparatorTab":"Tab","fieldsDataField":"Champ de données","fieldsName":"Nom du champ","fieldsValue":"Valeur","fieldsValuePlaceholder":"Entrer l\'expression","methodGet":"HTTP GET","methodPost":"HTTP POST","queryMode":{"JSON":"JSON","Text":"Texte"},"title":{"calcFields":"Champs calculés","columnSeparator":"Column Separator","datasets":"Datasets imbriqués","endpoint":"Uri/chemin","fields":"Champs de la base de données","headers":"En-têtes","headingRow":"Heading Row","jpath":"Chemin Json","mergeColumnSeparators":"Merge Column Separators","mergeRowSeparators":"Merge Row Separators","method":"Méthode","postBody":"Corps","queries":"Paramètres","queryType":"Type","rowSeparator":"Row Separator","startingRow":"Starting Row","value":"Value"}},"dataSourceDialog":{"btnConnectionString":"Chaîne de connection","btnExportTemplate":"Export...","btnProperties":"Propriétés","parseErrorTitle":"Chaîne de connection incorrecte","subtitle":"Modifier la source de données"},"dataSourceProps":{"btnLoadFromFile":"Charger à partir du fichier","category":{"connectionString":"Chaîne de connection","data":"Data","dataFormat":"Data Format","endpoint":"Point final","jsonData":"Données JSON","name":"Nom","parameters":"Paramètres","sourceType":"Source Type"},"exprMenuDialog":"Expression...","exprMenuReset":"Réinitialiser","headersKey":"En-tête","headersValue":"Valeur","providerCSV":"CSV","providerJSON":"JSON","queriesKey":"Paramètres","queriesValue":"Valeur","sourceEmbedded":"Embedded","sourceRemote":"Remote","title":{"headers":"En-tête HTTP","queries":"Paramètres de la requête"}},"dataTab":{"title":"Données"},"designer":{"dateFormats":[],"defaultSettings":{"propertiesMode":"Basic","snapToGridEnabled":"faux","snapToGuidesEnabled":"faux","units":"in"},"fonts":["Arial","Arial Black","Comic Sans MS","Courier New","Geneva","Georgia","Helvetica","Impact","Lucida Console","Meiryo","Meiryo UI","MingLiU","MingLiU-ExtB","MS Gothic","MS Mincho","MS PGothic","MS PMincho","MS Song","MS UI Gothic","NSimSun","Osaka","PMingLiU","PMingLiU-ExtB","SimSun","SimSun-ExtB","Song","Tahoma","Times New Roman","Trebuchet MS","Verdana","Yu Gothic"],"reportStyles":[],"reportTypes":["CPL","FPL","Pageless"]},"dialogs":{"cancel":"Annuler","confirmSavingChanges":{"dontSaveLabel":"Ne pas enregistrer","saveLabel":"Enregistrer","wantSaveChanges":"Souhaitez-vous enregistrer les modifications apportées à {{reportName}}?"},"saveChanges":"Enregistrer les modifications"},"error":{"api":{"createReportFailed":"Failed to create report."},"cantLoadImages":"Impossible de charger la liste des images","cantLoadReports":"Impossible de charger la liste des rapports","cantLoadThemes":"Impossible de charger la liste des thèmes","customInitTemplate":{"loadFailed":"Échec du chargement du modèle d\'initialisation personnalisé \'{{id}}\': {{error}}"},"dataProviderNotSupported":"Data provider \\"{{provider}}\\" is not supported.","errorCode":"Error code: {{code}}","errorSerializingReport":"Internal error: cannot serialize report","hasUnsavedChanges":"Unable to load report. Designer has unsaved changes in the report.","internalError":"Erreur interne","invalid-report-displayName":"Nom d\'affichage du rapport non valide","invalid-report-id":"ID du rapport non valide","invalidReportDef":"Le rapport \\"{{reportName}}\\" ne peut pas être ouvert.","invalidReportType":"Invalid report type","libraries":{"importFailed":"Library \\"{{path}}\\" importing failed","loadingFailed":"Library \\"{{path}}\\" loading failed","unknownLibrary":{"caption":"Report depends on unknown library","text":"The library \\"{{libName}}\\" used in this report is not found"}},"noHostElement":"Impossible de trouver l\'élément hôte.","noReportOpened":"No report currently opened","report-id-is-not-specified":"L\'id du rapport n\'est pas spécifié","reportLoadFailed":"The report \\"{{reportName}}\\" could not be opened.","theme":{"notFoundOrInvalid":"Le thème \'{{theme}}\' utilisé dans ce rapport n\'est pas trouvé ou a un contenu non valide."},"unableGetReport":"Unable to get a report: {{message}}","unablePerformAction":"Unable to perform the action: {{message}}","unableSetReport":"Unable to set a report: {{message}}","unsupportedDocumentType":"Internal error: unsupported document type"},"exportTemplateDialog":{"closeBtnText":"Fermer","title":"Exporter modèle"},"expressions":{"customCodeGroup":"Custom Code"},"license":{"eval":{"badge":"L\'évaluation se termine dans {days} jour(s)","badge-no_days":"Version d\'évaluation","banner":"Version d\'évaluation\\nPar ActiveReportsJS"},"expired":{"badge":"Démonstration expirée","banner":"Version d\'évaluation\\nPar by ActiveReportsJS"},"invalid":{"badge":"Clef de licence invalide","banner":"Clef de licence invalide.\\nVérifiez la clé de licence dans votre projet et ajoutez une licence valide.\\nSi vous avez besoin d\'assistance, s\'il vous <NAME_EMAIL>."},"no-license":{"badge":"Licence non trouvée","banner":"Licence non trouvée.\\nVous avez besoin d\'une clé de licence valide pour exécuter ActiveReportsJS\\nDes clés temporaires sont disponibles pour évaluation.\\nIsi vous avez acheté une licence, la clé se trouve dans votre e-mail de confirmation d\'achat.\\<EMAIL> pour une assistance."}},"menu":{"about":"A propos"},"nameTemplates":{"dataSetCalcFieldValue":"ChampCalculé","dataSetFieldValue":"Champ","reportParameter":"Paramètre"},"notificationPanel":{"collapse":"Réduire","dismiss":"Rejeter","dismissAll":"Rejeter tout","expand":"Étendre","oneError":"Erreur","oneNotification":"Notification","oneWarning":"Avertissement","showDetails":"Afficher détails"},"propertiesTab":{"title":"Propriétés"},"queryValuesDialog":{"btnSave":"Enregistrer et exécuter","title":"Valeurs de paramètre"},"sideBar":{"collapse":"Réduire","expand":"Étendre"},"warning":{"unsavedChanges":"Le rapport sur cette page comporte des modifications non enregistrées."}}}]')},45:function(e,a,t){"use strict";t.r(a);var r=t(27),n=t(28),i={wdCore:r,arjswd:n};if(window.arjsDesigner=window.arjsDesigner||{},window.arjsDesigner.addLocalization)window.arjsDesigner.addLocalization("fr",i);else{var o=window.arjsDesigner.loadLocalizations;window.arjsDesigner.loadLocalizations=function(){o&&o(),window.arjsDesigner.addLocalization("fr",i)}}}});