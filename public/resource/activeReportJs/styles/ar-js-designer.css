/* --------------- Core UI Styles --------------- */
:root {
  --cui-accent: #205F78;
  --cui-accent-hover: #0b455c;
  --cui-accent-semi-10: rgba(32, 95, 120, 0.1);
  --cui-accent-semi-40: rgba(32, 95, 120, 0.38);
  --cui-accent-semi-60: rgba(32, 95, 120, 0.62);
  --cui-accent-text: #205F78;
  --cui-accent-text-hover: #0b455c;
  --cui-accent-text-semi-10: rgba(32, 95, 120, 0.1);
  --cui-accent-text-semi-40: rgba(32, 95, 120, 0.38);
  --cui-accent-text-semi-60: rgba(32, 95, 120, 0.62);
  --cui-accent-icon: #205F78;
  --cui-accent-icon-hover: #0b455c;
  --cui-accent-secondary: #FAAB1C;
  --cui-accent-warning: #e59500;
  --cui-accent-warning-hover: #c78306;
  --cui-accent-warning-semi-10: rgba(229, 149, 0, 0.1);
  --cui-accent-warning-semi-40: rgba(229, 149, 0, 0.38);
  --cui-accent-warning-semi-60: rgba(229, 149, 0, 0.62);
  --cui-accent-warning-text: #e59500;
  --cui-accent-warning-text-hover: #c78306;
  --cui-accent-warning-text-semi-10: rgba(229, 149, 0, 0.1);
  --cui-accent-warning-text-semi-40: rgba(229, 149, 0, 0.38);
  --cui-accent-warning-text-semi-60: rgba(229, 149, 0, 0.62);
  --cui-accent-error: #be1f1f;
  --cui-accent-error-hover: #930f0f;
  --cui-accent-error-semi-10: rgba(190, 31, 31, 0.1);
  --cui-accent-error-semi-40: rgba(190, 31, 31, 0.38);
  --cui-accent-error-semi-60: rgba(190, 31, 31, 0.62);
  --cui-accent-error-text: #be1f1f;
  --cui-accent-error-text-hover: #930f0f;
  --cui-accent-error-text-semi-10: rgba(190, 31, 31, 0.1);
  --cui-accent-error-text-semi-40: rgba(190, 31, 31, 0.38);
  --cui-accent-error-text-semi-60: rgba(190, 31, 31, 0.62);
  --cui-contrast: #ffffff;
  --cui-contrast-semi-10: rgba(255, 255, 255, 0.1);
  --cui-contrast-semi-40: rgba(255, 255, 255, 0.38);
  --cui-contrast-semi-60: rgba(255, 255, 255, 0.62);
  --cui-contrast-text: #ffffff;
  --cui-contrast-text-semi-40: rgba(255, 255, 255, 0.38);
  --cui-bg-body: #E6E6E6;
  --cui-bg-body-overlay: rgba(230, 230, 230, 0.38);
  --cui-bg-panels: #f1f1f1;
  --cui-bg-panels-section: rgba(0, 0, 0, 0.075);
  --cui-bg-panels-border: #DCDCDC;
  --cui-bg-panels-overlay: rgba(241, 241, 241, 0.38);
  --cui-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.3);
  --cui-shadow-border: 0 0 5px 1px rgba(0, 0, 0, 0.1);
  --cui-overlay: rgba(0, 0, 0, 0.2);
  --cui-outline-offset: -2px;
  --cui-outline: 2px solid var(--cui-accent);
  --cui-outline-contrast: 2px solid var(--cui-contrast);
  --cui-outline-warning: 2px solid var(--cui-accent-warning);
  --cui-outline-error: 2px solid var(--cui-accent-error);
  --cui-text: #333333;
  --cui-text-semi-10: rgba(51, 51, 51, 0.1);
  --cui-text-semi-40: rgba(51, 51, 51, 0.38);
  --cui-text-semi-60: rgba(51, 51, 51, 0.62);
  --cui-text-family: "Open Sans", "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  --cui-text-size: 12px;
  --cui-text-size-lg: 14px;
  --cui-text-size-xl: 18px;
  --cui-text-size-sm: 10px;
  --cui-border-radius: 4px;
  --cui-btn-bg: rgba(0, 0, 0, 0.075);
  --cui-btn-bg-hover: rgba(0, 0, 0, 0.12);
  --cui-btn-transparent-warning: rgba(194, 130, 12, 0.1);
  --cui-btn-transparent-warning-hover: rgba(194, 130, 12, 0.2);
  --cui-btn-transparent-error: rgba(147, 15, 15, 0.1);
  --cui-btn-transparent-error-hover: rgba(147, 15, 15, 0.15);
  --cui-btn-group-header-bg: #dddddd;
  --cui-btn-group-header-bg-hover: #c6c6c6;
  --cui-item-bg: rgba(0, 0, 0, 0.075);
  --cui-item-bg-hover: rgba(0, 0, 0, 0.12);
  --cui-input-bg: rgba(0, 0, 0, 0.075);
  --cui-input-bg-hover: rgba(0, 0, 0, 0.12);
  --cui-input-bg-focus: var(--cui-input-bg-hover);
  --cui-input-text: var(--cui-text);
  --cui-input-text-disabled: var(--cui-text-semi-40);
  --cui-input-text-placeholder: var(--cui-text-semi-40);
  --cui-dd-background: #ffffff;
  --cui-dd-background-hover: #ededed;
  --cui-dd-divider: #e0e0e0;
  --cui-binding-default: var(--cui-text-semi-60);
  --cui-binding-default-shadow: var(--cui-text-semi-40);
  --cui-binding-modified: #4dca7d;
  --cui-binding-modified-shadow: rgba(77, 202, 125, 0.62);
  --cui-binding-bind: #e4d50a;
  --cui-binding-bind-shadow: rgba(225, 212, 18, 0.62);
  --cui-binding-error: var(--cui-accent-error);
  --cui-binding-error-shadow: var(--cui-accent-error-semi-60);
  --cui-menu-splitter: rgba(255, 255, 255, 0.15);
  --cui-prop-editors-drag-shadow: 1px 2px 5px 0 rgba(0, 0, 0, 0.38);
  --cui-wizard-bg: var(--cui-accent-semi-60);
  --cui-wizard-dark-bg-main: rgba(0, 0, 0, 0.62);
  --cui-wizard-dark-bg-secondary: rgba(0, 0, 0, 0.2);
  --cui-wizard-dark-hover: rgba(0, 0, 0, 0.62);
  --cui-wizard-light-bg-main: rgba(255, 255, 255, 0.62);
  --cui-wizard-light-bg-secondary: rgba(255, 255, 255, 0.2);
  --cui-wizard-light-hover: rgba(255, 255, 255, 0.62);
  --cui-progressbar-bg: var(--cui-bg-panels);
  --cui-progressbar-bg-semi: var(--cui-btn-bg-hover);
  --cui-progress-fill-color: var(--cui-text);
  --cui-progress-value-color: var(--cui-contrast-text);
  --cui-scrollbar-color: rgba(0, 0, 0, 0.2);
  --cui-scrollbar-color-contrast: rgba(255, 255, 255, 0.2);
  --cui-treeview-outline-color: var(--cui-bg-panels-border);
  --cui-calendar-range-fill-color: var(--cui-accent-text-semi-40);
  --cui-notification-btn-accent-bg: var(--cui-accent);
  --cui-notification-btn-warning-bg: var(--cui-accent-warning);
  --cui-notification-btn-error-bg: var(--cui-accent-error);
  --cui-notification-btn-bg: rgba(0, 0, 0, .15);
  --cui-notification-btn-bg-hover: rgba(0, 0, 0, .25);
}

.gc-disable-selection * {
  -webkit-user-select: none;
  user-select: none;
}

.gc-disable-transition {
  transition: none !important;
}

.gc-no-focus-effect .gc-btn:focus, .gc-no-focus-effect .gc-input:focus, .gc-no-focus-effect .gc-textarea:focus {
  outline: none !important;
  box-shadow: none !important;
}
.gc-no-focus-effect .gc-check__input:focus + .gc-check__mark {
  outline: none !important;
  box-shadow: none !important;
}
.gc-no-focus-effect .gc-radio__input:focus + .gc-radio__mark {
  outline: none !important;
  box-shadow: none !important;
}
.gc-no-focus-effect .gc-toggle__input:focus + .gc-toggle__mark {
  outline: none !important;
  box-shadow: none !important;
}

.gc-layout-app {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 100%;
  flex-flow: column;
}

.gc-layout-app-container {
  display: flex;
  overflow: hidden;
  flex: 1 1 auto;
  height: 0;
}
.gc-layout-app-container > .gc-menu {
  flex: 0 0 auto;
  height: 100%;
}

.gc-layout-main {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  height: 100%;
}

.gc-layout-appbar {
  flex: 0 0 auto;
  height: 31px;
  border-bottom: 1px solid var(--cui-bg-panels-border);
  background-color: var(--cui-bg-panels);
}

.gc-layout-legacy-appbar {
  flex: 0 0 auto;
  height: 50px;
  background-color: var(--cui-accent);
}

.gc-layout-toolbar {
  position: relative;
  display: flex;
  flex: 0 0 auto;
  width: 100%;
  height: 51px;
  border-bottom: 1px solid var(--cui-bg-panels-border);
  background-color: var(--cui-bg-panels);
}
.gc-layout-toolbar > .gc-toolbar {
  flex: 1 1 auto;
  margin: 5px;
}

.gc-layout-with-sidebar {
  display: flex;
  flex: 1 1 auto;
  height: 0;
}
.gc-layout-with-sidebar > .gc-sidebar {
  flex: 0 0 auto;
}

.gc-layout-design-surface {
  overflow: auto;
  flex: 1 1 auto;
  width: 0;
  min-width: 0;
}

.gc-layout-statusbar {
  flex: 0 0 auto;
  height: 31px;
  border-top: 1px solid var(--cui-bg-panels-border);
  background-color: var(--cui-bg-panels);
}

.gc-icon > svg {
  display: block;
  flex: 0 0 auto;
}
.gc-icon--r90 > svg {
  transform: rotate(90deg);
}
.gc-icon--r180 > svg {
  transform: rotate(180deg);
}
.gc-icon--r270 > svg {
  transform: rotate(270deg);
}

.gc-ci-a-text {
  fill: currentColor;
}

.gc-ci-a-accent {
  fill: var(--cui-accent-icon);
}

.gc-ci-c-accent {
  fill: var(--cui-accent-icon);
}

.gc-ci-c-accent-sec {
  fill: var(--cui-accent-secondary);
}

.gc-ci-c-accent-err {
  fill: var(--cui-accent-error);
}

.gc-btn__text {
  position: relative;
  display: inline-block;
  overflow: hidden;
  max-width: 100%;
  height: 40px;
  padding: 0 15px;
  cursor: default;
  transition: color 0.2s ease-in-out;
  text-align: inherit;
  vertical-align: top;
  white-space: nowrap;
  text-overflow: ellipsis;
  pointer-events: none;
  line-height: 40px;
}
.gc-btn__text--empty {
  padding: 0;
}

.gc-btn__icon {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  width: 40px;
  height: 40px;
  transition: color 0.2s ease-in-out;
  text-align: center;
  color: inherit;
  font-size: 20px;
  line-height: 40px;
  justify-content: center;
  align-items: center;
}

.gc-btn {
  position: relative;
  display: inline-block;
  overflow: visible;
  box-sizing: border-box;
  width: auto;
  height: 40px;
  padding: 0;
  user-select: none;
  transition: background-color 0.2s ease-in-out, opacity 0.2s ease-in-out;
  text-align: left;
  vertical-align: top;
  color: inherit;
  border: none;
  border-radius: var(--cui-border-radius);
  outline-offset: var(--cui-outline-offset);
  background-color: var(--cui-btn-bg);
  font-family: var(--cui-text-family);
  font-size: var(--cui-text-size);
  line-height: 40px;
}
.gc-btn > .gc-btn__icon-badge.gc-badge--bottom-right, .gc-btn > .gc-btn__icon-badge.gc-badge--top-right {
  right: calc(100% - 40px);
}
.gc-btn:focus {
  outline: var(--cui-outline);
  box-shadow: none;
}
.gc-btn.gc-btn--level-warning:focus {
  outline: var(--cui-outline-warning);
}
.gc-btn.gc-btn--level-error:focus {
  outline: var(--cui-outline-error);
}
.gc-btn.gc-btn--disabled {
  opacity: 0.38;
}
.gc-btn.gc-btn--disabled:focus {
  outline: none;
  box-shadow: none;
}
.gc-btn::-moz-focus-inner {
  border: 0;
}
.gc-btn:not([disabled]):not(.gc-btn--disabled):hover {
  background-color: var(--cui-btn-bg-hover);
}
.gc-btn--block {
  display: block;
}
.gc-btn--text-align-center {
  text-align: center;
}
.gc-btn--text-align-right {
  text-align: right;
}
.gc-btn--with-icon .gc-btn__text {
  padding: 0 0 0 40px;
}
.gc-btn--with-icon .gc-btn__text:not(.gc-btn__text--empty) {
  padding-right: 15px;
}
.gc-btn--with-badge .gc-badge {
  margin-right: 15px;
}
.gc-btn--with-badge .gc-btn__text:not(.gc-btn__text--empty) {
  padding-right: 5px;
}
.gc-btn--with-badge .gc-btn__text.gc-btn__text--empty + .gc-badge {
  margin-left: 0;
}
.gc-btn--accent {
  color: var(--cui-contrast-text);
  background-color: var(--cui-accent);
}
.gc-btn--accent:not([disabled]):not(.gc-btn--disabled):hover {
  background-color: var(--cui-accent-hover);
}
.gc-btn--accent:not([disabled]):not(.gc-btn--disabled):focus {
  outline: var(--cui-outline-contrast);
}
.gc-btn--transparent {
  background-color: transparent;
}
.gc-btn--transparent:not([disabled]):not(.gc-btn--disabled):hover {
  background-color: var(--cui-btn-bg-hover);
}
.gc-btn--custom {
  padding: 0;
}
.gc-btn--level-warning {
  background-color: var(--cui-btn-transparent-warning);
}
.gc-btn--level-warning:not([disabled]):not(.gc-btn--disabled):hover {
  background-color: var(--cui-btn-transparent-warning-hover);
}
.gc-btn--level-warning.gc-btn--accent:not(.gc-btn--transparent) {
  background-color: var(--cui-accent-warning);
}
.gc-btn--level-warning.gc-btn--accent:not(.gc-btn--transparent):not([disabled]):not(.gc-btn--disabled):hover {
  background-color: var(--cui-accent-warning-hover);
}
.gc-btn--level-warning.gc-btn--accent:not(.gc-btn--transparent):not([disabled]):not(.gc-btn--disabled):focus {
  outline: var(--cui-outline-contrast);
}
.gc-btn--level-warning .gc-accent-color {
  color: var(--cui-accent-warning-text);
}
.gc-btn--level-warning.gc-btn--transparent {
  background-color: transparent;
}
.gc-btn--level-warning.gc-btn--transparent:not([disabled]):not(.gc-btn--disabled):hover {
  background-color: var(--cui-btn-transparent-warning-hover);
}
.gc-btn--level-warning.gc-btn--transparent.gc-btn--accent {
  color: var(--cui-contrast-text);
}
.gc-btn--level-warning.gc-btn--transparent.gc-btn--accent:not([disabled]):not(.gc-btn--disabled):hover {
  color: var(--cui-contrast-text);
}
.gc-btn--level-error {
  background-color: var(--cui-btn-transparent-error);
}
.gc-btn--level-error:not([disabled]):not(.gc-btn--disabled):hover {
  background-color: var(--cui-btn-transparent-error-hover);
}
.gc-btn--level-error.gc-btn--accent:not(.gc-btn--transparent) {
  background-color: var(--cui-accent-error);
}
.gc-btn--level-error.gc-btn--accent:not(.gc-btn--transparent):not([disabled]):not(.gc-btn--disabled):hover {
  background-color: var(--cui-accent-error-hover);
}
.gc-btn--level-error.gc-btn--accent:not(.gc-btn--transparent):not([disabled]):not(.gc-btn--disabled):focus {
  outline: var(--cui-outline-contrast);
}
.gc-btn--level-error .gc-accent-color {
  color: var(--cui-accent-error-text);
}
.gc-btn--level-error.gc-btn--transparent {
  background-color: transparent;
}
.gc-btn--level-error.gc-btn--transparent:not([disabled]):not(.gc-btn--disabled):hover {
  background-color: var(--cui-btn-transparent-error-hover);
}
.gc-btn--level-error.gc-btn--transparent.gc-btn--accent {
  color: var(--cui-contrast-text);
}
.gc-btn--level-error.gc-btn--transparent.gc-btn--accent:hover {
  color: var(--cui-contrast-text);
  background-color: var(--cui-btn-transparent-error-hover);
}
.gc-btn--vertical {
  min-width: 40px;
  height: auto !important;
  text-align: center;
}
.gc-btn--vertical > .gc-btn__icon {
  position: relative;
  top: 8px;
  width: 100% !important;
}
.gc-btn--vertical > .gc-btn__text {
  padding: 0 15px !important;
}
.gc-btn--vertical > .gc-btn__text.gc-btn__text--empty {
  display: none;
}
.gc-btn--vertical > .gc-btn__icon-badge.gc-badge.gc-badge--inset.gc-badge--bottom-right, .gc-btn--vertical > .gc-btn__icon-badge.gc-badge.gc-badge--inset.gc-badge--top-right {
  right: 0;
}
.gc-btn--vertical > .gc-btn__icon-badge.gc-badge.gc-badge--inset.gc-badge--bottom-left, .gc-btn--vertical > .gc-btn__icon-badge.gc-badge.gc-badge--inset.gc-badge--bottom-right {
  bottom: calc(100% - 40px);
}
.gc-btn--vertical.gc-btn--with-badge > .gc-badge.gc-badge--inline {
  margin-right: 15px;
  margin-left: 0;
}
.gc-btn--vertical.gc-btn--with-badge > .gc-btn__text--empty + .gc-badge.gc-badge--inline {
  margin-right: 0;
}
.gc-btn--vertical.gc-size-sm {
  min-width: 30px;
}
.gc-btn--vertical.gc-size-sm > .gc-btn__text {
  padding: 0 10px !important;
}
.gc-btn--vertical.gc-size-sm > .gc-btn__icon {
  top: 4px;
}
.gc-btn--vertical.gc-size-sm > .gc-btn__icon-badge.gc-badge--bottom-left, .gc-btn--vertical.gc-size-sm > .gc-btn__icon-badge.gc-badge--bottom-right {
  bottom: calc(100% - 30px);
}
.gc-btn--vertical.gc-size-sm.gc-btn--with-badge > .gc-btn__text:not(.gc-btn__text--empty) + .gc-badge {
  margin-right: 10px;
}
.gc-btn--vertical.gc-size-lg {
  min-width: 50px;
}
.gc-btn--vertical.gc-size-lg > .gc-btn__icon-badge.gc-badge--bottom-left, .gc-btn--vertical.gc-size-lg > .gc-btn__icon-badge.gc-badge--bottom-right {
  bottom: calc(100% - 50px);
}
.gc-btn.gc-size-sm.gc-btn--with-icon > .gc-btn__text {
  padding-left: 30px;
}
.gc-btn.gc-size-sm.gc-btn--with-icon > .gc-btn__text:not(.gc-btn__text--empty) {
  padding-right: 10px;
}
.gc-btn.gc-size-sm .gc-btn__icon {
  width: 30px;
  height: 30px;
  font-size: 16px;
  line-height: 30px;
}
.gc-btn.gc-size-sm .gc-btn__icon-badge.gc-badge--bottom-right, .gc-btn.gc-size-sm .gc-btn__icon-badge.gc-badge--top-right {
  right: calc(100% - 30px);
}
.gc-btn.gc-size-sm .gc-btn__text {
  height: 30px;
  line-height: 30px;
}
.gc-btn.gc-size-lg.gc-btn--with-icon > .gc-btn__text {
  padding-left: 50px;
}
.gc-btn.gc-size-lg .gc-btn__icon {
  width: 50px;
  height: 50px;
  font-size: 24px;
  line-height: 50px;
}
.gc-btn.gc-size-lg .gc-btn__icon-badge.gc-badge--bottom-right, .gc-btn.gc-size-lg .gc-btn__icon-badge.gc-badge--top-right {
  right: calc(100% - 50px);
}
.gc-btn.gc-size-lg .gc-btn__text {
  height: 50px;
  line-height: 50px;
}
.gc-btn[disabled] {
  opacity: 0.38;
}

@media (any-hover: none) {
  .gc-btn:not([disabled]):not(.gc-btn--disabled):hover {
    background-color: var(--cui-btn-bg);
  }
  .gc-btn--accent:not([disabled]):not(.gc-btn--disabled):hover {
    background-color: var(--cui-accent);
  }
  .gc-btn--transparent:not([disabled]):not(.gc-btn--disabled):hover {
    background-color: transparent;
  }
  .gc-btn--level-warning.gc-btn--accent:not(.gc-btn--transparent):not([disabled]):not(.gc-btn--disabled):hover {
    background-color: var(--cui-accent-warning);
  }
  .gc-btn--level-warning.gc-btn--transparent.gc-btn--accent:hover {
    color: var(--cui-accent-warning);
    background-color: transparent;
  }
  .gc-btn--level-error.gc-btn--accent:not(.gc-btn--transparent):not([disabled]):not(.gc-btn--disabled):hover {
    background-color: var(--cui-accent-error);
  }
  .gc-btn--level-error.gc-btn--transparent.gc-btn--accent:hover {
    color: var(--cui-accent-error);
    background-color: transparent;
  }
}
.gc-btn-group {
  position: relative;
  display: inline-flex;
  border-radius: 4px;
}
.gc-btn-group > *:not(:only-child):not(:first-child):not(:last-child) {
  border-radius: 0 !important;
}
.gc-btn-group > *:not(:only-child):not(:first-child):not(:last-child) > .gc-btn, .gc-btn-group > *:not(:only-child):not(:first-child):not(:last-child) .gc-input {
  border-radius: 0 !important;
}
.gc-btn-group > *:not(:only-child):first-child {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}
.gc-btn-group > *:not(:only-child):first-child > .gc-btn, .gc-btn-group > *:not(:only-child):first-child .gc-input {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}
.gc-btn-group > *:not(:only-child):last-child {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}
.gc-btn-group > *:not(:only-child):last-child > .gc-btn, .gc-btn-group > *:not(:only-child):last-child .gc-input {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}
.gc-btn-group > *:not(:only-child).gc-dd--chevron > .gc-btn {
  min-width: auto;
}
.gc-btn-group--align-center {
  justify-content: center;
}
.gc-btn-group--align-right {
  justify-content: flex-end;
}
.gc-btn-group--align-justify > *:not(.gc-btn-group__splitter) {
  flex: 1;
}
.gc-btn-group--block {
  display: flex;
}
.gc-btn-group--disabled:after, .gc-btn-group--disabled .gc-btn-group__splitter {
  opacity: 0.38;
}
.gc-btn-group[class*="--header"] {
  overflow: hidden;
}
.gc-btn-group[class*="--header"]:after {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  content: "";
  transition: background-color 0.2s ease-in-out;
}
.gc-btn-group--header-default > *:first-child.gc-btn, .gc-btn-group--header-default > *:first-child.gc-input {
  background-color: var(--cui-btn-group-header-bg);
}
.gc-btn-group--header-default > *:first-child.gc-btn:hover:not([disabled]), .gc-btn-group--header-default > *:first-child.gc-input:hover:not([disabled]) {
  background-color: var(--cui-btn-group-header-bg-hover);
}
.gc-btn-group--header-default > *:first-child.gc-dd > .gc-btn, .gc-btn-group--header-default > *:first-child.gc-combo > .gc-input {
  background-color: var(--cui-btn-group-header-bg);
}
.gc-btn-group--header-default > *:first-child.gc-dd > .gc-btn:hover:not([disabled]), .gc-btn-group--header-default > *:first-child.gc-combo > .gc-input:hover:not([disabled]) {
  background-color: var(--cui-btn-group-header-bg-hover);
}
.gc-btn-group--header-default:after {
  background-color: var(--cui-btn-group-header-bg);
}
.gc-btn-group--header-info:after {
  background-color: var(--cui-accent);
}
.gc-btn-group--header-warning:after {
  background-color: var(--cui-accent-warning);
}
.gc-btn-group--header-error:after {
  background-color: var(--cui-accent-error);
}

.gc-btn-group__splitter {
  display: flex;
  width: 1px;
  background-color: var(--cui-btn-bg);
  align-items: center;
}
.gc-btn-group__splitter:after {
  width: 1px;
  height: 50%;
  content: "";
  background-color: var(--cui-btn-bg-hover);
}
.gc-btn-group__splitter--accent-full:after {
  background-color: var(--cui-contrast-text-semi-40);
}
.gc-btn-group__splitter--accent-info:after {
  background-color: var(--cui-accent-semi-60);
}
.gc-btn-group__splitter--accent-warning:after {
  background-color: var(--cui-accent-warning-semi-40);
}
.gc-btn-group__splitter--accent-error:after {
  background-color: var(--cui-accent-error-semi-40);
}
.gc-btn-group__splitter--background-transparent {
  background-color: transparent;
}

@media (any-hover: none) {
  .gc-btn-group--header-default > *:first-child.gc-btn:hover:not([disabled]), .gc-btn-group--header-default > *:first-child.gc-input:hover:not([disabled]) {
    background-color: var(--cui-btn-group-header-bg);
  }
  .gc-btn-group--header-default > *:first-child.gc-dd > .gc-btn:hover:not([disabled]), .gc-btn-group--header-default > *:first-child.gc-combo > .gc-input:hover:not([disabled]) {
    background-color: var(--cui-btn-group-header-bg);
  }
}
.gc-dd__chevron {
  position: absolute;
  top: 0;
  right: 0;
  display: none;
  width: 30px;
  height: 40px;
  transition: transform 0.2s ease-in-out;
  text-align: center;
  pointer-events: none;
  font-size: 20px;
  line-height: 40px;
}
.gc-dd__chevron--accent-full {
  color: var(--cui-contrast-text);
}
.gc-dd__chevron.gc-size-sm {
  width: 30px;
  font-size: 16px;
}
.gc-dd__chevron.gc-size-lg {
  width: 35px;
  font-size: 24px;
}

.gc-btn--level-warning + .gc-dd__chevron.gc-accent-color {
  color: var(--cui-accent-warning);
}

.gc-btn--level-error + .gc-dd__chevron.gc-accent-color {
  color: var(--cui-accent-error);
}

.gc-btn--accent.gc-btn--transparent.gc-btn--level-warning:not(:hover) + .gc-dd__chevron {
  color: var(--cui-accent-warning);
}

.gc-btn--accent.gc-btn--transparent.gc-btn--level-error:not(:hover) + .gc-dd__chevron {
  color: var(--cui-accent-error);
}

.gc-dd {
  position: relative;
  display: inline-block;
  width: auto;
  height: 40px;
  vertical-align: top;
  color: var(--cui-text);
  border-radius: 4px;
  font-size: var(--cui-text-size);
}
.gc-dd *, .gc-dd *:before, .gc-dd *:after {
  box-sizing: border-box;
}
.gc-dd:after {
  position: absolute;
  top: 0;
  left: 0;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  content: "";
  transition: opacity 0.2s ease-in-out;
  pointer-events: none;
  opacity: 0;
  border: 1px solid var(--cui-accent-error);
  border-radius: inherit;
}
.gc-dd > .gc-btn {
  width: 100%;
}
.gc-dd--block {
  display: block;
}
.gc-dd--disabled .gc-dd__preview, .gc-dd--disabled .gc-dd__chevron {
  opacity: 0.38;
}
.gc-dd--invalid:after {
  opacity: 1;
}
.gc-dd--invalid > .gc-btn:focus {
  outline: var(--cui-outline-error);
}
.gc-dd--with-preview > .gc-btn > .gc-btn__text {
  padding-left: 0;
}
.gc-dd--with-preview:not([class^=gc-size]) > .gc-btn {
  padding-left: 40px;
}
.gc-dd--with-preview.gc-size-sm > .gc-btn {
  padding-left: 30px;
}
.gc-dd--with-preview.gc-size-sm > .gc-dd__preview {
  width: 30px;
}
.gc-dd--with-preview.gc-size-lg > .gc-btn {
  padding-left: 50px;
}
.gc-dd--with-preview.gc-size-lg > .gc-dd__preview {
  width: 50px;
}
.gc-dd--chevron > .gc-dd__chevron {
  display: flex;
  justify-content: center;
  align-items: center;
}
.gc-dd--chevron > .gc-btn {
  min-width: 70px;
}
.gc-dd--chevron > .gc-btn--custom {
  width: 100%;
  padding-right: 30px;
  padding-left: 15px;
}
.gc-dd--chevron > .gc-btn .gc-btn__text {
  padding-right: 30px;
}
.gc-dd--chevron.gc-size-sm > .gc-btn {
  min-width: 60px;
}
.gc-dd--chevron.gc-size-sm > .gc-btn--custom {
  padding-right: 30px;
}
.gc-dd--chevron.gc-size-sm > .gc-btn .gc-btn__text {
  padding-right: 30px !important;
}
.gc-dd--chevron.gc-size-lg > .gc-btn {
  min-width: 85px;
}
.gc-dd--chevron.gc-size-lg > .gc-btn--custom {
  padding-right: 35px;
}
.gc-dd--chevron.gc-size-lg > .gc-btn .gc-btn__text {
  padding-right: 35px;
}
.gc-dd--menu-open > .gc-dd__chevron:not(.gc-dd__chevron--custom) {
  transform: rotate(180deg);
}
.gc-dd--placeholder-text:not(.gc-dd--disabled) > .gc-btn .gc-btn__text {
  opacity: 0.38;
}
.gc-dd__toggle-content {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.gc-dd__preview {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  width: 40px;
  height: 100%;
  padding: 5px;
  pointer-events: none;
  justify-content: center;
  align-items: center;
}

@media (any-hover: none) {
  .gc-btn--accent.gc-btn--transparent.gc-btn--level-warning + .gc-dd__chevron {
    color: var(--cui-accent-warning);
  }

  .gc-btn--accent.gc-btn--transparent.gc-btn--level-error + .gc-dd__chevron {
    color: var(--cui-accent-error);
  }
}
@keyframes gc-menu-show {
  0% {
    transform: scale(0.25);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
@keyframes gc-menu-show-center {
  0% {
    transform: scale(0.25) translateX(-50%);
    opacity: 0;
  }
  100% {
    transform: scale(1) translateX(-50%);
    opacity: 1;
  }
}
.gc-portal-root--dropdown .gc-positioner {
  z-index: 1030;
}

.gc-dd-menu {
  transform-origin: right top;
  animation: gc-menu-show 0.2s 1 ease-in-out;
  border-radius: var(--cui-border-radius);
  background-color: var(--cui-dd-background);
  box-shadow: var(--cui-shadow-border);
  font-family: var(--cui-text-family);
}
.gc-dd-menu *, .gc-dd-menu *:before, .gc-dd-menu *:after {
  box-sizing: border-box;
}
.gc-dd-menu--dropup {
  transform-origin: right bottom;
}
.gc-dd-menu--multiselect .gc-dd-menu__item {
  padding-left: 0;
}
.gc-dd-menu--multiselect .gc-dd-menu__item.gc-size-sm {
  padding-left: 5px;
}
.gc-dd-menu--align-center {
  transform-origin: left top;
  animation: gc-menu-show-center 0.2s 1 ease-in-out both;
}
.gc-dd-menu--align-center.gc-dd-menu--dropup {
  transform-origin: left bottom;
}
.gc-dd-menu--align-left {
  transform-origin: left top;
}
.gc-dd-menu--align-left.gc-dd-menu--dropup {
  transform-origin: left bottom;
}
.gc-dd-menu__scroll > .gc-scrollbars__view {
  padding: 7.5px 0;
}
.gc-dd-menu:not(.gc-dd-menu--multiselect) .gc-dd-menu__item--selected {
  color: var(--cui-contrast-text);
  background-color: var(--cui-accent);
}
.gc-dd-menu:not(.gc-dd-menu--multiselect) .gc-dd-menu__item--selected > i {
  color: var(--cui-contrast-text);
}
.gc-dd-menu:not(.gc-dd-menu--multiselect) .gc-dd-menu__item--selected:not([disabled]):not(.disabled):hover {
  background-color: var(--cui-accent-hover);
}
.gc-dd-menu:not(.gc-dd-menu--multiselect) .gc-dd-menu__item--selected:not([disabled]):not(.disabled):active {
  background-color: var(--cui-accent-hover);
}
.gc-dd-menu:not(.gc-dd-menu--multiselect) .gc-dd-menu__item--selected.gc-dd-menu__item--focused {
  outline: var(--cui-outline-contrast);
}

.gc-dd-menu--custom .gc-dd-menu__scroll > .gc-scrollbars__view {
  padding: 0;
}

.gc-dd-menu__header.gc-heading {
  padding: 0 15px;
  color: var(--cui-text-semi-60);
}
.gc-dd-menu__header.gc-heading .gc-heading__divider {
  border-bottom: 1px solid var(--cui-dd-divider);
}

.gc-dd-menu__divider {
  display: block;
  width: calc(100% - 30px);
  height: 1px;
  margin: 7px 15px;
  background-color: var(--cui-dd-divider);
}

.gc-dd-menu__group-dd {
  display: block;
  opacity: 1;
}
.gc-dd-menu__group-dd--transition {
  transition: opacity 0.2s ease-in-out;
  opacity: 0;
}
.gc-dd-menu__group-dd--hidden {
  display: none;
}

.gc-dd-menu__group-dd:hover {
  display: block;
  opacity: 1;
}

.gc-dd-menu__item {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 40px;
  margin: 0;
  padding: 0 15px;
  cursor: default;
  user-select: none;
  text-align: left;
  text-overflow: ellipsis;
  color: var(--cui-text);
  border: 0;
  border-radius: 0;
  outline-offset: var(--cui-outline-offset);
  background-color: transparent;
  font-family: var(--cui-text-family);
  font-size: var(--cui-text-size);
  line-height: 40px;
  align-items: center;
}
.gc-dd-menu__item--with-preview {
  padding-left: 5px;
}
.gc-dd-menu__item--with-preview.gc-size-sm {
  padding-left: 10px;
}
.gc-dd-menu__item--with-preview.gc-size-lg {
  padding-left: 0;
}
.gc-dd-menu__item > .gc-dd-menu__item-preview {
  display: flex;
  width: 40px;
  height: 40px;
  pointer-events: none;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}
.gc-dd-menu__item > span {
  overflow: hidden;
  flex: 1 1 auto;
  max-width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.gc-dd-menu__item > .gc-icon {
  display: flex;
  width: 20px;
  height: 40px;
  margin-right: 15px;
  text-align: center;
  font-size: 20px;
  line-height: 40px;
  justify-content: center;
  align-items: center;
}
.gc-dd-menu__item:focus {
  outline: none;
  box-shadow: none;
}
.gc-dd-menu__item::-moz-focus-inner {
  border: 0;
}
.gc-dd-menu__item:not([disabled]):not(.disabled):hover {
  background-color: var(--cui-dd-background-hover);
}
.gc-dd-menu__item:not([disabled]):not(.disabled):active {
  background-color: var(--cui-dd-background-hover);
}
.gc-dd-menu__item--focused {
  outline: var(--cui-outline);
}
.gc-dd-menu__item--disabled {
  opacity: 0.62;
}
.gc-dd-menu__item.gc-size-sm > .gc-dd-menu__item-preview {
  width: 30px;
  height: 30px;
  margin-right: 5px;
}
.gc-dd-menu__item.gc-size-sm > .gc-icon {
  width: 16px;
  height: 30px;
  margin-right: 10px;
  font-size: 16px;
  line-height: 30px;
}
.gc-dd-menu__item.gc-size-lg > .gc-dd-menu__item-preview {
  width: 50px;
  height: 50px;
}
.gc-dd-menu__item.gc-size-lg > .gc-icon {
  width: 24px;
  height: 50px;
  margin-right: 15px;
  font-size: 24px;
  line-height: 50px;
}
.gc-dd-menu__item.gc-dd-menu__group {
  padding: 0 5px 0 15px;
}
.gc-dd-menu__item.gc-dd-menu__group .gc-dd-menu__group-icon {
  margin-right: 0;
  opacity: 0.5;
}

@media (any-hover: none) {
  .gc-dd-menu:not(.gc-dd-menu--multiselect) .gc-dd-menu__item--selected:not([disabled]):not(.disabled):hover {
    background-color: var(--cui-accent);
  }
  .gc-dd-menu:not(.gc-dd-menu--multiselect) .gc-dd-menu__item--selected.gc-dd-menu__item--focused:hover {
    background-color: var(--cui-accent-hover) !important;
  }

  .gc-dd-menu__item:not([disabled]):not(.disabled):hover {
    background-color: var(--cui-dd-background);
  }
}
.gc-check__mark {
  position: absolute;
  top: 10px;
  left: 10px;
  display: block;
  width: 20px;
  height: 20px;
  transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
  border: 2px solid transparent;
  border-radius: 4px;
  outline-offset: 2px;
  background-color: var(--cui-input-bg);
}
.gc-check__mark::before, .gc-check__mark::after {
  position: absolute;
  top: 50%;
  left: 50%;
  content: "";
  opacity: 0;
}
.gc-check__mark::before {
  width: 42.5%;
  height: 72.5%;
  transform: rotate(45deg) scale(0) translate(-50%, -50%);
  transform-origin: 30% 0;
  border: 2px solid var(--cui-contrast);
  border-top: 0;
  border-left: 0;
}
.gc-check__mark::after {
  width: 50%;
  height: 50%;
  transform: scale(0) translate(-50%, -50%);
  transform-origin: 0 0;
  border-radius: 4px;
  background-color: var(--cui-contrast);
}

.gc-check__input {
  position: absolute;
  top: 0;
  left: 0;
  width: 1px;
  height: 1px;
  opacity: 0;
}
.gc-check__input:focus + .gc-check__mark {
  outline: var(--cui-outline);
  box-shadow: none;
}
.gc-check__input:checked:not(.gc-check__input--indeterminate) + .gc-check__mark {
  background-color: var(--cui-accent);
}
.gc-check__input:checked:not(.gc-check__input--indeterminate) + .gc-check__mark::before {
  transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
  transform: rotate(45deg) scale(1) translate(-50%, -50%);
  opacity: 1;
}
.gc-check__input--indeterminate + .gc-check__mark {
  border-color: var(--cui-accent);
  background-color: var(--cui-accent);
}
.gc-check__input--indeterminate + .gc-check__mark::after {
  transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
  transform: scale(1) translate(-50%, -50%);
  opacity: 1;
}

.gc-check {
  position: relative;
  display: inline-block;
  overflow: hidden;
  height: 40px;
  padding: 0 0 0 40px;
  -webkit-user-select: none;
  user-select: none;
  vertical-align: top;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: var(--cui-text-size);
  font-weight: normal;
  line-height: 40px;
}
.gc-check *, .gc-check *:before, .gc-check *:after {
  box-sizing: border-box;
}
.gc-check:hover .gc-check__mark {
  border-color: var(--cui-input-bg-hover);
}
.gc-check--block {
  display: block;
}
.gc-check--disabled {
  pointer-events: none;
  opacity: 0.38;
}
.gc-check--invalid .gc-check__mark {
  border-color: var(--cui-accent-error) !important;
}
.gc-check--invalid .gc-check__input:focus + .gc-check__mark {
  outline: var(--cui-outline-error);
}
.gc-check--invalid .gc-check__input:checked + .gc-check__mark {
  background-color: var(--cui-accent-error);
}
.gc-check--invalid .gc-check__input--indeterminate + .gc-check__mark {
  background-color: var(--cui-accent-error);
}
.gc-check--align-left {
  padding: 0 40px 0 0;
  text-align: right;
}
.gc-check--align-left .gc-check__mark {
  right: 10px;
  left: auto;
}

.gc-check.gc-size-sm {
  padding: 0 0 0 30px;
  line-height: 30px;
}
.gc-check.gc-size-sm .gc-check__mark {
  top: 5px;
  left: 5px;
}
.gc-check.gc-size-sm.gc-check--align-left {
  padding: 0 30px 0 0;
}
.gc-check.gc-size-sm.gc-check--align-left .gc-check__mark {
  right: 5px;
  left: auto;
}

.gc-check.gc-size-lg {
  padding: 0 0 0 50px;
  line-height: 50px;
}
.gc-check.gc-size-lg .gc-check__mark {
  top: 15px;
  left: 15px;
}
.gc-check.gc-size-lg.gc-check--align-left {
  padding: 0 50px 0 0;
}
.gc-check.gc-size-lg.gc-check--align-left .gc-check__mark {
  right: 15px;
  left: auto;
}

@media (any-hover: none) {
  .gc-check:hover .gc-check__mark {
    border-color: var(--cui-input-bg);
  }
}
.gc-radio__mark {
  position: absolute;
  top: 10px;
  left: 10px;
  display: block;
  width: 20px;
  height: 20px;
  transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
  border: 2px solid transparent;
  border-radius: 50%;
  outline-offset: 2px;
  background-color: var(--cui-input-bg);
}
.gc-radio__mark::before {
  position: absolute;
  top: -2px;
  left: -2px;
  display: block;
  width: 20px;
  height: 20px;
  content: "";
  transition: transform 0.2s ease-in-out, opacity 0.2s ease-in-out;
  transform: scale(0);
  opacity: 0;
  background-image: radial-gradient(var(--cui-contrast), var(--cui-contrast) 28%, transparent 32%);
}

.gc-radio__input {
  position: absolute;
  top: 0;
  left: 0;
  width: 1px;
  height: 1px;
  opacity: 0;
}
.gc-radio__input:focus + .gc-radio__mark {
  outline: var(--cui-outline);
  box-shadow: none;
}
.gc-radio__input:checked + .gc-radio__mark {
  border-color: var(--cui-accent);
  background-color: var(--cui-accent);
}
.gc-radio__input:checked + .gc-radio__mark::before {
  transform: scale(1);
  opacity: 1;
}

.gc-radio {
  position: relative;
  display: inline-block;
  overflow: hidden;
  height: 40px;
  padding: 0 0 0 40px;
  -webkit-user-select: none;
  user-select: none;
  vertical-align: top;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: var(--cui-text-size);
  font-weight: normal;
  line-height: 40px;
}
.gc-radio *, .gc-radio *:before, .gc-radio *:after {
  box-sizing: border-box;
}
.gc-radio:hover .gc-radio__mark {
  border-color: var(--cui-input-bg-hover);
}
.gc-radio--block {
  display: block;
}
.gc-radio--disabled {
  pointer-events: none;
  opacity: 0.38;
}
.gc-radio--invalid .gc-radio__mark {
  border-color: var(--cui-accent-error) !important;
}
.gc-radio--invalid .gc-radio__input:focus + .gc-radio__mark {
  outline: var(--cui-outline-error);
}
.gc-radio--invalid .gc-radio__input:checked + .gc-radio__mark {
  background-color: var(--cui-accent-error);
}
.gc-radio--align-left {
  padding: 0 40px 0 0;
  text-align: right;
}
.gc-radio--align-left > .gc-radio__mark {
  right: 10px;
  left: auto;
}

.gc-radio.gc-size-sm {
  padding: 0 0 0 30px;
  line-height: 30px;
}
.gc-radio.gc-size-sm .gc-radio__mark {
  top: 5px;
  left: 5px;
}
.gc-radio.gc-size-sm.gc-radio--align-left {
  padding: 0 30px 0 0;
}
.gc-radio.gc-size-sm.gc-radio--align-left .gc-radio__mark {
  right: 5px;
  left: auto;
}

.gc-radio.gc-size-lg {
  padding: 0 0 0 50px;
  line-height: 50px;
}
.gc-radio.gc-size-lg .gc-radio__mark {
  top: 15px;
  left: 15px;
}
.gc-radio.gc-size-lg.gc-radio--align-left {
  padding: 0 50px 0 0;
}
.gc-radio.gc-size-lg.gc-radio--align-left .gc-radio__mark {
  right: 15px;
  left: auto;
}

@media (any-hover: none) {
  .gc-radio:hover .gc-radio__mark {
    border-color: var(--cui-input-bg);
  }
}
.gc-toggle__input {
  position: absolute;
  top: 0;
  left: 0;
  width: 1px;
  height: 1px;
  opacity: 0;
}
.gc-toggle__input:focus + .gc-toggle__mark {
  outline: var(--cui-outline);
  box-shadow: none;
}
.gc-toggle__input:checked:not(.gc-toggle__input--indeterminate) + .gc-toggle__mark {
  background-color: var(--cui-accent);
}
.gc-toggle__input:checked:not(.gc-toggle__input--indeterminate) + .gc-toggle__mark:after {
  transform: translateX(20px);
  background-color: var(--cui-contrast);
}
.gc-toggle__input--indeterminate + .gc-toggle__mark:after {
  left: 50%;
  transition: border-color 0.2s ease-in-out;
  transform: translateX(-50%);
  border: 2px solid var(--cui-text);
  background-color: transparent;
}

.gc-toggle__mark {
  position: relative;
  flex: 0 0 auto;
  width: 40px;
  height: 20px;
  margin: 10px 15px 10px 0;
  transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
  border: 2px solid transparent;
  border-radius: 10px;
  outline-offset: 2px;
  background-color: var(--cui-btn-bg);
}
.gc-toggle__mark:after {
  position: absolute;
  top: 3px;
  left: 3px;
  width: 10px;
  height: 10px;
  content: "";
  transition: background-color 0.2s ease-in-out, transform 0.2s ease-in-out;
  border-radius: 50%;
  background-color: var(--cui-text);
}

.gc-toggle {
  position: relative;
  display: inline-flex;
  height: 40px;
  padding: 0;
  -webkit-user-select: none;
  user-select: none;
  text-align: left;
  vertical-align: top;
  color: var(--cui-text);
  border: none;
  background-color: transparent;
  font-size: var(--cui-text-size);
  font-weight: normal;
  line-height: 40px;
}
.gc-toggle *, .gc-toggle *:before, .gc-toggle *:after {
  box-sizing: border-box;
}
.gc-toggle:hover > .gc-toggle__mark {
  border-color: var(--cui-btn-bg-hover);
}
.gc-toggle--block {
  display: flex;
}
.gc-toggle--block.gc-toggle--align-left {
  justify-content: flex-end;
}
.gc-toggle--align-left .gc-toggle__mark {
  margin-right: 0;
  margin-left: 15px;
  order: 2;
}
.gc-toggle--disabled {
  pointer-events: none;
  opacity: 0.38;
}
.gc-toggle--invalid:hover > .gc-toggle__mark {
  border-color: var(--cui-accent-error);
}
.gc-toggle--invalid > .gc-toggle__input:not(:checked):not(.gc-toggle__input--indeterminate) + .gc-toggle__mark:after {
  background-color: var(--cui-accent-error);
}
.gc-toggle--invalid > .gc-toggle__input:focus + .gc-toggle__mark {
  outline: var(--cui-outline-error);
}
.gc-toggle--invalid > .gc-toggle__input:checked + .gc-toggle__mark {
  background-color: var(--cui-accent-error);
}
.gc-toggle--invalid > .gc-toggle__input--indeterminate + .gc-toggle__mark:after {
  border-color: var(--cui-accent-error);
}

.gc-toggle.gc-size-sm .gc-toggle__mark {
  margin-top: 5px;
  margin-bottom: 5px;
}

.gc-toggle.gc-size-lg .gc-toggle__mark {
  margin-top: 15px;
  margin-bottom: 15px;
}

@media (any-hover: none) {
  .gc-toggle:hover > .gc-toggle__mark {
    border-color: transparent;
  }
  .gc-toggle--invalid:hover > .gc-toggle__mark {
    border-color: transparent;
  }
}
.gc-toolbar {
  position: relative;
  display: inline-flex;
}
.gc-toolbar *, .gc-toolbar *:before, .gc-toolbar *:after {
  box-sizing: border-box;
}
.gc-toolbar--block {
  display: flex;
  height: 40px;
}
.gc-toolbar--block .gc-toolbar__content-container {
  overflow: hidden;
}
.gc-toolbar--block .gc-toolbar__content {
  position: absolute;
}
.gc-toolbar--with-toggle > .gc-btn {
  position: absolute;
  top: 0;
  right: 0;
}
.gc-toolbar--with-toggle .gc-toolbar__content {
  padding-right: 40px;
}
.gc-toolbar--with-toggle .gc-toolbar__content:before {
  position: absolute;
  top: -5px;
  right: -5px;
  width: calc(100% + 10px);
  height: calc(100% + 10px);
  content: "";
  opacity: 0;
  border-radius: var(--cui-border-radius);
  background-color: var(--cui-dd-background);
  box-shadow: var(--cui-shadow-border);
}
.gc-toolbar--with-toggle.gc-size-lg .gc-toolbar__content {
  padding-right: 50px;
}
.gc-toolbar--with-toggle.gc-size-sm .gc-toolbar__content {
  padding-right: 30px;
}
.gc-toolbar--expanded {
  z-index: 10;
}
.gc-toolbar--expanded .gc-toolbar__content-container {
  overflow: visible;
}
.gc-toolbar--expanded .gc-toolbar__content:before {
  opacity: 1;
}
.gc-toolbar--no-wrap .gc-toolbar__content {
  position: relative;
  flex-wrap: nowrap;
}
.gc-toolbar--rounded .gc-toolbar__content:before {
  border-radius: 20px;
}
.gc-toolbar--rounded.gc-size-lg .gc-toolbar__content:before {
  border-radius: 25px;
}
.gc-toolbar--rounded.gc-size-sm .gc-toolbar__content:before {
  border-radius: 15px;
}
.gc-toolbar--dropup .gc-toolbar__content {
  top: auto;
  bottom: 0;
  flex-wrap: wrap-reverse;
}
.gc-toolbar--dropup .gc-toolbar__content:before {
  top: auto !important;
  bottom: -5px;
  box-shadow: var(--cui-shadow-border);
}
.gc-toolbar.gc-size-lg {
  height: 50px;
}
.gc-toolbar.gc-size-sm {
  height: 30px;
}

.gc-toolbar__content {
  position: relative;
  top: 0;
  left: 0;
  display: flex;
  width: 100%;
  flex-wrap: wrap;
}

.gc-toolbar__content-container {
  position: relative;
  width: 100%;
}

.gc-toolbar__item-container {
  display: inline-block;
  flex: 0 0 auto;
  vertical-align: top;
}
.gc-toolbar__item-container--padding {
  padding: 0 5px 0 0;
}
.gc-toolbar__item-container--padding:last-of-type {
  padding: 0;
}

.gc-toolbar__splitter {
  position: relative;
  display: block;
  overflow: hidden;
  width: 1px;
  height: 40px;
}
.gc-toolbar__splitter::after {
  display: block;
  width: 1px;
  height: 20px;
  margin: 10px 0;
  content: " ";
  background-color: var(--cui-bg-panels-border);
}

.gc-size-sm .gc-toolbar__splitter {
  height: 30px;
}
.gc-size-sm .gc-toolbar__splitter::after {
  height: 15px;
  margin: 7.5px 0;
}

.gc-size-lg .gc-toolbar__splitter {
  height: 50px;
}
.gc-size-lg .gc-toolbar__splitter::after {
  height: 25px;
  margin: 12.5px 0;
}

.gc-tabs {
  display: inline-block;
}
.gc-tabs--block {
  display: block;
}
.gc-tabs--simplified .gc-btn:after, .gc-tabs--transparent .gc-btn:after {
  position: absolute;
  z-index: 1040;
  bottom: 0;
  left: 50%;
  display: block;
  width: 0;
  height: 1px;
  content: "";
  transition: width 0.2s ease-in-out;
  transform: translateX(-50%);
  border-radius: 4px;
}
.gc-tabs--simplified .gc-btn.selected:after, .gc-tabs--transparent .gc-btn.selected:after {
  width: calc(100% - 15px);
  height: 2px;
}
.gc-tabs--simplified .gc-btn:not([disabled]):not(.disabled):hover:after, .gc-tabs--transparent .gc-btn:not([disabled]):not(.disabled):hover:after {
  width: 100%;
}
.gc-tabs--simplified .gc-btn:after {
  background-color: var(--cui-accent-text);
}
.gc-tabs--simplified .gc-btn:not([disabled]):not(.disabled):hover {
  color: var(--cui-accent-text-hover);
  background-color: transparent;
}
.gc-tabs--simplified .gc-btn:not([disabled]):not(.disabled):hover:after {
  background-color: var(--cui-accent-text-hover);
}
.gc-tabs--transparent .gc-btn {
  color: var(--cui-contrast-text-semi-40);
}
.gc-tabs--transparent .gc-btn:after {
  background-color: var(--cui-contrast-text);
}
.gc-tabs--transparent .gc-btn:not([disabled]):not(.disabled):hover {
  color: var(--cui-contrast-text);
  background-color: transparent;
}
.gc-tabs--transparent .gc-btn.selected {
  color: var(--cui-contrast-text);
  background-color: transparent;
}

@media (any-hover: none) {
  .gc-tabs--simplified .gc-btn:not([disabled]):not(.disabled):hover:after, .gc-tabs--transparent .gc-btn:not([disabled]):not(.disabled):hover:after {
    width: 0;
  }
  .gc-tabs--simplified .gc-btn:not([disabled]):not(.disabled):hover {
    color: var(--cui-accent-text);
  }
  .gc-tabs--simplified .gc-btn:not([disabled]):not(.disabled):hover:after {
    background-color: var(--cui-accent-text);
  }
  .gc-tabs--transparent .gc-btn:not([disabled]):not(.disabled):hover {
    color: var(--cui-contrast-text-semi-40);
  }
}
.gc-input, input[type=text].gc-input, input[type=password].gc-input, input[type=email].gc-input, input[type=url].gc-input {
  box-sizing: border-box;
  height: 40px;
  padding: 0 10px;
  transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, opacity 0.2s ease-in-out, border-color 0.2s ease-in-out;
  vertical-align: top;
  color: var(--cui-text);
  border: 1px solid transparent;
  border-radius: var(--cui-border-radius);
  outline-offset: var(--cui-outline-offset);
  background-color: var(--cui-input-bg);
  box-shadow: none;
  font-family: var(--cui-text-family);
  font-size: var(--cui-text-size);
  line-height: 38px;
}
.gc-input:not([disabled]):hover, input[type=text].gc-input:not([disabled]):hover, input[type=password].gc-input:not([disabled]):hover, input[type=email].gc-input:not([disabled]):hover, input[type=url].gc-input:not([disabled]):hover {
  background-color: var(--cui-input-bg-hover);
}
.gc-input:not([disabled]):focus, input[type=text].gc-input:not([disabled]):focus, input[type=password].gc-input:not([disabled]):focus, input[type=email].gc-input:not([disabled]):focus, input[type=url].gc-input:not([disabled]):focus {
  outline: var(--cui-outline);
  background-color: var(--cui-input-bg-hover);
  box-shadow: none;
}
.gc-input::placeholder, input[type=text].gc-input::placeholder, input[type=password].gc-input::placeholder, input[type=email].gc-input::placeholder, input[type=url].gc-input::placeholder {
  color: var(--cui-text-semi-40);
  font-weight: normal;
}
.gc-input--block, input[type=text].gc-input--block, input[type=password].gc-input--block, input[type=email].gc-input--block, input[type=url].gc-input--block {
  display: block;
}
.gc-input--invalid, input[type=text].gc-input--invalid, input[type=password].gc-input--invalid, input[type=email].gc-input--invalid, input[type=url].gc-input--invalid {
  border-color: var(--cui-accent-error);
}
.gc-input--invalid:not([disabled]):focus, input[type=text].gc-input--invalid:not([disabled]):focus, input[type=password].gc-input--invalid:not([disabled]):focus, input[type=email].gc-input--invalid:not([disabled]):focus, input[type=url].gc-input--invalid:not([disabled]):focus {
  border-color: var(--cui-accent-error);
  outline: var(--cui-outline-error);
}
.gc-input--invalid:not([disabled]):hover, input[type=text].gc-input--invalid:not([disabled]):hover, input[type=password].gc-input--invalid:not([disabled]):hover, input[type=email].gc-input--invalid:not([disabled]):hover, input[type=url].gc-input--invalid:not([disabled]):hover {
  border-color: var(--cui-accent-error);
}
.gc-input--text-align-center, input[type=text].gc-input--text-align-center, input[type=password].gc-input--text-align-center, input[type=email].gc-input--text-align-center, input[type=url].gc-input--text-align-center {
  text-align: center;
}
.gc-input--text-align-right, input[type=text].gc-input--text-align-right, input[type=password].gc-input--text-align-right, input[type=email].gc-input--text-align-right, input[type=url].gc-input--text-align-right {
  text-align: right;
}
.gc-input[disabled], input[type=text].gc-input[disabled], input[type=password].gc-input[disabled], input[type=email].gc-input[disabled], input[type=url].gc-input[disabled] {
  cursor: default;
  color: var(--cui-text-semi-40);
}
.gc-input[disabled]::placeholder, input[type=text].gc-input[disabled]::placeholder, input[type=password].gc-input[disabled]::placeholder, input[type=email].gc-input[disabled]::placeholder, input[type=url].gc-input[disabled]::placeholder {
  color: var(--cui-text-semi-40);
}
.gc-input.gc-size-sm, input[type=text].gc-input.gc-size-sm, input[type=password].gc-input.gc-size-sm, input[type=email].gc-input.gc-size-sm, input[type=url].gc-input.gc-size-sm {
  height: 30px;
  line-height: 30px;
}
.gc-input.gc-size-lg, input[type=text].gc-input.gc-size-lg, input[type=password].gc-input.gc-size-lg, input[type=email].gc-input.gc-size-lg, input[type=url].gc-input.gc-size-lg {
  height: 50px;
  line-height: 50px;
}
.gc-input.gc-size-xl, input[type=text].gc-input.gc-size-xl, input[type=password].gc-input.gc-size-xl, input[type=email].gc-input.gc-size-xl, input[type=url].gc-input.gc-size-xl {
  height: 60px;
  line-height: 60px;
}

textarea.gc-input {
  height: initial;
}

@media (any-hover: none) {
  .gc-input:not([disabled]):hover, input[type=text].gc-input:not([disabled]):hover, input[type=password].gc-input:not([disabled]):hover, input[type=email].gc-input:not([disabled]):hover, input[type=url].gc-input:not([disabled]):hover {
    background-color: var(--cui-input-bg);
  }
}
.gc-textarea, textarea.gc-textarea {
  box-sizing: border-box;
  margin: 0;
  padding: 6.5px 15px;
  resize: none;
  transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, opacity 0.2s ease-in-out, border-color 0.2s ease-in-out;
  vertical-align: top;
  color: var(--cui-text);
  border: 1px solid transparent;
  border-radius: var(--cui-border-radius);
  outline-offset: var(--cui-outline-offset);
  background-color: var(--cui-input-bg);
  box-shadow: none;
  font-family: var(--cui-text-family);
  font-size: var(--cui-text-size);
  line-height: 25px;
}
.gc-textarea:not([rows]), textarea.gc-textarea:not([rows]) {
  height: 40px;
}
.gc-textarea:not([disabled]):hover, textarea.gc-textarea:not([disabled]):hover {
  background-color: var(--cui-input-bg-hover);
}
.gc-textarea:not([disabled]):focus, textarea.gc-textarea:not([disabled]):focus {
  outline: var(--cui-outline);
  background-color: var(--cui-input-bg-hover);
  box-shadow: none;
}
.gc-textarea::placeholder, textarea.gc-textarea::placeholder {
  color: var(--cui-text-semi-40);
  font-weight: normal;
}
.gc-textarea--block, textarea.gc-textarea--block {
  display: block;
}
.gc-textarea--resize-vertical, textarea.gc-textarea--resize-vertical {
  resize: vertical;
}
.gc-textarea--resize-both, textarea.gc-textarea--resize-both {
  resize: both;
}
.gc-textarea--invalid, textarea.gc-textarea--invalid {
  border-color: var(--cui-accent-error);
}
.gc-textarea--invalid:not([disabled]):focus, textarea.gc-textarea--invalid:not([disabled]):focus {
  border-color: var(--cui-accent-error);
  outline: var(--cui-outline-error);
}
.gc-textarea--invalid:not([disabled]):hover, textarea.gc-textarea--invalid:not([disabled]):hover {
  border-color: var(--cui-accent-error);
}
.gc-textarea--text-align-center, textarea.gc-textarea--text-align-center {
  text-align: center;
}
.gc-textarea--text-align-right, textarea.gc-textarea--text-align-right {
  text-align: right;
}
.gc-textarea[disabled], textarea.gc-textarea[disabled] {
  cursor: default;
  color: var(--cui-text-semi-40);
}
.gc-textarea[disabled]::placeholder, textarea.gc-textarea[disabled]::placeholder {
  color: var(--cui-text-semi-40);
}
.gc-textarea--size-sm, textarea.gc-textarea--size-sm {
  padding: 4px 15px;
  line-height: 20px;
}
.gc-textarea--size-sm:not([rows]), textarea.gc-textarea--size-sm:not([rows]) {
  height: 30px;
}
.gc-textarea--size-lg, textarea.gc-textarea--size-lg {
  padding: 9px 15px;
  line-height: 30px;
}
.gc-textarea--size-lg:not([rows]), textarea.gc-textarea--size-lg:not([rows]) {
  height: 50px;
}

@media (any-hover: none) {
  .gc-textarea:not([disabled]):hover, textarea.gc-textarea:not([disabled]):hover {
    background-color: var(--cui-input-bg);
  }
}
.gc-label {
  display: -ms-inline-grid;
  display: inline-grid;
  vertical-align: top;
  -ms-grid-columns: minmax(0, 1fr);
  -ms-grid-rows: minmax(0, auto) minmax(0, 1fr);
  grid-template-columns: minmax(0, 1fr);
  grid-template-rows: minmax(0, auto) minmax(0, 1fr);
  grid-template-areas: "label" "content";
}
.gc-label *, .gc-label *:before, .gc-label *:after {
  box-sizing: border-box;
}
.gc-label .gc-label__label {
  width: 100%;
  grid-area: label;
  -ms-grid-row: 1;
  -ms-grid-row-span: 1;
  -ms-grid-column: 1;
  -ms-grid-column-span: 1;
}
.gc-label .gc-label__content {
  grid-area: content;
  -ms-grid-row: 2;
  -ms-grid-row-span: 1;
  -ms-grid-column: 1;
  -ms-grid-column-span: 1;
}
.gc-label--block {
  display: -ms-grid;
  display: grid;
}
.gc-label--position-left, .gc-label--position-right {
  -ms-grid-columns: 33% 67%;
  -ms-grid-rows: minmax(0, 1fr);
  grid-template-columns: 33% 67%;
  grid-template-rows: minmax(0, 1fr);
  grid-template-areas: "label content";
}
.gc-label--position-left .gc-label__label, .gc-label--position-right .gc-label__label {
  grid-area: label;
  -ms-grid-row: 1;
  -ms-grid-row-span: 1;
  -ms-grid-column: 1;
  -ms-grid-column-span: 1;
}
.gc-label--position-left .gc-label__content, .gc-label--position-right .gc-label__content {
  width: 100%;
  grid-area: content;
  -ms-grid-row: 1;
  -ms-grid-row-span: 1;
  -ms-grid-column: 2;
  -ms-grid-column-span: 1;
}
.gc-label--position-left .gc-label__content > *, .gc-label--position-right .gc-label__content > * {
  width: 100%;
}
.gc-label--position-left.gc-label--width-half, .gc-label--position-right.gc-label--width-half {
  -ms-grid-columns: minmax(0, 1fr) minmax(0, 1fr);
  grid-template-columns: 50% 50%;
}
.gc-label--position-left.gc-label--width-auto, .gc-label--position-right.gc-label--width-auto {
  -ms-grid-columns: minmax(33%, auto) minmax(50%, 1fr);
  grid-template-columns: minmax(33%, auto) minmax(50%, 1fr);
}
.gc-label--position-left.gc-label--width-auto .gc-label__label, .gc-label--position-right.gc-label--width-auto .gc-label__label {
  width: auto;
}
.gc-label--position-left.gc-label--width-auto .gc-label__content, .gc-label--position-right.gc-label--width-auto .gc-label__content {
  width: auto;
}
.gc-label--position-left .gc-label__label {
  padding-right: 15px;
}
.gc-label--position-right {
  -ms-grid-columns: 67% 33%;
  -ms-grid-rows: minmax(0, 1fr);
  grid-template-columns: 67% 33%;
  grid-template-rows: minmax(0, 1fr);
  grid-template-areas: "content label";
}
.gc-label--position-right .gc-label__label {
  padding-left: 15px;
  text-align: right;
  -ms-grid-row: 1;
  -ms-grid-row-span: 1;
  -ms-grid-column: 2;
  -ms-grid-column-span: 1;
}
.gc-label--position-right .gc-label__content {
  -ms-grid-row: 1;
  -ms-grid-row-span: 1;
  -ms-grid-column: 1;
  -ms-grid-column-span: 1;
}
.gc-label--position-right.gc-label--width-auto {
  -ms-grid-columns: minmax(50%, 1fr) minmax(33%, auto);
  grid-template-columns: minmax(50%, 1fr) minmax(33%, auto);
}
.gc-label--position-bottom {
  -ms-grid-rows: minmax(0, 1fr) minmax(0, auto);
  grid-template-rows: minmax(0, 1fr) minmax(0, auto);
  grid-template-areas: "content" "label";
}
.gc-label--position-bottom .gc-label__label {
  -ms-grid-row: 2;
  -ms-grid-row-span: 1;
  -ms-grid-column: 1;
  -ms-grid-column-span: 1;
}
.gc-label--position-bottom .gc-label__content {
  -ms-grid-row: 1;
  -ms-grid-row-span: 1;
  -ms-grid-column: 1;
  -ms-grid-column-span: 1;
}
.gc-label--alignment-left .gc-label__label {
  text-align: left;
}
.gc-label--alignment-right .gc-label__label {
  text-align: right;
}
.gc-label--alignment-center .gc-label__label {
  text-align: center;
}
.gc-label--with-tooltip .gc-label__label {
  padding-right: 20px;
}
.gc-label--with-icon .gc-label__label {
  padding: 0;
}
.gc-label--disabled .gc-label__label {
  opacity: 0.38;
}

.gc-label--with-tooltip.gc-label--position-right .gc-label__label {
  padding-right: 0;
  padding-left: 20px;
}
.gc-label--with-tooltip.gc-label--position-right .gc-label__label > .gc-label__tooltip {
  right: initial;
  left: 0;
}

.gc-label__label {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 40px;
  margin: 0;
  padding: 0;
  -webkit-user-select: none;
  user-select: none;
  flex-wrap: nowrap;
  justify-content: center;
}
.gc-label__label > span {
  display: -webkit-box;
  overflow: hidden;
  flex: 0 0 auto;
  max-height: 30px;
  vertical-align: middle;
  text-overflow: ellipsis;
  font-size: var(--cui-text-size);
  line-height: 15px;
  /*
  		HACK: [ARD-4441] -webkit-line-clamp is supported in 97% of browsers and 100% in those we are required to support
  		It is clear that -webkit-line-clamp is a hack, but for now, there are no other options https://caniuse.com/?search=line-clamp
  		*/
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.gc-label__label > span > b {
  color: var(--cui-accent);
}

.gc-label--size-small .gc-label__label {
  height: 30px;
}
.gc-label--size-small .gc-label__icon {
  width: 30px;
  height: 30px;
}
.gc-label--size-large .gc-label__label {
  height: 50px;
}
.gc-label--size-large .gc-label__icon {
  width: 50px;
  height: 50px;
}

.gc-label__tooltip {
  position: absolute;
  top: 50%;
  right: 0;
  width: 20px;
  height: 20px;
  transform: translateY(-50%);
}
.gc-label__tooltip .gc-icon {
  display: flex;
  width: 20px;
  height: 20px;
  justify-content: center;
  align-items: center;
}

.gc-label__icon {
  display: flex;
  width: 40px;
  height: 40px;
  justify-content: center;
  align-items: center;
}

.gc-appbar {
  display: flex;
  height: 40px;
}
.gc-appbar--legacy .gc-appbar__title {
  color: var(--cui-contrast-text);
}
.gc-appbar--legacy .gc-appbar__subtitle {
  color: var(--cui-contrast-text-semi-40);
}
.gc-appbar--legacy .gc-appbar__badge--accent {
  background-color: var(--cui-accent-hover);
}
.gc-appbar--legacy .gc-menu__logo {
  height: 50px;
}

.gc-appbar__logo-container {
  position: relative;
  flex: 0 0 auto;
  height: 100%;
}

.gc-appbar__main-section {
  flex: 0 0 auto;
  height: 100%;
}

.gc-appbar__title-section {
  display: flex;
  overflow: hidden;
  flex: 1 1 auto;
  width: 0;
  height: 100%;
  justify-content: center;
  align-items: center;
}

.gc-appbar__control-section {
  flex: 0 0 auto;
  height: 100%;
}

.gc-appbar__badge {
  display: block;
  flex: 0 0 auto;
  height: 20px;
  margin-left: 15px;
  padding: 0 15px;
  color: var(--cui-text);
  border-radius: 10px;
  background-color: var(--cui-bg-panels-border);
  font-size: var(--cui-text-size);
  line-height: 20px;
}
.gc-appbar__badge--error {
  color: var(--cui-contrast-text);
  background-color: var(--cui-accent-error);
}
.gc-appbar__badge--warning {
  color: var(--cui-contrast-text);
  background-color: var(--cui-accent-warning);
}
.gc-appbar__badge--accent {
  color: var(--cui-contrast-text);
  background-color: var(--cui-accent);
}
.gc-appbar__badge--clickable {
  cursor: default;
}

.gc-appbar__title {
  display: block;
  flex: 0 0 auto;
  color: var(--cui-text);
  font-size: var(--cui-text-size);
}

.gc-appbar__subtitle {
  display: block;
  flex: 0 0 auto;
  padding-left: 15px;
  color: var(--cui-text-semi-60);
  font-size: var(--cui-text-size);
  font-style: italic;
}

.gc-menu__splitter {
  display: block;
  overflow: hidden;
  width: 100%;
  height: 1px;
}
.gc-menu__splitter::after {
  display: block;
  width: calc(100% - 20px);
  height: 1px;
  margin-left: 10px;
  content: " ";
  background-color: var(--cui-menu-splitter);
}

.gc-menu__logo {
  width: 40px;
  height: 41px;
  transition: width 0.2s ease-in-out, background-color 0.2s ease-in-out;
  background-color: var(--cui-accent);
}
.gc-menu__logo--menu-size-small {
  width: 30px;
}
.gc-menu__logo--menu-size-large {
  width: 50px;
}
.gc-menu__logo--size-small {
  height: 31px;
}
.gc-menu__logo--size-large {
  height: 51px;
}
.gc-menu__logo--drawer {
  position: absolute;
  z-index: 1011;
  top: 0;
  left: 0;
}
.gc-menu__logo--expanded {
  width: 180px;
}

.gc-menu__logo-placeholder {
  width: 40px;
}
.gc-menu__logo-placeholder--size-small {
  width: 30px;
}
.gc-menu__logo-placeholder--size-large {
  width: 50px;
}

.gc-menu__btn-container {
  position: relative;
  z-index: 1;
  display: flex;
  overflow: hidden;
  flex-direction: column;
  width: 40px;
  height: 100%;
  transition: width 0.2s ease-in-out, background-color 0.2s ease-in-out;
  background-color: var(--cui-accent);
}

.gc-menu__scrollable-buttons {
  position: relative;
  flex: 1 1 auto;
}

.gc-menu__panel-toggle {
  position: relative;
}
.gc-menu__panel-toggle .gc-btn__icon {
  transition: transform 0.2s ease-in-out;
}
.gc-menu__panel-toggle-chevron {
  position: absolute;
  top: 50%;
  left: 20px;
  transition: transform 0.2s ease-in-out, opacity 0.2s ease-in-out;
  transform: translate(-50%, -50%);
  pointer-events: none;
  opacity: 0;
  color: var(--cui-contrast-text-semi-40);
}
.gc-menu__panel-toggle-chevron--accent {
  color: var(--cui-accent-semi-60);
}
.gc-menu__panel-toggle:hover:not(.gc-menu__panel-toggle--no-chevron):not(.gc-menu__panel-toggle--disabled) .gc-btn__icon, .gc-menu__panel-toggle--active:not(.gc-menu__panel-toggle--no-chevron) .gc-btn__icon {
  transform: translateX(-5px);
}
.gc-menu__panel-toggle:hover:not(.gc-menu__panel-toggle--no-chevron):not(.gc-menu__panel-toggle--disabled) .gc-menu__panel-toggle-chevron, .gc-menu__panel-toggle--active:not(.gc-menu__panel-toggle--no-chevron) .gc-menu__panel-toggle-chevron {
  transform: translate(-50%, -50%) translateX(12px);
  opacity: 1;
}
.gc-menu__panel-toggle:hover:not(.gc-menu__panel-toggle--no-chevron):not(.gc-menu__panel-toggle--disabled).gc-menu__panel-toggle--size-small .gc-btn__icon, .gc-menu__panel-toggle--active:not(.gc-menu__panel-toggle--no-chevron).gc-menu__panel-toggle--size-small .gc-btn__icon {
  transform: translateX(-3px);
}
.gc-menu__panel-toggle:hover:not(.gc-menu__panel-toggle--no-chevron):not(.gc-menu__panel-toggle--disabled).gc-menu__panel-toggle--size-small .gc-menu__panel-toggle-chevron, .gc-menu__panel-toggle--active:not(.gc-menu__panel-toggle--no-chevron).gc-menu__panel-toggle--size-small .gc-menu__panel-toggle-chevron {
  transform: translate(-50%, -50%) translateX(9px);
  opacity: 1;
}
.gc-menu__panel-toggle--size-small .gc-menu__panel-toggle-chevron {
  left: 15px;
}
.gc-menu__panel-toggle--size-large .gc-menu__panel-toggle-chevron {
  left: 25px;
}

.gc-menu-panel {
  overflow-x: hidden;
}
.gc-menu-panel--fill-container {
  overflow: hidden;
  height: 100%;
}
.gc-menu-panel--notifications > .gc-notification-details {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}
.gc-menu-panel--notifications > .gc-notification-details .gc-notification-details__content {
  flex: 1 0 auto;
  padding-bottom: 0;
  order: 2;
}
.gc-menu-panel--notifications > .gc-notification-details .gc-notification-details__action {
  padding-bottom: 10px;
  order: 1;
}
.gc-menu-panel__placeholder {
  padding: 15px;
  text-align: center;
  color: var(--cui-text-semi-60);
  font-size: var(--cui-text-size);
  line-height: 30px;
}

.gc-menu__panel-container {
  width: 230px;
  height: 100%;
  margin-left: -230px;
  transition: margin 0.2s ease-in-out;
  background-color: var(--cui-bg-panels);
  will-change: margin;
}
.gc-menu__panel-container--visible {
  margin-left: 0 !important;
}

@keyframes gc-pin-appear-animation {
  0% {
    transform: scale(0, 0);
    opacity: 0;
  }
  50% {
    transform: scale(0, 0);
    opacity: 0;
  }
  100% {
    transform: scale(1, 1);
    opacity: 1;
  }
}
@keyframes gc-pin-feedback {
  50% {
    transform: scale(0.7);
  }
}
.gc-menu__panel-header {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 40px;
  margin: 0;
  padding: 0 40px 0 15px;
  white-space: nowrap;
  text-transform: uppercase;
  color: var(--cui-accent-text);
  box-shadow: 0 -1px 0 0 var(--cui-bg-panels-border) inset;
  font-size: var(--cui-text-size);
  font-weight: bold;
  line-height: 40px;
}
.gc-menu__panel-header > .gc-btn-pin {
  position: absolute;
  right: 5px;
  bottom: 5px;
  animation-name: gc-pin-appear-animation;
  animation-duration: 0.4s;
  animation-timing-function: ease-in-out;
}
.gc-menu__panel-header > .gc-btn-pin .gc-btn__icon {
  transition: transform 0.2s ease-in-out;
}
.gc-menu__panel-header > .gc-btn-pin--pinned .gc-btn__icon {
  animation: gc-pin-feedback 0.2s ease-out;
}

.gc-menu__panel-content {
  height: calc(100% - 40px);
  box-shadow: -1px 0 0 0 var(--cui-bg-panels-border) inset;
}

.gc-menu {
  position: relative;
  z-index: 1010;
  display: flex;
  height: 100%;
}
.gc-menu *, .gc-menu *:before, .gc-menu *:after {
  box-sizing: border-box;
}
.gc-menu--drawer {
  width: 40px;
}
.gc-menu--drawer .gc-menu__btn-container {
  position: absolute;
  top: 0;
  left: 0;
}
.gc-menu--drawer .gc-menu__panel-container {
  position: absolute;
  top: 0;
  left: 40px;
}
.gc-menu--drawer.gc-menu--size-small {
  width: 30px;
}
.gc-menu--drawer.gc-menu--size-small .gc-menu__panel-container {
  left: 30px;
}
.gc-menu--drawer.gc-menu--size-large {
  width: 50px;
}
.gc-menu--drawer.gc-menu--size-large .gc-menu__panel-container {
  left: 50px;
}
.gc-menu--size-small .gc-menu__btn-container {
  width: 30px;
}
.gc-menu--size-small .gc-menu__panel-header {
  height: 30px;
  padding: 0 30px 0 15px;
  line-height: 30px;
}
.gc-menu--size-small .gc-menu__panel-header > .gc-btn-pin {
  right: 0;
  bottom: 0;
}
.gc-menu--size-small .gc-menu__panel-content {
  height: calc(100% - 30px);
}
.gc-menu--size-large .gc-menu__btn-container {
  width: 50px;
}
.gc-menu--size-large .gc-menu__panel-header {
  height: 50px;
  padding: 0 50px 0 15px;
  line-height: 50px;
}
.gc-menu--size-large .gc-menu__panel-content {
  height: calc(100% - 50px);
}
.gc-menu--expanded .gc-menu__btn-container {
  width: 180px;
}
.gc-menu--legacy .gc-menu__btn-container {
  background-color: var(--cui-bg-panels);
}
.gc-menu--legacy .gc-menu__splitter:after {
  background-color: var(--cui-bg-panels-border);
}

@media (any-hover: none) {
  .gc-menu__panel-toggle:hover:not(.gc-menu__panel-toggle--no-chevron):not(.gc-menu__panel-toggle--disabled) .gc-btn__icon, .gc-menu__panel-toggle--active:not(.gc-menu__panel-toggle--no-chevron) .gc-btn__icon {
    transform: none;
  }
  .gc-menu__panel-toggle:hover:not(.gc-menu__panel-toggle--no-chevron):not(.gc-menu__panel-toggle--disabled) .gc-menu__panel-toggle-chevron, .gc-menu__panel-toggle--active:not(.gc-menu__panel-toggle--no-chevron) .gc-menu__panel-toggle-chevron {
    transform: translate(-50%, -50%);
    opacity: 0;
  }
  .gc-menu__panel-toggle:hover:not(.gc-menu__panel-toggle--no-chevron):not(.gc-menu__panel-toggle--disabled).gc-menu__panel-toggle--size-small .gc-btn__icon, .gc-menu__panel-toggle--active:not(.gc-menu__panel-toggle--no-chevron).gc-menu__panel-toggle--size-small .gc-btn__icon {
    transform: none;
  }
  .gc-menu__panel-toggle:hover:not(.gc-menu__panel-toggle--no-chevron):not(.gc-menu__panel-toggle--disabled).gc-menu__panel-toggle--size-small .gc-menu__panel-toggle-chevron, .gc-menu__panel-toggle--active:not(.gc-menu__panel-toggle--no-chevron).gc-menu__panel-toggle--size-small .gc-menu__panel-toggle-chevron {
    transform: translate(-50%, -50%);
    opacity: 0;
  }
}
.gc-sidebar {
  position: relative;
  flex: 1 0 auto;
  height: 100%;
  transition: width 0.2s ease-in-out;
}
.gc-sidebar > .gc-sidebar__menu {
  position: absolute;
  top: 0;
  right: 0;
}
.gc-sidebar[class*="--size"] > .gc-sidebar__menu > .gc-tabs {
  max-width: calc(100% - 30px);
}
.gc-sidebar--collapsed {
  width: 0 !important;
}
.gc-sidebar--collapsed .gc-sidebar__container {
  overflow: visible;
}
.gc-sidebar--collapsed .gc-sidebar__toggle-area {
  left: -19px;
  width: 19px;
  pointer-events: none;
}
.gc-sidebar--collapsed .gc-sidebar__toggle-area-button {
  top: calc(50% - 50px/2);
  height: 50px;
  pointer-events: all;
}
.gc-sidebar--collapsed .gc-sidebar__toggle-area-button:before {
  top: 18px;
  left: 35%;
}
.gc-sidebar--collapsed .gc-sidebar__toggle-area-button:after {
  top: 24px;
  left: 35%;
}
.gc-sidebar--collapsed .gc-sidebar__toggle-area-button:hover:before {
  transform: rotate(40deg);
}
.gc-sidebar--collapsed .gc-sidebar__toggle-area-button:hover:after {
  transform: rotate(-40deg);
}
.gc-sidebar--collapsed .gc-sidebar__toggle-area-bar {
  top: calc(50% - 50px/2);
  right: 0;
  left: auto;
  height: 50px;
  margin: 0;
}
.gc-sidebar--size-sm > .gc-sidebar__menu + .gc-sidebar__container {
  padding-top: 30px;
}
.gc-sidebar--size-sm > .gc-sidebar__menu + .gc-sidebar__container .gc-sidebar__toggle-area {
  height: calc(100% - 30px);
}
.gc-sidebar--size-md > .gc-sidebar__menu + .gc-sidebar__container {
  padding-top: 40px;
}
.gc-sidebar--size-md > .gc-sidebar__menu + .gc-sidebar__container .gc-sidebar__toggle-area {
  height: calc(100% - 40px);
}

.gc-sidebar__menu {
  display: flex;
  box-sizing: border-box;
  padding-right: 5px;
  transition: width 0.2s ease-in-out;
  justify-content: space-between;
  align-items: center;
}
.gc-sidebar__menu + .gc-sidebar__container {
  padding-top: 50px;
}
.gc-sidebar__menu + .gc-sidebar__container .gc-sidebar__toggle-area {
  height: calc(100% - 50px);
}
.gc-sidebar__menu > .gc-tabs {
  overflow: hidden;
  max-width: calc(100% - 40px);
}
.gc-sidebar__menu--hidden-toggles {
  justify-content: flex-end;
}
.gc-sidebar__menu .ci-sidebar-toggle__arrow {
  transition: transform 0.2s ease-in-out;
  transform-origin: center center;
}
.gc-sidebar__menu--collapsed .ci-sidebar-toggle__arrow {
  transform: scale(-1) translateX(4px);
}

.gc-sidebar__container {
  height: 100%;
  background-color: var(--cui-bg-panels);
  box-shadow: 1px 0 0 0 var(--cui-bg-panels-border) inset;
}

.gc-sidebar-panel {
  overflow-x: hidden;
}
.gc-sidebar-panel--fill-container {
  overflow: hidden;
  height: 100%;
}
.gc-sidebar-panel--notifications > .gc-notification-details {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}
.gc-sidebar-panel--notifications > .gc-notification-details .gc-notification-details__content {
  flex: 1 0 auto;
  padding-bottom: 0;
  order: 2;
}
.gc-sidebar-panel--notifications > .gc-notification-details .gc-notification-details__action {
  padding-bottom: 10px;
  order: 1;
}
.gc-sidebar-panel__placeholder {
  padding: 15px;
  text-align: center;
  color: var(--cui-text-semi-60);
  font-size: var(--cui-text-size);
  line-height: 30px;
}

.gc-sidebar__toggle-area {
  position: absolute;
  z-index: 1;
  bottom: 0;
  left: 0;
  width: 15px;
  height: 100%;
}
.gc-sidebar__toggle-area:hover .gc-sidebar__toggle-area-button {
  opacity: 1;
}
.gc-sidebar__toggle-area-button {
  position: absolute;
  top: calc(50% - 36px/2);
  left: 0;
  width: 100%;
  height: 36px;
  cursor: pointer;
  transition: opacity 0.2s ease-in-out;
  opacity: 0;
  border: none;
  outline: none;
  background: none;
}
.gc-sidebar__toggle-area-button:before, .gc-sidebar__toggle-area-button:after {
  position: absolute;
  left: 50%;
  width: 2px;
  height: 8px;
  content: "";
  transition: transform 0.2s ease-in-out, opacity 0.3s ease-in-out;
  transform: rotate(0deg);
  background-color: var(--cui-accent);
}
.gc-sidebar__toggle-area-button:before {
  top: 10px;
  transform-origin: 1px 7px;
}
.gc-sidebar__toggle-area-button:after {
  top: 16px;
  transform-origin: 1px 1px;
}
.gc-sidebar__toggle-area-button:hover:before {
  transform: rotate(-40deg);
}
.gc-sidebar__toggle-area-button:hover:after {
  transform: rotate(40deg);
}
.gc-sidebar__toggle-area-button:hover + .gc-sidebar__toggle-area-bar {
  transform: scaleY(1);
}
.gc-sidebar__toggle-area-bar {
  position: absolute;
  top: 0;
  left: -2px;
  width: 2px;
  height: calc(100% - 15px);
  margin: 7.5px 0;
  transition: transform 0.2s ease-in-out;
  transform: scaleY(0);
  transform-origin: center;
  pointer-events: none;
  background-color: var(--cui-accent);
}

@media (any-hover: none) {
  .gc-sidebar--collapsed .gc-sidebar__toggle-area-button:hover:before {
    transform: none;
  }
  .gc-sidebar--collapsed .gc-sidebar__toggle-area-button:hover:after {
    transform: none;
  }

  .gc-sidebar__toggle-area:hover .gc-sidebar__toggle-area-button {
    opacity: 0;
  }
  .gc-sidebar__toggle-area-button:hover:before {
    transform: none;
  }
  .gc-sidebar__toggle-area-button:hover:after {
    transform: none;
  }
  .gc-sidebar__toggle-area-button:hover + .gc-sidebar__toggle-area-bar {
    transform: none;
  }
}
.gc-combo {
  position: relative;
  display: inline-block;
  width: auto;
  height: 40px;
  vertical-align: top;
  color: #333;
  font-size: 12px;
}
.gc-combo *, .gc-combo *:before, .gc-combo *:after {
  box-sizing: border-box;
}
.gc-combo > .gc-input {
  width: 100%;
  text-overflow: ellipsis;
}
.gc-combo:not([class^=gc-size]) > .gc-input {
  padding-right: 40px;
}
.gc-combo--block {
  display: block;
}
.gc-combo--with-preview:not([class^=gc-size]) > .gc-input {
  padding-left: 40px;
}
.gc-combo--with-preview.gc-size-sm > .gc-input {
  padding-left: 30px;
}
.gc-combo--with-preview.gc-size-sm > .gc-combo__preview {
  width: 30px;
}
.gc-combo--with-preview.gc-size-lg > .gc-input {
  padding-left: 50px;
}
.gc-combo--with-preview.gc-size-lg > .gc-combo__preview {
  width: 50px;
}
.gc-combo--disabled .gc-combo__preview {
  opacity: 0.38;
}
.gc-combo--value-placeholder:not(.gc-combo--focused-input) > .gc-input::placeholder {
  color: var(--cui-text) !important;
}
.gc-combo__preview {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  width: 40px;
  height: 100%;
  padding: 5px;
  pointer-events: none;
  justify-content: center;
  align-items: center;
}
.gc-combo .gc-dd {
  position: absolute;
  top: 0;
  right: 0;
}
.gc-combo .gc-dd > .gc-btn {
  width: 40px;
  min-width: auto;
  background: transparent;
}
.gc-combo .gc-dd > .gc-dd__chevron {
  width: 40px;
}
.gc-combo.gc-size-sm > .gc-input {
  padding-right: 30px;
}
.gc-combo.gc-size-sm .gc-dd > .gc-btn {
  width: 30px;
}
.gc-combo.gc-size-sm .gc-dd > .gc-dd__chevron {
  width: 30px;
}
.gc-combo.gc-size-lg > .gc-input {
  padding-right: 50px;
}
.gc-combo.gc-size-lg .gc-dd > .gc-btn {
  width: 50px;
}
.gc-combo.gc-size-lg .gc-dd > .gc-dd__chevron {
  width: 50px;
}

.gc-portal-root--modal > .gc-modal-overlay {
  z-index: 1020;
}

.gc-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  overflow: hidden;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background-color: var(--cui-overlay);
  font-family: var(--cui-text-family);
  justify-content: center;
  align-items: center;
}
.gc-modal-overlay *, .gc-modal-overlay *:before, .gc-modal-overlay *:after {
  box-sizing: border-box;
}

.gc-modal--default {
  position: absolute;
  display: grid;
  width: 600px;
  border-radius: var(--cui-border-radius);
  outline: none;
  background-color: var(--cui-bg-panels);
  box-shadow: var(--cui-shadow);
  grid-template-rows: min-content 1fr min-content;
}
.gc-modal.gc-rounded {
  border-radius: 20px;
}
.gc-modal.gc-rounded .gc-modal__header {
  border-radius: 20px 20px 0 0;
}
.gc-modal.gc-rounded .gc-modal__footer {
  border-radius: 0 0 20px 20px;
}
.gc-modal.gc-rounded .gc-modal__resize-handle-wrapper {
  padding-right: 5px;
  padding-bottom: 5px;
}
.gc-modal.gc-rounded .gc-modal__resize-handle {
  width: 12px;
  height: 12px;
  border-radius: 0 0 17px 0;
}
.gc-modal--level-warning .gc-modal__header {
  background-color: var(--cui-accent-warning);
}
.gc-modal--level-error .gc-modal__header {
  background-color: var(--cui-accent-error);
}
.gc-modal--with-icon .gc-modal__header:not(.gc-modal__header--centered-title) {
  padding-left: 55px;
}
.gc-modal__header {
  display: grid;
  min-height: 60px;
  padding: 10px 10px 10px 15px;
  color: var(--cui-contrast-text);
  border-radius: var(--cui-border-radius) var(--cui-border-radius) 0 0;
  background-color: var(--cui-accent);
  align-items: center;
  grid-template-areas: "title title btn";
  grid-template-columns: 60px auto 60px;
}
.gc-modal__header--centered-title {
  position: relative;
  min-height: 60px;
  padding-right: 10px;
  grid-template-areas: ". title btn";
  grid-auto-columns: auto 40px;
}
.gc-modal__header--centered-title > .gc-modal__title-box {
  text-align: center;
}
.gc-modal__icon {
  position: absolute;
  top: 10px;
  left: 20px;
  display: flex;
  height: 40px;
  align-items: center;
}
.gc-modal__title-box {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  grid-area: title;
}
.gc-modal__title {
  overflow: hidden;
  margin: 0;
  text-overflow: ellipsis;
  font-weight: normal;
  line-height: 20px;
}
.gc-modal__subtitle {
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--cui-contrast-text-semi-40);
  font-size: var(--cui-text-size-lg);
  font-style: italic;
}
.gc-modal__close-btn {
  margin: 0 0 auto auto;
  grid-area: btn;
}
.gc-modal__message {
  padding: 15px 15px 10px;
  overflow-wrap: anywhere;
  font-size: var(--cui-text-size-lg);
}
.gc-modal__content {
  display: flex;
  flex-direction: column;
}
.gc-modal__footer {
  position: relative;
  display: flex;
  padding: 10px;
  border-radius: 0 0 var(--cui-border-radius) var(--cui-border-radius);
  background-color: var(--cui-bg-body);
  align-items: center;
  justify-content: flex-end;
}
.gc-modal__footer > .gc-btn {
  min-width: 100px;
  text-align: center;
}
.gc-modal__footer > .gc-btn + .gc-btn {
  margin-left: 15px;
}
.gc-modal__footer--align-left {
  justify-content: flex-start;
}
.gc-modal__footer--align-center {
  justify-content: center;
}
.gc-modal__footer--align-spread {
  justify-content: space-between;
}
.gc-modal__right-control {
  margin-left: auto !important;
}
.gc-modal__resize-handle-wrapper {
  position: absolute;
  right: 0;
  bottom: 0;
  padding: 3px;
}
.gc-modal__resize-handle {
  width: 9px;
  height: 9px;
  transition: border-color 200ms;
  border-right: 2px dotted #acacac;
  border-bottom: 2px dotted #acacac;
}
.gc-modal__resize-handle-wrapper:hover .gc-modal__resize-handle {
  border-right: 2px solid var(--cui-accent);
  border-bottom: 2px solid var(--cui-accent);
}

.gc-message-box.gc-modal--with-icon .gc-modal__header:not(.gc-modal__header--centered-title) {
  padding-left: 45px;
}
.gc-message-box .gc-modal__header {
  position: relative;
  min-height: auto;
  padding: 0;
}
.gc-message-box .gc-modal__header--centered-title {
  min-height: auto;
}
.gc-message-box .gc-modal__header > .gc-modal__icon {
  top: 0;
  left: 10px;
}
.gc-message-box .gc-modal__header > .gc-modal__title-box {
  padding: 10px;
}
.gc-message-box .gc-modal__footer {
  background-color: transparent;
}

.gc-accent-color {
  color: var(--cui-accent-text);
}

.gc-accent-background {
  background-color: var(--cui-accent);
}

.gc-accent-fill {
  fill: var(--cui-accent);
}

.gc-secondary-accent-fill {
  fill: var(--cui-accent-secondary);
}

.gc-contrast-fill {
  fill: var(--cui-contrast);
}

.gc-rounded, input[type=text].gc-rounded, input[type=password].gc-rounded, input[type=email].gc-rounded, input[type=url].gc-rounded, textarea.gc-rounded {
  border-radius: 20px;
}
.gc-rounded.gc-size-sm, input[type=text].gc-rounded.gc-size-sm, input[type=password].gc-rounded.gc-size-sm, input[type=email].gc-rounded.gc-size-sm, input[type=url].gc-rounded.gc-size-sm, textarea.gc-rounded.gc-size-sm {
  border-radius: 15px;
}
.gc-rounded.gc-size-lg, input[type=text].gc-rounded.gc-size-lg, input[type=password].gc-rounded.gc-size-lg, input[type=email].gc-rounded.gc-size-lg, input[type=url].gc-rounded.gc-size-lg, textarea.gc-rounded.gc-size-lg {
  border-radius: 25px;
}
.gc-rounded.gc-size-xl, input[type=text].gc-rounded.gc-size-xl, input[type=password].gc-rounded.gc-size-xl, input[type=email].gc-rounded.gc-size-xl, input[type=url].gc-rounded.gc-size-xl, textarea.gc-rounded.gc-size-xl {
  border-radius: 30px;
}

.gc-size-sm {
  height: 30px;
  line-height: 30px;
}

.gc-size-lg {
  height: 50px;
  line-height: 50px;
}

.gc-size-xl {
  height: 60px;
  line-height: 60px;
}

.gc-dd-adjustment {
  transition-timing-function: ease-out;
  transition-duration: 0.15s;
  transition-property: left, right, top;
}

.gc-property-grid {
  width: 100%;
  height: 100%;
  padding-top: 15px;
}
.gc-property-grid .gc-property-grid__header {
  position: relative;
  display: flex;
  margin: 0 15px 10px;
  border-radius: 4px;
  background-color: var(--cui-accent);
}
.gc-property-grid .gc-property-grid__header .gc-btn.gc-property-grid__button {
  width: 100%;
  padding: 0;
}
.gc-property-grid .gc-property-grid__header .gc-btn.gc-property-grid__button > .gc-btn__text {
  width: 100%;
  padding: 0;
  transition: opacity 0.2s ease-in-out;
  text-align: center;
}
.gc-property-grid .gc-property-grid__header .gc-btn.gc-property-grid__button > .gc-btn__icon {
  right: 0;
  left: auto;
}
.gc-property-grid .gc-property-grid__header .gc-btn.gc-property-grid__button--search-mode {
  position: static;
  width: auto;
  min-width: 60px;
  padding: 0 10px;
  border-radius: 4px 0 0 4px;
}
.gc-property-grid .gc-property-grid__header .gc-btn.gc-property-grid__button--search-mode > .gc-btn__text {
  position: static;
  opacity: 0.5;
  color: var(--cui-contrast-text);
  font-size: var(--cui-text-size-sm);
}
.gc-property-grid .gc-property-grid__header > .gc-property-grid__button-back {
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
}
.gc-property-grid .gc-property-grid__header > .gc-dd {
  position: absolute;
  z-index: 1;
  top: 0;
  right: 0;
}
.gc-property-grid .gc-property-grid__header > .gc-dd + .gc-btn {
  padding: 0 60px;
}
.gc-property-grid .gc-property-grid__header > .gc-dd + .gc-btn .gc-btn__icon {
  right: 30px;
}
.gc-property-grid .gc-property-grid__header .gc-btn > .gc-btn__icon {
  transition: color 0.2s ease-in-out, opacity 0.2s ease-in-out;
}
.gc-property-grid .gc-property-grid__header .gc-btn:not(:hover) > .gc-btn__icon {
  opacity: 0.5;
  color: var(--cui-contrast-text);
}
.gc-property-grid .gc-property-grid__header > .gc-input {
  flex: 1 0 auto;
  padding-right: 30px;
  text-overflow: ellipsis;
  color: var(--cui-contrast-text);
  border-radius: 0 4px 4px 0;
  background-color: var(--cui-accent);
}
.gc-property-grid .gc-property-grid__header > .gc-input::placeholder {
  color: var(--cui-contrast-text-semi-40);
}
.gc-property-grid .gc-property-grid__header > .gc-input:focus, .gc-property-grid .gc-property-grid__header > .gc-input:hover {
  border-color: transparent !important;
  background-color: var(--cui-accent-hover) !important;
}
.gc-property-grid .gc-property-grid__header--with-tabs:not([class*=search-view]) > .gc-tabs {
  display: flex;
  width: 0;
  padding-left: 30px;
  order: 1;
  flex-grow: 1;
}
.gc-property-grid .gc-property-grid__header--with-tabs:not([class*=search-view]) > .gc-tabs .gc-btn-group {
  flex: 1 1 auto;
  width: 0;
}
.gc-property-grid .gc-property-grid__header--with-tabs:not([class*=search-view]) > .gc-tabs .gc-btn {
  flex: 1 1 auto;
  width: 50%;
}
.gc-property-grid .gc-property-grid__header--with-tabs:not([class*=search-view]) > .gc-tabs .gc-btn:after {
  content: none;
}
.gc-property-grid .gc-property-grid__header--with-tabs:not([class*=search-view]) > .gc-property-grid__button {
  width: auto;
  padding: 0 !important;
  order: 2;
}
.gc-property-grid .gc-property-grid__header--with-tabs:not([class*=search-view]) > .gc-property-grid__button > .gc-btn__text {
  padding-left: 30px;
}
.gc-property-grid .gc-property-grid__header--with-tabs:not([class*=search-view]) > .gc-property-grid__button > .gc-btn__icon {
  right: 0;
}
.gc-property-grid .gc-property-grid__header--with-tabs:not([class*=search-view]) > .gc-dd {
  position: relative;
  order: 3;
}
.gc-property-grid .gc-property-grid__container {
  height: calc(100% - 40px);
}
.gc-property-grid .gc-property-category {
  padding: 0 15px;
}

.gc-property-list__placeholder {
  padding: 15px;
  text-align: center;
  color: var(--cui-text-semi-60);
  font-size: var(--cui-text-size);
  line-height: 30px;
}

.gc-property-grid .gc-property-list__placeholder {
  padding: 0 15px;
}

.gc-property-grid__container {
  overflow-x: hidden;
  height: 100%;
}

.gc-property-category {
  display: block;
  margin: 0;
  padding: 0;
  border: none;
}
.gc-property-category .gc-label__content {
  overflow: hidden;
}

.gc-property-category__flex-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.gc-property-category__content:empty + .gc-property-category__title {
  display: none;
}

.gc-property-category__title {
  order: 1;
}

.gc-property-category__content {
  margin-bottom: 5px;
  order: 2;
}
.gc-property-category__content:empty {
  margin-bottom: 0;
}
.gc-property-category__content > * {
  margin-bottom: 5px;
}

.gc-property-category__content--collapsed {
  display: none;
}

.gc-unknown-editor {
  height: 30px;
  margin-bottom: 5px;
  color: var(--cui-text-semi-60);
  font-size: var(--cui-text-size);
  line-height: 30px;
}

@media (any-hover: none) {
  .gc-property-grid .gc-property-grid__header .gc-btn:hover > .gc-btn__icon {
    opacity: 0.5;
    color: var(--cui-contrast-text);
  }
  .gc-property-grid .gc-property-grid__header > .gc-input:hover:not(:focus) {
    border-color: transparent !important;
    background-color: var(--cui-accent) !important;
  }
}
.gc-collection-editor {
  position: relative;
}
.gc-collection-editor--dragging * {
  user-select: none;
}
.gc-collection-editor--expanded {
  margin: 0 -15px 5px;
  padding: 0 15px 15px 15px;
  background-color: var(--cui-bg-panels-section);
}
.gc-collection-editor--expanded::before {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  width: 100%;
  height: 30px;
  content: "";
  background-color: var(--cui-bg-panels-section);
}
.gc-collection-editor--disabled .gc-collection-editor__main > .gc-collection-editor__counter {
  color: var(--cui-text-semi-40);
}
.gc-collection-editor--transparent {
  background-color: transparent;
}
.gc-collection-editor--transparent:before {
  content: none;
}
.gc-collection-editor__item {
  display: flex;
}
.gc-collection-editor__item > *:not(.gc-collection-editor__item-actions) {
  width: 0;
  flex-grow: 1;
}
.gc-collection-editor__item-actions {
  display: flex;
  margin-right: 5px;
  flex-shrink: 0;
}
.gc-collection-editor__item-actions > .gc-btn + .gc-btn {
  margin-left: 5px;
}
.gc-collection-editor__item + .gc-collection-editor__item {
  padding-top: 5px;
}

.gc-collection-editor__main {
  display: flex;
  justify-content: flex-end;
}
.gc-collection-editor__main > .gc-btn {
  flex-shrink: 0;
}
.gc-collection-editor__main > * + * {
  margin-left: 5px;
}
.gc-collection-editor__main .gc-collection-editor__counter {
  display: block;
  overflow: hidden;
  flex: 1 1 auto;
  width: 0;
  height: 30px;
  padding: 0 10px;
  cursor: default;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: var(--cui-text-semi-60);
  border-radius: 4px;
  background-color: var(--cui-input-bg);
  font-size: var(--cui-text-size);
  line-height: 30px;
}

.gc-collection-editor__arrows {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 30px;
  justify-content: center;
}
.gc-collection-editor__arrows > .gc-btn {
  position: absolute;
  width: 100%;
  height: 15px;
}
.gc-collection-editor__arrows > .gc-btn .gc-btn__text {
  height: 100%;
  padding: 0 !important;
}
.gc-collection-editor__arrows > .gc-btn .gc-btn__icon {
  position: static;
  display: flex;
  height: 100%;
  justify-content: center;
  align-items: center;
}
.gc-collection-editor__arrows .gc-collection-editor__arrow-up {
  top: 0;
  left: 0;
}
.gc-collection-editor__arrows .gc-collection-editor__arrow-down {
  bottom: 0;
  left: 0;
}
.gc-collection-editor__arrows + .gc-btn {
  margin-left: 5px;
}

.gc-collection-editor__items {
  position: relative;
}
.gc-collection-editor__items:only-child {
  padding-top: 15px;
}

.gc-collection-editor__item--dragged {
  pointer-events: none;
  opacity: 0;
}

.gc-collection-editor__clone {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  pointer-events: none;
  box-shadow: var(--cui-prop-editors-drag-shadow);
}
.gc-collection-editor__clone .gc-collection-editor__item {
  opacity: 0.8;
}

.gc-collection-editor__empty {
  display: flex;
  height: 30px;
  margin-top: 15px;
  color: var(--cui-text-semi-60);
  background-color: var(--cui-input-bg);
  font-size: var(--cui-text-size);
  justify-content: center;
  align-items: center;
}

.gc-collection-editor__header {
  overflow: hidden;
  margin: 5px 0;
  text-overflow: ellipsis;
  font-size: var(--cui-text-size);
}
.gc-collection-editor__header--offset-1 {
  padding-left: 35px;
}
.gc-collection-editor__header--offset-2 {
  padding-left: 70px;
}

.gc-side-editor .gc-expand-editor__content {
  flex-direction: row;
  flex-wrap: wrap;
}
.gc-side-editor--stretched .gc-side-editor__side:not([class*="--default"]) {
  width: calc(50% + 15px);
}
.gc-side-editor--stretched .gc-side-editor__side--left, .gc-side-editor--stretched .gc-side-editor__side--right {
  margin-left: -15px;
}
.gc-side-editor--stretched .gc-side-editor__side--top, .gc-side-editor--stretched .gc-side-editor__side--bottom {
  margin-right: -15px;
}

.gc-side-editor__side {
  width: 50%;
}
.gc-side-editor__side--default {
  width: 100%;
  order: 1;
}
.gc-side-editor__side--left {
  order: 2;
}
.gc-side-editor__side--top {
  order: 3;
}
.gc-side-editor__side--right {
  order: 4;
}
.gc-side-editor__side--bottom {
  order: 5;
}
.gc-side-editor__side:not(.gc-side-editor__side--default) .gc-label {
  -ms-grid-columns: 30px minmax(0, 1fr);
  grid-template-columns: 30px minmax(0, 1fr);
}

.gc-binding-wrapper__content {
  flex: 1 1 auto;
  width: calc(100% - 30px);
  max-width: calc(100% - 30px);
}

.gc-binding-wrapper__toggle {
  width: 10px;
  height: 10px;
  margin: 10px auto;
  transition: background-color 0.2s ease-in-out;
  border-radius: var(--cui-border-radius);
  background-color: var(--cui-binding-default);
}

.gc-binding-wrapper {
  display: flex;
}
.gc-binding-wrapper > .gc-dd {
  flex: 0 0 auto;
  width: 30px;
  min-width: 30px;
}
.gc-binding-wrapper > .gc-dd > .gc-btn:hover {
  background-color: transparent;
}
.gc-binding-wrapper > .gc-dd > .gc-btn:hover .gc-binding-wrapper__toggle {
  box-shadow: 0 0 4px 1px var(--cui-binding-default-shadow);
}
.gc-binding-wrapper--modified .gc-binding-wrapper__toggle {
  background-color: var(--cui-binding-modified);
}
.gc-binding-wrapper--modified > .gc-dd > .gc-btn:hover .gc-binding-wrapper__toggle {
  box-shadow: 0 0 4px 1px var(--cui-binding-modified-shadow);
}
.gc-binding-wrapper--bind .gc-binding-wrapper__toggle {
  background-color: var(--cui-binding-bind);
}
.gc-binding-wrapper--bind > .gc-dd > .gc-btn:hover .gc-binding-wrapper__toggle {
  box-shadow: 0 0 4px 1px var(--cui-binding-bind-shadow);
}

@media (any-hover: none) {
  .gc-binding-wrapper > .gc-dd > .gc-btn:hover .gc-binding-wrapper__toggle {
    box-shadow: none;
  }
  .gc-binding-wrapper--modified > .gc-dd > .gc-btn:hover .gc-binding-wrapper__toggle, .gc-binding-wrapper--bind > .gc-dd > .gc-btn:hover .gc-binding-wrapper__toggle {
    box-shadow: none;
  }
}
.gc-bool-editor {
  margin: 0 0 0 auto;
}

.gc-label:not([class*="--position"]) .gc-bool-editor {
  margin: 0;
}

.gc-number-editor {
  display: flex;
}
.gc-number-editor > .gc-btn {
  flex: 0 0 auto;
}
.gc-number-editor > .gc-btn:first-of-type {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.gc-number-editor > .gc-btn:last-of-type {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.gc-number-editor > .gc-input {
  flex: 1 1 auto;
  border-radius: 0;
}

.gc-enum-editor .gc-btn__text {
  padding: 0 10px;
}
.gc-enum-editor--empty .gc-btn__text {
  opacity: 0.38;
}

.gc-color-palette {
  width: 100%;
}
.gc-color-palette *, .gc-color-palette *:before, .gc-color-palette *:after {
  box-sizing: border-box;
}

.gc-color-palette__colors {
  display: flex;
  width: 100%;
  padding: 0 10px;
  flex-wrap: wrap;
}

.gc-color-palette__heading.gc-heading {
  padding: 0 15px;
  color: var(--cui-text-semi-60);
}
.gc-color-palette__heading.gc-heading .gc-heading__divider {
  border-bottom: 1px solid var(--cui-dd-divider);
}

.gc-color {
  display: flex;
  flex: 0 0 auto;
  width: 50%;
  min-width: 50%;
  max-width: 50%;
  height: 20px;
  padding: 0 5px;
}

.gc-color__main {
  position: relative;
  flex: 0 0 auto;
  width: 15px;
  height: 15px;
  margin-right: 5px;
  border-radius: 4px;
}
.gc-color__main::after {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  content: "";
  transition: border-color 0.2s ease-in-out;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}
.gc-color__main:hover::after {
  border: 1px solid rgba(0, 0, 0, 0.4);
}

.gc-color__shades {
  position: relative;
  display: flex;
  flex: 1 1 auto;
  height: 15px;
  border-radius: 4px;
}
.gc-color__shades::after {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  content: "";
  transition: border-color 0.2s ease-in-out;
  pointer-events: none;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}
.gc-color__shades > div {
  position: relative;
  flex: 1 1 auto;
  height: 100%;
}
.gc-color__shades > div::after {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  content: "";
  transition: border-color 0.2s ease-in-out;
  border: 1px solid transparent;
}
.gc-color__shades > div:first-of-type::after {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}
.gc-color__shades > div:last-of-type::after {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.gc-color__shades > div:hover::after {
  border: 1px solid rgba(0, 0, 0, 0.4);
}

.gc-color-picker {
  display: flex;
  flex-wrap: wrap;
}
.gc-color-picker *, .gc-color-picker *:before, .gc-color-picker *:after {
  box-sizing: border-box;
}

.gc-color-picker__preview {
  flex: 0 0 auto;
  width: 90px;
  height: 90px;
  margin-right: 15px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}
.gc-color-picker__preview:hover {
  border: 4px solid rgba(0, 0, 0, 0.1);
}

.gc-color-picker__settings {
  flex: 1 1 auto;
}
.gc-color-picker__settings > span {
  display: block;
  height: 15px;
  -webkit-user-select: none;
  user-select: none;
  font-size: var(--cui-text-size-sm);
  line-height: 15px;
}

.gc-color-picker__inputs {
  display: flex;
  width: 100%;
}

.gc-color-picker__hex .gc-label, .gc-color-picker__rgb .gc-label {
  width: 100%;
}
.gc-color-picker__hex .gc-label .gc-label__label, .gc-color-picker__rgb .gc-label .gc-label__label {
  align-items: center;
}
.gc-color-picker__hex .gc-input, .gc-color-picker__rgb .gc-input {
  display: block;
  width: 100%;
}

.gc-color-picker__hex {
  flex: 0 0 auto;
  width: 90px;
  margin-right: 15px;
}

.gc-color-picker__rgb {
  display: flex;
  flex: 1 1 auto;
  justify-content: space-between;
}
.gc-color-picker__rgb .gc-label {
  flex: 0 0 auto;
  width: calc(33.3% - 4px);
}

.gc-color-picker__slider {
  border: 1px solid var(--cui-dd-divider);
}

.gc-color-dropdown {
  width: 300px;
  padding-bottom: 15px;
}
.gc-color-dropdown *, .gc-color-dropdown *:before, .gc-color-dropdown *:after {
  box-sizing: border-box;
}
.gc-color-dropdown > .gc-btn-group {
  margin: 15px;
  margin-bottom: 5px;
}
.gc-color-dropdown > .gc-color-dropdown__web-colors {
  margin: 15px;
  margin-bottom: 0;
}
.gc-color-dropdown > .gc-color-dropdown__web-colors .gc-dd-menu__item--with-preview.gc-size-sm {
  padding: 0;
}
.gc-color-dropdown > .gc-color-picker {
  margin: 15px;
  margin-bottom: 0;
}
.gc-color-dropdown > .gc-slider {
  margin: 0 15px;
}

.gc-color-dropdown__web-color {
  width: 15px;
  height: 15px;
  border-radius: 4px;
}

@media (any-hover: none) {
  .gc-color__main:hover::after {
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  .gc-color__shades > div:hover::after {
    border: 1px solid transparent;
  }

  .gc-color-picker__preview:hover {
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
}
.gc-wizard {
  display: flex;
  width: 100%;
  height: 90px;
}
.gc-wizard *, .gc-wizard *:before, .gc-wizard *:after {
  box-sizing: border-box;
}
.gc-wizard--transparent-dark > .gc-wizard__step:before, .gc-wizard--transparent-dark > .gc-wizard__step:after {
  background-color: var(--cui-wizard-dark-bg-secondary);
}
.gc-wizard--transparent-dark > .gc-wizard__step > .gc-btn {
  background-color: var(--cui-wizard-dark-bg-main);
}
.gc-wizard--transparent-dark > .gc-wizard__step > .gc-btn:not([disabled]):not(.disabled):hover {
  background-color: var(--cui-wizard-dark-hover);
}
.gc-wizard--transparent-dark > .gc-wizard__step > .gc-btn:disabled {
  background-color: var(--cui-wizard-dark-bg-secondary);
}
.gc-wizard--transparent-dark > .gc-wizard__step > .gc-btn > .gc-btn__text {
  color: var(--cui-text);
}
.gc-wizard--transparent-dark > .gc-wizard__step--complete:before, .gc-wizard--transparent-dark > .gc-wizard__step--complete:after {
  background-color: var(--cui-wizard-dark-bg-main);
}
.gc-wizard--transparent-dark > .gc-wizard__step--complete:last-child > .gc-btn {
  background-color: var(--cui-wizard-dark-bg-main);
}
.gc-wizard--transparent-dark > .gc-wizard__step--current:before {
  background-color: var(--cui-wizard-dark-bg-main);
}
.gc-wizard--transparent-dark > .gc-wizard__step--current > .gc-btn {
  border-color: var(--cui-wizard-dark-bg-main);
}
.gc-wizard--transparent-light > .gc-wizard__step:before, .gc-wizard--transparent-light > .gc-wizard__step:after {
  background-color: var(--cui-wizard-light-bg-secondary);
}
.gc-wizard--transparent-light > .gc-wizard__step > .gc-btn {
  background-color: var(--cui-wizard-light-bg-main);
}
.gc-wizard--transparent-light > .gc-wizard__step > .gc-btn:not([disabled]):not(.disabled):hover {
  background-color: var(--cui-wizard-light-hover);
}
.gc-wizard--transparent-light > .gc-wizard__step > .gc-btn:disabled {
  background-color: var(--cui-wizard-light-bg-secondary);
}
.gc-wizard--transparent-light > .gc-wizard__step > .gc-btn .gc-btn__icon {
  color: var(--cui-accent);
}
.gc-wizard--transparent-light > .gc-wizard__step > .gc-wizard__step-text {
  color: var(--cui-contrast-text);
}
.gc-wizard--transparent-light > .gc-wizard__step--complete:before, .gc-wizard--transparent-light > .gc-wizard__step--complete:after {
  background-color: var(--cui-wizard-light-bg-main);
}
.gc-wizard--transparent-light > .gc-wizard__step--complete:last-child > .gc-btn {
  background-color: var(--cui-wizard-light-bg-main);
}
.gc-wizard--transparent-light > .gc-wizard__step--current:before {
  background-color: var(--cui-wizard-light-bg-main);
}
.gc-wizard--transparent-light > .gc-wizard__step--current > .gc-btn {
  border-color: var(--cui-wizard-light-bg-main);
}

.gc-wizard__step {
  position: relative;
  height: 100%;
  flex-grow: 1;
}
.gc-wizard__step > .gc-btn {
  position: absolute;
  z-index: 1;
  top: 10px;
  left: 50%;
  display: flex;
  transform: translateX(-50%);
  border-radius: 50%;
  align-items: center;
  justify-content: center;
}
.gc-wizard__step > .gc-btn:disabled {
  opacity: 1;
  background-color: var(--cui-wizard-bg);
}
.gc-wizard__step > .gc-btn > .gc-btn__text {
  width: 30px;
  height: auto;
  padding: 0;
  text-align: center;
  font-size: var(--cui-text-size-lg);
  line-height: 1;
}
.gc-wizard__step > .gc-btn--with-icon > .gc-btn__text {
  height: 30px;
}
.gc-wizard__step:before, .gc-wizard__step:after {
  position: absolute;
  top: 22px;
  left: 0;
  width: calc(50% - 15px - 1px);
  height: 6px;
  content: "";
  transition: color 0.2s ease-in-out;
  background-color: var(--cui-wizard-bg);
}
.gc-wizard__step:after {
  left: calc(50% + 15px + 1px);
}
.gc-wizard__step:first-child:before {
  content: none;
}
.gc-wizard__step:last-child:after {
  content: none;
}
.gc-wizard__step--complete:before, .gc-wizard__step--complete:after {
  background-color: var(--cui-accent);
}
.gc-wizard__step--complete:last-child > .gc-btn {
  background-color: var(--cui-accent);
}
.gc-wizard__step--current:before {
  background-color: var(--cui-accent);
}
.gc-wizard__step--current > .gc-btn {
  border: 2px solid var(--cui-accent);
}
.gc-wizard__step--current > .gc-btn > .gc-btn__text {
  display: flex;
  width: 26px;
  height: 26px;
  justify-content: center;
  align-items: center;
}

.gc-wizard__step-text {
  position: absolute;
  top: 50px;
  left: 0;
  display: block;
  overflow: hidden;
  width: 100%;
  max-height: 36px;
  margin: 0;
  padding: 0;
  text-align: center;
  color: var(--cui-text);
  font-size: var(--cui-text-size);
  font-weight: normal;
  line-height: 18px;
}

@media (any-hover: none) {
  .gc-wizard--transparent-dark > .gc-wizard__step > .gc-btn:not([disabled]):not(.disabled):hover {
    background-color: var(--cui-wizard-dark-bg-main);
  }
  .gc-wizard--transparent-light > .gc-wizard__step > .gc-btn:not([disabled]):not(.disabled):hover {
    background-color: var(--cui-wizard-light-bg-main);
  }
}
@keyframes gc-progress-animation {
  0% {
    left: -40px;
    width: 40px;
  }
  50% {
    width: 300px;
  }
  100% {
    left: 100%;
    width: 40px;
  }
}
.gc-progress {
  display: block;
  overflow: hidden;
  height: 20px;
  border-radius: 4px;
  background-color: var(--cui-progressbar-bg);
  line-height: 20px;
}
.gc-progress--inline {
  display: inline-block;
}
.gc-progress--semi-transparent {
  background-color: var(--cui-progressbar-bg-semi);
}
.gc-progress--transparent {
  background-color: transparent;
}
.gc-progress--accent > .gc-progress__fill {
  background-color: var(--cui-accent);
}
.gc-progress--accent > .gc-progress__value {
  color: var(--cui-contrast-text);
}
.gc-progress--pause-error > .gc-progress__fill {
  background-color: var(--cui-accent-error);
}
.gc-progress--pause-error > .gc-progress__value {
  color: var(--cui-contrast-text);
}
.gc-progress--pause-warning > .gc-progress__fill {
  background-color: var(--cui-accent-warning);
}
.gc-progress--pause-warning > .gc-progress__value {
  color: var(--cui-contrast-text);
}
.gc-progress--indeterminate {
  position: relative;
}
.gc-progress--indeterminate > .gc-progress__fill {
  position: absolute;
  top: 0;
  animation-name: gc-progress-animation;
  animation-duration: 1.6s;
  animation-timing-function: ease-in-out;
  animation-iteration-count: infinite;
}
.gc-progress--indeterminate.gc-progress--pause-error > .gc-progress__fill {
  animation-play-state: paused;
}
.gc-progress--indeterminate.gc-progress--pause-warning > .gc-progress__fill {
  animation-play-state: paused;
}

.gc-progress.gc-size-sm {
  height: 2px;
}

.gc-progress.gc-size-lg {
  height: 40px;
  line-height: 40px;
}

.gc-progress__fill {
  overflow: visible;
  height: 100%;
  transition: width 0.2s ease-in-out;
  text-align: center;
  background-color: var(--cui-progress-fill-color);
}

.gc-progress__value {
  display: block;
  height: 100%;
  color: var(--cui-progress-value-color);
  font-size: var(--cui-text-size-sm);
}

.gc-slider {
  position: relative;
  user-select: none;
  -webkit-user-select: none;
  display: block;
  transition: opacity 0.2s ease-in-out;
}
.gc-slider *, .gc-slider *:before, .gc-slider *:after {
  box-sizing: border-box;
}
.gc-slider--mode-X {
  width: auto;
  height: 15px;
}
.gc-slider--mode-X .gc-slider__background {
  top: 4px;
  height: calc(100% - 8px);
}
.gc-slider--mode-Y {
  width: 15px;
  height: 150px;
}
.gc-slider--mode-Y .gc-slider__background {
  left: 4px;
  width: calc(100% - 8px);
}
.gc-slider--mode-XY {
  width: 150px;
  height: 150px;
}
.gc-slider:hover .gc-slider__toggle:after {
  opacity: 1;
}
.gc-slider--disabled {
  pointer-events: none;
  opacity: 0.62;
}
.gc-slider--disabled .gc-slider__fill {
  opacity: 0 !important;
}
.gc-slider--invalid .gc-slider__toggle {
  background-color: var(--cui-accent-error);
}
.gc-slider--invalid .gc-slider__background {
  background-color: var(--cui-accent-error-semi-40);
}
.gc-slider--invalid .gc-slider__fill {
  opacity: 0 !important;
}

.gc-slider__area {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.gc-slider__fill {
  position: absolute;
  transition: opacity 0.2s ease-in-out;
  background-color: var(--cui-accent);
}

.gc-slider__background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: background-color 0.2s ease-in-out;
  border-radius: 4px;
  background-color: var(--cui-input-bg);
}

.gc-slider__toggle {
  position: absolute;
  top: 0;
  left: 0;
  width: 15px;
  height: 15px;
  transition: background-color 0.2s ease-in-out;
  transform: translateX(-50%) translateY(-50%);
  border: 2px solid white;
  border-radius: 50%;
  background-color: var(--cui-accent);
  box-shadow: var(--cui-shadow-border);
}
.gc-slider__toggle[class*="--tooltip"]:after {
  position: absolute;
  padding: 5px;
  content: attr(data-value);
  transition: opacity 0.2s ease-in-out;
  white-space: nowrap;
  pointer-events: none;
  opacity: 0;
  color: var(--cui-text);
  border-radius: 4px;
  background-color: var(--cui-contrast);
  box-shadow: 0 0 5px -1px rgba(0, 0, 0, 0.1);
  font-family: var(--cui-text-family);
  font-size: var(--cui-text-size);
  line-height: 1;
}
.gc-slider__toggle--tooltip-top:after {
  bottom: 100%;
  left: 50%;
  transform: translate(-50%, -8px);
}
.gc-slider__toggle--tooltip-bottom:after {
  top: 100%;
  left: 50%;
  transform: translate(-50%, 8px);
}
.gc-slider__toggle--tooltip-right:after {
  top: 50%;
  left: 100%;
  transform: translate(8px, -50%);
}
.gc-slider__toggle--tooltip-left:after {
  top: 50%;
  right: 100%;
  transform: translate(-8px, -50%);
}
.gc-slider__toggle:active {
  z-index: 1;
}
.gc-slider__toggle:active:after {
  opacity: 1;
}

@media (any-hover: none) {
  .gc-slider:hover .gc-slider__toggle:after {
    opacity: 0;
  }
}
.gc-text-editor {
  position: relative;
}
.gc-text-editor__preview {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  width: 30px;
  height: 100%;
  padding: 5px;
  pointer-events: none;
  justify-content: center;
  align-items: center;
}
.gc-text-editor__preview + .gc-input {
  padding-left: 30px !important;
}

.gc-expand-editor--expanded {
  position: relative;
  margin: 0 -15px 5px;
  padding: 0 15px;
  background-color: var(--cui-bg-panels-section);
}
.gc-expand-editor--expanded > .gc-expand-editor__main {
  margin-bottom: 5px;
}
.gc-expand-editor--expanded:before {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  width: 100%;
  height: 30px;
  content: "";
  background-color: var(--cui-input-bg);
}
.gc-expand-editor--transparent {
  background-color: transparent;
}
.gc-expand-editor--transparent:before {
  content: none;
}

.gc-expand-editor__main {
  position: relative;
}
.gc-expand-editor__main > .gc-label > .gc-label__content {
  padding-right: 30px;
}
.gc-expand-editor__main > .gc-label > .gc-label__content > *:first-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.gc-expand-editor__toggle {
  position: absolute;
  top: 0;
  right: 0;
  width: 30px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.gc-expand-editor__content {
  display: flex;
  flex-direction: column;
}
.gc-expand-editor__content > * {
  margin-bottom: 5px;
}

@keyframes gc-badge-show {
  0% {
    transform: scale(1) rotateX(90deg);
  }
  60% {
    transform: scale(1) rotateX(0);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes gc-badge-show-top-left {
  0% {
    transform: scale(1) rotateX(90deg) translate(-25%, -25%);
  }
  60% {
    transform: scale(1) rotateX(0) translate(-25%, -25%);
  }
  100% {
    transform: scale(1) translate(-25%, -25%);
  }
}
@keyframes gc-badge-show-top-right {
  0% {
    transform: scale(1) rotateX(90deg) translate(25%, -25%);
  }
  60% {
    transform: scale(1) rotateX(0) translate(25%, -25%);
  }
  100% {
    transform: scale(1) translate(25%, -25%);
  }
}
@keyframes gc-badge-show-bottom-left {
  0% {
    transform: scale(1) rotateX(90deg) translate(-25%, 25%);
  }
  60% {
    transform: scale(1) rotateX(0) translate(-25%, 25%);
  }
  100% {
    transform: scale(1) translate(-25%, 25%);
  }
}
@keyframes gc-badge-show-bottom-right {
  0% {
    transform: scale(1) rotateX(90deg) translate(25%, 25%);
  }
  60% {
    transform: scale(1) rotateX(0) translate(25%, 25%);
  }
  100% {
    transform: scale(1) translate(25%, 25%);
  }
}
.gc-badge {
  position: absolute;
  z-index: 1;
  display: flex;
  box-sizing: border-box;
  min-width: 20px;
  height: 20px;
  padding: 5px;
  cursor: default;
  animation: gc-badge-show 0.4s 1 ease-in-out;
  white-space: nowrap;
  color: var(--cui-contrast-text);
  border-radius: 4px;
  background-color: var(--cui-accent);
  box-shadow: var(--cui-shadow-border);
  font-size: var(--cui-text-size);
  line-height: 1;
  justify-content: center;
  align-items: center;
}
.gc-badge--size-sm {
  min-width: 15px;
  height: 15px;
  font-size: var(--cui-text-size-sm);
}
.gc-badge--size-lg {
  min-width: 25px;
  height: 25px;
  font-size: var(--cui-text-size-lg);
}
.gc-badge--top-left {
  top: 0;
  left: 0;
}
.gc-badge--top-left:not(.gc-badge--inset) {
  animation: gc-badge-show-top-left 0.4s 1 ease-in-out forwards;
}
.gc-badge--top-right {
  top: 0;
  right: 0;
}
.gc-badge--top-right:not(.gc-badge--inset) {
  animation: gc-badge-show-top-right 0.4s 1 ease-in-out forwards;
}
.gc-badge--bottom-left {
  bottom: 0;
  left: 0;
}
.gc-badge--bottom-left:not(.gc-badge--inset) {
  animation: gc-badge-show-bottom-left 0.4s 1 ease-in-out forwards;
}
.gc-badge--bottom-right {
  right: 0;
  bottom: 0;
}
.gc-badge--bottom-right:not(.gc-badge--inset) {
  animation: gc-badge-show-bottom-right 0.4s 1 ease-in-out forwards;
}
.gc-badge--rounded {
  border-radius: 10px;
}
.gc-badge--rounded.gc-badge--size-sm {
  border-radius: 7.5px;
}
.gc-badge--rounded.gc-badge--size-lg {
  border-radius: 12.5px;
}
.gc-badge--inline {
  position: relative;
  display: inline-flex;
}
.gc-badge--inline:last-child:not(:only-child) {
  margin-left: 5px;
}
.gc-badge--inline:first-child:not(:only-child) {
  margin-right: 5px;
}
.gc-badge--custom {
  padding: 0;
  color: inherit;
  background: transparent;
  box-shadow: none;
}
.gc-badge--custom.gc-badge--inline {
  height: 100%;
}
.gc-badge--level-warning {
  background-color: var(--cui-accent-warning);
}
.gc-badge--level-warning.gc-badge--inverted {
  color: var(--cui-accent-warning);
}
.gc-badge--level-error {
  background-color: var(--cui-accent-error);
}
.gc-badge--level-error.gc-badge--inverted {
  color: var(--cui-accent-error);
}
.gc-badge--inverted {
  color: var(--cui-accent);
  background-color: var(--cui-contrast);
  font-weight: 700;
}
.gc-badge--dot {
  width: 6px;
  min-width: auto;
  height: 6px;
  padding: 0;
  border-radius: 50%;
}

@keyframes gc-item-animation {
  0% {
    width: 0;
    height: 0;
  }
  33% {
    width: 60px;
    height: 60px;
  }
  66% {
    width: 60px;
    height: 60px;
  }
  100% {
    width: 100%;
    height: 60px;
  }
}
@keyframes gc-item-mini-animation {
  0% {
    width: 0;
    height: 0;
  }
  33% {
    width: 30px;
    height: 30px;
  }
  66% {
    width: 30px;
    height: 30px;
  }
  100% {
    width: 100%;
    height: 30px;
  }
}
@keyframes gc-item-icon-animation {
  0% {
    transform: scale(0);
  }
  33% {
    transform: scale(1);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes gc-expand-animation {
  0% {
    transform: scale(0) translate(-50%, -50%);
  }
  100% {
    transform: scale(1) translate(-50%, -50%);
  }
}
@keyframes gc-task-loader-animation {
  0% {
    transform: scale(0);
    transform: scale(0);
  }
  90% {
    transform: scale(0.7);
    transform: scale(0.7);
  }
  100% {
    transform: scale(1);
    transform: scale(1);
  }
}
@keyframes gc-timeout-bar-animation {
  0% {
    width: 100%;
  }
  100% {
    width: 0;
  }
}
@keyframes gc-appear-animation {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.gc-portal-root--notifications > .gc-positioner {
  z-index: 1090;
}

.gc-notifications-container {
  display: flex;
  overflow: hidden;
  flex-direction: column;
  width: 330px;
  max-width: 95vw;
  align-items: center;
}
.gc-notifications-container *, .gc-notifications-container *:before, .gc-notifications-container *:after {
  box-sizing: border-box;
}
.gc-notifications-container--position-bottom-center {
  margin: 0 0 30px 0;
}
.gc-notifications-container--position-bottom-left {
  margin: 0 0 30px 15px;
}
.gc-notifications-container--position-bottom-right {
  margin: 0 15px 30px 0;
}
.gc-notifications-container--position-top-center {
  flex-direction: column-reverse;
  margin: 30px 0 0 0;
}
.gc-notifications-container--position-top-left {
  flex-direction: column-reverse;
  margin: 30px 0 0 15px;
}
.gc-notifications-container--position-top-right {
  flex-direction: column-reverse;
  margin: 30px 15px 0 0;
}

.gc-notification {
  position: relative;
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 60px;
  margin-bottom: 10px;
  animation: gc-item-animation 0.8s 1;
  color: var(--cui-contrast);
  border-radius: 4px;
  background-color: var(--cui-notification-btn-accent-bg);
  flex-shrink: 0;
}
.gc-notification__tag {
  display: flex;
  flex: 0 0 auto;
  width: 60px;
  height: 60px;
  -webkit-user-select: none;
  user-select: none;
  transform-origin: top left;
  animation: gc-item-icon-animation 0.8s 1;
  text-align: center;
  border-radius: inherit;
  font-size: 20px;
  line-height: 60px;
  justify-content: center;
  align-items: center;
}
.gc-notification__content {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  width: 0;
  padding: 7px 15px 7px 0;
  user-select: none;
  justify-content: center;
}
.gc-notification__caption, .gc-notification__status {
  overflow: hidden;
  height: 20px;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: var(--cui-text-size);
  line-height: 20px;
}
.gc-notification__status {
  opacity: 0.7;
  font-style: italic;
}
.gc-notification__actions {
  display: flex;
  flex: 1 1 auto;
  max-height: 50%;
  margin-left: -2px;
  align-items: center;
}
.gc-notification__actions > .gc-btn.gc-notification__button {
  overflow: hidden;
  height: 20px;
  margin-right: 5px;
  padding: 0 5px;
  border-radius: 4px;
  background-color: var(--cui-notification-btn-bg);
  font-size: var(--cui-text-size-sm);
  line-height: 20px;
}
.gc-notification__actions > .gc-btn.gc-notification__button.gc-rounded {
  border-radius: 10px;
}
.gc-notification__actions > .gc-btn.gc-notification__button:hover {
  background-color: var(--cui-notification-btn-bg-hover);
}
.gc-notification__loader::before, .gc-notification__loader::after {
  position: absolute;
  top: calc(50% - 5.5px);
  display: block;
  width: 11px;
  height: 11px;
  content: "";
  border-radius: 50%;
  background-color: currentColor;
}
.gc-notification__loader::before {
  left: calc(50% - 11px - 1px);
  animation: gc-loader-btn 0.4s ease-in-out infinite alternate;
}
.gc-notification__loader::after {
  left: calc(50% + 1px);
  animation: gc-loader-btn 0.4s ease-in-out infinite alternate-reverse;
}
.gc-notification__timeout-bar.gc-progress {
  position: absolute;
  top: 2px;
  left: 2px;
  overflow: hidden;
  width: calc(100% - 4px);
  height: 2px;
  animation: 0.8s gc-appear-animation ease-in forwards;
  opacity: 0;
  border-radius: 2px;
  background-color: var(--cui-contrast-semi-40);
}
.gc-notification__timeout-bar.gc-progress > .gc-progress__fill {
  width: 100%;
  animation: 0.8s gc-timeout-bar-animation 0.8s linear forwards;
  background-color: var(--cui-contrast-semi-40);
}
.gc-notification--rounded {
  border-radius: 30px;
}
.gc-notification--info {
  background-color: var(--cui-notification-btn-accent-bg);
}
.gc-notification--warning {
  background-color: var(--cui-notification-btn-warning-bg);
}
.gc-notification--error {
  background-color: var(--cui-notification-btn-error-bg);
}
.gc-notification--task:hover .gc-notification__tag > .gc-btn {
  display: block;
  opacity: 1;
}
.gc-notification--task:hover .gc-notification__loader {
  display: none;
  opacity: 0;
}
.gc-notification--task .gc-notification__tag {
  position: relative;
}
.gc-notification--task .gc-notification__tag > .gc-btn {
  display: none;
  transition: opacity 0s;
  opacity: 0;
}
.gc-notification--task .gc-progress {
  margin: 3px 0 0 0;
  background-color: var(--cui-contrast-semi-10);
}
.gc-notification--task .gc-progress > .gc-progress__fill {
  background-color: var(--cui-contrast);
}
.gc-notification--batch .gc-notification__tag {
  background-color: var(--cui-accent);
  font-size: 16px;
}
.gc-notification--batch .gc-notification__actions {
  max-height: none;
}
.gc-notification--batch .gc-notification__actions > .gc-notification__button {
  width: 50%;
  height: 30px;
  margin-right: 10px;
  text-align: center;
  border-radius: 4px;
  font-size: var(--cui-text-size);
  line-height: 30px;
}
.gc-notification--batch .gc-notification__actions > .gc-notification__button.gc-rounded {
  border-radius: 15px;
}
.gc-notification--batch.gc-notification--size-small .gc-notification__tag {
  font-size: var(--cui-text-size);
}
.gc-notification--batch.gc-notification--size-small .gc-notification__actions {
  margin-left: auto;
}
.gc-notification--batch.gc-notification--size-small .gc-notification__actions .gc-notification__button {
  padding: 0 10px;
}
.gc-notification--size-small {
  height: 30px;
  animation: gc-item-mini-animation 0.8s 1;
}
.gc-notification--size-small .gc-notification__tag {
  width: 30px;
  height: 30px;
  font-size: 16px;
  line-height: 30px;
}
.gc-notification--size-small .gc-notification__content {
  flex-direction: row;
  padding: 0;
  justify-content: space-between;
  align-items: center;
}
.gc-notification--size-small .gc-notification__caption {
  height: 30px;
  padding: 0 10px 0 0;
  line-height: 30px;
}
.gc-notification--size-small .gc-notification__actions {
  flex: 0 0 auto;
  max-height: none;
}
.gc-notification--size-small .gc-notification__timeout-bar.gc-progress {
  height: 2px;
}
.gc-notification--size-small.gc-notification--task {
  position: relative;
}
.gc-notification--size-small.gc-notification--task .gc-notification__tag {
  z-index: 1;
  background-color: transparent;
}
.gc-notification--size-small.gc-notification--task .gc-notification__tag > .gc-notification__loader::before, .gc-notification--size-small.gc-notification--task .gc-notification__tag > .gc-notification__loader::after {
  position: absolute;
  top: calc(50% - 3.5px);
  display: block;
  width: 7px;
  height: 7px;
  content: "";
  border-radius: 50%;
  background-color: currentColor;
}
.gc-notification--size-small.gc-notification--task .gc-notification__tag > .gc-notification__loader::before {
  left: calc(50% - 7px - 1px);
  animation: gc-loader-btn 0.4s ease-in-out infinite alternate;
}
.gc-notification--size-small.gc-notification--task .gc-notification__tag > .gc-notification__loader::after {
  left: calc(50% + 1px);
  animation: gc-loader-btn 0.4s ease-in-out infinite alternate-reverse;
}
.gc-notification--size-small.gc-notification--task .gc-notification__tag > .gc-notification__button-mini {
  position: absolute;
  top: 0;
  left: 0;
}
.gc-notification--size-small.gc-notification--task .gc-notification__content {
  border-radius: inherit;
}
.gc-notification--size-small.gc-notification--task .gc-notification__content > .gc-notification__caption {
  z-index: 1;
}
.gc-notification--size-small.gc-notification--task .gc-notification__content > .gc-progress.gc-size-sm {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: inherit;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  background-color: transparent;
}
.gc-notification--size-small.gc-notification--task .gc-notification__content > .gc-progress.gc-size-sm > .gc-progress__fill {
  border-radius: inherit;
  background-color: var(--cui-accent-hover);
}

.gc-notifications-modal > .gc-notification-details {
  position: absolute;
  top: 50%;
  left: 50%;
  max-width: 95%;
  transform-origin: 0 0;
  animation: gc-expand-animation 0.2s 1 ease-in-out forwards;
  border-radius: 4px;
  background-color: var(--cui-dd-background);
  box-shadow: var(--cui-shadow-border);
}

.gc-notification-details {
  width: 330px;
  padding: 15px 0;
}
.gc-notification-details *, .gc-notification-details *:before, .gc-notification-details *:after {
  box-sizing: border-box;
}
.gc-notification-details__content {
  overflow: auto;
  height: 400px;
  padding-bottom: 15px;
  font-size: var(--cui-text-size);
}
.gc-notification-details__category {
  padding: 0 15px;
}
.gc-notification-details__category + .gc-notification-details__category {
  margin-top: 5px;
}
.gc-notification-details__category--info {
  color: var(--cui-accent-text);
}
.gc-notification-details__category--info .gc-notification-details-item__main {
  background-color: var(--cui-accent-text-semi-10);
}
.gc-notification-details__category--warning {
  color: var(--cui-accent-warning-text);
}
.gc-notification-details__category--warning .gc-notification-details-item__main {
  background-color: var(--cui-accent-warning-text-semi-10);
}
.gc-notification-details__category--error {
  color: var(--cui-accent-error-text);
}
.gc-notification-details__category--error .gc-notification-details-item__main {
  background-color: var(--cui-accent-error-text-semi-10);
}
.gc-notification-details__action {
  padding: 0 15px;
}

.gc-notification-details-item {
  position: relative;
  overflow: hidden;
  border-radius: 4px;
}
.gc-notification-details-item:before {
  position: absolute;
  top: 5px;
  left: 5px;
  width: 2px;
  height: calc(100% - 10px);
  content: "";
  background-color: currentColor;
}
.gc-notification-details-item + .gc-notification-details-item {
  margin-top: 5px;
}
.gc-notification-details-item__main {
  display: flex;
  padding-left: 15px;
  align-items: center;
  justify-content: space-between;
}
.gc-notification-details-item__main > .gc-btn-group {
  flex: 0 0 auto;
  align-self: flex-start;
}
.gc-notification-details-item__caption {
  display: flex;
  flex: 1 0 auto;
  width: 0;
  padding: 5px 0;
}
.gc-notification-details-item__caption > span {
  display: inline-block;
  overflow: hidden;
  max-height: 30px;
  vertical-align: middle;
  text-overflow: ellipsis;
  font-size: var(--cui-text-size);
  line-height: 15px;
}
.gc-notification-details-item__content {
  padding: 5px 5px 5px 15px;
  white-space: pre-line;
  word-wrap: break-word;
  color: var(--cui-text);
  background-color: var(--cui-bg-panels);
}

@media (any-hover: none) {
  .gc-notification .gc-notification__button.gc-btn:hover, .gc-notification .gc-notification__button-mini.gc-btn:hover {
    background-color: var(--cui-notification-btn-bg);
  }
  .gc-notification--warning .gc-btn.gc-notification__button:hover, .gc-notification--warning .gc-btn.gc-notification__button-mini:hover {
    background-color: var(--cui-notification-btn-bg);
  }
  .gc-notification--error .gc-btn.gc-notification__button:hover, .gc-notification--error .gc-btn.gc-notification__button-mini:hover {
    background-color: var(--cui-notification-btn-bg);
  }
}
.gc-status-bar {
  display: flex;
  height: 30px;
  background-color: inherit;
}
.gc-status-bar__items {
  background-color: inherit;
  flex-grow: 1;
}
.gc-status-bar__items .gc-toolbar, .gc-status-bar__items .gc-toolbar__content, .gc-status-bar__items .gc-toolbar__content:before {
  background-color: inherit;
}
.gc-status-bar__items .gc-toolbar__content {
  right: 0;
  left: auto;
  flex-direction: row-reverse;
  width: auto;
}
.gc-status-bar .gc-notifications-counter {
  display: flex;
}
.gc-status-bar .gc-notifications-counter__toggle.gc-dd--menu-open {
  background-color: var(--cui-dd-background);
}

.gc-notifications-counter__menu {
  box-shadow: 5px -5px 5px -5px rgba(0, 0, 0, 0.1);
}

.gc-heading {
  display: flex;
  align-items: center;
}
.gc-heading__container {
  display: flex;
  overflow: hidden;
  flex: 1 1 auto;
  margin: 0;
  padding: 0;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}
.gc-heading__text {
  overflow: hidden;
  min-width: 0;
  height: 30px;
  margin: 0;
  padding: 0;
  -webkit-user-select: none;
  user-select: none;
  white-space: nowrap;
  text-transform: uppercase;
  text-overflow: ellipsis;
  color: inherit;
  font-size: var(--cui-text-size);
  font-weight: normal;
  line-height: 30px;
}
.gc-heading__divider {
  flex: 10 10 auto;
  min-width: 16px;
  border-bottom: 1px solid;
}
.gc-heading__actions {
  flex: 0 0 auto;
}
.gc-heading--level-info .gc-heading__container {
  color: var(--cui-accent-text);
}
.gc-heading--level-warning .gc-heading__container {
  color: var(--cui-accent-warning);
}
.gc-heading--level-error .gc-heading__container {
  color: var(--cui-accent-error);
}
.gc-heading--size-md .gc-heading__text {
  height: 40px;
  line-height: 40px;
}
.gc-heading--size-lg .gc-heading__text {
  height: 50px;
  line-height: 50px;
}
.gc-heading--text-size-small .gc-heading__text {
  font-size: var(--cui-text-size-sm);
}
.gc-heading--text-size-large .gc-heading__text {
  font-size: var(--cui-text-size-lg);
}
.gc-heading--case-default .gc-heading__text {
  text-transform: none;
}
.gc-heading--align-center .gc-heading__container {
  justify-content: center;
}
.gc-heading--align-right .gc-heading__container {
  flex-direction: row-reverse;
}
.gc-heading--no-strike .gc-heading__divider {
  display: none;
}
.gc-heading--weight-light .gc-heading__text {
  font-weight: 300;
}
.gc-heading--weight-bold .gc-heading__text {
  font-weight: bold;
}

.gc-scrollbars:hover .gc-scrollbars__thumb, .gc-scrollbars--no-hide .gc-scrollbars__thumb {
  opacity: 1;
}
.gc-scrollbars--contrast .gc-scrollbars__thumb:after {
  background-color: var(--cui-scrollbar-color-contrast);
}
.gc-scrollbars--no-grow .gc-scrollbars__track--vertical {
  width: 4px;
}
.gc-scrollbars--no-grow .gc-scrollbars__track--horizontal {
  height: 4px;
}
.gc-scrollbars--no-grow.gc-scrollbars--size-sm .gc-scrollbars__track--vertical {
  width: 2px;
}
.gc-scrollbars--no-grow.gc-scrollbars--size-sm .gc-scrollbars__track--horizontal {
  height: 2px;
}
.gc-scrollbars--size-sm .gc-scrollbars__thumb--vertical:after {
  max-width: 2px;
}
.gc-scrollbars--size-sm .gc-scrollbars__thumb--horizontal:after {
  max-height: 2px;
}
.gc-scrollbars__view {
  z-index: 0;
  overflow: scroll;
}
.gc-scrollbars__view--vertical {
  overflow-x: hidden;
  margin-bottom: 0 !important;
}
.gc-scrollbars__view--horizontal {
  overflow-y: hidden;
  margin-right: 0 !important;
}
.gc-scrollbars__track {
  display: flex;
  pointer-events: none;
}
.gc-scrollbars__track--vertical {
  top: 2px;
  right: 0;
  bottom: 2px;
  width: 10px;
  justify-content: flex-end;
}
.gc-scrollbars__track--horizontal {
  right: 2px;
  bottom: 0;
  left: 2px;
  height: 10px;
  align-content: flex-end;
}
.gc-scrollbars__thumb {
  pointer-events: all;
  opacity: 0;
}
.gc-scrollbars__thumb.is-dragged {
  opacity: 1;
}
.gc-scrollbars__thumb:after {
  position: absolute;
  width: 100%;
  height: 100%;
  content: "";
  transition: max-width 0.2s ease-in-out, max-height 0.2s ease-in-out;
  border-radius: 4px;
  background-color: var(--cui-scrollbar-color);
}
.gc-scrollbars__thumb--vertical {
  transition: opacity 0.2s ease-in-out;
}
.gc-scrollbars__thumb--vertical:hover:after, .gc-scrollbars__thumb--vertical.is-dragged:after {
  max-width: 8px;
}
.gc-scrollbars__thumb--vertical:after {
  top: 0;
  right: 2px;
  max-width: 4px;
}
.gc-scrollbars__thumb--horizontal {
  transition: opacity 0.2s ease-in-out;
}
.gc-scrollbars__thumb--horizontal:hover:after, .gc-scrollbars__thumb--horizontal.is-dragged:after {
  max-height: 8px;
}
.gc-scrollbars__thumb--horizontal:after {
  bottom: 2px;
  left: 0;
  max-height: 4px;
}

@media (any-hover: none) {
  .gc-scrollbars__thumb--vertical:hover:after, .gc-scrollbars__thumb--vertical.is-dragged:after {
    max-width: 4px;
  }
  .gc-scrollbars__thumb--horizontal:hover:after, .gc-scrollbars__thumb--horizontal.is-dragged:after {
    max-height: 4px;
  }
}
.gc-treenode {
  position: relative;
}
.gc-treenode__subtree {
  padding-left: 20px;
}
.gc-treenode__subtree--outline > .gc-treenode:before {
  position: absolute;
  top: 15px;
  left: -5px;
  width: 3px;
  height: 1px;
  content: "";
  border-top: 1px solid var(--cui-treeview-outline-color);
}
.gc-treenode__subtree--outline > .gc-treenode:after {
  position: absolute;
  top: 0;
  left: -6px;
  width: 1px;
  height: 100%;
  content: "";
  border-left: 1px solid var(--cui-treeview-outline-color);
}
.gc-treenode__subtree--outline > .gc-treenode:last-of-type:before {
  display: none;
}
.gc-treenode__subtree--outline > .gc-treenode:last-of-type:after {
  position: absolute;
  top: 0;
  left: -6px;
  width: 4px;
  height: 15px;
  content: "";
  border-width: 1px;
  border-style: none none solid solid;
  border-color: var(--cui-treeview-outline-color);
}

.gc-treeitem__icon {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0.5;
  background-color: transparent !important;
}
.gc-treeitem__icon:hover {
  opacity: 1;
}
.gc-treeitem__text {
  width: 100%;
}
.gc-treeitem__text > .gc-btn__text {
  padding: 0 10px;
}
.gc-treeitem__text:not(:only-child) > .gc-btn__text {
  padding-left: 30px;
}

@media (any-hover: none) {
  .gc-treeitem__icon {
    opacity: 1;
  }
}
.gc-checklist {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
}
.gc-checklist *, .gc-checklist *:before, .gc-checklist *:after {
  box-sizing: border-box;
}
.gc-checklist--horizontal {
  flex-direction: row;
}
.gc-checklist--horizontal > .gc-check, .gc-checklist--horizontal > .gc-radio {
  flex: 1 0 100%;
  max-width: 100%;
}
.gc-checklist--horizontal.gc-col-1.gc-checklist--with-offset > .gc-check, .gc-checklist--horizontal.gc-col-1.gc-checklist--with-offset > .gc-radio {
  max-width: calc(100% - 5px);
}
.gc-checklist--horizontal.gc-col-2 > .gc-check, .gc-checklist--horizontal.gc-col-2 > .gc-radio {
  max-width: 50%;
}
.gc-checklist--horizontal.gc-col-2.gc-checklist--with-offset > .gc-check, .gc-checklist--horizontal.gc-col-2.gc-checklist--with-offset > .gc-radio {
  max-width: calc(100% / 2 - 5px);
}
.gc-checklist--horizontal.gc-col-3 > .gc-check, .gc-checklist--horizontal.gc-col-3 > .gc-radio {
  max-width: 33.3333333333%;
}
.gc-checklist--horizontal.gc-col-3.gc-checklist--with-offset > .gc-check, .gc-checklist--horizontal.gc-col-3.gc-checklist--with-offset > .gc-radio {
  max-width: calc(100% / 3 - 5px);
}
.gc-checklist--horizontal.gc-col-4 > .gc-check, .gc-checklist--horizontal.gc-col-4 > .gc-radio {
  max-width: 25%;
}
.gc-checklist--horizontal.gc-col-4.gc-checklist--with-offset > .gc-check, .gc-checklist--horizontal.gc-col-4.gc-checklist--with-offset > .gc-radio {
  max-width: calc(100% / 4 - 5px);
}
.gc-checklist--horizontal.gc-col-5 > .gc-check, .gc-checklist--horizontal.gc-col-5 > .gc-radio {
  max-width: 20%;
}
.gc-checklist--horizontal.gc-col-5.gc-checklist--with-offset > .gc-check, .gc-checklist--horizontal.gc-col-5.gc-checklist--with-offset > .gc-radio {
  max-width: calc(100% / 5 - 5px);
}
.gc-checklist--align-center {
  align-items: center;
}
.gc-checklist--align-right {
  align-items: flex-end;
}
.gc-checklist--with-offset:not(.gc-checklist--horizontal) .gc-check + .gc-check, .gc-checklist--with-offset:not(.gc-checklist--horizontal) .gc-radio + .gc-radio {
  margin-top: 5px;
}
.gc-checklist--with-offset.gc-checklist--horizontal {
  margin: -5px 0 0 -5px;
}
.gc-checklist--with-offset.gc-checklist--horizontal .gc-check, .gc-checklist--with-offset.gc-checklist--horizontal .gc-radio {
  margin: 5px 0 0 5px;
}

.gc-datetime {
  display: flex;
  flex-direction: column;
  width: 261px;
  height: 355px;
}
.gc-datetime__header {
  height: 70px;
}
.gc-datetime__body {
  position: relative;
  flex: 1 1 auto;
  height: 0;
  padding: 0 15px 15px;
}
.gc-datetime__footer {
  display: flex;
  height: 70px;
  margin: 0 15px;
  box-shadow: inset 0 1px 0 0 var(--cui-dd-divider);
  align-items: center;
}
.gc-datetime--barrel .gc-datetime__body {
  padding: 0 5px 15px;
}
.gc-datetime--extended-1 {
  height: 425px;
}
.gc-datetime--extended-2 {
  height: 495px;
}
@media screen and (max-width: 600px) {
  .gc-datetime {
    width: 100vw;
  }
}

.gc-datetime-header {
  display: grid;
  height: 100%;
  margin: 0 10px;
  grid-auto-flow: column;
}
.gc-datetime-header > .gc-btn {
  align-self: center;
}
.gc-datetime-header > .gc-btn:last-child {
  justify-self: end;
}
.gc-datetime-header > .gc-btn:first-child {
  justify-self: left;
}
.gc-datetime-header__buttons {
  display: grid;
  grid-auto-flow: column;
  justify-self: center;
}
.gc-datetime-header__title {
  -webkit-user-select: none;
  user-select: none;
  text-align: center;
  color: var(--cui-accent-text);
  font-size: var(--cui-text-size-lg);
  font-weight: bold;
  align-self: center;
}
.gc-datetime-header--contrast .gc-datetime-header__title {
  color: var(--cui-contrast-text);
}

.gc-datetime-footer-time {
  font-size: var(--cui-text-size-lg);
  font-weight: bold;
}

.gc-datetime-footer-control {
  width: calc(50% - 5px);
}
.gc-datetime-footer-control:first-child {
  margin-right: 10px;
}

.gc-datetime-date {
  width: 30px;
  height: 30px;
  -webkit-user-select: none;
  user-select: none;
}
.gc-datetime-date--neighbor {
  color: var(--cui-text-semi-40);
}
.gc-datetime-date--today {
  color: var(--cui-accent-text);
  font-weight: bold;
}
.gc-datetime-date--today:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 30px;
  height: 30px;
  content: "";
  transition: opacity 0.2s ease-in-out;
  transform: translate(-50%, -50%);
  border: 2px solid var(--cui-accent);
  border-radius: 50%;
}
.gc-datetime-date--selected {
  color: var(--cui-contrast);
  font-weight: bold;
}
.gc-datetime-date--selected:before {
  opacity: 0;
}
.gc-datetime-date--disabled {
  pointer-events: none;
  opacity: 0.38;
}

.gc-datetime-timer {
  position: absolute;
  top: 40%;
  left: 50%;
  display: flex;
  transform: translate(-50%, -50%);
}
.gc-datetime-timer__seconds {
  transform: translate(-55%, -50%);
}
.gc-datetime-timer__delimiter {
  display: flex;
  width: 15px;
  font-size: var(--cui-text-size-xl);
  justify-content: center;
  align-items: center;
}
.gc-datetime-timer .gc-clock__value, .gc-datetime-timer .gc-clock .gc-size-lg {
  width: 50px;
  font-size: var(--cui-text-size-xl);
}
.gc-datetime-timer__daypart {
  position: absolute;
  top: 50%;
  right: -40px;
  width: 40px;
  transform: translateY(-50%);
}
.gc-datetime-timer__daypart-title {
  text-align: center;
  color: var(--cui-accent-text);
  line-height: 25px;
}
.gc-datetime-timer__daypart-title--selected {
  font-weight: bold;
}

.gc-datetime-barrel {
  display: flex;
  width: 100%;
  height: 100%;
}
.gc-datetime-barrel--swapped {
  flex-direction: row-reverse;
}
.gc-datetime-barrel--swapped .gc-datetime-barrel__column:first-child {
  box-shadow: inset 1px 0 0 0 var(--cui-dd-divider);
}
.gc-datetime-barrel--swapped .gc-datetime-barrel__column:last-child {
  box-shadow: none;
}
.gc-datetime-barrel__column {
  width: 50%;
}
.gc-datetime-barrel__column:nth-child(2) {
  box-shadow: inset 1px 0 0 0 var(--cui-dd-divider);
}
.gc-datetime-barrel__column:only-child {
  width: 100%;
}
.gc-datetime-barrel__scroll {
  height: 100%;
  padding: 0 10px;
}
.gc-datetime-barrel__item {
  width: 100%;
}
.gc-datetime-barrel__item--selected .gc-btn__text {
  font-weight: bold;
}
.gc-datetime-barrel__years-container {
  position: relative;
}
.gc-datetime-barrel__year-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}

.gc-datetime-days {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.gc-datetime-days__header {
  display: flex;
  flex: 0 0 auto;
  height: 30px;
  font-size: var(--cui-text-size);
  font-weight: bold;
  line-height: 30px;
  justify-content: space-around;
}
.gc-datetime-days__content {
  display: flex;
  flex: 1 0 auto;
  box-shadow: inset 0 1px 0 0 var(--cui-dd-divider);
  flex-wrap: wrap;
}

.gc-datetime-date {
  position: relative;
  display: inline-flex;
  overflow: hidden;
  height: 34px;
  margin-top: auto;
  flex-grow: 1;
  flex-basis: 14.2857142857%;
  align-items: center;
  justify-content: center;
}
.gc-datetime-date:before {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  content: "";
  opacity: 0;
  background-color: var(--cui-calendar-range-fill-color);
}
.gc-datetime-date--large {
  height: 60px;
  flex-basis: 25%;
}
.gc-datetime-date--large .gc-datetime-date__button {
  width: 56px;
  height: 56px;
  border-radius: 50%;
}
.gc-datetime-date--large.gc-datetime-date--range-start:before {
  transform: translateX(-30px);
  border-radius: 60px;
}
.gc-datetime-date--large.gc-datetime-date--range-end:before {
  transform: translateX(30px);
  border-radius: 60px;
}
.gc-datetime-date--large.gc-datetime-date--range-both:before {
  width: 60px;
}
.gc-datetime-date--range-start:before {
  left: 50%;
  width: 200%;
  transition: opacity 0.2s ease-in-out;
  transform: translateX(-17px);
  opacity: 1;
  border-radius: 34px;
}
.gc-datetime-date--range-end:before {
  right: 50%;
  left: auto;
  width: 200%;
  transition: opacity 0.2s ease-in-out;
  transform: translateX(17px);
  opacity: 1;
  border-radius: 34px;
}
.gc-datetime-date--range-both:before {
  left: 50%;
  width: 34px;
  transition: opacity 0.2s ease-in-out;
  transform: translateX(-50%);
  opacity: 1;
  border-radius: 50%;
}
.gc-datetime-date--in-range:before {
  transition: opacity 0.2s ease-in-out;
  opacity: 1;
}

.gc-datetime-date__button {
  width: 30px;
  height: 30px;
  -webkit-user-select: none;
  user-select: none;
}
.gc-datetime-date__button--neighbor {
  color: var(--cui-text-semi-40);
}
.gc-datetime-date__button--today {
  color: var(--cui-accent-text);
  font-weight: bold;
}
.gc-datetime-date__button--today:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  content: "";
  transition: opacity 0.2s ease-in-out;
  transform: translate(-50%, -50%);
  border: 2px solid var(--cui-accent);
  border-radius: 50%;
}
.gc-datetime-date__button--selected {
  color: var(--cui-contrast);
  font-weight: bold;
}
.gc-datetime-date__button--selected:before {
  opacity: 0;
}
.gc-datetime-date__button .gc-btn__text {
  padding: 0;
}

@keyframes gc-loader-btn {
  0% {
    transform: scale(0.8);
    opacity: 0.62;
  }
  to {
    transform: scale(1.2);
    opacity: 1;
  }
}
.gc-loader-button::before, .gc-loader-button::after {
  position: absolute;
  top: calc(50% - 4.5px);
  display: block;
  width: 9px;
  height: 9px;
  content: "";
  border-radius: 50%;
  background-color: currentColor;
}
.gc-loader-button::before {
  left: calc(50% - 9px - 1px);
  animation: gc-loader-btn 0.4s ease-in-out infinite alternate;
}
.gc-loader-button::after {
  left: calc(50% + 1px);
  animation: gc-loader-btn 0.4s ease-in-out infinite alternate-reverse;
}

.gc-size-sm .gc-loader-button::before, .gc-size-sm .gc-loader-button::after {
  top: calc(50% - 3.5px);
  width: 7px;
  height: 7px;
}
.gc-size-sm .gc-loader-button::before {
  left: calc(50% - 7px - 1px);
}

.gc-size-lg .gc-loader-button::before, .gc-size-lg .gc-loader-button::after {
  top: calc(50% - 5.5px);
  width: 11px;
  height: 11px;
}
.gc-size-lg .gc-loader-button::before {
  left: calc(50% - 11px - 1px);
}

@keyframes gc-loader {
  50% {
    transform: scale(0.7);
    opacity: 0.62;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
.gc-loader {
  display: inline-flex;
  background-color: var(--cui-overlay);
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
}
.gc-loader > div {
  flex: 0 0 auto;
  width: 10px;
  height: 10px;
  margin: 1px;
  animation: gc-loader 0.8s 0s infinite linear;
  border-radius: 50%;
  background-color: currentColor;
}
.gc-loader > div:nth-child(2n-1) {
  animation-delay: -0.4s !important;
}
.gc-loader--block {
  display: flex;
}
.gc-loader--transparent {
  background-color: transparent;
}
.gc-loader--info > div {
  background-color: var(--cui-accent);
}
.gc-loader--warning > div {
  background-color: var(--cui-accent-warning);
}
.gc-loader--error > div {
  background-color: var(--cui-accent-error);
}

.gc-alert {
  --cui-alert-info-bg: var(--cui-accent-text-semi-10);
  --cui-alert-info-border: var(--cui-accent-text-semi-10);
  --cui-alert-warning-bg: var(--cui-accent-warning-text-semi-10);
  --cui-alert-warning-border: var(--cui-accent-warning-text-semi-10);
  --cui-alert-error-bg: var(--cui-accent-error-text-semi-10);
  --cui-alert-error-border: var(--cui-accent-error-text-semi-10);
}

.gc-alert {
  overflow: hidden;
  padding: 10px;
  border: 1px solid var(--cui-alert-info-border);
  border-radius: var(--cui-border-radius);
  background-color: var(--cui-alert-info-bg);
}
.gc-alert__main {
  display: flex;
  min-height: 30px;
  align-items: center;
}
.gc-alert__icon {
  display: flex;
  width: 30px;
  height: 30px;
  color: var(--cui-accent-text);
  align-items: center;
  justify-content: center;
}
.gc-alert__title {
  overflow: hidden;
  flex: 1 1 auto;
  width: 0;
  margin: 0;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: var(--cui-accent-text);
  font-size: var(--cui-text-size-lg);
  font-weight: normal;
  line-height: 30px;
}
.gc-alert__actions {
  margin-left: 15px;
}
.gc-alert__details {
  overflow-wrap: anywhere;
  padding: 4px 0 4px 6px;
  font-size: var(--cui-text-size);
}
.gc-alert--level-warning {
  border: 1px solid var(--cui-alert-warning-border);
  background-color: var(--cui-alert-warning-bg);
}
.gc-alert--level-warning > .gc-alert__main > .gc-alert__icon, .gc-alert--level-warning > .gc-alert__main > .gc-alert__title {
  color: var(--cui-accent-warning-text);
}
.gc-alert--level-error {
  border: 1px solid var(--cui-alert-error-border);
  background-color: var(--cui-alert-error-bg);
}
.gc-alert--level-error > .gc-alert__main > .gc-alert__icon, .gc-alert--level-error > .gc-alert__main > .gc-alert__title {
  color: var(--cui-accent-error-text);
}
.gc-alert--size-small {
  padding: 0;
  border: none;
}
.gc-alert--size-small .gc-alert__details {
  padding: 0 10px 10px 10px;
}
.gc-alert--size-small .gc-alert__title {
  font-size: var(--cui-text-size);
}
.gc-alert--size-small .gc-alert__title:first-child {
  margin-left: 10px;
}
.gc-alert--size-small .gc-alert__icon {
  margin-right: 0;
}

.gc-positioner {
  position: absolute;
  top: 0;
  left: 0;
}
.gc-positioner--calculating {
  visibility: hidden;
}
.gc-positioner--fill-container {
  width: 100%;
  height: 100%;
}

a.gc-link {
  color: var(--cui-accent-text);
}
a.gc-link:active, a.gc-link:focus, a.gc-link:visited {
  color: var(--cui-accent-text);
}
a.gc-link:hover {
  color: var(--cui-accent-text-hover);
}
a.gc-link--no-underline {
  text-decoration: none;
}

@media (any-hover: none) {
  a.gc-link:hover {
    color: var(--cui-accent-text);
  }
}
.gc-grid-item--appbar {
  background-color: var(--cui-bg-panels);
  box-shadow: inset 0 -1px 0 0 var(--cui-bg-panels-border);
  grid-area: appbar;
}
.gc-grid-item--legacy-appbar {
  background-color: var(--cui-accent);
  grid-area: appbar;
}
.gc-grid-item--menu {
  grid-area: menu;
}
.gc-grid-item--toolbar {
  display: flex;
  justify-content: flex-end;
  background-color: var(--cui-bg-panels);
  box-shadow: inset 0 -1px 0 0 var(--cui-bg-panels-border);
  grid-area: toolbar;
}
.gc-grid-item--toolbar > .gc-toolbar {
  flex: 1 1 auto;
  margin: 5px;
}
.gc-grid-item--surface {
  grid-area: surface;
}
.gc-grid-item--sidebar {
  grid-area: sidebar;
}
.gc-grid-item--statusbar {
  border-top: 1px solid var(--cui-bg-panels-border);
  background-color: var(--cui-bg-panels);
  grid-area: statusbar;
}

.gc-grid {
  position: relative;
  z-index: 0;
  display: grid;
  display: -ms-grid;
  width: 100%;
  height: 100%;
}
.gc-grid *, .gc-grid *:before, .gc-grid *:after {
  box-sizing: border-box;
}
.gc-grid--designer {
  -ms-grid-rows: auto auto minmax(0, 1fr) auto;
  -ms-grid-columns: auto minmax(0, 1fr) auto;
  grid-template: "appbar appbar appbar" auto "menu toolbar toolbar" auto "menu surface sidebar" minmax(0, 1fr) "menu statusbar statusbar" auto/auto minmax(0, 1fr) auto;
}
.gc-grid--designer > .gc-grid-item--appbar {
  -ms-grid-row: 1;
  -ms-grid-column: 1;
  -ms-grid-column-span: 3;
}
.gc-grid--designer > .gc-grid-item--legacy-appbar {
  -ms-grid-row: 1;
  -ms-grid-column: 1;
  -ms-grid-column-span: 3;
}
.gc-grid--designer > .gc-grid-item--menu {
  -ms-grid-row: 2;
  -ms-grid-row-span: 3;
  -ms-grid-column: 1;
}
.gc-grid--designer > .gc-grid-item--toolbar {
  -ms-grid-row: 2;
  -ms-grid-column: 2;
  -ms-grid-column-span: 2;
}
.gc-grid--designer > .gc-grid-item--surface {
  -ms-grid-row: 3;
  -ms-grid-column: 2;
}
.gc-grid--designer > .gc-grid-item--sidebar {
  -ms-grid-row: 3;
  -ms-grid-column: 3;
}
.gc-grid--designer > .gc-grid-item--statusbar {
  -ms-grid-row: 4;
  -ms-grid-column: 2;
  -ms-grid-column-span: 2;
}

.gc-app {
  position: relative;
  z-index: 0;
  overflow: hidden;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  color: var(--cui-text);
  background-color: var(--cui-bg-body);
  font-family: var(--cui-text-family);
}

.gc-resizable {
  position: relative;
}
.gc-resizable--disabled .gc-resizable__handler {
  display: none;
}
.gc-resizable--horizontal {
  height: 100%;
  will-change: width;
}
.gc-resizable--horizontal .gc-resizable__handler {
  top: 0;
  width: 10px;
  height: 100%;
  cursor: ew-resize;
}
.gc-resizable--vertical {
  width: 100%;
  will-change: height;
}
.gc-resizable--vertical .gc-resizable__handler {
  left: 0;
  width: 100%;
  height: 10px;
  cursor: ns-resize;
}
.gc-resizable__handler {
  position: absolute;
}
.gc-resizable__handler--side-left {
  left: -5px;
}
.gc-resizable__handler--side-right {
  right: -5px;
}
.gc-resizable__handler--side-top {
  top: -5px;
}
.gc-resizable__handler--side-bottom {
  bottom: -5px;
}

.gc-date-range {
  display: flex;
}
.gc-date-range__main {
  display: flex;
}
.gc-date-range__main .gc-datetime:first-child:not(:only-child) .gc-datetime__body {
  padding-right: 10px;
}
.gc-date-range__main .gc-datetime:nth-child(2) .gc-datetime__body {
  padding-left: 10px;
}
.gc-date-range__main .gc-datetime--months .gc-datetime__body, .gc-date-range__main .gc-datetime--years .gc-datetime__body {
  display: flex;
  flex-wrap: wrap;
  align-content: space-around;
}
.gc-date-range__aside {
  background-color: var(--cui-accent);
}
@media screen and (max-width: 812px) {
  .gc-date-range {
    flex-direction: column;
  }
  .gc-date-range__aside {
    border-radius: 0 0 var(--cui-border-radius) var(--cui-border-radius);
  }
}

.gc-date-range-aside {
  display: flex;
  flex-direction: column;
  width: 190px;
  height: 100%;
}
.gc-date-range-aside--simple .gc-date-range-shortcuts__list {
  padding: 15px 0;
}
.gc-date-range-aside__header {
  height: 70px;
}
.gc-date-range-aside__body {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  height: 0;
  padding: 0 5px;
}
.gc-date-range-aside__title {
  flex: 0 0 auto;
  height: 30px;
  margin: 0 10px;
  text-align: center;
  color: var(--cui-contrast-text);
  box-shadow: inset 0 -1px 0 0 var(--cui-menu-splitter);
  font-size: var(--cui-text-size);
  font-weight: bold;
  line-height: 30px;
}
.gc-date-range-aside__list {
  flex: 1 1 auto;
  overflow-y: auto;
  padding: 10px 0;
}
.gc-date-range-aside__item {
  width: 100%;
}
.gc-date-range-aside__scroll {
  height: 100%;
  padding: 0 10px;
}
@media screen and (max-width: 812px) {
  .gc-date-range-aside {
    width: 100%;
  }
  .gc-date-range-aside__body {
    height: 100%;
  }
  .gc-date-range-aside__list {
    max-height: 200px;
  }
}

.gc-btn.gc-size-sm .gc-btn__icon:not(.gc-icon--custom) > svg {
  width: 16px;
  height: 16px;
}
.gc-btn.gc-size-lg .gc-btn__icon > svg {
  width: 24px;
  height: 24px;
}

.gc-dd-menu__item > .gc-icon > svg {
  flex: 0 0 auto;
}
.gc-dd-menu__item.gc-size-sm > .gc-icon:not(.gc-icon--custom) > svg {
  width: 16px;
  height: 16px;
}
.gc-dd-menu__item.gc-size-lg > .gc-icon:not(.gc-icon--custom) > svg {
  width: 24px;
  height: 24px;
}

.gc-icon > svg {
  width: 24px;
  height: 24px;
}
.gc-icon--small > svg {
  width: 16px;
  height: 16px;
}
.gc-icon--large > svg {
  width: 24px;
  height: 24px;
}
.gc-icon--core > svg {
  width: initial !important;
  height: initial !important;
}

.gc-icon-color {
  transition: fill 0.2s ease-in-out, stroke 0.2s ease-in-out;
}
.gc-icon-color--text {
  transition: fill 0.2s ease-in-out;
  fill: currentColor;
}
.gc-icon-color--stroke-text {
  transition: stroke 0.2s ease-in-out;
  stroke: currentColor;
}
.gc-icon-color--accent {
  transition: fill 0.2s ease-in-out;
  fill: var(--cui-accent-secondary);
}
.gc-icon-color--stroke-accent {
  transition: stroke 0.2s ease-in-out;
  stroke: var(--cui-accent-secondary);
}

/* -------------- Designer Core Styles --------------- */
/*
 *
 * Data Visualiztion Library
 *
 * Copyright(c) GrapeCity, Inc.  All rights reserved.
 * 
 */
.gcdv-control {
  outline: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.gcdv-flexdv {
  position: relative;
}

.gcdv-state-disabled {
  opacity: 0.5;
  cursor: default;
  pointer-events: none;
}

.gcdv-tooltip {
  position: absolute;
  z-index: 1000;
  top: 0;
  left: 0;
  pointer-events: none;
  max-width: 400px;
  padding: 6px;
  background-color: #ffffe5;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  box-sizing: border-box;
  line-height: 110%;
}

.gcdv-tooltip-color-symbol {
  width: 0.8em;
  height: 0.8em;
  margin: 0px 1.5px 0px 1.5px;
  -moz-border-radius: 50%;
  -webkit-border-radius: 50%;
  border-radius: 50%;
  border-style: solid;
  box-sizing: border-box;
}

.gcdv-popup {
  background-color: #fff;
  box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  z-index: 1500;
  margin: 2px 0;
}

.gcdv-popup-backdrop {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1500;
  background-color: rgba(0, 0, 0, 0.5);
}

/* Scrollbar */
.gcdv-scrollbar-x.gcdv-scrolling > .gcdv-scrollbar-track,
.gcdv-scrollbar-x > .gcdv-scrollbar-track:hover {
  background-color: #eee;
  opacity: 0.9;
}

.gcdv-scrollbar-y.gcdv-scrolling > .gcdv-scrollbar-track,
.gcdv-scrollbar-y > .gcdv-scrollbar-track:hover {
  background-color: #eee;
  opacity: 0.9;
}

.gcdv-scrollbar-x.gcdv-scrolling > .gcdv-scrollbar-track > .gcdv-scrollbar-thumb,
.gcdv-scrollbar-x > .gcdv-scrollbar-track:hover > .gcdv-scrollbar-thumb {
  top: 1px;
  bottom: 1px;
}

.gcdv-scrollbar-y.gcdv-scrolling > .gcdv-scrollbar-track > .gcdv-scrollbar-thumb .gcdv-scrollbar-y > .gcdv-scrollbar-track:hover > .gcdv-scrollbar-thumb {
  left: 1px;
  right: 1px;
}

.gcdv-scrollbar-x > .gcdv-scrollbar-track {
  opacity: 0.6;
  border-radius: 4px;
}

.gcdv-scrollbar-y > .gcdv-scrollbar-track {
  opacity: 0.6;
  border-radius: 6px;
}

.gcdv-scrollbar-x .gcdv-scrollbar-thumb {
  position: absolute;
  top: 2px;
  bottom: 2px;
  border-radius: 6px;
  background-color: #aaa;
  transition: background-color 0.5s linear, height 0.5s ease-in-out;
  -webkit-transition: background-color 0.5s linear, height 0.5s ease-in-out;
}

.gcdv-scrollbar-y .gcdv-scrollbar-thumb {
  position: absolute;
  left: 2px;
  right: 2px;
  border-radius: 6px;
  background-color: #aaa;
  transition: background-color 0.5s linear, width 0.5s ease-in-out;
  -webkit-transition: background-color 0.5s linear, width 0.5s ease-in-out;
}

/* End */
/* Zoom Button */
.gcdv-zoom-container-buttons, .gcdv-zoom-buttons {
  transition: background-color 0.5s;
  -webkit-transition: background-color 0.5s;
}

[class*=gcdv-zoom-button-] {
  color: rgb(102, 102, 102);
  position: relative;
  border-radius: 8px;
  cursor: pointer;
  border: 0px solid;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.175);
  padding: 5px;
  transition: background-color 0.5s;
  -webkit-transition: background-color 0.5s;
}

@keyframes button-click-animation {
  0% {
    background-color: transparent;
  }
  50% {
    background-color: rgba(0, 0, 0, 0.2);
  }
  100% {
    background-color: transparent;
  }
}
@-webkit-keyframes button-click-animation {
  0% {
    background-color: transparent;
  }
  50% {
    background-color: rgba(0, 0, 0, 0.2);
  }
  100% {
    background-color: transparent;
  }
}
[class*=gcdv-zoom-button-]:active::before {
  display: block;
  content: "";
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  animation: button-click-animation 0.16s ease-in-out alternate;
  -webkit-animation: button-click-animation 0.16s ease-in-out alternate;
  transition: background-color 0.1s;
  -webkit-transition: background-color 0.1s;
}

[class*=gcdv-zoom-button-]:focus {
  outline: 0;
}

.gcdv-container-icon {
  position: relative;
  display: inline-block;
  cursor: pointer;
  box-sizing: border-box;
  margin: 0;
}

[class*=gcdv-icon] {
  display: inline-block;
  vertical-align: middle;
  position: relative;
  font-style: normal;
  text-align: left;
  box-sizing: border-box;
  width: 24px;
  height: 24px;
}

[class*=gcdv-icon]:before, [class*=gcdv-icon]:after {
  position: absolute;
  box-sizing: border-box;
}

.gcdv-zoom-button-zoomin .gcdv-icon:before {
  content: " ";
  border: 1px solid;
  width: 12px;
  height: 2px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
}

.gcdv-zoom-button-zoomin .gcdv-icon:after {
  content: " ";
  border: 1px solid;
  width: 2px;
  height: 12px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
}

.gcdv-zoom-button-zoomout .gcdv-icon:before {
  content: " ";
  border: 1px solid;
  width: 12px;
  height: 2px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
}

.gcdv-zoom-button-reset .gcdv-icon {
  border: 2.5px solid;
  border-radius: 50%;
  border-right-color: transparent !important;
  transform: scale(0.65);
  -webkit-transform: scale(0.65);
  -ms-transform: scale(0.65);
}

.gcdv-zoom-button-reset .gcdv-icon:before {
  content: " ";
  height: 8px;
  width: 8px;
  border: 2.5px solid;
  background-color: transparent !important;
  border-top-color: transparent !important;
  border-left-color: transparent !important;
  left: 11px;
  top: -4px;
}

/* End */
:root {
  --wd-rulers-bg: white;
  --wd-rulers-span-primary: #1A4B5E;
  --wd-rulers-span-secondary: #0B3546;
  --wd-bg-panels-light: #f8f8f8;
}

.wd-drop-area {
  transition: border 0.2s ease-in-out, background-color 0.2s ease-in-out;
  border: 1px solid transparent;
  border-radius: 4px;
  background-color: transparent;
}
.wd-drop-area--can-drop {
  border: 1px dashed var(--cui-accent-semi-60);
  background-color: var(--cui-accent-semi-10);
}
.wd-drop-area--hover {
  border: 1px solid var(--cui-accent);
  background-color: var(--cui-accent-semi-40);
}

.wd-property-grid .gc-property-category__title, .wd-property-list .gc-property-category__title {
  margin-bottom: 5px;
}
.wd-property-grid .gc-property-category__title .gc-heading__text, .wd-property-list .gc-property-category__title .gc-heading__text {
  font-weight: bold;
}
.wd-property-grid .gc-property-category__title .gc-heading__divider, .wd-property-list .gc-property-category__title .gc-heading__divider {
  opacity: 0.1;
}

.wd-surface-grid {
  display: flex;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.wd-icon-color-accent {
  fill: var(--cui-accent);
}

.wd-icon-color-secondary {
  fill: var(--cui-accent-secondary);
}

.wd-icon-color-body {
  fill: currentColor;
}

.wd-icon-color-none {
  fill: none;
}

.wd-icon-color-white {
  fill: white;
}

.wd-icon-color-light-blue {
  fill: #9cf;
}

.wd-icon-color-light-blue-second {
  fill: #bcddff;
}

.wd-icon-color-light-gray-blue {
  fill: #9fc9eb;
}

.wd-icon-color-gray-blue {
  fill: #819cce;
}

.wd-icon-color-blue {
  fill: #1c7cdc;
}

.wd-icon-color-blue-second {
  fill: #007bf4;
}

.wd-icon-color-blue-third {
  fill: #6cf;
}

.wd-icon-color-dark-blue {
  fill: #3b79bd;
}

.wd-icon-color-gray {
  fill: #b3b3b3;
}

.wd-icon-color-gray-second {
  fill: #b4b4b4;
}

.wd-icon-color-gray-third {
  fill: #737373;
}

.wd-icon-color-gray-fourth {
  fill: #808080;
}

.wd-icon-color-dark-gray {
  fill: #727272;
}

.wd-icon-color-dark-yellow {
  fill: #f5c633;
}

.wd-icon-color-red {
  fill: #f00;
}

.wd-icon-color-red-second {
  fill: #ff1e00;
}

.wd-toolbox-icon-fill-gray {
  fill: gray;
}

.wd-toolbox-icon-fill-gray-second {
  fill: #969696;
}

.wd-toolbox-icon--main {
  fill: var(--cui-text-semi-60);
}

.wd-toolbox-icon--main-accent {
  fill: var(--cui-accent);
}

.wd-toolbox-icon--accent {
  fill: var(--cui-accent);
}

.wd-toolbox-icon--secondary-accent {
  fill: var(--cui-accent-secondary);
}

.wd-icon-color-adorner-blue {
  fill: #000080;
}

.wd-icon-color-adorner-gray {
  fill: #ababab;
}

.wd-if-c-white {
  fill: white;
}

.wd-if-c-text {
  fill: var(--cui-text);
}

.wd-icon-fill-class-text {
  fill: var(--cui-text);
}

.wd-icon-fill-class-accent {
  fill: var(--cui-accent);
}

.wd-icon-fill-class-secondary-accent {
  fill: var(--cui-accent-secondary);
}

.wd-icon-fill-class-error {
  fill: var(--cui-accent-error);
}

.wd-icon-opacity-class-60 {
  fill-opacity: 0.6;
}

.wd-icon-opacity-class-40 {
  fill-opacity: 0.4;
}

.wd-icon-opacity-class-20 {
  fill-opacity: 0.2;
}

.wd-invert-icons .wd-icon-fill-class-text {
  fill: var(--cui-contrast);
}
.wd-invert-icons .wd-icon-fill-class-accent {
  fill: var(--cui-contrast);
}
.wd-invert-icons .wd-icon-fill-class-secondary-accent {
  fill: var(--cui-accent-secondary);
}
.wd-invert-icons .wd-icon-fill-class-error {
  fill: var(--cui-accent-error);
}

.wd-master-report-errors-list {
  list-style-type: none;
  margin: 10px 0;
  padding: 0;
}
.wd-master-report-errors-list li {
  margin: 7px 0;
  padding-left: 15px;
}
.wd-master-report-errors-list .gc-alert__title {
  white-space: pre-line;
  line-height: normal;
  padding: 5px 10px 5px 0;
}
.wd-master-report-errors-list .gc-alert__icon {
  margin-bottom: auto;
}

.wd-menu .gc-menu__panel-toggle .gc-btn__icon .gc-ci-a-accent {
  fill: var(--cui-contrast-text);
}

.wd-menu-logo {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 100%;
  flex-wrap: nowrap;
  align-items: center;
}
.wd-menu-logo__icon {
  display: block;
  flex: 0 0 auto;
  width: 50px;
  height: 16px;
  margin-top: 4px;
}
.wd-menu-logo__icon > svg {
  width: 16px;
  height: 16px;
  margin: 0 auto;
}
.wd-menu-logo__text {
  margin-top: 4px;
  margin-left: 15px;
  white-space: nowrap;
  text-overflow: ellipsis;
  opacity: 0;
  color: var(--cui-contrast-text);
  font-size: var(--cui-text-size-sm);
}
.wd-menu-logo__icon + .wd-menu-logo__text {
  margin-left: -5px;
}

.wd-menu-toggle {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 100%;
  align-items: center;
  flex-wrap: nowrap;
}
.wd-menu-toggle__burger {
  display: flex;
  flex: 0 0 auto;
  width: 50px;
  height: 50px;
  margin-top: -2px;
  align-items: center;
}
.wd-menu-toggle__burger > div {
  position: relative;
  width: 20px;
  height: 14px;
  margin: 13px auto;
  cursor: pointer;
  transition: 0.5s ease-in-out;
  transform: rotate(0deg);
}
.wd-menu-toggle__burger > div > span {
  position: absolute;
  left: 0;
  display: block;
  width: 100%;
  height: 2px;
  transition: 0.25s ease-in-out;
  transform: rotate(0deg);
  transform-origin: left center;
  opacity: 1;
  border-radius: 2px;
  background: var(--cui-contrast-text);
}
.wd-menu-toggle__burger > div > span:nth-child(1) {
  top: 0;
}
.wd-menu-toggle__burger > div > span:nth-child(2) {
  top: 50%;
}
.wd-menu-toggle__burger > div > span:nth-child(3) {
  top: 100%;
}
.wd-menu-toggle__text {
  white-space: nowrap;
  text-overflow: ellipsis;
  opacity: 0;
  color: var(--cui-contrast-text);
}

.gc-menu__logo--expanded .wd-menu-logo__text {
  opacity: 1;
}

.gc-menu--expanded .wd-menu-toggle__burger > div > span:nth-child(1) {
  left: 3px;
  transform: rotate(45deg);
}
.gc-menu--expanded .wd-menu-toggle__burger > div > span:nth-child(2) {
  width: 0;
  opacity: 0;
}
.gc-menu--expanded .wd-menu-toggle__burger > div > span:nth-child(3) {
  top: 100%;
  left: 3px;
  transform: rotate(-45deg);
}
.gc-menu--expanded .wd-menu-toggle__text {
  opacity: 1;
}

.wd-toolbox-item {
  position: relative;
  width: 100%;
  height: 50px;
  padding-left: 50px;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out, opacity 0.2s ease-in-out;
  text-align: left;
  vertical-align: top;
  color: var(--cui-contrast-text);
  border-radius: 4px;
  background-color: var(--cui-btn-bg);
  background-color: var(--cui-accent);
  font-size: var(--cui-text-size);
  line-height: 50px;
}
.wd-toolbox-item > i.gc-btn__icon {
  width: 50px;
  height: 50px;
}
.wd-toolbox-item > i.gc-btn__icon .gc-ci-a-accent {
  fill: var(--cui-contrast-text);
}
.wd-toolbox-item > .gc-btn__text {
  height: 50px;
  padding-left: 0;
  vertical-align: middle;
  line-height: 50px;
}
.wd-toolbox-item:hover {
  background-color: var(--cui-accent-hover);
}
.wd-toolbox-item svg {
  pointer-events: none;
}

.gc-menu__btn-container__scrollable-container .gc-scrollbars__thumb {
  background-color: --cui-scrollbar-color-contrast;
}

.wd-multiple-menu-item--selected {
  background-color: var(--cui-accent-hover);
}
.wd-multiple-menu-item--selected .gc-btn__icon {
  transform: translateX(-5px);
}
.wd-multiple-menu-item--selected .wd-multiple-menu-item__chevron {
  transform: translate(-50%, -50%) translateX(12px);
  opacity: 1;
}
.wd-multiple-menu-item .gc-btn__icon {
  transition: transform 0.2s ease-in-out;
}
.wd-multiple-menu-item__dd {
  display: block;
  opacity: 1;
}
.wd-multiple-menu-item__dd--transition {
  transition: opacity 0.2s ease-in-out;
  opacity: 0;
}
.wd-multiple-menu-item__dd--hidden {
  display: none;
}
.wd-multiple-menu-item__dd:hover {
  display: block;
  opacity: 1;
}
.wd-multiple-menu-item__chevron {
  position: absolute;
  top: 50%;
  left: 28px;
  transition: transform 0.2s ease-in-out, opacity 0.2s ease-in-out;
  transform: translate(-50%, -50%);
  opacity: 0;
  color: var(--cui-contrast-text-semi-40);
}
.wd-multiple-menu-item__custom-dd-menu-item {
  display: grid;
  width: 100%;
  grid-auto-flow: column;
  grid-template-columns: min-content 1fr;
  align-items: center;
  gap: 15px;
}
.wd-multiple-menu-item__custom-dd-menu-item > span {
  overflow: hidden;
  text-overflow: ellipsis;
}

.gc-appbar__logo-container .gc-btn.wd-menu-back-button > .gc-btn__icon {
  width: 50px;
}
.gc-appbar__logo-container .gc-btn.wd-menu-back-button > .gc-btn__text {
  padding-left: 50px;
  opacity: 0;
}
.gc-appbar__logo-container .gc-menu__logo--expanded .gc-btn.wd-menu-back-button > .gc-btn__text {
  opacity: 1;
}

.wd-explorer {
  display: inline-flex;
  min-width: 100%;
  padding: 10px;
}
.wd-explorer > .gc-treeview {
  min-width: 100%;
}

.wd-explorer-node__button {
  width: 100%;
}
.wd-explorer-node__button.gc-btn.gc-size-sm > .gc-btn__icon > svg {
  width: 24px;
  height: 24px;
}
.wd-explorer-node__chevron {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0.5;
  background-color: transparent;
}
.wd-explorer-node__chevron:not([disabled]):not(.gc-btn--disabled):hover {
  opacity: 1;
  background-color: transparent;
}
.wd-explorer-node__chevron--collapsed {
  transform: rotate(-90deg);
}
.wd-explorer-node--non-leaf .wd-explorer-node__button {
  padding-left: 20px;
}
.wd-explorer-node--non-leaf .wd-explorer-node__button > .gc-btn__icon {
  left: 20px;
}
.wd-explorer-node--leaf .wd-explorer-node__button {
  width: calc(100% - 20px);
  margin-left: 20px;
}

.wd-explorer-node__button--selected {
  background-color: var(--cui-accent-text-semi-10);
}
.wd-explorer-node__button--selected:not([disabled]):not(.gc-btn--disabled):hover {
  background-color: var(--cui-accent-text-semi-10);
}
.wd-explorer-node__button--selected .gc-btn__text {
  color: var(--cui-accent-text);
}

.wd-sidebar-block {
  position: relative;
  width: 100%;
  padding: 0 15px;
}

.wd-sidebar-block__heading {
  width: 100%;
  padding: 10px 0 5px 0;
}
.wd-sidebar-block__content {
  width: 100%;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--cui-bg-panels-border);
}
.wd-sidebar-block__deprecated {
  position: absolute;
  top: 10px;
  right: 15px;
  height: 30px;
}
.wd-sidebar-block__placeholder {
  display: block;
  overflow: hidden;
  width: 100%;
  user-select: none;
  text-align: center;
  color: var(--cui-text-semi-60);
  font-size: var(--cui-text-size);
  line-height: 40px;
}

.wd-sidebar-item {
  display: flex;
  overflow: hidden;
  min-height: 30px;
  padding-right: 5px;
  border-radius: 4px;
  align-items: flex-start;
  justify-content: stretch;
}
.wd-sidebar-item > .gc-icon {
  display: flex;
  flex: 0 0 40px;
  width: 40px;
  height: 40px;
  align-items: center;
  justify-content: center;
}
.wd-sidebar-item__icon-drag {
  display: block;
  flex: 0 0 40px;
  width: 40px;
  height: 40px;
}
.wd-sidebar-item__icon-drag > .gc-icon {
  display: flex;
  width: 40px;
  height: 40px;
  align-items: center;
  justify-content: center;
}
.wd-sidebar-item > span, .wd-sidebar-item__title, .wd-sidebar-item__subtitle {
  display: block;
  overflow: hidden;
  user-select: none;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: var(--cui-text);
  font-size: var(--cui-text-size);
  line-height: 40px;
}
.wd-sidebar-item__title {
  padding-top: 5px;
  line-height: 15px;
}
.wd-sidebar-item__subtitle {
  color: var(--cui-text-semi-60);
  line-height: 15px;
}
.wd-sidebar-item__text {
  overflow: hidden;
  flex: 1 1 100%;
  padding-bottom: 5px;
}
.wd-sidebar-item__controls {
  display: flex;
  flex-direction: row-reverse;
  height: 40px;
  align-items: center;
  justify-content: flex-end;
  flex-grow: 0;
  flex-shrink: 0;
}
.wd-sidebar-item--expanded {
  margin-left: 30px;
}
.wd-sidebar-item:hover {
  background-color: var(--cui-btn-bg);
}

.wd-theme-dialog {
  padding: 15px;
}
.wd-theme-dialog .theme-color-editor > input {
  padding-right: 15px;
}
.wd-theme-dialog .theme-color-editor__preview {
  overflow: hidden;
  width: 20px;
  height: 20px;
  margin: 0;
  border-radius: 4px;
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);
}
.wd-theme-dialog .theme-color-editor > span {
  display: block;
  float: left;
  width: 10px;
  height: 10px;
}
.wd-theme-dialog .theme-editor-line {
  display: flex;
  justify-content: space-between;
}
.wd-theme-dialog .theme-editor-line__block {
  width: 49%;
}

.wd-btn-units {
  width: 30px;
  text-align: center;
}
.wd-btn-units .gc-btn__text {
  padding: 0;
}

.wd-snap-settings {
  padding: 5px 15px 0 15px;
}
.wd-snap-settings__container {
  margin-bottom: 10px;
}

.wd-zoom-control {
  display: flex;
}
.wd-zoom-control__value > .gc-btn {
  min-width: 45px;
  text-align: center;
}
.wd-zoom-control__value > .gc-btn .gc-btn__text {
  padding: 0;
}

.wd-toolstrip-color-dropdown > .gc-btn {
  padding-left: 0;
}

.wd-toolstrip-color-preview {
  overflow: hidden;
  width: 40px;
  height: 40px;
}
.wd-toolstrip-color-preview > svg {
  display: block;
  width: 16px;
  height: 16px;
  margin: 10px auto 0;
}
.wd-toolstrip-color-preview > div {
  width: 24px;
  height: 6px;
  margin: 0 auto;
  border-radius: 2px;
}

.ar-rulers {
  position: relative;
  display: grid;
  min-width: fit-content;
  min-height: 100%;
  user-select: none;
  grid-template-areas: "pivot ruler-top" "ruler-left document-container";
  grid-template-columns: min-content auto;
  grid-template-rows: min-content auto;
}
.ar-rulers__pivot {
  position: sticky;
  z-index: 9300;
  top: 0;
  left: 0;
  border-right: 1px solid var(--cui-bg-panels-border);
  border-bottom: 1px solid var(--cui-bg-panels-border);
  background-color: var(--cui-bg-panels);
  grid-area: pivot;
}
.ar-rulers__document-container {
  position: relative;
  grid-area: document-container;
}
.ar-rulers__disabled {
  pointer-events: none;
}

.ar-ruler-guide {
  position: absolute;
  z-index: 9100;
}

.ar-ruler-guide--vertical {
  top: 0;
  width: 0;
  height: 100%;
  margin-left: 63px;
  border-left: 2px var(--cui-accent-text) dashed;
}

.ar-ruler-guide--horizontal {
  left: 0;
  width: 100%;
  height: 0;
  margin-top: 63px;
  border-top: 2px var(--cui-accent-text) dashed;
}

.ar-ruler-guide--vertical.ar-ruler-guide--bold {
  border-left: 2px var(--cui-accent) solid;
}

.ar-ruler-guide--horizontal.ar-ruler-guide--bold {
  border-top: 2px var(--cui-accent) solid;
}

.ar-ruler-container {
  position: sticky;
  z-index: 9200;
  background-color: var(--cui-bg-panels);
}
.ar-ruler-container--top {
  top: 0;
  padding-left: 64px;
  border-bottom: 1px solid var(--cui-bg-panels-border);
  grid-area: ruler-top;
}
.ar-ruler-container--left {
  left: 0;
  padding-top: 64px;
  border-right: 1px solid var(--cui-bg-panels-border);
  grid-area: ruler-left;
}

.ar-ruler {
  position: relative;
}

.ar-ruler-span {
  position: absolute;
  background-color: var(--wd-rulers-bg);
}

.ar-ruler--top .ar-ruler-span {
  top: 0;
  height: 100%;
}

.ar-ruler--left .ar-ruler-span {
  left: 0;
  width: 100%;
}

.ar-ruler-scale {
  position: relative;
  display: block;
  box-shadow: inset 0 0 0 1px var(--cui-bg-panels-border);
}
.ar-ruler-scale--top {
  margin-top: -1px;
  margin-bottom: -1px;
}
.ar-ruler-scale--left {
  margin-right: -1px;
  margin-left: -1px;
}
.ar-ruler-scale__ticks {
  stroke: var(--cui-text);
  stroke-opacity: 0.25;
}
.ar-ruler-scale text {
  font-family: inherit;
  font-size: 11px;
  fill: var(--cui-text-semi-60);
}

.ar-ruler-marker {
  position: absolute;
  display: grid;
  gap: 5px;
}
.ar-ruler-marker__tooltip {
  padding: 0 3px;
  color: var(--cui-contrast-text);
  border-radius: 4px;
  background-color: var(--cui-accent);
  box-shadow: var(--cui-shadow-border);
  font-size: 11px;
}
.ar-ruler-marker--top {
  bottom: 0;
  cursor: ew-resize;
  justify-items: center;
  justify-content: center;
  grid-template-rows: 100% auto;
}
.ar-ruler-marker--left {
  right: 0;
  cursor: ns-resize;
  align-items: center;
  grid-template-columns: 100% auto;
}

.ar-ruler-marker__tick {
  background-color: var(--cui-bg-panels-border);
}

.ar-ruler-marker--top .ar-ruler-marker__tick {
  width: 2px;
}

.ar-ruler-marker--left .ar-ruler-marker__tick {
  height: 2px;
}

.quick-edit-box {
  position: absolute;
  width: 0;
  height: 0;
  padding: 0;
  resize: none;
  text-align: initial;
  border-width: 0;
  background-color: transparent;
  box-shadow: inset 0 0 0 1px;
}

.quick-edit-box--visible {
  width: 100%;
  height: 100%;
  padding: 2pt;
}

.quick-edit-box__clicker {
  position: absolute;
  width: 100%;
  height: 100%;
}

.wd-number-editor {
  display: flex;
  width: 100%;
}
.wd-number-editor__button {
  flex: 0 0 auto;
}
.wd-number-editor__button:first-of-type {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.wd-number-editor__button:last-of-type {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.wd-number-editor__input.gc-input {
  flex: 2 2 auto;
  width: 100%;
  text-align: center;
  border-radius: 0;
}

.wd-color-editor > input {
  padding-right: 15px;
}
.wd-color-editor .wd-color-editor__preview {
  width: 18px;
  height: 18px;
  margin: 0;
}
.wd-color-editor div.wd-color-editor-side__preview {
  overflow: hidden;
  width: 18px;
  height: 18px;
  margin: 0;
  border-radius: 4px;
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);
}
.wd-color-editor div.wd-color-editor-side__preview > span {
  display: block;
  float: left;
  width: 9px;
  height: 9px;
}

.wd-line-style-editor__preview-box {
  display: flex;
  width: 18px;
  height: 18px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  align-items: center;
}
.wd-line-style-editor__preview-content {
  width: 100%;
  border-top-width: 2px;
}
.wd-line-style-editor__preview-content--double {
  border-top-width: 4px;
}

.wd-binding-wrapper--error .gc-binding-wrapper__toggle {
  background-color: red;
}

.wd-binding-wrapper--inherited .gc-binding-wrapper__toggle {
  background-color: #0074e0;
}

.wd-hatch-style-editor__preview-box {
  display: flex;
  width: 18px;
  height: 18px;
  border-radius: 4px;
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);
  align-items: center;
  justify-content: center;
}

.wd-gradient-style-editor__preview-box {
  display: flex;
  width: 18px;
  height: 18px;
  border-radius: 4px;
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);
  align-items: center;
  justify-content: center;
}

.wd-chart-editor-tile {
  display: flex;
  overflow: hidden;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 0 0 auto;
  width: 33.3333333333%;
  margin: 0;
  padding: 5px;
  user-select: none;
  transition: background-color 0.2s ease-in-out;
  pointer-events: all;
  border-radius: 4px;
}
.wd-chart-editor-tile:hover:not(.wd-chart-editor-tile--selected) {
  background-color: var(--cui-dd-background-hover);
}
.wd-chart-editor-tile > div {
  width: 100%;
  height: 100%;
  pointer-events: none;
}
.wd-chart-editor-tile--wizard > div {
  width: 65%;
  height: 65%;
  padding-top: 5px;
}
.wd-chart-editor-tile__label {
  display: block;
  overflow: hidden;
  font-size: var(--cui-text-size);
  line-height: 30px;
  text-align: center;
}
.wd-chart-editor-tile #palette-label {
  font-size: var(--cui-text-size);
  line-height: 30px;
  text-anchor: middle;
  fill: var(--cui-text);
}
.wd-chart-editor-tile #palette-stroke {
  fill: transparent;
  stroke: var(--cui-text);
  stroke-linejoin: round;
}
.wd-chart-editor-tile--selected {
  color: var(--cui-contrast-text);
  background-color: var(--cui-accent);
}
.wd-chart-editor-tile--selected #palette-stroke {
  stroke: var(--cui-contrast-text);
}
.wd-chart-editor-tile--selected #palette-label {
  fill: var(--cui-contrast-text);
}

.wd-chart-editor-group {
  display: block;
  width: 100%;
}
.wd-chart-editor-group__heading {
  display: block;
  width: 100%;
  min-height: 40px;
  padding: 5px 15px;
}
.wd-chart-editor-group__content {
  display: flex;
  width: 100%;
  padding: 0 15px;
  flex-wrap: wrap;
}

.wd-chart-palette-editor .wd-chart-editor-tile {
  width: 25%;
}
.wd-chart-palette-editor .wd-chart-editor-tile > div {
  width: calc(100% - 10px - 10px);
  height: 22px;
  margin-left: 10px;
}

.ar-decorationlayer {
  position: relative;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: all;
}
.ar-decorationlayer .ar-decorationview {
  position: absolute;
  z-index: 100000;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.ar-semantic-data-field-picker-dropdown-content {
  position: relative;
  display: block;
  font-size: 12px;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-dropdown-content-separator {
  position: relative;
  border-bottom: 1px solid var(--cui-bg-panels-border);
  border-top: 1px solid var(--cui-bg-panels-border);
  height: 3px;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity {
  position: relative;
  height: auto;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-title {
  padding: 0 30px;
  background-color: transparent;
  position: relative;
  height: 30px;
  line-height: 30px;
  user-select: none;
  cursor: pointer;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-title .ar-semantic-data-field-picker-entity-title-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-title > span {
  display: block;
  position: absolute;
  top: 5px;
  left: 5px;
  width: 20px;
  height: 20px;
  text-align: center;
  line-height: 20px;
  font-size: 15px;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-title:hover {
  background-color: var(--cui-accent-hover);
  color: var(--cui-contrast-text);
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-fields {
  position: relative;
  min-width: 100%;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-fields .ar-semantic-data-field-picker-attribute {
  display: block;
  position: relative;
  min-width: 100%;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-fields .ar-semantic-data-field-picker-attribute > .ar-semantic-data-field-picker-attribute-title {
  display: block;
  position: relative;
  white-space: nowrap;
  min-width: 100%;
  height: 30px;
  line-height: 30px;
  padding: 0 10px;
  max-width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  cursor: pointer;
  user-select: none;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-fields .ar-semantic-data-field-picker-attribute > .ar-semantic-data-field-picker-attribute-title > .datatype-label {
  position: absolute;
  top: 5px;
  left: 5px;
  display: block;
  width: 20px;
  height: 20px;
  text-align: center;
  background-color: var(--cui-accent);
  color: var(--cui-contrast-text);
  font-size: 10px;
  line-height: 20px;
  border-radius: 4px;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-fields .ar-semantic-data-field-picker-attribute > .ar-semantic-data-field-picker-attribute-title > .datatype-label > i {
  padding: 2px;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-fields .ar-semantic-data-field-picker-attribute > .ar-semantic-data-field-picker-attribute-title > .aggregate-label {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  width: 30px;
  height: 30px;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-fields .ar-semantic-data-field-picker-attribute > .ar-semantic-data-field-picker-attribute-title > .aggregate-label > span {
  display: block;
  width: 5px;
  height: 30px;
  font-size: 12px;
  line-height: 30px;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-fields .ar-semantic-data-field-picker-attribute > .ar-semantic-data-field-picker-attribute-title > .aggregate-label > span:first-child {
  float: left;
  text-align: left;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-fields .ar-semantic-data-field-picker-attribute > .ar-semantic-data-field-picker-attribute-title > .aggregate-label > span:last-child {
  float: right;
  text-align: right;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-fields .ar-semantic-data-field-picker-attribute > .ar-semantic-data-field-picker-attribute-title .ar-semantic-data-field-picker-attribute-title-text.with-label {
  margin-left: 25px;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-fields .ar-semantic-data-field-picker-attribute > .ar-semantic-data-field-picker-attribute-title > .ar-semantic-data-field-picker-attribute-variations-toggle {
  position: absolute;
  pointer-events: all;
  cursor: pointer;
  top: 0;
  right: 0;
  display: block;
  width: 30px;
  height: 30px;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-fields .ar-semantic-data-field-picker-attribute > .ar-semantic-data-field-picker-attribute-title > .ar-semantic-data-field-picker-attribute-variations-toggle > .gc-icon {
  display: flex;
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-fields .ar-semantic-data-field-picker-attribute > .ar-semantic-data-field-picker-attribute-title > .ar-semantic-data-field-picker-attribute-variations-toggle > .gc-icon > svg {
  width: 16px;
  height: 16px;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-fields .ar-semantic-data-field-picker-attribute > .ar-semantic-data-field-picker-attribute-title.ar-semantic-data-field-picker-attribute-with-variation {
  padding-right: 30px;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-fields .ar-semantic-data-field-picker-attribute > .ar-semantic-data-field-picker-attribute-title.ar-semantic-data-field-picker-attribute-disabled {
  cursor: auto;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-fields .ar-semantic-data-field-picker-attribute > .ar-semantic-data-field-picker-attribute-title.ar-semantic-data-field-picker-attribute-disabled .ar-semantic-data-field-picker-attribute-title-text {
  opacity: 0.5;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-fields .ar-semantic-data-field-picker-attribute > .ar-semantic-data-field-picker-attribute-title.ar-semantic-data-field-picker-attribute-selected {
  background-color: var(--cui-accent-semi-40);
  color: var(--cui-contrast-text);
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-fields .ar-semantic-data-field-picker-attribute > .ar-semantic-data-field-picker-attribute-title.ar-semantic-data-field-picker-attribute-selected > .datatype-label {
  background-color: var(--cui-accent-semi-60);
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-fields .ar-semantic-data-field-picker-attribute > .ar-semantic-data-field-picker-attribute-title.ar-semantic-data-field-picker-attribute-selected > .ar-semantic-data-field-picker-attribute-variations-toggle {
  color: var(--cui-contrast-text);
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-fields .ar-semantic-data-field-picker-attribute > .ar-semantic-data-field-picker-attribute-title.ar-semantic-data-field-picker-attribute-selected.ar-semantic-data-field-picker-attribute-disabled {
  cursor: no-drop;
  background-color: #d87979;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-fields .ar-semantic-data-field-picker-attribute > .ar-semantic-data-field-picker-attribute-title.ar-semantic-data-field-picker-attribute-selected.ar-semantic-data-field-picker-attribute-disabled .ar-semantic-data-field-picker-attribute-title-text {
  color: var(--cui-accent-error);
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-fields .ar-semantic-data-field-picker-attribute > .ar-semantic-data-field-picker-attribute-title:hover:not(.ar-semantic-data-field-picker-attribute-disabled) {
  background-color: var(--cui-accent-hover);
  color: var(--cui-contrast-text);
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-fields .ar-semantic-data-field-picker-attribute > .ar-semantic-data-field-picker-attribute-title:hover:not(.ar-semantic-data-field-picker-attribute-disabled) > .datatype-label {
  background-color: var(--cui-accent-semi-60);
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-fields .ar-semantic-data-field-picker-attribute > .ar-semantic-data-field-picker-attribute-title:hover:not(.ar-semantic-data-field-picker-attribute-disabled) > .ar-semantic-data-field-picker-attribute-variations-toggle {
  color: var(--cui-contrast-text);
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-fields .ar-semantic-data-field-picker-attribute > .ar-semantic-data-field-picker-attribute-variations {
  display: block;
  position: relative;
  min-width: 100%;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity > .ar-semantic-data-field-picker-entity-fields .ar-semantic-data-field-picker-attribute.variations-expanded {
  background-color: #f7f7f7;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity.ar-semantic-data-field-picker-entity-expanded > .ar-semantic-data-field-picker-entity-title, .ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity.ar-semantic-data-field-picker-entity-base > .ar-semantic-data-field-picker-entity-title {
  padding: 0 10px;
  background-color: var(--cui-accent);
  color: var(--cui-contrast-text);
  border-bottom: 1px solid var(--cui-bg-panels-border);
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity.ar-semantic-data-field-picker-entity-expanded > .ar-semantic-data-field-picker-entity-title:hover, .ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity.ar-semantic-data-field-picker-entity-base > .ar-semantic-data-field-picker-entity-title:hover {
  background-color: var(--cui-accent-hover);
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity.ar-semantic-data-field-picker-entity-expanded > .ar-semantic-data-field-picker-entity-title > .ar-semantic-data-field-picker-attribute-title-arrow {
  position: absolute;
  bottom: -3.5px;
  left: 11.5px;
  width: 7px;
  height: 7px;
  transform: rotate(45deg);
  background-color: var(--cui-accent);
  z-index: 999;
  border-bottom: 1px solid var(--cui-bg-panels-border);
  border-right: 1px solid var(--cui-bg-panels-border);
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity.ar-semantic-data-field-picker-entity-expanded > .ar-semantic-data-field-picker-entity-title:hover > .ar-semantic-data-field-picker-attribute-title-arrow {
  background-color: var(--cui-accent-hover);
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity.ar-semantic-data-field-picker-entity-tree-view.ar-semantic-data-field-picker-entity {
  display: inline-block;
  min-width: 100%;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity.ar-semantic-data-field-picker-entity-tree-view > .ar-semantic-data-field-picker-entity-title {
  background-color: transparent;
  color: var(--cui-text);
  padding: 0 30px;
  border: none;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity.ar-semantic-data-field-picker-entity-tree-view > .ar-semantic-data-field-picker-entity-title > span {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  width: 30px;
  height: 30px;
  text-align: center;
  font-size: 8px;
  line-height: 31px;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity.ar-semantic-data-field-picker-entity-tree-view > .ar-semantic-data-field-picker-entity-title > span.entity-expanded {
  transform: rotate(180deg);
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity.ar-semantic-data-field-picker-entity-tree-view > .ar-semantic-data-field-picker-entity-title:hover {
  background-color: var(--cui-accent-hover);
  color: var(--cui-contrast-text);
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity.ar-semantic-data-field-picker-entity-tree-view > .ar-semantic-data-field-picker-entity-fields {
  padding-left: 13px;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity.ar-semantic-data-field-picker-entity-tree-view > .ar-semantic-data-field-picker-entity-fields .ar-semantic-data-field-picker-attribute {
  overflow: auto;
  text-overflow: initial;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity.ar-semantic-data-field-picker-entity-tree-view > .ar-semantic-data-field-picker-entity-fields .ar-semantic-data-field-picker-attribute.variations-expanded {
  background-color: transparent;
}
.ar-semantic-data-field-picker-dropdown-content .ar-semantic-data-field-picker-entity.ar-semantic-data-field-picker-entity-tree-view > .ar-semantic-data-field-picker-entity-fields .ar-semantic-data-field-picker-attribute > .ar-semantic-data-field-picker-attribute-title:not(.ar-semantic-data-field-picker-attribute-disabled):hover {
  background-color: var(--cui-accent-hover);
  color: var(--cui-contrast-text);
}
.ar-semantic-data-field-picker-dropdown-content .ar-data-field-picker-dropdown-no-fields-message {
  width: 100%;
  text-align: center;
  color: #757575;
  font-size: 12px;
  line-height: 30px;
}
.ar-semantic-data-field-picker-dropdown-content .ar-data-field-picker-dropdown-search {
  position: relative;
  height: 30px;
}
.ar-semantic-data-field-picker-dropdown-content .ar-data-field-picker-dropdown-search > input {
  display: block;
  width: 100%;
  height: 100%;
  padding: 5px 30px 5px 30px;
  border: 1px solid transparent;
  background-color: rgba(0, 0, 0, 0.02);
  font-size: 12px;
}
.ar-semantic-data-field-picker-dropdown-content .ar-data-field-picker-dropdown-search > input::-ms-clear {
  display: none;
}
.ar-semantic-data-field-picker-dropdown-content .ar-data-field-picker-dropdown-search > input:focus, .ar-semantic-data-field-picker-dropdown-content .ar-data-field-picker-dropdown-search > input:hover {
  background-color: rgba(0, 0, 0, 0.07);
}
.ar-semantic-data-field-picker-dropdown-content .ar-data-field-picker-dropdown-search > i {
  position: absolute;
  top: 0;
  left: 0;
  width: 30px;
  height: 30px;
  text-align: center;
  pointer-events: none;
  opacity: 0.5;
  font-size: 14px;
  line-height: 30px;
}
.ar-semantic-data-field-picker-dropdown-content .ar-data-field-picker-dropdown-search > button {
  position: absolute;
  top: 0;
  right: 0;
  width: 30px;
  height: 30px;
  padding-left: 0;
  cursor: pointer;
  text-align: center;
  opacity: 0.5;
  color: #a5a5a5;
  border: none;
  background: transparent;
}
.ar-semantic-data-field-picker-dropdown-content .ar-data-field-picker-dropdown-search > button:hover {
  color: #212121;
}
.ar-semantic-data-field-picker-dropdown-content .ar-data-field-picker-dropdown-search > button > i {
  font-size: 16px;
  line-height: 30px;
}

.wd-add-rows-columns-dialog__editors {
  width: 75%;
  margin: auto;
}
.wd-add-rows-columns-dialog__editors > * {
  margin-bottom: 5px;
}

.wd-data-attribute__variations-toggle {
  position: absolute;
  top: 0;
  right: 0;
}

.wd-data-item-container--attribute {
  position: relative;
  padding-left: 40px;
}
.wd-data-item-container--attribute .wd-data-item__icon-drag {
  flex: 0 0 30px;
  width: 30px;
  height: 30px;
}
.wd-data-item-container--attribute .wd-data-item__icon-drag > .gc-icon {
  flex: 0 0 30px;
  width: 30px;
  height: 30px;
}
.wd-data-item-container--attribute .wd-data-item > .gc-icon {
  flex: 0 0 30px;
  width: 30px;
  height: 30px;
}
.wd-data-item-container--attribute .wd-data-item > span, .wd-data-item-container--attribute .wd-data-item__title, .wd-data-item-container--attribute .wd-data-item__subtitle {
  line-height: 30px;
}
.wd-data-item-container--attribute .wd-data-item--expanded {
  margin: 0;
  background-color: var(--cui-btn-bg);
  box-shadow: inset 1px 0 0 0 var(--cui-bg-panels-border);
}
.wd-data-item-container--attribute > .branch {
  position: absolute;
  top: 0;
  left: 20px;
  display: block;
  width: 20px;
  height: 100%;
  flex-grow: 0;
  flex-shrink: 0;
}
.wd-data-item-container--attribute > .branch.offset + .branch {
  left: 55px;
  width: 15px;
}
.wd-data-item-container--attribute > .branch .corner {
  display: block;
  width: 100%;
  height: 50%;
  border-bottom: 1px dotted var(--cui-bg-panels-border);
  border-left: 1px dotted var(--cui-bg-panels-border);
}
.wd-data-item-container--attribute > .branch .line {
  display: block;
  width: 100%;
  height: 50%;
  border-left: 1px dotted var(--cui-bg-panels-border);
}
.wd-data-item-container--attribute:last-child .branch .line {
  display: none;
}
.wd-data-item-container--attribute:last-child .branch.offset .line {
  display: block;
}
.wd-data-item-container--attribute:last-child .wd-data-attribute__variations .branch.offset + .branch .line:last-child {
  display: none;
}

.wd-data-item-container--inner-attribute {
  margin-left: 40px;
  padding-left: 30px;
}
.wd-data-item-container--inner-attribute > .branch {
  left: 15px;
  width: 15px;
}

.wd-data-item--disabled-attribute {
  pointer-events: none;
  opacity: 0.62;
}

.wd-data-item-data-set__search {
  display: flex;
  width: 100%;
  height: 40px;
  padding-left: 20px;
  align-items: center;
}
.wd-data-item-data-set__fields--empty {
  display: flex;
  width: 100%;
  height: 30px;
  padding-left: 20px;
}
.wd-data-item-data-set__children {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}
.wd-data-item-data-set__children > div {
  display: flex;
  width: 100%;
  padding-left: 20px;
}
.wd-data-item-data-set__children > div > .wd-data-item-data-set {
  min-width: 0;
  flex-grow: 10;
}
.wd-data-item-data-set__children > div:last-child > .wd-data-item-data-set__branch-corner {
  max-height: 20px;
}
.wd-data-item-data-set__message {
  width: 100%;
  text-align: center;
  color: var(--cui-text-semi-60);
  font-size: var(--cui-text-size);
  line-height: 30px;
}
.wd-data-item-data-set__branch-corner {
  width: 20px;
  height: 100%;
  border-left: 1px dotted var(--cui-bg-panels-border);
  flex-grow: 0;
  flex-shrink: 0;
}
.wd-data-item-data-set__branch-corner::after {
  content: "";
  display: block;
  margin-top: 20px;
  border-bottom: 1px dotted var(--cui-bg-panels-border);
}
.wd-data-item-data-set__corner {
  width: 20px;
  height: 50%;
  border-left: 1px dotted var(--cui-bg-panels-border);
  border-bottom: 1px dotted var(--cui-bg-panels-border);
  flex-grow: 0;
  flex-shrink: 0;
}
.wd-data-item-data-set__no-branch-line {
  width: 20px;
  height: 100%;
  border-left: 1px dotted var(--cui-bg-panels-border);
  flex-grow: 0;
  flex-shrink: 0;
}
.wd-data-item-data-set--with-children > .wd-data-item-data-set__fields .wd-data-item-container--field:last-child > .branch > .line {
  display: block;
}

.wd-data-field__variations-toggle {
  position: absolute;
  top: 0;
  right: 0;
}

.wd-data-item-container--field {
  position: relative;
  padding-left: 40px;
}
.wd-data-item-container--field .wd-data-item > .gc-icon {
  flex: 0 0 30px;
  width: 30px;
  height: 30px;
}
.wd-data-item-container--field .wd-data-item > span, .wd-data-item-container--field .wd-data-item__title, .wd-data-item-container--field .wd-data-item__subtitle {
  line-height: 30px;
}
.wd-data-item-container--field > .branch {
  position: absolute;
  top: 0;
  left: 20px;
  display: block;
  width: 20px;
  height: 100%;
  flex-grow: 0;
  flex-shrink: 0;
}
.wd-data-item-container--field > .branch.offset + .branch {
  left: 55px;
  width: 15px;
}
.wd-data-item-container--field > .branch .corner {
  display: block;
  width: 100%;
  height: 50%;
  border-bottom: 1px dotted var(--cui-bg-panels-border);
  border-left: 1px dotted var(--cui-bg-panels-border);
}
.wd-data-item-container--field > .branch .line {
  display: block;
  width: 100%;
  height: 50%;
  border-left: 1px dotted var(--cui-bg-panels-border);
}
.wd-data-item-container--field:last-child .branch .line {
  display: none;
}
.wd-data-item-container--field:last-child .branch.offset .line {
  display: block;
}
.wd-data-item-container--field:last-child .wd-data-field__variations .branch.offset + .branch .line:last-child {
  display: none;
}

.wd-data-field-custom-action__container {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  flex-direction: row-reverse;
  height: 30px;
  justify-content: flex-start;
  align-items: center;
}
.wd-data-field-custom-action__container--with-variations {
  right: 30px;
}

.wd-data-item {
  display: flex;
  overflow: hidden;
  min-height: 30px;
  padding-right: 5px;
  border-radius: 4px;
  align-items: center;
  justify-content: stretch;
}
.wd-data-item > .gc-icon {
  display: flex;
  flex: 0 0 40px;
  width: 40px;
  height: 40px;
  align-items: center;
  justify-content: center;
}
.wd-data-item__icon-drag {
  display: block;
  flex: 0 0 40px;
  width: 40px;
  height: 40px;
}
.wd-data-item__icon-drag > .gc-icon {
  display: flex;
  width: 40px;
  height: 40px;
  color: var(--cui-accent-text);
  align-items: center;
  justify-content: center;
}
.wd-data-item > span, .wd-data-item__title, .wd-data-item__subtitle {
  display: block;
  overflow: hidden;
  user-select: none;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: var(--cui-text);
  font-size: var(--cui-text-size);
  line-height: 40px;
}
.wd-data-item__title {
  padding-top: 5px;
  line-height: 15px;
}
.wd-data-item__subtitle {
  color: var(--cui-text-semi-60);
  line-height: 15px;
}
.wd-data-item__text {
  overflow: hidden;
  flex: 1 1 100%;
  padding-bottom: 5px;
}
.wd-data-item__controls {
  display: flex;
  flex-direction: row-reverse;
  height: 40px;
  align-items: center;
  justify-content: flex-end;
  flex-grow: 0;
  flex-shrink: 0;
}
.wd-data-item--expanded {
  margin-left: 30px;
}
.wd-data-item:hover {
  background-color: var(--cui-btn-bg);
}

.wd-data-item-relation__fields--empty {
  display: flex;
  width: 100%;
  height: 30px;
  padding-left: 20px;
}
.wd-data-item-relation__message {
  width: 100%;
  text-align: center;
  color: var(--cui-text-semi-60);
  font-size: var(--cui-text-size);
  line-height: 30px;
}
.wd-data-item-relation__branch-line {
  width: 20px;
  height: 100%;
  border-left: 1px dotted var(--cui-bg-panels-border);
  flex-grow: 0;
  flex-shrink: 0;
}
.wd-data-item-relation__branch-corner {
  width: 20px;
  height: 50%;
  border-bottom: 1px dotted var(--cui-bg-panels-border);
  border-left: 1px dotted var(--cui-bg-panels-border);
  flex-grow: 0;
  flex-shrink: 0;
}

.wd-data-item--expanded-relation {
  background-color: var(--cui-btn-bg);
  box-shadow: inset 0 -1px 0 0 var(--cui-bg-panels-border);
}
.wd-data-item--expanded-relation:hover {
  background-color: var(--cui-btn-bg-hover);
}

.wd-data-item-container--relation {
  position: relative;
}
.wd-data-item-container--relation .wd-data-item--relation {
  margin-left: 40px;
}
.wd-data-item-container--relation .wd-data-item--relation .wd-data-item__icon-drag {
  flex: 0 0 30px;
  width: 30px;
}
.wd-data-item-container--relation .wd-data-item--relation .wd-data-item__icon-drag > .gc-icon {
  flex: 0 0 30px;
  width: 30px;
}
.wd-data-item-container--relation > .branch {
  position: absolute;
  top: 0;
  left: 20px;
  display: block;
  width: 20px;
  height: 100%;
  flex-grow: 0;
  flex-shrink: 0;
}
.wd-data-item-container--relation > .branch.offset + .branch {
  left: 55px;
  width: 15px;
}
.wd-data-item-container--relation > .branch .corner {
  display: block;
  width: 100%;
  height: 20px;
  border-bottom: 1px dotted var(--cui-bg-panels-border);
  border-left: 1px dotted var(--cui-bg-panels-border);
}
.wd-data-item-container--relation > .branch .line {
  display: block;
  width: 100%;
  height: 50%;
  border-left: 1px dotted var(--cui-bg-panels-border);
}
.wd-data-item-container--relation:last-child > .branch .line {
  display: none;
}
.wd-data-item-container--relation:last-child > .branch.offset .line {
  display: block;
}
.wd-data-item-container--relation:last-child .wd-data-field__variations .branch.offset + .branch .line:last-child {
  display: none;
}

.wd-data-item-container--inner-relation {
  margin-left: 40px;
}
.wd-data-item-container--inner-relation .wd-data-item--relation {
  margin-left: 30px;
}
.wd-data-item-container--inner-relation > .branch {
  left: 15px;
  width: 15px;
}

.wd-data-search {
  position: relative;
  display: block;
  width: 100%;
}
.wd-data-search > .gc-icon {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  width: 30px;
  height: 30px;
  align-items: center;
  justify-content: center;
}
.wd-data-search > input[type=text].gc-input {
  width: 100%;
  padding-left: 30px;
}
.wd-data-search > .gc-btn {
  position: absolute;
  top: 0;
  right: 0;
}
.wd-data-search--has-value > input[type=text].gc-input {
  padding-right: 30px;
}

.wd-text-editor-dialog {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: min-content minmax(160px, 30%) 1fr;
  height: 100%;
  grid-template-areas: "tree header header" "tree textarea textarea" "tree function-list info";
  padding: 7.5px 15px;
}
.wd-text-editor-dialog__tree-container {
  box-shadow: -1px 0 0 0 var(--cui-bg-panels-border) inset;
  grid-area: tree;
  display: grid;
  grid-template-rows: min-content 1fr;
  margin-right: 15px;
}
.wd-text-editor-dialog__heading-container {
  grid-area: header;
  display: grid;
  grid-auto-flow: column;
  grid-template-columns: auto min-content;
  margin-bottom: 10px;
}
.wd-text-editor-dialog__searchItem {
  display: flex;
  flex-direction: column;
  width: 100%;
  align-items: left;
  justify-content: center;
}
.wd-text-editor-dialog__searchItem > span {
  display: block;
  overflow: hidden;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  line-height: 16px;
}
.wd-text-editor-dialog__searchItem > span > b {
  color: var(--cui-accent-text);
}
.wd-text-editor-dialog__searchItem > span:last-of-type {
  opacity: 0.62;
}
.wd-text-editor-dialog__searchItem--disabled {
  text-align: center;
}
.wd-text-editor-dialog__textarea {
  grid-area: textarea;
  padding: 7.5px 7.5px 7.5px 7.5px;
  margin-bottom: 7.5px;
  resize: none;
  color: var(--cui-text);
  border-style: none;
  background-color: rgba(0, 0, 0, 0.1);
  font-size: var(--cui-text-size);
}
.wd-text-editor-dialog__textarea:focus {
  color: var(--cui-accent);
}
.wd-text-editor-dialog__textarea--invalid {
  box-shadow: 0 0 0 1px var(--cui-accent-error) inset;
}
.wd-text-editor-dialog__function-list {
  grid-area: function-list;
}
.wd-text-editor-dialog__function-list .expression-functions {
  display: grid;
  grid-template-rows: min-content 1fr;
  height: 100%;
}
.wd-text-editor-dialog__function-list .expression-functions .gc-treeview {
  padding-right: 15px;
}
.wd-text-editor-dialog__info {
  grid-area: info;
  padding-left: 15px;
  box-shadow: 1px 0 0 0 var(--cui-bg-panels-border) inset;
}
.wd-text-editor-dialog__info .expression-info {
  display: grid;
  grid-template-rows: min-content 1fr;
  height: 100%;
  font: var(--cui-text-size) var(--cui-text-family);
}
.wd-text-editor-dialog__error-container {
  padding: 15px;
  padding-bottom: 0;
}
.wd-text-editor-dialog__error-container > .gc-alert {
  margin-bottom: 10px;
}
.wd-text-editor-dialog__error-container > .gc-alert:last-of-type {
  margin-bottom: 0;
}
.wd-text-editor-dialog__error-container > .gc-alert .gc-alert__title {
  overflow: visible;
  padding: 5px 0;
  white-space: normal;
  line-height: 1.5em;
}

.preview-view-container .pallete-dd {
  display: flex;
  align-items: center;
  justify-content: center;
}
.preview-view-container .chart-container {
  margin-top: 10px;
  height: 380px;
  background-color: white;
  border-radius: 2px;
}
.preview-view-container .bt-group {
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 10px;
}
.preview-view-container .bt-group .gc-btn {
  margin: 2px;
}

.select-type-view-container .charts-container {
  margin: 10px 0;
  height: 420px;
}
.select-type-view-container .charts-container .wd-chart-editor-tile {
  width: 25%;
}

.chart-wizard-container {
  padding: 15px;
}
.chart-wizard-container .gc-wizard .gc-wizard__step:first-child .gc-btn {
  left: 20%;
}
.chart-wizard-container .gc-wizard .gc-wizard__step:first-child .gc-wizard__step-text {
  text-align: left;
}
.chart-wizard-container .gc-wizard .gc-wizard__step:first-child:after {
  left: calc(20% + 15px + 1px);
  width: calc(80% - 15px - 1px);
}
.chart-wizard-container .gc-wizard .gc-wizard__step:last-child .gc-btn {
  width: 30px;
  left: 90%;
}
.chart-wizard-container .gc-wizard .gc-wizard__step:last-child .gc-wizard__step-text {
  text-align: right;
}
.chart-wizard-container .gc-wizard .gc-wizard__step:last-child:before {
  width: calc(90% - 15px - 1px);
}
.chart-wizard-container .break-down-method-dropdown .gc-btn__text {
  padding: 0 10px;
}

.wd-tablix-wizard-filters-panel {
  position: relative;
  width: 100%;
  height: calc(100% - 40px);
}
.wd-tablix-wizard-filters-panel__back-button {
  width: calc(100% - 20px);
  margin: 0 10px;
}
.wd-tablix-wizard-filters-panel__content {
  position: relative;
  width: calc(100% - 20px);
  height: calc(100% - 50px);
  margin: 10px;
  border-radius: 4px;
  background-color: var(--cui-bg-panels);
}
.wd-tablix-wizard-filters-panel .wd-filters {
  margin: 10px;
}

.wd-tablix-wizard-layout {
  display: flex;
  flex-wrap: wrap;
}
.wd-tablix-wizard-layout__column {
  position: relative;
  z-index: 0;
  display: flex;
  flex-direction: column;
  flex: 1 1 50%;
  width: 50%;
  height: 100%;
  flex-wrap: nowrap;
}
.wd-tablix-wizard-layout__as-rows-toggle {
  position: absolute;
  top: 50%;
  right: 15px;
  cursor: pointer;
  font-size: 13px;
}

.wd-tablix-wizard-tablix-filters, .wd-tablix-wizard-row-column-swap {
  position: absolute;
  top: 0;
  right: 0;
  display: inline-flex;
  padding: 0 5px 6px;
  cursor: pointer;
  align-items: center;
}
.wd-tablix-wizard-tablix-filters--disabled, .wd-tablix-wizard-row-column-swap--disabled {
  cursor: not-allowed;
  opacity: 0.5;
}
.wd-tablix-wizard-tablix-filters__icon, .wd-tablix-wizard-row-column-swap__icon {
  display: flex;
  width: 30px;
  height: 30px;
  background-position: 3px 3px;
  font-size: 16px;
  line-height: 31px;
  align-items: center;
  justify-content: center;
}
.wd-tablix-wizard-tablix-filters__label, .wd-tablix-wizard-row-column-swap__label {
  height: 20px;
  font-size: 13px;
  line-height: 20px;
}

.wd-tablix-wizard-row-column-swap {
  top: 50%;
}
.wd-tablix-wizard-row-column-swap__icon {
  transform: rotate(-45deg);
}

.wd-tablix-wizard-layout-cell {
  overflow: hidden;
  flex: 1 1 50%;
  height: 50%;
}
.wd-tablix-wizard-layout-cell--left {
  padding: 0 5px 10px 10px;
}
.wd-tablix-wizard-layout-cell--right {
  padding: 0 10px 10px 5px;
}
.wd-tablix-wizard-layout-cell__header {
  display: inline-flex;
  padding: 0 0 6px;
  cursor: default;
  align-items: center;
}
.wd-tablix-wizard-layout-cell__header-icon {
  display: flex;
  width: 30px;
  height: 30px;
  background-position: 3px 3px;
  font-size: 16px;
  line-height: 31px;
  align-items: center;
  justify-content: center;
}
.wd-tablix-wizard-layout-cell__header-label {
  height: 20px;
  font-size: 13px;
  line-height: 20px;
}

.wd-tablix-wizard-layout-options {
  width: 100%;
  height: calc(100% - 36px);
  margin: 0;
  border-radius: 2px;
  background-color: var(--cui-bg-panels);
}
.wd-tablix-wizard-layout-options__tabs {
  position: relative;
  width: calc(100% - 10px);
  height: 30px;
  margin: 0 5px 0 5px;
}
.wd-tablix-wizard-layout-options__tabs::after {
  position: relative;
  display: block;
  width: calc(100% - 14px);
  height: 1px;
  margin: -1px 7px 0 7px;
  content: "";
  border-top: 1px solid var(--cui-bg-panels-border);
}
.wd-tablix-wizard-layout-options__content {
  height: calc(100% - 30px - 5px);
  margin-top: 5px;
  padding-left: 5px;
}

.wd-tablix-wizard-integer-editor {
  width: calc(100% - 10px);
  height: 30px;
  padding-left: 5px;
  font-size: 12px;
}
.wd-tablix-wizard-integer-editor__title-container {
  display: flex;
  float: left;
  flex-direction: column;
  width: 60%;
  height: 30px;
  justify-content: center;
}
.wd-tablix-wizard-integer-editor__input-container {
  position: relative;
  display: flex;
  float: right;
  width: 35%;
  height: 20px;
  margin-top: 5px;
}
.wd-tablix-wizard-integer-editor__input {
  width: 100%;
  padding-right: 25px;
  padding-left: 25px;
  text-align: center;
  border: none;
  outline: none;
  background-color: var(--cui-input-bg);
}
.wd-tablix-wizard-integer-editor__command-button {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  width: 25px;
  height: 20px;
  cursor: pointer;
  pointer-events: all;
  background-color: var(--cui-input-bg) !important;
}
.wd-tablix-wizard-integer-editor__command-button > i {
  width: 100% !important;
  height: 100% !important;
  color: var(--cui-text);
}
.wd-tablix-wizard-integer-editor__command-button--right {
  left: calc(100% - 25px);
}
.wd-tablix-wizard-integer-editor__command-button:disabled {
  cursor: not-allowed;
}

.wd-tablix-wizard-tablix-area {
  position: relative;
  width: 100%;
  height: calc(100% - 36px);
  border-radius: 2px;
  background-color: var(--cui-bg-panels);
}

.wd-tablix-wizard-tablix-area-item {
  position: relative;
  display: flex;
  width: 100%;
  height: 40px;
}
.wd-tablix-wizard-tablix-area-item:active .wd-tablix-wizard-tablix-area-item-title {
  background-color: var(--cui-item-bg-hover);
}

.wd-tablix-wizard-drop-area {
  position: absolute;
  z-index: 9000;
  left: 0;
  display: none;
  width: 100%;
  min-height: 10px;
  pointer-events: all;
}
.wd-tablix-wizard-drop-area__color {
  position: absolute;
  left: 0;
  display: none;
  width: 100%;
  min-height: 2px;
  opacity: 0.7;
  background-color: var(--cui-accent);
}
.wd-tablix-wizard-drop-area__color--line {
  width: calc(100% - 20px);
  height: 2px;
  margin: 4px 10px 4px 10px;
  opacity: 1;
}
.wd-tablix-wizard-drop-area--active {
  display: block;
}
.wd-tablix-wizard-drop-area--hover .wd-tablix-wizard-drop-area__color {
  display: block;
}

.wd-tablix-wizard-tablix-area-item-title {
  position: relative;
  width: calc(100% - 20px);
  height: 30px;
  margin: 10px 10px 0 10px;
  border-radius: 2px;
  background-color: var(--cui-item-bg);
  line-height: 30px;
}
.wd-tablix-wizard-tablix-area-item-title__label {
  display: block;
  overflow: hidden;
  width: calc(100% - 120px);
  padding-left: 10px;
  cursor: default;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 12px;
}
.wd-tablix-wizard-tablix-area-item-title__overlay {
  position: absolute;
  top: 0;
  right: 0;
}
.wd-tablix-wizard-tablix-area-item-title__overlay-button {
  float: left;
}
.wd-tablix-wizard-tablix-area-item-title .gc-btn .gc-icon > svg > path {
  fill: var(--cui-text);
}
.wd-tablix-wizard-tablix-area-item-title .gc-btn:hover {
  background-color: transparent !important;
}
.wd-tablix-wizard-tablix-area-item-title .gc-btn:hover .gc-icon > svg > path {
  fill: var(--cui-accent);
}
.wd-tablix-wizard-tablix-area-item-title:hover {
  background-color: var(--cui-item-bg-hover);
}

.wd-tablix-wizard-tablix-value__accented {
  color: var(--cui-accent);
  font-weight: bold;
}
.wd-tablix-wizard-tablix-value__strong {
  font-weight: bold;
}
.wd-tablix-wizard-tablix-value .wd-tablix-wizard-tablix-area-item-title__label {
  width: calc(100% - 120px);
}
.wd-tablix-wizard-tablix-value--wide .wd-tablix-wizard-tablix-area-item-title__label {
  width: calc(100% - 60px);
}

.wd-tablix-wizard-warning .gc-modal__title {
  overflow: hidden;
  width: 500px;
  cursor: default;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.wd-tablix-wizard-warning .gc-modal__subtitle {
  cursor: default;
}
.wd-tablix-wizard-warning__body {
  position: relative;
  display: flex;
  width: calc(100% - 20px);
  height: auto;
  margin: 10px;
  padding: 8px 12px;
  text-align: left;
  align-items: center;
}
.wd-tablix-wizard-warning__icon {
  display: block;
  width: 30px;
  height: 30px;
  background-position: 1px 2px;
}
.wd-tablix-wizard-warning__msg {
  margin-left: 10px;
  padding-left: 10px;
  cursor: default;
  font-size: 12px;
}

@media screen and (max-height: 780px) {
  .wd-tablix-wizard {
    top: 0 !important;
    margin-top: 15px !important;
  }
}
.wd-tablix-wizard .gc-modal__title {
  overflow: hidden;
  width: 800px;
  cursor: default;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.wd-tablix-wizard .gc-modal__subtitle {
  cursor: default;
}
.wd-tablix-wizard .gc-scrollbars {
  max-height: 570px !important;
}
.wd-tablix-wizard .gc-scrollbars__view.gc-scrollbars__view--vertical {
  max-height: 317px !important;
}
@media screen and (min-height: 600px) {
  .wd-tablix-wizard .gc-scrollbars__view.gc-scrollbars__view--vertical {
    max-height: calc(570px - 120px + 17px) !important;
  }
}
@media screen and (min-height: 650px) {
  .wd-tablix-wizard .gc-scrollbars__view.gc-scrollbars__view--vertical {
    max-height: calc(570px - 70px + 17px) !important;
  }
}
@media screen and (min-height: 700px) {
  .wd-tablix-wizard .gc-scrollbars__view.gc-scrollbars__view--vertical {
    max-height: calc(570px + 17px) !important;
  }
}

.wd-tablix-wizard__body {
  height: 570px;
}
.wd-tablix-wizard__body h2 {
  display: block;
  height: 40px;
  margin: 0;
  padding: 0 15px;
  cursor: default;
  text-transform: uppercase;
  color: var(--cui-accent-text);
  font-size: var(--cui-text-size);
  font-weight: bold;
  line-height: 40px;
}

.wd-tablix-wizard__layout-container {
  float: right;
  width: calc(100% * 0.7);
  height: 100%;
  background-color: var(--wd-bg-panels-light);
}
.wd-tablix-wizard__layout-container .wd-tablix-wizard-layout {
  width: 100%;
  height: calc(100% - 40px);
}

.wd-tablix-wizard__layout-overlay {
  position: absolute;
  z-index: 1;
  width: inherit;
  height: 570px;
  cursor: not-allowed;
  opacity: 0.1;
  background-color: #b0b0b0;
}

.wd-tablix-wizard__data-set-panel-container {
  float: left;
  width: calc(100% * (1 - 0.7));
  height: 100%;
  background-color: var(--cui-bg-panels);
}
.wd-tablix-wizard__data-set-panel-container .wd-tablix-wizard-data-set-panel {
  width: 100%;
  height: calc(100% - 40px);
}

.wd-tablix-wizard-info {
  position: relative;
  display: flex;
  width: calc(100% - 30px);
  height: auto;
  margin: 15% 15px 0 15px;
  padding: 8px 12px;
  text-align: left;
  border: 1px solid var(--cui-bg-panels-border);
  border-radius: 2px;
  background: var(--cui-bg-panels-section);
  align-items: center;
}
.wd-tablix-wizard-info__icon {
  display: block;
  width: 30px;
  height: 30px;
  background-position: 1px 2px;
}
.wd-tablix-wizard-info__msg {
  margin-left: 10px;
  padding-left: 10px;
  cursor: default;
  border-left: 1px solid var(--cui-bg-panels-border);
  font-size: 12px;
}

.wd-tablix-wizard__body .wd-tablix-wizard__layout-container .wd-tablix-wizard-layout .wd-tablix-wizard-layout-cell .ar-member .delete .wd-svg {
  display: flex;
}
.wd-tablix-wizard__body .wd-tablix-wizard__layout-container .wd-tablix-wizard-layout .wd-tablix-wizard-layout-cell .ar-member .delete .wd-svg > svg {
  width: 14px;
  height: 20px;
}
.wd-tablix-wizard__body .wd-tablix-wizard__layout-container .wd-tablix-wizard-layout .wd-tablix-wizard-layout-cell .ar-member .action .dropdown-toggle.btn.btn-default .wd-svg {
  display: flex;
}
.wd-tablix-wizard__body .wd-tablix-wizard__layout-container .wd-tablix-wizard-layout .wd-tablix-wizard-layout-cell .ar-member .action .dropdown-toggle.btn.btn-default .wd-svg > svg {
  width: 16px;
  height: 20px;
}

.tw-icon-color-none {
  fill: none;
}

.tw-icon-color-blue {
  fill: #1c7cdc;
}

.tw-icon-color-light-blue {
  fill: #9cf;
}

.tw-icon-color-gray-blue {
  fill: #819cce;
}

.tw-icon-color-white {
  fill: white;
}

.tw-icon-color-yellow {
  fill: #c78306;
}

.wd-group-editor-member {
  position: relative;
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 30px;
  padding-left: 18px;
  user-select: none;
  transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out;
  color: var(--cui-text);
  border-radius: 4px;
  align-items: center;
}
.wd-group-editor-member .icon {
  flex: 0 0 auto;
  width: 18px;
  height: 20px;
  padding-top: 2px;
  padding-left: 8px;
}
.wd-group-editor-member .icon span {
  display: block;
  width: 6px;
  height: 16px;
  transition: border-color 0.2s ease-in-out;
  border-left: 2px solid var(--cui-text-semi-60);
}
.wd-group-editor-member .text {
  overflow: hidden;
  flex: 1 1 100%;
  text-overflow: ellipsis;
  font-size: 12px;
  line-height: 21px;
}
.wd-group-editor-member .action {
  transition: opacity 0.2s ease-in-out;
  opacity: 0;
}

.wd-group-editor-member__items {
  padding-left: 18px;
}
.wd-group-editor-member:hover {
  background-color: var(--cui-btn-bg);
}
.wd-group-editor-member:hover .action {
  opacity: 1;
}
.wd-group-editor-member--tree-selected {
  background-color: var(--cui-btn-bg);
}
.wd-group-editor-member--tree-selected .action {
  opacity: 1;
}
.wd-group-editor-member--tree-selected:hover {
  background-color: var(--cui-btn-bg-hover);
}
.wd-group-editor-member--selected {
  color: white;
  background-color: var(--cui-accent);
}
.wd-group-editor-member--selected .icon > span {
  border-left: 2px solid white;
}
.wd-group-editor-member--selected .action {
  opacity: 1;
}
.wd-group-editor-member--selected:hover {
  background-color: var(--cui-accent-hover);
}
.wd-group-editor-member--dynamic .icon {
  padding-left: 7px;
}
.wd-group-editor-member--dynamic .icon span {
  border-top: 2px solid var(--cui-accent);
  border-bottom: 2px solid var(--cui-accent);
  border-left: 2px solid var(--cui-accent);
}
.wd-group-editor-member--dynamic.wd-group-editor-member--selected {
  color: white;
  background-color: var(--cui-accent);
}
.wd-group-editor-member--dynamic.wd-group-editor-member--selected .icon > span {
  border-top: 2px solid white;
  border-bottom: 2px solid white;
  border-left: 2px solid white;
}

.wd-group-editor-member-container {
  position: relative;
  display: block;
  width: 100%;
}
.wd-group-editor-member-container .branch {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  width: 18px;
  height: 100%;
  padding-left: 9px;
}
.wd-group-editor-member-container .branch .corner {
  display: block;
  width: 9px;
  height: 15px;
  border-bottom: 1px dashed var(--cui-bg-panels-border);
  border-left: 1px dashed var(--cui-bg-panels-border);
}
.wd-group-editor-member-container .branch .line {
  display: block;
  width: 9px;
  height: calc(100% - 15px);
  border-left: 1px dashed var(--cui-bg-panels-border);
}
.wd-group-editor-member-container--no-branch > .branch {
  display: none !important;
}
.wd-group-editor-member-container--no-branch > .wd-group-editor-member {
  padding-left: 0;
}
.wd-group-editor-member-container--no-branch > .ar-member-items {
  padding-left: 0;
}

.wd-group-editor-hierarchy {
  padding: 0 15px;
}
.wd-group-editor-hierarchy > .gc-heading {
  padding: 10px 0;
}
.wd-group-editor-hierarchy > .gc-heading .gc-heading__text {
  font-weight: bold;
}
.wd-group-editor-hierarchy > .gc-heading .gc-heading__divider {
  opacity: 0.1;
}
.wd-group-editor-hierarchy .wd-group-editor-member-container:last-child > .branch .line {
  display: none;
}
.wd-group-editor-hierarchy--collapsed .wd-group-editor-hierarchy__content {
  display: none;
}

.wd-group-editor {
  display: flex;
  overflow: hidden;
  flex-direction: column;
  width: 100%;
  height: 100%;
  align-items: stretch;
  justify-content: stretch;
}
.wd-group-editor__content {
  display: block;
  flex: 1 1 100%;
  width: 100%;
  height: 100%;
}
.wd-group-editor > p {
  display: block;
  overflow: hidden;
  width: 100%;
  user-select: none;
  text-align: center;
  color: var(--cui-text-semi-60);
  font-size: var(--cui-text-size);
  line-height: 40px;
}
.wd-group-editor > .gc-toggle {
  flex: 0 0 30px;
  margin: 0 15px;
}

.wd-layer-list {
  padding: 15px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.wd-layer-list__content {
  margin-bottom: auto;
}

.wd-layer-item {
  transition: background-color 0.2s ease-in-out, opacity 0.2s ease-in-out;
  display: grid;
  grid-template-columns: 1fr auto;
  border-radius: 4px;
}
.wd-layer-item:hover {
  background-color: var(--cui-btn-bg-hover);
}

.wd-layer-item--selected {
  background-color: var(--cui-accent);
}
.wd-layer-item--selected:hover {
  background-color: var(--cui-accent);
}

.wd-layer-item__button {
  overflow: hidden;
  text-overflow: ellipsis;
  background-color: transparent !important;
}

.wd-panels-container {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background: var(--cui-bg-panels);
  box-shadow: 1px 0 0 0 var(--cui-bg-panels-border) inset;
  align-items: stretch;
  justify-content: stretch;
}

.wd-panels-content {
  position: relative;
  flex: 1 1 100%;
  margin-top: 15px;
}

.wd-panels-header {
  display: flex;
  flex: 0 0 auto;
  margin: 15px;
  margin-bottom: 0;
  border-radius: 4px;
  background-color: var(--cui-accent);
  justify-content: stretch;
}
.wd-panels-header__nav {
  display: flex;
  overflow: hidden;
  flex: 1 1 auto;
  color: var(--cui-contrast);
  justify-content: left;
  align-items: center;
}
.wd-panels-header__nav > .gc-icon {
  display: flex;
  width: var(--cui-text-size);
  height: var(--cui-text-size);
  opacity: 0.62;
  justify-content: center;
  align-items: center;
}
.wd-panels-header__nav > .gc-btn {
  opacity: 0.62;
}
.wd-panels-header__nav > .gc-btn > .gc-btn__text {
  padding: 0 5px;
  font-size: var(--cui-text-size-sm);
}
.wd-panels-header__nav > span {
  margin: 0 5px;
  font-size: var(--cui-text-size);
  line-height: 30px;
}

.pve-bound-item {
  position: relative;
  padding: 0 15px;
}
.pve-bound-item > p {
  display: block;
  height: 20px;
  margin: 0;
  padding: 0;
  font-size: var(--cui-text-size-sm);
  line-height: 25px;
}
.pve-bound-item > p > span {
  opacity: 0.62;
}
.pve-bound-item > p > b {
  margin-right: 5px;
  text-transform: uppercase;
}
.pve-bound-item > h4 {
  display: block;
  height: 20px;
  margin: 0;
  padding: 0;
  font-size: var(--cui-text-size);
  line-height: 15px;
}
.pve-bound-item > .gc-icon {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  width: 30px;
  height: 30px;
  align-items: center;
  justify-content: center;
}
.pve-bound-item > .gc-icon > svg {
  width: 16px;
  height: 16px;
}
.pve-bound-item--unset > h4 {
  opacity: 0.62;
  font-style: italic;
}

.pve-binding-menu {
  padding-bottom: 7.5px;
  border-radius: 4px;
}
.pve-binding-menu__details {
  padding: 7.5px 0 15px 0;
  color: var(--cui-contrast-text);
  border-radius: 4px 0 0 4px;
  background-color: var(--cui-accent);
}
.pve-binding-menu__details .gc-heading {
  margin: 0 15px;
  opacity: 0.62;
}
.pve-binding-menu__details .gc-heading .gc-heading__divider {
  opacity: 0.38;
}
.pve-binding-menu__params {
  height: 150px;
  padding-top: 7.5px;
}

.pve-binding {
  position: absolute;
  z-index: 1;
  right: 0;
  bottom: 0;
  display: block;
  width: 30px;
  height: 30px;
  transition: opacity 0.2s ease-in-out;
  opacity: 0;
}
.pve-binding > .gc-dd {
  width: 100%;
}
.pve-binding > .gc-dd > .gc-btn--transparent:not([disabled]):not(.disabled):hover {
  background: transparent;
}
.pve-binding > .gc-dd .gc-dd__toggle-content {
  overflow: visible;
}
.pve-binding__dot {
  position: relative;
  display: block;
  width: 14px;
  height: 14px;
  margin: 0 auto;
  transition: box-shadow 0.2s ease-in-out;
  border: 3px solid rgba(0, 0, 0, 0.25);
  border-radius: 4px;
  background-color: var(--cui-accent-error);
}
.pve-binding__dot:hover {
  box-shadow: 0 0 5px 0 var(--cui-accent-error);
}
.pve-binding__dot--partial {
  background-color: #999999;
}
.pve-binding__dot--full {
  border-color: rgba(0, 0, 0, 0.35);
  background-color: var(--cui-accent);
}
.pve-binding__dot--full:hover {
  box-shadow: 0 0 5px 0 var(--cui-accent);
}

.pve-tree {
  overflow: hidden;
  height: 100%;
  max-height: 100%;
}

.pve-textarea {
  overflow: hidden;
  height: 100%;
  max-height: 100%;
  pointer-events: none;
}

.pve-checklist > label {
  max-width: none;
}

.pve-slider-container {
  display: flex;
  height: 30px;
  align-items: center;
  justify-content: center;
}
.pve-slider-container--mode-X > .pve-slider {
  width: 100%;
  pointer-events: none;
}

.pve-number-editor {
  display: flex;
}
.pve-number-editor__button {
  flex: 0 0 auto;
  pointer-events: none;
}
.pve-number-editor__button:first-of-type {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.pve-number-editor__button:last-of-type {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.pve-number-editor__input.gc-input {
  flex: 1 1 auto;
  width: 100%;
  text-align: center;
  pointer-events: none;
  border-radius: 0;
}

.pve-float-editor {
  display: flex;
}
.pve-float-editor__button {
  flex: 0 0 auto;
}
.pve-float-editor__button:first-of-type {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.pve-float-editor__button:last-of-type {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.pve-float-editor__input.gc-input {
  flex: 1 1 auto;
  width: 100%;
  text-align: center;
  border-radius: 0;
}

.pve-default-editor {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
}
.pve-default-editor > .gc-btn {
  margin-top: 5px;
}

.pve-max-range-editor {
  display: flex;
}
.pve-max-range-editor--unset > .gc-number-editor .gc-input {
  transition: none;
  border-color: transparent;
}
.pve-max-range-editor > .gc-number-editor {
  flex: 1 1 auto;
  min-width: 100px;
  margin-right: 5px;
}
.pve-max-range-editor > .gc-dd {
  flex: 1 1 100px;
}

.pve-editor-ranges__item {
  display: block;
  overflow: hidden;
  height: 30px;
  padding: 0 10px;
  user-select: none;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: var(--cui-text);
  border-radius: 4px;
  background-color: var(--cui-input-bg);
  font-family: var(--cui-text-family);
  font-size: var(--cui-text-size);
  line-height: 30px;
}

.pve-editor-range-preview {
  display: flex;
  flex-direction: column;
  margin-bottom: 5px;
  padding: 0;
  padding: 15px;
  border-radius: var(--cui-border-radius);
  background-color: var(--cui-btn-bg);
  justify-content: center;
  align-items: center;
}
.pve-editor-range-preview__label, .pve-editor-range-preview__example {
  overflow: hidden;
  flex: 0 0 auto;
  height: 20px;
  margin: 0;
  padding: 0;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: var(--cui-text-size);
  line-height: 20px;
}
.pve-editor-range-preview__label {
  margin-bottom: 5px;
  color: var(--cui-accent-text);
  font-size: var(--cui-text-size-lg);
  font-weight: bold;
}

.pve-editor-range-preview__example span:last-of-type {
  margin-left: 5px;
  opacity: 0.62;
}
.pve-editor-range-preview__example--time span:first-of-type {
  opacity: 0.62;
}
.pve-editor-range-preview__example--time span:last-of-type {
  opacity: 1;
}

.pve-nullable {
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;
  align-items: flex-start;
  justify-content: flex-start;
}
.pve-nullable > .gc-check {
  display: flex;
  flex: 0 0 auto;
  width: 60px;
}
.pve-nullable__content {
  flex: 1 1 auto;
  height: 100%;
  margin-right: 15px;
}

.pve-range-edit-panel {
  padding: 0 15px;
}

.pve-parameters-panel {
  padding: 15px;
}
.pve-parameters-panel--with-headings {
  padding: 0 15px 15px 15px;
}
.pve-parameters-panel--with-headings .gc-heading {
  padding: 10px 0 5px 0;
}

.pve-parameters-panel__parameter {
  display: flex;
  overflow: hidden;
  border-radius: 4px;
  align-items: center;
}
.pve-parameters-panel__parameter:hover {
  background-color: var(--cui-btn-bg);
}
.pve-parameters-panel__parameter > .gc-icon {
  display: flex;
  flex: 0 0 40px;
  width: 40px;
  height: 40px;
  color: var(--cui-accent-text);
  justify-content: center;
  align-items: center;
}
.pve-parameters-panel__parameter__icon--level-warning.gc-icon {
  color: var(--cui-accent-warning);
}
.pve-parameters-panel__parameter__text {
  flex: 1 1 100%;
  overflow: hidden;
  padding-bottom: 5px;
}
.pve-parameters-panel__parameter__text__title, .pve-parameters-panel__parameter__text__subtitle {
  display: block;
  overflow: hidden;
  padding-right: 10px;
  user-select: none;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: var(--cui-text-size);
  line-height: 40px;
}
.pve-parameters-panel__parameter__text__title {
  padding-top: 5px;
  color: var(--cui-text);
  line-height: 15px;
}
.pve-parameters-panel__parameter__text__subtitle {
  color: var(--cui-text-semi-60);
  line-height: 15px;
}
.pve-parameters-panel__parameter__controls {
  display: flex;
  flex-direction: row-reverse;
  height: 40px;
  padding-right: 5px;
  align-items: center;
  justify-content: flex-end;
  flex-grow: 0;
  flex-shrink: 0;
}
.pve-parameters-panel__parameter--hidden-param > .gc-icon {
  opacity: 0.5;
  color: var(--cui-text);
}
.pve-parameters-panel__parameter--hidden-param .pve-parameters-panel__parameter__text {
  opacity: 0.5;
}
.pve-parameters-panel__parameter--hidden-param:hover {
  background-color: transparent;
}

.pve-plain-text {
  overflow: hidden;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  color: var(--cui-text);
  font-size: var(--cui-text-size);
  line-height: 20px;
}
.pve-plain-text--info {
  color: var(--cui-accent-text);
}
.pve-plain-text--warning {
  color: var(--cui-accent-warning);
}
.pve-plain-text--error {
  color: var(--cui-accent-error);
}

.pve-control-wrapper {
  position: absolute;
  border-radius: 4px;
  background-color: var(--cui-bg-panels);
}

.pve-control-simple-wrapper {
  position: relative;
  margin-bottom: 15px;
  border-radius: 4px;
}

.pve-control-wrapper:before, .pve-control-simple-wrapper:before {
  position: absolute;
  width: 5px;
  height: 5px;
  transform: translateY(-50%);
  border-radius: 50%;
  background-color: var(--cui-accent-error);
}
.pve-control-wrapper--required-top-left:before, .pve-control-simple-wrapper--required-top-left:before {
  top: 15px;
  left: -10px;
  content: "";
}
.pve-control-wrapper--required-top-right:before, .pve-control-simple-wrapper--required-top-right:before {
  top: 15px;
  right: -10px;
  content: "";
}
.pve-control-wrapper--required-bottom-left:before, .pve-control-simple-wrapper--required-bottom-left:before {
  bottom: 15px;
  left: -10px;
  content: "";
  transform: translateY(50%);
}
.pve-control-wrapper--selected::after, .pve-control-simple-wrapper--selected::after {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  content: "";
  border: 1px solid var(--cui-accent);
}
.pve-control-wrapper--selected > .pve-binding, .pve-control-simple-wrapper--selected > .pve-binding {
  opacity: 1;
}
.pve-control-wrapper--invalid > .pve-binding, .pve-control-simple-wrapper--invalid > .pve-binding {
  opacity: 1;
}
.pve-control-wrapper--dragging, .pve-control-simple-wrapper--dragging {
  opacity: 0.38;
}
.pve-control-wrapper:hover > .pve-binding, .pve-control-simple-wrapper:hover > .pve-binding {
  opacity: 1;
}
.pve-control-wrapper .gc-label, .pve-control-simple-wrapper .gc-label {
  display: -ms-grid;
  display: grid;
  height: 100%;
  -ms-grid-columns: minmax(0, 1fr);
  -ms-grid-rows: 30px minmax(0, 1fr);
  grid-template-columns: minmax(0, 1fr);
  grid-template-rows: 30px minmax(0, 1fr);
  grid-template-areas: "label" "content";
}
.pve-control-wrapper .gc-label__label, .pve-control-simple-wrapper .gc-label__label {
  width: 100%;
  grid-area: label;
  -ms-grid-row: 1;
  -ms-grid-row-span: 1;
  -ms-grid-column: 1;
  -ms-grid-column-span: 1;
}
.pve-control-wrapper .gc-label__content, .pve-control-simple-wrapper .gc-label__content {
  width: 100%;
  height: 100%;
  grid-area: content;
  -ms-grid-row: 2;
  -ms-grid-row-span: 1;
  -ms-grid-column: 1;
  -ms-grid-column-span: 1;
}
.pve-control-wrapper .gc-label--position-left, .pve-control-simple-wrapper .gc-label--position-left {
  -ms-grid-columns: 1fr minmax(0, 2fr);
  -ms-grid-rows: 1fr;
  grid-template-columns: 1fr minmax(0, 2fr);
  grid-template-rows: 100%;
  grid-template-areas: "label content";
}
.pve-control-wrapper .gc-label--position-right, .pve-control-simple-wrapper .gc-label--position-right {
  -ms-grid-columns: minmax(0, 2fr) 1fr;
  -ms-grid-rows: 1fr;
  grid-template-columns: minmax(0, 2fr) 1fr;
  grid-template-rows: 100%;
  grid-template-areas: "content label";
}
.pve-control-wrapper .gc-label--position-bottom, .pve-control-simple-wrapper .gc-label--position-bottom {
  -ms-grid-columns: 1fr;
  -ms-grid-rows: minmax(0, 1fr) 30px;
  grid-template-columns: 1fr;
  grid-template-rows: minmax(0, 1fr) 30px;
  grid-template-areas: "content" "label";
}

.pve-surface {
  position: relative;
  outline: var(--cui-bg-panels-border) solid 1px;
  background-color: var(--cui-bg-panels);
  background-image: linear-gradient(transparent 4px, var(--cui-bg-panels-border) 1px), linear-gradient(90deg, transparent 4px, var(--cui-bg-panels-border) 1px);
  background-size: 5px 5px;
}
.pve-surface::before {
  top: 0;
  left: 0;
  display: block;
  width: 100%;
  height: 100%;
  content: "";
  opacity: 0.6;
  background-color: var(--cui-bg-panels);
}
.pve-surface--vertical {
  width: 330px;
  margin: 50px auto;
  padding: 15px;
  background-image: none;
  box-shadow: 5px 5px 10px 0 rgba(0, 0, 0, 0.2);
}
.pve-surface--vertical > .pve-control-simple-wrapper:last-of-type {
  margin-bottom: 0;
}
.pve-surface--vertical > p {
  margin: 0;
  margin-bottom: 15px;
  padding: 0;
  opacity: 0.62;
  font-size: var(--cui-text-size);
}

.pve-surface-empty {
  display: flex;
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  align-content: center;
}

.pve-surface, .pve-surface-empty {
  grid-area: surface;
}

.pve-surface-message {
  display: flex;
  width: 100%;
  padding: 30px;
  grid-area: message;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  align-content: center;
}

.pve-surface-text {
  display: flex;
  width: 100%;
  height: 30px;
  margin: 0;
  padding: 0;
  font-size: var(--cui-text-size);
  line-height: 30px;
  justify-content: center;
  align-items: center;
}
.pve-surface-text > .gc-btn {
  margin: 0 15px;
}

.pve-surface-container {
  display: grid;
  width: 100%;
  height: 100%;
  grid-template-columns: 1fr;
  grid-template-rows: minmax(0, 1fr) auto;
  grid-template-areas: "surface" "message";
}

.pve-drag-box {
  border: 1px solid var(--cui-accent);
}
.pve-drag-box--resize {
  background-color: rgba(255, 255, 255, 0.62);
}

.pve-resize-grip {
  position: absolute;
  z-index: 1091;
  display: flex;
  width: 10px;
  height: 10px;
  border: 1pt solid var(--cui-accent);
  border-radius: 50%;
  background-color: white;
  align-items: center;
  justify-content: center;
}
.pve-resize-grip::after {
  display: block;
  width: 6px;
  height: 6px;
  content: "";
  border-radius: 50%;
  background-color: var(--cui-accent);
}
.pve-resize-grip--s {
  bottom: 0;
  left: 50%;
  cursor: ns-resize;
  transform: translate(-50%, 50%);
}
.pve-resize-grip--e {
  top: 50%;
  right: 0;
  cursor: ew-resize;
  transform: translate(50%, -50%);
}
.pve-resize-grip--se {
  right: 0;
  bottom: 0;
  cursor: se-resize;
  transform: translate(50%, 50%);
}
.pve-resize-grip--se::after {
  display: none;
}

.pve-no-events {
  pointer-events: none;
}

.pve-icon-color-accent {
  fill: var(--cui-accent);
}

.pve-icon-color-secondary {
  fill: var(--cui-accent-secondary);
}

.pve-icon-color-error {
  fill: var(--cui-accent-error);
}

.pve-date-time-range-from-input {
  margin-bottom: 5px;
}

.wd-plot-rules-panel {
  width: 100%;
  height: 100%;
}
.wd-plot-rules-panel .wd-plot-rules {
  padding: 0 15px;
}

.wd-plot-rules__nav {
  display: flex;
  overflow: hidden;
  width: calc(100% + 5px);
  height: 30px;
  margin-bottom: 15px;
  align-items: flex-start;
  justify-content: stretch;
}
.wd-plot-rules__nav > .gc-btn {
  flex: 1 1 30px;
  margin-right: 5px;
}

.wd-editor-rule-property {
  display: flex;
  width: 100%;
  height: 30px;
  align-items: flex-start;
  justify-content: space-between;
}
.wd-editor-rule-property > .wd-enum-editor, .wd-editor-rule-property > .gc-binding-wrapper {
  width: calc(50% - 2.5px);
}

.wd-nested-property-grid-panel {
  width: 100%;
  height: 100%;
}
.wd-nested-property-grid-panel .gc-property-category {
  padding: 0 15px;
}

.wd-report-parts-libraries-panel {
  padding: 15px;
  display: flex;
  flex-direction: column;
  height: 100%;
}
.wd-report-parts-libraries-panel__content {
  margin-bottom: auto;
}
.wd-report-parts-libraries-panel__content .wd-libraries-item {
  display: grid;
  grid-template-columns: minmax(0, max-content) max-content;
  align-items: center;
}
.wd-report-parts-libraries-panel__heading .gc-heading__divider {
  opacity: 0.1;
}
.wd-report-parts-libraries-panel__no-elements-label {
  display: block;
  overflow: hidden;
  width: 100%;
  user-select: none;
  text-align: center;
  color: var(--cui-text-semi-60);
  font-size: var(--cui-text-size);
  line-height: 40px;
}

.wd-report-parts-libraries-panel-item {
  display: flex;
  overflow: hidden;
  min-height: 30px;
  padding-right: 6px;
  border-radius: var(--cui-border-radius);
  align-items: center;
  justify-content: stretch;
}
.wd-report-parts-libraries-panel-item__icon {
  margin: 0 6px;
}
.wd-report-parts-libraries-panel-item__label {
  font-size: var(--cui-text-size);
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  user-select: none;
}
.wd-report-parts-libraries-panel-item:hover {
  background-color: var(--cui-btn-bg-hover);
  transition: background-color 0.2s ease-in-out, opacity 0.2s ease-in-out;
}

.wd-dv-preview {
  overflow: hidden;
  height: 100%;
  border-radius: 4px;
  box-shadow: inset 0 0 0 1px var(--cui-bg-panels-border);
}
.wd-dv-preview__bar {
  position: relative;
  top: 25%;
  height: 50%;
}
.wd-dv-preview__data-bar {
  position: relative;
  height: 100%;
}
.wd-dv-preview__progress-line {
  position: relative;
  top: -12.5%;
  height: 25%;
}
.wd-dv-preview--gradient {
  background-repeat: no-repeat;
}
.wd-dv-preview--range-bar {
  background-color: white;
}

.wd-dv-preview-container {
  width: 100%;
  height: 100px;
  padding: 10px 0;
}

.wd-data-visualizer {
  padding: 15px;
}

.wd-data-visualizer-dialog {
  width: 400px;
}
.wd-data-visualizer-dialog--wide {
  width: 800px;
}

.rdlx-theme-picker {
  display: grid;
  width: calc(4 * var(--item-width) + 30px);
  padding: 0 15px;
  cursor: default;
  color: var(--cui-text);
  --item-width: 150px;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(auto);
}

.rdlx-theme-picker-item {
  display: grid;
  width: var(--item-width);
  height: calc(var(--item-width) * 10 / 16);
  padding: 10px;
  border-radius: var(--cui-border-radius);
  font-size: var(--cui-text-size);
  gap: 5px;
  grid-template-columns: 1fr;
  grid-template-rows: 1fr 1fr 1fr;
  justify-items: center;
  align-items: center;
}
.rdlx-theme-picker-item:hover:not(.rdlx-theme-picker-item--selected) {
  background-color: var(--cui-dd-background-hover);
}
.rdlx-theme-picker-item--selected {
  color: var(--cui-accent-text);
  background-color: var(--cui-accent-text-semi-10);
  font-weight: bold;
}

.rdlx-theme-picker-item__text {
  display: flex;
  width: 100%;
  gap: 10px;
  justify-content: center;
  align-items: flex-end;
}
.rdlx-theme-picker-item__text::after {
  display: block;
  flex: 1 1 auto;
  max-width: 50%;
  height: 1.2em;
  content: " ";
  opacity: 0.38;
  background-image: linear-gradient(to bottom, var(--cui-text) 1px, transparent 1px, transparent 1px);
  background-size: 5px 5px;
}

.rdlx-theme-picker-item__text > div {
  display: flex;
  flex: 0 0 auto;
  font-size: 1.6em;
  line-height: 1;
  aspect-ratio: 1/1;
  justify-content: center;
  align-items: center;
}
.rdlx-theme-picker-item__text > div > span {
  display: block;
}

.rdlx-theme-picker-item__colors {
  display: flex;
}
.rdlx-theme-picker-item__colors > div {
  width: 1em;
  border: 1px solid var(--cui-text);
  border-right: none;
  border-left: none;
  aspect-ratio: 1/1;
}
.rdlx-theme-picker-item__colors > div:last-child {
  border-right: 1px solid var(--cui-text);
  border-radius: 0 var(--cui-border-radius) var(--cui-border-radius) 0;
}
.rdlx-theme-picker-item__colors > div:first-child {
  border-left: 1px solid var(--cui-text);
  border-radius: var(--cui-border-radius) 0 0 var(--cui-border-radius);
}

.rdlx-theme-picker-item__label {
  overflow: hidden;
  max-width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.rdlx-theme-picker-item__icon-none {
  display: flex;
  grid-row: span 2;
  justify-content: center;
  align-items: center;
}

.rdlx-field-picker {
  width: 100%;
  height: 100%;
}
.rdlx-field-picker__toggle {
  position: absolute;
  top: 0.04in;
  right: 0.04in;
  display: block;
  width: 0.17in;
  height: 0.17in;
  pointer-events: all !important;
  opacity: 0;
  line-height: 0.17in;
}
.rdlx-field-picker__toggle--offset-to-start {
  top: 0;
  left: 0;
}
.rdlx-field-picker__toggle--hidden > .gc-input[type=text] {
  display: none;
}
.rdlx-field-picker__toggle--non-selected > .gc-input[type=text] {
  box-sizing: content-box;
}
.rdlx-field-picker__toggle .gc-input[type=text] {
  opacity: 0;
  border-color: transparent;
  background-color: transparent;
}
.rdlx-field-picker__toggle .gc-dd > .gc-btn.gc-size-sm {
  width: 0.17in;
  height: 0.17in;
  line-height: 0.17in;
}
.rdlx-field-picker__toggle .gc-dd__toggle-content {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  backdrop-filter: blur(2px);
}
.rdlx-field-picker__toggle .gc-dd__toggle-content .gc-icon > svg {
  width: 100%;
  height: 100%;
}

.rdlx-field-picker__toggle--open:not(.rdlx-field-picker__toggle--semantic) {
  z-index: 100014;
  top: calc((0.17in / 2 + 0.04in) - 15px);
  right: calc((0.17in / 2 + 0.04in) - 15px);
  width: 200px;
  height: 30px;
  opacity: 1;
}
.rdlx-field-picker__toggle--open:not(.rdlx-field-picker__toggle--semantic) .gc-input[type=text] {
  padding-right: 10px;
  opacity: 1;
  border-radius: var(--cui-border-radius);
  background-color: var(--cui-dd-background);
  box-shadow: var(--cui-shadow-border);
}
.rdlx-field-picker__toggle--open:not(.rdlx-field-picker__toggle--semantic) .gc-input[type=text]:not([disabled]):hover {
  background-color: var(--cui-dd-background);
}
.rdlx-field-picker__toggle--open:not(.rdlx-field-picker__toggle--semantic) .gc-input[type=text]:not([disabled]):focus {
  background-color: var(--cui-dd-background);
  box-shadow: var(--cui-shadow-border) !important;
}
.rdlx-field-picker__toggle--open:not(.rdlx-field-picker__toggle--semantic) .gc-dd > .gc-btn.gc-size-sm {
  display: none;
}

.rdlx-field-picker__toggle--semantic.rdlx-field-picker__toggle--open {
  opacity: 1;
}
.rdlx-field-picker__toggle--semantic > .gc-btn.gc-size-sm {
  width: 0.17in;
  height: 0.17in;
  line-height: 0.17in;
}

.rdlx-field-picker:hover .rdlx-field-picker__toggle {
  opacity: 1;
}

.rdlx-ruler-highlight-span__primary {
  background-color: var(--wd-rulers-span-primary);
}
.rdlx-ruler-highlight-span__secondary {
  background-color: var(--wd-rulers-span-secondary);
}

.rdlx-ruler-highlight-marker {
  background-color: var(--cui-accent);
}

.ar-adorner-panel {
  position: absolute;
  overflow: hidden;
  cursor: default;
  pointer-events: all;
  border-radius: 4px;
  background-color: var(--cui-bg-panels);
  box-shadow: 1px 1px 5px 0 rgba(0, 0, 0, 0.1);
}
.ar-adorner-panel__heading {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 30px;
  padding: 0 5px;
  align-items: center;
}
.ar-adorner-panel__content {
  display: block;
  overflow: hidden;
  width: 100%;
  height: calc(100% - 30px);
  padding: 4px;
  padding-top: 0;
}
.ar-adorner-panel__content > .wd-drop-area {
  width: 100%;
  height: 100%;
}
.ar-adorner-panel__content > .wd-drop-area .gc-scrollbars__view .ar-adorner-panel__item:last-of-type {
  margin-bottom: 0;
}

.ar-adorner-panel__item {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 25px;
  margin-bottom: 5px;
  padding: 0 5px;
  user-select: none;
  transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out;
  white-space: nowrap;
  text-overflow: ellipsis;
  border-radius: 4px;
  background-color: var(--cui-btn-bg);
  font-family: var(--cui-text-family);
  font-size: var(--cui-text-size);
  line-height: 30px;
  justify-content: left;
  align-items: center;
}
.ar-adorner-panel__item:hover {
  background-color: var(--cui-btn-bg-hover);
}
.ar-adorner-panel__item--selected {
  color: var(--cui-contrast);
  background-color: var(--cui-accent);
}
.ar-adorner-panel__item--selected .gc-ci-a-accent {
  fill: var(--cui-contrast);
}
.ar-adorner-panel__item--selected:hover {
  background-color: var(--cui-accent-hover);
}

.ar-adorner-panel__item-icon {
  display: block;
  width: 16px;
  height: 16px;
  margin-right: 5px;
}

.ar-adorner-panel__item-icon-group {
  display: flex;
  width: 100%;
  height: 100%;
  white-space: nowrap;
  font-size: var(--cui-text-size-sm);
  line-height: 16px;
  align-items: center;
  justify-content: left;
}
.ar-adorner-panel__item-icon-group > .gc-icon {
  overflow: hidden;
  width: 7px;
}

.rdlx-tabs {
  display: grid;
  width: 100%;
  height: calc(30px + 1px);
  border-top: 1px solid var(--cui-bg-panels-border);
  background-color: var(--cui-bg-panels);
  grid-template-columns: auto minmax(0, 100%) auto;
  grid-template-rows: 100%;
  grid-template-areas: "rulers container add";
}
.rdlx-tabs--with-rulers::before {
  width: 17px;
  height: 100%;
  content: " ";
  border-right: 1px solid var(--cui-bg-panels-border);
  grid-area: rulers;
}
.rdlx-tabs__container {
  grid-area: container;
}
.rdlx-tabs__container .gc-scrollbars:hover .gc-scrollbars__track--horizontal {
  display: none;
}
.rdlx-tabs__add {
  border-left: 1px solid var(--cui-bg-panels-border);
  border-radius: 0;
  grid-area: add;
}
.rdlx-tabs__items {
  position: relative;
  display: flex;
  pointer-events: all;
  align-items: center;
  flex-wrap: nowrap;
}
.rdlx-tabs__separator {
  width: 1px;
  height: 15px;
  transition: height 0.3s ease-in-out;
  background-color: var(--cui-bg-panels-border);
  flex-grow: 0;
  flex-shrink: 0;
}
.rdlx-tabs__indicator {
  position: absolute;
  bottom: 5px;
  display: block;
  height: 2px;
  padding: 0 8px;
  transition: left 0.2s ease-in-out, width 0.2s ease-in-out;
}
.rdlx-tabs__indicator::before {
  display: block;
  width: 100%;
  height: 100%;
  content: " ";
  border-radius: 2px;
  background-color: var(--cui-accent-text);
}

.rdlx-tabs__separator--selected {
  height: 30px;
}

.rdlx-tab {
  position: relative;
  display: block;
  min-width: 60px;
  max-width: 160px;
  height: 30px;
  padding: 0 10px;
  transition: background-color 0.2s ease-in-out;
  flex-grow: 0;
  flex-shrink: 0;
  transition: 0.2s ease-out;
}
.rdlx-tab__name {
  display: block;
  overflow: hidden;
  width: 100%;
  height: 100%;
  user-select: none;
  transition: line-height 0.2s ease-in-out;
  white-space: nowrap;
  text-overflow: ellipsis;
  pointer-events: none;
  font-size: var(--cui-text-size);
  line-height: 30px;
}
.rdlx-tab__indicator {
  position: absolute;
  bottom: 5px;
  left: 50%;
  display: block;
  width: 0;
  height: 2px;
  transition: opacity 0.3s ease-in-out, width 0.3s ease-in-out;
  transform: translateX(-50%);
  opacity: 0;
  border-radius: 2px;
  background-color: var(--cui-bg-panels-border);
}
.rdlx-tab--selected {
  background-color: var(--cui-bg-panels-border);
  font-weight: bold;
}
.rdlx-tab--selected .rdlx-tab__name {
  line-height: 24px;
}
.rdlx-tab--hide {
  display: none;
}
.rdlx-tab--hidden {
  opacity: 0.38;
}
.rdlx-tab--dragging {
  position: absolute;
  background: var(--cui-accent);
  color: white;
  transition: none;
  z-index: 1000;
  pointer-events: none;
  transform: translateX(-100%);
}
.rdlx-tab:not(.rdlx-tab--selected):hover .rdlx-tab__name {
  line-height: 24px;
}
.rdlx-tab:not(.rdlx-tab--selected):hover .rdlx-tab__indicator {
  width: calc(100% - 16px);
  opacity: 1;
}

.wd-binding-wrapper--error .gc-binding-wrapper__toggle {
  background-color: red;
}

.wd-editor-line {
  display: flex;
  justify-content: space-between;
}
.wd-editor-line__block {
  width: 49%;
}

.wd-bool-editor--expr {
  float: right;
  color: var(--cui-text-semi-60);
  font-size: var(--cui-text-size);
  line-height: 30px;
}

.wd-editor-chart-plot-template .gc-btn__text, .wd-editor-chart-template .gc-btn__text {
  padding-left: 10px;
}

.wd-color-editor > input {
  padding-right: 15px;
}
.wd-color-editor .wd-color-editor__preview {
  width: 20px;
  height: 20px;
  margin: 0;
}
.wd-color-editor div.wd-color-editor-side__preview {
  overflow: hidden;
  width: 20px;
  height: 20px;
  margin: 0;
  border-radius: 4px;
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);
}
.wd-color-editor div.wd-color-editor-side__preview > span {
  display: block;
  float: left;
  width: 10px;
  height: 10px;
}

.wd-editor-icon-set-value {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 30px;
}
.wd-editor-icon-set-value > .gc-icon {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  width: 20px;
  height: 30px;
  align-items: center;
  justify-content: center;
}
.wd-editor-icon-set-value .gc-label__label > span {
  padding-left: 25px;
}

.wd-editor-encoding-collection__item {
  display: flex;
}
.wd-editor-encoding-collection__item-title {
  font-size: var(--cui-text-size);
  line-height: 30px;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-grow: 1;
}

.wd-color-encoding-editor__collection--disabled {
  cursor: not-allowed;
}
.wd-color-encoding-editor__collection--disabled > div {
  pointer-events: none;
  opacity: 0.5;
}

.wd-editor-image .gc-label__content {
  overflow: hidden;
}
.wd-editor-image .gc-label__content .gc-btn__text {
  padding-left: 10px;
}

.wd-dropdown-image-uploader {
  display: flex;
  overflow: hidden;
  overflow: hidden;
  width: 100%;
  height: 30px;
  user-select: none;
  transition: background-color 0.2s ease-in-out;
  border-radius: 4px;
  background-color: var(--cui-btn-bg);
  font-size: var(--cui-text-size);
  align-items: center;
  justify-content: flex-start;
}
.wd-dropdown-image-uploader > .gc-icon {
  color: var(--cui-accent-text);
  display: flex;
  width: 30px;
  height: 30px;
  align-items: center;
  justify-content: center;
}
.wd-dropdown-image-uploader > .gc-icon > svg {
  width: 16px;
  height: 16px;
}
.wd-dropdown-image-uploader > input[type=file] {
  width: 1px;
  height: 1px;
  opacity: 0;
}
.wd-dropdown-image-uploader:hover {
  background-color: var(--cui-btn-bg-hover);
}

.wd-dropdown-image-tile {
  position: relative;
  display: flex;
  overflow: hidden;
  flex: 0 0 auto;
  width: calc((100% - 15px) / 3);
  height: 80px;
  margin-right: 5px;
  transition: background-color 0.2s ease-in-out;
  border-radius: 4px;
  align-items: center;
  justify-content: center;
}
.wd-dropdown-image-tile::after {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  content: "";
  transition: box-shadow 0.2s ease-in-out;
  pointer-events: none;
  box-shadow: inset 0 0 0 2px transparent, inset 0 0 0 3px transparent;
}
.wd-dropdown-image-tile > .gc-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  transition: opacity 0.2s ease-in-out;
  opacity: 0;
}
.wd-dropdown-image-tile:hover:not(.wd-dropdown-image-tile--selected) {
  background-color: var(--cui-dd-background-hover);
}
.wd-dropdown-image-tile:hover > .gc-btn {
  opacity: 1;
}
.wd-dropdown-image-tile:hover::after {
  box-shadow: inset 0 0 0 2px var(--cui-accent-hover), inset 0 0 0 3px var(--cui-dd-background);
}
.wd-dropdown-image-tile--selected {
  color: var(--cui-contrast-text);
  background-color: var(--cui-accent);
}
.wd-dropdown-image-tile--selected::after {
  box-shadow: inset 0 0 0 2px var(--cui-accent);
}
.wd-dropdown-image-tile--selected:hover {
  background-color: var(--cui-accent);
}

.wd-dropdown-image-tile__img {
  display: block;
  flex: 0 0 auto;
  height: 100%;
}

.wd-dropdown-image-tile__stub {
  display: flex;
  overflow: hidden;
  flex-direction: column;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: stretch;
}
.wd-dropdown-image-tile__stub > i {
  color: var(--cui-accent-text);
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
}
.wd-dropdown-image-tile__stub > i > svg {
  width: 24px;
  height: 24px;
}
.wd-dropdown-image-tile__stub > span {
  overflow: hidden;
  flex: 0 0 auto;
  width: 100%;
  height: 30px;
  padding: 0 5px;
  user-select: none;
  text-align: center;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: var(--cui-text-size);
  line-height: 20px;
}

.wd-chart-editor-tile--selected .wd-dropdown-image-tile__stub > i {
  color: var(--cui-contrast-text);
}

.wd-dropdown-image-panel {
  display: flex;
  overflow: hidden;
  flex-direction: column;
  width: 100%;
  padding: 15px;
  padding-bottom: 0;
}
.wd-dropdown-image-panel .gc-scrollbars__view {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: flex-start;
}

.wd-dropdown-image-panel__loader {
  width: 100%;
  height: 45px;
}

.wd-dropdown-image-panel__content {
  width: 100%;
  height: 100%;
}

.wd-dropdown-image-panel__empty {
  display: flex;
  height: 120px;
  opacity: 0.38;
  font-size: var(--cui-text-size);
  justify-content: center;
  align-items: center;
}

.wd-dropdown-image-db-group .gc-heading {
  opacity: 0.62;
}

.wd-dropdown-image-db-item {
  display: flex;
  height: 30px;
  transition: background-color 0.2s ease-in-out;
  border-radius: 4px;
  font-size: var(--cui-text-size);
  align-items: center;
}
.wd-dropdown-image-db-item > .gc-icon {
  display: flex;
  width: 30px;
  height: 30px;
  align-items: center;
  justify-content: center;
}
.wd-dropdown-image-db-item > .gc-icon > svg {
  width: 20px;
  height: 20px;
}
.wd-dropdown-image-db-item > span {
  user-select: none;
}
.wd-dropdown-image-db-item:hover:not(.wd-dropdown-image-db-item--selected) {
  background-color: var(--cui-dd-background-hover);
}
.wd-dropdown-image-db-item--selected {
  color: var(--cui-contrast-text);
  background-color: var(--cui-accent);
}
.wd-dropdown-image-db-item--selected:hover {
  background-color: var(--cui-accent);
}

.wd-dropdown-image-db-panel {
  width: 100%;
  padding: 15px;
  padding-bottom: 0;
}
.wd-dropdown-image-db-panel__empty {
  display: flex;
  height: 120px;
  opacity: 0.38;
  font-size: var(--cui-text-size);
  justify-content: center;
  align-items: center;
}

.wd-dropdown-image {
  position: relative;
  width: 300px;
  padding: 15px 0;
}
.wd-dropdown-image .gc-btn {
  overflow: hidden;
}
.wd-dropdown-image > .gc-btn-group {
  margin: 0 15px;
}
.wd-dropdown-image--single-source .wd-dropdown-image-db-panel, .wd-dropdown-image--single-source .wd-dropdown-image-panel {
  padding-top: 0;
}
.wd-dropdown-image--single-source .wd-dropdown-image__provider {
  margin-top: 0;
  margin-bottom: 15px;
}
.wd-dropdown-image__loader {
  position: absolute;
  top: 0;
  left: 0;
  min-height: 100px;
}
.wd-dropdown-image__provider {
  width: calc(100% - 30px);
  margin: 0 15px;
  margin-top: 15px;
}

.wd-embedded-collection-input {
  display: block;
  width: 0;
  height: 0;
  margin: 0;
  opacity: 0;
}

.wd-embedded-collection__item {
  position: relative;
  display: flex;
  height: 30px;
}
.wd-embedded-collection__item > input.gc-input {
  padding-left: 30px;
}
.wd-embedded-collection__item__preview {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  height: 100%;
  padding: 5px;
  pointer-events: none;
  border-radius: 4px;
  justify-content: center;
  align-items: center;
}
.wd-embedded-collection__item__img, .wd-embedded-collection__item__stub {
  width: 20px;
  height: 20px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.wd-line-style-editor__preview {
  width: 20px;
  border-top-width: 4px;
}

.wd-length-editor, .wd-length-expr-editor {
  width: 100%;
}
.wd-length-editor__input.gc-input, .wd-length-expr-editor__input.gc-input {
  text-align: left;
}

.wd-number-editor, .wd-number-expr-editor {
  display: flex;
  width: 100%;
}
.wd-number-editor__button, .wd-number-expr-editor__button {
  flex: 0 0 auto;
}
.wd-number-editor__button:first-of-type, .wd-number-expr-editor__button:first-of-type {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.wd-number-editor__button:last-of-type, .wd-number-expr-editor__button:last-of-type {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.wd-number-editor__input.gc-input, .wd-number-expr-editor__input.gc-input {
  flex: 2 2 auto;
  width: 100%;
  text-align: center;
  border-radius: 0;
}

.wd-editor-params-collection__item {
  display: flex;
}
.wd-editor-params-collection__item > *:not(.wd-editor-params-collection__item-toggle) {
  width: 0;
  flex-grow: 1;
  flex-shrink: 1;
}
.wd-editor-params-collection__item > .wd-editor-params-collection__item-toggle {
  margin-left: 5px;
  flex-grow: 0;
  flex-shrink: 0;
}

.wd-collection-editor-row {
  display: flex;
  gap: 5px;
  justify-content: space-between;
}
.wd-collection-editor-row__element {
  overflow: hidden;
  flex: 1 1 0;
  min-width: 0;
}

.wd-editor-chart-collection-editor__item {
  display: block;
  overflow: hidden;
  height: 30px;
  padding: 0 10px;
  user-select: none;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: var(--cui-text);
  border-radius: 4px;
  background-color: var(--cui-input-bg);
  font-family: var(--cui-text-family);
  font-size: var(--cui-text-size);
  line-height: 30px;
}

.wd-editor-chart-plot-pointers__item {
  display: grid;
  grid-auto-flow: column;
  align-items: center;
  grid-auto-columns: min-content;
}

.wd-editor-chart-plot-rules__item {
  display: block;
  overflow: hidden;
  height: 30px;
  padding: 0 10px;
  user-select: none;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: var(--cui-text);
  border-radius: 4px;
  background-color: var(--cui-input-bg);
  font-family: var(--cui-text-family);
  font-size: var(--cui-text-size);
  line-height: 30px;
}
.wd-editor-chart-plot-rules__item--empty {
  opacity: 0.38;
}

.wd-chart-rule-header {
  display: flex;
  justify-content: space-between;
}
.wd-chart-rule-header__block {
  width: 50%;
}

.wd-editor-simple-toggle {
  min-width: 0;
}
.wd-editor-simple-toggle .gc-dd__toggle-content {
  height: 100%;
  white-space: normal;
}

.wd-editor-simple-toggle-content {
  overflow: hidden;
  width: 100%;
  height: 100%;
  padding: 3px 8px 0;
  user-select: none;
}
.wd-editor-simple-toggle-content__name {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  height: 15px;
  line-height: 15px;
}
.wd-editor-simple-toggle-content__value {
  display: flex;
  width: 100%;
  height: 20px;
  align-items: center;
}
.wd-editor-simple-toggle-content__text {
  overflow: hidden;
  width: 100%;
  height: 100%;
  text-overflow: ellipsis;
  color: var(--cui-accent-text);
  line-height: 20px;
}
.wd-editor-simple-toggle-content__color {
  width: 100%;
  height: 7px;
  border-radius: 3px;
}
.wd-editor-simple-toggle-content__color--transparent {
  border: 1px dotted var(--cui-text-semi-60);
}

.wd-editor-simple-row {
  display: flex;
}
.wd-editor-simple-row.wd-editor-simple-buttons > .gc-btn-group--block {
  width: 0;
}

.wd-editor-simple-section + .wd-editor-simple-section {
  margin-left: 5px;
}
.wd-editor-simple-section.gc-btn-group--block {
  flex: 1 1 auto;
}
.wd-editor-simple-section.gc-btn-group--block > *:not(.gc-btn-group__splitter) {
  flex: 1;
}

.wd-font-dropdown__item {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.wd-editor-s-font-family {
  min-width: 130px;
}

.wd-editor-s-item-font-family {
  white-space: nowrap;
  line-height: 1;
}

.wd-editor-s-section-font:only-child .wd-editor-s-font-family {
  max-width: 250px;
  flex-grow: 1 !important;
}

.wd-editor-simple-dropdown__item {
  width: 50%;
  border-radius: 0;
}
.wd-editor-simple-dropdown__item.gc-btn--accent .wd-editor-simple-dropdown__preview {
  color: inherit;
}
.wd-editor-simple-dropdown__preview {
  display: flex;
  height: 25px;
  color: var(--cui-accent);
  font-size: 16px;
  line-height: 1.1;
  align-items: flex-end;
  justify-content: center;
}
.wd-editor-simple-dropdown__text {
  height: 20px;
  text-align: center;
  line-height: 20px;
}

.wd-editor-simple-toggle-sides__preview {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-width: 1px;
  border-style: dotted;
  border-color: var(--cui-accent);
  border-radius: 1px;
  background-color: var(--cui-contrast);
  flex-shrink: 0;
}

.wd-editor-simple-toggle-sides__menu .gc-btn--accent .wd-editor-simple-toggle-sides__preview {
  border-color: currentColor;
  background-color: inherit;
}

.wd-editor-simple-toggle-sides {
  flex: 0 0 auto !important;
  min-width: 50px;
}
.wd-editor-simple-toggle-sides__preview {
  margin-right: 3px;
}

.wd-editor-simple-border-style__preview {
  display: block;
  width: 20px;
  height: 5px;
  border-top-width: 4px;
}

.wd-editor-simple-border-width__preview {
  display: block;
  width: 20px;
  height: auto;
  border-top-style: solid;
  border-top-color: currentColor;
}

.wd-editor-sub-item-collection__item {
  display: flex;
}
.wd-editor-sub-item-collection__item > *:not(.wd-editor-sub-item-collection__item-toggle) {
  width: 0;
  flex-grow: 1;
  flex-shrink: 1;
}
.wd-editor-sub-item-collection__item > .wd-editor-sub-item-collection__item-toggle {
  margin-left: 5px;
  flex-grow: 0;
  flex-shrink: 0;
}

.wd-sort-expression-line {
  position: relative;
}
.wd-sort-expression-line > .gc-text-editor > .gc-input {
  padding-right: 30px;
}
.wd-sort-expression-line > .gc-binding-wrapper > .gc-binding-wrapper__content > .gc-input {
  padding-right: 30px;
  text-overflow: ellipsis;
}
.wd-sort-expression-line__button {
  position: absolute;
  top: 0;
  right: 30px;
}

.wd-editor-format {
  display: flex;
  overflow: hidden;
  width: 100%;
  justify-content: stretch;
  align-items: flex-start;
}
.wd-editor-format > .gc-combo {
  flex: 1 1 auto;
  margin-right: 5px;
}

.wd-editor-format-digits {
  position: relative;
  display: block;
  flex: 0 0 auto;
  width: 90px;
}
.wd-editor-format-digits__input.gc-input {
  display: block;
  width: 100%;
  padding: 0 30px;
  text-align: center;
  border-radius: 0;
}
.wd-editor-format-digits__button {
  position: absolute;
  top: 0;
}
.wd-editor-format-digits__button:first-of-type {
  left: 0;
}
.wd-editor-format-digits__button:last-of-type {
  right: 0;
}

.wd-parameter-valid-value__checklist-item, .wd-parameter-default-value__checklist-item {
  overflow: hidden;
  text-overflow: ellipsis;
}

.wd-parameter-valid-value-reference > div, .wd-parameter-default-value-reference > div {
  margin-bottom: 5px;
}

.wd-parameter-valid-value-collection {
  display: flex;
}
.wd-parameter-valid-value-collection__element {
  flex: 1 1 50%;
}

.wd-parameter-order__label {
  position: relative;
}
.wd-parameter-order__button {
  position: absolute;
  top: 0;
  right: 8%;
}

.wd-themes-editor__preview-color, .wd-themes-editor__preview-item {
  display: -ms-grid;
  display: grid;
  overflow: hidden;
  width: 20px;
  height: 20px;
  margin: 5px;
  border-radius: 4px;
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);
  grid: repeat(2, 0.5fr) / repeat(2, 0.5fr);
}
.wd-themes-editor__preview-color span, .wd-themes-editor__preview-item span {
  display: block;
  width: 10px;
  height: 10px;
}
.wd-themes-editor__preview-color span:nth-child(1), .wd-themes-editor__preview-item span:nth-child(1) {
  -ms-grid-column: 1;
  -ms-grid-row: 1;
}
.wd-themes-editor__preview-color span:nth-child(2), .wd-themes-editor__preview-item span:nth-child(2) {
  -ms-grid-column: 2;
  -ms-grid-row: 1;
}
.wd-themes-editor__preview-color span:nth-child(3), .wd-themes-editor__preview-item span:nth-child(3) {
  -ms-grid-column: 1;
  -ms-grid-row: 2;
}
.wd-themes-editor__preview-color span:nth-child(4), .wd-themes-editor__preview-item span:nth-child(4) {
  -ms-grid-column: 2;
  -ms-grid-row: 2;
}
.wd-themes-editor__preview-item {
  margin: 0;
}

.wd-filter-add {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 30px;
  justify-content: stretch;
  align-items: stretch;
}
.wd-filter-add__field-dd {
  overflow: hidden;
  flex: 1 1 auto;
  width: 100%;
}
.wd-filter-add__operator-stub {
  overflow: hidden;
  flex: 0 0 auto;
  width: 30px;
  margin: 0 5px;
  opacity: 0.38;
  background-color: var(--cui-btn-bg);
}
.wd-filter-add__value-stub {
  overflow: hidden;
  flex: 1 1 auto;
  width: 100%;
  opacity: 0.38;
  background-color: var(--cui-btn-bg);
}
.wd-filter-add__delete-stub {
  overflow: hidden;
  flex: 0 0 auto;
  width: 30px;
  margin-left: 5px;
  opacity: 0.38;
  background-color: var(--cui-btn-bg);
}

.wd-filter-criterion {
  display: flex;
  overflow: hidden;
  width: 100%;
  justify-content: stretch;
  align-items: stretch;
}
.wd-filter-criterion__field-dd {
  overflow: hidden;
  flex: 1 1 auto;
  width: 100%;
}
.wd-filter-criterion__operator-dd {
  overflow: hidden;
  flex: 0 0 auto;
  width: 30px;
  margin: 0 5px;
}
.wd-filter-criterion > .wd-filter-value {
  overflow: hidden;
  flex: 1 1 auto;
  width: 100%;
}
.wd-filter-criterion > .gc-btn {
  flex: 0 0 auto;
  margin-left: 5px;
}

.wd-filters-group {
  display: block;
  overflow: hidden;
  width: 100%;
}
.wd-filters-group__header {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 30px;
  justify-content: space-between;
  align-items: stretch;
}

.wd-filters-group__container {
  position: relative;
  width: 100%;
  padding-top: 15px;
  padding-left: 30px;
}
.wd-filters-group__container::before, .wd-filters-group__container::after {
  position: absolute;
  top: 0;
  left: 14px;
  display: block;
  width: 14px;
  height: 29px;
  content: "";
  border-width: 0;
  border-style: solid;
  border-color: var(--cui-accent);
  border-left-width: 2px;
}
.wd-filters-group__container::before {
  border-bottom-width: 2px;
}
.wd-filters-group__container::after {
  top: 29px;
  height: calc(100% - 29px);
}

.wd-filters-group__container--last::after {
  border-color: var(--cui-accent-semi-40);
}
.wd-filters-group__container--new::before, .wd-filters-group__container--new::after {
  border-color: var(--cui-accent-semi-40);
}

.wd-filters-group__container:last-of-type::after {
  display: none;
}

.wd-filter-value-editor {
  display: flex;
  width: 100%;
  height: 30px;
  align-items: center;
  justify-content: stretch;
}
.wd-filter-value-editor > .wd-filter-value-editor__input {
  width: calc(100% - 30px);
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.wd-filter-value-editor > .gc-dd {
  width: 30px;
}
.wd-filter-value-editor > .gc-dd > .gc-btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.gc-btn.gc-size-sm.gc-btn--with-icon.wd-filter-value-editor__binding {
  width: 100%;
  border-radius: 0;
}
.gc-btn.gc-size-sm.gc-btn--with-icon.wd-filter-value-editor__binding > .gc-btn__icon {
  left: 7.5px;
}
.gc-btn.gc-size-sm.gc-btn--with-icon.wd-filter-value-editor__binding > .gc-btn__text {
  padding-left: 40px;
}
.gc-btn.gc-size-sm.gc-btn--with-icon.wd-filter-value-editor__binding--field .gc-icon {
  color: var(--cui-accent-text);
}
.gc-btn.gc-size-sm.gc-btn--with-icon.wd-filter-value-editor__binding--parameter .gc-icon {
  color: #70087d;
}
.gc-btn.gc-size-sm.gc-btn--with-icon.wd-filter-value-editor__binding--expression .gc-icon {
  color: #dfc40f;
}

.wd-filter-value-editor__overlay {
  display: flex;
  overflow: hidden;
  width: calc(100% - 30px);
  height: 100%;
  pointer-events: none;
  border: 1px solid transparent;
  border-radius: 2px;
  background-color: var(--cui-input-bg);
  justify-content: stretch;
  align-items: stretch;
}
.wd-filter-value-editor__overlay > .gc-icon {
  overflow: hidden;
  flex: 0 0 auto;
}
.wd-filter-value-editor__overlay > span {
  overflow: hidden;
  flex: 1 1 auto;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: var(--cui-text-size);
  line-height: 30px;
}

.wd-filter-value-editor__overlay--invalid {
  border-color: var(--cui-accent-error);
}

.wd-filter-icon {
  display: flex;
  width: 30px;
  height: 30px;
  color: var(--cui-contrast);
  border-radius: 4px;
  justify-content: center;
  align-items: center;
}

.wd-filter-value > .wd-filter-value-editor + .wd-filter-value-editor__input, .wd-filter-value > .gc-btn, .wd-filter-value > .wd-filter-value-editor__input + .wd-filter-value-editor__input {
  margin-top: 5px;
}
.wd-filter-value > .wd-filter-value-editor--with-btn {
  display: flex;
  margin-top: 5px;
}
.wd-filter-value .wd-filter-value-editor__input {
  flex: 1 1 100%;
}

.wd-filter-value--between > .wd-filter-value {
  margin-bottom: 5px;
}

.wd-edit-param-panel, .wd-filters-panel {
  width: 100%;
  height: 100%;
}

.wd-filters-panel .wd-filters {
  margin: 0 15px;
}

.wd-edit-param-panel .wd-parameter-editor {
  margin: 0 15px;
}

.wd-filters-btn {
  border-radius: 0;
}

.wd-icon-rdlx-adorner-move {
  width: 11px;
}
.wd-icon-rdlx-adorner-move > svg {
  width: 11px !important;
}

.wd-if-rdlx-c-field {
  fill: var(--cui-accent);
}

.wd-if-rdlx-c-parameter {
  fill: #70087d;
}

.wd-if-rdlx-c-expression {
  fill: #dfc40f;
}

.wd-if-rdlx-c-dark {
  fill: #333;
}

.wd-if-rdlx-c-light {
  fill: #fff;
}

.wd-svg {
  display: flex;
  height: 100%;
  background-repeat: no-repeat;
  background-position: 8px 7px;
  align-items: center;
  justify-content: center;
}

.gc-dd-menu__item .wd-svg {
  background-position: 0 7px;
}
.gc-dd-menu__item.gc-size-sm .wd-svg {
  width: 20px;
  background-position: 0 3px;
}
.gc-dd-menu__item.gc-size-lg .wd-svg {
  background-position: 0 12px;
}

.wd-expression-editor__fields-container .title > i.wd-svg {
  display: flex;
  opacity: 0.4;
}

.wizard-body .layout-container .layout-grid .layout-row-column-swap .wd-svg {
  width: 14px;
  height: 20px;
}
.wizard-body .layout-container .layout-grid .layout-cell .ar-member .delete .wd-svg {
  display: flex;
}
.wizard-body .layout-container .layout-grid .layout-cell .ar-member .delete .wd-svg > svg {
  width: 14px;
  height: 20px;
}
.wizard-body .layout-container .layout-grid .layout-cell .ar-member .action .dropdown-toggle.btn.btn-default .wd-svg {
  display: flex;
}
.wizard-body .layout-container .layout-grid .layout-cell .ar-member .action .dropdown-toggle.btn.btn-default .wd-svg > svg {
  width: 16px;
  height: 20px;
}

.ar-data-field-picker-dropdown-search .wd-svg {
  display: flex;
}
.ar-data-field-picker-dropdown-search .wd-svg > svg {
  width: 20px;
}

.ar-semantic-data-field-picker-entity-title .wd-svg > svg {
  width: 15px;
}

.ar-barcode-glyph {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  pointer-events: all;
}
.ar-barcode-glyph .ar-barcodejs {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
  padding: 0;
  pointer-events: none;
}
.ar-barcode-glyph .ar-barcodejs .ar-barcodejs-component {
  position: absolute;
  display: block;
}
.ar-barcode-glyph .ar-barcodejs .ar-barcodejs-component svg {
  display: block;
}
.ar-barcode-glyph .ar-barcodejs-error {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 100%;
  text-align: center;
  word-break: break-word;
  pointer-events: none;
  font-size: 12px;
  align-items: center;
  justify-content: center;
}

.ar-barcode-preview-stub-label {
  position: absolute;
  overflow: hidden;
  width: 100%;
  height: 100%;
  padding: 2pt;
  pointer-events: none;
  color: var(--cui-text);
  border: none;
  background-color: var(--cui-bg-panels);
  font-size: 8pt;
  font-weight: bold;
}

.ar-barcode-model-dumper {
  position: absolute;
  bottom: 0;
  left: -23px;
  width: 19px;
  cursor: pointer;
  text-align: center;
  pointer-events: all;
  border: 1px solid var(--cui-bg-panels-border);
  background-color: var(--cui-bg-panels);
  line-height: 18px;
}
.ar-barcode-model-dumper > i {
  pointer-events: none;
}

.ar-banded-list-glyph {
  width: 100%;
  height: 100%;
  background: white;
}
.ar-banded-list-glyph table {
  position: absolute;
  table-layout: fixed;
  border-collapse: collapse;
  border-style: none;
}

.ar-banded-list {
  border-spacing: 0;
  pointer-events: all;
  border-collapse: collapse;
}
.ar-banded-list td .ar-banded-list-section {
  position: relative;
  width: 100%;
  height: 100%;
}
.ar-banded-list td .ar-banded-list-borders {
  position: absolute;
}

.banded-list-row-adorner {
  position: absolute;
  z-index: 13;
  margin-left: -24px;
  table-layout: fixed;
  border-spacing: 0;
  pointer-events: none;
  border-collapse: collapse;
}
.banded-list-row-adorner__row {
  position: relative;
  padding: 0;
  border: 0;
}
.banded-list-row-adorner__row td {
  position: relative;
  width: 18px;
}
.banded-list-row-adorner__row-header {
  width: 100%;
  height: 100%;
  pointer-events: all;
  border: 1px solid var(--cui-bg-panels-border);
  background-color: var(--cui-bg-panels);
}
.banded-list-row-adorner__row-label {
  position: absolute;
  top: calc(50% - 18px / 2);
  display: flex;
  width: 100%;
  color: var(--cui-text);
  justify-items: center;
  align-items: center;
}
.banded-list-row-adorner__row.selected .banded-list-row-adorner__row-header {
  background-color: var(--cui-bg-panels-border);
}
.banded-list-row-adorner__row:nth-child(2) .table-adorner-row-header {
  border-radius: 4px 4px 0 0;
}
.banded-list-row-adorner__row:last-child .table-adorner-row-header {
  border-radius: 0 0 4px 4px;
}

.banded-list-adorner-spot {
  position: absolute;
  z-index: 13;
  top: calc(-8px / 2 + 100%);
  bottom: -4px;
  left: 0;
  width: 100%;
  pointer-events: all;
}
.banded-list-adorner-spot__add {
  position: absolute;
  z-index: 13;
  left: -20px;
  display: none;
  width: 100%;
  height: 100%;
}
.banded-list-adorner-spot__add-deco {
  position: absolute;
  top: calc(50% - 3px / 2);
  left: 15px;
  width: 29px;
  height: 3px;
  background-color: var(--cui-accent);
}
.banded-list-adorner-spot__add-deco-area {
  position: relative;
  width: 100%;
  height: 100%;
}
.banded-list-adorner-spot__add-deco-area div {
  position: absolute;
  left: 100%;
  height: 100%;
  background-color: var(--cui-accent);
}
.banded-list-adorner-spot:hover .banded-list-adorner-spot__add {
  display: block;
}
.banded-list-adorner-spot__grip {
  position: absolute;
  z-index: 13;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  cursor: row-resize;
}

.banded-list-adorner-move-grip {
  position: absolute;
  z-index: 13;
  top: -24px;
  left: -24px;
  display: flex;
  width: 18px;
  height: 18px;
  cursor: pointer;
  pointer-events: all;
  border: 1px solid var(--cui-bg-panels-border);
  border-radius: 50%;
  background-color: var(--cui-bg-panels);
  line-height: 15px;
  justify-content: center;
  align-items: center;
}
.banded-list-adorner-move-grip > .gc-icon {
  pointer-events: none;
}

.banded-list-adorner .ar-adorner-panel {
  position: absolute;
  top: 0;
  left: calc(100% + 5px);
  width: 200px;
  height: 100%;
  min-height: 120px;
}

.banded-list-band-adorner {
  position: absolute;
  z-index: 13;
  border-spacing: 0;
  pointer-events: none;
  border-collapse: collapse;
}
.banded-list-band-adorner td {
  position: relative;
  z-index: 1313;
  padding: 0;
  border: 0;
  border-width: 2px;
  border-style: none;
  border-color: var(--cui-accent);
}
.banded-list-band-adorner td.top-border {
  border-top-style: solid;
}
.banded-list-band-adorner td.bottom-border {
  border-bottom-style: solid;
}
.banded-list-band-adorner td.left-border {
  border-left-style: solid;
}
.banded-list-band-adorner td.right-border {
  border-right-style: solid;
}
.banded-list-band-adorner td.selected {
  background-color: rgba(0, 0, 0, 0.125);
}
.banded-list-band-adorner td.primary {
  background-color: transparent;
}

.ar-bullet-adorner {
  width: 100%;
  height: 100%;
}

.ar-bullet-adorner .ar-bullet-adorner-element {
  position: absolute;
  overflow: hidden;
  width: 100%;
  height: 30px;
  padding: 1pt;
  cursor: default;
  white-space: nowrap;
  color: #8e8e8e;
  border-style: solid;
  border-color: #f3f3f3;
  background-color: white;
  font-family: Arial;
  font-size: 10pt;
}

.ar-bullet-adorner-element.ar-adorner-value {
  top: -33px;
  display: flex;
}
.ar-bullet-adorner-element.ar-adorner-value > span {
  flex: 1 1 100px;
  text-align: center;
  line-height: 17pt;
}

.ar-bullet-adorner-element.ar-adorner-targetValue {
  bottom: -33px;
  display: flex;
}
.ar-bullet-adorner-element.ar-adorner-targetValue > span {
  flex: 1 1 100px;
  text-align: center;
  line-height: 17pt;
}

.ar-adorner-bullet-val {
  top: 1pt;
  left: 1pt;
  overflow: hidden;
  flex: 0.1 1 auto;
  max-width: 50%;
  text-align: center;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: black;
  color: #797979;
  border-width: thin;
  border-style: solid;
  border-color: #d8d8d8;
  background-color: #eceeef;
  font-size: 8pt;
  line-height: 14pt;
}

.ar-bullet-adorner-element.ar-drop-target {
  pointer-events: all;
  opacity: 1;
}

.ar-bullet-adorner-element.ar-drop-target.can {
  opacity: 0.4;
  background-color: #eaeaea;
}

.ar-bullet-adorner-element.ar-drop-target.over {
  opacity: 0.9;
  background-color: #5f5f5f;
}

.ar-bullet-glyph {
  overflow: hidden;
  width: 100%;
  height: 100%;
  pointer-events: all;
  color: black;
  border: none;
  border-width: thin;
  border-style: solid;
  border-color: #e0e0e0;
}
.ar-bullet-glyph > div {
  height: 100%;
}

.ar-bullet-container {
  pointer-events: none;
}
.ar-bullet-container .ar-bullet-container-vertical {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;
  flex-wrap: nowrap;
}
.ar-bullet-container .ar-bullet-vertical-scale {
  position: relative;
  z-index: 1;
  width: auto;
  height: 100%;
}
.ar-bullet-container .ar-bullet-vertical-graph {
  position: relative;
  overflow: hidden;
  width: 100%;
  border: none;
  background-color: #d6d6d6;
}
.ar-bullet-container .ar-bullet-graph {
  position: relative;
  overflow: hidden;
  width: 100%;
  border: none;
  background-color: #d6d6d6;
}
.ar-bullet-container .ar-bullet-range1 {
  position: absolute;
  background-color: #5f5f5f;
}
.ar-bullet-container .ar-bullet-range2 {
  position: absolute;
  background-color: darkgrey;
}

.ar-chart-adorner-pane {
  position: absolute;
  overflow: hidden;
  width: 100%;
  height: 70px;
  cursor: default;
  pointer-events: all;
  border-radius: 4px;
  background-color: var(--cui-bg-panels);
  box-shadow: 1px 1px 5px 0 rgba(0, 0, 0, 0.1);
}

.ar-chart-adorner-pane__heading {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 30px;
  padding: 0 5px;
  align-items: center;
}
.ar-chart-adorner-pane__heading .gc-icon {
  margin-right: 5px;
}
.ar-chart-adorner-pane__content {
  display: block;
  overflow: hidden;
  width: 100%;
  height: 40px;
  padding: 4px;
}

.ar-chart-adorner-pane--top {
  top: 0;
  left: 0;
  transform: translateY(-75px);
}
.ar-chart-adorner-pane--bottom {
  bottom: 0;
  left: 0;
  transform: translateY(75px);
}
.ar-chart-adorner-pane--right {
  top: 0;
  right: 0;
  display: grid;
  width: 150px;
  height: unset;
  min-height: 100%;
  transform: translateX(155px);
  grid-template-rows: min-content 1fr;
}
.ar-chart-adorner-pane--right .ar-chart-adorner-pane__content {
  height: unset;
  padding: 0;
}

.ar-chart-adorner {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.ar-chart-adorner-pane__item {
  display: inline-block;
  overflow: hidden;
  min-width: 10px;
  height: 30px;
  margin-right: 5px;
  padding: 0 5px;
  user-select: none;
  transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out;
  white-space: nowrap;
  text-overflow: ellipsis;
  border-radius: 4px;
  background-color: var(--cui-btn-bg);
  font-family: var(--cui-text-family);
  font-size: var(--cui-text-size);
  line-height: 30px;
}
.ar-chart-adorner-pane__item:hover {
  background-color: var(--cui-btn-bg-hover);
}
.ar-chart-adorner-pane__item--selected {
  color: var(--cui-contrast);
  background-color: var(--cui-accent);
}
.ar-chart-adorner-pane__item--selected:hover {
  background-color: var(--cui-accent-hover);
}

.ar-chart-drop-target {
  display: flex;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}
.ar-chart-drop-target--empty {
  overflow: hidden;
  text-align: center;
  color: var(--cui-text-semi-40);
  font-size: var(--cui-text-size);
  align-items: center;
  justify-content: center;
}
.ar-chart-drop-target.wd-drop-area--can-drop li {
  color: var(--cui-contrast-text);
  background-color: var(--cui-accent);
}
.ar-chart-drop-target.wd-drop-area--hover li {
  background-color: var(--cui-accent-hover);
}

.ar-chart-adorner--gauge .ar-chart-adorner-pane--right .ar-chart-adorner-pane__content {
  padding: 4px;
}
.ar-chart-adorner--gauge .ar-chart-adorner-pane--right .ar-chart-drop-target {
  display: grid;
  align-content: start;
  gap: 5px;
}
.ar-chart-adorner--gauge .ar-chart-adorner-pane--right .ar-chart-adorner-pane__item {
  margin-right: 0;
}

.ar-chart-move-adorner {
  position: absolute;
  top: 0;
  left: -29px;
  width: 24px;
  height: 24px;
  cursor: pointer;
  pointer-events: all;
  border-radius: 4px;
  background-color: var(--cui-bg-panels);
  box-shadow: 1px 1px 5px 0 rgba(0, 0, 0, 0.1);
}
.ar-chart-move-adorner > .gc-icon {
  pointer-events: none;
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
}
.ar-chart-move-adorner > .gc-icon > svg {
  width: 16px;
  height: 16px;
}

.chart-encodings-panel-group:hover {
  background-color: var(--cui-btn-bg-hover);
}
.chart-encodings-panel-group__header {
  position: relative;
  display: flex;
  overflow: hidden;
  height: 35px;
  padding: 5px 5px 0 2px;
  align-items: center;
}
.chart-encodings-panel-group__header::before {
  position: absolute;
  top: 0;
  left: 0;
  width: calc(100% - 10px);
  height: 1px;
  margin: 0 5px;
  content: "";
  opacity: 0.62;
  background-color: var(--cui-bg-panels-border);
}
.chart-encodings-panel-group__header > span {
  flex: 1 1 auto;
  width: 100%;
  font-size: var(--cui-text-size);
}
.chart-encodings-panel-group__header .gc-icon {
  flex: 0 0 auto;
  transition: transform 0.2s ease-in-out;
  color: var(--cui-accent);
  display: flex;
  width: 30px;
  height: 30px;
  align-items: center;
  justify-content: center;
}
.chart-encodings-panel-group__header .gc-icon > svg {
  width: 20px;
  height: 20px;
}

.chart-encodings-panel-group__content {
  overflow: hidden;
  height: 0;
  padding: 4px;
}
.chart-encodings-panel-group__content .ar-chart-drop-target {
  flex-direction: column;
}
.chart-encodings-panel-group__content .ar-chart-adorner-pane__item {
  width: 100%;
  margin: 0 0 5px 0;
}

.chart-encodings-panel-group {
  overflow: hidden;
  flex: 0 0 auto;
  width: 100%;
  height: 40px;
  min-height: 40px;
}

.chart-encodings-panel-group--expanded {
  display: grid;
  flex: 1 1 auto;
  height: unset;
  min-height: 90px;
  grid-template-rows: min-content 1fr;
}
.chart-encodings-panel-group--expanded:hover {
  background-color: transparent;
}
.chart-encodings-panel-group--expanded .chart-encodings-panel-group__header .gc-icon {
  transform: rotate(-90deg);
}
.chart-encodings-panel-group--expanded .chart-encodings-panel-group__content {
  height: unset;
}
.chart-encodings-panel-group--expanded .chart-encodings-panel-group__content .ar-chart-adorner-pane__item {
  flex-shrink: 0;
}

.chart-encodings-panel {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding-top: 5px;
  justify-content: stretch;
}

.ar-chart {
  position: absolute;
  pointer-events: all;
  width: 100%;
  height: 100%;
}

.ar-chart-error {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 100%;
  text-align: center;
  word-break: break-word;
  pointer-events: none;
  background-color: white;
  font-size: 12px;
  align-items: center;
  justify-content: center;
}

.ar-chart-dvchart {
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.ar-chart-clicker {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
}

.ar-chart-decoration-view {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}
.ar-chart-decoration-view .selection-decoration {
  position: absolute;
  pointer-events: none;
  border: 1px dashed var(--cui-accent);
}

.ar-chart-overlay {
  position: absolute;
  top: 0;
  left: 0;
  display: none;
  width: 100%;
  height: 100%;
  pointer-events: none;
  opacity: 0.7;
  background-color: white;
}
.ar-chart-overlay.visible {
  display: block;
}

.ar-chart-model-dumper {
  position: absolute;
  bottom: 0;
  left: -23px;
  width: 19px;
  cursor: pointer;
  text-align: center;
  pointer-events: all;
  border: 1px solid var(--cui-bg-panels-border);
  background-color: var(--cui-bg-panels);
  line-height: 18px;
}
.ar-chart-model-dumper > i {
  pointer-events: none;
}

.ar-checkbox {
  position: absolute;
  overflow: hidden;
  width: 100%;
  height: 100%;
  pointer-events: all;
  border: none;
}
.ar-checkbox * {
  pointer-events: none;
}
.ar-checkbox .ar-checkbox-inner {
  position: relative;
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 100%;
  text-decoration: inherit;
  -webkit-text-decoration: inherit;
  /* Safari v16+ */
  align-items: center;
}
.ar-checkbox .ar-checkbox-inner.selected .ar-checkbox-check {
  pointer-events: all;
}
.ar-checkbox .ar-checkbox-inner.selected .ar-checkbox-text-wrapper {
  pointer-events: all;
}
.ar-checkbox .ar-checkbox-check {
  position: absolute;
  display: inline-block;
  box-sizing: border-box;
  width: 16px;
  height: 16px;
  border: solid black 1px;
  font-size: 14px;
}
.ar-checkbox .ar-checkbox-check.expression {
  background-color: black;
}
.ar-checkbox .ar-checkbox-text-wrapper {
  width: 100%;
  text-decoration: inherit;
  -webkit-text-decoration: inherit;
  /* Safari v16+ */
}
.ar-checkbox .no-wrap {
  white-space: nowrap;
}
.ar-checkbox .word-wrap {
  word-wrap: break-word;
}
.ar-checkbox .char-wrap {
  word-break: break-all;
}
.ar-checkbox.top .ar-checkbox-check {
  top: 0;
}
.ar-checkbox.top .ar-checkbox-inner {
  padding-top: 17px;
  align-items: flex-start;
}
.ar-checkbox.middle .ar-checkbox-check {
  top: calc(50% - 16px/2);
}
.ar-checkbox.middle .ar-checkbox-inner {
  align-items: center;
}
.ar-checkbox.bottom .ar-checkbox-check {
  bottom: 0;
}
.ar-checkbox.bottom .ar-checkbox-inner {
  padding-bottom: 17px;
  align-items: flex-end;
}
.ar-checkbox.left .ar-checkbox-check {
  left: 0;
}
.ar-checkbox.left .ar-checkbox-inner {
  padding-left: 17px;
}
.ar-checkbox.left .ar-checkbox-text-wrapper {
  text-align: left;
}
.ar-checkbox.center .ar-checkbox-check {
  left: calc(50% - 16px/2);
}
.ar-checkbox.center .ar-checkbox-text-wrapper {
  text-align: center;
}
.ar-checkbox.right .ar-checkbox-check {
  right: 0;
}
.ar-checkbox.right .ar-checkbox-inner {
  padding-right: 17px;
}
.ar-checkbox.right .ar-checkbox-text-wrapper {
  text-align: right;
}

.ar-checkbox .ar-checkbox__textarea-area {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.ar-checkbox-check .wd-svg > svg {
  width: 12px;
}

.ar-contentplaceholder {
  position: absolute;
  display: flex;
  width: 100%;
  height: 100%;
  pointer-events: all;
  color: var(--cui-text-semi-60);
  border: none;
  background-color: white;
  font-size: 8pt;
  align-items: center;
  justify-content: center;
}
.ar-contentplaceholder__text {
  position: absolute;
  overflow: hidden;
  text-align: center;
  text-overflow: ellipsis;
  pointer-events: none;
  color: #5c5c5c;
}

.ar-default-report-item {
  overflow: hidden;
  width: 100%;
  height: 100%;
  padding: 2px;
  user-select: none;
  opacity: 0.6;
  border: 1px dotted rgba(170, 170, 170, 0.6);
  font-size: var(--cui-text-size-sm);
}

.placeholder-grip {
  position: absolute;
  width: 9pt;
  height: 9pt;
  cursor: pointer;
  pointer-events: all;
  border: 1px solid #838383;
  border-radius: 50%;
  background-color: #969696;
}
.placeholder-grip.fpl-e {
  top: calc(50% - 9pt / 2);
  right: -15pt;
  cursor: w-resize;
}
.placeholder-grip.fpl-s {
  bottom: -15pt;
  left: calc(50% - 9pt / 2);
  cursor: s-resize;
}
.placeholder-grip.fpl-se {
  right: -15pt;
  bottom: -15pt;
  cursor: se-resize;
}

.ar-placeholder {
  position: relative;
  width: 100%;
  height: 100%;
  pointer-events: all;
}
.ar-placeholder .placeholder-border {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
  border: 1px solid var(--cui-accent-semi-10);
  background: repeating-linear-gradient(-45deg, transparent, transparent 4px, var(--cui-accent-semi-10) 4px, var(--cui-accent-semi-10) 6px);
}

.ar-report.fpl {
  height: calc(100% - 31px);
}

.ar-formatted-text {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
  word-wrap: break-word;
  pointer-events: all;
}
.ar-formatted-text .preview {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}
.ar-formatted-text .preview.error {
  padding: 2px;
  color: var(--cui-accent-error);
  background-color: #d87979;
  font-size: 12px;
}

.ar-image-glyph {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: all;
}
.ar-image-glyph .image-container {
  overflow: hidden;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background-repeat: no-repeat;
}
.ar-image-glyph span {
  display: inline-block;
  overflow: hidden;
  width: 100%;
  max-height: 100%;
  padding: 2pt;
  word-wrap: break-word;
  pointer-events: none;
  font-size: 10pt;
}
.ar-image-glyph .fit-proportional {
  background-attachment: local;
  background-size: contain;
}
.ar-image-glyph .fit {
  background-size: 100% 100%;
}

.rdlx-input-field-text {
  position: absolute;
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 100%;
  word-wrap: break-word;
  pointer-events: all;
  border: none;
  align-items: center;
}
.rdlx-input-field-text * {
  pointer-events: none;
}
.rdlx-input-field-text--text-wrapper {
  width: 100%;
  text-decoration: inherit;
}

.rdlx-input-field-checkbox {
  position: absolute;
  overflow: hidden;
  width: 100%;
  height: 100%;
  pointer-events: all;
  border: none;
}
.rdlx-input-field-checkbox * {
  pointer-events: none;
}
.rdlx-input-field-checkbox__icon {
  position: relative;
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 100%;
  text-align: center;
  text-decoration: inherit;
  align-items: center;
  justify-content: center;
}
.rdlx-input-field-checkbox .selected {
  pointer-events: all;
}
.rdlx-input-field-checkbox--checked {
  display: inline-block;
  box-sizing: border-box;
  width: 100%;
}
.rdlx-input-field-checkbox--checked.wd-svg {
  display: flex;
}
.rdlx-input-field-checkbox--checked.wd-svg > svg {
  width: 100%;
  height: 100%;
}

.ar-line {
  position: absolute;
  width: 100%;
  height: 100%;
}
.ar-line > div {
  position: absolute;
}
.ar-line * {
  pointer-events: none;
}

.ar-list {
  display: grid;
  width: 100%;
  height: 100%;
  font-size: var(--cui-text-size);
}

.ar-list--rows {
  grid-auto-flow: row;
  grid-auto-rows: 1fr;
}
.ar-list--rows .ar-list__segment {
  border-top-width: 1px;
}

.ar-list--columns {
  grid-auto-flow: column;
  grid-auto-columns: 1fr;
}
.ar-list--columns .ar-list__segment {
  border-left-width: 1px;
}

.ar-list__work-area {
  pointer-events: none;
}

.ar-list__segment {
  display: flex;
  overflow: hidden;
  pointer-events: none;
  color: var(--cui-text-semi-60);
  border-width: 0;
  border-style: dashed;
  border-color: var(--cui-bg-panels-border);
  background-color: var(--cui-bg-panels);
  align-items: center;
  justify-content: center;
}

.ar-list__segment-caption {
  display: grid;
  margin: 0 8px;
  grid-auto-flow: column;
}

.ar-list__segment-caption-start {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.ar-list__segment-caption-end {
  white-space: pre-wrap;
}

.ar-report.msl {
  height: calc(100% - 31px);
}

/* report designer styles */
.ar-report {
  position: relative;
  overflow: auto;
  width: 100%;
  height: 100%;
}
.ar-report .ar-page-margin {
  position: relative;
  width: calc(100% - 64px * 2);
  height: calc(100% - 128px);
  margin: 64px;
}
.ar-report .ar-page {
  display: inline-block;
  position: relative;
  pointer-events: all;
  background-color: white;
  box-shadow: 0 8px 17px 0 var(--cui-accent-semi-20), 0 6px 20px 0 var(--cui-text-semi-20);
}
.ar-report .ar-page > div {
  pointer-events: all;
}
.ar-report .ar-page .ar-page__overlay {
  opacity: 0.62;
  background-color: var(--cui-bg-body);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.ar-report .ar-decorationlayer .section-header {
  position: absolute;
  z-index: 10001;
  overflow: hidden;
  padding: 2px 5px;
  cursor: default;
  transition: all 0.2s ease-in-out;
  white-space: nowrap;
  text-overflow: ellipsis;
  pointer-events: none;
  opacity: 0.5;
  color: #5c5c5c;
  border: 1px solid transparent;
  background-color: transparent;
  font-size: var(--cui-text-size-sm);
}
.ar-report .ar-decorationlayer .section-header__hidden {
  visibility: hidden;
  opacity: 0;
}
.ar-report .ar-decorationlayer .section-header__primary {
  transition: all 0.2s ease-in-out;
  transform: translateY(-100%);
  opacity: 1;
  color: var(--cui-contrast-text);
  border: 1px solid var(--cui-accent);
  background-color: var(--cui-accent);
}
.ar-report .ar-resizeline-vertical {
  position: absolute;
  top: 0;
  width: 6px;
  height: 100%;
  cursor: ew-resize;
  pointer-events: all;
}
.ar-report .ar-resizeline-horizontal {
  position: absolute;
  width: 100%;
  height: 6px;
  cursor: ns-resize;
  pointer-events: all;
}
.ar-report .ar-resizeline-left {
  left: -3px;
}
.ar-report .ar-resizeline-right {
  left: calc(100% - 3px);
}
.ar-report .ar-resizeline-top {
  top: -3px;
}
.ar-report .ar-resizeline-bottom {
  top: calc(100% - 3px);
}
.ar-report .ar-vertical-separator {
  position: absolute;
  top: 0;
  width: 1px;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.19);
}
.ar-report .ar-horizontal-separator {
  position: absolute;
  width: 100%;
  height: 1px;
  background-color: rgba(0, 0, 0, 0.19);
}
.ar-report .ar-page-borders {
  width: calc(100% + 2px);
  height: calc(100% + 2px);
  margin-top: -1px;
  margin-left: -1px;
  border: 1px dashed #5c5c5c;
}

.ar-report::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.ar-report::-webkit-scrollbar-thumb {
  background-color: var(--cui-accent-semi-60);
  border-radius: 15px;
}

.ar-report::-webkit-scrollbar-track {
  background-color: var(--cui-text-semi-10);
}

.ar-report::-webkit-scrollbar-corner {
  background-color: var(--cui-text-semi-10);
}

.ar-partitem {
  display: flex;
  overflow: hidden;
  flex-direction: column;
  width: 100%;
  height: 100%;
}
.ar-partitem--line {
  padding: 2pt;
}
.ar-partitem--report-part-name {
  font-size: medium;
  font-weight: normal;
}
.ar-partitem__border-wrapper {
  position: absolute;
  pointer-events: none;
  overflow: hidden;
}
.ar-partitem__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 1;
}

.fpl .ar-partitem--grow:not(.ar-partitem--line) {
  background: repeating-linear-gradient(-45deg, transparent, transparent 4px, var(--cui-accent-semi-10) 4px, var(--cui-accent-semi-10) 6px);
}

.msl .ar-partitem--grow:not(.ar-partitem--line) {
  background-color: white;
  background-image: radial-gradient(var(--cui-accent) 0.5pt, transparent 0px), radial-gradient(rgba(128, 128, 128, 0.3) 1pt, transparent 0px);
  background-size: 16px 16px;
  background-position: 0 0, 8px 8px;
}

.ar-sel-adorner {
  position: absolute;
  width: calc(100% + 20px);
  height: calc(100% + 20px);
  margin: -10px;
  border: 10px solid transparent;
  /* top grips */
}
.ar-sel-adorner .grip, .ar-sel-adorner .grip-angle {
  position: absolute;
  z-index: 9002;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 10px;
  height: 10px;
  border-radius: 50%;
}
.ar-sel-adorner .grip > div, .ar-sel-adorner .grip-angle > div {
  display: none;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--cui-accent);
}
.ar-sel-adorner .grip {
  border: 1px solid var(--cui-accent);
  background: white;
}
.ar-sel-adorner .grip-angle {
  box-sizing: content-box;
  background: var(--cui-accent);
  width: 6px;
  height: 6px;
  border: 1.5px solid white;
}
.ar-sel-adorner .grip-angle::after {
  content: "";
  position: absolute;
  width: 12px;
  height: 12px;
  background: none;
  border-radius: 50%;
}
.ar-sel-adorner .grip-angle-guide {
  position: absolute;
  width: 2px;
  background: var(--cui-accent);
  opacity: 0.5;
  border-radius: 50%;
}
.ar-sel-adorner .grip-n {
  top: -5px;
}
.ar-sel-adorner .grip-m {
  top: calc(50% - 5px);
}
.ar-sel-adorner .grip-s {
  top: calc(100% - 5px);
}
.ar-sel-adorner .grip-w {
  left: -5px;
}
.ar-sel-adorner .grip-c {
  left: calc(50% - 5px);
}
.ar-sel-adorner .grip-e {
  left: calc(100% - 5px);
}
.ar-sel-adorner .grip-angle-tl {
  top: 0;
  left: 0;
}
.ar-sel-adorner .grip-angle-tr {
  top: 0;
  right: 0;
}
.ar-sel-adorner .grip-angle-bl {
  bottom: 0;
  left: 0;
}
.ar-sel-adorner .grip-angle-br {
  bottom: 0;
  right: 0;
}
.ar-sel-adorner .sel-frame {
  position: absolute;
  z-index: 9000;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  border: 1px solid var(--cui-accent);
}
.ar-sel-adorner .sel-frame__rounding {
  border: none;
}
.ar-sel-adorner .move-grip-solid .overlay {
  position: absolute;
  z-index: 9000;
  cursor: move;
}
.ar-sel-adorner .move-grip-solid .overlay.pos-e {
  top: 0;
  left: -3px;
  width: 6px;
  height: 100%;
  cursor: move;
}
.ar-sel-adorner .move-grip-solid .overlay.pos-n {
  top: -3px;
  left: 0;
  width: 100%;
  height: 6px;
  cursor: move;
}
.ar-sel-adorner .move-grip-solid .overlay.pos-w {
  top: 0;
  right: -3px;
  width: 6px;
  height: 100%;
  cursor: move;
}
.ar-sel-adorner .move-grip-solid .overlay.pos-s {
  bottom: -3px;
  left: 0;
  width: 100%;
  height: 6px;
  cursor: move;
}
.ar-sel-adorner .ar-selection-marker {
  position: absolute;
  z-index: 9002;
  top: -5px;
  right: -5px;
  width: 10px;
  height: 10px;
  transform: rotate(-45deg);
  border: 1px solid var(--cui-accent-semi-40);
  background: white;
}
.ar-sel-adorner .ar-selection-marker > div {
  display: none;
  width: 6px;
  height: 6px;
  margin: 1px;
  background-color: var(--cui-accent);
}
.ar-sel-adorner .ar-selection-marker.primary > div {
  display: block;
}

.shape-rounding {
  opacity: 0.5;
  border-style: dashed !important;
}

.ar-sel-primary .grip > div {
  display: block;
}

.line-move-grip {
  position: absolute;
  z-index: 9001;
  display: block;
  height: 100%;
}
.line-move-grip > div {
  position: absolute;
  z-index: 9001;
  display: block;
  border: 1px solid var(--cui-accent);
}

.ar-line-selection-marker {
  position: absolute;
  z-index: 9002;
  top: calc(50% - 5px);
  left: calc(50% - 5px);
  width: 10px;
  height: 10px;
  transform: rotate(-45deg);
  border: 1px solid var(--cui-accent-semi-40);
  background: white;
}
.ar-line-selection-marker > div {
  display: none;
  width: 6px;
  height: 6px;
  margin: 1px;
  background-color: var(--cui-accent);
}
.ar-line-selection-marker.primary > div {
  display: block;
}

.ar-rectangle-glyph {
  position: relative;
  width: 100%;
  height: 100%;
}
.ar-rectangle-glyph .bg-color, .ar-rectangle-glyph .bg-image, .ar-rectangle-glyph .bg-grid {
  position: absolute;
  top: 0;
  overflow: hidden;
  width: 100%;
  height: 100%;
  pointer-events: none;
}
.ar-rectangle-glyph .ar-rectangle-glyph__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.62;
  background-color: var(--cui-bg-body);
  pointer-events: none;
}

.ar-rectangle-glyph__draggable-area {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: all;
}

.ar-border {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}
.ar-border__primary {
  z-index: 2 !important;
  border: 1px solid var(--cui-accent) !important;
}
.ar-border__secondary {
  z-index: 1 !important;
  border: 1px solid var(--cui-accent-semi-60) !important;
}

.ar-reportitemplace {
  position: absolute;
  text-decoration-skip-ink: none;
}

.ar-decorative-item * {
  pointer-events: none !important;
}

/*fpl items*/
.ar-rectangle-glyph {
  pointer-events: all;
}
.ar-rectangle-glyph .ar-overflowPlaceHolder-glyph {
  position: absolute;
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 100%;
  text-align: center;
  word-wrap: break-word;
  color: gray;
  border: none;
  background-color: white;
  align-items: center;
}
.ar-rectangle-glyph .ar-overflowPlaceHolder-glyph > span {
  width: 100%;
  pointer-events: none;
}

.ar-rich-text {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
  word-wrap: break-word;
  pointer-events: all;
}
.ar-rich-text * {
  pointer-events: none;
}
.ar-rich-text .ar-rich-text-inner {
  position: absolute;
  display: flex;
  overflow: hidden;
}
.ar-rich-text .ar-rich-text-inner .text-wrapper {
  overflow: hidden;
  width: 100%;
  text-decoration: inherit;
}

.ar-corner-adorner {
  position: absolute;
  width: 100%;
  height: 100%;
  border: none;
}
.ar-corner-adorner .grip {
  position: absolute;
  z-index: 9002;
  border: none;
  background: transparent;
}
.ar-corner-adorner .grip > div {
  display: block;
  width: 100%;
  height: 100%;
  transform: rotate(-45deg);
  border: 1px solid #969696;
  border-radius: 0;
  background-color: var(--cui-accent);
}

.ar-corner-preview {
  position: absolute;
  z-index: 9005;
  overflow: hidden;
  width: 100%;
  height: 100%;
  border: 1px solid #969696;
}

.ar-shape {
  position: absolute;
  overflow: visible;
  width: 100%;
  height: 100%;
  pointer-events: all;
}
.ar-shape .ar-shape-border {
  position: absolute;
  overflow: hidden;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background: transparent;
}

.ellipse {
  border-radius: 50%;
}

.ar-sparkline-adorner {
  width: 100%;
  height: 100%;
}

.ar-sparkline-adorner .ar-sparkline-adorner-element {
  position: absolute;
  overflow: hidden;
  width: 100%;
  height: 30px;
  padding: 1pt;
  cursor: default;
  white-space: nowrap;
  color: #8e8e8e;
  border-style: solid;
  border-color: #f7f7f7;
  background-color: white;
  font-size: 10pt;
}

.ar-sparkline-adorner-element.ar-adorner-value {
  top: -33px;
  display: flex;
}
.ar-sparkline-adorner-element.ar-adorner-value > span {
  flex: 1 1 100px;
  text-align: center;
  line-height: 17pt;
}

.ar-sparkline-adorner-element.ar-adorner-groupingExpressions {
  bottom: -33px;
  display: flex;
}
.ar-sparkline-adorner-element.ar-adorner-groupingExpressions > span {
  flex: 1 1 100px;
  text-align: center;
  line-height: 17pt;
}

.ar-sparkline-adorner-val {
  top: 1pt;
  left: 1pt;
  overflow: hidden;
  flex: 0.1 1 auto;
  max-width: 50%;
  text-align: center;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: #757575;
  border-width: thin;
  border-style: solid;
  border-color: #dedede;
  background-color: #eceeef;
  font-size: 8pt;
  line-height: 14pt;
}

.ar-sparkline-adorner-element.ar-drop-target {
  opacity: 1;
}

.ar-sparkline-adorner-element.ar-drop-target.can {
  opacity: 0.4;
  background-color: #eaeaea;
}

.ar-sparkline-adorner-element.ar-drop-target.over {
  opacity: 0.9;
  background-color: #5c5c5c;
}

.ar-sparkline-glyph {
  pointer-events: all;
  position: absolute;
  width: 100%;
  height: 100%;
  border: none;
  overflow: hidden;
  padding: 2pt;
}

.ar-subreport {
  position: absolute;
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 100%;
  padding: 2pt;
  pointer-events: all;
  color: var(--cui-text);
  border: none;
  background-color: var(--cui-bg-panels);
  font-size: 14pt;
  font-weight: bold;
  align-items: center;
  justify-content: center;
}
.ar-subreport > span {
  overflow: hidden;
  text-overflow: ellipsis;
  pointer-events: none;
}

.table-adorner-cells-selection {
  position: absolute;
  border-spacing: 0;
  pointer-events: none;
  border-collapse: collapse;
}
.table-adorner-cells-selection .table-adorner-cell {
  position: relative;
  padding: 0;
  border: 0;
  border-width: 2px;
  border-style: none;
  border-color: var(--cui-accent);
}
.table-adorner-cells-selection .table-adorner-cell > div {
  position: relative;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.table-adorner-cells-selection .table-adorner-cell .table-adorner-cell-area {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
}
.table-adorner-cells-selection .table-adorner-cell.table-adorner-cell-selected .table-adorner-cell-area {
  background-color: rgba(0, 0, 0, 0.125);
}
.table-adorner-cells-selection .table-adorner-cell.table-adorner-cell-selected.primary .table-adorner-cell-area {
  background-color: transparent;
}
.table-adorner-cells-selection .table-adorner-cell .table-adorner-cell-border {
  position: absolute;
  z-index: 1313;
  top: -1px;
  left: -1px;
  width: calc(100% + 2px);
  height: calc(100% + 2px);
  border-width: 2px;
  border-style: none;
  border-color: var(--cui-accent);
}
.table-adorner-cells-selection .table-adorner-cell .table-adorner-cell-border.top {
  border-top-style: solid;
}
.table-adorner-cells-selection .table-adorner-cell .table-adorner-cell-border.bottom {
  border-bottom-style: solid;
}
.table-adorner-cells-selection .table-adorner-cell .table-adorner-cell-border.left {
  border-left-style: solid;
}
.table-adorner-cells-selection .table-adorner-cell .table-adorner-cell-border.right {
  border-right-style: solid;
}

.table-adorner-rows {
  position: absolute;
  z-index: 13;
  margin-left: -24px;
  border-spacing: 0;
  pointer-events: none;
  border-collapse: collapse;
}
.table-adorner-rows .table-adorner-row {
  position: relative;
  padding: 0;
  border: 0;
}
.table-adorner-rows .table-adorner-row td {
  position: relative;
  width: 20px;
  padding: 0;
}
.table-adorner-rows .table-adorner-row td .table-adorner-row-header {
  width: 100%;
  height: 100%;
  pointer-events: all;
  border: 1px solid var(--cui-bg-panels-border);
  background-color: var(--cui-bg-panels);
}
.table-adorner-rows .table-adorner-row td .table-adorner-row-label {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  width: 100%;
  height: 100%;
  color: var(--cui-text);
  justify-content: center;
  align-items: center;
}
.table-adorner-rows .table-adorner-row.selected .table-adorner-row-header {
  background-color: var(--cui-bg-body);
}
.table-adorner-rows .table-adorner-row:nth-child(2) .table-adorner-row-header {
  border-radius: 4px 4px 0 0;
}
.table-adorner-rows .table-adorner-row:last-child .table-adorner-row-header {
  border-radius: 0 0 4px 4px;
}

.table-adorner-columns {
  position: absolute;
  z-index: 13;
  margin-top: -24px;
  border-spacing: 0;
  pointer-events: none;
  border-collapse: collapse;
}
.table-adorner-columns .table-adorner-column {
  position: relative;
  height: 20px;
  padding: 0;
  border: 0;
}
.table-adorner-columns .table-adorner-column .table-adorner-column-header {
  width: 100%;
  height: 100%;
  pointer-events: all;
  border: 1px solid var(--cui-bg-panels-border);
  background-color: var(--cui-bg-panels);
}
.table-adorner-columns .table-adorner-column.selected .table-adorner-column-header {
  background-color: var(--cui-bg-body);
}
.table-adorner-columns .table-adorner-column:nth-child(2) .table-adorner-column-header {
  border-radius: 4px 0 0 4px;
}
.table-adorner-columns .table-adorner-column:last-child .table-adorner-column-header {
  border-radius: 0 4px 4px 0;
}

.table-adorner-spot {
  position: absolute;
  z-index: 13;
  pointer-events: all;
}
.table-adorner-spot.row-spot {
  top: calc(-8px / 2 + 100%);
  bottom: -4px;
  left: 0;
  width: 100%;
}
.table-adorner-spot.row-spot .table-adorner-spot-add {
  left: -20px;
}
.table-adorner-spot.row-spot .table-adorner-spot-add .table-adorner-spot-add-button {
  top: calc(50% - 15px / 2);
}
.table-adorner-spot.row-spot .table-adorner-spot-add .table-adorner-spot-add-deco {
  top: calc(50% - 3px / 2);
  left: 15px;
  width: 29px;
  height: 3px;
}
.table-adorner-spot.row-spot .table-adorner-spot-add .table-adorner-spot-add-deco .table-adorner-spot-add-deco-area div {
  left: 100%;
  height: 100%;
}
.table-adorner-spot.row-spot .table-adorner-spot-add-grip {
  cursor: row-resize;
}
.table-adorner-spot.column-spot {
  top: 0;
  right: -4px;
  width: 8px;
  height: 100%;
}
.table-adorner-spot.column-spot .table-adorner-spot-add {
  top: -20px;
}
.table-adorner-spot.column-spot .table-adorner-spot-add .table-adorner-spot-add-button {
  left: calc(50% - 15px / 2);
}
.table-adorner-spot.column-spot .table-adorner-spot-add .table-adorner-spot-add-deco {
  top: 15px;
  left: calc(50% - 3px / 2);
  width: 3px;
  height: 29px;
}
.table-adorner-spot.column-spot .table-adorner-spot-add .table-adorner-spot-add-deco .table-adorner-spot-add-deco-area div {
  top: 100%;
  width: 100%;
}
.table-adorner-spot.column-spot .table-adorner-spot-add-grip {
  cursor: col-resize;
}
.table-adorner-spot .table-adorner-spot-add {
  position: absolute;
  z-index: 13;
  display: none;
  width: 100%;
  height: 100%;
}
.table-adorner-spot .table-adorner-spot-add .table-adorner-spot-add-button {
  position: absolute;
  width: 15px;
  height: 15px;
  cursor: pointer;
  border-radius: 50%;
  background-color: var(--cui-accent);
}
.table-adorner-spot .table-adorner-spot-add .table-adorner-spot-add-button i {
  color: white;
}
.table-adorner-spot .table-adorner-spot-add .table-adorner-spot-add-button i > svg {
  width: 12px !important;
}
.table-adorner-spot .table-adorner-spot-add .table-adorner-spot-add-deco {
  position: absolute;
  background-color: var(--cui-accent);
}
.table-adorner-spot .table-adorner-spot-add .table-adorner-spot-add-deco .table-adorner-spot-add-deco-area {
  position: relative;
  width: 100%;
  height: 100%;
}
.table-adorner-spot .table-adorner-spot-add .table-adorner-spot-add-deco .table-adorner-spot-add-deco-area div {
  position: absolute;
  background-color: var(--cui-accent);
}
.table-adorner-spot:hover .table-adorner-spot-add {
  display: block;
}
.table-adorner-spot .table-adorner-spot-add-grip {
  position: absolute;
  z-index: 13;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.table-adorner-move-grip {
  position: absolute;
  z-index: 13;
  top: -24px;
  left: -24px;
  display: flex;
  width: 20px;
  height: 20px;
  cursor: pointer;
  pointer-events: all;
  border: 1px solid var(--cui-bg-panels-border);
  border-radius: 50%;
  background-color: var(--cui-bg-panels);
  justify-content: center;
  align-items: center;
}
.table-adorner-move-grip > .gc-icon {
  pointer-events: none;
}

.table-adorner .ar-adorner-panel {
  position: absolute;
  top: 0;
  left: calc(100% + 5px);
  width: 200px;
  height: 100%;
  min-height: 120px;
}

.table-adorner-icon-group {
  display: flex;
  width: 16px;
  height: 16px;
  white-space: nowrap;
  font-size: var(--cui-text-size-sm);
  line-height: 16px;
  align-items: center;
  justify-content: left;
}
.table-adorner-icon-group > .gc-icon {
  overflow: hidden;
  width: 7px;
}

.wd-table-overlay {
  position: absolute;
  z-index: 9002;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.wd-column-drop {
  position: absolute;
  bottom: 0;
  left: 0;
  display: none;
  width: 10px;
  height: 100%;
  transform: translateX(-50%);
  pointer-events: all;
}
.wd-column-drop:after {
  position: absolute;
  bottom: 0;
  left: 50%;
  display: none;
  width: 3px;
  height: calc(100% + 30px);
  content: "";
  transform: translateX(-50%);
  background-color: var(--cui-accent);
}
.wd-column-drop__icon {
  position: absolute;
  bottom: 100%;
  left: 50%;
  display: none;
  width: 15px;
  height: 15px;
  transform: translate(-50%, -29px);
  color: var(--cui-contrast);
  border-radius: 50%;
  background-color: var(--cui-accent);
}
.wd-column-drop--active {
  display: block;
}
.wd-column-drop--hover {
  background-color: var(--cui-contrast-semi-10);
}
.wd-column-drop--hover .wd-column-drop__icon, .wd-column-drop--hover:after {
  display: block;
}
.wd-column-drop--fill-column {
  height: calc(100% + 24px);
  transform: none;
}
.wd-column-drop--drop-before .wd-column-drop__icon {
  display: none;
}
.wd-column-drop--drop-before.wd-column-drop--hover:after {
  left: 0;
  display: block;
  height: 100%;
}
.wd-column-drop--drop-after .wd-column-drop__icon {
  display: none;
}
.wd-column-drop--drop-after.wd-column-drop--hover:after {
  right: 0;
  left: auto;
  display: block;
  height: 100%;
  transform: translateX(50%);
}

.ar-table-glyph {
  width: 100%;
  height: 100%;
}

.ar-table {
  border-spacing: 0;
  pointer-events: all;
  border-collapse: collapse;
}
.ar-table td {
  padding: 0;
}

.ar-tablix-cell > div {
  position: relative;
  width: 100%;
  height: 100%;
}
.ar-tablix-cell > div > div:not(.ar-item):not(.ar-rectangle-glyph) {
  position: absolute;
  pointer-events: none;
}

.ar-tablix-cell-hover {
  background-color: var(--cui-accent-hover) !important;
}

.ar-tablix-adorner {
  position: absolute;
  z-index: 13;
  border-spacing: 0;
  pointer-events: none;
  border: 0;
}
.ar-tablix-adorner .ar-tablix-adorner-table {
  box-sizing: border-box;
  table-layout: fixed;
  border-spacing: 0;
  color: var(--cui-text);
  border-collapse: separate;
}
.ar-tablix-adorner .ar-tablix-adorner-table tr > td {
  padding: 0;
}
.ar-tablix-adorner .pointer-grip {
  position: absolute;
  top: 0;
  left: 0;
  width: 18px;
  height: 18px;
  cursor: pointer;
  pointer-events: all;
}
.ar-tablix-adorner .ar-tablix-adorner-resize {
  border-right: 2px solid var(--cui-accent);
  border-bottom: 2px solid var(--cui-accent);
}
.ar-tablix-adorner .ar-tablix-sections {
  position: absolute;
}
.ar-tablix-adorner .ar-tablix-sections > div {
  position: absolute;
  border-width: 0;
  border-style: dashed;
  border-color: #c2c2c2;
}
.ar-tablix-adorner .ar-tablix-sections > div.ar-tablix-section-v {
  width: 5px;
  border-right-width: 1px;
  border-left-width: 1px;
}
.ar-tablix-adorner .ar-tablix-sections > div.ar-tablix-section-h {
  height: 5px;
  border-top-width: 1px;
  border-bottom-width: 1px;
}

.ar-tablix-selection {
  position: absolute;
  margin: 0;
  padding: 0;
  border-spacing: 0;
  border: 0;
}

.ar-tablix-groups {
  position: absolute;
  pointer-events: none;
}
.ar-tablix-groups .ar-tablix-col-group {
  position: absolute;
  height: 10px;
  border-style: solid;
  border-color: #c2c2c2;
  border-top-width: 3px;
  border-right-width: 3px;
  border-bottom-width: 0;
  border-left-width: 3px;
}
.ar-tablix-groups .ar-tablix-row-group {
  position: absolute;
  width: 10px;
  border-style: solid;
  border-color: #c2c2c2;
  border-top-width: 3px;
  border-right-width: 0;
  border-bottom-width: 3px;
  border-left-width: 3px;
}
.ar-tablix-groups .ar-tablix-col-group.highlighted, .ar-tablix-groups .ar-tablix-row-group.highlighted {
  border-color: var(--cui-accent-hover);
}

.ar-tablix-selection-selected:not(.ar-tablix-selection-primary) {
  background-color: rgba(0, 0, 0, 0.125);
}

.ar-tablix-selection-selected.ar-tablix-selection-primary {
  background-color: transparent;
}

.ar-tablix-selection-cell {
  padding: 0;
  border: 0;
}
.ar-tablix-selection-cell > div {
  position: relative;
  width: 100%;
  height: 100%;
}
.ar-tablix-selection-cell > div > div {
  position: absolute;
  z-index: 1313;
  top: -1px;
  left: -1px;
  width: calc(100% + 2px);
  height: calc(100% + 2px);
  border-width: 2px;
  border-style: none;
  border-color: var(--cui-accent);
}

.ar-tablix-header-cell {
  position: relative;
  text-align: center;
  pointer-events: all;
  border: 1px solid var(--cui-bg-panels-border);
  background-color: var(--cui-bg-panels);
  line-height: 17px;
}

.ar-tablix-header-cell.corner {
  border-radius: 9px;
}

.ar-tablix-header-cell.left {
  border-radius: 4px 0 0 4px;
}

.ar-tablix-header-cell.top {
  border-radius: 4px 4px 0 0;
}

.ar-tablix-header-cell.right {
  border-radius: 0 4px 4px 0;
}

.ar-tablix-header-cell.bottom {
  border-radius: 0 0 4px 4px;
}

.ar-tablix-header-cell-selection-primary {
  border-color: var(--cui-bg-panels-border);
  background-color: var(--cui-bg-body);
}

.ar-tablix-header-cell-selection-selected:not(.ar-tablix-header-cell-selection-primary) {
  border-color: var(--cui-bg-panels-border);
  background-color: var(--cui-bg-body);
}

.ar-tablix-header-cell-selection-projection:not(.ar-tablix-header-cell-selection-primary) {
  border-color: var(--cui-bg-panels-border);
  background-color: var(--cui-bg-body);
}

.ar-tablix-adorner-spot {
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;
  pointer-events: none;
  justify-content: center;
  align-items: center;
}
.ar-tablix-adorner-spot > .gc-icon {
  pointer-events: none;
}
.ar-tablix-adorner-spot .ar-tablix-adorner-spot-label {
  position: absolute;
  top: calc(50% - 16px / 2);
  width: 100%;
}

.ar-tablix-adorner-spot-col {
  position: absolute;
  z-index: 13;
  top: -1px;
  height: 18px;
  pointer-events: all;
}
.ar-tablix-adorner-spot-col:hover .ar-tablix-add-column {
  display: block;
}
.ar-tablix-adorner-spot-col > div.ar-tablix-add-column-grip {
  position: absolute;
  z-index: 13;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  cursor: col-resize;
}

.ar-tablix-add-column {
  position: absolute;
  z-index: 13;
  top: -22px;
  left: -4px;
  display: none;
  width: 16px;
}
.ar-tablix-add-column .ar-tablix-add-column-knob {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  width: 16px;
  height: 16px;
  cursor: pointer;
  border-radius: 16px;
  background-color: var(--cui-accent);
}
.ar-tablix-add-column .ar-tablix-add-column-knob:hover {
  text-decoration: none;
}
.ar-tablix-add-column .ar-tablix-add-column-knob > i {
  display: flex;
  width: 16px;
  height: 16px;
  text-align: center;
  color: white;
  font-size: 15px;
  line-height: 16px;
}
.ar-tablix-add-column .ar-tablix-add-column-knob > i > svg {
  width: 12px !important;
}
.ar-tablix-add-column > div.ar-tablix-add-column-deco {
  position: absolute;
  top: 14px;
  left: 6.5px;
  width: 3px;
  height: calc(100% - 13px);
  background-color: var(--cui-accent);
}

.ar-tablix-adorner-spot-row {
  position: absolute;
  z-index: 13;
  left: -1px;
  width: 18px;
  pointer-events: all;
}
.ar-tablix-adorner-spot-row:hover .ar-tablix-add-row {
  display: block;
}
.ar-tablix-adorner-spot-row > div.ar-tablix-add-row-grip {
  position: absolute;
  z-index: 13;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  cursor: row-resize;
}

.ar-tablix-add-row {
  position: absolute;
  z-index: 13;
  bottom: -4px;
  left: -22px;
  display: none;
  height: 16px;
}
.ar-tablix-add-row .ar-tablix-add-row-knob {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  width: 16px;
  height: 16px;
  cursor: pointer;
  border-radius: 16px;
  background-color: var(--cui-accent);
}
.ar-tablix-add-row .ar-tablix-add-row-knob:hover {
  text-decoration: none;
}
.ar-tablix-add-row .ar-tablix-add-row-knob > i {
  display: flex;
  width: 16px;
  height: 16px;
  text-align: center;
  color: white;
  font-size: 15px;
  line-height: 16px;
}
.ar-tablix-add-row .ar-tablix-add-row-knob > i > svg {
  width: 12px !important;
}
.ar-tablix-add-row > div.ar-tablix-add-row-deco {
  position: absolute;
  top: 6.5px;
  left: 14px;
  width: calc(100% - 14px);
  height: 3px;
  background: var(--cui-accent);
}

.ar-tablix-header-cell.edit {
  width: 18px;
  height: 19px;
  cursor: pointer;
  pointer-events: all;
  border-radius: 4px;
}
.ar-tablix-header-cell.edit.disabled {
  cursor: no-drop;
  opacity: 0.5;
}

.ar-tablix-header-cell.edit:hover {
  background-color: var(--cui-bg-body);
}

.ar-tablix {
  border-spacing: 0;
  pointer-events: all;
  border-collapse: collapse;
  border-collapse: collapse;
  border-collapse: collapse;
}
.ar-tablix td {
  padding: 0;
}

.ar-textbox {
  position: absolute;
  pointer-events: all;
  width: 100%;
  height: 100%;
  border: none;
}
.ar-textbox * {
  pointer-events: none;
}
.ar-textbox .ar-textbox-inner {
  position: absolute;
  display: flex;
  overflow: hidden;
}
.ar-textbox .ar-textbox-inner .text-wrapper {
  width: 100%;
  text-decoration: inherit;
  -webkit-text-decoration: inherit;
  /* Safari v16+ */
}
.ar-textbox .ar-textbox-inner .text-wrapper span {
  -webkit-text-combine: horizontal;
  /* Safari */
  -ms-text-combine-horizontal: all;
  text-combine-upright: all;
}
.ar-textbox .ar-textbox-inner.middle {
  align-items: center;
}
.ar-textbox .ar-textbox-inner.bottom {
  align-items: flex-end;
}
.ar-textbox .ar-textbox-inner.top {
  align-items: flex-start;
}
.ar-textbox .ar-textbox-inner.no-wrap {
  white-space: nowrap;
}
.ar-textbox .ar-textbox-inner.word-wrap {
  word-wrap: break-word;
}
.ar-textbox .ar-textbox-inner.char-wrap {
  word-break: break-all;
}
.ar-textbox .ar-textbox-inner.tb-rl {
  writing-mode: vertical-rl;
  -ms-writing-mode: tb-rl;
}

.ar-toc {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: all;
}
.ar-toc-glyph {
  width: 100%;
  height: 100%;
}
.ar-toc .toc-body {
  position: absolute;
  overflow: hidden;
  width: calc(100% + 25px);
  height: 100%;
  pointer-events: none;
}
.ar-toc .toc-body .toc-levels {
  width: 100%;
  height: auto;
  pointer-events: all;
}
.ar-toc:hover .toc-insert-wrapper {
  display: block;
}
.ar-toc .toc-insert-wrapper {
  position: relative;
  z-index: 9002;
  top: calc(100% - 5px);
  display: none;
  padding-top: 12px;
}
.ar-toc .toc-insert-wrapper:hover {
  display: block;
}
.ar-toc .toc-insert-wrapper:active {
  pointer-events: none;
}
.ar-toc .toc-insert-wrapper .toc-insert {
  overflow: hidden;
  width: 100%;
  height: auto;
  text-align: left;
  pointer-events: all;
  border-radius: 4px;
  background-color: var(--cui-bg-panels);
  box-shadow: 1px 1px 5px 0 rgba(0, 0, 0, 0.1);
}

.toc-level {
  width: 100%;
  height: auto;
  white-space: nowrap;
  pointer-events: none;
}

.toc-level__body {
  position: relative;
  float: left;
  overflow: hidden;
  width: calc(100% - 25px);
  pointer-events: all;
}
.toc-level__body .ar-item {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: auto;
  white-space: nowrap;
  pointer-events: none;
}

.toc-level__container {
  overflow: hidden;
  width: 100%;
  height: auto;
  padding: 2pt;
  vertical-align: middle;
  white-space: normal;
  pointer-events: none;
  background: transparent;
}

.toc-level__numbering {
  display: inline-block;
  padding-right: 2pt;
}

.toc-level__name {
  padding-right: 2pt;
  word-wrap: break-word;
}

.toc-level__fill-char {
  position: absolute;
  bottom: 2pt;
  white-space: nowrap;
}

.toc-level__page-number {
  position: absolute;
  right: 0;
  bottom: 0;
  padding: 2pt;
}

.toc-level__selection {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  border: 1px solid var(--cui-accent);
  background-color: rgba(0, 0, 0, 0.125);
}
.toc-level__selection--primary {
  background-color: transparent;
}

.toc-level-delete {
  position: absolute;
  right: 0;
  visibility: hidden;
  width: 20px;
  height: 20px;
  pointer-events: all;
}
.toc-level-delete > .gc-icon.gc-btn__icon {
  display: flex;
  width: 20px;
  height: 20px;
  align-items: center;
  justify-content: center;
}
.toc-level-delete > .gc-icon.gc-btn__icon > svg {
  width: 20px;
  height: 20px;
}

.toc-body:hover .toc-level-delete {
  visibility: visible;
}

.items-text-input-area {
  pointer-events: all;
  position: fixed;
  top: 100%;
  left: 100%;
  width: 0;
  height: 0;
  padding: 0;
  border-width: 0;
  text-align: initial;
  resize: none;
  background-color: transparent;
  box-shadow: inset 0 0 0 1px;
}
.items-text-input-area.active-input {
  z-index: 14;
  position: absolute;
}

.dnd-is-over {
  background-color: var(--cui-accent-hover) !important;
}

.dnd-can-drop {
  background-color: var(--cui-accent) !important;
}

.bg-no-repeat {
  background-repeat: no-repeat;
}

.bg-repeat {
  background-repeat: repeat;
}

.bg-repeat-x {
  background-repeat: repeat-x;
}

.bg-repeat-y {
  background-repeat: repeat-y;
}

.ar-view {
  position: relative;
  z-index: 0;
  overflow: hidden;
  width: 100%;
  height: 100%;
  background: transparent;
}

.ar-adorner-primary {
  z-index: 9007;
}

.ar-adorner-secondary {
  z-index: 9006;
}

.rpx-surface {
  overflow: hidden;
  width: 100%;
  height: 100%;
}

.rpx-area-selector {
  padding-top: 1px;
  padding-bottom: 1px;
  min-height: 100%;
  min-width: 100%;
  height: 100%;
}
.rpx-area-selector__drag-source {
  padding-top: 1px;
  padding-bottom: 1px;
  min-height: 100%;
  min-width: 100%;
  height: 100%;
  pointer-events: all;
}

.rpx-page {
  position: relative;
  z-index: 0;
  background-color: white;
  box-shadow: 0 0 7px 0 rgba(0, 0, 0, 0.1);
}
.rpx-page__gutter {
  height: 100%;
  pointer-events: none;
  background-color: rgba(170, 170, 170, 0.6);
}
.rpx-page__content {
  position: relative;
  width: 100%;
  height: 100%;
}

.rpx-page-wrapper {
  margin-top: 64px;
  margin-bottom: 64px;
  margin-left: 64px;
}

.drag-layer {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0px;
  pointer-events: none;
}
.drag-layer .dnd-preview {
  position: absolute;
}
.drag-layer .rectangle-area-frame {
  position: absolute;
  z-index: 2;
  border: 1px solid var(--cui-accent);
  background-color: var(--cui-accent-semi-10);
}

.rpx-section {
  position: relative;
  width: 100%;
}

.rpx-rectangle {
  position: relative;
  width: 100%;
  height: 100%;
}

.rpx-item {
  position: absolute;
  z-index: 1;
}

.rpx-item--over {
  background-color: var(--cui-accent);
}

.rpx-item--can-drop {
  box-shadow: inset 0px 0px 2px 2px var(--cui-accent);
}

.fixed-zoom {
  zoom: 1;
}

.rpx-selection {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 1px solid rgba(170, 170, 170, 0.6);
}
.rpx-selection__primary {
  border: 1px solid var(--cui-accent);
}
.rpx-selection__secondary {
  border: 1px solid var(--cui-accent-semi-60);
}
.rpx-selection__rounding {
  border: none;
}
.rpx-selection .mover > div {
  position: absolute;
  z-index: 10001;
  cursor: move;
}
.rpx-selection .mover .mover-n {
  top: -3px;
  left: 0;
  width: 100%;
  height: 6px;
}
.rpx-selection .mover .mover-e {
  top: 0;
  left: -3px;
  width: 6px;
  height: 100%;
}
.rpx-selection .mover .mover-s {
  bottom: -3px;
  left: 0;
  width: 100%;
  height: 6px;
}
.rpx-selection .mover .mover-w {
  top: 0;
  right: -3px;
  width: 6px;
  height: 100%;
}
.rpx-selection .grip, .rpx-selection .grip-angle {
  position: absolute;
  z-index: 10002;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 10px;
  height: 10px;
  border-radius: 50%;
}
.rpx-selection .grip > div, .rpx-selection .grip-angle > div {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--cui-accent);
}
.rpx-selection .grip {
  border: 1px solid var(--cui-accent);
  background: white;
}
.rpx-selection .grip-angle {
  box-sizing: content-box;
  background: var(--cui-accent);
  width: 6px;
  height: 6px;
  border: 1.5px solid white;
}
.rpx-selection .grip-angle::after {
  content: "";
  position: absolute;
  width: 12px;
  height: 12px;
  background: none;
  border-radius: 50%;
}
.rpx-selection .grip-angle-guide {
  position: absolute;
  width: 2px;
  background: var(--cui-accent);
  opacity: 0.5;
  border-radius: 50%;
}
.rpx-selection .grip-n {
  top: -5px;
}
.rpx-selection .grip-vm {
  top: calc(50% - 5px);
}
.rpx-selection .grip-s {
  top: calc(100% - 5px);
}
.rpx-selection .grip-w {
  left: -5px;
}
.rpx-selection .grip-hm {
  left: calc(50% - 5px);
}
.rpx-selection .grip-e {
  left: calc(100% - 5px);
}
.rpx-selection .grip-angle-tl {
  top: 0;
  left: 0;
}
.rpx-selection .grip-angle-tr {
  top: 0;
  right: 0;
}
.rpx-selection .grip-angle-bl {
  bottom: 0;
  left: 0;
}
.rpx-selection .grip-angle-br {
  bottom: 0;
  right: 0;
}
.rpx-selection .grip-point {
  cursor: crosshair;
}
.rpx-selection .disabled {
  cursor: default;
  background-color: lightgray;
}
.rpx-selection .disabled > div {
  /* primary selection marker */
  opacity: 0.6;
}

.rpx-line-selection {
  border: none;
}

.rpx-shape-rounding {
  opacity: 0.5;
  border-style: dashed !important;
}

.rpx-line-border {
  position: absolute;
  z-index: 1000;
  transform-origin: top left;
  border: 1px solid var(--cui-accent);
}

textarea.gc-textarea.rpx-script-editor {
  font-family: monospace;
  line-height: 1.2;
}
textarea.gc-textarea.rpx-script-editor:not([disabled]):hover {
  background-color: var(--cui-input-bg);
}
textarea.gc-textarea.rpx-script-editor:not([disabled]):focus {
  background-color: var(--cui-input-bg);
}

.rpx-script-editor__menu .gc-menu__logo .gc-btn {
  overflow: hidden;
}
.rpx-script-editor__menu .gc-menu__logo .gc-btn .gc-icon {
  width: 50px;
}
.rpx-script-editor__menu .gc-menu__logo .gc-btn .gc-btn__text {
  padding-left: 50px;
}

.rpx-ruler-highlighter__primary {
  background-color: var(--cui-accent);
  opacity: 0.7;
}
.rpx-ruler-highlighter__secondary {
  background-color: var(--cui-accent);
  opacity: 0.85;
}

.rpx-ruler-marker--accent {
  background-color: var(--cui-accent);
}

.rpx-picture-item {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 100%;
  user-select: none;
  pointer-events: none;
  border: 1px dotted rgba(170, 170, 170, 0.6);
  font-size: var(--cui-text-size-sm);
}
.rpx-picture-item__image {
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
}

.rpx-reportinfo-item {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 100%;
  padding: 2px;
  user-select: none;
  pointer-events: none;
  border: 1px dotted rgba(170, 170, 170, 0.6);
  font-size: 10pt;
}

.rpx-subreport-item {
  display: block;
  overflow: hidden;
  width: 100%;
  height: 100%;
  padding: 2px;
  user-select: none;
  pointer-events: none;
  border: 1px dotted rgba(170, 170, 170, 0.6);
  word-wrap: break-word;
  font-size: 10pt;
}

.rpx-checkbox-item {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 100%;
  padding: 2px;
  user-select: none;
  pointer-events: none;
  border: 1px dotted rgba(170, 170, 170, 0.6);
  font-size: var(--cui-text-size-sm);
}
.rpx-checkbox-item__text {
  display: inline-block;
}
.rpx-checkbox-item__check {
  position: absolute;
  display: inline-block;
  box-sizing: border-box;
  width: 16px;
  height: 16px;
  border: solid black 1px;
}
.rpx-checkbox-item__check .wd-svg {
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: center;
}
.rpx-checkbox-item__check .wd-svg > svg {
  width: 12px;
}
.rpx-checkbox-item--top .rpx-checkbox-item__check {
  top: 0;
}
.rpx-checkbox-item--top .rpx-checkbox-item__text {
  padding-top: 17px;
}
.rpx-checkbox-item--middle .rpx-checkbox-item__check {
  top: calc(50% - 16px/2);
}
.rpx-checkbox-item--bottom .rpx-checkbox-item__check {
  bottom: 0;
}
.rpx-checkbox-item--bottom .rpx-checkbox-item__text {
  padding-bottom: 17px;
}
.rpx-checkbox-item--left .rpx-checkbox-item__check {
  left: 0;
}
.rpx-checkbox-item--left .rpx-checkbox-item__text {
  padding-left: 17px;
}
.rpx-checkbox-item--center .rpx-checkbox-item__check {
  left: calc(50% - 16px/2);
}
.rpx-checkbox-item--right .rpx-checkbox-item__check {
  right: 0;
}
.rpx-checkbox-item--right .rpx-checkbox-item__text {
  padding-right: 17px;
}

.rpx-textbox-item {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 100%;
  padding: 2px;
  user-select: none;
  pointer-events: none;
  border: 1px dotted rgba(170, 170, 170, 0.6);
  font-size: var(--cui-text-size-sm);
}
.rpx-textbox-item__text {
  display: inline-block;
}
.rpx-textbox-item__text--rtl {
  direction: rtl;
  unicode-bidi: embed;
}

.rpx-label-item {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 100%;
  padding: 2px;
  user-select: none;
  pointer-events: none;
  border: 1px dotted rgba(170, 170, 170, 0.6);
  font-size: var(--cui-text-size-sm);
}
.rpx-label-item__text {
  display: inline-block;
}
.rpx-label-item__text--rtl {
  direction: rtl;
  unicode-bidi: embed;
}

.rpx-shape-item {
  overflow: hidden;
  width: 100%;
  height: 100%;
  padding: 2px;
  user-select: none;
  pointer-events: none;
  border: 1px dotted rgba(170, 170, 170, 0.6);
  font-size: var(--cui-text-size-sm);
}
.rpx-shape-item--ellipse {
  border-radius: 50%;
}

.rpx-barcode-item {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  pointer-events: all;
}
.rpx-barcode-item .rpx-barcode-item-wrapper {
  position: relative;
  pointer-events: none;
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding: 0;
}
.rpx-barcode-item .rpx-barcode-item-wrapper__content {
  position: absolute;
  display: block;
}
.rpx-barcode-item .rpx-barcode-item-wrapper__content svg {
  display: block;
}
.rpx-barcode-item__error {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 100%;
  text-align: center;
  word-break: break-word;
  pointer-events: none;
  font-size: 12px;
  align-items: center;
  justify-content: center;
}
.rpx-barcode-item__stub {
  position: absolute;
  overflow: hidden;
  width: 100%;
  height: 100%;
  padding: 2pt;
  pointer-events: none;
  color: var(--cui-text);
  border: none;
  background-color: var(--cui-bg-panels);
  font-size: 8pt;
  font-weight: bold;
}
.rpx-barcode-item__model-dumper {
  position: absolute;
  bottom: 4px;
  left: calc((4px * -1) - 30px);
  width: 30px;
  height: 30px;
  cursor: pointer;
  text-align: center;
  pointer-events: all;
  border-radius: 4px;
  background-color: var(--cui-bg-panels);
  line-height: 30px;
}
.rpx-barcode-item__model-dumper > i {
  display: flex;
  width: 30px;
  height: 30px;
  pointer-events: none;
  align-items: center;
  justify-content: center;
}

.rpx-default-item {
  overflow: hidden;
  width: 100%;
  height: 100%;
  padding: 2px;
  user-select: none;
  pointer-events: none;
  opacity: 0.6;
  border: 1px dotted rgba(170, 170, 170, 0.6);
  font-size: var(--cui-text-size-sm);
}

.dnd-preview {
  position: absolute;
}

.default-dnd-preview {
  opacity: 0.6;
  margin: 1px;
  border: solid 2px black;
}

.rpx-line-item {
  position: absolute;
  transform-origin: top left;
}

.dnd-preview-line {
  position: absolute;
  transform-origin: top left;
}

.rpx-unknown-item {
  overflow: hidden;
  width: 100%;
  height: 100%;
  padding: 2px;
  user-select: none;
  pointer-events: none;
  opacity: 0.6;
  border: 1px dotted rgba(170, 170, 170, 0.6);
  font-size: var(--cui-text-size-sm);
}

.rpx-page-break-item {
  background-color: var(--cui-accent);
  left: 0;
  height: 1px;
}

.rpx-section > .rpx-rectangle-selection {
  pointer-events: none;
}
.rpx-section > .angle {
  height: 100%;
  width: 100%;
}
.rpx-section .section-header {
  position: absolute;
  z-index: 10001;
  max-width: 100%;
  overflow: hidden;
  padding: 2px 5px;
  cursor: default;
  transition: all 0.2s ease-in-out;
  white-space: nowrap;
  text-overflow: ellipsis;
  pointer-events: none;
  opacity: 0.5;
  color: #5c5c5c;
  border: 1px solid transparent;
  background-color: transparent;
  font-size: var(--cui-text-size-sm);
}
.rpx-section .section-header__hidden {
  visibility: hidden;
  opacity: 0;
}
.rpx-section .section-header__primary {
  transition: all 0.2s ease-in-out;
  transform: translateY(-100%);
  opacity: 1;
  color: var(--cui-contrast-text);
  border: 1px solid var(--cui-accent);
  background-color: var(--cui-accent);
}

.rpx-cross-section-box-item {
  overflow: hidden;
  width: 100%;
  height: 100%;
  padding: 2px;
  user-select: none;
  pointer-events: none;
}

.rpx-cross-section-line-item {
  overflow: hidden;
  width: 100%;
  height: 100%;
  user-select: none;
  pointer-events: none;
}

.dnd-preview-cross-section-line {
  opacity: 0.6;
  margin: 1px;
  border-left: solid 2px black;
}

.rpx-rich-text {
  overflow: hidden;
  width: 100%;
  height: 100%;
  user-select: none;
  word-wrap: break-word;
  pointer-events: none;
  border: 1px dotted rgba(170, 170, 170, 0.6);
  font-size: 10pt;
}

.rpx-input-field-checkbox {
  width: 100%;
  height: 100%;
  user-select: none;
  pointer-events: none;
  border: 1px dotted rgba(170, 170, 170, 0.6);
}
.rpx-input-field-checkbox__icon {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
}
.rpx-input-field-checkbox--checked {
  display: inline-block;
  box-sizing: border-box;
  width: 100%;
}
.rpx-input-field-checkbox--checked.wd-svg {
  display: flex;
}
.rpx-input-field-checkbox--checked.wd-svg > svg {
  width: 100%;
  height: 100%;
}

.rpx-input-field-text {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 100%;
  user-select: none;
  word-break: break-word;
  pointer-events: none;
  border: 1px dotted rgba(170, 170, 170, 0.6);
  font-size: var(--cui-text-size-sm);
  align-items: center;
}
.rpx-input-field-text__text {
  display: inline-block;
}

.wd-image-editor-native-input {
  opacity: 0;
  width: 0;
  height: 0;
  position: absolute;
  user-select: none;
  overflow: hidden;
}

.wd-rpx-shape-editor__preview-box {
  display: flex;
  width: 18px;
  height: 18px;
  align-items: center;
  justify-content: center;
}
.wd-rpx-shape-editor__preview-content {
  border: 2px solid;
}
.wd-rpx-shape-editor__preview-content--rectangle {
  width: 16px;
  height: 12px;
}
.wd-rpx-shape-editor__preview-content--round-rect {
  width: 16px;
  height: 12px;
  border-radius: 5px;
}
.wd-rpx-shape-editor__preview-content--ellipse {
  width: 16px;
  height: 12px;
  border-radius: 50%;
}

.wd-rpx-background-style-editor__preview-box {
  display: flex;
  width: 18px;
  height: 18px;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}
.wd-rpx-background-style-editor__preview-box--solid {
  background-color: currentColor;
}
.wd-rpx-background-style-editor__preview-box--gradient {
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);
  background-image: linear-gradient(135deg, transparent 30%, currentColor 80%);
}
.wd-rpx-background-style-editor__preview-box--pattern {
  width: 19px;
  height: 19px;
  background-image: linear-gradient(currentColor 2px, transparent 2px), linear-gradient(to right, currentColor 2px, transparent 2px);
  background-size: 7px 7px;
  background-position: 5px 5px;
  box-shadow: inset 0 0 0 2px currentColor;
}

/* -------------- Application Styles --------------- */
.app-about {
  padding: 15px;
}
.app-about .app-name, .app-about .product-name, .app-about .product-help-title {
  font-size: 24px;
}
.app-about .app-version, .app-about .product-version {
  font-size: 14px;
}
.app-about .copyright, .app-about .product-help-link {
  font-size: 14px;
}
.app-about > p > a {
  word-break: break-all;
  word-wrap: break-word;
}

.wd-data-attribute__variations-toggle {
  position: absolute;
  top: 0;
  right: 0;
}

.wd-data-item-container--attribute {
  position: relative;
  padding-left: 40px;
}
.wd-data-item-container--attribute .wd-data-item__icon-drag {
  flex: 0 0 30px;
  width: 30px;
  height: 30px;
}
.wd-data-item-container--attribute .wd-data-item__icon-drag > .gc-icon {
  flex: 0 0 30px;
  width: 30px;
  height: 30px;
}
.wd-data-item-container--attribute .wd-data-item > .gc-icon {
  flex: 0 0 30px;
  width: 30px;
  height: 30px;
}
.wd-data-item-container--attribute .wd-data-item > span, .wd-data-item-container--attribute .wd-data-item__title, .wd-data-item-container--attribute .wd-data-item__subtitle {
  line-height: 30px;
}
.wd-data-item-container--attribute .wd-data-item--expanded {
  margin: 0;
  background-color: var(--cui-btn-bg);
  box-shadow: inset 1px 0 0 0 var(--cui-bg-panels-border);
}
.wd-data-item-container--attribute > .branch {
  position: absolute;
  top: 0;
  left: 20px;
  display: block;
  width: 20px;
  height: 100%;
  flex-grow: 0;
  flex-shrink: 0;
}
.wd-data-item-container--attribute > .branch.offset + .branch {
  left: 55px;
  width: 15px;
}
.wd-data-item-container--attribute > .branch .corner {
  display: block;
  width: 100%;
  height: 50%;
  border-bottom: 1px dotted var(--cui-bg-panels-border);
  border-left: 1px dotted var(--cui-bg-panels-border);
}
.wd-data-item-container--attribute > .branch .line {
  display: block;
  width: 100%;
  height: 50%;
  border-left: 1px dotted var(--cui-bg-panels-border);
}
.wd-data-item-container--attribute:last-child .branch .line {
  display: none;
}
.wd-data-item-container--attribute:last-child .branch.offset .line {
  display: block;
}
.wd-data-item-container--attribute:last-child .wd-data-attribute__variations .branch.offset + .branch .line:last-child {
  display: none;
}

.wd-data-item-container--inner-attribute {
  margin-left: 40px;
  padding-left: 30px;
}
.wd-data-item-container--inner-attribute > .branch {
  left: 15px;
  width: 15px;
}

.wd-data-item--disabled-attribute {
  pointer-events: none;
  opacity: 0.62;
}

.wd-data-item-data-set__search {
  display: flex;
  width: 100%;
  height: 40px;
  padding-left: 20px;
  align-items: center;
}
.wd-data-item-data-set__fields--empty {
  display: flex;
  width: 100%;
  height: 30px;
  padding-left: 20px;
}
.wd-data-item-data-set__children {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}
.wd-data-item-data-set__children > div {
  display: flex;
  width: 100%;
  padding-left: 20px;
}
.wd-data-item-data-set__children > div > .wd-data-item-data-set {
  min-width: 0;
  flex-grow: 10;
}
.wd-data-item-data-set__children > div:last-child > .wd-data-item-data-set__branch-corner {
  max-height: 20px;
}
.wd-data-item-data-set__message {
  width: 100%;
  text-align: center;
  color: var(--cui-text-semi-60);
  font-size: var(--cui-text-size);
  line-height: 30px;
}
.wd-data-item-data-set__branch-corner {
  width: 20px;
  height: 100%;
  border-left: 1px dotted var(--cui-bg-panels-border);
  flex-grow: 0;
  flex-shrink: 0;
}
.wd-data-item-data-set__branch-corner::after {
  content: "";
  display: block;
  margin-top: 20px;
  border-bottom: 1px dotted var(--cui-bg-panels-border);
}
.wd-data-item-data-set__corner {
  width: 20px;
  height: 50%;
  border-left: 1px dotted var(--cui-bg-panels-border);
  border-bottom: 1px dotted var(--cui-bg-panels-border);
  flex-grow: 0;
  flex-shrink: 0;
}
.wd-data-item-data-set__no-branch-line {
  width: 20px;
  height: 100%;
  border-left: 1px dotted var(--cui-bg-panels-border);
  flex-grow: 0;
  flex-shrink: 0;
}
.wd-data-item-data-set--with-children > .wd-data-item-data-set__fields .wd-data-item-container--field:last-child > .branch > .line {
  display: block;
}

.wd-data-field__variations-toggle {
  position: absolute;
  top: 0;
  right: 0;
}

.wd-data-item-container--field {
  position: relative;
  padding-left: 40px;
}
.wd-data-item-container--field .wd-data-item > .gc-icon {
  flex: 0 0 30px;
  width: 30px;
  height: 30px;
}
.wd-data-item-container--field .wd-data-item > span, .wd-data-item-container--field .wd-data-item__title, .wd-data-item-container--field .wd-data-item__subtitle {
  line-height: 30px;
}
.wd-data-item-container--field > .branch {
  position: absolute;
  top: 0;
  left: 20px;
  display: block;
  width: 20px;
  height: 100%;
  flex-grow: 0;
  flex-shrink: 0;
}
.wd-data-item-container--field > .branch.offset + .branch {
  left: 55px;
  width: 15px;
}
.wd-data-item-container--field > .branch .corner {
  display: block;
  width: 100%;
  height: 50%;
  border-bottom: 1px dotted var(--cui-bg-panels-border);
  border-left: 1px dotted var(--cui-bg-panels-border);
}
.wd-data-item-container--field > .branch .line {
  display: block;
  width: 100%;
  height: 50%;
  border-left: 1px dotted var(--cui-bg-panels-border);
}
.wd-data-item-container--field:last-child .branch .line {
  display: none;
}
.wd-data-item-container--field:last-child .branch.offset .line {
  display: block;
}
.wd-data-item-container--field:last-child .wd-data-field__variations .branch.offset + .branch .line:last-child {
  display: none;
}

.wd-data-field-custom-action__container {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  flex-direction: row-reverse;
  height: 30px;
  justify-content: flex-start;
  align-items: center;
}
.wd-data-field-custom-action__container--with-variations {
  right: 30px;
}

.wd-data-item {
  display: flex;
  overflow: hidden;
  min-height: 30px;
  padding-right: 5px;
  border-radius: 4px;
  align-items: center;
  justify-content: stretch;
}
.wd-data-item > .gc-icon {
  display: flex;
  flex: 0 0 40px;
  width: 40px;
  height: 40px;
  align-items: center;
  justify-content: center;
}
.wd-data-item__icon-drag {
  display: block;
  flex: 0 0 40px;
  width: 40px;
  height: 40px;
}
.wd-data-item__icon-drag > .gc-icon {
  display: flex;
  width: 40px;
  height: 40px;
  color: var(--cui-accent-text);
  align-items: center;
  justify-content: center;
}
.wd-data-item > span, .wd-data-item__title, .wd-data-item__subtitle {
  display: block;
  overflow: hidden;
  user-select: none;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: var(--cui-text);
  font-size: var(--cui-text-size);
  line-height: 40px;
}
.wd-data-item__title {
  padding-top: 5px;
  line-height: 15px;
}
.wd-data-item__subtitle {
  color: var(--cui-text-semi-60);
  line-height: 15px;
}
.wd-data-item__text {
  overflow: hidden;
  flex: 1 1 100%;
  padding-bottom: 5px;
}
.wd-data-item__controls {
  display: flex;
  flex-direction: row-reverse;
  height: 40px;
  align-items: center;
  justify-content: flex-end;
  flex-grow: 0;
  flex-shrink: 0;
}
.wd-data-item--expanded {
  margin-left: 30px;
}
.wd-data-item:hover {
  background-color: var(--cui-btn-bg);
}

.wd-data-item-relation__fields--empty {
  display: flex;
  width: 100%;
  height: 30px;
  padding-left: 20px;
}
.wd-data-item-relation__message {
  width: 100%;
  text-align: center;
  color: var(--cui-text-semi-60);
  font-size: var(--cui-text-size);
  line-height: 30px;
}
.wd-data-item-relation__branch-line {
  width: 20px;
  height: 100%;
  border-left: 1px dotted var(--cui-bg-panels-border);
  flex-grow: 0;
  flex-shrink: 0;
}
.wd-data-item-relation__branch-corner {
  width: 20px;
  height: 50%;
  border-bottom: 1px dotted var(--cui-bg-panels-border);
  border-left: 1px dotted var(--cui-bg-panels-border);
  flex-grow: 0;
  flex-shrink: 0;
}

.wd-data-item--expanded-relation {
  background-color: var(--cui-btn-bg);
  box-shadow: inset 0 -1px 0 0 var(--cui-bg-panels-border);
}
.wd-data-item--expanded-relation:hover {
  background-color: var(--cui-btn-bg-hover);
}

.wd-data-item-container--relation {
  position: relative;
}
.wd-data-item-container--relation .wd-data-item--relation {
  margin-left: 40px;
}
.wd-data-item-container--relation .wd-data-item--relation .wd-data-item__icon-drag {
  flex: 0 0 30px;
  width: 30px;
}
.wd-data-item-container--relation .wd-data-item--relation .wd-data-item__icon-drag > .gc-icon {
  flex: 0 0 30px;
  width: 30px;
}
.wd-data-item-container--relation > .branch {
  position: absolute;
  top: 0;
  left: 20px;
  display: block;
  width: 20px;
  height: 100%;
  flex-grow: 0;
  flex-shrink: 0;
}
.wd-data-item-container--relation > .branch.offset + .branch {
  left: 55px;
  width: 15px;
}
.wd-data-item-container--relation > .branch .corner {
  display: block;
  width: 100%;
  height: 20px;
  border-bottom: 1px dotted var(--cui-bg-panels-border);
  border-left: 1px dotted var(--cui-bg-panels-border);
}
.wd-data-item-container--relation > .branch .line {
  display: block;
  width: 100%;
  height: 50%;
  border-left: 1px dotted var(--cui-bg-panels-border);
}
.wd-data-item-container--relation:last-child > .branch .line {
  display: none;
}
.wd-data-item-container--relation:last-child > .branch.offset .line {
  display: block;
}
.wd-data-item-container--relation:last-child .wd-data-field__variations .branch.offset + .branch .line:last-child {
  display: none;
}

.wd-data-item-container--inner-relation {
  margin-left: 40px;
}
.wd-data-item-container--inner-relation .wd-data-item--relation {
  margin-left: 30px;
}
.wd-data-item-container--inner-relation > .branch {
  left: 15px;
  width: 15px;
}

.wd-data-search {
  position: relative;
  display: block;
  width: 100%;
}
.wd-data-search > .gc-icon {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  width: 30px;
  height: 30px;
  align-items: center;
  justify-content: center;
}
.wd-data-search > input[type=text].gc-input {
  width: 100%;
  padding-left: 30px;
}
.wd-data-search > .gc-btn {
  position: absolute;
  top: 0;
  right: 0;
}
.wd-data-search--has-value > input[type=text].gc-input {
  padding-right: 30px;
}

.wd-sidebar-panel {
  position: relative;
  width: 100%;
  padding: 0 15px;
}
.wd-sidebar-panel__heading {
  width: 100%;
  padding: 10px 0 5px 0;
}
.wd-sidebar-panel__content {
  width: 100%;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--cui-bg-panels-border);
}
.wd-sidebar-panel__placeholder {
  display: block;
  overflow: hidden;
  width: 100%;
  user-select: none;
  text-align: center;
  color: var(--cui-text-semi-60);
  font-size: var(--cui-text-size);
  line-height: 40px;
}

.dataset-list-level-1 {
  margin-left: 16px;
}

.dataset-list-level-2 {
  margin-left: 32px;
}

.dataset-list-level-3, .dataset-list-level-4, .dataset-list-level-5 {
  margin-left: 40px;
}

.ar-data-panel {
  height: 100%;
}

#parameter-editor-shutter .ar-shutter, #smart-suggestions-editor-shutter .ar-shutter {
  height: 100%;
  margin-bottom: 0;
}
#parameter-editor-shutter .ar-shutter .ar-shutter-content, #smart-suggestions-editor-shutter .ar-shutter .ar-shutter-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}
#parameter-editor-shutter .ar-shutter .ar-shutter-content .ar-shutter-body, #smart-suggestions-editor-shutter .ar-shutter .ar-shutter-content .ar-shutter-body {
  position: relative;
  flex-grow: 1;
  overflow: auto;
  margin-left: 5px;
  margin-right: 5px;
}
#parameter-editor-shutter .ar-shutter .ar-shutter-content .ar-shutter-body .panel-heading, #smart-suggestions-editor-shutter .ar-shutter .ar-shutter-content .ar-shutter-body .panel-heading {
  height: 45px;
  padding: 0 15px;
  text-transform: uppercase;
  color: var(--cui-accent);
  background-color: transparent;
  font-size: 14px;
  font-weight: 600;
  line-height: 50px;
}
#parameter-editor-shutter .ar-shutter .ar-shutter-content .ar-shutter-body .panel-body, #smart-suggestions-editor-shutter .ar-shutter .ar-shutter-content .ar-shutter-body .panel-body {
  padding: 0 0 15px 0;
  border-bottom-width: 1px;
  border-bottom-style: solid;
  border-image: linear-gradient(to right, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 3%, var(--cui-bg-panels-border) 3%, var(--cui-bg-panels-border) 97%, rgba(0, 0, 0, 0) 97%, rgba(0, 0, 0, 0) 100%) 1 stretch;
  border-image-slice: 0 0 100 0;
}
#parameter-editor-shutter .ar-shutter .ar-shutter-content .ar-shutter-body .wd-parameter-editor, #smart-suggestions-editor-shutter .ar-shutter .ar-shutter-content .ar-shutter-body .wd-parameter-editor {
  margin: 0 15px;
}
#parameter-editor-shutter .ar-shutter .ar-shutter-content .ar-shutter-body .ar-with-buttons, #smart-suggestions-editor-shutter .ar-shutter .ar-shutter-content .ar-shutter-body .ar-with-buttons {
  position: relative;
}
#parameter-editor-shutter .ar-shutter .ar-shutter-content .ar-shutter-body .ar-with-buttons .btn.btn-default, #parameter-editor-shutter .ar-shutter .ar-shutter-content .ar-shutter-body .ar-with-buttons .btn.btn-default:focus, #smart-suggestions-editor-shutter .ar-shutter .ar-shutter-content .ar-shutter-body .ar-with-buttons .btn.btn-default, #smart-suggestions-editor-shutter .ar-shutter .ar-shutter-content .ar-shutter-body .ar-with-buttons .btn.btn-default:focus {
  position: absolute;
  top: 0;
  border-style: none;
  display: block;
  width: 30px;
  height: 30px;
  padding: 0;
  text-align: left;
  background: none;
  font-size: 12px;
  line-height: 40px;
}
#parameter-editor-shutter .ar-shutter .ar-shutter-content .ar-shutter-body .ar-with-buttons .btn.btn-default > span, #parameter-editor-shutter .ar-shutter .ar-shutter-content .ar-shutter-body .ar-with-buttons .btn.btn-default:focus > span, #smart-suggestions-editor-shutter .ar-shutter .ar-shutter-content .ar-shutter-body .ar-with-buttons .btn.btn-default > span, #smart-suggestions-editor-shutter .ar-shutter .ar-shutter-content .ar-shutter-body .ar-with-buttons .btn.btn-default:focus > span {
  margin-left: -5px;
  padding-right: 10px;
}
#parameter-editor-shutter .ar-shutter .ar-shutter-content .ar-shutter-body .ar-with-buttons .btn.btn-default > i, #parameter-editor-shutter .ar-shutter .ar-shutter-content .ar-shutter-body .ar-with-buttons .btn.btn-default:focus > i, #smart-suggestions-editor-shutter .ar-shutter .ar-shutter-content .ar-shutter-body .ar-with-buttons .btn.btn-default > i, #smart-suggestions-editor-shutter .ar-shutter .ar-shutter-content .ar-shutter-body .ar-with-buttons .btn.btn-default:focus > i {
  display: flex;
  float: left;
  width: 30px;
  height: 30px;
  text-align: center;
  font-size: 13px;
  line-height: 32px;
}
#parameter-editor-shutter .ar-shutter .ar-shutter-content .ar-shutter-body .ar-with-buttons .btn.btn-default:hover, #smart-suggestions-editor-shutter .ar-shutter .ar-shutter-content .ar-shutter-body .ar-with-buttons .btn.btn-default:hover {
  background-color: #d9d9d9;
}
#parameter-editor-shutter .ar-shutter .ar-shutter-content .ar-shutter-body .ar-with-buttons.left, #smart-suggestions-editor-shutter .ar-shutter .ar-shutter-content .ar-shutter-body .ar-with-buttons.left {
  padding-left: 40px;
}
#parameter-editor-shutter .ar-shutter .ar-shutter-content .ar-shutter-body .ar-with-buttons.left .btn.btn-default, #smart-suggestions-editor-shutter .ar-shutter .ar-shutter-content .ar-shutter-body .ar-with-buttons.left .btn.btn-default {
  top: 10px;
  left: 5px;
}

.ed-datasource-dialog__content {
  padding: 15px;
}
.ed-datasource-dialog__content > .gc-label {
  margin-bottom: 15px;
}

.monospaced-editor {
  font-family: monospace !important;
}

.ed-dataset-dialog__content {
  padding: 15px;
}
.ed-dataset-dialog__content > .gc-label {
  margin-bottom: 15px;
}
.ed-dataset-dialog .ed-dataset-navbar {
  display: flex;
  margin-bottom: 10px;
  align-items: center;
  justify-content: left;
}
.ed-dataset-dialog .ed-dataset-navbar > .ed-dataset-breadcrumbs {
  display: flex;
  flex: 1 1 auto;
}
.ed-dataset-dialog .wd-filters {
  margin: 0 15px;
}
.ed-dataset-dialog .wd-data-set-filters__header {
  display: flex;
  height: 30px;
  margin: 0 15px;
  font-size: 12px;
  line-height: 14px;
  align-items: center;
  justify-content: space-between;
}

.treecombo-dropdown {
  padding: 6px;
}
.treecombo-dropdown > button {
  width: 100%;
  min-width: 120px;
}

.wd-app-overlay {
  position: fixed;
  z-index: 9999;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--wrd-bg-body-semi-70);
}
.wd-app-overlay .loader-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -20px;
  margin-left: -20px;
}
.wd-app-overlay span {
  position: absolute;
  top: 50%;
  right: 0;
  left: 0;
  display: block;
  margin-top: 30px;
  text-align: center;
  color: var(--cui-text);
  font-size: 14px;
}
.wd-app-overlay .loader {
  display: flex;
  justify-content: center;
  height: 100%;
}

.wd-web-designer {
  width: 100%;
  height: 100%;
  font-family: var(--cui-text-family);
}
.wd-web-designer *, .wd-web-designer *:before, .wd-web-designer *:after {
  box-sizing: border-box;
}
.wd-web-designer .gc-portal-root--notifications > .gc-positioner {
  pointer-events: none;
}
.wd-web-designer .gc-portal-root--notifications > .gc-positioner > * {
  pointer-events: all;
}
.wd-web-designer--viewer > .gc-app {
  display: none;
}

.arjs-collection-header,
.arjs-collection-element {
  display: flex;
}
.arjs-collection-header > div,
.arjs-collection-element > div {
  padding: 2px;
  width: 100%;
}

.eval-text {
  position: absolute;
  bottom: 30pt;
  right: 50pt;
  pointer-events: none;
  text-align: right;
}

.open-file-dialog-hidden {
  top: 0;
  left: 0;
  width: 0;
  height: 0;
  opacity: 0;
}

button[data-aid="Hide Section-button"] {
  display: none;
}