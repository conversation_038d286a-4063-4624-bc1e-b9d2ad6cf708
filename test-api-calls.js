// 测试API调用次数的脚本
// 在浏览器控制台中运行此脚本来验证优化效果

console.log('=== 前端依赖关系优化测试 ===');

// 监控网络请求
let apiCallCount = 0;
let apiCalls = [];

// 重写fetch函数来监控API调用
const originalFetch = window.fetch;
window.fetch = function(...args) {
  const url = args[0];
  if (typeof url === 'string' && url.includes('/api/')) {
    apiCallCount++;
    apiCalls.push({
      url: url,
      timestamp: new Date().toISOString(),
      method: args[1]?.method || 'GET'
    });
    console.log(`API调用 #${apiCallCount}: ${url}`);
  }
  return originalFetch.apply(this, args);
};

// 重写XMLHttpRequest来监控API调用
const originalXHROpen = XMLHttpRequest.prototype.open;
XMLHttpRequest.prototype.open = function(method, url, ...args) {
  if (typeof url === 'string' && url.includes('/api/')) {
    apiCallCount++;
    apiCalls.push({
      url: url,
      timestamp: new Date().toISOString(),
      method: method
    });
    console.log(`API调用 #${apiCallCount}: ${method} ${url}`);
  }
  return originalXHROpen.apply(this, [method, url, ...args]);
};

// 测试函数
function startTest() {
  console.log('开始测试...');
  apiCallCount = 0;
  apiCalls = [];
  
  // 记录开始时间
  const startTime = performance.now();
  
  // 模拟刷新项目列表（需要手动触发）
  console.log('请手动刷新项目列表或切换体检人来触发API调用');
  
  // 5秒后输出结果
  setTimeout(() => {
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    console.log('\n=== 测试结果 ===');
    console.log(`总API调用次数: ${apiCallCount}`);
    console.log(`测试持续时间: ${duration.toFixed(2)}ms`);
    console.log('\nAPI调用详情:');
    
    // 按接口分组统计
    const apiStats = {};
    apiCalls.forEach(call => {
      const endpoint = call.url.split('/api/')[1]?.split('?')[0] || call.url;
      apiStats[endpoint] = (apiStats[endpoint] || 0) + 1;
    });
    
    Object.entries(apiStats).forEach(([endpoint, count]) => {
      console.log(`  ${endpoint}: ${count}次`);
    });
    
    // 检查是否使用了新接口
    const usedNewAPI = apiCalls.some(call => 
      call.url.includes('getItemGroupWithDependencyAnalysis')
    );
    
    const usedOldAPI = apiCalls.some(call => 
      call.url.includes('getRelationGroupsByMainId')
    );
    
    console.log('\n=== 优化状态 ===');
    if (usedNewAPI && !usedOldAPI) {
      console.log('✅ 成功使用新的优化接口');
    } else if (usedOldAPI && !usedNewAPI) {
      console.log('⚠️ 使用了旧的接口（可能是降级）');
    } else if (usedNewAPI && usedOldAPI) {
      console.log('⚠️ 同时使用了新旧接口');
    } else {
      console.log('ℹ️ 未检测到相关接口调用');
    }
    
  }, 5000);
}

// 重置监控
function resetTest() {
  apiCallCount = 0;
  apiCalls = [];
  console.log('测试重置完成');
}

// 导出测试函数到全局
window.dependencyTest = {
  start: startTest,
  reset: resetTest,
  getStats: () => ({ apiCallCount, apiCalls })
};

console.log('测试脚本加载完成！');
console.log('使用方法:');
console.log('1. dependencyTest.start() - 开始测试');
console.log('2. dependencyTest.reset() - 重置测试');
console.log('3. dependencyTest.getStats() - 获取统计信息');
