# 异常项目一键选中功能测试指南

## 功能概述

本功能允许用户在复查管理模态框中一键选中当前体检人员的所有异常项目，大大提高医生的工作效率。

## 测试场景

### 1. 基础功能测试

#### 1.1 异常项目展示测试
- **测试步骤**：
  1. 选择一个有异常项目的体检人员
  2. 点击"复查"按钮打开复查管理模态框
  3. 观察异常项目展示区域

- **预期结果**：
  - 异常项目区域正确显示当前体检人员的所有异常项目
  - 每个异常项目显示科室名称、项目名称和检查部位
  - 异常项目数量标签正确显示

#### 1.2 一键选中功能测试
- **测试步骤**：
  1. 在复查管理模态框中点击"一键选中异常项目"按钮
  2. 观察项目选择区域的变化
  3. 检查复查表单中的项目列表

- **预期结果**：
  - 所有异常项目被自动添加到复查项目列表
  - 显示成功提示消息，包含添加的项目数量
  - 复查表单中的项目字段正确填充

#### 1.3 单个异常项目添加测试
- **测试步骤**：
  1. 在异常项目列表中点击单个项目的"添加"按钮
  2. 观察复查表单的变化

- **预期结果**：
  - 单个异常项目被添加到复查项目列表
  - 显示成功提示消息

### 2. 边界情况测试

#### 2.1 无异常项目测试
- **测试步骤**：
  1. 选择一个没有异常项目的体检人员
  2. 打开复查管理模态框

- **预期结果**：
  - 显示"当前体检人员暂无异常项目"的提示信息
  - "一键选中异常项目"按钮处于禁用状态
  - 异常项目数量标签显示为0

#### 2.2 重复添加测试
- **测试步骤**：
  1. 先通过一键选中添加异常项目
  2. 再次点击"一键选中异常项目"按钮

- **预期结果**：
  - 系统应该正确处理重复项目，避免重复添加
  - 或者显示相应的提示信息

#### 2.3 网络错误测试
- **测试步骤**：
  1. 模拟网络断开或API错误
  2. 尝试打开复查管理模态框

- **预期结果**：
  - 显示适当的错误提示信息
  - 异常项目区域显示加载失败状态

### 3. 用户体验测试

#### 3.1 加载状态测试
- **测试步骤**：
  1. 打开复查管理模态框
  2. 观察异常项目加载过程

- **预期结果**：
  - 在数据加载期间显示加载指示器
  - 加载完成后正确显示异常项目

#### 3.2 响应式设计测试
- **测试步骤**：
  1. 在不同屏幕尺寸下测试功能
  2. 检查布局和交互是否正常

- **预期结果**：
  - 在各种屏幕尺寸下功能正常
  - 布局合理，不出现重叠或错位

### 4. 数据一致性测试

#### 4.1 异常项目识别准确性测试
- **测试步骤**：
  1. 对比总检面板中的异常项目标签页
  2. 检查复查管理中显示的异常项目是否一致

- **预期结果**：
  - 两处显示的异常项目完全一致
  - 异常项目的判断逻辑正确（abnormalFlag == '1'）

#### 4.2 状态同步测试
- **测试步骤**：
  1. 在复查管理中选择异常项目
  2. 保存复查记录
  3. 检查复查列表是否正确更新

- **预期结果**：
  - 复查记录正确保存
  - 复查列表实时更新
  - 复查数量徽章正确更新

## 测试检查清单

- [ ] 异常项目正确展示
- [ ] 一键选中功能正常工作
- [ ] 单个项目添加功能正常
- [ ] 无异常项目时的提示正确
- [ ] 重复添加处理正确
- [ ] 网络错误处理正确
- [ ] 加载状态显示正确
- [ ] 成功提示消息显示
- [ ] 数据一致性验证通过
- [ ] 响应式设计正常
- [ ] 状态同步正确

## 已知问题和注意事项

1. **异常项目判断逻辑**：基于 `abnormalFlag == '1'` 的小项来判断大项是否异常
2. **重复项目处理**：在 RecheckNotifyForm 组件中已有去重逻辑
3. **权限控制**：确保用户有相应的复查管理权限

## 测试完成标准

- 所有测试场景通过
- 无严重bug或用户体验问题
- 功能符合设计要求
- 代码质量良好，无明显性能问题
