<template>
  <a-modal
    v-bind="$attrs"
    :title="getTitle"
    :open="visible"
    size="small"
    width="80%"
    style="top: 1px"
    :okButtonProps="{ hidden: true }"
    :cancelText="'关闭'"
    :destroy-on-close="true"
    @cancel="
      () => {
        visible = false;
      }
    "
  >
    <div class="viewer-host">
      <Viewer ref="reportViewer" language="zh" />
    </div>
  </a-modal>
</template>
<script lang="ts" setup>
  // 这是改造前的原始组件备份
  import { Core } from '@grapecity/activereports';
  import { Viewer } from '@grapecity/activereports-vue';
  import '@grapecity/activereports-localization';
  import '@grapecity/activereports/styles/ar-js-ui.css';
  import '@grapecity/activereports/styles/ar-js-viewer.css';
  import { computed, nextTick, ref } from 'vue';

  const visible = ref(false);
  let report = {};
  const filename = ref('');
  const reportViewer = ref();

  function open(options) {
    visible.value = true;
    report = options.template;
    filename.value = options.filename;
    openReport(report);
  }

  async function openReport(report) {
    await Core.FontStore.registerFonts('/fonts/fontsConfig.json');
    nextTick(async () => {
      const viewer = reportViewer.value.Viewer();
      viewer.resetDocument();
      viewer.availableExports = ['pdf'];
      viewer.open(report);
      viewer.viewMode = 'Continuous';
    });
  }

  const getTitle = computed(() => '预览报表-' + filename.value);

  defineExpose({
    open,
  });
</script>
<style src="@grapecity/activereports/styles/ar-js-ui.css"></style>
<style src="@grapecity/activereports/styles/ar-js-viewer.css"></style>
<style lang="less" scoped>
  .viewer-host {
    width: 100%;
    height: calc(100vh - 100px);
  }
</style>
<style>
  #reportViewer {
    margin: 0 auto;
    width: 100%;
    height: 100vh;
  }
</style>


