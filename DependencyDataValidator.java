package org.jeecg.modules.basicinfo.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jeecg.modules.basicinfo.entity.ItemGroup;
import org.jeecg.modules.basicinfo.entity.ItemGroupRelation;
import org.jeecg.modules.basicinfo.mapper.ItemGroupMapper;
import org.jeecg.modules.basicinfo.mapper.ItemGroupRelationMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 依赖关系数据验证器
 * 用于检查和修复项目依赖关系数据中的错误
 */
@Slf4j
@Component
public class DependencyDataValidator {

    @Autowired
    private ItemGroupRelationMapper itemGroupRelationMapper;
    
    @Autowired
    private ItemGroupMapper itemGroupMapper;

    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private boolean valid;
        private List<ValidationError> errors;
        private List<ValidationWarning> warnings;
        private Map<String, Object> statistics;

        public ValidationResult() {
            this.errors = new ArrayList<>();
            this.warnings = new ArrayList<>();
            this.statistics = new HashMap<>();
        }

        // getters and setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        public List<ValidationError> getErrors() { return errors; }
        public List<ValidationWarning> getWarnings() { return warnings; }
        public Map<String, Object> getStatistics() { return statistics; }
    }

    /**
     * 验证错误类
     */
    public static class ValidationError {
        private String type;
        private String description;
        private String itemGroupId;
        private String relationId;
        private String suggestion;

        public ValidationError(String type, String description, String itemGroupId) {
            this.type = type;
            this.description = description;
            this.itemGroupId = itemGroupId;
        }

        // getters and setters
        public String getType() { return type; }
        public String getDescription() { return description; }
        public String getItemGroupId() { return itemGroupId; }
        public String getRelationId() { return relationId; }
        public String getSuggestion() { return suggestion; }
        public void setSuggestion(String suggestion) { this.suggestion = suggestion; }
    }

    /**
     * 验证警告类
     */
    public static class ValidationWarning {
        private String type;
        private String description;
        private String itemGroupId;

        public ValidationWarning(String type, String description, String itemGroupId) {
            this.type = type;
            this.description = description;
            this.itemGroupId = itemGroupId;
        }

        // getters and setters
        public String getType() { return type; }
        public String getDescription() { return description; }
        public String getItemGroupId() { return itemGroupId; }
    }

    /**
     * 全面验证依赖关系数据
     */
    public ValidationResult validateAllDependencyData() {
        log.info("开始全面验证依赖关系数据...");
        
        ValidationResult result = new ValidationResult();
        
        try {
            // 1. 数据完整性检查
            validateDataIntegrity(result);
            
            // 2. 业务逻辑检查
            validateBusinessLogic(result);
            
            // 3. 性能相关检查
            validatePerformanceIssues(result);
            
            // 4. 生成统计信息
            generateStatistics(result);
            
            // 5. 设置验证结果
            result.setValid(result.getErrors().isEmpty());
            
            log.info("依赖关系数据验证完成，错误数量: {}, 警告数量: {}", 
                result.getErrors().size(), result.getWarnings().size());
                
        } catch (Exception e) {
            log.error("依赖关系数据验证失败", e);
            result.getErrors().add(new ValidationError("VALIDATION_FAILED", 
                "验证过程中发生异常: " + e.getMessage(), null));
            result.setValid(false);
        }
        
        return result;
    }

    /**
     * 验证特定项目的依赖关系
     */
    public ValidationResult validateProjectDependencies(String itemGroupId) {
        log.info("验证项目 {} 的依赖关系", itemGroupId);
        
        ValidationResult result = new ValidationResult();
        
        try {
            // 获取项目的所有关系
            List<ItemGroupRelation> relations = itemGroupRelationMapper.selectList(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<ItemGroupRelation>()
                    .eq(ItemGroupRelation::getGroupId, itemGroupId)
            );
            
            if (CollectionUtils.isEmpty(relations)) {
                result.getWarnings().add(new ValidationWarning("NO_RELATIONS", 
                    "项目没有配置任何依赖关系", itemGroupId));
                result.setValid(true);
                return result;
            }
            
            // 验证关系的有效性
            validateRelationsIntegrity(relations, result);
            validateRelationsLogic(relations, result);
            
            result.setValid(result.getErrors().isEmpty());
            
        } catch (Exception e) {
            log.error("验证项目依赖关系失败: {}", itemGroupId, e);
            result.getErrors().add(new ValidationError("VALIDATION_FAILED", 
                "验证过程中发生异常: " + e.getMessage(), itemGroupId));
            result.setValid(false);
        }
        
        return result;
    }

    /**
     * 数据完整性检查
     */
    private void validateDataIntegrity(ValidationResult result) {
        log.info("执行数据完整性检查...");
        
        // 检查无效的关联项目ID
        List<ItemGroupRelation> allRelations = itemGroupRelationMapper.selectList(null);
        Set<String> allItemGroupIds = itemGroupMapper.selectList(null).stream()
            .map(ItemGroup::getId).collect(Collectors.toSet());
        
        for (ItemGroupRelation relation : allRelations) {
            // 检查主项目ID有效性
            if (!allItemGroupIds.contains(relation.getGroupId())) {
                ValidationError error = new ValidationError("INVALID_MAIN_PROJECT", 
                    "主项目ID不存在: " + relation.getGroupId(), relation.getGroupId());
                error.setSuggestion("删除此关系记录或修正项目ID");
                result.getErrors().add(error);
            }
            
            // 检查关联项目ID有效性
            if (!allItemGroupIds.contains(relation.getRelationGroupId())) {
                ValidationError error = new ValidationError("INVALID_RELATED_PROJECT", 
                    "关联项目ID不存在: " + relation.getRelationGroupId(), relation.getGroupId());
                error.setSuggestion("删除此关系记录或修正关联项目ID");
                result.getErrors().add(error);
            }
            
            // 检查关系类型规范性
            if (!Arrays.asList("依赖", "附属", "赠送", "互斥").contains(relation.getRelation())) {
                ValidationError error = new ValidationError("INVALID_RELATION_TYPE", 
                    "关系类型不规范: " + relation.getRelation(), relation.getGroupId());
                error.setSuggestion("修正关系类型为标准值");
                result.getErrors().add(error);
            }
        }
    }

    /**
     * 业务逻辑检查
     */
    private void validateBusinessLogic(ValidationResult result) {
        log.info("执行业务逻辑检查...");
        
        List<ItemGroupRelation> allRelations = itemGroupRelationMapper.selectList(null);
        
        // 检查自依赖
        for (ItemGroupRelation relation : allRelations) {
            if (relation.getGroupId().equals(relation.getRelationGroupId())) {
                ValidationError error = new ValidationError("SELF_DEPENDENCY", 
                    "项目不能依赖自己", relation.getGroupId());
                error.setSuggestion("删除自依赖关系");
                result.getErrors().add(error);
            }
        }
        
        // 检查循环依赖
        validateCircularDependencies(allRelations, result);
        
        // 检查重复关系
        validateDuplicateRelations(allRelations, result);
        
        // 检查互斥冲突
        validateMutexConflicts(allRelations, result);
    }

    /**
     * 检查循环依赖
     */
    private void validateCircularDependencies(List<ItemGroupRelation> relations, ValidationResult result) {
        Map<String, Set<String>> dependencyGraph = new HashMap<>();
        
        // 构建依赖图
        for (ItemGroupRelation relation : relations) {
            if ("依赖".equals(relation.getRelation())) {
                dependencyGraph.computeIfAbsent(relation.getGroupId(), k -> new HashSet<>())
                    .add(relation.getRelationGroupId());
            }
        }
        
        // 检查循环依赖
        for (String projectId : dependencyGraph.keySet()) {
            if (hasCircularDependency(projectId, dependencyGraph, new HashSet<>())) {
                ValidationError error = new ValidationError("CIRCULAR_DEPENDENCY", 
                    "检测到循环依赖", projectId);
                error.setSuggestion("重新设计依赖关系，消除循环");
                result.getErrors().add(error);
            }
        }
    }

    /**
     * 递归检查循环依赖
     */
    private boolean hasCircularDependency(String projectId, Map<String, Set<String>> graph, Set<String> visited) {
        if (visited.contains(projectId)) {
            return true;
        }
        
        visited.add(projectId);
        Set<String> dependencies = graph.get(projectId);
        
        if (dependencies != null) {
            for (String dependency : dependencies) {
                if (hasCircularDependency(dependency, graph, new HashSet<>(visited))) {
                    return true;
                }
            }
        }
        
        return false;
    }

    /**
     * 检查重复关系
     */
    private void validateDuplicateRelations(List<ItemGroupRelation> relations, ValidationResult result) {
        Map<String, List<ItemGroupRelation>> groupedRelations = relations.stream()
            .collect(Collectors.groupingBy(r -> 
                r.getGroupId() + "|" + r.getRelation() + "|" + r.getRelationGroupId() + "|" + 
                (r.getRelationItemType() != null ? r.getRelationItemType() : "") + "|" +
                (r.getRelationItemId() != null ? r.getRelationItemId() : "")
            ));
        
        for (Map.Entry<String, List<ItemGroupRelation>> entry : groupedRelations.entrySet()) {
            if (entry.getValue().size() > 1) {
                String projectId = entry.getValue().get(0).getGroupId();
                ValidationWarning warning = new ValidationWarning("DUPLICATE_RELATIONS", 
                    "存在重复的依赖关系定义", projectId);
                result.getWarnings().add(warning);
            }
        }
    }

    /**
     * 检查互斥冲突
     */
    private void validateMutexConflicts(List<ItemGroupRelation> relations, ValidationResult result) {
        Map<String, Set<String>> mutexMap = new HashMap<>();
        Map<String, Set<String>> otherRelationsMap = new HashMap<>();
        
        // 分类关系
        for (ItemGroupRelation relation : relations) {
            String key = relation.getGroupId() + "|" + relation.getRelationGroupId();
            
            if ("互斥".equals(relation.getRelation())) {
                mutexMap.computeIfAbsent(relation.getGroupId(), k -> new HashSet<>())
                    .add(relation.getRelationGroupId());
            } else {
                otherRelationsMap.computeIfAbsent(relation.getGroupId(), k -> new HashSet<>())
                    .add(relation.getRelationGroupId());
            }
        }
        
        // 检查冲突
        for (Map.Entry<String, Set<String>> entry : mutexMap.entrySet()) {
            String projectId = entry.getKey();
            Set<String> mutexProjects = entry.getValue();
            Set<String> otherRelatedProjects = otherRelationsMap.get(projectId);
            
            if (otherRelatedProjects != null) {
                Set<String> conflicts = new HashSet<>(mutexProjects);
                conflicts.retainAll(otherRelatedProjects);
                
                if (!conflicts.isEmpty()) {
                    ValidationError error = new ValidationError("MUTEX_CONFLICT", 
                        "项目既互斥又有其他依赖关系: " + String.join(", ", conflicts), projectId);
                    error.setSuggestion("解决互斥与依赖的冲突");
                    result.getErrors().add(error);
                }
            }
        }
    }

    /**
     * 性能相关检查
     */
    private void validatePerformanceIssues(ValidationResult result) {
        log.info("执行性能相关检查...");
        
        // 检查复杂度过高的项目
        List<ItemGroupRelation> allRelations = itemGroupRelationMapper.selectList(null);
        Map<String, Long> relationCounts = allRelations.stream()
            .collect(Collectors.groupingBy(ItemGroupRelation::getGroupId, Collectors.counting()));
        
        for (Map.Entry<String, Long> entry : relationCounts.entrySet()) {
            if (entry.getValue() > 20) { // 关系数量超过20的项目
                ValidationWarning warning = new ValidationWarning("HIGH_COMPLEXITY", 
                    "项目关系复杂度过高: " + entry.getValue() + " 个关系", entry.getKey());
                result.getWarnings().add(warning);
            }
        }
    }

    /**
     * 验证关系完整性
     */
    private void validateRelationsIntegrity(List<ItemGroupRelation> relations, ValidationResult result) {
        // 实现关系完整性验证逻辑
    }

    /**
     * 验证关系逻辑
     */
    private void validateRelationsLogic(List<ItemGroupRelation> relations, ValidationResult result) {
        // 实现关系逻辑验证
    }

    /**
     * 生成统计信息
     */
    private void generateStatistics(ValidationResult result) {
        List<ItemGroupRelation> allRelations = itemGroupRelationMapper.selectList(null);
        
        Map<String, Long> relationTypeStats = allRelations.stream()
            .collect(Collectors.groupingBy(ItemGroupRelation::getRelation, Collectors.counting()));
        
        result.getStatistics().put("totalRelations", allRelations.size());
        result.getStatistics().put("relationTypeStats", relationTypeStats);
        result.getStatistics().put("uniqueProjects", 
            allRelations.stream().map(ItemGroupRelation::getGroupId).distinct().count());
    }
}
