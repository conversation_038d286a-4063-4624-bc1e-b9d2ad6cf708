# .claude/agents/enhanced-agents.yaml
version: "2.0.0"
agents:
  # ============ 1. 主控制器 Agent ============
  master_controller:
    name: "Master Migration Controller"
    type: "orchestrator"
    priority: "critical"
    
    capabilities:
      # 决策能力
      decision_making:
        - risk_assessment
        - priority_scheduling
        - resource_allocation
        - conflict_resolution
        
      # 监控能力
      monitoring:
        - real_time_progress_tracking
        - performance_metrics_collection
        - error_detection_and_alerting
        - health_check_automation
        
      # 协调能力
      coordination:
        - agent_task_distribution
        - inter_agent_communication
        - workflow_optimization
        - parallel_execution_management
    
    memory:
      type: "persistent"
      size: "2GB"
      features:
        - context_retention
        - learning_from_mistakes
        - pattern_recognition
        - optimization_history
    
    intelligence:
      model: "claude-3-opus"
      capabilities:
        - strategic_planning
        - predictive_analysis
        - adaptive_learning
        - multi_agent_orchestration
    
    communication:
      protocols:
        - websocket
        - grpc
        - rest_api
      message_queue: "redis"
      pubsub_channels:
        - "migration_events"
        - "agent_status"
        - "error_alerts"
    
    config:
      max_retries: 3
      timeout: 300
      checkpoint_interval: 60
      auto_recovery: true

  # ============ 2. 代码分析专家 Agent ============
  code_analysis_expert:
    name: "Deep Code Analyzer"
    type: "analyzer"
    priority: "high"
    
    capabilities:
      # 静态分析
      static_analysis:
        - ast_parsing
        - control_flow_analysis
        - data_flow_analysis
        - dependency_graph_generation
        - complexity_metrics
        
      # 语义分析
      semantic_analysis:
        - business_logic_extraction
        - design_pattern_detection
        - code_smell_identification
        - architectural_pattern_recognition
        
      # 智能理解
      ai_comprehension:
        - intent_recognition
        - context_understanding
        - code_explanation_generation
        - improvement_suggestions
    
    specialized_knowledge:
      frameworks:
        vue:
          versions: ["2.x", "3.x"]
          features:
            - composition_api
            - options_api
            - vuex_store
            - vue_router
        jeecgboot:
          components:
            - j_modal
            - j_table
            - j_form_designer
            - j_dict
        native_ui:
          target_version: "latest"
          component_mapping: true
    
    tools_integration:
      - eslint
      - typescript_compiler
      - babel_parser
      - swc
      - rome
    
    output:
      formats:
        - json
        - markdown
        - graph_visualization
      reports:
        - migration_feasibility
        - complexity_assessment
        - risk_analysis
        - effort_estimation

  # ============ 3. UI 转换专家 Agent ============
  ui_transformation_expert:
    name: "UI Component Transformer"
    type: "transformer"
    priority: "high"
    
    capabilities:
      # 组件映射
      component_mapping:
        strategy: "intelligent"
        features:
          - property_translation
          - event_handler_mapping
          - slot_content_migration
          - directive_conversion
          
      # 样式处理
      style_processing:
        - css_to_styled_components
        - sass_compilation
        - css_modules_handling
        - theme_variable_extraction
        - responsive_design_adaptation
        
      # 模板转换
      template_transformation:
        - syntax_conversion
        - binding_translation
        - conditional_rendering_adaptation
        - loop_structure_conversion
    
    mapping_rules:
      # JeecgBoot 到 Native UI 映射规则
      components:
        "j-modal":
          target: "n-modal"
          prop_mappings:
            "visible": "show"
            "title": "title"
            "width": "style.width"
          event_mappings:
            "@ok": "@positive-click"
            "@cancel": "@negative-click"
            
        "j-table":
          target: "n-data-table"
          prop_mappings:
            "dataSource": "data"
            "columns": "columns"
            "rowKey": "row-key"
          features:
            - pagination_conversion
            - sorting_adaptation
            - selection_migration
            
        "j-form":
          target: "n-form"
          prop_mappings:
            "model": "model"
            "rules": "rules"
            "labelCol": "label-placement"
          validation:
            preserve_all_rules: true
            enhance_with_native: true
    
    optimization:
      - remove_unused_props
      - optimize_render_functions
      - lazy_load_components
      - tree_shake_imports

  # ============ 4. 状态管理迁移 Agent ============
  state_management_migrator:
    name: "State Management Expert"
    type: "migrator"
    priority: "high"
    
    capabilities:
      # Vuex 迁移
      vuex_migration:
        - store_structure_analysis
        - module_extraction
        - action_conversion
        - mutation_translation
        - getter_optimization
        
      # 状态管理选择
      state_solution:
        options:
          - pinia
          - zustand
          - redux_toolkit
          - mobx
        recommendation_engine: true
        
      # 数据流优化
      data_flow_optimization:
        - unnecessary_state_removal
        - computed_property_optimization
        - reactive_reference_management
        - subscription_cleanup
    
    migration_strategies:
      vuex_to_pinia:
        auto_convert: true
        preserve_structure: false
        optimize_for_composition_api: true
        
      local_state_extraction:
        identify_component_state: true
        suggest_composables: true
        
    validation:
      - state_consistency_check
      - action_flow_verification
      - side_effect_analysis
      - performance_impact_assessment

  # ============ 5. API 层迁移 Agent ============
  api_layer_specialist:
    name: "API Integration Specialist"
    type: "integrator"
    priority: "high"
    
    capabilities:
      # 请求库迁移
      http_client_migration:
        from: ["axios", "fetch"]
        to: ["axios", "ky", "got"]
        features:
          - interceptor_migration
          - error_handler_conversion
          - retry_logic_preservation
          - timeout_configuration
          
      # API 适配
      api_adaptation:
        - endpoint_mapping
        - request_transformation
        - response_normalization
        - authentication_migration
        
      # Mock 服务
      mock_services:
        - auto_mock_generation
        - scenario_based_mocking
        - error_simulation
        - latency_injection
    
    security:
      - token_management
      - csrf_protection
      - xss_prevention
      - api_key_rotation
    
    optimization:
      - request_batching
      - cache_strategy
      - debounce_throttle
      - parallel_request_management

  # ============ 6. 测试自动化 Agent ============
  test_automation_specialist:
    name: "Test Automation Expert"
    type: "validator"
    priority: "critical"
    
    capabilities:
      # 测试生成
      test_generation:
        - unit_test_creation
        - integration_test_synthesis
        - e2e_scenario_generation
        - snapshot_test_creation
        - visual_regression_setup
        
      # 测试迁移
      test_migration:
        - jest_to_vitest
        - mocha_to_jest
        - karma_to_jest
        - protractor_to_playwright
        
      # 覆盖率分析
      coverage_analysis:
        - line_coverage
        - branch_coverage
        - function_coverage
        - statement_coverage
        target: 90
    
    test_strategies:
      unit_testing:
        framework: "vitest"
        mock_strategy: "auto"
        assertion_library: "chai"
        
      e2e_testing:
        framework: "playwright"
        browsers: ["chromium", "firefox", "webkit"]
        parallel_execution: true
        
      performance_testing:
        tools: ["lighthouse", "webpagetest"]
        metrics: ["FCP", "LCP", "TTI", "CLS"]
    
    continuous_validation:
      - regression_detection
      - flaky_test_identification
      - test_optimization_suggestions
      - maintenance_automation

  # ============ 7. 性能优化 Agent ============
  performance_optimizer:
    name: "Performance Optimization Expert"
    type: "optimizer"
    priority: "high"
    
    capabilities:
      # 构建优化
      build_optimization:
        - bundle_splitting
        - tree_shaking
        - minification
        - compression
        - cdn_integration
        
      # 运行时优化
      runtime_optimization:
        - lazy_loading
        - virtual_scrolling
        - memoization
        - debouncing_throttling
        - worker_thread_utilization
        
      # 资源优化
      asset_optimization:
        - image_optimization
        - font_subsetting
        - svg_optimization
        - css_purging
    
    monitoring:
      metrics:
        - bundle_size
        - load_time
        - time_to_interactive
        - first_contentful_paint
        - memory_usage
      
      tools:
        - webpack_bundle_analyzer
        - lighthouse_ci
        - performance_observer
    
    targets:
      bundle_size: "< 200KB"
      first_paint: "< 1s"
      interactive: "< 3s"
      lighthouse_score: "> 90"

  # ============ 8. 文档生成 Agent ============
  documentation_generator:
    name: "Documentation Expert"
    type: "documenter"
    priority: "medium"
    
    capabilities:
      # 文档类型
      documentation_types:
        - api_documentation
        - component_documentation
        - migration_guide
        - troubleshooting_guide
        - best_practices
        
      # 自动生成
      auto_generation:
        - jsdoc_extraction
        - typescript_types_documentation
        - example_code_generation
        - playground_creation
        
      # 多语言支持
      i18n:
        languages: ["en", "zh", "ja", "ko"]
        auto_translation: true
        terminology_consistency: true
    
    output_formats:
      - markdown
      - html
      - pdf
      - docusaurus
      - vuepress
      - storybook
    
    features:
      - live_code_examples
      - interactive_demos
      - search_functionality
      - version_management

  # ============ 9. 安全审计 Agent ============
  security_auditor:
    name: "Security Audit Expert"
    type: "auditor"
    priority: "critical"
    
    capabilities:
      # 漏洞扫描
      vulnerability_scanning:
        - dependency_vulnerabilities
        - code_vulnerabilities
        - configuration_issues
        - secrets_detection
        
      # 合规检查
      compliance_checking:
        - owasp_top_10
        - gdpr_compliance
        - pci_dss
        - hipaa
        
      # 安全加固
      security_hardening:
        - csp_implementation
        - cors_configuration
        - authentication_enhancement
        - encryption_implementation
    
    tools:
      - snyk
      - sonarqube
      - eslint_security
      - npm_audit
    
    reporting:
      severity_levels: ["critical", "high", "medium", "low"]
      auto_fix: true
      remediation_guidance: true

  # ============ 10. 部署管理 Agent ============
  deployment_manager:
    name: "Deployment Orchestrator"
    type: "deployer"
    priority: "high"
    
    capabilities:
      # 环境管理
      environment_management:
        - development
        - staging
        - production
        - feature_branches
        
      # 部署策略
      deployment_strategies:
        - blue_green
        - canary
        - rolling_update
        - feature_flags
        
      # 容器化
      containerization:
        - dockerfile_generation
        - docker_compose_setup
        - kubernetes_manifests
        - helm_charts
    
    ci_cd_integration:
      platforms:
        - github_actions
        - gitlab_ci
        - jenkins
        - azure_devops
      
      features:
        - auto_deployment
        - rollback_capability
        - health_checks
        - smoke_tests
    
    monitoring:
      - deployment_status
      - application_health
      - performance_metrics
      - error_tracking
