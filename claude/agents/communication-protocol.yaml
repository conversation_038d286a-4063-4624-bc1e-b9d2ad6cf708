# .claude/agents/communication-protocol.yaml
communication:
  # 消息总线配置
  message_bus:
    type: "redis"
    config:
      host: "localhost"
      port: 6379
      db: 0
      password: ""
    
    channels:
      # 任务分发通道
      task_distribution:
        name: "tasks"
        pattern: "task:*"
        
      # 状态更新通道
      status_updates:
        name: "status"
        pattern: "status:*"
        
      # 错误报告通道
      error_reporting:
        name: "errors"
        pattern: "error:*"
        
      # 结果收集通道
      result_collection:
        name: "results"
        pattern: "result:*"
  
  # 消息格式
  message_format:
    structure:
      id: "uuid"
      timestamp: "iso8601"
      sender: "agent_id"
      receiver: "agent_id|broadcast"
      type: "task|status|error|result"
      priority: "low|medium|high|critical"
      payload: "object"
      metadata:
        correlation_id: "uuid"
        retry_count: "number"
        timeout: "seconds"
  
  # 协议规则
  protocols:
    request_response:
      timeout: 30
      retries: 3
      
    publish_subscribe:
      guaranteed_delivery: true
      ordering: "fifo"
      
    streaming:
      chunk_size: 1024
      compression: true
