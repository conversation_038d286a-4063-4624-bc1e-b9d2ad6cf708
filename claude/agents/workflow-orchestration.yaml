# .claude/agents/workflow-orchestration.yaml
workflows:
  # 主迁移工作流
  main_migration_workflow:
    name: "Complete Migration Flow"
    version: "1.0.0"
    
    stages:
      # ====== 阶段1: 准备和分析 ======
      stage_1_preparation:
        name: "Preparation & Analysis"
        parallel: false
        tasks:
          - task_id: "init_project"
            agent: "master_controller"
            action: "initialize_migration_project"
            timeout: 300
            
          - task_id: "analyze_source"
            agent: "code_analysis_expert"
            action: "deep_source_analysis"
            dependencies: ["init_project"]
            outputs:
              - "component_inventory.json"
              - "dependency_graph.json"
              - "complexity_report.md"
            
          - task_id: "security_scan"
            agent: "security_auditor"
            action: "initial_security_scan"
            dependencies: ["init_project"]
            critical: true
            
        checkpoint: true
        rollback_point: true
        
      # ====== 阶段2: 架构迁移 ======
      stage_2_architecture:
        name: "Architecture Migration"
        parallel: true
        tasks:
          - task_id: "setup_project_structure"
            agent: "master_controller"
            action: "create_target_structure"
            
          - task_id: "migrate_routing"
            agent: "ui_transformation_expert"
            action: "convert_routing_system"
            
          - task_id: "setup_state_management"
            agent: "state_management_migrator"
            action: "initialize_state_solution"
            
          - task_id: "configure_build_system"
            agent: "performance_optimizer"
            action: "setup_build_configuration"
            
        validation:
          - "project_builds_successfully"
          - "routing_functional"
          - "state_management_operational"
          
      # ====== 阶段3: 组件迁移 ======
      stage_3_components:
        name: "Component Migration"
        parallel: true
        max_parallel: 5
        tasks:
          - task_id: "migrate_layouts"
            agent: "ui_transformation_expert"
            action: "migrate_layout_components"
            batch_size: 10
            
          - task_id: "migrate_pages"
            agent: "ui_transformation_expert"
            action: "migrate_page_components"
            batch_size: 5
            
          - task_id: "migrate_shared"
            agent: "ui_transformation_expert"
            action: "migrate_shared_components"
            batch_size: 20
            
        progress_tracking:
          report_interval: 60
          notification: true
          
      # ====== 阶段4: 业务逻辑迁移 ======
      stage_4_business_logic:
        name: "Business Logic Migration"
        parallel: false
        tasks:
          - task_id: "migrate_stores"
            agent: "state_management_migrator"
            action: "migrate_all_stores"
            validation: "required"
            
          - task_id: "migrate_api_layer"
            agent: "api_layer_specialist"
            action: "migrate_api_services"
            
          - task_id: "migrate_utils"
            agent: "code_analysis_expert"
            action: "migrate_utility_functions"
            
          - task_id: "migrate_validators"
            agent: "code_analysis_expert"
            action: "migrate_validation_logic"
            
        testing:
          auto_generate_tests: true
          run_after_each_task: true
          
      # ====== 阶段5: 测试和优化 ======
      stage_5_testing:
        name: "Testing & Optimization"
        parallel: true
        tasks:
          - task_id: "generate_tests"
            agent: "test_automation_specialist"
            action: "generate_comprehensive_tests"
            
          - task_id: "run_tests"
            agent: "test_automation_specialist"
            action: "execute_all_tests"
            dependencies: ["generate_tests"]
            
          - task_id: "optimize_performance"
            agent: "performance_optimizer"
            action: "optimize_application"
            
          - task_id: "security_audit"
            agent: "security_auditor"
            action: "comprehensive_security_audit"
            
        success_criteria:
          test_pass_rate: 95
          coverage: 85
          performance_score: 90
          
      # ====== 阶段6: 文档和部署 ======
      stage_6_finalization:
        name: "Documentation & Deployment"
        parallel: true
        tasks:
          - task_id: "generate_docs"
            agent: "documentation_generator"
            action: "create_all_documentation"
            
          - task_id: "prepare_deployment"
            agent: "deployment_manager"
            action: "prepare_deployment_artifacts"
            
          - task_id: "final_validation"
            agent: "master_controller"
            action: "final_migration_validation"
            
        outputs:
          - "migration_report.html"
          - "documentation/"
          - "deployment_package.zip"
