# .claude/mcp-suite.yaml
mcp_suite:
  # 核心 MCP 工具组合
  essential:
    - code_intelligence      # 代码智能分析
    - test_genius           # 测试生成
    - security_guard        # 安全扫描
    priority: "critical"
    
  # 效率提升组合
  productivity:
    - migration_visualizer  # 可视化管理
    - docu_master          # 文档生成
    - git_master           # 版本控制
    priority: "high"
    
  # 质量保证组合
  quality:
    - performance_wizard    # 性能优化
    - api_mocker           # API测试
    - dependency_master    # 依赖管理
    priority: "medium"
    
  # 数据处理组合
  data:
    - data_migration_pro   # 数据迁移
    priority: "high"
    
  # 协同工作配置
  orchestration:
    parallel_execution: true
    max_concurrent_tools: 5
    resource_allocation:
      cpu: "80%"
      memory: "70%"
      
    # 工具间通信
    inter_tool_communication:
      enabled: true
      protocol: "grpc"
      message_queue: "rabbitmq"
      
    # 结果聚合
    result_aggregation:
      format: "unified_dashboard"
      real_time_update: true
      export_options:
        - json
        - html
        - pdf
