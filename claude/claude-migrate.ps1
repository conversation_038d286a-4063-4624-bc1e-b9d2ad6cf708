# claude-migrate.ps1

# 检测平台
$Platform = [System.Environment]::OSVersion.Platform
Write-Host "🚀 Starting migration on Windows" -ForegroundColor Cyan

# 设置环境变量
$env:CLAUDE_PLATFORM = "win32"
$env:NODE_OPTIONS = "--max-old-space-size=6144"

# 初始化
claude-code init migration `
  --config .claude\ `
  --platform auto

# 启动迁移
claude-code migrate `
  --source .\jeecgboot-vue-project `
  --target .\native-ui-project `
  --agents all `
  --parallel `
  --watch

# 监控
claude-code monitor --dashboard
