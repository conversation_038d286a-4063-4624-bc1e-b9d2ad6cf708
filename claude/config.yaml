# .claude/config.yaml
project:
  name: "JeecgBoot to Native UI Migration"
  version: "1.0.0"
  platform_detection: auto
  
# 平台特定配置
platforms:
  darwin:  # macOS
    shell: "zsh"
    path_separator: "/"
    home_var: "$HOME"
    temp_dir: "/tmp"
    
  win32:   # Windows
    shell: "powershell"
    path_separator: "\\"
    home_var: "%USERPROFILE%"
    temp_dir: "%TEMP%"
    
  linux:   # Linux (额外支持)
    shell: "bash"
    path_separator: "/"
    home_var: "$HOME"
    temp_dir: "/tmp"
