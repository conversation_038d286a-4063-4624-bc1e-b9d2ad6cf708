// .claude/build-config.js
const os = require('os');
const path = require('path');

const platform = os.platform();
const isWindows = platform === 'win32';
const isMac = platform === 'darwin';
const isLinux = platform === 'linux';

module.exports = {
  // 基础配置
  base: {
    projectName: 'claude-migration',
    sourceDir: 'src-jeecgboot',
    targetDir: 'src-native-ui',
  },
  
  // 平台特定配置
  platform: {
    // macOS 配置
    darwin: {
      shell: 'zsh',
      buildTool: 'vite',
      parallelJobs: os.cpus().length,
      memoryLimit: '8192',
      env: {
        NODE_ENV: 'development',
        FORCE_COLOR: '1',
      },
      scripts: {
        prebuild: 'chmod +x ./scripts/*.sh',
        build: 'vite build',
        postbuild: './scripts/post-build-mac.sh'
      }
    },
    
    // Windows 配置
    win32: {
      shell: 'powershell',
      buildTool: 'vite',
      parallelJobs: Math.max(1, os.cpus().length - 1), // Windows 保留一个核心
      memoryLimit: '6144', // Windows 内存限制稍低
      env: {
        NODE_ENV: 'development',
        FORCE_COLOR: '1',
      },
      scripts: {
        prebuild: 'powershell -ExecutionPolicy Bypass -File .\\scripts\\pre-build-win.ps1',
        build: 'vite build',
        postbuild: 'powershell -ExecutionPolicy Bypass -File .\\scripts\\post-build-win.ps1'
      }
    },
    
    // Linux 配置（额外）
    linux: {
      shell: 'bash',
      buildTool: 'vite',
      parallelJobs: os.cpus().length,
      memoryLimit: '8192',
      env: {
        NODE_ENV: 'development',
        FORCE_COLOR: '1',
      },
      scripts: {
        prebuild: 'chmod +x ./scripts/*.sh',
        build: 'vite build',
        postbuild: './scripts/post-build-linux.sh'
      }
    }
  },
  
  // 路径处理
  paths: {
    resolve: (p) => {
      if (isWindows) {
        return path.win32.resolve(p);
      }
      return path.posix.resolve(p);
    },
    join: (...args) => {
      if (isWindows) {
        return path.win32.join(...args);
      }
      return path.posix.join(...args);
    }
  },
  
  // 获取当前平台配置
  get current() {
    return this.platform[platform] || this.platform.linux;
  }
};
