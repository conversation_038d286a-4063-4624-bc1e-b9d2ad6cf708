# .claude/scripts/init-windows.ps1

Write-Host "🪟 Initializing Claude Code for Windows..." -ForegroundColor Cyan

# 检查管理员权限
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

if (-not (Test-Administrator)) {
    Write-Host "⚠️  Please run this script as Administrator" -ForegroundColor Yellow
    exit 1
}

# 检查并安装必要的工具
function Install-RequiredTools {
    # 检查 Chocolatey
    if (!(Get-Command choco -ErrorAction SilentlyContinue)) {
        Write-Host "Installing Chocolatey..."
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    }
    
    # 必要的工具
    $tools = @("nodejs", "git", "vscode", "python")
    foreach ($tool in $tools) {
        Write-Host "Checking $tool..."
        choco install $tool -y
    }
    
    # 安装 Node.js 包管理器
    npm install -g pnpm yarn windows-build-tools
}

# 设置环境变量
function Setup-Environment {
    $claudeHome = "$env:USERPROFILE\.claude"
    $claudeWorkspace = "$env:USERPROFILE\claude-workspace"
    
    [Environment]::SetEnvironmentVariable("CLAUDE_HOME", $claudeHome, "User")
    [Environment]::SetEnvironmentVariable("CLAUDE_CONFIG", "$claudeHome\config", "User")
    [Environment]::SetEnvironmentVariable("CLAUDE_WORKSPACE", $claudeWorkspace, "User")
    [Environment]::SetEnvironmentVariable("NODE_OPTIONS", "--max-old-space-size=8192", "User")
    
    # 更新 PATH
    $currentPath = [Environment]::GetEnvironmentVariable("Path", "User")
    $newPath = "$claudeHome\bin;$currentPath"
    [Environment]::SetEnvironmentVariable("Path", $newPath, "User")
}

# 创建工作目录
function Create-Directories {
    $dirs = @(
        "$env:USERPROFILE\.claude\bin",
        "$env:USERPROFILE\.claude\config",
        "$env:USERPROFILE\.claude\logs",
        "$env:USERPROFILE\.claude\cache",
        "$env:USERPROFILE\.claude\temp",
        "$env:USERPROFILE\claude-workspace\source",
        "$env:USERPROFILE\claude-workspace\target",
        "$env:USERPROFILE\claude-workspace\backup"
    )
    
    foreach ($dir in $dirs) {
        New-Item -ItemType Directory -Force -Path $dir | Out-Null
    }
}

Install-RequiredTools
Setup-Environment
Create-Directories

Write-Host "✅ Windows initialization complete!" -ForegroundColor Green
