# .claude/mcp-config-cross-platform.yaml
mcp_tools:
  # 文件系统工具（跨平台）
  filesystem:
    enabled: true
    cross_platform:
      path_resolution: automatic
      encoding: "utf-8"
      line_endings:
        darwin: "lf"
        win32: "crlf"
        linux: "lf"
    permissions:
      - read
      - write
      - create
      - delete
    paths:
      darwin:
        source: "./src-jeecgboot"
        target: "./src-native-ui"
        backup: "./backup"
        temp: "/tmp/claude-migration"
      win32:
        source: ".\\src-jeecgboot"
        target: ".\\src-native-ui"
        backup: ".\\backup"
        temp: "%TEMP%\\claude-migration"

  # 进程管理（跨平台）
  process_manager:
    enabled: true
    platforms:
      darwin:
        shell: "/bin/zsh"
        env_prefix: "export"
        path_sep: ":"
        file_ext: ".sh"
      win32:
        shell: "powershell.exe"
        env_prefix: "$env:"
        path_sep: ";"
        file_ext: ".ps1"
        
  # 包管理器（跨平台）
  package_manager:
    enabled: true
    auto_detect: true
    managers:
      npm:
        darwin: "npm"
        win32: "npm.cmd"
      yarn:
        darwin: "yarn"
        win32: "yarn.cmd"
      pnpm:
        darwin: "pnpm"
        win32: "pnpm.cmd"
        
  # Git 配置（跨平台）
  git:
    enabled: true
    auto_crlf:
      darwin: false
      win32: true
    ignore_case:
      darwin: false
      win32: true
    symbolic_links:
      darwin: true
      win32: false  # 需要管理员权限
      
  # 监视器（跨平台）
  file_watcher:
    enabled: true
    implementations:
      darwin: "fsevents"  # macOS 原生
      win32: "chokidar"   # 跨平台
      linux: "inotify"    # Linux 原生
    options:
      polling:
        darwin: false
        win32: true  # Windows 可能需要轮询
      interval: 100
