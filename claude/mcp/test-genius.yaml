# .claude/mcp/test-genius.yaml
mcp_tools:
  test_genius:
    name: "AI-Powered Test Generator"
    version: "4.2.0"
    rating: "⭐⭐⭐⭐⭐"
    
    features:
      # 自动测试生成
      auto_test_generation:
        - unit_test_creation
        - integration_test_synthesis
        - e2e_scenario_generation
        - edge_case_discovery
        
      # 测试优化
      test_optimization:
        - redundancy_elimination
        - coverage_gap_analysis
        - test_execution_ordering
        - parallel_test_distribution
        
      # 智能断言
      smart_assertions:
        - behavior_verification
        - state_validation
        - performance_benchmarking
        - visual_regression_testing
        
    config:
      frameworks_supported:
        - jest
        - vitest
        - playwright
        - cypress
        - storybook
        
      generation_strategy:
        coverage_target: 95
        focus_on_critical_paths: true
        generate_negative_tests: true
        include_performance_tests: true
        
    capabilities:
      - learns_from_existing_tests
      - generates_test_documentation
      - maintains_test_suites
      - suggests_test_improvements
