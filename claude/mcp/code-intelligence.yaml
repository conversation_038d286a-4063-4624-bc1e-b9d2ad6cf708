# .claude/mcp/code-intelligence.yaml
mcp_tools:
  code_intelligence:
    name: "Advanced Code Intelligence"
    version: "2.0.0"
    rating: "⭐⭐⭐⭐⭐"
    features:
      # 深度代码理解
      deep_analysis:
        - semantic_understanding
        - design_pattern_recognition
        - business_logic_extraction
        - dependency_mapping
        - complexity_analysis
        
      # AI 驱动的代码建议
      ai_suggestions:
        - code_completion
        - refactoring_recommendations
        - performance_optimizations
        - security_vulnerability_detection
        
      # 跨文件分析
      cross_file_analysis:
        - import_chain_tracking
        - circular_dependency_detection
        - unused_code_identification
        - impact_analysis
        
    config:
      engines:
        - typescript_analyzer
        - vue_parser
        - react_analyzer
        - style_processor
        
      intelligence_level: "advanced"
      real_time_analysis: true
      cache_analysis_results: true
      
    use_cases:
      - component_migration_mapping
      - code_quality_assurance
      - automated_refactoring
      - technical_debt_identification
