# .claude/mcp/data-migration-pro.yaml
mcp_tools:
  data_migration_pro:
    name: "Enterprise Data Migration Suite"
    version: "5.1.0"
    rating: "⭐⭐⭐⭐⭐"
    
    features:
      # 数据转换
      data_transformation:
        - schema_mapping
        - data_type_conversion
        - format_standardization
        - validation_rules_migration
        
      # 数据质量
      data_quality:
        - duplicate_detection
        - integrity_checking
        - consistency_validation
        - completeness_verification
        
      # 迁移策略
      migration_strategies:
        - incremental_migration
        - parallel_migration
        - rollback_capability
        - zero_downtime_migration
        
    config:
      supported_databases:
        - mysql
        - postgresql
        - mongodb
        - redis
        - elasticsearch
        
      features:
        transaction_support: true
        batch_processing: true
        real_time_sync: true
        conflict_resolution: "automatic"
        
    performance:
      - handles_millions_of_records
      - parallel_processing
      - memory_efficient
      - resumable_migrations
