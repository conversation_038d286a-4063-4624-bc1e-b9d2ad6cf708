# .claude/mcp/performance-wizard.yaml
mcp_tools:
  performance_wizard:
    name: "Advanced Performance Optimizer"
    version: "3.0.0"
    rating: "⭐⭐⭐⭐⭐"
    
    features:
      # 构建优化
      build_optimization:
        - bundle_size_reduction
        - tree_shaking_enhancement
        - code_splitting_strategy
        - lazy_loading_implementation
        - asset_optimization
        
      # 运行时优化
      runtime_optimization:
        - memory_leak_detection
        - render_performance_tuning
        - api_call_optimization
        - caching_strategy_implementation
        
      # 监控和分析
      monitoring:
        - real_user_monitoring
        - synthetic_monitoring
        - performance_budgeting
        - regression_detection
        
    config:
      optimization_level: "aggressive"
      target_metrics:
        first_contentful_paint: "< 1s"
        time_to_interactive: "< 2s"
        bundle_size: "< 200kb"
        
      tools_integrated:
        - webpack_bundle_analyzer
        - lighthouse
        - web_vitals
        - performance_observer_api
