# .claude/mcp/dependency-master.yaml
mcp_tools:
  dependency_master:
    name: "Smart Dependency Manager"
    version: "2.2.0"
    rating: "⭐⭐⭐⭐⭐"
    
    features:
      # 依赖分析
      dependency_analysis:
        - circular_dependency_detection
        - unused_dependency_identification
        - version_conflict_resolution
        - security_vulnerability_scanning
        
      # 自动更新
      auto_update:
        - safe_update_detection
        - breaking_change_analysis
        - gradual_migration_planning
        - rollback_capability
        
      # 优化建议
      optimization:
        - bundle_size_impact_analysis
        - alternative_package_suggestions
        - monorepo_optimization
        - peer_dependency_management
        
    config:
      package_managers:
        - npm
        - yarn
        - pnpm
        - bun
        
      strategies:
        update_strategy: "conservative"
        security_priority: "high"
        performance_consideration: true
        
      monitoring:
        - license_compliance
        - cost_analysis
        - maintenance_status
        - community_health
