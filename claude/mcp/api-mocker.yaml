# .claude/mcp/api-mocker.yaml
mcp_tools:
  api_mocker:
    name: "Intelligent API Mock Server"
    version: "2.8.0"
    rating: "⭐⭐⭐⭐⭐"
    
    features:
      # Mock 服务
      mock_services:
        - dynamic_response_generation
        - stateful_mocking
        - latency_simulation
        - error_injection
        
      # API 录制回放
      record_replay:
        - traffic_recording
        - scenario_replay
        - request_modification
        - response_templating
        
      # 契约测试
      contract_testing:
        - schema_validation
        - backward_compatibility_check
        - consumer_driven_contracts
        - provider_verification
        
    config:
      server:
        port: 3001
        https: true
        cors: true
        
      features:
        graphql_support: true
        websocket_support: true
        grpc_support: true
        openapi_integration: true
        
    data_generation:
      - faker_integration
      - custom_generators
      - relational_data
      - time_series_data
