# .claude/mcp/git-master.yaml
mcp_tools:
  git_master:
    name: "Advanced Git Operations"
    version: "3.3.0"
    rating: "⭐⭐⭐⭐⭐"
    
    features:
      # 智能合并
      smart_merge:
        - conflict_prediction
        - auto_resolution
        - semantic_merge
        - three_way_merge_optimization
        
      # 分支管理
      branch_management:
        - auto_branching_strategy
        - feature_branch_lifecycle
        - release_management
        - hotfix_automation
        
      # 历史分析
      history_analysis:
        - code_evolution_tracking
        - contributor_analytics
        - change_impact_analysis
        - regression_identification
        
    config:
      strategies:
        - git_flow
        - github_flow
        - gitlab_flow
        - custom_flow
        
      automation:
        auto_commit_formatting: true
        pre_commit_hooks: true
        ci_cd_integration: true
        automatic_versioning: true
