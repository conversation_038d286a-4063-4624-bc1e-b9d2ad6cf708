# .claude/mcp/docu-master.yaml
mcp_tools:
  docu_master:
    name: "Intelligent Documentation Generator"
    version: "2.5.0"
    rating: "⭐⭐⭐⭐⭐"
    
    features:
      # 多格式文档生成
      documentation_types:
        - api_documentation
        - component_storybook
        - migration_guide
        - user_manual
        - technical_specification
        
      # 智能内容生成
      content_generation:
        - code_example_extraction
        - use_case_documentation
        - troubleshooting_guide
        - best_practices_compilation
        
      # 多语言支持
      i18n_support:
        - auto_translation
        - cultural_adaptation
        - terminology_consistency
        
    config:
      output_formats:
        - markdown
        - html
        - pdf
        - docusaurus
        - gitbook
        - confluence
        
      features:
        auto_update_on_code_change: true
        version_tracking: true
        collaborative_editing: true
        ai_content_enhancement: true
        
    integrations:
      - github_pages
      - confluence
      - notion
      - swagger
