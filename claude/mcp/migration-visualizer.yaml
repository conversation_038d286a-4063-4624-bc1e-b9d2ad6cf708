# .claude/mcp/migration-visualizer.yaml
mcp_tools:
  migration_visualizer:
    name: "Interactive Migration Dashboard"
    version: "3.1.0"
    rating: "⭐⭐⭐⭐⭐"
    
    features:
      # 实时可视化
      real_time_dashboard:
        - migration_progress_chart
        - component_dependency_graph
        - error_heat_map
        - performance_metrics_timeline
        
      # 交互式控制
      interactive_controls:
        - drag_drop_component_mapping
        - visual_conflict_resolution
        - rollback_timeline
        - priority_adjustment
        
      # 3D 可视化
      3d_visualization:
        - project_structure_3d
        - data_flow_animation
        - migration_path_visualization
        
    config:
      ui_framework: "react-three-fiber"
      update_frequency: "real-time"
      export_formats:
        - html
        - pdf
        - interactive_web
        
    endpoints:
      dashboard: "http://localhost:8080/migration-dashboard"
      api: "http://localhost:8080/api/v1/"
      websocket: "ws://localhost:8080/ws"
