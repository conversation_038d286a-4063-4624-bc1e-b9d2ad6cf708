#!/bin/bash
# claude-migrate.sh

# 检测平台
PLATFORM=$(uname -s)
echo "🚀 Starting migration on $PLATFORM"

# 设置环境变量
export CLAUDE_PLATFORM=$PLATFORM
export NODE_OPTIONS="--max-old-space-size=8192"

# 初始化
claude-code init migration \
  --config .claude/ \
  --platform auto

# 启动迁移
claude-code migrate \
  --source ./jeecgboot-vue-project \
  --target ./native-ui-project \
  --agents all \
  --parallel \
  --watch

# 监控
claude-code monitor --dashboard
