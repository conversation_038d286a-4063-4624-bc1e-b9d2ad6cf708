# .claude/test-config-cross-platform.yaml
testing:
  # 单元测试
  unit_tests:
    framework: "vitest"
    config:
      darwin:
        workers: 4
        reporter: "verbose"
        coverage:
          provider: "c8"
      win32:
        workers: 2  # Windows 上减少并发
        reporter: "verbose"
        coverage:
          provider: "istanbul"
          
  # E2E 测试
  e2e_tests:
    framework: "playwright"
    browsers:
      darwin:
        - chromium
        - firefox
        - webkit  # Safari
      win32:
        - chromium
        - firefox
        - msedge  # Edge
    config:
      darwin:
        headless: true
        video: "retain-on-failure"
      win32:
        headless: false  # Windows 可能需要可视化调试
        video: "on"
        
  # 性能测试
  performance_tests:
    tools:
      darwin:
        - lighthouse
        - chrome-devtools
      win32:
        - lighthouse
        - edge-devtools
    thresholds:
      darwin:
        first_contentful_paint: 1200
        time_to_interactive: 3000
      win32:
        first_contentful_paint: 1500  # Windows 阈值稍宽松
        time_to_interactive: 3500
