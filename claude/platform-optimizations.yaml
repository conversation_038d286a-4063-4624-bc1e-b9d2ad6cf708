# .claude/platform-optimizations.yaml
optimizations:
  macos:
    development:
      - use_native_file_watching  # FSEvents
      - enable_parallel_compilation
      - use_swc_for_transpilation
      - leverage_m1_acceleration
    production:
      - optimize_bundle_size
      - enable_tree_shaking
      - use_cdn_for_static_assets
      
  windows:
    development:
      - disable_windows_defender_for_dev_folders
      - use_polling_for_file_watching
      - optimize_node_memory_usage
      - enable_long_path_support
    production:
      - minimize_dll_dependencies
      - optimize_for
