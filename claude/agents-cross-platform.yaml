# .claude/agents-cross-platform.yaml
agents:
  # 平台适配 Agent
  platform_adapter:
    name: "Platform Adapter"
    role: "处理平台差异"
    responsibilities:
      - 检测运行平台
      - 转换路径格式
      - 处理行尾符
      - 管理权限差异
    platform_specific:
      darwin:
        - handle_case_sensitivity
        - manage_quarantine_attributes
        - handle_macos_security
      win32:
        - handle_case_insensitivity
        - manage_windows_defender
        - handle_long_paths
        - convert_powershell_scripts
        
  # 环境配置 Agent
  environment_configurator:
    name: "Environment Configurator"
    role: "配置开发环境"
    tasks:
      darwin:
        - configure_xcode_tools
        - setup_homebrew_packages
        - configure_zsh_environment
      win32:
        - configure_visual_studio_tools
        - setup_chocolatey_packages
        - configure_powershell_environment
        - enable_developer_mode
        
  # 性能优化 Agent
  performance_optimizer:
    name: "Performance Optimizer"
    role: "针对平台优化性能"
    optimizations:
      darwin:
        - use_native_fsevents
        - optimize_for_arm64  # Apple Silicon
        - leverage_metal_acceleration
      win32:
        - optimize_antivirus_exclusions
        - configure_windows_defender
        - use_native_windows_apis
        - optimize_ntfs_settings
