{"mcpServers": {"mysql-database": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-mysql"], "env": {"MYSQL_HOST": "**************", "MYSQL_PORT": "3306", "MYSQL_USER": "pes", "MYSQL_PASSWORD": "Pes123!@#", "MYSQL_DATABASE": "physicalex-lkd", "MYSQL_SSL": "false", "MYSQL_CONNECTION_LIMIT": "10", "MYSQL_TIMEOUT": "60000"}, "description": "PhysicalEX体检系统MySQL数据库连接", "capabilities": {"schema_operations": {"description": "数据库模式操作", "features": ["查看表结构", "创建和修改表", "管理索引", "外键约束管理"]}, "data_operations": {"description": "数据CRUD操作", "features": ["SELECT查询", "INSERT插入", "UPDATE更新", "DELETE删除", "批量操作"]}, "query_optimization": {"description": "查询优化分析", "features": ["EXPLAIN执行计划", "慢查询分析", "索引使用分析", "性能统计"]}}, "safety_features": {"read_only_mode": false, "transaction_support": true, "backup_before_changes": false, "query_timeout": 60, "max_rows_returned": 10000}}}, "connection_pools": {"physicalex_primary": {"description": "PhysicalEX主数据库连接池", "config": {"host": "**************", "port": 3306, "user": "pes", "password": "Pes123!@#", "database": "physicalex-lkd", "charset": "UTF8MB4", "timezone": "+08:00", "ssl": false, "connectionLimit": 20, "acquireTimeout": 60000, "timeout": 60000, "reconnect": true, "rewriteBatchedStatements": true, "useServerPrepStmts": true, "cachePrepStmts": true, "prepStmtCacheSize": 250, "prepStmtCacheSqlLimit": 2048}}}, "database_schema": {"physicalex-lkd": {"description": "PhysicalEX体检管理系统数据库", "core_tables": {"customer": {"description": "客户基础信息表", "primary_key": "id", "key_columns": ["id", "name", "id_card", "phone", "create_time"], "indexes": ["idx_id_card", "idx_phone", "idx_create_time"]}, "customer_reg": {"description": "体检登记表", "primary_key": "id", "key_columns": ["id", "customer_id", "company_id", "reg_date", "status"], "indexes": ["idx_customer_id", "idx_company_id", "idx_reg_date", "idx_status"]}, "customer_reg_item_group": {"description": "体检项目组表", "primary_key": "id", "key_columns": ["id", "customer_reg_id", "item_group_id", "check_part_code"], "indexes": ["idx_customer_reg_id", "idx_item_group_id", "idx_check_part"]}, "customer_reg_item_result": {"description": "体检结果表", "primary_key": "id", "key_columns": ["id", "customer_reg_id", "item_id", "result_value", "create_time"], "indexes": ["idx_customer_reg_id", "idx_item_id", "idx_create_time"]}, "questionnaire_common_options": {"description": "问卷常用选项字典", "primary_key": "id", "key_columns": ["id", "field_name", "option_text", "use_count"], "indexes": ["idx_field_name", "idx_option_text", "idx_use_count"]}, "icd": {"description": "ICD疾病编码表", "primary_key": "id", "key_columns": ["id", "code", "name", "assist_code"], "indexes": ["idx_code", "idx_name", "idx_assist_code"]}, "item_dict": {"description": "检查项目字典", "primary_key": "id", "key_columns": ["id", "item_name", "item_code", "category"], "indexes": ["idx_item_code", "idx_category"]}, "item_group": {"description": "项目组字典", "primary_key": "id", "key_columns": ["id", "group_name", "group_code"], "indexes": ["idx_group_code"]}, "company": {"description": "企业信息表", "primary_key": "id", "key_columns": ["id", "company_name", "company_code"], "indexes": ["idx_company_code"]}}, "data_volume_info": {"customer": "100万+ 记录", "customer_reg": "500万+ 记录/年", "customer_reg_item_result": "1亿+ 记录/年", "questionnaire_common_options": "10万+ 记录", "icd": "10万+ 记录"}}}, "common_queries": {"customer_management": {"find_customer_by_idcard": "SELECT * FROM customer WHERE id_card = ? AND del_flag = 0", "find_customer_by_phone": "SELECT * FROM customer WHERE phone = ? AND del_flag = 0", "customer_reg_history": "SELECT cr.*, c.name, c.id_card FROM customer_reg cr LEFT JOIN customer c ON cr.customer_id = c.id WHERE cr.customer_id = ? ORDER BY cr.reg_date DESC"}, "examination_data": {"reg_item_groups": "SELECT rig.*, ig.group_name FROM customer_reg_item_group rig LEFT JOIN item_group ig ON rig.item_group_id = ig.id WHERE rig.customer_reg_id = ?", "item_results": "SELECT rir.*, id.item_name FROM customer_reg_item_result rir LEFT JOIN item_dict id ON rir.item_id = id.id WHERE rir.customer_reg_id = ?", "examination_summary": "SELECT cr.id, cr.reg_date, c.name, c.id_card, COUNT(DISTINCT rig.id) as group_count, COUNT(DISTINCT rir.id) as result_count FROM customer_reg cr LEFT JOIN customer c ON cr.customer_id = c.id LEFT JOIN customer_reg_item_group rig ON cr.id = rig.customer_reg_id LEFT JOIN customer_reg_item_result rir ON cr.id = rir.customer_reg_id WHERE cr.id = ? GROUP BY cr.id"}, "questionnaire_data": {"search_common_options": "SELECT * FROM questionnaire_common_options WHERE field_name = ? AND option_text LIKE CONCAT('%', ?, '%') ORDER BY use_count DESC LIMIT ?", "popular_options": "SELECT * FROM questionnaire_common_options WHERE field_name = ? ORDER BY use_count DESC LIMIT ?", "record_usage": "UPDATE questionnaire_common_options SET use_count = use_count + 1, last_used_time = NOW() WHERE field_name = ? AND option_value = ?"}, "icd_operations": {"search_icd": "SELECT * FROM icd WHERE name LIKE CONCAT('%', ?, '%') OR code LIKE CONCAT('%', ?, '%') OR assist_code LIKE CONCAT('%', ?, '%') LIMIT ?", "find_icd_by_code": "SELECT * FROM icd WHERE code = ?", "popular_diseases": "SELECT i.* FROM icd i WHERE i.code IN (SELECT DISTINCT icd_code FROM questionnaire_common_options WHERE field_name = 'disease' AND icd_code IS NOT NULL AND icd_code != '') ORDER BY i.name LIMIT ?"}, "performance_queries": {"slow_query_analysis": "SELECT * FROM information_schema.PROCESSLIST WHERE TIME > 10 AND COMMAND != 'Sleep'", "table_sizes": "SELECT TABLE_NAME, ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'SIZE_MB' FROM information_schema.TABLES WHERE TABLE_SCHEMA = 'physicalex-lkd' ORDER BY SIZE_MB DESC", "index_usage": "SELECT OBJECT_SCHEMA, OBJECT_NAME, INDEX_NAME, COUNT_FETCH, COUNT_INSERT, COUNT_UPDATE, COUNT_DELETE FROM performance_schema.table_io_waits_summary_by_index_usage WHERE OBJECT_SCHEMA = 'physicalex-lkd'"}}, "optimization_scripts": {"index_recommendations": {"find_missing_indexes": "SELECT table_name, column_name FROM information_schema.statistics WHERE table_schema = 'physicalex-lkd' GROUP BY table_name, column_name HAVING COUNT(*) = 1", "unused_indexes": "SELECT OBJECT_SCHEMA, OBJECT_NAME, INDEX_NAME FROM performance_schema.table_io_waits_summary_by_index_usage WHERE OBJECT_SCHEMA = 'physicalex-lkd' AND INDEX_NAME IS NOT NULL AND COUNT_STAR = 0"}, "data_quality_checks": {"duplicate_customers": "SELECT id_card, COUNT(*) as count FROM customer WHERE del_flag = 0 GROUP BY id_card HAVING count > 1", "orphaned_reg_records": "SELECT cr.id FROM customer_reg cr LEFT JOIN customer c ON cr.customer_id = c.id WHERE c.id IS NULL", "invalid_dates": "SELECT id, reg_date FROM customer_reg WHERE reg_date > NOW() OR reg_date < '1900-01-01'"}, "maintenance_scripts": {"update_statistics": "ANALYZE TABLE customer, customer_reg, customer_reg_item_group, customer_reg_item_result, questionnaire_common_options", "optimize_tables": "OPTIMIZE TABLE customer, customer_reg, customer_reg_item_group, customer_reg_item_result", "check_table_integrity": "CHECK TABLE customer, customer_reg, customer_reg_item_group, customer_reg_item_result"}}, "backup_and_recovery": {"backup_commands": {"full_backup": "mysqldump -h************** -upes -pPes123!@# --single-transaction --routines --triggers physicalex-lkd > physicalex_backup_$(date +%Y%m%d_%H%M%S).sql", "schema_backup": "mysqldump -h************** -upes -pPes123!@# --no-data --routines --triggers physicalex-lkd > physicalex_schema_$(date +%Y%m%d_%H%M%S).sql", "data_backup": "mysqldump -h************** -upes -pPes123!@# --no-create-info --skip-triggers physicalex-lkd > physicalex_data_$(date +%Y%m%d_%H%M%S).sql"}, "recovery_procedures": {"full_restore": "mysql -h************** -upes -pPes123!@# physicalex-lkd < backup_file.sql", "table_restore": "mysql -h************** -upes -pPes123!@# physicalex-lkd -e \"source backup_file.sql\""}}, "monitoring_configuration": {"performance_metrics": ["Queries per second", "Connection count", "InnoDB buffer pool usage", "Slow query count", "Table lock waits", "Replication lag"], "alert_thresholds": {"max_connections": 800, "slow_query_threshold": 100, "disk_usage_threshold": 85, "cpu_usage_threshold": 80, "memory_usage_threshold": 90}, "health_checks": {"connection_test": "SELECT 1", "replication_status": "SHOW SLAVE STATUS", "processlist": "SHOW PROCESSLIST", "engine_status": "SHOW ENGINE INNODB STATUS"}}, "security_configuration": {"access_control": {"admin_user": "pes", "read_only_users": [], "application_users": ["app_user"], "backup_user": "backup_user"}, "encryption_settings": {"data_at_rest": false, "data_in_transit": false, "backup_encryption": true}, "audit_configuration": {"enable_general_log": false, "enable_slow_query_log": true, "enable_binary_log": true, "audit_connection_events": true}}, "development_helpers": {"data_generation": {"sample_customers": "INSERT INTO customer (id, name, id_card, phone, create_time) VALUES (REPLACE(UUID(), '-', ''), CONCAT('测试用户', LPAD(FLOOR(RAND()*10000), 4, '0')), CONCAT('110101', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL FLOOR(RAND()*365*30) DAY), '%Y%m%d'), LPAD(FLOOR(RAND()*999), 3, '0'), CASE WHEN RAND() > 0.5 THEN '1' ELSE '2' END), CONCAT('138', LPAD(FLOOR(RAND()*100000000), 8, '0')), NOW())", "sample_registrations": "INSERT INTO customer_reg (id, customer_id, reg_date, status, create_time) SELECT REPLACE(UUID(), '-', ''), id, DATE_SUB(NOW(), INTERVAL FLOOR(RAND()*365) DAY), 1, NOW() FROM customer LIMIT 10"}, "test_queries": {"performance_test": "SELECT COUNT(*) FROM customer_reg WHERE reg_date BETWEEN DATE_SUB(NOW(), INTERVAL 1 YEAR) AND NOW()", "join_performance": "SELECT c.name, COUNT(cr.id) as reg_count FROM customer c LEFT JOIN customer_reg cr ON c.id = cr.customer_id GROUP BY c.id LIMIT 100"}}}