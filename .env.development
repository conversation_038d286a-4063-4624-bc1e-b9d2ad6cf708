# 是否打开mock
VITE_USE_MOCK = true

# 发布路径
VITE_PUBLIC_PATH = /


# 跨域代理，您可以配置多个 ,请注意，没有换行符
VITE_PROXY = [["/jeecgboot","http://localhost:8090/jeecgboot"],["/upload","http://localhost:3300/upload"]]

#后台接口全路径地址(必填)
VITE_GLOB_DOMAIN_URL=http://localhost:8090/jeecgboot

#后台接口父地址(必填)
VITE_GLOB_API_URL=/jeecgboot

# 接口前缀
VITE_GLOB_API_URL_PREFIX=

#文件上传的类型,aliyun,minio,local
VITE_GLOB_UPLOAD_TYPE=minio

# ==================== MinIO内外网分离配置（开发环境） ====================
# 内网地址（HTTP，开发环境直连）
VITE_GLOB_MINIO_INTERNAL_URL=http://***********:9000

# 外网地址（开发环境可以使用相同地址或测试地址）
VITE_GLOB_MINIO_EXTERNAL_URL=http://***********:9000

# 保留原有配置作为备用
# VITE_GLOB_MINIO_URL=http://************:9000

#微前端qiankun应用,命名必须以VITE_APP_SUB_开头,jeecg-app-1为子应用的项目名称,也是子应用的路由父路径
VITE_APP_SUB_jeecg-app-1 = '//localhost:8092'

