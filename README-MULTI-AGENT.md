# PhysicalEX 多Agent协作系统

这是为PhysicalEX体检管理系统设计的多Agent协作配置，包含项目经理、交互设计师、UI设计师、前端工程师和后端工程师五个专业角色。

## 🎯 系统架构

### 技术栈
- **前端**: Vue 3.5.18 + TypeScript 4.9.5 + Ant Design Vue 4.2.6
- **后端**: Spring Boot 2.7.18 + Java 17 + MyBatis Plus
- **数据库**: MySQL 8.0.27
- **架构**: JeecgBoot微服务架构

### 项目结构
```
physicalex/
├── admin-front/           # Vue3前端项目
├── physicalex-lkd/        # Spring Boot后端项目
├── multi-agent-config.yaml   # 主配置文件
├── mcp-config.json        # MCP服务器配置
└── agents/                # Agent详细配置
    ├── project-manager-agent.yaml
    ├── frontend-engineer-agent.yaml
    └── backend-engineer-agent.yaml
```

## 🤖 Agent角色定义

### 1. 项目经理 (Project Manager)
**职责**: 需求分析、项目管理、团队协调
- 医疗业务流程梳理
- 需求优先级评估
- 项目进度跟踪
- 风险识别和管理

### 2. 交互设计师 (UX Designer)
**职责**: 用户体验设计、交互流程优化
- 用户研究和分析
- 医疗工作流程优化
- 交互原型设计
- 可用性测试

### 3. UI设计师 (UI Designer)
**职责**: 视觉设计、界面美化
- 医疗主题设计
- 设计系统构建
- 组件库维护
- 视觉规范制定

### 4. 前端工程师 (Frontend Engineer)
**职责**: Vue3前端开发、界面实现
- Vue3 + TypeScript开发
- Ant Design Vue组件定制
- 医疗专用组件开发
- 前端性能优化

### 5. 数据库设计师 (Database Designer)
**职责**: 配合业务逻辑进行建表和更新表工作
- 根据后端实体设计对应数据表
- 配合业务逻辑需求调整表结构
- 创建和修改表的SQL脚本
- 管理表结构变更和数据迁移

### 6. 后端工程师 (Backend Engineer)
**职责**: Spring Boot后端开发、API设计
- RESTful API开发
- 医疗业务逻辑实现
- 与数据库设计师协作定义数据模型
- 安全合规实现

## 🔄 协作工作流

### 功能开发流程
1. **需求分析** (项目经理) → 
2. **UX设计** (交互设计师) → 
3. **UI设计** (UI设计师) → 
4. **数据库设计** (数据库设计师) → 
5. **后端开发** (后端工程师) → 
6. **前端开发** (前端工程师)

### 业务逻辑建表流程
1. **业务实体分析** (后端工程师)
2. **数据表设计** (数据库设计师) 
3. **表结构实现** (数据库设计师)
4. **ORM映射验证** (后端工程师)

### 问题修复流程
1. **问题分析** (项目经理)
2. **问题定位** (前端/后端工程师)
3. **修复实施** (相关工程师)
4. **测试验证** (全团队)

## 🚀 快速开始

### 1. 启动后端服务
```bash
cd physicalex-lkd
./start-app.bat  # Windows
# 或
./start-app.sh   # Linux/Mac
```

### 2. 启动前端服务
```bash
cd admin-front
pnpm install
pnpm dev
```

### 3. 配置Agent环境
```bash
# 设置环境变量
export PROJECT_PATH="/path/to/physicalex"
export NODE_ENV="development"
export SPRING_PROFILES_ACTIVE="dev"

# MySQL数据库连接配置
export MYSQL_HOST="**************"
export MYSQL_PORT="3306"
export MYSQL_USER="pes"
export MYSQL_PASSWORD="Pes123!@#"
export MYSQL_DATABASE="physicalex-lkd"
```

### 4. 启动MySQL MCP服务器
```bash
# 安装MySQL MCP服务器
npx -y @modelcontextprotocol/server-mysql

# 使用专门的MySQL配置
# 配置文件: mysql-mcp-config.json
```

## 📋 主要功能模块

### 医疗业务模块
- **预约管理** (`appointment`) - 体检预约和时间管理
- **客户登记** (`reg`) - 客户信息录入和管理
- **体检流程** (`station`) - 各科室检查流程
- **报告生成** (`summary`) - 体检报告生成和打印
- **费用管理** (`fee`) - 费用计算和支付处理
- **职业健康** (`occu`) - 职业病相关检查

### 数据库设计师使用场景
- **新功能开发**: 根据后端实体类自动生成对应数据表
- **业务变更**: 配合业务逻辑修改现有表结构
- **表结构优化**: 基于业务需求调整字段类型和约束
- **数据迁移**: 处理表结构变更的数据迁移脚本
- **关系设计**: 建立表与表之间的外键关系

### 技术特性
- **响应式设计** - 适配PC和移动端
- **权限控制** - 基于角色的访问控制
- **数据安全** - 医疗数据加密和脱敏
- **性能优化** - 缓存策略和查询优化
- **国际化** - 多语言支持

## 🔧 配置说明

### Agent配置文件
每个Agent都有独立的配置文件，包含：
- 角色定义和职责
- 技术能力和工具
- 协作接口规范
- 质量标准要求

### MCP服务器配置
`mcp-config.json`定义了：
- 各Agent的服务配置
- 工作流程编排
- 通信协议规范
- 质量门禁设置

## 📊 质量标准

### 代码质量
- **前端**: TypeScript严格模式、ESLint检查、测试覆盖率≥80%
- **后端**: 单元测试覆盖率≥80%、接口响应时间<200ms

### 医疗合规
- **数据隐私**: HIPAA合规
- **访问控制**: 基于角色的权限
- **审计日志**: 全操作记录
- **数据加密**: 敏感信息加密

## 🔍 监控指标

### 性能指标
- API响应时间 < 200ms
- 前端首屏加载 < 3s
- 系统并发支持 > 1000用户
- 系统可用性 > 99.9%

### 业务指标
- 用户满意度 ≥ 4.0/5
- 数据准确性 100%
- 工作流效率提升
- 错误率控制

## 🤝 团队协作

### 日常协作
- **每日站会**: 进度同步、问题讨论
- **周例会**: 里程碑回顾、计划调整
- **代码评审**: 质量保证、知识共享

### 沟通渠道
- **实时沟通**: 技术问题快速解决
- **文档协作**: 需求、设计文档共享
- **版本管理**: Git协作和代码管理

## 📞 支持和反馈

如有问题或建议，请联系：
- 技术支持: 邦健信息技术有限公司
- 邮箱: <EMAIL>
- 项目地址: https://www.yingyangyun.cn

---

> 本配置专为医疗健康管理系统设计，确保符合医疗行业规范和数据安全要求。